[{"account": "***********", "behavior": "下订单", "behavior_time": **********, "detail": "使用30元优惠券下单购买498元自营商品"}, {"account": "***********", "behavior": "下订单", "behavior_time": **********, "detail": "使用50元优惠券下单购买398元自营商品"}, {"account": "<EMAIL>", "behavior": "加入购物车", "behavior_time": **********, "detail": "将140个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将450个电子元器件加入了购物车"}, {"account": "***********", "behavior": "下订单", "behavior_time": **********, "detail": "使用9折优惠券下单购买1280元自营商品"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满100元减10元优惠券一张"}, {"account": "<EMAIL>", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满200元减10元优惠券一张"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将300个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将450个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将230个电子元器件加入了购物车"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满100元打9折优惠券一张"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满200元打9折优惠券一张"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满10元打9折优惠券一张"}, {"account": "***********", "behavior": "下订单", "behavior_time": **********, "detail": "使用50元优惠券下单购买3528元联营商品"}, {"account": "***********", "behavior": "下订单", "behavior_time": **********, "detail": "使用30元优惠券下单购买3528元联营商品"}, {"account": "<EMAIL>", "behavior": "下订单", "behavior_time": **********, "detail": "下单购买134元联营商品"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满1000元减50元优惠券一张"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满3000元减80元优惠券一张"}, {"account": "***********", "behavior": "下订单", "behavior_time": **********, "detail": "使用20元优惠券下单购买258元自营商品"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满5000元减200元优惠券一张"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满8000元减200元优惠券一张"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满100元减20元优惠券一张"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满15000元减300元优惠券一张"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将200个电子元器件加入了购物车"}, {"account": "<EMAIL>", "behavior": "加入购物车", "behavior_time": **********, "detail": "将100个电子元器件加入了购物车"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满5000元减100元优惠券一张"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满300元减10元优惠券一张"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将130个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将150个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将90个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将110个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将2300个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将30个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将50个电子元器件加入了购物车"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满100元减5元优惠券一张"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满100元减5元优惠券一张"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将230个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将40个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将140个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将240个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将120个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将240个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将180个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将80个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将280个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将160个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将500个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将1000个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将60个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将10个电子元器件加入了购物车"}]