[{"account": "***********", "behavior": "下订单", "behavior_time": **********, "detail": "使用30元优惠券下单购买498元自营商品"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "注册获得400积分"}, {"account": "<EMAIL>", "behavior": "加入购物车", "behavior_time": **********, "detail": "将140个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将450个电子元器件加入了购物车"}, {"account": "***********", "behavior": "下订单", "behavior_time": **********, "detail": "使用9折优惠券下单购买1280元自营商品"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满100元减10元优惠券一张"}, {"account": "<EMAIL>", "behavior": "获得积分", "behavior_time": **********, "detail": "签到获得100积分"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将300个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将450个电子元器件加入了购物车"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "订单交易完成获得3250积分"}, {"account": "***********", "behavior": "兑换礼品", "behavior_time": **********, "detail": "用8688积分兑换了抱枕一个"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "订单交易完成获得350积分"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满10元打9折优惠券一张"}, {"account": "***********", "behavior": "下订单", "behavior_time": **********, "detail": "使用50元优惠券下单购买3528元联营商品"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "完善资料获得40积分"}, {"account": "<EMAIL>", "behavior": "下订单", "behavior_time": **********, "detail": "下单购买134元联营商品"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "订单交易完成获得4002积分"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满3000元减80元优惠券一张"}, {"account": "***********", "behavior": "下订单", "behavior_time": **********, "detail": "使用20元优惠券下单购买258元自营商品"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "订单交易完成获得3391积分"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满8000元减200元优惠券一张"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满100元减20元优惠券一张"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满15000元减300元优惠券一张"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将200个电子元器件加入了购物车"}, {"account": "<EMAIL>", "behavior": "获得积分", "behavior_time": **********, "detail": "签到获得100积分"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满5000元减100元优惠券一张"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满300元减10元优惠券一张"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将130个电子元器件加入了购物车"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "订单交易完成获得528积分"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将90个电子元器件加入了购物车"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "订单交易完成获得527积分"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将2300个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将30个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将50个电子元器件加入了购物车"}, {"account": "***********", "behavior": "获得优惠券", "behavior_time": **********, "detail": "获得了满100元减5元优惠券一张"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "订单交易完成获得228积分"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将230个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将40个电子元器件加入了购物车"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "签到获得100积分"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "订单交易完成获得110积分"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将120个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将240个电子元器件加入了购物车"}, {"account": "***********", "behavior": "兑换礼品", "behavior_time": **********, "detail": "用1888个积分兑换了10元优惠券一个"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将80个电子元器件加入了购物车"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "订单交易完成获得134积分"}, {"account": "***********", "behavior": "获得积分", "behavior_time": **********, "detail": "订单交易完成获得3990积分"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将500个电子元器件加入了购物车"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将1000个电子元器件加入了购物车"}, {"account": "***********", "behavior": "兑换礼品", "behavior_time": **********, "detail": "用8088个积分兑换了精美笔记本一个"}, {"account": "***********", "behavior": "加入购物车", "behavior_time": **********, "detail": "将10个电子元器件加入了购物车"}]