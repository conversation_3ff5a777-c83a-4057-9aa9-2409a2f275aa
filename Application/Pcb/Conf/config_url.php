<?php
return [

    "APP_ENV" =>"develop",

    "pcb_api_url"=>[
        "develop"=>"https://jdbwebapi_test.jdbpcb.com",
        "product"=>"https://jdbwebapi.jdbpcb.com",
    ],


    'api_key'=>[
        "develop"=>"0001LX 985efdba96be720f60cd376b5784b6f6",
        "product"=>"177251C 77cfd8c0aa24110cf3e1bc98eeb49e64",
    ],

//    'URL_CASE_INSENSITIVE'   => true, // 默true 表示URL不区分大小写 false则表示区分大小写
//    'URL_MODEL'              => 2, // URL访问模式,可选参数0、1、2、3,代表以下四种模式： 0 (普通模式); 1 (PATHINFO 模式); 2 (REWRITE  模式); 3 (兼容模式)  默认为PATHINFO 模式
//
//    'URL_ROUTER_ON'          => true, // 是否开启URL路由
//    'URL_ROUTE_RULES'        =>[//动态路由
//
//        '/^pcb\/(\w+)/' => 'Pcb/Index/:1',
//
//    ],


];