<?php
namespace Pcb\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class PcbValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            'Layer' => 'required|integer',#板子层数
            'Length' => 'required|numeric|between:0.1,120',#板子长度
            'Width' => 'required|numeric|between:0.1,120',#板子宽度
            'Qty' => 'required|integer',#数量
            'Material' => 'required|string',#板材类型
            'Thickness' => 'required|string',#厚度
            'LineSpace' => 'required|numeric',#宽/线距
            'MinHoleSize' => 'required|numeric',#厚度
            'BoardColor' => 'required|string',#阻焊颜色
            'FontColor' => 'required|string',#字符颜色
            'Surface' => 'required|string',#表面处理
            'TestType' => 'required|string',#测试
            'ViaProcess' => 'string',#阻焊覆盖
            'CopperThickness' => 'required|numeric',#铜厚
            'InsideCopperThickness' => 'required|numeric',#内层铜厚
            'DesignNum' => 'required|integer',#设计文件数量
//            'BillType' => 'required|integer',#发票
            'IsJiaji' => 'required|boolean',#是否加急
            'SpHalfHole' => 'boolean',#特殊技参数-是否有半孔工艺
            'SpGoldfingers' => 'boolean',#特殊技参数-是否要金手指
            'SpGoldfingersBevelling' => 'string',#特殊技参数-金手指斜边
            'SpULMarking' => 'string',#特殊技参数-UL标记
//            'Days' => 'required|numeric',#天数

        ];

        $this->title = [
            'Layer' => '板子层数',
            'Length' => '板子长度',
            'Width' => '板子宽度',
            'Qty' => '数量',
            'Material' => '板材类型',
            'Thickness' => '厚度',
            'LineSpace' => '宽/线距',
            'MinHoleSize' => '厚度',
            'BoardColor' => '阻焊颜色',
            'FontColor' => '字符颜色',
            'Surface' => '表面处理',
            'TestType' => '测试',
            'ViaProcess' => '阻焊覆盖',
            'CopperThickness' => '铜厚',
            'InsideCopperThickness' => '内层铜厚',
            'DesignNum' => '设计文件数量',
//            'BillType' => '发票',
            'IsJiaji' => '是否加急',
            'SpHalfHole' => '特殊技参数-是否有半孔工艺',
            'SpGoldfingers' => '特殊技参数-是否要金手指',
            'SpGoldfingersBevelling' => '特殊技参数-金手指斜边',
            'SpULMarking' => '特殊技参数-UL标记',
//            'Days' => '生产周期',
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){
        $arr = [];
        $arr['Layer'] = (int)$this->data['Layer'];
        $arr['Length'] = floatval($this->data['Length']);
        $arr['Width'] = floatval($this->data['Width']);
        $arr['Qty'] = intval($this->data['Qty']);
        $arr['Material'] = trim(strval($this->data['Material']));
        $arr['Thickness'] = trim(strval($this->data['Thickness']));
        $arr['LineSpace'] = floatval($this->data['LineSpace']);
        $arr['MinHoleSize'] = floatval($this->data['MinHoleSize']);
        $arr['BoardColor'] = trim(strval($this->data['BoardColor']));
        $arr['FontColor'] = trim(strval($this->data['FontColor']));
        $arr['Surface'] = trim(strval($this->data['Surface']));
        $arr['TestType'] = trim(strval($this->data['TestType']));
        $arr['ViaProcess'] = isset($this->data['ViaProcess']) ? trim(strval($this->data['ViaProcess'])) : '';
        $arr['CopperThickness'] = floatval($this->data['CopperThickness']);
        $arr['InsideCopperThickness'] = floatval($this->data['InsideCopperThickness']);
        $arr['DesignNum'] = intval($this->data['DesignNum']);
//        $arr['BillType'] = intval($this->data['BillType']);
        $arr['IsJiaji'] = boolval($this->data['IsJiaji']);
        $arr['SpHalfHole'] = isset($this->data['SpHalfHole']) ?  boolval($this->data['SpHalfHole']) : false;
        $arr['SpGoldfingers'] = isset($this->data['SpGoldfingers']) ?  boolval($this->data['SpGoldfingers']) : false;
        $arr['SpGoldfingersBevelling'] =isset($this->data['SpGoldfingersBevelling']) ? trim(strval($this->data['SpGoldfingersBevelling'])) : '';
        $arr['SpULMarking'] =isset($this->data['SpULMarking']) ? trim(strval($this->data['SpULMarking'])) : '';
        $this->fieldArr = $arr;
        unset($arr);
    }
}