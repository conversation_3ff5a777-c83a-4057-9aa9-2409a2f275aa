<?php
namespace Pcb\Service;
use Pcb\Service\Guzzle;



class AAA{


    public function __construct(Guzzle $guzzle)
    {
        $this->guzzle = $guzzle;
    }

    public  function aaa(){
        $pcb_order = new \Pcb\Model\PcbSupplier;
        $b = $pcb_order->create(
            [
                'supplier_name'=>'张三',
                'supplier_address'=>'深圳市南山区',
                'supplier_tel'=>'15896359865',
                'web'=>'www.baidu.com',
            ]
        );
    }
}