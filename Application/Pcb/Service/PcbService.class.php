<?php
namespace Pcb\Service;
use Pcb\Service\Guzzle;
use Exception;
use GuzzleHttp\Exception\RequestException;
use Pcb\Service\Service;
use Pcb\Service\PcbValidator;
class PcbService extends  Service
{

    public function __construct(Guzzle $guzzle)
    {
        parent::__construct($guzzle);
    }



    /*
     * 计价请求
     */
    public function quote($data)
    {
        try{
            $response = $this->client->post('api/Pcb/Quote', $this->setPostData($data));
            $response_code = $response->getStatusCode();
            $ret = \GuzzleHttp\json_decode($response->getBody()->getContents(), true);
            if(!$ret['success']) throw new Exception($ret['msg'],'190003');
            return $ret['priceList'];
        }catch(RequestException $e){
            throw new \Exception($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new \Exception($e->getMessage(),$e->getCode());
        }

    }

    /**
     * 报价验证
     * @param $data
     * @return mixed
     * @throws \Exception
     */
    public function quoteValidatorDatas($data){
        try{
            $checkData = LaravelApp(PcbValidator::class)->setData($data);
            $ruleMessage = $checkData->getMessage();
//            dump($ruleMessage);
            if($ruleMessage !== true){
                if(is_object($ruleMessage)){
                    throw new Exception($ruleMessage->first(),190001);
                }
                throw new Exception("字段类型错误",190001);
            }
            $fields = $checkData->fieldArr;
            return $fields;
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    //获取报价列表
    public function getPcbPrice($fields){
        try{
            $getPrice = $this->quote($fields);
            if(!is_array($getPrice) || empty($getPrice) || count($getPrice) < 1){
                throw new \Exception('暂无对应周期报价',190031);
            }
            $getPrice = collect($getPrice)->unique();


            $getPrice = $getPrice->sortBy(function($arr,$key){
                return floatval($arr['days']);
            });
            $getPrice = $getPrice->sortBy(function($arr,$key){
                return $arr['days'] == false;
            });
            return array_merge($getPrice->toArray());
        }catch(Exception $e){
            throw new \Exception($e->getMessage(),$e->getCode());
        }
    }

    //获取最终报价  获取最接近的报价
    public function getOnePrice($price=[],$day,$isJiaji=0){
        if(!is_array($price) || empty($price) ||  count($price) < 1){
            throw new Exception("暂无对应周期报价",190031);
        }
        $arr = [];
        foreach($price as $key=>$value){

            if(floatval($value['days']) === floatval($day) && boolval($isJiaji) === boolval($value['isJiaji'])){
                $arr =  $value;
                break;
            }
        }
        if(empty($arr)) throw new Exception("暂无对应周期报价",190031);
        return $arr;
    }


    /**
     * 发送积分消息
     */
    public function sendMessage($kefuId,$user_id,$order_id){
        call_user_func_array([$this,'sendJiFen'],[$user_id,$order_id]);
    }

    public function sendJiFen($user_id,$order_id){
        $RbmqModel = D('Common/Rbmq');
        $push_data = array(
            'user_id' => $user_id,//用户id
            'flow_type' => 1,//积分流向 订单完成固定为1
            'flow_reason_type' => 3,//积分流向原因 订单固定为3
            'flow_pf' => 1, //平台'1PC 2H5 3小程序 4后台人工调整'
            'flow_extra_id' => $order_id,//order_id
        );
        $RbmqModel->queue(C('QUEUE_MKT_POINT'))->push($push_data, C('QUEUE_MKT_POINT'));
    }

}