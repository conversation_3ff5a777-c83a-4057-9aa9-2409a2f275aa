<?php
namespace Pcb\Service;


class Service
{
    public function __construct(Guzzle $guzzle)
    {
        $this->guzzle = $guzzle;
        $this->http = C("pcb_api_url")[C("APP_ENV")];
        $this->client = $this->getClient();
    }

    protected function getClient()
    {
        $client = call_user_func_array([$this->guzzle, 'getGuzzle'], [$this->http]);
        if ($client == false) {
            throw new \Exception('创建请求失败', '190002');
        }
        return $client;
    }

    protected function setHttpHeader(){
        return [
            'api-key'=>C("api_key")[C("APP_ENV")],
            'Content-Type'=>'application/json',
        ];
    }


    protected function setPostData($data,$type='json',$cookie=[]){
        if($type === 'json'){
            return [
                'headers' => $this->setHttpHeader(),
                'json' => $data,
                'cookies' => $cookie,
            ];
        }else{
            return [
                'headers' => $this->setHttpHeader(),
                'body' => $data,
                'cookies' => $cookie,
            ];
        }
    }

}