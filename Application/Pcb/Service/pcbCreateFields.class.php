<?php
/**
 * Created by PhpStorm.
 * User: ICHUNT
 * Date: 2018/12/21
 * Time: 10:11
 */

namespace Pcb\Service;


class pcbCreateFields
{

    //添加订单生成订单字段
    public function getOrderFields($preData){
        //生成订单号
        $orderData = [];
        $amount = floatval(array_get($preData,'price.price'));
        $orderData['order_sn'] = array_get($preData,'other.order_sn');
        $order_rate = floatval(C('order_rate'));
        $orderData['order_amount'] = floatval($amount+$amount*$order_rate);
        $orderData['integral'] = ceil($orderData['order_amount']);//积分 一元一分
        $orderData['status'] = 1;//订单状态
        $orderData['purchase_status'] = 0;//采购状态
        $user_id = cookie('uid') ? cookie('uid') :********;
        $orderData['user_id'] = $user_id;//测试用-用户id
        $userInfo = $this->getUserInfo($user_id);
        $orderData['user_account'] = $userInfo ? $userInfo->mobile:'';//测试用-用户账户
        $orderData['order_type'] = intval(array_get($preData,'pcdData.Qty'),0) < 50 ? 1 : 2;//订单类型 1样板 2小批量
        $orderData['adtag'] = $preData['other']['adtag'];array_get($preData,'');
        $orderData['ptag'] = $preData['other']['ptag'];
        $orderData['order_remark'] = trim(array_get($preData,'other.note',''));//备注
        //发货方式
        $order_shipping_type = intval(array_get($preData,'other.order_shipping_type'));//发货方式 1 快递  2自提
        $orderData['order_shipping_type'] = in_array($order_shipping_type,[1,2]) ? $order_shipping_type : 2;
        //发票信息
        $orderData['invoice_type'] = 0;//发票类型默认为0
        return $orderData;
    }

    //修改订单生成订单字段
    public function getEditOrderFields($preData){
        //生成订单号
        $orderData = [];
        $amount = floatval(array_get($preData,'price.price'));
        $order_rate = floatval(C('order_rate'));
        $orderData['order_amount'] = floatval($amount+$amount*$order_rate);
        $orderData['integral'] = ceil($orderData['order_amount']);//积分 一元一分
        $orderData['status'] = 1;//订单状态
        $orderData['purchase_status'] = 0;//采购状态
        $orderData['order_type'] = intval($preData['Qty']) < 50 ? 1 : 2;//订单类型 1样板 2小批量
        $orderData['adtag'] = $preData['other']['adtag'];array_get($preData,'');
        $orderData['ptag'] = $preData['other']['ptag'];
        $orderData['order_remark'] = trim(array_get($preData,'other.note',''));//备注
        $orderData['confirm_time'] = 0;//确认时间
        $orderData['cancel_time'] = 0;//取消时间
        //发货方式
        $order_shipping_type = intval(array_get($preData,'other.order_shipping_type'));//发货方式 1 快递  2自提
        $orderData['order_shipping_type'] = in_array($order_shipping_type,[1,2]) ? $order_shipping_type : 2;
        //发票信息
        $orderData['invoice_type'] = 0;
        if(array_get($preData,'other.order_invoice_id',0) > 0){
            $invoiceObj = (new  \Pcb\Model\PcbOrderInvoice)->where("order_id",array_get($preData,'order.order_id'))->first();
            $orderData['invoice_type'] = $invoiceObj ? ($invoiceObj->inv_type ? $invoiceObj->inv_type : 1) : 1;
        }
        return $orderData;
    }


    public function getUserInfo($user_id){
        $LiexinUserMainModel = new \Pcb\Model\LiexinUserMain;
        return $LiexinUserMainModel->where("user_id",$user_id)->first();
    }

    //生成订单扩展信息表数据
    public function getOrderInfoFields($preData){
        $orderInfoData = [];
        $orderInfoData['order_id'] = 0;
        $orderInfoData['order_sn'] = '';
        //技术联系人
        $orderInfoData['technical_contact'] = trim(array_get($preData,'other.technology.technology_contact',''));
        $orderInfoData['technical_contact_tel'] = trim(array_get($preData,'other.technology.technical_contact_tel',''));
        $orderInfoData['days'] = floatval(array_get($preData,'price.days',0));//天数
        $orderInfoData['note'] = trim(array_get($preData,'other.note',''));
        //pcb文件
        $orderInfoData['file_name'] = trim(array_get($preData,'other.pcb_file.FileName',''));
        $orderInfoData['file_path'] = trim(array_get($preData,'other.pcb_file.FilePath',''));
        $orderInfoData['layer'] = intval(array_get($preData,'pcdData.Layer',0));//板子层数
        $orderInfoData['length'] = floatval(array_get($preData,'pcdData.Length',0));//长
        $orderInfoData['width'] = floatval(array_get($preData,'pcdData.Width',0));//宽
        $orderInfoData['qty'] = intval(array_get($preData,'pcdData.Qty',0));//数量
        $orderInfoData['material'] = trim(array_get($preData,'pcdData.Material',''));//板材
        $orderInfoData['thickness'] = floatval(array_get($preData,'pcdData.Thickness',0));//厚度，单位mm
        $orderInfoData['line_space'] = floatval(array_get($preData,'pcdData.LineSpace',0));//宽/线距,单位mil
        $orderInfoData['line_space'] = floatval(array_get($preData,'pcdData.LineSpace',0));//宽/线距,单位mil
        $orderInfoData['min_hole_size'] = floatval(array_get($preData,'pcdData.MinHoleSize',0));//厚度,单位mm
        $orderInfoData['board_color'] = trim(array_get($preData,'pcdData.BoardColor',''));//阻焊颜色
        $orderInfoData['font_color'] = trim(array_get($preData,'pcdData.FontColor',''));//字符颜色
        $orderInfoData['surface'] = trim(array_get($preData,'pcdData.Surface',''));//表面处理
        $orderInfoData['test_type'] = trim(array_get($preData,'pcdData.TestType',''));//测试
        $orderInfoData['via_process'] = trim(array_get($preData,'pcdData.ViaProcess',''));//阻焊覆盖
        $orderInfoData['copper_thickness'] = floatval(array_get($preData,'pcdData.CopperThickness',0));//铜厚
        $inside_copper_thickness = array_get($preData,'pcdData.InsideCopperThickness',0);
        $inside_copper_thickness = $inside_copper_thickness > 0 ? $inside_copper_thickness : 0;
        $orderInfoData['inside_copper_thickness'] = floatval($inside_copper_thickness);//内层铜厚
        $orderInfoData['design_num'] = intval(array_get($preData,'pcdData.DesignNum',0));//设计文件款数
        $orderInfoData['bill_type'] = intval(array_get($preData,'pcdData.BillType',0));//发票
        $orderInfoData['is_jiaji'] = intval(array_get($preData,'pcdData.IsJiaji',0));//是否加急
        $orderInfoData['sp_half_hole'] = intval(array_get($preData,'pcdData.SpHalfHole',0));//特殊技参数-是否有半孔工艺
        $orderInfoData['sp_goldfingers'] = intval(array_get($preData,'pcdData.SpGoldfingers',0));//特殊技参数-是否要金手指
        $orderInfoData['sp_goldfingers_bevelling'] = trim(array_get($preData,'pcdData.SpGoldfingersBevelling',''));//特殊技参数-金手指斜边
        $orderInfoData['sp_UL_marking'] = trim(array_get($preData,'pcdData.SpULMarking',''));//特殊技参数-UL标记
        return $orderInfoData;
    }

    //生成费用明细数据字段
    public function getOrderAuditDetailField($preData){
        $OrderAuditDetailField = [];
        $OrderAuditDetailField['order_id']=intval(array_get($preData,'order.order_id',0));
        $OrderAuditDetailField['order_sn']=trim(array_get($preData,'order.order_sn',''));
        $OrderAuditDetailField['text']=trim(array_get($preData,'price.text',''));#显示值 如2-3天
        $OrderAuditDetailField['pcb_gcf_net']=floatval(array_get($preData,'price.costGcf',0));//工程费
        $OrderAuditDetailField['pcb_bf_net']=floatval(array_get($preData,'price.costBoard',0));//版费
        $OrderAuditDetailField['pcb_pinban_net']=floatval(array_get($preData,'price.costPbf',0));//拼板费
        $OrderAuditDetailField['pcb_flf_net']=floatval(array_get($preData,'price.costFilm',0));//菲林费
        $OrderAuditDetailField['pdf_net']=floatval(array_get($preData,'price.costSurface',0));//喷镀费/表面处理
        $OrderAuditDetailField['pcb_ysf_net']=floatval(array_get($preData,'price.costColor',0));//颜色费
        $OrderAuditDetailField['pcb_ccf_net']=floatval(array_get($preData,'price.costTest',0));//测试费
        $OrderAuditDetailField['pcb_jjf_net']=floatval(array_get($preData,'price.costUrgent',0));//加急费
        $OrderAuditDetailField['pcb_kdf_net']=floatval(array_get($preData,'price.costShip',0));//快递费
        $OrderAuditDetailField['pcb_other_net']=floatval(array_get($preData,'price.costOther',0));//其它
        $OrderAuditDetailField['pcb_sf_net']=floatval(array_get($preData,'price.costTax',0));//税 费
        $OrderAuditDetailField['price']=floatval(array_get($preData,'price.price',0));//总价格
        $OrderAuditDetailField['price_discount']=floatval(array_get($preData,'price.priceDiscount',0));//已优惠的金额
        $OrderAuditDetailField['discount']=floatval(array_get($preData,'price.discount',0));//已优惠折扣
        return $OrderAuditDetailField;
    }

    //生成收货地址字段
    public function getOrderAddressFields($preData,$order_shipping_type){
        $order_address_id = intval(array_get($preData,'other.order_address_id',0));
        $order_shipping_type = array_get($preData,'other.order_shipping_type',0);//1快递 2自提

        $address = [];
        $address['order_id'] = intval(array_get($preData,'order.order_id',0));
        $address['order_sn'] = trim(array_get($preData,'order.order_sn',''));

        if($order_shipping_type == 1){
            if($order_address_id <= 0) throw new \Exception('未找到用户对应的地址信息',190035);
            $addressModel = new \Pcb\Model\UserAddress;
            $addressModel = $addressModel->where("address_id",$order_address_id)->first();
            if(!$addressModel) throw new \Exception('未找到用户对应的地址信息',190035);
            $address['address_type'] = 1;#1订单地址2发票地址
            $address['consignee'] = trim($addressModel->consignee);//收货人姓名
            $address['zipcode'] = trim($addressModel->zipcode);//邮编
            $address['mobile'] = trim($addressModel->mobile);//手机/电话
            $address['province'] = intval($addressModel->province);//省份
            $address['city'] = intval($addressModel->city);//市
            $address['district'] = intval($addressModel->district);//区
            $address['detail_address'] = trim($addressModel->detail_address);//详细地址
            $address['intl_code'] = trim($addressModel->intl_code);//国际群号
            return $address;
        }else{
            $order_address_ziti_id = array_get($preData,'other.order_address_ziti_id',0);
            $datas = C('SELF_PICK_ADDRESS');
            if ($order_address_ziti_id == 1) {
                $datas = $datas[1];
            } else {
                $datas = $datas[2];
            }
            $address['consignee'] = array_get($preData,'other.order_address_ziti.consignee',0);//收货人姓名
            $address['mobile'] =array_get($preData,'other.order_address_ziti.mobile',0);//手机/电话
            $address['detail_address'] = implode(",",$datas);//详细地址
        }
        return $address;
    }


    //新增收货地址
    public function addUserAddress($data){
        try{
            $user_id = cookie("uid")?cookie("uid"):99999;//99999测试用
            $userAddressModel = new \Pcb\Model\UserAddress;
            $mobile = get_inte_mobile(trim($data['mobile']), trim($data['intl_code']));
            if (!is_mobile($mobile)) {
                throw new \Exception('手机号码格式错误，请重新输入','11006');
            }
            //大于20个地址就不添加了
            $addressnums = $userAddressModel->where('address_type',0)->where("user_id",$user_id)->count();
            if($addressnums > 20){
                return false;
            }

            $order_address = I('order_address',[]);
            $is_default = isset($order_address['is_default']) ? intval($order_address['is_default']) : 0;
            $is_default = in_array($is_default,[0,1]) ?$is_default:0;
            $bk = $userAddressModel->create([
                'user_id'=>$user_id,
                'consignee'=>$data['consignee'],
                'zipcode'=>$data['zipcode'],
                'mobile'=>$data['mobile'],
                'province'=>$data['province'],
                'city'=>$data['city'],
                'district'=>$data['district'],
                'detail_address'=>$data['detail_address'],
                'is_default'=>$is_default,//是否默认（1=是|0=否）
                'address_type'=>0,//地址类型（1-发货地址，0-收货地址）
                'intl_code'=>$data['intl_code'],
            ]);
            if($is_default == 1 && $bk){
                $userAddressModel->where("user_id",$user_id)->where("address_type",0)->where("address_id","!=",$bk->address_id)->update(['is_default'=>0]);
            }
        }catch(\Exception $e){
//            throw new \Exception($e->getMessage(),'190006');
            throw new \Exception('新增收货地址失败','190006');
        }

    }

    //生成发票字段
    public function getOrderInvoiceFields($preData){
        $tax_id = array_get($preData,'other.order_invoice_id');
        $taxModel = new \Pcb\Model\TaxInfo;
        $tax = $taxModel->findOrFail(intval($tax_id))->toArray();
        $needFields = collect($tax)->except(['tax_id','user_id','is_default','is_self']);
        $createNeedFields = [];
        $createNeedFields = $needFields->toArray();
        $createNeedFields['order_id']=intval(array_get($preData,'order.order_id',0));
        $createNeedFields['order_sn']=trim(array_get($preData,'order.order_sn',''));
//        dump($createNeedFields);
        return $createNeedFields;
    }

    /**
     * 天数转换
     */
    public function getDaysVal($days){
        $days = floatval($days);
        $str = '';
        if($days == 0.5){
            $str .= "12小时加急";
        }elseif($days == 1){
            $str .= "24小时加急";
        }else{
            $str .= "正常".ceil($days)."-".(ceil($days)+1)."天";
        }
        return $str;
    }

    /**
     * 生产工艺
     */
    public function getSpVal($data){
        $str = [];
        if($data['order_info']['sp_goldfingers'] && $data['order_info']['sp_goldfingers_bevelling']){
            $str[] = "金手指 ".$data['order_info']['sp_goldfingers_bevelling'];
        }

        if($data['order_info']['sp_half_hole']){
            $str[] = $data['order_info']['sp_half_hole'] ? '半孔工艺':'';
        }

        if($data['order_info']['sp_UL_marking']){
            $str[] = "UL标记:".$data['order_info']['sp_UL_marking'];
        }

        
        return $str;
    }


    //设置订单显示时间
    public function setOrderTime($data){
        if(in_array($data['status'],[2,3])){
            $data['confirm_time'] = 0;//确认时间
            $data['shipping_time'] = 0;//发货时间
            $data['receive_delivery_time'] = 0;//确认收货的时间
            $data['cancel_time'] = 0;//取消时间
            $data['production_time'] = 0;//生产完成的时间
            $data['finish_time'] = 0;//完成时间
            $data['pay_time'] = 0;//支付时间
        }elseif(in_array($data['status'],[4,10])){
            $data['shipping_time'] = 0;//发货时间
            $data['receive_delivery_time'] = 0;//确认收货的时间
            $data['cancel_time'] = 0;//取消时间
            $data['production_time'] = 0;//生产完成的时间
            $data['finish_time'] = 0;//完成时间
            $data['pay_time'] = 0;//支付时间
        }
        return $data;
    }


}