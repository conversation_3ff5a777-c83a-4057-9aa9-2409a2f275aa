<?php
namespace Pcb\Service;
use Pcb\Service\Guzzle;
use Exception;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Database\Capsule\Manager as DB;
use Pcb\Service\Service;
use Pcb\Service\PcbValidator;
use Pcb\Model\PcbPurchase;
use Pcb\Model\PcbPurchaseInfo;
use Pcb\Model\PurchaseAuditDetail;
use Pcb\Model\PcbOrder;
use Pcb\Model\PcbOrderInfo;
use Pcb\Model\PcbOrderAuditDetail;


class AdminapiService extends  Service
{

    public $purchaseData = [];
    public $purchaseInfoData = [];
    public $purchaseAuditDetailData = [];
    public function __construct(Guzzle $guzzle)
    {
        parent::__construct($guzzle);
        $this->pcbPurchaseModel = new PcbPurchase;
        $this->pcbPurchaseInfoModel = new PcbPurchaseInfo;
        $this->purchaseAuditDetailModel = new PurchaseAuditDetail;
    }

    /**
     * 生成采购单
     */
    public function addPurchase(){

        if(empty($this->purchaseData) || empty($this->purchaseInfoData) || empty($this->purchaseAuditDetailData)){
            throw new \Exception('生成采购单失败','190020');
        }
        $purchase = null;
        $purchase = call_user_func_array([$this,'addPurchaseTransaction'],[]);
        if(!$purchase) throw new \Exception('生成采购单失败','190020');
        return $purchase;
    }

    //添加采购单事务
    protected function addPurchaseTransaction(){
        $purchase = null;
        DB::transaction(function()use(&$purchase){
            $order_id = $this->purchaseData['order_id'];
            //新增还是修改采购单
            $isAddOrEdit = !!($this->pcbPurchaseModel->where("order_id",$order_id)->count());
            //添加采购单数据
            if($isAddOrEdit){
                //二次发起采购不需要更新这些信息
                if(isset($this->purchaseData['purchase_sn'])) unset($this->purchaseData['purchase_sn']);
                if(isset($this->purchaseData['order_id'])) unset($this->purchaseData['order_id']);
                if(isset($this->purchaseData['order_sn'])) unset($this->purchaseData['order_sn']);
            }
            $pcbPurchaseObj =  $this->pcbPurchaseModel->updateOrCreateData(['order_id'=>$order_id],$this->purchaseData);
            if(!$pcbPurchaseObj || ($pcbPurchaseObj instanceof PcbPurchase) == false){
                throw new \Exception('生成采购单失败','190020');
            }
            //如果是更新 则返回的不是对象 而是bool  此时重新获取对象
            if(($pcbPurchaseObj instanceof PcbPurchase) == false){
                $pcbPurchaseObj = $this->pcbPurchaseModel->where("order_id",$order_id)->first();
                //如果没有找到对应数据 则抛出异常
                if(($pcbPurchaseObj instanceof PcbPurchase) == false){
                    throw new \Exception('生成采购单失败','190020');
                }
            }


            //添加采购单附表数据
            $this->purchaseInfoData['purchase_id'] = $pcbPurchaseObj->purchase_id;
            $this->purchaseInfoData['purchase_sn'] = $pcbPurchaseObj->purchase_sn;
            $pcbPurchaseInfoObj =  $this->pcbPurchaseInfoModel->updateOrCreateData(['order_id'=>$order_id],$this->purchaseInfoData);
            if(!$pcbPurchaseInfoObj || ($pcbPurchaseInfoObj instanceof PcbPurchaseInfo) == false){
                throw new \Exception('生成采购单-附表失败','190021');
            }

            //添加采购单费用明细数据
            $this->purchaseAuditDetailData['purchase_id'] = $pcbPurchaseObj->purchase_id;
            $this->purchaseAuditDetailData['purchase_sn'] = $pcbPurchaseObj->purchase_sn;
            $purchaseAuditDetailObj =  $this->purchaseAuditDetailModel->updateOrCreateData(['order_id'=>$order_id],$this->purchaseAuditDetailData);
            if(!$purchaseAuditDetailObj || ($purchaseAuditDetailObj instanceof PurchaseAuditDetail) == false){
                throw new \Exception('生成采购单-费用明细失败','190022');
            }


            $purchase = $pcbPurchaseObj;
            if(!($purchase instanceof  \Pcb\Model\PcbPurchase)){
                throw new \Exception('生成采购单失败','190020');
            }


            //更新交易订单状态为采购中
            $orderModel = new \Pcb\Model\PcbOrder;
            if(!$orderModel->where("order_id",$order_id)->update(['status'=>2])){
                throw new \Exception('生成采购单成功，更新交易订单失败','190028');
            }

            //向第三方发起订单保存流程
            call_user_func_array([$this,'saveOrder'],[$purchase]);
        });

        return $purchase;
    }

    //添加采购单事务  备用
    protected function editPurchaseTransaction(){
        $purchase = null;
        DB::transaction(function()use(&$purchase){
            //添加采购单数据
            $pcbPurchaseObj =  $this->pcbPurchaseModel->createData($this->purchaseData);
            if(!$pcbPurchaseObj || ($pcbPurchaseObj instanceof PcbPurchase) == false){
                throw new \Exception('生成采购单失败','190020');
            }
            //添加采购单附表数据
            $this->purchaseInfoData['purchase_id'] = $pcbPurchaseObj->purchase_id;
            $this->purchaseInfoData['purchase_sn'] = $pcbPurchaseObj->purchase_sn;
            $pcbPurchaseInfoObj =  $this->pcbPurchaseInfoModel->createData($this->purchaseInfoData);
            if(!$pcbPurchaseInfoObj || ($pcbPurchaseInfoObj instanceof PcbPurchaseInfo) == false){
                throw new \Exception('生成采购单-附表失败','190021');
            }

            //添加采购单费用明细数据
            $this->purchaseAuditDetailData['purchase_id'] = $pcbPurchaseObj->purchase_id;
            $this->purchaseAuditDetailData['purchase_sn'] = $pcbPurchaseObj->purchase_sn;
            $purchaseAuditDetailObj =  $this->purchaseAuditDetailModel->createData($this->purchaseAuditDetailData);
            if(!$purchaseAuditDetailObj || ($purchaseAuditDetailObj instanceof PurchaseAuditDetail) == false){
                throw new \Exception('生成采购单-费用明细失败','190022');
            }


            $purchase = $pcbPurchaseObj;
            if(!($purchase instanceof  \Pcb\Model\PcbPurchase)){
                throw new \Exception('生成采购单失败','190020');
            }


            //更新交易订单状态为采购中
            $orderModel = new \Pcb\Model\PcbOrder;
            if(!$orderModel->where("order_id",$pcbPurchaseObj->order_id)->update(['status'=>2])){
                throw new \Exception('生成采购单成功，更新交易订单失败','190028');
            }

            //向第三方发起订单保存流程
            call_user_func_array([$this,'saveOrder'],[$purchase]);
        });

        return $purchase;
    }




    /**
     * 生成采购单发起第三方订单保存
     */
    public function saveOrder($purchase){
        $ret = call_user_func_array([$this,'JdbSaveOrder'],[$purchase]);
        if($ret['success'] !== true){
            throw new \Exception('保存PCB订单到第三方失败','190028');
        }
        if($ret['jdbOrderNo']){
            $bk = (new \Pcb\Model\PcbPurchase)->updatejdbOrderNo($purchase->purchase_id,trim($ret['jdbOrderNo']));
            if(!$bk){
                throw new \Exception('保存PCB订单到第三方失败','190028');
            }
        }
    }


    /**
     * 准备Purchase数据
     */
    public function setPurchaseData($order){
        try{
            $pcbPurchase = new PcbPurchase;
            $purchaseData = [];
            $purchaseData['purchase_sn'] = $pcbPurchase->findSn(C('purchase_sn_prefix'));
            $purchaseData['order_id'] = $order->order_id;
            $purchaseData['order_sn'] = $order->order_sn;
            $purchaseData['order_amount'] = $order->order_amount;
            $purchaseData['purchase_amount'] = 0;
            $purchaseData['supplier_id'] = C('supplier_id');//供应商id
            $purchaseData['status'] = 2;
            $purchaseData['user_id'] = $order->user_id;
            $purchaseData['user_account'] = $order->user_account;
            $purchaseData['order_source_type'] = $order->order_source_type;
            $purchaseData['order_type'] = $order->order_type;
            $purchaseData['adtag'] = $order->adtag;
            $purchaseData['ptag'] = $order->ptag;
            $purchaseData['is_return_order'] = $order->is_return_order;
            $purchaseData['pay_time'] = $order->pay_time;
            $purchaseData['order_shipping_type'] = $order->order_shipping_type;
            $purchaseData['invoice_type'] = $order->invoice_type;
            $purchaseData['currency'] = $order->currency;
            $purchaseData['sale_id'] = $order->sale_id;
            $purchaseData['purchase_amount'] = 0;//调整后金额 二次发起采购
            $purchaseData['pay_time'] = 0;//支付时间 二次发起采购
            $purchaseData['deliveryDate'] = 0;//生产周期 二次发起采购
            $purchaseData['confirm_time'] = 0;//审核时间 二次发起采购
            $purchaseData['shipping_time'] = 0;//审核时间 二次发起采购
            $purchaseData['cancel_time'] = 0;//审核时间 二次发起采购
            $purchaseData['order_create_time'] = $order->create_time->timestamp;//交易订单下单时间
            $this->purchaseData = $purchaseData;
        }catch(\Exception $e){
            $this->purchaseData = [];
        }

        return $this;
    }

    /**
     * 准备数据data
     */
    public function setPurchaseInfoData($orderInfo){
        try{
            $exceptData = collect($orderInfo)->except(['id','create_time','update_time']);
            //复制订单info数据到采购单
            if($exceptData->count() !=  30) throw new \Exception('生成采购单扩展数据失败','190012');
            $this->purchaseInfoData = $exceptData->toArray();
        }catch(\Exception $e){
            $this->purchaseInfoData = [];
        }
        return $this;
    }

    /**
     * 订单明细数据
     */
    public function setPurchaseAuditDetailData($orderAuditDetailData){
        try{
            $exceptData = collect($orderAuditDetailData)->except(['id','create_time','update_time']);
            //复制订单info数据到采购单
            if($exceptData->count() !=  17) throw new \Exception('生成采购单费用明细失败','190011');
            $this->purchaseAuditDetailData = $exceptData->toArray();
        }catch(\Exception $e){
            $this->purchaseAuditDetailData = [];
        }
        return $this;
    }


    /**
     * 向jdb更新订单数据
     * @param $pcbPurchase
     */
    public function JdbUpdateOrder($pcbPurchase){
        $data = $this->JdbUpdateOrderFields($pcbPurchase);
        try{
            $response = $this->client->post('api/Pcb/UpdateOrder', $this->setPostData($data));
            $response_code = $response->getStatusCode();
            $ret = \GuzzleHttp\json_decode($response->getBody()->getContents(), true);
            if(!$ret['success']) throw new \Exception($ret['msg'],'190028');
            return $ret;
        }catch(RequestException $e){
            throw new \Exception($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new \Exception($e->getMessage(),$e->getCode());
        }
    }


    /**
     * 向jdb保存订单 更新订单
     * @param $pcbPurchase
     */
    public function JdbSaveOrder($pcbPurchase){
        $data = $this->JdbSaveOrderFields($pcbPurchase);
        try{
            $response = $this->client->post('api/Pcb/Save', $this->setPostData($data));
            $response_code = $response->getStatusCode();
            $ret = \GuzzleHttp\json_decode($response->getBody()->getContents(), true);
            if(!$ret['success']) throw new \Exception($ret['msg'],'190028');
            return $ret;
        }catch(RequestException $e){
            throw new \Exception($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new \Exception($e->getMessage(),$e->getCode());
        }
    }


    /**
     * 采购确认
     * 审核结果均同步到交易订单中，且订单状态变为待支付
     * @param $pcbPurchase
     * @return array
     * @throws \Exception
     */
    public function confirmPcbOrder($purchase_id){
        $pcbPurchase = new PcbPurchase;
        try{
            $order = $pcbPurchase->findOrFail($purchase_id);

            if(!$order) throw new \Exception('采购单不存在','190029');
            if(!$order->jdb_order_sn) throw new \Exception('第三方订单号不存在','190029');
            if(!in_array($order->status,[2,3])) throw new \Exception('采购单当前状态无法操作','190029');

        }catch(\Exception $e){
            throw new \Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     * 确认PCB订单，确认过后，订单将进入生产  模拟数据
     * @param $pcbPurchase
     * @return array
     * @throws \Exception
     */
    public function confirmOrder($msg){
        $pcbPurchase = new PcbPurchase;
        try{
            $msg = json_decode($msg,true);
            //订单生产中
            PcbOrder::where("order_sn",$msg['order_sn'])->update(['status'=>12]);
            //交易单生产完成
            PcbPurchase::where("order_sn",$msg['order_sn'])->update(['status'=>13]);
            //修改相关时间字段
        }catch(RequestException $e){
            throw new \Exception($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new \Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     * 同步采购单数据到交易单
     * 订单附表数据 总金额 交货时间 费用明细
     * 状态变更 待支付/确认不通过
     */
    public function syncOrder($purchase_id,$isok){
        $pcbPurchase = new PcbPurchase;
        try{
            $pcbPurchaseOrder = $pcbPurchase->findOrFail($purchase_id);
            if(!$pcbPurchaseOrder) throw new \Exception('采购单不存在','190029');
            if(!$pcbPurchaseOrder->jdb_order_sn) throw new \Exception('第三方订单号不存在','190029');
            if(!in_array($pcbPurchaseOrder->status,[2,3])) throw new \Exception('采购单当前状态无法操作','190029');
            DB::transaction(function()use($pcbPurchaseOrder,$isok){
                //修改采购单状态为待支付,不通过
                call_user_func_array([$this,'syncPurchaseStatus'],[$pcbPurchaseOrder,$isok]);
                //同步采购单数据到交易订单 金额 交货时间 状态 确认时间
                call_user_func_array([$this,'syncOrderData'],[$pcbPurchaseOrder,$isok]);
                //审核通过
                if($isok > 0){
                    //同步订单附表
                    call_user_func_array([$this,'syncOrderInfoData'],[$pcbPurchaseOrder]);

                    //同步订单费用明细
                    call_user_func_array([$this,'syncAuditDetailData'],[$pcbPurchaseOrder]);
                }
            });
        }catch(\Exception $e){
            throw new \Exception($e->getMessage(),$e->getCode());
        }
    }


    //修改采购单状态为待支付
    private function syncPurchaseStatus($pcbPurchaseOrder,$isok){
        if($isok <= 0){
            //不通过
            $pcbPurchaseOrder->status=4;//确认不通过
        }else{
            $pcbPurchaseOrder->order_amount=$pcbPurchaseOrder->purchase_amount;//订单金额=调整后金额
            $pcbPurchaseOrder->status=5;//确认通过
        }
        if(!$pcbPurchaseOrder->save()){
            throw new \Exception('采购单状态更新失败','190029');
        }
    }


    //同步订单数据
    private function syncOrderData($pcbPurchaseOrder,$isok){
        $arr = [];
        if($isok > 0 && $pcbPurchaseOrder->purchase_amount > 0){
            $arr['order_amount'] = floatval($pcbPurchaseOrder->purchase_amount);
            $arr['integral'] = intval($pcbPurchaseOrder->purchase_amount);
        }
        $arr['status'] = $isok > 0 ? 10 : 4;
        $arr['purchase_status'] = $pcbPurchaseOrder->status;
        $arr['order_type'] = $pcbPurchaseOrder->order_type;
        $arr['confirm_time'] = time();
        $res = (new PcbOrder)->where("order_id",$pcbPurchaseOrder->order_id)->update($arr);
        if(!$res){
            throw new \Exception('交易订单数据更新失败','190029');
        }
    }

    //同步订单附表
    private function syncOrderInfoData($pcbPurchaseOrder){
        $purchaseInfo = $pcbPurchaseOrder->purchaseInfo;
        if(!$purchaseInfo || !($purchaseInfo instanceof  PcbPurchaseInfo)){
            throw new \Exception('获取采购单扩展表信息失败','190027');
        }
        $orderInfo = (new PcbOrderInfo)->where("order_id",$pcbPurchaseOrder->order_id)->first();
        if(!$orderInfo) throw new \Exception('获取交易单扩展表信息失败','190027');
        $orderInfo->days = $purchaseInfo->days;
        $orderInfo->note = $purchaseInfo->note;
        $orderInfo->file_name = $purchaseInfo->file_name;
        $orderInfo->file_path = $purchaseInfo->file_path;
        $orderInfo->layer = $purchaseInfo->layer;
        $orderInfo->length =$purchaseInfo->length;
        $orderInfo->width = $purchaseInfo->width;
        $orderInfo->qty =  $purchaseInfo->qty;
        $orderInfo->material = $purchaseInfo->material;
        $orderInfo->thickness = $purchaseInfo->thickness;
        $orderInfo->line_space = $purchaseInfo->line_space;
        $orderInfo->min_hole_size = $purchaseInfo->min_hole_size;
        $orderInfo->board_color = $purchaseInfo->board_color;
        $orderInfo->font_color = $purchaseInfo->font_color;
        $orderInfo->surface =  $purchaseInfo->surface;
        $orderInfo->test_type = $purchaseInfo->test_type;
        $orderInfo->via_process = $purchaseInfo->via_process;
        $orderInfo->copper_thickness = $purchaseInfo->copper_thickness;
        $orderInfo->inside_copper_thickness = $purchaseInfo->inside_copper_thickness;
        $orderInfo->design_num = $purchaseInfo->design_num;
        $orderInfo->bill_type = $purchaseInfo->bill_type;
        $orderInfo->is_jiaji = $purchaseInfo->is_jiaji;
        $orderInfo->sp_half_hole = $purchaseInfo->sp_half_hole;
        $orderInfo->sp_goldfingers = $purchaseInfo->sp_goldfingers;
        $orderInfo->sp_goldfingers_bevelling = $purchaseInfo->sp_goldfingers_bevelling;
        $orderInfo->sp_UL_marking = $purchaseInfo->sp_UL_marking;
        if(!$orderInfo->save()){
            throw new \Exception('更新交易单扩展表信息失败','190027');
        }
    }


    //同步费用明细
    private function syncAuditDetailData($pcbPurchaseOrder){
        $purAuditDetail = $pcbPurchaseOrder->purAuditDetail;
        if(!$purAuditDetail || !($purAuditDetail instanceof  PurchaseAuditDetail)){
            throw new \Exception('获取交易订单费用明细失败','190027');
        }
        $auditDetail = (new PcbOrderAuditDetail)->where("order_id",$pcbPurchaseOrder->order_id)->first();
        if(!$auditDetail) throw new \Exception('获取交易订单费用明细失败','190027');
        $auditDetail->price = $purAuditDetail->price;
        $auditDetail->price_discount = $purAuditDetail->price_discount;
        $auditDetail->discount = $purAuditDetail->discount;
        $auditDetail->pcb_gcf_net = $purAuditDetail->pcb_gcf_net;
        $auditDetail->pcb_bf_net = $purAuditDetail->pcb_bf_net;
        $auditDetail->pcb_pinban_net = $purAuditDetail->pcb_pinban_net;
        $auditDetail->pcb_flf_net = $purAuditDetail->pcb_flf_net;
        $auditDetail->pdf_net = $purAuditDetail->pdf_net;
        $auditDetail->pcb_ysf_net = $purAuditDetail->pcb_ysf_net;
        $auditDetail->pcb_ccf_net = $purAuditDetail->pcb_ccf_net;
        $auditDetail->pcb_jjf_net = $purAuditDetail->pcb_jjf_net;
        $auditDetail->pcb_kdf_net = $purAuditDetail->pcb_kdf_net;
        $auditDetail->pcb_other_net = $purAuditDetail->pcb_other_net;
        $auditDetail->pcb_sf_net = $purAuditDetail->pcb_sf_net;
        $auditDetail->text = $purAuditDetail->text;
        $auditDetail->pcb_other_net = $purAuditDetail->pcb_other_net;
        if(!$auditDetail->save()){
            throw new \Exception('更新交易订单费用明细失败','190027');
        }
    }



    //线下支付
    public function offline_payment($data){
        $orderModel = new PcbOrder;
        $payLogModel = new \Pcb\Model\PcbPayLog;
        $order = $orderModel->findOrFail($data['order_id']);
        if($order->status != 10)  throw new \Exception('当前订单状态不支持该操作','190036');
        if($data['pay_money'] < $order->order_amount) throw new \Exception('付款金额不能小于商品价格','190038');
        $order->status = 11;//支付完成
        $order->pay_time = time();//支付时间
        if(!$order->save()){
            throw new \Exception('操作失败','190036');
        }
        $bk = $payLogModel->createData($this->payLogFields($order,$data));
        if(!$bk) throw new \Exception('操作失败','190036');
    }

    private function payLogFields($order,$data){
        $arr=[];
        $arr['user_id'] = $order->user_id;
        $arr['order_id'] = $order->order_id;
        $arr['order_sn'] = $order->order_sn;
        $arr['pay_name'] = '线下支付';
        $arr['pay_type'] = 1;
        $arr['pay_amount'] = $data['pay_money'];
        $arr['is_paid'] = 1;
        $arr['pay_time'] = time();
        return $arr;
    }


    private function JdbUpdateOrderFields($pcbPurchase){
        $purchaseInfo = $pcbPurchase->purchaseInfo;
        if(!$purchaseInfo || !($purchaseInfo instanceof  PcbPurchaseInfo)){
            throw new \Exception('获取采购单扩展表信息失败','190027');
        }
        $purchaseInfoData = $purchaseInfo;

        $data=[];
        $data['OrderNo'] = $pcbPurchase->purchase_sn;
        $data['Days'] = $purchaseInfoData->days;
        $data['Note'] = $purchaseInfoData->note;
        $data['FileName'] = $purchaseInfoData->file_name;
        $data['FilePath'] = $purchaseInfoData->file_path;
        $data['Layer'] = $purchaseInfoData->layer;
        $data['Length'] = $purchaseInfoData->length;
        $data['Width'] = $purchaseInfoData->width;
        $data['Qty'] = $purchaseInfoData->qty;
        $data['Material'] = $purchaseInfoData->material;
        $data['Thickness'] = $purchaseInfoData->thickness;
        $data['LineSpace'] = $purchaseInfoData->line_space;
        $data['MinHoleSize'] = $purchaseInfoData->min_hole_size;
        $data['BoardColor'] = $purchaseInfoData->board_color;
        $data['FontColor'] = $purchaseInfoData->font_color;
        $data['Surface'] = $purchaseInfoData->surface;
        $data['TestType'] = $purchaseInfoData->test_type;
        $data['ViaProcess'] = $purchaseInfoData->via_process;
        $data['CopperThickness'] = $purchaseInfoData->copper_thickness;
        $data['InsideCopperThickness'] = $purchaseInfoData->inside_copper_thickness;
        $data['DesignNum'] = $purchaseInfoData->design_num;
        $data['BillType'] = intval($purchaseInfoData->bill_type);
        $data['IsJiaji'] = boolval($purchaseInfoData->is_jiaji);
        $data['SpHalfHole'] = boolval($purchaseInfoData->sp_half_hole);
        $data['SpGoldfingers'] = boolval($purchaseInfoData->sp_goldfingers);
        $data['SpGoldfingersBevelling'] = $purchaseInfoData->sp_goldfingers_bevelling;
        $data['SpULMarking'] = $purchaseInfoData->sp_UL_marking;
        return $data;
    }


    private function JdbSaveOrderFields($pcbPurchase){
        $purchaseInfo = $pcbPurchase->purchaseInfo;
        if(!$purchaseInfo || !($purchaseInfo instanceof  PcbPurchaseInfo)){
            throw new \Exception('获取采购单扩展表信息失败','190027');
        }
        $purchaseInfoData = $purchaseInfo;

        $data=[];
        if($pcbPurchase->jdb_order_sn){
            $data['OrderNo'] = trim($pcbPurchase->jdb_order_sn);
        }
        $data['Days'] = $purchaseInfoData->days;
        $data['Note'] = $purchaseInfoData->note;
        $data['FileName'] = $purchaseInfoData->file_name;
        $data['FilePath'] = $purchaseInfoData->file_path;
        $data['Layer'] = $purchaseInfoData->layer;
        $data['Length'] = $purchaseInfoData->length;
        $data['Width'] = $purchaseInfoData->width;
        $data['Qty'] = $purchaseInfoData->qty;
        $data['Material'] = $purchaseInfoData->material;
        $data['Thickness'] = $purchaseInfoData->thickness;
        $data['LineSpace'] = $purchaseInfoData->line_space;
        $data['MinHoleSize'] = $purchaseInfoData->min_hole_size;
        $data['BoardColor'] = $purchaseInfoData->board_color;
        $data['FontColor'] = $purchaseInfoData->font_color;
        $data['Surface'] = $purchaseInfoData->surface;
        $data['TestType'] = $purchaseInfoData->test_type;
        $data['ViaProcess'] = $purchaseInfoData->via_process;
        $data['CopperThickness'] = $purchaseInfoData->copper_thickness;
        $data['InsideCopperThickness'] = $purchaseInfoData->inside_copper_thickness;
        $data['DesignNum'] = $purchaseInfoData->design_num;
        $data['BillType'] = intval($purchaseInfoData->bill_type);
        $data['IsJiaji'] = boolval($purchaseInfoData->is_jiaji);
        $data['SpHalfHole'] = boolval($purchaseInfoData->sp_half_hole);
        $data['SpGoldfingers'] = boolval($purchaseInfoData->sp_goldfingers);
        $data['SpGoldfingersBevelling'] = $purchaseInfoData->sp_goldfingers_bevelling;
        $data['SpULMarking'] = $purchaseInfoData->sp_UL_marking;
        return $data;
    }

}