<?php
namespace Pcb\Controller;
use \Common\Controller\BaseController as Controller;
use Illuminate\Container\Container;

class BaseController extends Controller
{
    public $client =  null;
    public $container = null;
    public static $DB;
    public function _initialize()
    {
        parent::_initialize();

//        require "./vendor/autoload.php";
        $container = new Container();
        //普通绑定
//        $container->bind('\Pcb\Model\LARAVELDB',null);
        //单例绑定
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
                return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');

    }



    public function getUserName($uid){
        static::$DB->connection('LIEXIN')->table('user')->pluck('mobile');
    }



    public function sendOrderMsg($keyword, $data, $to_user = '', $cn_uncode = false)
    {
        // 推送给内部人员
        $data = json_decode($data, true);
        if ($cn_uncode) {
            $send_data = json_encode($data['data'], JSON_UNESCAPED_UNICODE); //防止中文乱码
            $wechat_data = json_encode($data['wechat_data'], JSON_UNESCAPED_UNICODE); //防止中文乱码
        } else {
            $send_data = json_encode($data['data']); //防止中文乱码
            $wechat_data = json_encode($data['wechat_data']); //防止中文乱码
        }
        if (strval($to_user) === 'test') {
            $to_user = C('INNER_PERSON_TEST');
        }
        if (!$to_user) { // 给正式的内部人员推送
            $to_user = 'INNER_PERSON';
        }

        $touser_json = json_encode($to_user);
        $check['touser'] = $touser_json;
        $check['data'] = $send_data;
        $check['wechat_data'] = $wechat_data;
        $check['pf'] = platform();
        $check['keyword'] = $keyword;
        $check['is_ignore'] = false;
        $check = array_merge($check, authkey());
        $res = post_curl(API_DOMAIN.'/msg/sendMessageByAuto', $check);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }


}