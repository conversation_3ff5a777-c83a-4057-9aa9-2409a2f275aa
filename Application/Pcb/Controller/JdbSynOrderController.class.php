<?php
namespace Pcb\Controller;

use Think\Controller;
use Illuminate\Container\Container;
use Pcb\Controller\BaseController;
use Illuminate\Database\Capsule\Manager as DB;
use Pcb\Exception\PcbException;

class JdbSynOrderController extends BaseController
{
    public $api_key;
    public static $DB;
    public static $suc=[
        0=>'failDealWith',
        1=>'successDealWith',
    ];
    public function _initialize(){
        $this->api_key = isset($_SERVER['HTTP_API_KEY']) ? $_SERVER['HTTP_API_KEY'] : '';
        if(!$this->api_key){
            header('Content-Type:application/json; charset=utf-8');
            $this->apiReturn([
                'Success'=>false,
                'msg'=>'非法请求'
            ]);
        }

        try{
            $key = explode(" ",trim($this->api_key));

            if(count($key) != 2){
                header('Content-Type:application/json; charset=utf-8');
                $this->apiReturn([
                    'Success'=>false,
                    'msg'=>'非法请求'
                ]);
            }

            list($key1,$key2) = $key;

            if(md5(md5(trim($key1)).$key2) != md5(md5('JDB001').C('JDB_AUTH_KEY'))){
                $this->apiReturn([
                    'Success'=>false,
                    'msg'=>'非法请求'
                ]);
            }

        }catch(\Exception $e){
            $this->apiReturn([
                'Success'=>false,
                'msg'=>'非法请求'
            ]);
            \Think\Log::write('非法请求','WARN');
        }

        //注册db驱动
        $container = new Container();
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
            return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');
        $this->pcbPurchaseModel = new \Pcb\Model\PcbPurchase;
        $this->pcbPurchaseInfoModel = new \Pcb\Model\PcbPurchaseInfo;
        $this->PcbOrderAuditReasonModel = new \Pcb\Model\PcbOrderAuditReason;
        $this->PurchaseAuditDetailModel = new \Pcb\Model\PurchaseAuditDetail;
    }

    /**
     * t=1  jdb供应商
     */
    public function index(){
        try{
            $result = file_get_contents('php://input');
//        dump(I('t'));exit;
            \Think\Log::write($result,'WARN');
            $res = json_decode($result,true);
            if(is_array($res) && !empty($res) && isset($res['Status']) && in_array($res['Status'],[0,1])){
                $func = static::$suc[$res['Status']];
                call_user_func_array([$this,$func],[$res]);
            }
            $this->apiReturn([
                'Success'=>true,
                'msg'=>'ok'
            ]);
        }catch(PcbException $e){
            $this->apiReturn([
                'Success'=>true,
                'msg'=>$e->getMessage()
            ]);
        }catch(\Exception $e){
            $this->apiReturn([
                'Success'=>false,
                'msg'=>'处理失败'
            ]);
        }
    }

    //返回
    protected function apiReturn($data){
        header('Content-Type:application/json; charset=utf-8');
        echo json_encode($data);
        \Think\Log::write('测试日志信息，这是警告级别','WARN');
        exit;
    }


    /**
     * 审核失败
     * 1,更新采购单状态
     * 2，新增审核结果
     * @param $res
     * @throws \Exception
     */
    protected function failDealWith($res){
        if(!isset($res['OrderNo']) || !$res['OrderNo']){
            throw new \Exception("订单号不存在",190029);
        }
        DB::transaction(function() use($res){
            $pcbPurchase = $this->pcbPurchaseModel->where("jdb_order_sn",trim($res['OrderNo']))->first();
            if(!$pcbPurchase) throw new \Exception("订单号不存在",190029);
            $pcbPurchase->status = 3;//确认不通过
            $pcbPurchase->confirm_time = time();//审核时间
            if(!$pcbPurchase->save()){
                throw new \Exception("处理失败",190029);
            }

            $bk = $this->PcbOrderAuditReasonModel->create([
                'order_sn'=>$pcbPurchase->order_sn,
                'order_id'=>$pcbPurchase->order_id,
                'type'=>2,
                'remark'=>$res['NoPassNote'],
            ]);
            if(!$bk){
                throw new \Exception("处理失败",190029);
            }
        });
    }

    /**
     * 审核成功
     * 1,更新采购单状态
     * 2，更新采购单pcb信息info
     * 3,更新价格信息
     * 3，新增审核结果
     * @param $res
     * @throws \Exception
     */
    protected function successDealWith($res){
        if(!isset($res['OrderNo']) || !$res['OrderNo']){
            throw new \Exception("订单号不存在",190029);
        }
        DB::transaction(function() use($res){
            $pcbPurchase = $this->updatePcbPurchase($res);
            $this->updatePcbPurchaseInfo($pcbPurchase,$res);
            $this->updatePcbPurPrice($pcbPurchase,$res);
            $this->addOrderAuditreason($pcbPurchase,$res);
        });
    }

    private function updatePcbPurchase($res){
        $pcbPurchase = $this->pcbPurchaseModel->where("jdb_order_sn",trim($res['OrderNo']))->first();
        if(!$pcbPurchase) throw new PcbException("订单已取消",190029);
        //猎芯收取用户10%税
        $order_rate = floatval(C('order_rate'));
        $pcbPurchase->purchase_amount = floatval($res['TotalMoney']+$res['TotalMoney']*$order_rate);
        $pcbPurchase->order_type = intval($res['OrderType']);
        $pcbPurchase->status = 3;//采购完成待确认
        $pcbPurchase->confirm_time = time();//审核时间
        $pcbPurchase->DeliveryDate = strtotime($res['DeliveryDate']);
        if(!$pcbPurchase->save()){
            throw new \Exception("修改采购单状态失败",190029);
        }
        return $pcbPurchase;
    }

    private function updatePcbPurchaseInfo($pcbPurchase,$res){
        $pcbPurchaseInfo = $this->pcbPurchaseInfoModel->where("purchase_id",$pcbPurchase->purchase_id)->first();
        if(!$pcbPurchaseInfo) throw new \Exception("订单号不存在",190029);
        $pcbPurchaseInfo->is_jiaji = boolval($res['IsJiaji']) ? 1 : 0;
        $pcbPurchaseInfo->days = $res['Days'];
        $pcbPurchaseInfo->layer = $res['Layer'];
        $pcbPurchaseInfo->length = $res['Length'];
        $pcbPurchaseInfo->width = $res['Width'];
        $pcbPurchaseInfo->qty = $res['Qty'];
        $pcbPurchaseInfo->material = $res['Material'];
        $pcbPurchaseInfo->thickness = $res['Thickness'];
        $pcbPurchaseInfo->line_space = $res['LineSpace'];
        $pcbPurchaseInfo->min_hole_size = $res['MinHoleSize'];
        $pcbPurchaseInfo->board_color = $res['BoardColor'];
        $pcbPurchaseInfo->font_color = $res['FontColor'];
        $pcbPurchaseInfo->surface = $res['Surface'];
        $pcbPurchaseInfo->via_process = $res['ViaProcess'];
        $pcbPurchaseInfo->test_type = $res['TestType'];
        $pcbPurchaseInfo->copper_thickness = $res['CopperThickness'];
        $pcbPurchaseInfo->inside_copper_thickness = $res['InsideCopperThickness'];
        $pcbPurchaseInfo->design_num = $res['DesignNum'];
        $pcbPurchaseInfo->bill_type = $res['bill_type'];
        $pcbPurchaseInfo->sp_half_hole = boolval($res['SpHalfHole']) ? 1 : 0;
        $pcbPurchaseInfo->sp_goldfingers = boolval($res['SpGoldfingers']) ? 1 : 0;
        $pcbPurchaseInfo->sp_goldfingers_bevelling = $res['SpGoldfingersBevelling'] ? $res['SpGoldfingersBevelling'] : "";
        $pcbPurchaseInfo->sp_UL_marking = $res['SpULMarking'] ? $res['SpULMarking'] : '';
        if(!$pcbPurchaseInfo->save()){
            throw new \Exception("修改采购单附表信息失败",190029);
        }
    }

    /**
     * 更新费用明细
     * @param $pcbPurchase
     * @param $res
     * @throws \Exception
     */
    private function updatePcbPurPrice($pcbPurchase,$res){
        if(!isset($res['PriceInfo']) || empty($res['PriceInfo']) || !is_array($res['PriceInfo'])) throw new \Exception("jdb审核数据找不到价格明细",190029);
        $pcbPurchasePrice = $this->PurchaseAuditDetailModel->where("purchase_id",$pcbPurchase->purchase_id)->first();
        if(!$pcbPurchasePrice) throw new \Exception("采购价格明细不存在",190029);
        $pcbPurchasePrice->price = floatval($res['PriceInfo']['TotalMoney']);
        $pcbPurchasePrice->price_discount = floatval($res['PriceInfo']['PriceDiscount']);
        $pcbPurchasePrice->pcb_bf_net = floatval($res['PriceInfo']['CostBoard']);
        $pcbPurchasePrice->pcb_ysf_net = floatval($res['PriceInfo']['CostColor']);
        $pcbPurchasePrice->pcb_flf_net = floatval($res['PriceInfo']['CostFilm']);
        $pcbPurchasePrice->pcb_gcf_net = floatval($res['PriceInfo']['CostGcf']);
        $pcbPurchasePrice->pcb_other_net = floatval($res['PriceInfo']['CostOther']);
        $pcbPurchasePrice->pcb_pinban_net = floatval($res['PriceInfo']['CostPbf']);
        $pcbPurchasePrice->pcb_kdf_net = floatval($res['PriceInfo']['CostShip']);
        $pcbPurchasePrice->pdf_net = floatval($res['PriceInfo']['CostSurface']);
        $pcbPurchasePrice->pcb_sf_net = floatval($res['PriceInfo']['CostTax']);
        $pcbPurchasePrice->pcb_jjf_net = floatval($res['PriceInfo']['CostUrgent']);
        $pcbPurchasePrice->pcb_ccf_net = floatval($res['PriceInfo']['CostTest']);
        if(!$pcbPurchasePrice->save()){
            throw new \Exception("修改采购单价格明细失败",190029);
        }
    }

    private function addOrderAuditreason($pcbPurchase,$res){
        $bk = $this->PcbOrderAuditReasonModel->create([
            'order_sn'=>$pcbPurchase->order_sn,
            'order_id'=>$pcbPurchase->order_id,
            'type'=>2,
            'remark'=>$res['ResultNote'],
        ]);
        if(!$bk){
            throw new \Exception("新增审核信息失败",190029);
        }
    }





}