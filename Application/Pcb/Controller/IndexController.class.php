<?php
namespace Pcb\Controller;

use Pcb\Controller\BaseController;
use Pcb\Service\Guzzle;
use Illuminate\Validation\Validator;
use Exception;
use Pcb\Service\PcbValidator;
use Pcb\Service\PcbService;
use Illuminate\Container\Container;
use Illuminate\Database\Capsule\Manager as DB;
use Pcb\Model\LARAVELDB;
use Pcb\Model\PcbOrder;
use Pcb\Model\PcbSupplier;
use Pcb\Model\PcbPurchase;
use Pcb\Model\PcbOrderInfo;
use Pcb\Model\PcbOrderAuditDetail;
use Pcb\Model\OrderAddress;


class IndexController extends BaseController
{
    public static $page_size = 6;
    public function _initialize()
    {
//        dump(ACTION_NAME);exit;
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), ['quote'])) {
            // 检查登录
            if (in_array(strtolower(ACTION_NAME), ['pcblist','pcb_order_count','order_details','back_order'])) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
        parent::_initialize();

    }

    public function index()
    {

    }


    /**
     * 计价接口
     */
    public function quote(){
        //获取数据 验证数据 预处理数据
        $data = I("quoteData");
        try{
            $pcbServices = LaravelApp(PcbService::class);
            $fields = $pcbServices->quoteValidatorDatas($data);
            $ret = $pcbServices->getPcbPrice($fields);
            //$ret返回的是多个周期价格的数据取最接近的
//            $onePrice = $pcbServices->getOnePrice($ret,$data['Days']);
            if(empty($ret)) throw new \Exception('没有对应报价',190032);
            return $this->apiReturn(0,'ok',$ret);
        }catch(Exception $e){
            $code = $e->getCode() == 0 ? 190003 : $e->getCode();
            return $this->apiReturn($code,$e->getMessage());
        }

    }



    /*
     * 下单接口
     */
    public function aOrder(){
        //pcb参数
        $data = I("quoteData");
        //配送方式
        $order_shipping_type = I('order_shipping_type',null);
        //收货人信息
        $order_address = I('order_address',[]);
        $order_address_id = I('order_address_id',0);
        //自提
        $order_address_ziti_id = I('order_address_ziti_id',0);//自提id
        $order_address_ziti = I('order_address_ziti',[]);//自提联系人 电话
        //发票信息
        $order_invoice = I('order_invoice',[]);//暂时废弃
        $order_invoice_id = I('order_invoice_id',0);//tax_id
        $order_invoice_type = I('order_invoice_type',0);//
        //备注
        $note = I('note','');
        $adtag = I('adtag','');
        $ptag = I('ptag','');
        //技术联系人
        //technology_contact 联系人
        //technology_tel 联系方式
        $technology = I('technology',[]);
        //pcb文件
        //FileName  文件名
        //FilePath  文件路径
        $pcb_file = I('pcb_file',[]);


        //编辑订单会用到这两个参数 原始订单id 是否修改
        $order_id = I('order_id',0,'intval');
        $isEdit = I('isedit',false,'boolval');
        try{
            //验证文件是否上传
            $this->check_pcb_file($pcb_file);
            //依赖解析对象
            $pcbServices = LaravelApp(PcbService::class);
            //验证pcb信息
            $fields = $pcbServices->quoteValidatorDatas($data);
            //报价
            $price = $pcbServices->getPcbPrice($fields);
            $price = $pcbServices->getOnePrice($price,$data['Days'],$data['IsJiaji']);
            if(empty($price)) throw new \Exception('获取报价失败',190031);
            $preData = [
                'pcdData'=>$fields,//pcb
                'price'=>$price,//价格
                'other'=>[
                    'order_shipping_type' => intval($order_shipping_type),//配送方式
                    'order_address_id'=>intval($order_address_id),//配送地址id 已有地址 默认地址
                    'order_address'=>$order_address,//配送地址array
                    'order_address_ziti_id'=>$order_address_ziti_id,//自提id
                    'order_address_ziti'=>$order_address_ziti,//自提联系人 电话
                    'order_invoice'=>$order_invoice,//发票信息array
                    'order_invoice_id'=>$order_invoice_id,//用户默认发票信息id
                    'order_invoice_type'=>0,//是否需要发票 0不需要 1专用 2普通
                    'note'=>$note,//备注
                    'technology'=>$technology,//技术人员信息 array
                    'pcb_file'=>$pcb_file,//pcb文件 array
                    'adtag'=>$adtag,
                    'ptag'=>$ptag,
                ]
            ];
            //修改订单
            if($order_id && $isEdit){
                $this->editingOrder($preData,$order_id);
            }else{
                $this->addOrder($preData);
            }

            //发送短信
//            $pcbServices->sendMessage($kefuId,cookie('uid'),$preData['order']['order_id']);

            return $this->apiReturn(0,'ok',['order_id'=>$preData['order']['order_id']]);
        }catch(\Exception $e){
            $code = $e->getCode() == 0 ? 190005 : $e->getCode();
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn($code,'下单失败');
        }

    }


    /*
     *
     * 验证pcb上传文件
     */
    private function check_pcb_file($technology){
        if(empty($technology) || !isset($technology['FileName']) || !isset($technology['FilePath']))
            throw new Exception("请上传pcb文件",190004);
    }

    //添加订单
    protected function addOrder(&$preData)
    {
        try{
            $pcb_order = new PcbOrder;
            $pcb_order_info = new PcbOrderInfo;

            $preData['other']['order_sn'] = $pcb_order->findSn(C("order_sn_prefix"));
            $pcbCreateFields = LaravelApp('\Pcb\Service\pcbCreateFields');
            $orderFields = $pcbCreateFields->getOrderFields($preData);
            $orderInfoFields = $pcbCreateFields->getOrderInfoFields($preData);
            //插入表数据
            //开启事务
            DB::transaction(function () use($pcb_order,$pcb_order_info,$orderFields,$orderInfoFields,&$preData,$pcbCreateFields) {
                try{
                    //插入订单信息
                    if(!($pcbOrderObj = $pcb_order->createData($orderFields))){
                        throw new \Exception('新增订单失败','190005');
                    }

                    $isPcbOrderObj = ($pcbOrderObj instanceof PcbOrder );
                    if(!$isPcbOrderObj){
                        throw new \Exception('新增订单失败','190005');
                    }

                    //插入订单扩展表信息
                    if(!($pcbOrderInfoObj = $pcb_order_info->createData($orderInfoFields,$pcbOrderObj))){
                        throw new \Exception('新增订单失败','190005');
                    }

                    $isPcbOrderInfoObj = ($pcbOrderInfoObj instanceof PcbOrderInfo );
                    if(!$isPcbOrderInfoObj){
                        throw new \Exception('新增订单失败','190005');
                    }
                    //插入费用表
                    $preData['order'] = [
                        'order_id'=>$pcbOrderObj->order_id,
                        'order_sn'=>$pcbOrderObj->order_sn,
                    ];

                    $OrderAuditDetailFields = $pcbCreateFields->getOrderAuditDetailField($preData);
                    $pcb_order_audit = new \Pcb\Model\PcbOrderAuditDetail;
                    if(!$pcb_order_audit->createData($OrderAuditDetailFields)){
                        throw new \Exception('新增订单失败','190005');
                    }

                    //如果是自提 则不需要关注收货地址
                    //新增收货地址
                    $OrderAddressField = $pcbCreateFields->getOrderAddressFields($preData,array_get($preData,'other.order_shipping_type',0));
                    $orderAddressModel = new \Pcb\Model\OrderAddress;
                    if(!$orderAddressModel->createData($OrderAddressField)){
                            throw new \Exception('新增订单地址失败','190006');
                        }


                    //新增发票信息 如果新增发票成功 则修改订单发票类型
                    if(array_get($preData,'other.order_invoice_id',0) > 0){
                        $invoiceObj = (new  \Pcb\Model\PcbOrderInvoice)->createData($pcbCreateFields->getOrderInvoiceFields($preData));
                        $pcbOrderObj->invoice_type = in_array($invoiceObj->inv_type,[2,3,4]) ? $invoiceObj->inv_type : 1;
                        $pcbOrderObj->save();
                    }

                }catch(\Exception $e){
                    throw new \Exception($e->getMessage(),$e->getCode());
                }

            });
            call_user_func_array([$this,'addOrderSendMessageToKefu'],[$preData]);
            return $preData;
        }catch(\Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    protected function addOrderSendMessageToKefu($preData){
        if(C('APP_ENV') == 'product'){
            $messageData=[
                'data'=>[
                    'order_sn'=>$preData['other']['order_sn'],
                ]
            ];
            $res = $this->sendOrderMsg('pcb_kefu_new_order', json_encode($messageData), C('kefu_telphone'),true);
        }
    }

    //修改订单
    protected function editingOrder(&$preData,$order_id)
    {
        try{
            $pcb_order = new PcbOrder;
            $pcb_order_info = new PcbOrderInfo;

            $oldOrder = $pcb_order->where("user_id",cookie('uid'))->find($order_id);
            if(!$oldOrder) throw new Exception('修改的订单不存在',190035);

            $preData['order'] = [
                'order_id'=>$oldOrder->order_id,
                'order_sn'=>$oldOrder->order_sn,
            ];
            $preData['other']['order_sn'] = $oldOrder->order_sn;
            $pcbCreateFields = LaravelApp('\Pcb\Service\pcbCreateFields');
            //修改订单
            $orderFields = $pcbCreateFields->getEditOrderFields($preData);
            $orderInfoFields = $pcbCreateFields->getOrderInfoFields($preData);
            //插入表数据
            //开启事务
            DB::transaction(function () use($pcb_order,$pcb_order_info,$orderFields,$orderInfoFields,&$preData,$pcbCreateFields) {
                try{
                    $order_id = $preData['order']['order_id'];

                    //修改订单信息
                    if(!($pcbOrderObj = $pcb_order->updateData($order_id,$orderFields))){
                        throw new \Exception('修改订单失败','190036');
                    }
                    $pcbOrderObj = $pcb_order->find($order_id);
                    $isPcbOrderObj = ($pcbOrderObj instanceof PcbOrder );
                    if(!$isPcbOrderObj){
                        throw new \Exception('修改订单失败','190036');
                    }


                    //修改订单扩展表信息
                    if(isset($orderInfoFields['order_id'])){
                        unset($orderInfoFields['order_id']);
                    }
                    if(isset($orderInfoFields['order_sn'])){
                        unset($orderInfoFields['order_sn']);
                    }
                    if(!($pcbOrderInfoObj = $pcb_order_info->updateData($order_id,$orderInfoFields))){

                        throw new \Exception('修改订单失败','190036');
                    }


                    //修改费用表

                    $OrderAuditDetailFields = $pcbCreateFields->getOrderAuditDetailField($preData);
                    if(isset($OrderAuditDetailFields['order_id'])){
                        unset($OrderAuditDetailFields['order_id']);
                    }
                    if(isset($OrderAuditDetailFields['order_sn'])){
                        unset($OrderAuditDetailFields['order_sn']);
                    }
                    $pcb_order_audit = new \Pcb\Model\PcbOrderAuditDetail;
                    if(!$pcb_order_audit->updateData($order_id,$OrderAuditDetailFields)){
                        throw new \Exception('修改订单失败','190036');
                    }



                    //如果是自提 则不需要关注收货地址
                    //修改收货地址

                    $OrderAddressField = $pcbCreateFields->getOrderAddressFields($preData,array_get($preData,'other.order_shipping_type',0));
                    $orderAddressModel = new \Pcb\Model\OrderAddress;
                    if(isset($OrderAddressField['order_id'])){
                        unset($OrderAddressField['order_id']);
                    }
                    if(!$orderAddressModel->updateData($order_id,$OrderAddressField)){
                        throw new \Exception('修改订单地址失败','190036');
                    }


                    //修改发票信息
                    if(array_get($preData,'other.order_invoice_id',0) > 0){
                        $orderInvoceFields = $pcbCreateFields->getOrderInvoiceFields($preData);
                        if(isset($orderInvoceFields['order_id'])){
                            unset($orderInvoceFields['order_id']);
                        }
                        (new  \Pcb\Model\PcbOrderInvoice)->updateData($order_id,$orderInvoceFields);
                        $invoiceObj = (new  \Pcb\Model\PcbOrderInvoice)->where("order_id",$order_id)->first();
                        if($invoiceObj){
                            $pcbOrderObj->invoice_type = in_array($invoiceObj->inv_type,[2,3,4]) ? $invoiceObj->inv_type : 1;
                            $pcbOrderObj->save();
                        }
                    }

                    //修改订单后 以前的审核信息状态更改为0
                    (new \Pcb\Model\PcbOrderAuditReason)->where("order_id",$order_id)->update(['status'=>0]);

                }catch(\Exception $e){
                    throw new \Exception($e->getMessage(),$e->getCode());
                }

            });
            return $preData;
        }catch(\Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }


    /**
     * pc端 订单列表接口
     *
     */
    public function pcblist(){
        $user_id = cookie('uid');
        $page = I('page',0,'intval');
        $limit = I('limit',static::$page_size,'intval');
        $pf = I('pf',0,'intval'); // 1 pc  2 h5  6 小程序
        $order =  new PcbOrder;
        try{
            $query = $order->where("user_id",$user_id);
            $query = $this->makeQuery($query);
            $count = $query->count();
            if($page > ceil($count/$limit)) return $this->apiReturn(190023,'没有更多数据');
            $list = $query->paginate($limit,['order_id','order_sn','order_amount','status','order_type','create_time'],'page',$page);
            $data = $list->toArray();
            foreach($data['data'] as $k=>&$v){
                $v['statusEn'] = array_get(PcbOrder::$PcStatus,$v['status']);
                $v['order_type_en'] = array_get(PcbOrder::$OrderType,$v['order_type']);
            }

            if(isset($data['from'])) unset($data['from']);
            if(isset($data['to'])) unset($data['to']);
            if(isset($data['path'])) unset($data['path']);
            if(isset($data['last_page'])) unset($data['last_page']);
            if(isset($data['per_page'])) unset($data['per_page']);
            if(isset($data['next_page_url'])) unset($data['next_page_url']);
            if(isset($data['prev_page_url'])) unset($data['prev_page_url']);

            return $this->apiReturn(0,'',$data);


        }catch(\Exception $e){
            $code = $e->getCode() == 0 ? 190023 : $e->getCode();
            return $this->apiReturn($code,$e->getMessage());
        }
    }


    /**
     * @return array
     * 获取订单总数
     */
    public function pcb_order_count(){
        $user_id = cookie('uid');
        $order =  new PcbOrder;
        try{
            $data[0] = $order->where("user_id",$user_id)->count();//总订单数
            $data[1] = $order->where("user_id",$user_id)->whereIn("status",[1,2,3])->count();//审核中
            $data[4] = $order->where("user_id",$user_id)->where("status",4)->count();//审核不通过
            $data[10] = $order->where("user_id",$user_id)->where("status",10)->count();//待支付
            $data[11] = $order->where("user_id",$user_id)->whereIn("status",[11,12])->count();//生产中
            $data[13] = $order->where("user_id",$user_id)->whereIn("status",[13,14])->count();//待发货
            $data[15] = $order->where("user_id",$user_id)->where("status",'=',15)->count();//已发货
            $data[20] = $order->where("user_id",$user_id)->where("status",'=',20)->count();//交易完成
            $data[-1] = $order->where("user_id",$user_id)->where("status",'=',-1)->count();//已取消
            return $this->apiReturn(0,'',$data);


        }catch(\Exception $e){
            $code = $e->getCode() == 0 ? 190023 : $e->getCode();
            return $this->apiReturn($code,$e->getMessage());
        }
    }


    /**
     * 取消订单
     */
    public function cancelOrder(){
        $order_id = I('order_id',0,'intval');
        $user_id = cookie('uid');
        $order_id = I('order_id',0,'intval');
        $orderModel =  new PcbOrder;
        $pcbPurchaseModel =  new PcbPurchase;
        try{
            $order = $orderModel->where("user_id",$user_id)->findOrFail($order_id);
            if(!$order) throw new \Exception('订单不存在','190009');
            $order->status = -1;
            $order->cancel_time = time();
            if(!$order->save()) throw new \Exception('取消订单失败','190009');
            $pcbPurchaseObj = $pcbPurchaseModel->where("order_id",$order_id)->first();
            if($pcbPurchaseObj){
                $pcbPurchaseObj->status = -1;
                $pcbPurchaseObj->cancel_time = time();
                if(!$pcbPurchaseObj->save()){
                    throw new \Exception('取消订单失败','190009');
                }
            }
            return $this->apiReturn(0,'');
        }catch(\Exception $e){
            $code = $e->getCode() == 0 ? 190009 : $e->getCode();
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn($code,$e->getMessage());
        }
    }


    /**
     * 订单详情
     * @param $query
     * @return mixed
     */
    public function order_details(){
        $user_id = cookie('uid');
        $order_id = I('order_id',0,'intval');
        $order_sn = I('order_sn','','trim');
        $orderModel =  new PcbOrder;
        try{
            if($this->auth()){
                if($order_id > 0){
                    $order = $orderModel->findOrFail($order_id);
                }else{
                    $order = $orderModel->where('order_sn',$order_sn)->firstOrFail();
                }

            }else{

                if($order_id > 0){
                    $order = $orderModel->where("user_id",$user_id)->findOrFail($order_id);
                }else{
                    $order = $orderModel->where('order_sn',$order_sn)->firstOrFail();
                }

            }

            if(!$order) throw new \Exception('订单不存在','190009');

            //订单扩展信息
            if(!$order->orderInfo){
                throw new \Exception('订单扩展信息不存在','190025');
            }

            //费用明细
            if(!$order->orderAuditDetail){
                throw new \Exception('订单费用明细不存在','190024');
            }

            $order->order_address = $order->orderAddress ? $order->orderAddress : [];
            $order->order_invoice = $order->orderInvoice ? $order->orderInvoice : [];


            $result = collect($order);
            $result = $result->except(['is_return_order','sale_id','purchase_status','order_source_type','update_time','order_address.id','order_address.order_id','order_address.order_sn'
                ,'order_address.create_time','order_address.update_time','order_invoice.id','order_invoice.order_id','order_invoice.order_sn'
                ,'order_invoice.create_time','order_invoice.update_time','order_info.id','order_info.order_id','order_info.order_sn'
                ,'order_info.create_time','order_info.update_time','order_audit_detail.id','order_audit_detail.order_sn','order_audit_detail.order_id'
                ,'order_audit_detail.create_time','order_audit_detail.update_time']);


            $data = $result->toArray();

            //税费
            $order_rate = floatval(C('order_rate'));
            $data['tax_cost'] = floatval($data['order_audit_detail']['price']*$order_rate);

            $data['pay_name'] = '';
            if($order->pay_time > 0){
                $payLogModel = new \Pcb\Model\PcbPayLog;
                $pay_name = $payLogModel->where("order_id",$order_id)->value('pay_name');
                $data['pay_name'] = $pay_name ? $pay_name : '';
            }


            //字段默认值处理
            $data['create_time'] = $data['create_time'] ? strtotime($data['create_time']) : 0;
            //审核结果
            $data['audit_result'] = (new \Pcb\Model\PcbOrderAuditReason)->get_audit_result($order_id,$order);

            if(isset($data['order_info']['days']) && $data['order_info']['days'] > 0){
                $data['order_info']['days_val'] = LaravelApp('\Pcb\Service\pcbCreateFields')->getDaysVal($data['order_info']['days']);
            }


            //特殊工艺
            $spl_val = LaravelApp('\Pcb\Service\pcbCreateFields')->getSpVal($data);
            $data['order_info']['sp_val'] =  !empty($spl_val) ? implode(";",LaravelApp('\Pcb\Service\pcbCreateFields')->getSpVal($data)) : '无';

            $data['order_info']['pb_type'] = '/';//拼板方式
            $data['order_audit_detail']['pcb_other_net_note'] = '无';//其它费用要求说明
            if($order->order_shipping_type == 1){
                $data['order_address']['prex_address'] = (new \Pcb\Model\Region)->getDesAddress($data['order_address']['district']);
            }else{
                $data['order_address']['prex_address'] = '';
            }

            if($order->invoice_type != 0){
                $data['order_invoice']['prex_address'] = (new \Pcb\Model\Region)->getDesAddress($data['order_invoice']['consignee_district']);
            }

            //处理时间
            $data = LaravelApp('\Pcb\Service\pcbCreateFields')->setOrderTime($data);

            //订单备注:
            if(isset($data['order_info']['note']) && !$data['order_info']['note'] ){
                $data['order_info']['note'] = '无';
            }
            return $this->apiReturn(0,'',$data);

        }catch(\Exception $e){
            $code = $e->getCode() == 0 ? 190009 : $e->getCode();
            return $this->apiReturn($code,$e->getMessage());
        }
    }


    /**
     * 返单接口
     * @param $query
     * @return mixed
     */
    public function back_order(){
        $user_id = cookie('uid');
        $order_id = I('order_id',0,'intval');
        $orderModel =  new PcbOrder;
        try{
            $order = $orderModel->where("user_id",$user_id)->findOrFail($order_id);
            if(!$order) throw new \Exception('订单不存在','190009');

            //订单扩展信息
            if(!$order->orderInfo){
                throw new \Exception('订单扩展信息不存在','190025');
            }

            //费用明细
            if(!$order->orderAuditDetail){
                throw new \Exception('订单费用明细不存在','190024');
            }

            $order->order_address = $order->orderAddress ? $order->orderAddress : [];
            $order->order_invoice = $order->orderInvoice ? $order->orderInvoice : [];

            $result = collect($order);
            $result = $result->except(['is_return_order','sale_id','purchase_status','user_id','user_account','order_source_type','update_time','order_address.id','order_address.order_id','order_address.order_sn'
                ,'order_address.create_time','order_address.update_time','order_invoice.id','order_invoice.order_id','order_invoice.order_sn'
                ,'order_invoice.create_time','order_invoice.update_time','order_info.id','order_info.order_id','order_info.order_sn'
                ,'order_info.create_time','order_info.update_time','order_audit_detail.id','order_audit_detail.order_sn','order_audit_detail.order_id'
                ,'order_audit_detail.create_time','order_audit_detail.update_time']);
            $data = $result->toArray();
            //特殊工艺
            $spl_val = LaravelApp('\Pcb\Service\pcbCreateFields')->getSpVal($data);
            $data['order_info']['sp_val'] =  !empty($spl_val) ? implode(";",LaravelApp('\Pcb\Service\pcbCreateFields')->getSpVal($data)) : '无';
            return $this->apiReturn(0,'',$data);
        }catch(\Exception $e){
            $code = $e->getCode() == 0 ? 190033 : $e->getCode();
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn($code,$e->getMessage());
        }
    }


    /**
     * 编辑订单
     * @param $query
     * @return mixed
     */
    public function editOrder(){
        $user_id = cookie('uid');
        $order_id = I('order_id',0,'intval');
        try{
            $orderModel =  new PcbOrder;
            $order = $orderModel->where("user_id",$user_id)->findOrFail($order_id);
            if(!$order) throw new \Exception('订单不存在','190009');
            //订单扩展信息
            if(!$order->orderInfo || !($order->orderInfo instanceof PcbOrderInfo)){
                throw new \Exception('订单扩展信息不存在','190025');
            }
            $orderInfo  = $order->orderInfo;
            $orderInfo = collect($orderInfo)->except(['id','order_id','order_sn','technical_contact','technical_contact_tel','note','create_time','update_time']);
//            dump($orderInfo);
            return $this->apiReturn(0,'',$orderInfo->toArray());
        }catch(\Exception $e){
            $code = $e->getCode() == 0 ? 190034 : $e->getCode();
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn($code,$e->getMessage());
        }
    }


    protected function makeQuery($query){
        $status = I('status','0');
        $order_sn = I('order_sn','','trim');
        $order_type = I('order_type','0','intval');
        $start_time =  I('start_time','','trim');
        $end_time =  I('end_time','','trim');
        $query = $query->Status($status)->OrderSn($order_sn)->OrderType($order_type)
            ->SearchCreateTime($start_time,$end_time)->orderBy("order_id","desc");
        return $query;
    }

}
