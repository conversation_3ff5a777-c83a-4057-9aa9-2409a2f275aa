<?php
namespace Pcb\Controller;

use Illuminate\Container\Container;
use Pcb\Controller\BaseController;
use Pcb\Service\Guzzle;
use Illuminate\Validation\Validator;
use Think\Exception;
use Pcb\Service\PcbValidator;
use Pcb\Service\PcbService;
use Pcb\Service\AdminapiService;
use Illuminate\Database\Capsule\Manager as DB;
use Pcb\Model\LARAVELDB;
use Pcb\Model\PcbOrder;
use Pcb\Model\PcbSupplier;
use Pcb\Model\PcbPurchase;
use Pcb\Model\PcbOrderAuditDetail;
use Pcb\Model\PcbOrderInfo;
use Pcb\Model\OrderAddress;
use Pcb\Model\PcbPayLog;



class AdminapiController extends BaseController
{

    public function _initialize()
    {
//        parent::_initialize();
//        require "./vendor/autoload.php";
        $container = new Container();
        //普通绑定
//        $container->bind('\Pcb\Model\LARAVELDB',null);
        //单例绑定
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
            return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');
    }

    /**
     * 生成采购单
     */
    public function addPurchase(){
        if(!IS_POST){
            return $this->apiReturn(21005,"非法请求");
        }
        $admin_id = I('admin_id');//操作人
        $order_id = I('order_id',0,'intval');
        $order =  new PcbOrder;
        $purchaseModel =  new PcbPurchase;
        $adminApiService = LaravelApp(AdminapiService::class);
        try{
            $order = $order->findOrFail($order_id);
            if(!$order) throw new \Exception('订单不存在','190009');
            if($order->status == -1) throw new \Exception('订单已取消无法生成采购单','190026');
            //orderinfo数据
            $orderInfo = $order->orderInfo;

            if(!$orderInfo) throw new \Exception('订单不存在','190009');

            //费用明细
            $orderAuditDetail = $order->orderAuditDetail;
            if(!$orderAuditDetail) throw new \Exception('订单明细不存在','190010');
            $orderAuditDetailData = $orderAuditDetail;


            //生成采购单
            $return = $adminApiService->setPurchaseData($order)
                ->setPurchaseInfoData($orderInfo)
                ->setPurchaseAuditDetailData($orderAuditDetailData)
                ->addPurchase();

            return $this->apiReturn(0,'ok',['purchase_id'=>$return->purchase_id,'purchase_sn'=>$return->purchase_sn]);
        }catch(\Exception $e){
            $code = $e->getCode() == 0 ? 190020 : $e->getCode();
            return $this->apiReturn($code,$e->getMessage());
        }
    }




    /**
     *
     * 后台采购单确认是否通过
     */
    public function confirmOrder(){
        $purchase_id = I('purchase_id',0,'intval');
        $status = I('status',0,'intval');//-1 不通过  1通过
        try{
            if(!in_array($status,[-1,1])) throw new \Exception('参数错误','190035');
            //同步采购单信息到交易单
            LaravelApp(AdminapiService::class)->syncOrder($purchase_id,$status);
            if($status > 0){
                //通过发送短信给用户
                $messageData = [];
                $porder = PcbPurchase::where("purchase_id",$purchase_id)->first();
                if(!$porder) throw new \Exception('没找到相应的订单','190009');
                $messageData=[
                    'data'=>[
                        'order_sn'=>$porder->order_sn,
                    ]
                ];
                $res = $this->sendOrderMsg('pcb_order_product_finish', json_encode($messageData), $porder->user_account,true);
                if($res['err_code'] > 0){
                    throw new \Exception($res['err_msg'],'190040');
                }
            }
            return $this->apiReturn(0,'ok');
        }catch(\Exception $e){
            return $this->apiReturn($e->getCode(),$e->getMessage());
        }

    }


    /**
     * 线下支付
     */
    public function offline_payment(){
        $data['order_id'] = I('order_id',0,'intval');
        $data['pay_money'] = I('pay_money',0,'floatval');//支付金额
        try{
            if($data['pay_money'] <= 0) throw new \Exception('请正确填写付款金额','190037');
            LaravelApp(AdminapiService::class)->offline_payment($data);
            //发送rbmq消息 pcb进去生产流程
            $this->sendRabbitmqPcbFinishPay($data['order_id']);
            return $this->apiReturn(0,'ok');
        }catch(\Exception $e){
            return $this->apiReturn($e->getCode(),$e->getMessage());
        }

    }

    protected function sendRabbitmqPcbFinishPay($order_id){
        $RbmqModel = D('Common/Rbmq');
        $push = array(
            'job' => 'pcb.pay.finish',
            'data' => array('order_id' => $order_id)
        );
        $bk = $RbmqModel->exchange(C("EXCHANGE_NAME_PCB"))->queue(C("QUEUE_PCB_FINISH_PAY"))
            ->exchangeBind(C('EXCHANGE_NAME_PCB'), C('QUEUE_PCB_FINISH_PAY'))
            ->push($push, C("QUEUE_PCB_FINISH_PAY"));
        if(!$bk){
            throw new \Exception('触发生产流程消息发送失败','190037');
        }
    }


    public function rbmq(){
        $RbmqModel = D('Common/Rbmq');
        $push = array(
            'job' => 'pcb.pay.finish',
            'data' => array('order_id' => 208)
        );
        $a= $RbmqModel->exchange(C("EXCHANGE_NAME_PCB"))->queue(C("QUEUE_PCB_FINISH_PAY"))
            ->exchangeBind(C('EXCHANGE_NAME_PCB'), C('QUEUE_PCB_FINISH_PAY'));
//        dump($a);
        $bk = $a->push($push, C("QUEUE_PCB_FINISH_PAY"));
        dump($bk);

    }


    //设置采购才物流 货已到猎芯 确认入库
    public function setOrderProduct(){
        $purchase_id = I('purchase_id',0,'intval');
        try{
            $pcbPurchase = PcbPurchase::findOrFail($purchase_id);
            $order = PcbOrder::findOrFail($pcbPurchase->order_id);
            DB::transaction(function()use($pcbPurchase,$order){
                $pcbPurchase->status = 15;//猎芯已收货
                if(!$pcbPurchase->save()){
                    throw new \Exception('修改失败','190039');
                }
                $order->status = 14;//已发货到猎芯
                $order->purchase_status = 15;//已发货到猎芯
                $order->production_time = time();//生产完成时间
                if(!$order->save()){
                    throw new \Exception('修改失败','190039');
                }
            });
            return $this->apiReturn(0,'ok');
        }catch(\Exception $e){
            return $this->apiReturn($e->getCode(),$e->getMessage());
        }
    }



    /**
     * 发送积分消息
     */
    public function setuser_autoreceive(){
        $order_id = I('order_id',0,'intval');
        try{
            if(!$order_id) throw new \Exception('订单不存在','400');
            $order = PcbOrder::findOrFail($order_id);
            $RbmqModel = D('Common/Rbmq');
            $push_data = array(
                'user_id' => $order->user_id,//用户id
                'flow_type' => 1,//积分流向 订单完成固定为1
                'flow_reason_type' => 7,//积分流向原因 订单固定为3
                'flow_pf' => 1, //平台'1PC 2H5 3小程序 4后台人工调整'
                'flow_extra_id' => $order_id,//order_id
            );
            $bk = $RbmqModel->queue(C('QUEUE_MKT_POINT'))->push($push_data, C('QUEUE_MKT_POINT'));
            if(!$bk) throw new \Exception('消息发送失败','400');
            return $this->apiReturn(0,'ok');
        }catch(\Exception $e){
            return $this->apiReturn($e->getCode(),$e->getMessage());
        }
    }

    /**
     * 发短信 订单从猎芯发货后，发送短信给客户通知已发货
     */
    public function pcb_delivered_customer(){
        $order_sn = I('order_sn','','trim');
        $shipping_no = I('shipping_no','','trim');
        $shipping_name = I('shipping_name','','trim');
        $data=[
            'data'=>[
                    'order_sn'=>$order_sn,
                    'shipping_name'=>$shipping_name,
                    'shipping_no'=>$shipping_no,
            ]
        ];
        $pcbOrder = PcbOrder::where("order_sn",$order_sn)->first();
        if($pcbOrder){
            $res = $this->sendOrderMsg('pcb_delivered_customer', json_encode($data), $pcbOrder->user_account,true);
        }else{
            return $this->apiReturn(190009,'没找到相应的订单');
        }
        return $this->apiReturn($res['err_code'],$res['err_msg']);
    }



    /**
     * pcb交易订单
     * 发货接口
     */
    public function deliverying(){
        $result = file_get_contents('php://input');
        LaravelApp(AdminapiService::class)->confirmOrder($result);
    }

    /**
     * pcb交易订单
     * 确认后审核结果
     */
    public function confirmAudit(){

    }




}