<?php
namespace Pcb\Model;

use Illuminate\Container\Container;
use Illuminate\Database\Capsule\Manager as Capsule;

class LARAVELDB extends  Capsule{

    public  $DB ;
    public function __construct(Container $container = null)
    {
        parent::__construct($container);
        $capsule = new Capsule;
        $this->add_connection($capsule);
        $capsule->setAsGlobal();
        $capsule->bootEloquent();

        $this->DB = $capsule;
    }


    private function add_connection($capsule){
        $capsule->addConnection(C('LARAVEL_DB_PCB'),'default');
        $capsule->addConnection(C('LARAVEL_DB_LIEXIN'),'LIEXIN');
        $capsule->addConnection(C('SUPPLY_CHAIN'),'SUPPLYCHAIN');
        $capsule->addConnection(C('LARAVEL_DB_TAX'),'TAX');
        $capsule->addConnection(C('LARAVEL_MESSAGE_TAX'),'MESSAGE');
        $capsule->addConnection(C('LIEXIN_CREDIT'),'LIEXINCREDIT');
        $capsule->addConnection(C('SCM_INTERNAL'),'SCM_INTERNAL');
    }



}


