<?php
namespace Pcb\Model;
use \Pcb\Model\BaseModelLaravel;


class PcbOrderInvoice extends BaseModelLaravel
{
    protected $connection="default";
    protected $table = 'order_invoice';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;





    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }



    public  function createData($data,$pcbOrderObj){
        return $this->create($data);
    }


    public  function updateData($order_id,$data){
        return $this->updateOrCreate(['order_id'=>$order_id],$data);
    }








}