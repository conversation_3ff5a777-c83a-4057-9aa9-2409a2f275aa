<?php
namespace Pcb\Model;
use Illuminate\Support\Facades\DB;
use \Pcb\Model\BaseModelLaravel;
use Supplychain\Exception\ErpException;
use Supplychain\Model\CompanyModel;


class OrderAddress extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'order_address';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }



    public  function createData($data){
//        dump($data);
        return $this->create($data);
    }


    public  function updateData($order_id,$data){
        return $this->updateOrCreate(['order_id'=>$order_id],$data);
    }

    /*
     * TODO 20190925新增收货信息
     * RECEIVERCOMPANY 收货方即客户公司名（有收货地址的时候，收货方不能为空）
        RECEIVER 收货联系人
        RECEIVERPHONE 收货联系人手机
        RECEIVERADDR 收货地址
     *
     * */
    static public function getOrderAddress($orderid,$arr)
    {
        //没有就返回空
        if (empty($address = self::where('order_id','=',intval($orderid))->first())){
            $arr['RECEIVERADDR'] = ' ';
            $arr['RECEIVERPHONE'] = ' ';
            $arr['RECEIVER'] = ' ';
            $arr['RECEIVERCOMPANY'] = ' ';
            return $arr;
        }

        $arr['RECEIVERADDR'] = $address->detail_address;
        $arr['RECEIVERPHONE'] = $address->mobile;
        $arr['RECEIVER'] = $address->consignee;

        if (empty($companyName = CompanyModel::where('company_id','=',$address->company_id)->value('company_full_name'))){
            $companyName = ' ';
        }

        $arr['RECEIVERCOMPANY'] = $companyName;
        return $arr;
    }






}