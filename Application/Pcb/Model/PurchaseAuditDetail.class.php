<?php
namespace Pcb\Model;
use \Pcb\Model\BaseModelLaravel;


class PurchaseAuditDetail extends BaseModelLaravel
{
    protected $connection="default";
    protected $table = 'purchase_audit_detail';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;





    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }




    public  function createData($data){
        return $this->create($data);
    }



    public function updateOrCreateData($wheredata,$data){
        return $this->updateOrCreate($wheredata,$data);
    }


}