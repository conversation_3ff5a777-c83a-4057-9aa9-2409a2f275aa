<?php
namespace Pcb\Model;
use \Pcb\Model\BaseModelLaravel;


class PcbPayLog extends BaseModelLaravel
{
    protected $connection="default";
    protected $table = 'pay_log';
    protected $primaryKey = 'pay_log_id';
    protected $guarded = ['pay_log_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = null;
    public $timestamps = true;


    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }



    public function createData($data){
        return $this->create($data);
    }



}