<?php
namespace Pcb\Model;
use \Pcb\Model\BaseModelLaravel;


class PcbOrderInfo extends BaseModelLaravel
{
    protected $connection="default";
    protected $table = 'order_info';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;





    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }



    public  function createData($data,$pcbOrderObj){
        $data['order_id'] = $pcbOrderObj->order_id;
        $data['order_sn'] = $pcbOrderObj->order_sn;
        return $this->create($data);
    }


    public  function updateData($order_id,$data){
        return $this->where('order_id',$order_id)->update($data);
    }








}