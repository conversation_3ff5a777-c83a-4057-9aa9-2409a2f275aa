<?php
namespace Pcb\Model;
use \Pcb\Model\BaseModelLaravel;


class Region extends BaseModelLaravel
{
    protected $connection="LIEXIN";
    protected $table = 'region';
    protected $primaryKey = 'region_id';
    protected $guarded = ['region_id'];
    public $timestamps = false;
    public $area;


    public function getAreas($area_id){
        return $this->deepGetArea($area_id);
    }


    public function getDesAddress($area_id){
        if($area_id <= 0) return '';
        $ares = $this->getAreas($area_id);
        if(is_array($ares)){
            $ares = array_reverse($ares);
            return implode("",$ares);
        }
        return '';
    }

    protected function deepGetArea($area_id){
        $tmp = $this->where('region_id',intval($area_id))->first();
        if(!$tmp){
            $this->area[] = [];
            return $this->area;
        }
        $this->area[] = $tmp->region_name;
        if($tmp->region_type != 1){
            $this->deepGetArea($tmp->parent_id);
        }
        return $this->area;
    }

}