<?php
namespace Pcb\Model;
use \Pcb\Model\BaseModelLaravel;
use \Pcb\Model\PcbPurchaseInfo;

class PcbOrderAuditReason extends BaseModelLaravel
{
    protected $connection="default";
    protected $table = 'order_audit_reason';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = null;
    public $timestamps = true;


    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public function get_audit_result($order_id,$order){
        $status = $order->status;
        if($status == -1){
            return '用户已取消';
        }
        if(in_array($status,[1,2,3])){
            return '审核中';
        }
        $res = $this->where("order_id",$order_id)->where("type",1)->where("status",1)->orderBy("id",'desc')->limit(1)->first();
        if(!$res){
            if($status == 4){
                return '审核不通过';
            }else{
                return '审核通过';
            }
        }
        return  $res->remark;
    }


}