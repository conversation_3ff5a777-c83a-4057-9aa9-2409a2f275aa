<?php
namespace Pcb\Model;
use \Pcb\Model\BaseModelLaravel;
use \Pcb\Model\PcbOrderAuditDetail;
use \Pcb\Model\PcbOrderInfo;
use \Pcb\Model\OrderAddress;
use \Pcb\Model\PcbOrderInvoice;

class PcbOrder extends BaseModelLaravel
{
    protected $connection="default";
    protected $table = 'order';
    protected $primaryKey = 'order_id';
    protected $guarded = ['order_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;



    //订单状态
    public static $Status=[
        "-1"=>"已取消",
        "1"=>"已提交",
        "2"=>"采购中",
        "3"=>"采购完成待确认",
        "4"=>"确认不通过",
        "10"=>"待支付",
        "11"=>"支付完成",
        "12"=>"生产中",
        "13"=>"生产完成",
        "14"=>"已发货到猎芯",
        "15"=>"已发货给客户",
        "20"=>"交易完成",

    ];


    //前端订单状态
    public static $PcStatus=[
        "-1"=>"已取消",
        "1"=>"审核中",
        "2"=>"审核中",
        "3"=>"审核中",
        "4"=>"审核不通过",
        "10"=>"待支付",
        "11"=>"生产中",
        "12"=>"生产中",
        "13"=>"待发货",
        "14"=>"待发货",
        "15"=>"已发货",
        "20"=>"交易完成",

    ];

    //订单类型
    public static $OrderType=[
        1=>"样板",
        2=>"小批量",
    ];

    //发货方式
    public static $OrderShippingType=[
        0=>'未知',
        1=>'快递',
        2=>'自提',
    ];


    //发票类型
    public static $InvoiceType=[
        1=>'不用',
        2=>'专用',
        3=>'普通',
    ];



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }



    public  function createData($data){
//        dump($data);
        return $this->create($data);
    }


    public  function updateData($order_id,$data){
        return $this->where("order_id",$order_id)->update($data);
    }


    public function findSn($prefix = '', $suffix = '')
    {
        $sn = pcb_order_sn($prefix, $suffix);
        $order_id = $this->where(array('order_sn' => $sn))->pluck('order_id');
        if (!$order_id->isEmpty()) {
            $sn = $this->findSn($prefix, $suffix);
        }
        return $sn;
    }


    public function orderInfo()
    {
        return $this->hasOne(PcbOrderInfo::class,"order_id","order_id");
    }


    public function orderAuditDetail()
    {
        return $this->hasOne(PcbOrderAuditDetail::class,"order_id","order_id");
    }


    public function orderAddress()
    {
        return $this->hasOne(OrderAddress::class,"order_id","order_id");
    }

    public function orderInvoice()
    {
        return $this->hasOne(PcbOrderInvoice::class,"order_id","order_id");
    }



    //状态
    public function scopeStatus($query, $search) {
        if($search == '-1'){
            return  $query->where('status',-1);
        }
        if(intval($search) > 0){
            if($search == '1'){
                $query = $query->whereIn('status',[1,2,3]);
            }else if($search == '11'){
                $query = $query->whereIn('status',[11,12]);
            }else if($search == '13'){
                $query = $query->whereIn('status',[13,14]);
            }else{
                $query = $query->where('status',$search);
            }
            return $query;
        }
    }

    //
    public function scopeOrderSn($query, $search) {
        if($search != ''){
            return $query->where('order_sn',$search);
        }
    }


    public function scopeOrderType($query, $search) {
        if($search != 0){
            return $query->where('order_type',$search);
        }
    }


    public function scopeSearchCreateTime($query, $start_time,$end_time) {
        if($start_time != ''){
            if($end_time != ''){
                return $query->where('create_time','>=',strtotime($start_time))->where("create_time",'<=',strtotime($end_time." 23:59:59"));
            }else{
                return $query->where('create_time','>=',strtotime($start_time));
            }
        }elseif($end_time != ''){
            return $query->where('create_time','<=',strtotime($end_time." 23:59:59"));
        }
    }



    public function updateStatus($order_id,$status){
        if(!$this->where("order_id",$order_id)->update(['status'=>$status])){
            return false;
        }
        return true;
    }




}