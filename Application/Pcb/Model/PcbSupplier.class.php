<?php
namespace Pcb\Model;
use \Pcb\Model\BaseModelLaravel;


class PcbSupplier extends BaseModelLaravel
{
    protected $connection="default";
    protected $table = 'supplier';
    protected $primaryKey = 'supplier_id';
    protected $guarded = ['supplier_id'];

//    protected $connection="LIEXIN";
//    protected $table = 'user_address';
//    protected $primaryKey = 'address_id';
//    protected $guarded = ['address_id'];

    const CREATED_AT = 'create_time';
    const UPDATED_AT = null;
    public $timestamps = false;


    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }




}