<?php
namespace Pcb\Model;
use \Pcb\Model\BaseModelLaravel;


class PcbOrderAuditDetail extends BaseModelLaravel
{
    protected $connection="default";
    protected $table = 'order_audit_detail';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }



    public  function createData($data){
        return $this->create($data);
    }


    public  function updateData($order_id,$data){
        return $this->where("order_id",$order_id)->update($data);
    }






}