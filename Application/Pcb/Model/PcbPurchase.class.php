<?php
namespace Pcb\Model;
use \Pcb\Model\BaseModelLaravel;
use \Pcb\Model\PcbPurchaseInfo;
use \Pcb\Model\PurchaseAuditDetail;

class PcbPurchase extends BaseModelLaravel
{
    protected $connection="default";
    protected $table = 'purchase';
    protected $primaryKey = 'purchase_id';
    protected $guarded = ['purchase_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;



    //采购订单状态
    public static $Status=[
        "-1"=>"已取消",
        "2"=>"采购中",
        "3"=>"采购完成待确认",
        "4"=>"确认不通过",
        "5"=>"确认通过",
        "12"=>"生产中",
        "13"=>"生产完成",
        "14"=>"已发货到猎芯",
        "15"=>"猎芯已收货",
        "20"=>"交易完成",
    ];



    //订单类型
    public static $OrderType=[
        1=>"样板",
        2=>"小批量",
    ];



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public function findSn($prefix = 5, $suffix = '')
    {
        $sn = pcb_order_sn($prefix, $suffix);
        $order_id = $this->where(array('purchase_sn' => $sn))->pluck('purchase_id');
        if (!$order_id->isEmpty()) {
            $sn = $this->findSn($prefix, $suffix);
        }
        return $sn;
    }


    public  function createData($data){
        return $this->create($data);
    }


    public function purchaseInfo()
    {
        return $this->hasOne(PcbPurchaseInfo::class,"purchase_id","purchase_id");
    }

    public function purAuditDetail()
    {
        return $this->hasOne(PurchaseAuditDetail::class,"purchase_id","purchase_id");
    }



    public function updatejdbOrderNo($purchase_id,$jdb_order_sn){
        return $this->where('purchase_id',$purchase_id)->update(['jdb_order_sn'=>$jdb_order_sn]);
    }


    public function updateOrCreateData($wheredata,$data){
        return $this->updateOrCreate($wheredata,$data);
    }






}