<?php
namespace Bigtv\Controller;

header('Access-Control-Allow-Origin: *');

use Think\Controller;
use Illuminate\Container\Container;
use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Database\Capsule\Manager as DB;


class ApiController extends Controller{

    /**
     * 跨域头部设置
     */
    private function setHeaders()
    {

        $origin = isset($_SERVER['HTTP_ORIGIN'])? $_SERVER['HTTP_ORIGIN'] : '';
        $origin_arr = explode('//', $origin);
        $allow_origin = C('ALLOW_ORIGIN');
        if(in_array($origin_arr['1'], $allow_origin)){
            header('Access-Control-Allow-Origin:'.$origin);
            header('Access-Control-Allow-Credentials:true');
            header('Access-Control-Allow-Methods:POST');
//            header('Access-Control-Allow-Headers:x-requested-with,content-type');
            header('Access-Control-Allow-Headers:x-requested-with,content-type,hunt_types');
        }
        header("Content-type: text/html; charset=utf-8");
    }


    //大屏数据
    public function bigTv()
    {
        $date = $this->handleDate($_GET['date']);
        $returnJson = [];
        //日交易
        $orderTransaction = DB::table('btv_daily_order_transaction')->where('datetime',$date)
            ->select('daily_ic_num','daily_ic_sn_num','daily_ic_search_num','history_ic_search_xinum','current_sku_sum')->first();
        foreach ($orderTransaction as $key=>$value){
            $returnJson[$key] = $value;
        }

        //造假数据
        $returnJson['daily_ic_num'] *= 10;
        $returnJson['daily_ic_sn_num'] *= 20;
        $returnJson['daily_ic_search_num'] *= 100;
        $returnJson['history_ic_search_xinum'] *= 10;



        //供应商与品牌
        $goodsSupply =  DB::table('btv_goods_supply')->orderBy('id','desc')
            ->select('plat_sku_sum','plat_supply_sum','supply_brand_num','supply_class_num')->first();
        foreach ($goodsSupply as $key=>$value){
            $returnJson[$key] = $value;
        }

        //日20搜索
        $returnJson['daily_search'] = $this->turnTwoArr(DB::table('btv_daily_top_search')->where('status',1)->where('datetime',$date)->limit(20)->pluck('search_num','sn'),99);

        //日20下单
        $returnJson['daily_order'] = $this->turnTwoArr(DB::table('btv_daily_top_order')->where('status',1)->where('datetime',$date)->limit(20)->pluck('order_num','sn'),15);


        $returnJson['customer_city'] = $this->turnTwoArr(DB::table('btv_customer_city')->where('status',1)->pluck('customer_baifen','region'));


        echo json_encode($returnJson);
    }


    //大屏数据
    public function bigTvTwo()
    {
        $date = $this->handleDate($_GET['date']);
        $returnJson = [];

        $returnJson['daily_pv'] = $this->turnTwoArr(DB::table('btv_daily_pv')->where('status',1)->pluck('daily_pv','year'));

        $returnJson['daily_active_users'] = $this->turnTwoArr(DB::table('btv_daily_active_users')->where('status',1)->orderBy('id','desc')->pluck('daily_active_uv','year'));

        $returnJson['customer_application'] = $this->turnTwoArr(DB::table('btv_customer_application')->where('status',1)->pluck('customer_baifen','application_region'));

        $returnJson['daily_order_users'] = $this->turnTwoArr(DB::table('btv_daily_order_users')->where('status',1)->pluck('daily_order_users','year'));

        $returnJson['daily_order'] = $this->turnTwoArr(DB::table('btv_daily_order')->orderBy('id','desc')->where('status',1)->pluck('daily_order','year'));

        $returnJson['customer_city'] = $this->turnTwoArr(DB::table('btv_customer_city')->where('status',1)->pluck('customer_baifen','region'));

        $mediaMarketing =  DB::table('btv_media_marketing')
            ->select('yunyin_pubilc','public_fans_num','group_num','banner_sun_num','year_read_num')->first();
        foreach ($mediaMarketing as $key=>$value){
            $returnJson[$key] = $value;
        }
        echo json_encode($returnJson);
    }



    //大屏数据
    public function cityCoordinate()
    {
        if (empty($_GET['is_china']) ){
            $city = '国外';
        }else{
            $city = '国内';
        }


        $dbData = DB::table('btv_city_coordinate')
            ->where('status',1)
            ->where('is_china',$city)
            ->select('city_name','coordinate')->get()->toArray();


        foreach ($dbData as $val){

            $returnJson['geoCoordMap'][$val->city_name] = explode(',',$val->coordinate);
            if ($val->city_name == '深圳'){
                continue;
            }
            $returnJson['BJData'][] = [
                ['name'=>$val->city_name,'value'=>rand(2000,3000)],
                ['name'=>'深圳'],
            ];
        }
        echo json_encode($returnJson);
    }






    public function __construct()
    {
        $capsule = new Capsule;
        // 创建链接
        $capsule->addConnection(C('DASHOBOARD'));

        // 设置全局静态可访问DB
        $capsule->setAsGlobal();

        // 启动Eloquent
        $capsule->bootEloquent();
    }



    private function turnTwoArr($arr,$bei=1)
    {
        $returnArr = [];
        $i=0;
        foreach ($arr as $key=>$value){
            $returnArr[$i][$key] = $value*$bei;
            $i++;
        }
        return $returnArr;
    }

    //处理日期，周天，周一设置成星期五的日期
    private function handleDate($date)
    {
        if (date('w') == '1'){
            return date('Y-m-d',time()-(86400*3));
        }

        if (date('w') == '0'){
            return date('Y-m-d',time()-(86400*2));
        }

        return $date;
    }


    //新增录入公安app
    public function addPublicManInfo()
    {
        $this->setHeaders();

        $post = I('post.');
        $insertData['name'] = $post['name'];
        $insertData['tel'] = $post['tel'];
        $insertData['department'] = $post['department'];
        $insertData['position'] = $post['position'];
        $insertData['pic'] = $post['pic'];
        $insertData['create_time'] = time();

        DB::table('policeman_info')->insertGetId($insertData);
        $returnJson = ['err_code'=>0,'err_msg'=>'ok','data'=>[]];
        header("Content-type: application/json");
        echo json_encode($returnJson);
    }



}