<?php
/**
 * Created by PhpStorm.
 * User: leo
 * Date: 2018/10/20
 * Time: 14:25
 */
namespace Point\Controller;
class BaseController extends \Common\Controller\BaseController
{


    public function _initialize(){

        setHeader();

        //临时屏蔽
        // $res = $this->checkLogin($verify_mode);
        // if ($res['err_code'] != 0) {
        //     return $this->apiReturn($res['err_code'], $res['err_msg']);
        // }
    }
    /**
     * 获取用户当前积分
     * @param $user_id
     * @return mixed
     */
    protected function getUserCurrentPoints($user_id)
    {
        $data['user_id'] = $user_id;
        $data = array_merge($data, authkey());
        $res = post_curl(MARKET_DOMAIN.'/webapi/getUserCurrentPoint', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        
        return $res;
    }

    /**
     * 获取指定用户积分明细(收入+支出)
     * @param $user_id
     * @param $page
     * @param $limit
     * @param $flow_type
     * @return mixed
     */
    protected function getUserPointDetails($user_id,$page,$limit,$flow_type,$start_time,$end_time){
        $data['user_id'] = $user_id;
        $data['page'] = $page;
        $data['limit'] = $limit;
        $data['flow_type'] = $flow_type;
        $data['start_time'] = $start_time;
        $data['end_time'] = $end_time;
        $res = post_curl(MARKET_DOMAIN.'/webapi/getUserPointDetail', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取指定用户兑换礼品列表
     * @param $user_id
     * @param $page
     * @param $limit
     * @param $prize_type
     * @return mixed
     */
    protected function getUserPointExchangeLists($user_id,$page,$limit,$prize_type,$start_time,$end_time){
        $data['user_id'] = $user_id;
        $data['page'] = $page;
        $data['limit'] = $limit;
        $data['prize_type'] = $prize_type;
        $data['start_time'] = $start_time;
        $data['end_time'] = $end_time;
        $res = post_curl(MARKET_DOMAIN.'/webapi/getUserPointExchangeList', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 获取指定用户兑换奖品明细  PC兑换奖品 查看按钮
     * @param $point_exchange_id
     * @return mixed
     */
    protected function getUserPointExchangeDetails($point_exchange_id){
        $data['point_exchange_id'] = $point_exchange_id;
        $res = post_curl(MARKET_DOMAIN.'/webapi/getUserPointExchangeDetail', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 获取积分商城奖品
     * @param $prize_type
     * @param $sort
     * @param $page
     * @param $limit
     * @return mixed
     */
    protected function getPointPrizes($prize_type,$sort,$page,$limit){
        $data['prize_type'] = $prize_type;
        $data['sort'] = $sort;
        $data['page'] = $page;
        $data['limit'] = $limit;
        $cacheKey = "getPointPrizes_".$data['page']."_".$data['limit'];
        if (S($cacheKey)){
            return S($cacheKey);
        }
        $res = post_curl(MARKET_DOMAIN.'/webapi/getPointPrize', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
            S($cacheKey,$res,["expire"=>3600]);
        }

        return $res;
    }

    /** 用户兑换奖品
     * @param $user_id
     * @param $point_prize_id
     * @param $address_id
     * @param $pf
     * @return mixed
     */
    protected function exchangePrizes($user_id,$point_prize_id,$address_id,$pf){
        $data['user_id'] = $user_id;
        $data['point_prize_id'] = $point_prize_id;
        $data['address_id'] = $address_id;
        $data['pf'] = $pf;
        $res = post_curl(MARKET_DOMAIN.'/webapi/exchangePrize', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 变更用户兑换奖品收奖地址信息
     * @param $user_id
     * @param $point_exchange_id
     * @param $address_id
     * @param $pf
     * @return mixed
     */
    protected  function changeReceivePrizeAddresses($user_id,$point_exchange_id,$address_id,$pf){
        $data['user_id'] = $user_id;
        $data['point_exchange_id'] = $point_exchange_id;
        $data['address_id'] = $address_id;
        $data['pf'] = $pf;
        $res = post_curl(MARKET_DOMAIN.'/webapi/changeReceivePrizeAddress', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }
}