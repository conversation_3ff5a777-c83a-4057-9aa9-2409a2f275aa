<?php
/**
 * Created by PhpStorm.
 * User: leo
 * Date: 2018/10/20
 * Time: 14:26
 */

namespace Point\Controller;

use Point\Controller\BaseController;
use Order\Model\OrderModel;

class PointController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();

        if (!in_array(strtolower(ACTION_NAME), array('getpointprize','getusercurrentpoint'))) {
            // 检查登录
            if (in_array(strtolower(ACTION_NAME), array('getusercurrentpoint', 'getuserpointdetail', 'getuserpointexchangelist', 'getuserpointexchangedetail'))) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    private function Export($errcode='',$errmsg='',$data=''){
        $callback = I('callback', '');
        if(!empty($callback)){
            echo $callback.'('.json_encode(['errcode'=>$errcode,'errmsg'=>$errmsg,'data'=>$data]).')';
        }else{
            echo json_encode(['errcode'=>$errcode,'errmsg'=>$errmsg,'data'=>$data]);
        }
        exit();
    }

    /**
     * 获取用户当前积分
     * 必传参数  user_id
     */
    public function getUserCurrentPoint(){
        $cookie = cookie();
        $user_id = $cookie['Yo4teW_uid'];
        
        $res = $this->getUserCurrentPoints($user_id);
        $this->Export($res['errcode'], $res['errmsg'],$res['data']);
    }

    /**
     * 获取指定用户积分明细(收入+支出)
     * @instructions : 默认获取所有积分变更记录
     * 必传参数 user_id 用户id
     * 可选参数 page         第几页
     *         limit        每页几条
     *         flow_type    积分流向类型
     *         start_time   开始时间
     *         end_time     结束时间
     * flow_type=1 : 获取获得积分记录
     * flow_type=2 : 获取消耗积分记录
     */
    public function getUserPointDetail(){
        $cookie = cookie();
        $user_id = $cookie['Yo4teW_uid'];
        $page = I('page');
        $limit = I('limit');
        $flow_type = I('flow_type');
        $start_time = I('start_time');
        $end_time = I('end_time');



        $res = $this->getUserPointDetails($user_id,$page,$limit,$flow_type,$start_time,$end_time);
        $this->Export($res['errcode'], $res['errmsg'],$res['data']);
    }

    /**
     * 获取指定用户兑换礼品列表
     * @instructions : 默认获取所有兑换记录
     * 必传参数 user_id     用户id
     * 可选参数 page        第几页
     *         limit       每页几条
     *         prize_type  奖品类型
     *         start_time   开始时间
     *         end_time     结束时间
     * prize_type=1 : 获取实物兑换记录
     * prize_type=2 : 获取优惠券兑换记录
     * prize_type=3 : 获取储值卡兑换记录
     */
    public function getUserPointExchangeList(){
        $cookie = cookie();
        $user_id = $cookie['Yo4teW_uid'];
        $page = I('page');
        $limit = I('limit');
        $prize_type = I('prize_type');
        $start_time = I('start_time');
        $end_time = I('end_time');
        $res = $this->getUserPointExchangeLists($user_id,$page,$limit,$prize_type,$start_time,$end_time);
        $this->Export($res['errcode'], $res['errmsg'],$res['data']);
    }

    /**
     * 获取指定用户兑换奖品明细  PC兑换奖品 查看按钮
     * 必传字段 point_exchange_id  兑换奖品id
     * 返回值：只有实物 会返回地址信息
     */
    public function getUserPointExchangeDetail(){
        $point_exchange_id = I('point_exchange_id');
        $res = $this->getUserPointExchangeDetails($point_exchange_id);
        $this->Export($res['errcode'], $res['errmsg'],$res['data']);
    }

    /**
     * 获取积分商城奖品
     * @instructions : 默认获取所有奖品
     * 必传参数
     * 可选参数  prize_type
     *          sort
     *          page
     *          limit
     * sort='asc'  : 升序排列(默认)
     * sort='desc' : 降序排列
     *
     * prize_type=1 : 获取实物奖品
     * prize_type=2 : 获取优惠券奖品
     * prize_type=3 : 获取储值卡奖品
     */
    public function getPointPrize(){
        $prize_type = I('prize_type');
        $sort = I('sort');
        $page = I('page');
        $limit = I('limit');
        $res = $this->getPointPrizes($prize_type,$sort,$page,$limit);
        $this->Export($res['errcode'], $res['errmsg'],$res['data']);
    }

    /**
     * 用户兑换奖品
     * 必传参数  user_id            用户id
     *          point_prize_id     积分商城奖品id
     *          address_id         用户地址信息id
     *          pf                 用户平台
     */
    public function exchangePrize(){
        $user_id = cookie('uid');
        $point_prize_id = I('point_prize_id');
        $address_id = I('address_id');
        $pf = I('pf');
        $res = $this->exchangePrizes($user_id,$point_prize_id,$address_id,$pf);
        $this->Export($res['errcode'], $res['errmsg'],$res['data']);
    }

    /**
     * 变更用户兑换奖品收奖地址信息
     * 必传参数
     * user_id              用户id
     * point_exchange_id    用户兑换奖品id
     * address_id           用户收货地址id
     * pf                   用户平台
     */
    public function changeReceivePrizeAddress(){
        $user_id = I('user_id');
        $point_exchange_id = I('point_exchange_id');
        $address_id = I('address_id');
        $pf = I('pf');
        $res = $this->changeReceivePrizeAddresses($user_id,$point_exchange_id,$address_id,$pf);
        $this->Export($res['errcode'], $res['errmsg'],$res['data']);
    }

    /**
     * 用户冻结积分
     */
    public function getFrozenPoint() {
        $user_id = cookie('uid');
        $page = I('page', 1, 'intval');
        $pf = I('pf', 1);
        $map['O.status'] = array('between', [3,9]);
        $OrderModel = D('Order/Order');
        $res = $OrderModel->getUserList($user_id, $map, $page);
        $total = $OrderModel->getUserCount($user_id, $map);
//        $res = array_slice($res, 0, $page);
        $return = [];
        $frozen_quantity = 0;
        foreach ($res as &$v) {
            if($v['order_type']==1) {
                $point['point_behavior'] = '订单付款';
                $point['point_status'] = '冻结中';
                $point['point'] = conver_mkt_point($v['order_amount'], $v['order_goods_type'], $v['currency'],$v['order_pay_type']);
                $point['create_time'] = $v['create_time'];
                $point['order_id'] = $v['order_id'];
                $point['order_sn'] = $v['order_sn'];
                $return[] = $point;
                $frozen_quantity += !empty($point['point'])?$point['point']:0;
            }
        }
        unset($v);
        $data = [
            'frozen_quantity' => $frozen_quantity,
            'total'           => !empty($total)?$total:0,
            'list'            => $return,
        ];
        $this->Export(0, 'success',$data);
    }
}