<?php
namespace Wechat\Controller;

use Think\Controller;
use GuzzleHttp\Client;

class WechatArticleJinJiController extends BaseController
{

    public function _initialize(){
        $origin = isset($_SERVER['HTTP_ORIGIN'])? $_SERVER['HTTP_ORIGIN'] : '';
        $origin_arr = explode('//', $origin);
        $allow_origin = C('ALLOW_ORIGIN');
        if(in_array($origin_arr['1'], $allow_origin)){
            header('Access-Control-Allow-Origin:'.$origin);
            header('Access-Control-Allow-Credentials:true');
            header('Access-Control-Allow-Methods:POST');
            header('Access-Control-Allow-Headers:x-requested-with,content-type');
        }
        header("Content-type: text/html; charset=utf-8");
    }
    public function article(){
        try{
            $type_id = I('type_id','','trim');
            $page = I('page',1,'intval');
            $limit = I('limit',0,'intval');
            $params=[];
            $params['type_id'] = $type_id;
            $params['page'] = $page;
            $params['limit'] = $limit;
            $client = new Client();
            $response = $client->post(MARKET_DOMAIN.'/wechatMenu/article', [
                'form_params' => $params
            ]);
            $body = $response->getBody()->getContents(); //获取响应体，对象
            $res = json_decode($body,true);
            if($res['err_code'] == 0){
//                $this->apiReturn(0,'',$res['data']);
                header('Content-type:application/json');
                echo json_encode([
                    'err_code' => 0,
                    'err_msg' => '',
                    'data' => $res['data'],
                ]);
                exit;
            }else{
                throw new \Exception('请求失败');
            }
        }catch(\Exception $e){
            header('Content-type:application/json');
            echo json_encode([
                'err_code' => -1,
                'err_msg' => '',
                'data' => ['top'=>[],'list'=>[],'total'=>0,'menu'=>''],
            ]);
            exit;
//            $this->apiReturn(-1,'',['top'=>[],'list'=>[],'total'=>0,'menu'=>'']);exit;
        }

    }



    public function updateWxuUserCache()
    {
        $codeId = I('code_id',0,'intval');
        $isShowUserInfo = I('is_show_user_info',0,'intval');
        $IntracodeModel = D('Cms/Intracode');
        $info = $IntracodeModel->getIntracodeInfo($codeId);
        if(empty($info)){
            echo json_encode([
                'err_code' => -1,
                'err_msg' => '没有查询到内部编码绑定的用户信息',
                'data' => [],
            ]);
            exit;
        }
        $s_user_info = S_user($info["user_id"]);
        if(empty($s_user_info)){
            echo json_encode([
                'err_code' => -1,
                'err_msg' => '没查询到该手机号对应的前台账号缓存信息',
                'data' => [],
            ]);
            exit;
        }

        if(empty($s_user_info["com_wx_oauth"])){
            $s_user_info["com_wx_oauth"] = [];
        }

        if(!empty($isShowUserInfo)){
            p($s_user_info);exit;
        }
//        p($s_user_info);

        $mobile = !empty($s_user_info["mobile"]) ? $s_user_info["mobile"] : "";
        if(empty($mobile)){
            echo json_encode([
                'err_code' => -1,
                'err_msg' => '绑定的前台用户手机号不能为空',
                'data' => [],
            ]);
            exit;
        }

        //请求crm 获取用户的企业微信userid
        $res = post_curl(CRM_V2_DOMAIN.'/open/wx/getAdminWxUserId', [
            "mobile"=>$mobile,
        ]);
        $res = json_decode($res, true);
        if($res["code"] != 0 || empty($res["data"]["wx_user_id"])){
            echo json_encode([
                'err_code' => -1,
                'err_msg' => $res["msg"],
                'data' => [],
            ]);
            exit;
        }

        $s_user_info["com_wx_oauth"]["user_id"] = !empty($res["data"]["wx_user_id"]) ? $res["data"]["wx_user_id"] : "";
        S_user($info["user_id"], $s_user_info);



        echo json_encode([
            'err_code' => 0,
            'err_msg' => '',
            'data' => $s_user_info["com_wx_oauth"]["user_id"],
        ]);
        exit;
    }

    public function getdingtalkinfo()
    {
        //         \Think\Log::write("进来了");
        //         $token = getDDAccessToken();
        //         $url = 'https://oapi.dingtalk.com/department/list?access_token=' . $token;
        //         $result=dtcurl($url);
        //         $department_arr = $result['department'];
        // p($department_arr);die;
        //         foreach ($department_arr as $key => $value) {
        //             $department_id = $value['id'];
        //             $url_user = "https://oapi.dingtalk.com/user/list?access_token=". $token ."&department_id=" . $department_id;
        //             $result=dtcurl($url_user);
        //             $user_list = $result['userlist'];
        //             if ($user_list && is_array($user_list)) {
        //                 foreach ($user_list as $key => $value) {
        //                     $mobile = $value['mobile'];
        //                     $user_id = S_account($mobile);
        //                     $user_info = S_user($user_id);
        //                     if ($user_id && $user_info) {
        //                         $user_info['dtalk_oauth']['user_id'] = $value['userid'];
        //                         S_user($user_id, $user_info);
        //                         echo $value['name'] . '<br>';
        //                     }
        //                 }
        //             }
        //         }

        $token = getDDAccessToken(1);

        $CmsModel = D('Cms/Cms');
        $map = [];
        $map['user_id'] = ['neq', 0];
        $map['update_time'] = ['gt', strtotime(date('Y-m-d'))];

        $user = $CmsModel->getData('lie_intracode', $map);
        if (empty($user)) {
            return false;
        }

        $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件

        foreach ($user as $v) {
            $s_user_info = S_user($v['user_id']);

            if (empty($s_user_info)) continue;
            if (empty($s_user_info['mobile'])) continue;
            if (isset($s_user_info['dtalk_oauth'])) continue;

            $url = 'https://oapi.dingtalk.com/topapi/v2/user/getbymobile?access_token=' . $token;

            $post_data = [];
            $post_data['mobile'] = $s_user_info['mobile'];
            $post_data['support_exclusive_account_search'] = 'true';

            $res = post_curl($url, $post_data);
            $res = json_decode($res, true);

            if (!$res || $res['errcode'] != 0) {
                \Think\Log::write('更新用户订单缓存失败，原因：' . json_encode($res), INFO, '', $path);
                continue;
            } 

            if (isset($res['result']['userid'])) {
                $s_user_info['dtalk_oauth']['user_id'] = $res['result']['userid'];

                S_user($v['user_id'], $s_user_info);
  
                \Think\Log::write('更新用户订单缓存成功，用户缓存数据：' . json_encode($s_user_info), INFO, '', $path);
            }

        }


    }

}