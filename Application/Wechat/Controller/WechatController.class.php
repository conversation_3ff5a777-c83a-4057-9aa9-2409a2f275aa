<?php
namespace Wechat\Controller;

use Think\Controller;
use Com\Wechat;


class WechatController extends BaseController
{
//    public function _initialize()
//    {
//        parent::_initialize();
//        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), array("testaaa","testbbb"))) {
//            // 检查登录
//            if (in_array(strtolower(ACTION_NAME), array("testaaa","testbbb"))) {//弱校验
//                $verify_mode = false;
//            } else {//强校验
//                $verify_mode = true;
//            }
//            $res = $this->checkLogin($verify_mode);
//            if ($res['err_code'] != 0) {
//                return $this->apiReturn($res['err_code'], $res['err_msg']);
//            }
//        }
//    }
//
//    public function testaaa(){
//        $bk = M("WxQrRelation")->where('1')->delete();
//        dump($bk);
//    }





    public function callback(){
        if(I('get.echostr')){//token验证
            echo I('get.echostr');exit();
        }
        try{
            $wechat = new Wechat(C('WX_PUBLIC.token'), C('WX_PUBLIC.appid'), C('WX_PUBLIC.key'));
            //$wechat = new Wechat('weixin', 'wx0078c0d21d19e76b', ''); // 目前支付仅针对正式服务号
            $data = $wechat->request();
            if($data && is_array($data)){
                file_put_contents('./data.json', json_encode($data));
                \Think\Log::write(print_r($data,true));
                $wetch_com_id  = 1;
                $this->response($wechat, $data,$wetch_com_id);
            }
        } catch(\Exception $e){
            file_put_contents('./error.json', json_encode($e->getMessage()));
        }
    }

    public function callbackxinychuang(){
        if(I('get.echostr')){//token验证
            echo I('get.echostr');exit();
        }
        try{
            $wechat = new Wechat(C('WX_PUBLIC_XinYChuang.token'), C('WX_PUBLIC_XinYChuang.appid'), C('WX_PUBLIC_XinYChuang.key'));
            //$wechat = new Wechat('weixin', 'wx0078c0d21d19e76b', ''); // 目前支付仅针对正式服务号
            $data = $wechat->request();
            \Think\Log::write(print_r($data,true));
            if($data && is_array($data)){
                file_put_contents('./data.json', json_encode($data));
                \Think\Log::write(print_r($data,true));
                $wetch_com_id  = 2;
                $this->responseXinYChuang($wechat, $data,$wetch_com_id);
            }
        } catch(\Exception $e){
            file_put_contents('./error.json', json_encode($e->getMessage()));
        }
    }

    /**
     * [uploadWxImg 获取微信图片素材，返货media_id 或图片链接]
     * @return [type] [description]
     */
    public function uploadWxImg()
    {
        $wetch_com_id = I("qrcode_use",1,"intval");
        $upload = new \Think\Upload();// 实例化上传类
        $upload->maxSize   =     3145728 ;// 设置附件上传大小
        $upload->exts      =     array('jpg', 'gif', 'png', 'jpeg');// 设置附件上传类型
        $upload->rootPath  =      './Uploads/'; // 设置附件上传根目录
        // 上传单个文件
        $info   =   $upload->uploadOne($_FILES['upload']);
        if(!$info) {// 上传错误提示错误信息
            $res = $this->error($upload->getError());
        }else{// 上传成功 获取上传文件信息
             $data = $info['savepath'].$info['savename'];
//             $wechatModel = wechat();
             $wechatModel = wechatPublic(intval($wetch_com_id));
             $imgPath = './Uploads/' . $data;
             $weData['media'] = '@' . $imgPath;
             $type = 'image';
             $res = $wechatModel->uploadForeverMedia($weData, $type);
             //$res = $wechatModel->uploadImg($weData);
             if ($res['url'] || $res['media_id']) {
                 unlink($imgPath);
             }
        }
        return $this->apiReturn(0, 'success', $res);
    }
    // 删除某个服务号二维码
    public function deleteqrcode()
    {
        $qrcode_id = I('qrcode_id');
        if (!$qrcode_id) return false;
        $WxQrConfigModel = D('WxQrConfig');
        $map = array();
        $map['id'] = $qrcode_id;
        $save['status'] = -1;
        $res = $WxQrConfigModel->where($map)->save($save);
        return $this->apiReturn(0, 'success');
    }
    // wechat 带参数自定义二维码生成
    /**
     * 创建二维码ticket
     * @param int|string $scene_id 自定义追踪id,临时二维码只能用数值型
     * @param int $type 0:临时二维码；1:数值型永久二维码(此时expire参数无效)；2:字符串型永久二维码(此时expire参数无效)
     * @param int $expire 临时二维码有效期，最大为604800秒
     * @return array('ticket'=>'qrcode字串','expire_seconds'=>604800,'url'=>'二维码图片解析后的短连接地址')
     */
    public function getWxCode()
    {
        \Think\Log::write(print_r($_REQUEST,true));
        $scene_desc         = I('scene_desc'); // 二维码渠道标识
        $type               = I('type'); // 类型 （3 为 单图推送）
        $expire             = I('expire_seconds'); // 过期时间
        $title              = I('title'); // 标题 （或media_id）
        $description        = I('description'); // 描述
        $picture_url        = I('picture_url'); // 图文推送的图片链接
        $url                = I('url'); // 图文跳转的链接
        $qrcode_id          = I('qrcode_id'); // 二维码id(有则为编辑，没有则为新增)
        $lottery_id          = I('lottery_id',0); // 活动id
        $wetch_com_id          = I('wetch_com_id',1,"intval"); // 1猎芯  2芯片硬创
        $media_id_url          = I('media_id_url'); // 微信图片的url地址
        $qrcode_use          = I('qrcode_use',1,"intval"); // 二维码的用途 1 普通扫码  2 裂变

        if (!$scene_desc) return $this->apiReturn(140001, '自定义渠道标识 不可为空');
        if (!isset($type)) return $this->apiReturn(140002, '类型不可为空');
        if (strval($type) === '0') {
            if (!$expire) return $this->apiReturn(140003, '临时二维码有效期不可为空');
        }

        if ($qrcode_id) { // 编辑
            $scene_id = I('scene_id');
            if (!$scene_id) return $this->apiReturn(140001, '自定义追踪id 不可为空');
        } else { // 新增
            $scene_id = $_SERVER['REQUEST_TIME']; // 自定义追踪id
            // 0:临时二维码；1:数值型永久二维码(此时expire参数无效)；2:字符串型永久二维码(此时expire参数无效)；3：单图
            if (strval($type) === '0' && $qrcode_use == 2) {
                //如果是裂变的二维码  就必须是永久二维码
                $type = 2;
                $codeType = 2;
                $expire = '';
            }

            if (strval($type) !== '0') {
                $expire = '';
                if($qrcode_use == 2){
                    //裂变二维码
                    $scene_id = 'liebian_' . $scene_id; // 自定义追踪id
                }else{
                    $scene_id = 'liexin_' . $scene_id; // 自定义追踪id
                }
                $type = 2;
                $codeType = 2;
                $expire = '';
            }


            $codeType = $type;
            if (strval($type) === '3') {
                //单图
                $codeType = '2';
            }
            $wechatModel = wechatPublic(intval($wetch_com_id));

            // $codeType 0:临时二维码；1:数值型永久二维码(此时expire参数无效)；2:字符串型永久二维码(此时expire参数无效)
            $res = $wechatModel->getQRCode($scene_id, $codeType, $expire);
            $ticket = $wechatModel->getQRUrl($res['ticket']);
            $qrurl = $wechatModel->getShortUrl($ticket); // 获取短连接
        }

        $addData['type'] = $type; //类型（永久，暂时）
        $addData['create_time'] = $_SERVER['REQUEST_TIME']; //创建时间
        $addData['expire_seconds'] = $expire; //有效期（秒）
        $addData['scene_desc'] = $scene_desc; // 渠道标识
        $addData['title'] = $title;
        $addData['description'] = $description;
        $addData['picture_url'] = $picture_url;
        $addData['url'] = $url;
        $addData['wetch_com_id'] = $wetch_com_id;
        $addData['media_id_url'] = $media_id_url;
        $addData['qrcode_use'] = $qrcode_use;
        $addData['lottery_id'] = intval($lottery_id);

        $WxQrConfigModel = D('WxQrConfig');
        try {
            if ($qrcode_id) { // 编辑
                $map['id'] = array('eq', $qrcode_id);
                $map['scene_id'] = array('eq', $scene_id);
                $WxQrConfigModel->where($map)->save($addData);
            } else { // 新增
                $addData['qrurl'] = $qrurl;
                $addData['qrurl_logo'] = $this->qrurl_logo($qrurl,$scene_id,$wetch_com_id);
                $addData['count'] = 0; //渠道统计，每次关注后新增一次
                $addData['status'] = 1;
                $addData['scene_id'] = $scene_id; //渠道id，自定义（可以是字符串或数字）
                $res = $WxQrConfigModel->add($addData);
            }
        } catch(\Exception $e){

            \Think\Log::write($e->getMessage());
            echo $e->getMessage();
        }
        return $this->apiReturn(0, 'success');
    }


    protected function upload($dir,$filename){
        $k1 = time();
        $ch = curl_init(API_DOMAIN.'/oss/upload');
        $cfile = curl_file_create(realpath($dir.$filename),"image/png",realpath($dir.$filename));
        $data = [
            'source'=>1,
            'upload'=> $cfile,
            'is_rename'=>0,
            'set_dir'=>"wechat/",
            'k1'=>$k1,
            "k2"=>md5(md5($k1).C('SUPER_AUTH_KEY'))
        ];
        curl_setopt($ch, CURLOPT_POST,1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $res = curl_exec($ch);
        curl_close($ch);
        return $res ? $res : false;
    }
    /*
     * weixin_erweima_logo
     */
    protected function qrurl_logo($QR,$scene_id,$wetch_com_id=1){
        try{
            $logo='./public/img/weixin_erweima_logo.png';
            if($wetch_com_id == 1){
                $logo='./public/img/weixin_erweima_logo.png';
            }elseif($wetch_com_id == 2){
                $logo='./public/img/weixin_erweima_logo_xinyingchuang.jpg';
            }

            $im = @imagecreatetruecolor(430, 430);

            $QR = imagecreatefromstring(file_get_contents($QR));
            $logo = imagecreatefromstring(file_get_contents($logo));
            $QR_width = imagesx($QR);//二维码图片宽度 
            $QR_height = imagesy($QR);//二维码图片高度 
            $logo_width = imagesx($logo);//logo图片宽度 
            $logo_height = imagesy($logo);//logo图片高度 
            $logo_qr_width = $QR_width / 5;
            $scale = $logo_width/$logo_qr_width;
            $logo_qr_height = $logo_height/$scale;
            $from_width = ($QR_width - $logo_qr_width) / 2;
            //重新组合图片并调整大小
            $a = imagecopyresampled($QR, $logo, $from_width, $from_width, 0, 0, $logo_qr_width, $logo_qr_height, $logo_width, $logo_height);
            $dir = "./public/img/";
            $filename = $scene_id."_".time()."_qr.png";
            imagepng($QR, $dir.$filename);
            if(file_exists($dir.$filename)){
                //上传图片到oss
                $res = $this->upload($dir,$filename);
                @unlink($dir.$filename);
                if(!$res) throw new \Exception("生成logo失败");
                $res = \GuzzleHttp\json_decode($res,true);
                if($res['code'] == '200' && isset($res['data'][0])){
                    return $res['data'][0];
                }else{
                    throw new \Exception("生成logo失败");
                }
            }
        }catch(\Exception $e){
            @unlink($dir.$filename);
            return $QR;
        }
    }

    public function testRedBag()
    {
        echo "dd";
        /*$openIdArr = C('WHITE_MEMBER');
        if (in_array('oCmCit9ZVeF7HaF_7rxfaLkB6LV4',$openIdArr)) { //白名单 测试
            file_put_contents('./error_ee.json', json_encode($openIdArr));
            $WechatModel = D('Wechat');
            $WechatModel->getRedBag($data['FromUserName']);
        }*/
    }
    /**
     * 扫码次数统计
     * @param [type] $EventKey [二维码渠道id]
     * @param [type] $openid   [description]
     */
    public function addScanCounts($EventKey, $openid,$wetch_com_id=1)
    {
        if (!$EventKey || !$openid) {
            return false;
        }
        $WxScanHistoryModel = D('WxScanHistory');
        $where['open_id'] = array('eq', $openid);
        $where['event_key'] = array('eq', $EventKey);
        $where['wetch_com_id'] = array('eq', $wetch_com_id);
        $count = $WxScanHistoryModel->where($where)->count();
        if ($count) {
            return false;
        }
        $addData['open_id'] = $openid;
        $addData['event_key'] = $EventKey;
        $addData['wetch_com_id'] = $wetch_com_id;
        $addData['create_time'] = time();
        $WxScanHistoryModel->add($addData);
        return true;

    }
    /**
     * 扫码关注次数统计（仅针对后台生成的二维码）
     * @param [type] $EventKey [description]
     * @param [type] $openid   [description]
     */
    public function addScanScribe($EventKey, $openid,$wetch_com_id=1)
    {
        if (!$EventKey || !$openid) {
            return false;
        }
        //wx_scan_subscribe
        $WxScanSubscribeModel = D('WxScanSubscribe');
        $where['open_id'] = array('eq', $openid);
        $where['wetch_com_id'] = array('eq', intval($wetch_com_id));
        $scanData = $WxScanSubscribeModel->field('open_id, status')->where($where)->find();
        if (count($scanData)) {
            if (strval($scanData['status']) === '-1') { //之前已经取消关注
                //更改状态
                $save['status'] = '1'; //更改为已经关注
                $save['event_key'] = $EventKey;
                $save['wetch_com_id'] = intval($wetch_com_id);
                $save['mod_time'] = time();
                $WxScanSubscribeModel->where($where)->save($save);
            }
        } else { //还没关注，新增关注记录
            $addData['status'] = '1';
            $addData['event_key'] = $EventKey;
            $addData['open_id'] = $openid;
            $addData['wetch_com_id'] = $wetch_com_id;
            $addData['create_time'] = time();
            $WxScanSubscribeModel->add($addData);
        }
        return true;
    }
    /**
     * 取消关注（仅针对后台生成的二维码）
     * @param  [type] $openid [description]
     * @return [type]         [description]
     */
    public function unsubscribe($openid,$wetch_com_id=1){
        $WxScanSubscribeModel = D('WxScanSubscribe');
        $where['open_id'] = array('eq', $openid);
        $where['status'] = array('eq', 1);
        $where['wetch_com_id'] = array('eq', intval($wetch_com_id));
        $count = $WxScanSubscribeModel->where($where)->count();
        if ($count) { // 说明该用户已经关注了（通过后台生成的二维码）
            // 取消关注
            $whereSave['open_id'] = array('eq', $openid);
            $whereSave['wetch_com_id'] = array('eq', intval($wetch_com_id));
            $save['status'] = '-1';
            $save['mod_time'] = time();
            $WxScanSubscribeModel->where($whereSave)->save($save);
        }
        return true;
    }


    /**
     * @param $wechat
     * @param $data
     * 裂变
     * 第一次扫码 返回 {"ToUserName":"gh_c906f7a913dd","FromUserName":"o3We-wJWDFABO_ZXjmHbUdddNSc0","CreateTime":"1586403112","MsgType":"event","Event":"subscribe","EventKey":"qrscene_liexin_1586316369","Ticket":"gQEg8TwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAycERkU3RZdERlLTAxMDAwME0wNzEAAgRSRI1eAwQAAAAA"}
     * 分享后：
     *  {"ToUserName":"gh_c906f7a913dd","FromUserName":"o3We-wGVVAe-6h8z3D9akUhhZlxo","CreateTime":"1586403859","MsgType":"event","Event":"subscribe","EventKey":"qrscene_liexin_1586316369_@_o3We-wKekDrE2XkKCB7jYsiXKhhI","Ticket":"gQGp8TwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyZkRSWXRQdERlLTAxMDAwMDAwN3YAAgRrmI5eAwQAAAAA"}
     * $info    lie_wx_qr_config表信息
     * $wechat   对象
     * $resdata  关注二维码后 返回的信息
     * $fromOpenid  分享者的openid
     */
    public function liebian($info,$wechat,$resdata,$fromOpenid,$isShare=true,$wetch_com_id,$is_scan_subscribe=false){
        try{
            $description = $info['description'];


            //如果该用户已经扫描了该二维码  1从海报  2从他人  提示已经扫描过了二维码了

            if($fromOpenid){
                $where = sprintf(" wx_qr_config_id = %d  and (from_openid = '%s' and openid = '%s') ",intval($info["id"]),$fromOpenid,$resdata["FromUserName"]);
            }else{
                $where = sprintf(" wx_qr_config_id = %d  and (from_openid = '%s' or openid = '%s') ",intval($info["id"]),$resdata["FromUserName"],$resdata["FromUserName"]);
            }

            \Think\Log::write($where);

            $isSaoMa = M("WxQrRelation")->where($where)->count("id");

            if($description){
                $msgType = "text";
                $textTpl = "<xml>
                              <ToUserName><![CDATA[%s]]></ToUserName>
                              <FromUserName><![CDATA[%s]]></FromUserName>
                              <CreateTime>%s</CreateTime>
                              <MsgType><![CDATA[%s]]></MsgType>
                              <Content><![CDATA[%s]]></Content>
                              <FuncFlag>0</FuncFlag>
                              </xml>";
                $description  =   htmlspecialchars_decode($description,ENT_QUOTES);
                $resultStr = sprintf($textTpl,$resdata["FromUserName"],$resdata["ToUserName"],time(),$msgType,$description);
                echo $resultStr;
            }


            $wechatModel = wechatPublic(intval($info["wetch_com_id"]));


            $this->liebianPushText($wechatModel,$resdata["FromUserName"],C("XINYINGCHUANG_LIEBIAN_CONFIG.liebianPushText"));


            if ($info['picture_url']) { // 推送新的分享二维码图片
                $this->liebianPushImage($wechatModel,$resdata["FromUserName"],$info);
            }



            //判断用户是否已经关注过 关注过 就不能助力他人了

           if($is_scan_subscribe)  die;


            //添加扫码记录

            if(!$isSaoMa){
                $insertQrRelation = M("WxQrRelation")->add([
                    "wx_qr_config_id" => $info["id"],
                    "scene_id" => $info["scene_id"],
                    "from_openid" => $fromOpenid ? $fromOpenid : "",
                    "openid" => $resdata["FromUserName"],
                    "create_time" => time(),
                ]);
            }




            //判断邀请是否成功 成功就推送消息
            //分享15个好友就可以提示分享者成功 拿到口令
            if(!$isSaoMa && $fromOpenid && $isShare){
                //发送口令 查询是否达到分享15个
                $hasFenXiangNums = M("WxQrRelation")->where(["from_openid"=>$fromOpenid,"wx_qr_config_id" => intval($info["id"])])->count();
                $pushNums = C("XINYINGCHUANG_LIEBIAN_CONFIG.PushNums");
                $cachePushFinishKey = $info["scene_id"].$fromOpenid;
                if($hasFenXiangNums >= intval($pushNums) && !S($cachePushFinishKey)){
                    //给分享者发送  任务完成的消息  领取奖品
                    $content = C("XINYINGCHUANG_LIEBIAN_CONFIG.fromOpenidFinishPush");
                    $this->liebianPushText($wechatModel,$fromOpenid,$content);
                    //小姐姐微信  领取奖品
                    $this->pushImage($wechatModel,$fromOpenid,C("XINYINGCHUANG_LIEBIAN_CONFIG.PushImagewxFriendsMedia_id"));
                    S($cachePushFinishKey,1,["expire"=>3600*24*7]);
                }


                $content = C("XINYINGCHUANG_LIEBIAN_CONFIG.toOpenidFinishPush");
                $this->liebianPushText($wechatModel,$resdata["FromUserName"],$content);
            }



        }catch(\Exception $e){
            \Think\Log::write($e->getMessage());
        }
    }

    /*
     * 裂变发送文本消息
     */
    protected function liebianPushText($wechatModel,$openid="",$content){
        try{
            $wechatModel = $wechatModel;
            $data["touser"] = $openid;
            $data["msgtype"] = "text";
            $data['text']["content"] = $content;
            $wechatModel->sendCustomMessage($data);
        }catch(\Exception $e){

        }
    }



    /*
     * 裂变发送图片二维码推送消息
     */
    protected function liebianPushImage($wechatModel,$openid="",$info){
        try{
            //缓存发给用户的二维码key
            $cacheKey = $info["wx_qr_config_id"]."_".$info["scene_id"]."_".$openid."media_id";
            $wechatModel = $wechatModel;
            $data["touser"] = $openid;
            $data["msgtype"] = "image";
            if(!S($cacheKey)){
                $media_id = $this->getNewMediaId($wechatModel,$info["picture_url"],$openid,$info);
                S($cacheKey,$media_id,["expire"=>3600*24*7]);
            }else{
                $media_id = S($cacheKey);
            }
            $data['image']["media_id"] = $media_id;
            $wechatModel->sendCustomMessage($data);
        }catch(\Exception $e){

        }

    }

    /*
     * 单独的推送一张客服消息 图片
     */
    protected function pushImage($wechatModel,$openid="",$media_id=""){
        try{
            //缓存发给用户的二维码key
            $wechatModel = $wechatModel;
            $data["touser"] = $openid;
            $data["msgtype"] = "image";
            $data['image']["media_id"] = $media_id;
            $wechatModel->sendCustomMessage($data);
        }catch(\Exception $e){

        }
    }



    /*
     * params:$backgroundImage 背景图  $openid  $info后台二维码相关数据
     * 生成带参数的二维码
     * 背景图+二维码+用户昵称
     * 上传到微信
     * return  media_id
     */
    public function getNewMediaId($wechatModel,$backgroundImage,$openid,$info){
        try{

            $wechatModel = $wechatModel;
            $userinfo = $wechatModel->getUserInfo($openid);

            //获取带参数的二维码
            $wechatModel = $wechatModel;
            $scene_id = trim($info["scene_id"])."_@_".$openid;
            $res = $wechatModel->getQRCode($scene_id, 2, 3600*24*5);
            $QRUrl = $wechatModel->getQRUrl($res['ticket']);
            $erweima = $this->qrurl_logo($QRUrl,$scene_id,$info["wetch_com_id"]);


            $logo='./public/img/weixin_erweima_logo.png';
            $im = @imagecreatetruecolor(430, 430);

            $originImage = imagecreatefromstring(file_get_contents($backgroundImage));


            $qrcodeImage = imagecreatefromstring(file_get_contents($erweima));

            $origin_width = imagesx($originImage);//原始背景图片宽度 
            $origin_height = imagesy($originImage);//原始背景图片高度 


            $qrcode_width = imagesx($qrcodeImage);//二维码图片宽度 
            $qrcode_height = imagesy($qrcodeImage);//二维码图片高度 


//            echo sprintf("%s,%s,%s,%s",$origin_width,$origin_height,$qrcode_width,$qrcode_height);
//            exit;


            $logo_qr_width = $origin_width / 2;
            $scale = $qrcode_width/$logo_qr_width;
            $logo_qr_height = $origin_height/$scale;



            $from_width = ($origin_width - 200) / 2;
            $from_height = ($origin_height/2+($origin_height/2-200)/2);
            //重新组合图片并调整大小
            // 目标图 源图 目标X坐标点 目标Y坐标点 源的X坐标点 源的Y坐标点 目标宽度 目标高度 源图宽度 源图高度
//            $a = imagecopyresampled($originImage, $qrcodeImage, $from_width, $from_height, 0, 0,200,200,$qrcode_width, $qrcode_height);
            imagecopyresampled($originImage, $qrcodeImage, 285,  1420, 0, 0,180,180,$qrcode_width, $qrcode_height);


            if($userinfo){
                if(isset($userinfo["nickname"]) && $userinfo["nickname"]){
                    $red = imagecolorallocate($im, 255, 255, 255);
                    $font = "./public/font/Droid Sans Fallback.ttf";
//                    $font = "./public/font/DroidSansChinese.ttf";
                    $content=  sprintf("%s,邀您一起免费参加~",$userinfo["nickname"]);
//                    list($post_0,,$post_2,,,,,) = imagettfbbox(24 , 0,$font, $content);
//                    imagettftext($originImage, 20, 0, ($origin_width-$post_2)/2+50, 40, $red, $font,$content);
                    imagettftext($originImage, 20, 0, 40, 40, $red, $font,$content);
                }

                if(isset($userinfo["headimgurl"]) && $userinfo["headimgurl"]){
//                    dump($userinfo["headimgurl"]);
//                    $user_touxiang  = $this->radius_img($userinfo["headimgurl"],60);
//                    $user_touxiang = $userinfo["headimgurl"];
//                    $usertouxiang = imagecreatefromstring(file_get_contents($user_touxiang));
//                    $touxiang_width = imagesx($usertouxiang);//二维码图片宽度 
//                    $touxiang_height = imagesy($usertouxiang);//二维码图片高度 
//                    imagecopyresampled($originImage, $usertouxiang, 300,  1000, 0, 0,100,100,$touxiang_width, $touxiang_height);
                }


            }

//            header('Content-type: image/jpeg');
//            $filename = "./public/liebian_001_o3We-wJWDFABO_ZXjmHbUdddNSc0.jpg";
            $filename = "./public/liebianImg/".trim($scene_id).".jpg";
            imagejpeg($originImage,$filename,90); // 输出图象到浏览器或文件
            imagedestroy($originImage);
            $weData['media'] = '@' . $filename;
            $type = 'image';
            $res = $wechatModel->uploadForeverMedia($weData, $type);
            @unlink($filename);
            \Think\Log::write(print_r($res,true));
            if ($res && $res['url'] && $res['media_id']) {
                return $res['media_id'];
            }
            return false;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage());
           return false;
        }

        //把二维码合并到背景图




    }


    /**
     * 芯硬创
     */
    public function responseXinYChuang($wechat, $data,$wetch_com_id=2){
        switch ($data['MsgType']) {
            case Wechat::MSG_TYPE_EVENT:
                switch ($data['Event']) {
                    case Wechat::MSG_EVENT_SUBSCRIBE://关注事件
                        \Think\Log::write($data);
                        // 裂变二维码关注

                        if (isset($data['EventKey']) && $data['Event'] == 'subscribe' && strrpos($data['EventKey'],"qrscene_liebian") !== false){
                            $EventKey = str_replace("qrscene_", "", $data['EventKey']);
                            \Think\Log::write($EventKey);
                            $fromOpenid = "";
                            if(strrpos($EventKey,"_@_") === false){
                                $EventKey = $EventKey;
                            }else{
                                list($EventKey,$fromOpenid) = explode("_@_",$EventKey);
                            }

                            \Think\Log::write($EventKey);
                            $WxQrConfigModel = D('WxQrConfig');
                            \Think\Log::write(sprintf("wetch_com_id: %s",$wetch_com_id));
                            $openid = $data['FromUserName']; //必须先赋值，不然获取不了值

                            //先查询该用户是否已经关注了公共账号
                            $is_scan_subscribe = M("WxScanSubscribe")->where(["open_id"=>$openid,"wetch_com_id"=>$wetch_com_id])->count("id");
                            $is_scan_subscribe =  $is_scan_subscribe > 0 ? true : false;
                            \Think\Log::write(sprintf("是否关注了 %s",$is_scan_subscribe));
                            try{
                                $this->addScanCounts($EventKey, $openid,$wetch_com_id); // 扫码统计处理

                                $this->addScanScribe($EventKey, $openid,$wetch_com_id); // 扫码关注统计处理
                            }catch(\Exception $e){
                                \Think\Log::write($e->getMessage());
                            }


                            $map = array();
                            $map['scene_id'] = $EventKey;
                            $map['wetch_com_id'] = intval($wetch_com_id);
                            $WxQrConfigModel->where($map)->setInc('count');

                            $info = $WxQrConfigModel->where($map)->find();
                            \Think\Log::write(print_r($info,true));
                            if($info){
                                $this->liebian($info,$wechat,$data,$fromOpenid,true,$wetch_com_id,$is_scan_subscribe);
                            }

                        }


                        /**
                         * 普通二维码关注回复
                         */
                        if (isset($data['EventKey'])){
                            $EventKey = str_replace("qrscene_", "", $data['EventKey']);
                            $WxQrConfigModel = D('WxQrConfig');

                            $openid = $data['FromUserName']; //必须先赋值，不然获取不了值
                            $this->addScanCounts($EventKey, $openid,$wetch_com_id); // 扫码统计处理

                            $this->addScanScribe($EventKey, $openid,$wetch_com_id); // 扫码关注统计处理

                            $map = array();
                            $map['scene_id'] = $EventKey;
                            $map['wetch_com_id'] = intval($wetch_com_id);
                            $WxQrConfigModel->where($map)->setInc('count');
                            //根据是否有首关元素
                            //replyText 文本
                            //replyNewsOnce 图文
                            $info = $WxQrConfigModel->where($map)->find();
                            if($info){
                                $title = $info['title'];
                                $description = $info['description'];
                                $url = $info['url'];
                                $picture_url = $info['picture_url'];
                                $info['tt'] = 1;
                                // $this->logger($info);
                                if ($info['type'] == '3') { // 单图输出
                                    $wechat->replyImage($title);die;
                                }
                                if ($info['url'] && $info['title'] && $info['description'] && $info['picture_url']) {
                                    $wechat->replyNewsOnce($title, $description, $url, $picture_url);die;
                                } else if ($description) {
                                    $wechat->replyText($description);die;
                                }
                            }

                        }



                        //首次关注推送信息
//                        $wechat->replyText(C("WX_PUBLIC_XinYChuang.first_desc"));die;

                        //首次关注推送信息
                        $WxReplyModel = new \Wechat\Model\WxReplyModel('wx_keyword','lie_','MKTWX_DB_CONFIG');
//                        $res = $WxReplyModel->where("status = 1 and toUserType=2")->field("type,content")->limit(1)->order("create_time desc")->find();
                        $res = $WxReplyModel->where(sprintf("status = %d and toUserType= %d and wetch_com_id = %d  ",1,2,intval($wetch_com_id)))->field("type,content")
                            ->limit(1)->order("create_time desc")->find();
                        if($res  && !empty($res)){
                            $content  = json_decode($res['content'],true);
                            $this->autoReply($res['type'],$wechat,$content,$data);
                        }else{
                            $wechat->replyText(C("WX_PUBLIC_XinYChuang.first_desc"));die;
                        }
                        die;

                        break;
                    case Wechat::MSG_EVENT_UNSUBSCRIBE:
                        //取消关注，记录日志
                        $openid = $data['FromUserName']; //必须先赋值，不然获取不了值
                        $this->unsubscribe($openid,$wetch_com_id);
                        break;
                    case Wechat::MSG_EVENT_CLICK:
                        // 自定义菜单点击事件
                        if (isset($data['EventKey'])){
                            $EventKey = str_replace("qrscene_", "", $data['EventKey']);
                            $WxQrConfigModel = D('WxQrConfig');

                            $map = array();
                            $map['scene_id'] = $EventKey;
                            $map['wetch_com_id'] = intval($wetch_com_id);
                            $info = $WxQrConfigModel->where($map)->find();
                            $title = $info['title'];
                            $description = $info['description'];
                            $url = $info['url'];
                            $picture_url = $info['picture_url'];
                            if ($info['type'] == '3') { // 单图输出
                                $wechat->replyImage($title);die;
                            }
                            if ($info['url'] && $info['title'] && $info['description'] && $info['picture_url']) {
                                $wechat->replyNewsOnce($title, $description, $url, $picture_url);die;
                            } else if ($description) {
                                $wechat->replyText($description);die;
                            }
                        }
                        break;
                    case Wechat::MSG_EVENT_SCAN:  // 二维码扫描事件
                        if (isset($data['EventKey'])){
                            \Think\Log::write("----普通二维码扫描----");
                            //判断是否是裂变邀请二维码
                            $EventKey = str_replace("qrscene_", "", $data['EventKey']);
                            \Think\Log::write($EventKey);

                            $fromOpenid = "";//分享二维码的用户openid
                            if(strrpos($EventKey,"_@_") === false){
                                $EventKey = $EventKey;
                            }else{
                                list($EventKey,$fromOpenid) = explode("_@_",$EventKey);
                            }

                            $WxQrConfigModel = D('WxQrConfig');
                            // 扫码事件统计 wx_scan_history
                            $openid = $data['FromUserName']; //必须先赋值，不然获取不了值
                            $this->addScanCounts($EventKey, $openid,$wetch_com_id); // 扫码统计处理

                            $map = array();
                            $map['scene_id'] = $EventKey;
                            $map['wetch_com_id'] = intval($wetch_com_id);
                            $WxQrConfigModel->where($map)->setInc('count');
                            $info = $WxQrConfigModel->where($map)->find();
                            $title = $info['title'];
                            $description = $info['description'];
                            $url = $info['url'];
                            $picture_url = $info['picture_url'];
                            if(strrpos($data['EventKey'],"liebian_") !== false){
                                \Think\Log::write("----进入裂变----");
                                //如果是裂变扫码 邀请活动
                                $this->liebian($info,$wechat,$data,$fromOpenid,false,$wetch_com_id,false);
                                die;
                            }



                            if ($info['type'] == '3') { // 单图输出
                                $wechat->replyImage($title);die;
                            }
                            if ($info['url'] && $info['title'] && $info['description'] && $info['picture_url']) {
                                $wechat->replyNewsOnce($title, $description, $url, $picture_url);die;
                            } else if ($description) {
                                $wechat->replyText($description);die;
                            }

                        }
                        break;
                    default:
                        break;
                }
                break;
            case Wechat::MSG_TYPE_TEXT:
                $WxKeywordModel = new \Wechat\Model\WxKeywordModel('wx_keyword','lie_','MKTWX_DB_CONFIG');
                $keyword = $data['Content'];
                $map['keyword'] = array('eq', $keyword);
                $map['status'] = array('eq', '1');
                $map['toUserType'] = array('eq', '1');
                $map['wetch_com_id'] = array('eq', intval($wetch_com_id));
                $field = 'keyword,content,type,status';
                $res = $WxKeywordModel->field($field)->where($map)->find();
                if ($res) {
                    $content = json_decode($res['content'], true);
                    switch (strval($res['type'])) {
                        case '1': // 单文
                            $wechat->replyText($content['content']);die;
                            break;
                        case '2': // 图文
                            $wechat->replyNewsOnce($content['title'], $content['content'], $content['link'], $content['img']);die;
                            break;
                        case '3': // 单图
                            $wechat->replyImage($content['img']);die;
                            break;
                        case '4': // 复合
                            $wechatModel = wechat();
                            $openid = $data['FromUserName']; //必须先赋值，不然获取不了值

                            if ($content['content']) {
                                $redata = array(
                                    'touser' => $openid,
                                    "msgtype" => "text",
                                    "text" => array(
                                        'content' => $content['content'],
                                    ),
                                );
                                $wechatModel->sendCustomMessage($redata);
                            }
                            if ($content['contentsec']) {
                                $redata = array(
                                    'touser' => $openid,
                                    "msgtype" => "text",
                                    "text" => array(
                                        'content' => $content['contentsec'],
                                    ),
                                );
                                $wechatModel->sendCustomMessage($redata);
                            }

                            if ($content['img']) {
                                $redata['msgtype'] = 'image';
                                $redata['touser'] = $openid;
                                $redata['image'] = array(
                                    'media_id' => $content['img'],
                                );
                                $wechatModel->sendCustomMessage($redata);
                            }


                            break;
                        default:
                            # code...
                            break;
                    }

                } else {
                    if($this->autoReplyLimit($data)) die;

                    $WxReplyModel = new \Wechat\Model\WxReplyModel('wx_keyword','lie_','MKTWX_DB_CONFIG');
                    $res = $WxReplyModel->where(sprintf("status = %d and toUserType= %d and wetch_com_id = %d  ",1,3,intval($wetch_com_id)))->field("type,content")
                        ->limit(1)->order("create_time desc")->find();
                    if($res  && !empty($res)){
                        $content  = json_decode($res['content'],true);
                        $this->SetAutoReplyLimit($data);
                        $this->autoReply($res['type'],$wechat,$content,$data);
                    }
//                        $wechat->replyText('暂无此关键词相关信息~_~');
                    $this->SetAutoReplyLimit($data);
                    die;
                }

                break;
            default:
                # code...
                break;
        }
    }

    /*
     * 获取accesstoken
     */
    protected function getAccessToken($openId=""){
        $key = $openId+"_ichuntAccessToken";
        if(S($key)){
            return S($key);
        }
        $appid = C('WX_PUBLIC.appid');
        $appkey = C('WX_PUBLIC.appkey');
        $result = get_curl("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$appkey}");
        \Think\Log::write($result);
        if ($result)
        {
            $json = json_decode($result,true);
            if (!$json || isset($json['errcode'])) {
                return false;
            }
            $access_token = $json['access_token'];
            $expire = $json['expires_in'] ? intval($json['expires_in'])-200 : 3600;
            S($key,$access_token,['expire'=>$expire]);
            return $access_token;
        }
    }

    /*
     * 获取关注用户的信息
     */
    protected function getUnionID($openId=""){
        try{
            $ACCESS_TOKEN =  $this->getAccessToken($openId);
            if(!$ACCESS_TOKEN){
                return false;
            }
            $result = get_curl("https://api.weixin.qq.com/cgi-bin/user/info?access_token={$ACCESS_TOKEN}&openid={$openId}&lang=zh_CN");
            if ($result)
            {
                $json = json_decode($result,true);
                if (!$json || isset($json['errcode'])) {
                    return false;
                }
                return $json;
            }
        }catch(\Exception $e){
            return "";
        }
    }

    /*
     * $type 猎芯平台
     */
    public function response($wechat, $data,$wetch_com_id=1)
    {
            switch ($data['MsgType']) {
                case Wechat::MSG_TYPE_EVENT:
                    switch ($data['Event']) {
                        case Wechat::MSG_EVENT_SUBSCRIBE://关注事件

                            ///临时代码 by long  抽奖
                            try{
                                \Think\Log::write(json_encode($data));
//                                \Think\Log::write(print_r($this->getUnionID("o3We-wHBb_ipwLr2SG2qa4glRqXc"),true));
                                if(isset($data['Event']) && $data['Event'] == 'subscribe' && strrpos($data['EventKey'],"qrscene_choujiang-") !== false ){
                                    list(,$user_id,$lottery_id) = explode("-",$data['EventKey']);
                                    $k1 = time();
                                    $res = post_curl(API_DOMAIN.'/Activity/Lottery/increaseQualifyByFollow', [
                                        'lottery_id'=>intval($lottery_id),
                                        'pf'=>3,
                                        'user_id'=>$user_id,
                                        'k1'=>$k1,
                                        "k2"=>md5(md5($k1).C('SUPER_AUTH_KEY'))
                                    ]);
                                    \Think\Log::write($res);
                                    $choujiang_config = C('CHOU_JIANG');
                                    \Think\Log::write(json_encode($choujiang_config));
                                    $wechat->replyNewsOnce($choujiang_config['title'],
                                        $choujiang_config['des'], $choujiang_config['url'],
                                        $choujiang_config['img']);
                                    die;
                                }
                            }catch(\Exception $e){

                            }

                            /**
                             * 普通二维码关注回复
                             */
                            if (isset($data['EventKey'])){
                                $EventKey = str_replace("qrscene_", "", $data['EventKey']);
                                $WxQrConfigModel = D('WxQrConfig');

                                $openid = $data['FromUserName']; //必须先赋值，不然获取不了值
                                $this->addScanCounts($EventKey, $openid); // 扫码统计处理

                                $this->addScanScribe($EventKey, $openid); // 扫码关注统计处理

                                $map = array();
                                $map['scene_id'] = $EventKey;
                                $map['wetch_com_id'] = intval($wetch_com_id);
                                $WxQrConfigModel->where($map)->setInc('count');
                                //根据是否有首关元素
                                //replyText 文本
                                //replyNewsOnce 图文
                                $info = $WxQrConfigModel->where($map)->find();
                                if($info){
                                    $title = $info['title'];
                                    $description = $info['description'];
                                    $url = $info['url'];
                                    $picture_url = $info['picture_url'];
                                    $info['tt'] = 1;
                                    // $this->logger($info);
                                    if ($info['type'] == '3') { // 单图输出
                                        $wechat->replyImage($title);die;
                                    }
                                    if ($info['url'] && $info['title'] && $info['description'] && $info['picture_url']) {
                                        $wechat->replyNewsOnce($title, $description, $url, $picture_url);die;
                                    } else if ($description) {
                                        $wechat->replyText($description);die;
                                    }
                                }

                            }

                            //首次关注推送信息

                            $WxReplyModel = new \Wechat\Model\WxReplyModel('wx_keyword','lie_','MKTWX_DB_CONFIG');
                            $res = $WxReplyModel->where(sprintf("status = %d and toUserType= %d and wetch_com_id = %d  ",1,2,intval($wetch_com_id)))->field("type,content")
                                ->limit(1)->order("create_time desc")->find();
                            //获取关注用户的基本信息
                            $wechatUserInfo = $this->getUnionID($data['FromUserName']);
                            $this->addWechatOauth($wechatUserInfo);
                            if($res  && !empty($res)){
                                $content  = json_decode($res['content'],true);
                                $this->autoReply($res['type'],$wechat,$content,$data);
                            }else{
                                $wechat->replyText('欢迎关注猎芯网');die;
                            }
                            die;

                            break;
                        case Wechat::MSG_EVENT_UNSUBSCRIBE:
                            //取消关注，记录日志
                            $openid = $data['FromUserName']; //必须先赋值，不然获取不了值
                            $this->unsubscribe($openid);
                            break;
                        case Wechat::MSG_EVENT_CLICK:
                            // 自定义菜单点击事件
                            if (isset($data['EventKey'])){
                                $EventKey = str_replace("qrscene_", "", $data['EventKey']);
                                $WxQrConfigModel = D('WxQrConfig');

                                $map = array();
                                $map['scene_id'] = $EventKey;
                                $map['wetch_com_id'] = intval($wetch_com_id);
                                $info = $WxQrConfigModel->where($map)->find();
                                $title = $info['title'];
                                $description = $info['description'];
                                $url = $info['url'];
                                $picture_url = $info['picture_url'];
                                if ($info['type'] == '3') { // 单图输出
                                    $wechat->replyImage($title);die;
                                }
                                if ($info['url'] && $info['title'] && $info['description'] && $info['picture_url']) {
                                    $wechat->replyNewsOnce($title, $description, $url, $picture_url);die;
                                } else if ($description) {
                                    $wechat->replyText($description);die;
                                }
                            }
                            break;
                        case Wechat::MSG_EVENT_SCAN:  // 二维码扫描事件
                            if (isset($data['EventKey'])){
                                $EventKey = str_replace("qrscene_", "", $data['EventKey']);
                                $WxQrConfigModel = D('WxQrConfig');
                                // 扫码事件统计 wx_scan_history
                                $openid = $data['FromUserName']; //必须先赋值，不然获取不了值
                                $this->addScanCounts($EventKey, $openid); // 扫码统计处理

                                $map = array();
                                $map['scene_id'] = $EventKey;
                                $map['wetch_com_id'] = intval($wetch_com_id);
                                $WxQrConfigModel->where($map)->setInc('count');
                                $info = $WxQrConfigModel->where($map)->find();
                                $title = $info['title'];
                                $description = $info['description'];
                                $url = $info['url'];
                                $picture_url = $info['picture_url'];
                                // $this->logger($info);


                                if ($info['type'] == '3') { // 单图输出
                                    $wechat->replyImage($title);die;
                                }
                                if ($info['url'] && $info['title'] && $info['description'] && $info['picture_url']) {
                                    $wechat->replyNewsOnce($title, $description, $url, $picture_url);die;
                                } else if ($description) {
                                    $wechat->replyText($description);die;
                                }

                            }
                            break;
                        default:
                            break;
                    }
                    break;
                case Wechat::MSG_TYPE_TEXT:
                    $WxKeywordModel = new \Wechat\Model\WxKeywordModel('wx_keyword','lie_','MKTWX_DB_CONFIG');
                    $keyword = $data['Content'];
                    $map['keyword'] = array('eq', $keyword);
                    $map['status'] = array('eq', '1');
                    $map['toUserType'] = array('eq', '1');
                    $map['wetch_com_id'] = array('eq', intval($wetch_com_id));
                    $field = 'keyword,content,type,status';
                    $res = $WxKeywordModel->field($field)->where($map)->find();
                    if ($res) {
                        $content = json_decode($res['content'], true);
                        switch (strval($res['type'])) {
                            case '1': // 单文
                                $wechat->replyText($content['content']);die;
                                break;
                            case '2': // 图文
                                $wechat->replyNewsOnce($content['title'], $content['content'], $content['link'], $content['img']);die;
                                break;
                            case '3': // 单图
                                $wechat->replyImage($content['img']);die;
                                break;
                            case '4': // 复合
                                $wechatModel = wechat();
                                $openid = $data['FromUserName']; //必须先赋值，不然获取不了值

                                if ($content['content']) {
                                    $redata = array(
                                        'touser' => $openid,
                                        "msgtype" => "text",
                                        "text" => array(
                                            'content' => $content['content'],
                                        ),
                                    );
                                    $wechatModel->sendCustomMessage($redata);
                                }
                                if ($content['contentsec']) {
                                    $redata = array(
                                        'touser' => $openid,
                                        "msgtype" => "text",
                                        "text" => array(
                                            'content' => $content['contentsec'],
                                        ),
                                    );
                                    $wechatModel->sendCustomMessage($redata);
                                }

                                if ($content['img']) {
                                    $redata['msgtype'] = 'image';
                                    $redata['touser'] = $openid;
                                    $redata['image'] = array(
                                        'media_id' => $content['img'],
                                    );
                                    $wechatModel->sendCustomMessage($redata);
                                }


                                break;
                            default:
                                # code...
                                break;
                        }

                    } else {
                        if($this->autoReplyLimit($data)) die;

                        $WxReplyModel = new \Wechat\Model\WxReplyModel('wx_keyword','lie_','MKTWX_DB_CONFIG');
                        $res = $WxReplyModel->where(sprintf("status = %d and toUserType= %d and wetch_com_id = %d  ",1,3,intval($wetch_com_id)))->field("type,content")
                            ->limit(1)->order("create_time desc")->find();
                        if($res  && !empty($res)){
                            $content  = json_decode($res['content'],true);
                            $this->SetAutoReplyLimit($data);
                            $this->autoReply($res['type'],$wechat,$content,$data);
                        }
                        $this->SetAutoReplyLimit($data);
                        die;
                    }

                    break;
                default:
                    # code...
                    break;
            }
    }

    protected function addWechatOauth($wechatUserInfo){
        try{
//            $res = M()->table("lie_wechat_oauth")->where(["union_id"=>trim($wechatUserInfo['unionid']),"open_id"=>trim($wechatUserInfo["openid"])])->count();
//            \Think\Log::write(print_r($wechatUserInfo,true));
//            \Think\Log::write(print_r($res,true));
//            if($res <= 0){
//            \Think\Log::write("111111111111111111111111111111111111111");
                $data =  [
                    "oauth_type"=>3,
                    "open_id"=>trim($wechatUserInfo['openid']),
                    "bind_time"=>time(),
                    "oauth_head"=>$wechatUserInfo['headimgurl'],
                    "oauth_nickname"=>$wechatUserInfo['nickname'],
                    "oauth_status"=>1,
                    "union_id"=>trim($wechatUserInfo['unionid']),
                ];
//                M()->table("lie_wechat_oauth")->add($data);
                $RbmqModel = D('Common/Rbmq');
                $rbmq  = $RbmqModel->connect('WMS_RBMQ_CONFIG')->queue(C("PUSHWECHATINFO_TO_YUNXIN"));
                $rbmq->push($data, C("PUSHWECHATINFO_TO_YUNXIN"));
//            }
        }catch(\Exception $e){

        }
    }

    /*
     * 判断是否一个小时之内有回复
     */
    protected function autoReplyLimit($data){
        if(isset($data['FromUserName']) && $data['FromUserName'] && S($data['FromUserName']."_".$data["ToUserName"])){
            return true;
        }
        return false;
    }

    /*
     * 每小时只回复一次普通消息
     */
    protected function SetAutoReplyLimit($data){
        if(isset($data['FromUserName']) && $data['FromUserName']){
            S($data['FromUserName']."_".$data["ToUserName"],1,['expire'=>3600]);
        }
    }

    /*
     * 普通消息 自动回复
     */
    private function autoReply($type,$wechat,$content,$data){
        switch (strval($type)) {
            case '1': // 单文
                $wechat->replyText($content['content']);die;
                break;
            case '3': // 单图
                $wechat->replyImage($content['img']);die;
                break;
            case '2': // 图文
                $wechat->replyNewsOnce($content['title'], $content['content'], $content['link'], $content['img']);die;
                break;
            default:
                break;
        }
    }

    /** 统计到活动扫码人数 到Redis
     * INVITE_QRCODE_COUNT_KEY 扫码总人数  不去重
     * INVITE_QRCODE_COUNT_DISTINCT_KEY   扫码去重gid 统计gid则为实际去重扫码人数
     * @return array
     */
    public function addQrcodeCount(){
        $invite_user_id = I('invite_user_id');
        $activity_id = I('activity_id');

        $res = S_invite_qrcode_count($invite_user_id,$activity_id);
        $count = !empty($res)?($res+1):1;
        S_invite_qrcode_count($invite_user_id,$activity_id,$count);

        if(!empty($_COOKIE['Yo4teW_gid'])){
            $ret = S_invite_qrcode_count_distinct($invite_user_id,$activity_id);
            if(!empty($ret)){
                $data = explode(',',$ret);
                if(false===in_array($_COOKIE['Yo4teW_gid'],$data)){
                    $data[] = $_COOKIE['Yo4teW_gid'];
                    $ret = implode(',',$data);
                }
            }else{
                $ret = $_COOKIE['Yo4teW_gid'];
            }
            S_invite_qrcode_count_distinct($invite_user_id,$activity_id,$ret);
        }
        return $this->apiReturn(0, 'success');
    }

    public function sendEmailRemianMsg(){
        $wechatModel = wechatPublic(1);
        $openId = I("openId","","trim");
        $first_value = I("first_value","","trim");
        $keyword1 = I("keyword1","","trim");
        $keyword2 = I("keyword2","","trim");
        $remark = I("remark","","trim");

//        $data["touser"] = 'oCmCit5m3L5JmD3t7hCqrp65R-0k';
        $data["touser"] = $openId;
        $data["template_id"] = "h4aBE1QkPFN69vupN93wBZAsKmnsyg6wkGhafOxOSXc";
        $data['data']["first"]["value"] = $first_value;
        $data['data']["first"]["color"] = '#173177';
        $data['data']["keyword1"]["value"] = $keyword1;
        $data['data']["keyword1"]["color"] = '#173177';
        $data['data']["keyword2"]["value"] = $keyword2;
        $data['data']["keyword2"]["color"] = '#173177';
        $data['data']["remark"]["value"] = $remark;
        $data['data']["remark"]["color"] = '#173177';
        $bk = $wechatModel->sendTemplateMessage($data);
        header('Content-Type:application/json; charset=utf-8');
        echo  json_encode(["code"=>0,"msg"=>"发送成功"]);
        exit;
    }

}