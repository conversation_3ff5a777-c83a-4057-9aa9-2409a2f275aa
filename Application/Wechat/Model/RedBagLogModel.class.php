<?php
namespace Wechat\Model;

use Think\Model;

class RedBagLogModel extends Model
{
    /**
     * 检验某用户是否已经获取红包（参与）活动
     * @param  [type] $openid [description]
     * @param  string $where   [description]
     * @return [type]          [description]
     */
    public function getRecordByOpenID($openid, $activity_type = 1)
    {
        if (!$openid) {
            return false;
        }
        $map = array(
            'status' => 1, //已领取
            'openid' => $openid,
            'activity_type' => $activity_type, // 活动类型（现金红包）
        );
        $RedBagLogModel = D('RedBagLog');
        $counts = $RedBagLogModel->where($map)->count();
        return $counts ? $counts : false;
    }
}