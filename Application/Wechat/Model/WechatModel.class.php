<?php
namespace Wechat\Model;

use Think\Model;

class WechatModel extends Model
{
    Protected $autoCheckFields = false;
    private $private_key = 'ichunt201507ichunt20170317165411';//32位的密钥，在商户平台上设置的

    private $mch_id = "1310276901"; // 商户id
    private $wxappid = 'wx0078c0d21d19e76b'; // appid

    //核心支付函数,参数：请求地址和参数
    public function pay($url, $obj)
    {
        $obj['nonce_str'] = $this->create_noncestr();    //创建随机字符串
        $stringA = $this->create_sign($obj, false);    //创建签名
        $stringSignTemp = $stringA . "&key=" . $this->private_key;    //签名后加api
        $sign = strtoupper(md5($stringSignTemp));    //签名加密并大写
        $obj['sign'] = $sign;    //将签名传入数组
        $postXml = $this->arrayToXml($obj);    //将参数转为xml格式
        $responseXml = $this->curl_post_ssl($url, $postXml);    //提交请求
        return $responseXml;
    }

    // 获取现金红包的金额
    public function getRedBagNumber()
    {
        // 设置红包金额（不得超过微信账户里的剩余金额）
        $RedBagLogModel = D('RedBagLog');
        $map = array(
            'status' => 1,
            'activity_type' => 1, // 默认活动类型为现金红包
        );
        $total_amount = $RedBagLogModel->where($map)->sum('total_amount'); // 目前已发出红包金额
        if (intval($total_amount) >= C('RED_BAG_AMOUNT')) {
            return false;
        }
        $left_amount = intval(C('RED_BAG_AMOUNT')) - intval($total_amount); // 目前的余额
        $min = C('RB_MIN_MONEY');
        $max = C('RB_MAX_MONEY');
        if ($left_amount >= C('RB_BIG_MONEY')) {
            // 是否是特殊时间段 需要调整时间，不要10:30准时，可以10:31，两天时间都要不一样
                if (time() > intval(C('RB_START_TIME')) && time() < intval(C('RB_END_TIME'))) {
                    // 今天是否已经有人获取100元红包
                    $maps = array(
                        'status' => 1,
                        'activity_type' => 1,
                        'total_amount' => C('RB_BIG_MONEY'), //100块，以分为单位
                    );
                    $maps['time'] = array('gt', strtotime(date('Y-m-d'))); // 每日只有一个
                    $counts = $RedBagLogModel->where($maps)->count();
                    if ($counts) { // 表明今日已抢过
                        $money_numbers = mt_rand($min, $max);
                    } else { //今日没领过,可以获取
                        $money_numbers = C('RB_BIG_MONEY');
                    }
                } else {
                    // 普通获取金额在1到3.88之间
                    $money_numbers = mt_rand($min, $max);
                }
        } else if ($left_amount >= $max) {
            $money_numbers = mt_rand($min, $max);
        } else if ($left_amount >= $min) {
            $money_numbers = mt_rand($min, $left_amount);
        } else { // 余额不足
            $money_numbers = 0;
        }
        return $money_numbers;
    }
    public function getRedBag($openid)
    {
        if (!$openid) {
            return false;
        }
        // 检查是否可以发红包
        $RedBagLogModel = D('RedBagLog');
        $openIdArr = array(
            'oCmCit9ZVeF7HaF_7rxfaLkB6LV4', // 测试时用
        );
        if (!in_array($openid,$openIdArr)) {
            $counts = $RedBagLogModel->getRecordByOpenID($openid);
            if ($counts) { // 表明已经领取过红包了
                return false;
            }
        }

        // 获取红包金额
        $money_sum = $this->getRedBagNumber(); // 以分为单位
        if (!$money_sum) {
            return false;
        }

        $sender = "猎芯网";

        //设置调接口的参数
        $pay_params = array();
        $pay_params['wxappid'] = $this->wxappid; //appid
        $pay_params['mch_id'] = $this->mch_id;//商户id
        $pay_params['mch_billno'] = $pay_params['mch_id'] . date('YmdHis') . rand(1000, 9999);//组合成28位，根据官方开发文档，可以自行设置
        $pay_params['client_ip'] = get_client_ip(0, true);
        $pay_params['re_openid'] = $openid;//接收红包openid
        $pay_params['total_amount'] = $money_sum;
        $pay_params['min_value'] = $money_sum;
        $pay_params['max_value'] = $money_sum;
        $pay_params['total_num'] = 1;//发放给的人数
        $pay_params['nick_name'] = $sender;
        $pay_params['send_name'] = $sender;
        $pay_params['wishing'] = "恭喜发财";
        $pay_params['act_name'] = $sender . "关注有奖";
        $pay_params['remark'] = $sender . "红包";

        $url = "https://api.mch.weixin.qq.com/mmpaymkttransfers/sendredpack";
        $pay_result = $this->pay($url, $pay_params);
        $responseObj = simplexml_load_string($pay_result, 'SimpleXMLElement', LIBXML_NOCDATA);

        $data['pay_params'] = $pay_params;
        $data['pay_result'] = $pay_result;
        $data['responseObj'] = $responseObj;

        //获取openid对应的昵称
        //导入类库
        Vendor('Wechat.Wechat');
        //实例化微信
        $option = array(
                'token'=> 'weixin', //填写你设定的key
                'encodingaeskey'=> '', //填写加密用的EncodingAESKey
                'appid'=> 'wx0078c0d21d19e76b', //填写高级调用功能的app id, 请在微信开发模式后台查询
                'appsecret'=> 'aae1e225933a645045436ffbc223fc20', //填写高级调用功能的密钥
        );
        $we_obj = new \Wechat($option);
        $user_info = $we_obj->getUserInfo($openid);
        $nick_name = $user_info['nickname'];

        $responseArr = (array)$responseObj;
        $err_code_des = $responseArr['err_code_des'];
        $err_code = $responseArr['err_code'];
        $mch_billno = $responseArr['mch_billno'];
        $send_listid = $responseArr['send_listid'];

        //记录日志
        $addLogDatas = array(
            'openid' => $openid ? $openid : '',
            'activity_type' => 1,
            'nick_name' => $nick_name ? $nick_name : '',
            'status' => -1,
            'total_amount' => $money_sum ? $money_sum : '',
            'time' => time(),
            'err_code' => $err_code ? $err_code : '',
            'err_code_des' => $err_code_des ? $err_code_des : '',
            'err_json' => json_encode($responseArr) ? json_encode($responseArr) : '',
        );

        //判断发送成功需下面两个值都为SUCCESS
        if ($pay_result && $responseArr['return_code'] == 'SUCCESS' && $responseArr['result_code'] == 'SUCCESS') {
            file_put_contents('./data_redbag.json', json_encode($data));
            $addLogDatas['status'] = 1;
            $addLogDatas['mch_billno'] = $mch_billno ? $mch_billno : '';
            $addLogDatas['send_listid'] = $send_listid ? $send_listid : '';
            $RedBagLogModel->data($addLogDatas)->add();
            return true;
        }
        file_put_contents('./data_redbag.json', json_encode($data));
        $RedBagLogModel->data($addLogDatas)->add();
        return false;
    }

    //生成签名,参数：生成签名的参数和是否编码
    public function create_sign($arr, $urlencode)
    {
        $buff = "";
        ksort($arr); //对传进来的数组参数里面的内容按照字母顺序排序，a在前面，z在最后（字典序）
        foreach ($arr as $k => $v) {
            if (null != $v && "null" != $v && "sign" != $k) {    //签名不要转码
                if ($urlencode) {
                    $v = urlencode($v);
                }
                $buff .= $k . "=" . $v . "&";
            }
        }
        if (strlen($buff) > 0) {
            $reqPar = substr($buff, 0, strlen($buff) - 1); //去掉末尾符号“&”
        }
        return $reqPar;
    }

    //生成随机字符串，默认32位
    public function create_noncestr($length = 32)
    {
        //创建随机字符
        $chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

    //数组转xml
    public function arrayToXml($arr)
    {
        $xml = "<xml>";
        foreach ($arr as $key => $val) {
            if (is_numeric($val)) {
                $xml .= "<" . $key . ">" . $val . "</" . $key . ">";
            } else {
                $xml .= "<" . $key . "><![CDATA[" . $val . "]]></" . $key . ">";
            }
        }
        $xml .= "</xml>";
        return $xml;
    }

    //post请求网站，需要证书
    public function curl_post_ssl($url, $vars, $second = 30, $aHeader = array())
    {
        $ch = curl_init();
        //超时时间
        curl_setopt($ch, CURLOPT_TIMEOUT, $second);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        //这里设置代理，如果有的话
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        //cert 与 key 分别属于两个.pem文件
        //请确保您的libcurl版本是否支持双向认证，版本高于7.20.1
        curl_setopt($ch, CURLOPT_SSLCERT, VENDOR_PATH . 'payment/wxpay/certs/apiclient_cert.pem');
        curl_setopt($ch, CURLOPT_SSLKEY, VENDOR_PATH . 'payment/wxpay/certs/apiclient_key.pem');
        curl_setopt($ch, CURLOPT_CAINFO, VENDOR_PATH . 'payment/wxpay/certs/rootca.pem');
        if (count($aHeader) >= 1) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $aHeader);
        }
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $vars);
        $data = curl_exec($ch);
        if ($data) {
            curl_close($ch);
            return $data;
        } else {
            $error = curl_errno($ch);
            echo "call faild, errorCode:$error\n";
            curl_close($ch);
            return false;
        }
    }
}