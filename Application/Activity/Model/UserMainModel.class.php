<?php
namespace Activity\Model;

use Think\Model;

class UserMainModel extends Model
{
    /**
     * 获取邀请人的邀请记录，以及统计人数
     * @param  [type] $user_id [邀请人的user_id]
     * @return [type]          [description]
     */
    public function getInviteInfoByUserId($user_id)
    {
        $where = array('invite_uid' => $user_id);
        $res = $this->field('user_id, mobile, email')->where($where)->select();
        $count = count($res);
        $info = array();
        if ($count) {
            foreach ($res as $key => $value) {
                if ($value['mobile']) {
                    $info['link'][$key] = $value['mobile'];
                } else if ($value['email']) {
                    $info['link'][$key] = $value['email'];
                }
                $info['link'][$key] = hideStar($info['link'][$key]);
            }
            $info['count'] = $count;
        } else {
            $info['count'] = 0;
            $info['link'] = '';
        }
        //生成3位数，不足前面补0
        //$info['count']=sprintf("%03d", $info['count']);
        return $info ? $info : false;
    }

    /**
     * 优惠券展示
     * $user_id ,优惠券类型type:1（邀请别人注册） 2（被邀请注册）
     */
    public function getCouponList($user_id)
    {
        $where['invite_uid'] = array('eq', $user_id);
        $res = $this->field('create_time')->where($where)->select();
        $count = count($res);
        $info = array();
        $infos = array();
        $user_info = S_user($user_id);
        if ($count) { //说明其他会员是通过该会员邀请注册，该会员每次获得9.7折优惠券
            foreach ($res as $key => $value) {
                $info[$key]['type'] = 2;
                $start_time = date("Y.m.d", $value['create_time']);
                $end_time = date("Y.m.d", intval($value['create_time']) + 60 * 60 * 24 * 30);
                $info[$key]['overtime'] = $start_time . '-' . $end_time;
            }
        }
        if ($user_info['invite_uid']) { //说明通过邀约途径注册，可获得9.8折优惠券
            $infos['type'] = 1;
            $start_time = date("Y.m.d", $user_info['create_time']);
            $end_time = date("Y.m.d", intval($user_info['create_time']) + 60 * 60 * 24 * 30);
            $infos['overtime'] = $start_time . '-' . $end_time;
            $count = $count + 1;
            $info[$count] = $infos;
        }
        return $info ? $info : false;
    }
}