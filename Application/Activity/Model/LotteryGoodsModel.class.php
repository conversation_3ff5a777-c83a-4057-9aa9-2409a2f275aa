<?php
namespace Activity\Model;

use Think\Model;

class LotteryGoodsModel extends Model
{
    //概率抽奖获取奖品编号
    public function getPrizeBatchByLotteryGoodsInfo($lotteryGoodsInfo)
    {
        if (!$lotteryGoodsInfo || !count($lotteryGoodsInfo)) {
            return false;
        }
        $arr = array();
        foreach ($lotteryGoodsInfo as $key => $val) {
          $arr[$key+1] = $val['chance'];
        }
        $resNum = get_rand($arr); //根据概率获取奖项id (对应前台奖品位置编号)
        $prize_key = intval($resNum)-1;
        if (!$lotteryGoodsInfo[$prize_key]['goods_real_stock']) { //假如刚好没有库存，再随机一次
            $resNum = get_rand($arr); //根据概率获取奖项id (对应前台奖品位置编号)
            $prize_key = intval($resNum)-1;
        }
        $resPrizeName = $lotteryGoodsInfo[$prize_key]['goods_name']; //奖品名称
        $res['prize_key'] = $prize_key; //奖品数组的key
        $res['prize_key_id'] = $resNum; //奖品数组的key+1
        $res['prize_name'] = $resPrizeName;
        return $res ? $res : '';
    }
}