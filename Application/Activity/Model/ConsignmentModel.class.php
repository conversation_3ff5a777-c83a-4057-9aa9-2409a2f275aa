<?php
namespace Activity\Model;

use Think\Model;

class ConsignmentEmailSendModel extends Model
{
    
    //批量发送邮件给通知人员，便于去后台审核
    public function sendEmail(){
        $where['status'] = array('eq',1);
        $list = $this->where($where)->getField('ces_email',true);
        foreach ($list as $key => $value) {
            $toemail = $value;
            $time = date("Y-m-d H:i:s",time());
            $title = '猎芯网-系统后台通知';
            $html = '<p>【猎芯寄售】有新的寄售需求</p><br><p>'.$time.'&nbsp;&nbsp;有客户提交寄售需求，快去处理吧！~</p>';
            sendMail($toemail,$title,$html);
        }
    }
}