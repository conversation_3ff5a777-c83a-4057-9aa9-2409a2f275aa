<?php

namespace Activity\Model;

use Think\Model;

class ActivityNewModel extends Model
{
    protected $tableName = 'activity_new';
    protected $connection = 'SPECIAL_DB_CONFIG';

    //获取所有需要展示在活动中心的活动
    public function getActivityCenterActivityList()
    {
        $activities = $this->where(['is_activity_center_show' => 1])
            ->where(['org_id' => 1])
            ->where(['activity_enable' => 1])
            ->where(['activity_status' => 3])
            ->where(['activity_end_time' => ['gt', time()]])->order('create_time desc')->select();
        $result = [];
        foreach ($activities as $activity) {
            $result[] = array_only($activity, [
                'activity_url',
                'web_title',
                'activity_name',
                'web_description',
                'image_pc',
                'image_h5',
                'create_time'
            ]);
        }
        $result = array_map(function ($value) {
            $value['create_time'] = date('Y-m-d H:i:s', $value['create_time']);
            return $value;
        }, $result);
        return $result;
    }

}
