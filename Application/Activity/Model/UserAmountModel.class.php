<?php
namespace Activity\Model;

use Think\Model;

class UserAmountModel extends Model
{
	// 获取排名前十的用户
	public function getUserRank()
	{
		return $this->field('amount_id, user_id, account, amount')->order('amount desc')->limit(10)->select();
	}
        // 获取排名前十的用户
	public function addData($data)
	{   
        $res  = $this->where(array('account'=>$data['account']))->find();
        if(!$res){
            $this->add($data);
        }else{
            $this->where(array('user_id'=>0, 'account'=>$data['account']))->save($data);
        }
            
	}

}