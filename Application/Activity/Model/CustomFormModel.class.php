<?php

namespace Activity\Model;

use Think\Model;

class CustomFormModel extends Model
{
    protected $tableName = 'custom_form_data';
    protected $connection = 'SPECIAL_DB_CONFIG';

    //新增表单模块数据提交
    public function addCustomFormData($data)
    {
        $data['create_time'] = time();
        if (is_array($data['form_data'])) {
            $data['form_data'] = json_encode($data['form_data']);
        }
        $data['form_data'] = stripslashes($data['form_data']);
        $result = D('CustomForm')->add($data);
        return $result;
    }

}