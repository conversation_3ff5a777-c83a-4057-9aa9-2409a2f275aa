<?php

namespace Activity\Model;

use Think\Model;

class SampleModel extends Model
{
    protected $tableName = 'user_sample_apply';

    //判断工具尺是否能领取
    public function getCanGetRuler($user_id, $invite_count)
    {
        //判断是否能领取工具尺,首要条件是邀请人数大于等于3
        if ($invite_count >= 3) {
            //直接判断是否有工具尺的订单
            $orderItem = D('OrderItems')->where([
                'user_id' => $user_id,
                'goods_id' => C('RULER_ACTIVITY_GOODS_ID')
            ])->order('order_id desc')->limit(1)->find();
            if ($orderItem) {
                //再去找对应的订单表看是否是非取消的订单
                $order = D('Order')->where(['order_id' => $orderItem['order_id']])->find();
                //已经取消的或者审核不通过单子,可以再去领取的,所以可领取设置为1
                if ($order['status'] == -1 || $order['status'] == -2) {
                    return true;
                } else {
                    return false;
                }
            } else {
                //没有订单,可以领取
                return true;
            }
        }
        return false;
    }

    //判断是否有工具尺订单
    public function checkHasRulerOrder($user_id)
    {
        $orderItem = D('OrderItems')->where([
            'user_id' => $user_id,
            'goods_id' => C('RULER_ACTIVITY_GOODS_ID')
        ])->order('order_id desc')->limit(1)->find();
        if ($orderItem) {
            //再去找对应的订单表看是否是非取消的订单
            $order = D('Order')->where(['order_id' => $orderItem['order_id']])->find();
            //已经取消的或者审核不通过单子,可以再去领取的,所以可领取设置为1
            if ($order['status'] == -1 || $order['status'] == -2) {
                return false;
            } else {
                return true;
            }
        } else {
            //没有订单,可以领取
            return false;
        }
    }

    //判断是否可以申请样片(如果已经有样片订单的,且订单不为取消的(不等于-1),不允许再申请)
    public function checkCanApplyDomesticGoods($userId, $goodsId)
    {
        //先去样片申请表找出类型为3(国产样片)的订单
        $apply = $this->where(['type' => 3, 'user_id' => $userId, 'goods_id' => $goodsId])->order('apply_id desc')->find();
        if (!empty($apply['order_id'])) {
            //再去找订单表看订单状态,如果是不为取消的,就不允许申请
            $order = D('Order')->where(['order_id' => $apply['order_id']])->find();
            return $order['status'] != -1 ? false : true;
        }
        return true;
    }
}