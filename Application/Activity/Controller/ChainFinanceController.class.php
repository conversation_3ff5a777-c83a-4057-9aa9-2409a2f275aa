<?php
namespace Activity\Controller;

use Activity\Controller\BaseController;

class ChainFinanceController extends BaseController
{
    const VERIFY_LIMIT_COUNT = 3;//验证码出现时机
    /**
     * 报名
     * @return [type] [description]
     */
    public function apply()
    {
        $type = I('request.type', 0, 'intval'); //服务类型
        $linkType = I('request.linkType', 0, 'intval'); //联系类型
        $mobile = I('request.mobile', ''); //联系号码
        $linkName = I('request.link_name', ''); //联系人
        $area_num = I('request.area_num', '');
        $verify = I('request.verify', '');
        //输入除数字之外的内容，或者位数错误提示：手机号码格式错误
        if (empty($linkName)) {
            return $this->apiReturn(110005, '请填写姓名~');
        } elseif (empty($mobile)) {
            return $this->apiReturn(110006, '请填写联系号码~');
        } elseif (empty($linkType)) {
            return $this->apiReturn(110010, '联系类型不能为空~');
        } elseif (empty($type)) {
            return $this->apiReturn(110011, '供应链金融服务类型不能为空~');
        } elseif (!is_numeric($mobile)) {
            return $this->apiReturn(110001, '联系号码请填写数字~');
        }

        if ($linkType == 2) {
            if(empty($area_num)) {
                return $this->apiReturn(110002, '区号不能为空~');
            }

            $mobile = $area_num . '-' . $mobile;
            if (!is_mobile_phone($mobile)) {
               return $this->apiReturn(110003, '座机号码格式有误~');
            }
        } elseif ($linkType == 1) {
            if (!is_mobile($mobile)) {
               return $this->apiReturn(110004, '手机号码格式有误~');
            }
        }
        $ChainFinanceModel = D('ChainFinance');

        $map['link_type'] = $linkType;
        $map['link_text'] = $mobile;
        $map['source'] = array('eq', 1);
        $map['type'] = array('eq', $type);
        $count = $ChainFinanceModel->where($map)->count();
        if ($count) {
            return $this->apiReturn(110007, '该联系方式已经提交过,请不要重复提交哦~');
        }
        
        //1个IP，提交10次需求，且号码不能重复 超过10次 上限
        $maps['ip_address'] = array('eq', get_client_ip(0, true));
        $maps['source'] = array('eq', 1);
        $maps['type'] = array('eq', $type);
        $counts = $ChainFinanceModel->where($maps)->count();
        if ($counts > 9) {
            return $this->apiReturn(110008, '您当前的IP地址提交次数已经到达上限（10次），若要继续提交，请联系客服~');
        } elseif ($counts >= self::VERIFY_LIMIT_COUNT) {
            //校验验证码
            $Verify = new \Think\VerifyApi();
            if (!$Verify->check($verify, 5)) {
                return $this->apiReturn(110012, '验证码错误，请重新获取');
            }
        }
        $inData = array(
            'type' => $type,
            'link_name' => $linkName,
            'link_text' => $mobile,
            'link_type' => $linkType,
            'source'    => 1,
            'ip_address'=> get_client_ip(0, true),
            'action_user' => 0,
            'create_time' => $_SERVER['REQUEST_TIME'],
            'status' => 0
            );
        if (false === $ChainFinanceModel->data($inData)->add()) {
            return $this->apiReturn(110009, '系统繁忙，请稍后再试');
        }
        return $this->apiReturn(0, '报名成功');
    }

    /**
     * 检测是否需要验证码
     * @return [type] [description]
     */
    public function needVerify()
    {
        $type = I('request.type', 0, 'intval'); //服务类型
        $maps['ip_address'] = array('eq', get_client_ip(0, true));
        $maps['source'] = array('eq', 1);
        $maps['type'] = array('eq', $type);
        $ChainFinanceModel = D('ChainFinance');
        $counts = $ChainFinanceModel->where($maps)->count();
        if ($counts >= self::VERIFY_LIMIT_COUNT) {
            return $this->apiReturn(110012, '请填写验证码');
        } else {
            return $this->apiReturn(0, '无需验证码');
        }
    }
}
