<?php

namespace Activity\Controller;

class BaseController extends \Common\Controller\BaseController
{

    /**
     * 获取中奖名单
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    protected function getWinner($lottery_id)
    {
        $data['lottery_id'] = $lottery_id;
        $data = array_merge($data, authkey());
        $res = post_curl(MARKET_DOMAIN . '/webapi/getWinners', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取中奖名单
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    protected function getLotteryPic($lottery_id, $shape)
    {
        $data['lottery_id'] = $lottery_id;
        $data['shape'] = $shape;
        $data = array_merge($data, authkey());
        $res = post_curl(MARKET_DOMAIN . '/webapi/getLotteryPics', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取中奖名单
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    protected function drawByRealUsers($lottery_id, $user_id, $pf, $ad_tag,$org_id)
    {
        $data['lottery_id'] = $lottery_id;
        $data['user_id'] = $user_id;
        $data['ad_tag'] = $ad_tag;
        $data['org_id'] = $org_id;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/drawByRealUser', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取用户抽奖资格数
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    protected function getUserDrawCounts($lottery_id, $user_id, $pf)
    {
        $data['lottery_id'] = $lottery_id;
        $data['user_id'] = $user_id;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getUserDrawCount', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取指定用户中奖列表
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    protected function getUserPrize($lottery_id, $user_id, $pf)
    {
        $data['lottery_id'] = $lottery_id;
        $data['user_id'] = $user_id;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getUserPrizes', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 获取指定用户指定活动获得的红包总额
     * @param $lottery_id
     * @param $user_id
     * @param $pf
     * @return mixed
     */
    protected function getUserRedPocket($lottery_id, $user_id, $pf)
    {
        $data['lottery_id'] = $lottery_id;
        $data['user_id'] = $user_id;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getUserRedBonus', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取邮寄地址
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    protected function getSendAddresses($user_id, $pf)
    {
        $data['user_id'] = $user_id;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getSendAddress', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 提交收货地址
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    protected function commitReceivePrizeInfos($tel_prefix, $user_id, $consignee, $mobile, $province, $city, $district, $detail_address, $pf)
    {
        $data['tel_prefix'] = $tel_prefix;
        $data['user_id'] = $user_id;
        $data['consignee'] = $consignee;
        $data['mobile'] = $mobile;
        $data['province'] = $province;
        $data['city'] = $city;
        $data['district'] = $district;
        $data['detail_address'] = $detail_address;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/commitReceivePrizeInfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 增加抽奖次数
     * @param $lottery_id
     * @param $pf
     * @param $user_id
     * @param $increase_draw_count
     * @return mixed
     */
    protected function increaseQualifyToUser($lottery_id, $pf, $user_id, $increase_draw_count, $increase_type, $org_id = 1)
    {
        $data['lottery_id'] = $lottery_id;
        $data['user_id'] = $user_id;
        $data['increase_draw_count'] = $increase_draw_count;
        $data['pf'] = $pf;
        $data['org_id'] = $org_id;
        $data['increase_type'] = $increase_type;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/increaseQualifyToUser', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取当前活动
     * @return [type] [description]
     */
    public function getCurrentActivities($lottery_id = 0, $user_id = 0)
    {
        if (!empty($lottery_id)) {
            $data['lottery_id'] = $lottery_id;
            $data['user_id'] = $user_id;
        } else {
            $data = array();
        }
        $res = post_curl(MARKET_DOMAIN . '/webapi/getCurrentActivities', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 获取用户在指定活动下获得的抽奖次数  （用来根据各种情况 赠送的抽奖次数 判定 是否还能添加抽奖机会）
     * @param $lottery_id
     * @param $user_id
     * @param $pf
     * @return mixed
     */
    protected function getUserLotteryQualify($lottery_id, $user_id, $pf)
    {
        $data['lottery_id'] = $lottery_id;
        $data['user_id'] = $user_id;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getReceivedQualifyDuringActivity', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 邀请注册
     * @param $invite_uid
     * @param $activity_id
     * @param $pf
     * @return mixed
     */
    protected function inviteRegister($invite_uid, $activity_id, $pf)
    {
        $data['invite_uid'] = $invite_uid;
        $data['activity_id'] = $activity_id;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/inviteRegister', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 获取用户通过分享获奖
     * @param $invite_uid
     * @param $activity_id
     * @param $pf
     * @return mixed
     */
    protected function getUserPrizeBySharing($invite_uid, $activity_id, $pf)
    {
        $data['invite_uid'] = $invite_uid;
        $data['activity_id'] = $activity_id;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getUserPrizeBySharing', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 获取用户邀请信息   20181224迭代  与 之前的分享活动接口分离
     * @param $invite_uid
     * @param $activity_id
     * @param $pf
     * @return mixed
     */
    protected function getUserShareInfos($invite_uid, $activity_id, $pf)
    {
        $data['invite_uid'] = $invite_uid;
        $data['activity_id'] = $activity_id;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getUserShareInfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 获取被邀请人下单详情
     * @param $invite_uid
     * @param $activity_id
     * @param $pf
     * @return mixed
     */
    protected function getInviteeOrder($invite_uid, $activity_id, $wpr_id, $pf)
    {
        $data['invite_uid'] = $invite_uid;
        $data['activity_id'] = $activity_id;
        $data['wpr_id'] = $wpr_id;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getInviteeOrders', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 获取邀请人微信信息(如已绑定) 头像/昵称
     * @param $user_id
     * @param $pf
     * @return mixed
     */
    protected function getInviteUserInfos($user_id, $pf)
    {
        $data['user_id'] = $user_id;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getInviteUserInfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 获取邀请人 总返现金额
     * @param $user_id
     * @param $wpr_id
     * @param $pf
     * @return mixed
     */
    protected function getCashBackAmountByIds($user_id, $wpr_id, $pf)
    {
        $data['user_id'] = $user_id;
        $data['wpr_id'] = $wpr_id;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getCashBackAmountById', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 根据wpr_id获取钱包优惠规则
     * @param $wpr_id
     * @param $pf
     * @return mixed
     */
    protected function getWalletPreferentialRuleByIds($wpr_id, $pf)
    {
        $data['wpr_id'] = $wpr_id;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getWalletPreferentialRuleById', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取10条最近的用户返现数据
     */
    protected function getLastTenOrderCashbacks($pf)
    {
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getLastTenOrderCashback', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 用户红包是否 可提现 及金额
     */
    protected function getUserRedPocketAmounts($lottery_id, $user_id, $pf)
    {
        $data['lottery_id'] = $lottery_id;
        $data['user_id'] = $user_id;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getUserRedPocketAmount', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /*****************************-----答题活动接口-----*******************************/
    /** 获取题目及 用户答题id
     * @param $user_id
     * @param $activity_id
     * @param $pf
     * @return mixed
     */
    protected function getQuestionsForQuiz($user_id, $activity_id, $pf)
    {
        $data['user_id'] = $user_id;
        $data['activity_id'] = $activity_id;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getQuestions', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 用户提交 答题答案
     * @param $ques_id
     * @param $answer_score_id
     * @param $answer
     * @param $ques_count
     * @param $pf
     * @return mixed
     */
    protected function commitOption($ques_id, $answer_score_id, $answer, $ques_count, $pf)
    {
        $data['ques_id'] = $ques_id;
        $data['answer_score_id'] = $answer_score_id;
        $data['answer'] = $answer;
        $data['ques_count'] = $ques_count;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/commitAnswer', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 获取用户得分 及奖品
     * @param $user_id
     * @param $activity_id
     * @param $answer_score_id
     * @param $pf
     * @return mixed
     */
    protected function getUserScorePrize($user_id, $activity_id, $answer_score_id, $pf)
    {
        $data['user_id'] = $user_id;
        $data['activity_id'] = $activity_id;
        $data['answer_score_id'] = $answer_score_id;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getUserScoreAndPrize', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /** 领取参与活动资格
     * @param $user_id
     * @param $activity_id
     * @param $pf
     * @return mixed
     */
    protected function takePartInActivityQualify($user_id, $activity_id, $type, $pf)
    {
        $data['user_id'] = $user_id;
        $data['activity_id'] = $activity_id;
        $data['type'] = $type;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/takeActivityQualify', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /*******************************  钱包优惠规则  ********************************/
    /** 获取钱包优惠规则
     * @param $mall_type
     * @param $preferential_type
     * @param $pf
     * @return mixed
     */
    protected function getWalletPreferentialRules($mall_type, $preferential_type, $pf)
    {
        $data['mall_type'] = $mall_type;
        $data['preferential_type'] = $preferential_type;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getWalletPreferentialRule', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }


    /*******************************  金蛋活动  ***********************************/

    /** 获取最近十条中奖用户
     * 使用魔方系统的活动id记录，为了防止与营销系统中的活动重复，从300000开始往上加
     */
    protected function getLastTenEggWinner($activity_id, $type, $pf)
    {
        $data['activity_id'] = $activity_id;
        $data['type'] = $type;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getLastTenEggWinners', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    protected function isJoinActivity($user_id, $activity_id, $type, $pf)
    {
        $data['user_id'] = $user_id;
        $data['activity_id'] = $activity_id;
        $data['type'] = $type;
        $data['pf'] = $pf;
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/isJoinedActivity', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /******************************   用户标签   ******************************/
    /**
     * 根据用户标签展示不同弹窗 from    shuju.ichunt.net
     */
    protected function getUserLabels($params)
    {
        $res = post_curl(SHUJU_DOMAIN . '/WebApi/ApiUserLabel', $params);

        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }
}
