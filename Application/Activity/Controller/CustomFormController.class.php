<?php
/**
 * Created by PhpStorm.
 * User: leo
 * Date: 2018/5/29
 * Time: 14:57
 */

namespace Activity\Controller;

use Activity\Controller\BaseController;
use Activity\Model\CustomFormModel;

class CustomFormController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        $res = $this->checkLogin();
        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
    }

    public function addCustomFormData()
    {
        $data = I('post.');
        $data['user_id'] = cookie('uid');
        $data = array_only($data, [
            'activity_id',
            'mobile',
            'user_type',
            'form_data',
            'company_name'
        ]);
        if (isset($data['user_type'])) {
            if ($data['user_type'] == 1) {
                $data['is_new_reg'] = 1;
            } else {
                $data['is_new_reg'] = -1;
            }
        }
        unset($data['user_type']);
        if (empty($data['activity_id'])) {
            return $this->apiReturn(-1, '活动ID不能为空');
        }
        if (empty($data['mobile'])) {
            return $this->apiReturn(-1, '手机号码不能为空');
        }
//        if (empty($data['company_name'])) {
//            return $this->apiReturn(-1, '公司名称不能为空');
//        }
        if (empty($data['form_data'])) {
            return $this->apiReturn(-1, '表单内容不能为空');
        }
        $result = (new CustomFormModel())->addCustomFormData($data);
        if (!$result) {
            return $this->apiReturn(0, '新增表单内容失败');
        }
        return $this->apiReturn(0, '新增成功');
    }
}