<?php
namespace Activity\Controller;

use Activity\Controller\BaseController;

class ConsignmentController extends BaseController
{
    const VERIFY_LIMIT_COUNT = 3;//验证码出现时机
    /**
     * 报名
     * @return [type] [description]
     */
    public function apply()
    {
        $linkType = I('request.linkType', 0, 'intval'); //联系类型
        $mobile = I('request.mobile', ''); //联系号码
        $linkName = I('request.link_name', ''); //联系人
        $area_num = I('request.area_num', '');
        $verify = I('request.verify', '');
        //输入除数字之外的内容，或者位数错误提示：手机号码格式错误
        if (!is_numeric($mobile)) {
            return $this->apiReturn(100001, '请填写数字~');
        } elseif (empty($linkName)) {
            return $this->apiReturn(100005, '请填写姓名~');
        } elseif (empty($mobile)) {
            return $this->apiReturn(100006, '请填写联系号码~');
        }

        if ($linkType == 2) {
            if(empty($area_num)) {
                return $this->apiReturn(100002, '区号不能为空~');
            }

            $mobile = $area_num . '-' . $mobile;
            if (!is_mobile_phone($mobile)) {
                return $this->apiReturn(100003, '座机号码格式有误~');
            }
        } else if ($linkType == 1) {
            if (!is_mobile($mobile)) {
                return $this->apiReturn(100004, '手机号码格式有误~');
            }
        }
        $ConsignmentModel = D('Consignment');
        $ConsignmentEmailSendModel = D('ConsignmentEmailSend');
        $map['link_type'] = $linkType;
        $map['link_text'] = $mobile;
        $map['source'] = array('eq', 1);
        $count = $ConsignmentModel->where($map)->count();

        $maps['ip_address'] = array('eq', get_client_ip(0, true));
        $maps['source'] = array('eq', 1);
        $counts = $ConsignmentModel->where($maps)->count();
        //1个IP，提交10次需求，且号码不能重复 超过10次 上限
        if ($count) {
            return $this->apiReturn(100007, '该联系方式已经提交过,请不要重复提交哦~');
        } elseif ($counts > 9) {
            return $this->apiReturn(100008, '您当前的IP地址提交次数已经到达上限（10次），若要继续提交，请联系客服~');
        } elseif ($counts >= self::VERIFY_LIMIT_COUNT) {
            //校验验证码
            $Verify = new \Think\VerifyApi();
            if (!$Verify->check($verify, 5)) {
                return $this->apiReturn(100010, '验证码错误，请重新获取');
            }
        }

        $inData = array(
            'link_name' => $linkName,
            'link_text' => $mobile,
            'link_type' => $linkType,
            'source'    => 1,
            'ip_address'=> get_client_ip(0, true),
            'action_user' => 0,
            'create_time' => $_SERVER['REQUEST_TIME'],
            'status' => 0
            );
        if (false === $ConsignmentModel->data($inData)->add()) {
            return $this->apiReturn(100009, '系统繁忙，请稍后重试');
        }

        $ConsignmentEmailSendModel->sendEmail();
    
        return $this->apiReturn(0, '成功');
    }

    /**
     * 检测是否需要验证码
     * @return [type] [description]
     */
    public function needVerify()
    {
        $maps['ip_address'] = array('eq', get_client_ip(0, true));
        $maps['source'] = array('eq', 1);
        $ConsignmentModel = D('Consignment');
        $counts = $ConsignmentModel->where($maps)->count();
        if ($counts >= self::VERIFY_LIMIT_COUNT) {
            return $this->apiReturn(100010, '请填写验证码');
        } else {
            return $this->apiReturn(0, '无需验证码');
        }
    }
}
