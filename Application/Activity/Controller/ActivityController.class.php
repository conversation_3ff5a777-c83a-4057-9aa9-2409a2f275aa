<?php

namespace Activity\Controller;

use Activity\Controller\BaseController;
use Activity\Model\SampleModel;

class ActivityController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        if (!in_array(strtolower(ACTION_NAME), array(
            'waterarmylist',
            'geturlinfo',
            'getlxuserrank',
            'setamount',
            'invited',
            'rotation',
            'yaohaoyouJP',
            'getyaohaoyougoodid',
            'getactivityinfo',
        ))) {
            //检查登录
            $res = $this->checkLogin();
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }

        setHeader();
    }
    //抽奖活动 1129===========================================================================================================================
    //获取单个用户的奖品
    public function getUserPrize()
    {
        $lottery_id = I('lottery_id');
        $user_id = cookie('uid');
        if (!$lottery_id) {
            return $this->apiReturn(90003, '缺少参数~');
        }

        $LotteryRecordModel = D('LotteryRecord');
        $where = array(
            'lottery_id' => $lottery_id,
            'user_id' => $user_id,
        );
        $userPrizeInfo = $LotteryRecordModel->where($where)->find();
        $data = array(
            'lg_id' => $userPrizeInfo['lg_id'],
            'goods_name' => $userPrizeInfo['goods_name'],

        );
        return $this->apiReturn(0, '成功', $data);
    }

    //点击抽奖
    public function ajaxLotteryChance()
    {
        //活动id
        $lottery_id = I('lottery_id');
        if (!$lottery_id) {
            return $this->apiReturn(90003, '缺少参数~');
        }

        if (!isMobile()) {
            return $this->apiReturn(90004, '本次活动仅限移动端~');
        }

        $lotteryModel = D('lottery');
        $lotteryInfo = $lotteryModel->find($lottery_id);
        if ($lotteryInfo['status'] == '0') { //该活动是否已被禁用
            return $this->apiReturn(90005, '该活动已经禁用~');
        }
        if (intval($lotteryInfo['end_time']) < time()) { //活动是否已过期
            return $this->apiReturn(90006, '该活动已经过期~');
        }
        if (intval($lotteryInfo['start_time']) > time()) { //活动是否已开始
            return $this->apiReturn(90007, '该活动还没开始~');
        }

        $UserActivityLogModel = D('UserActivityLog');//活动规则限制：是否有抽奖资格
        $user_id = cookie('uid');

        $userWhere = array(
            'user_id' => $user_id,
            'lottery_id' => $lottery_id,
            'action_source' => 0, //操作来源是前台用户
        );
        $count = $UserActivityLogModel->where($userWhere)->count();//只要有记录,表明已参加
        //后期可能补充黑名单机制。。。
        $LotteryRecordModel = D('LotteryRecord');
        $record_map = array(
            'user_id' => $user_id,
            'lottery_id' => $lottery_id,
        );
        $record_count = $LotteryRecordModel->where($record_map)->count();
        if (isset($count) && intval($count) >= C('LOTTERY_ALLOW_NUMBER')) { //超出允许次数
            if (intval($record_count) !== 0) {
                $resp = array(
                    'errcode' => 90008,
                    'errmsg' => '该活动您已经参加过了(已达允许抽奖次数)~',
                );
                userActivityLog($resp, $lottery_id);
                return $this->apiReturn(90008, '该活动您已经参加过了~');
            }
        }

        //白名单用户：必中奖（100%中奖概率）
        $lotteryAllowModel = D('LotteryAllow');
        $allowWhere = array(
            'lottery_id' => $lottery_id,
            'user_id' => $user_id,
            'type' => 0, //白名单
            'status' => 1, //有效
        );
        $lotteryAllowInfo = $lotteryAllowModel->where($allowWhere)->find(); //每个活动每个会员只有一次记录
        $lotteryGoodsModel = D('LotteryGoods');
        $lotteryGoodsInfo = $lotteryGoodsModel->where(array('lottery_id' => $lottery_id))->select();//奖品信息
        foreach ($lotteryGoodsInfo as $key => $value) { //获取奖品映射关系
            $lgRelation[$key] = $value['lg_id'];
        }
        $lotteryGoodsRealInfo = array();
        foreach ($lotteryGoodsInfo as $key => $value) {
            $lotteryGoodsRealInfo[$value['lg_id']] = $value;
        }
        if (is_array($lotteryAllowInfo) && count($lotteryAllowInfo)) { //必须中奖
            //奖品编号
            //获取奖品id
            $prizeId = $lotteryAllowInfo['lg_id'];
            $resInfo['prize_key_id'] = array_search($prizeId, $lgRelation);
            $resInfo['prize_key'] = intval($resInfo['prize_key_id']);
            $resInfo['prize_name'] = $lotteryGoodsRealInfo[$prizeId]['goods_name'];
            $member_type = 1; //是否是VIP，白名单
            if (!$resInfo['prize_key_id']) {
                $resInfo = $lotteryGoodsModel->getPrizeBatchByLotteryGoodsInfo($lotteryGoodsInfo);
            }
        } else { //概率中奖
            $member_type = 0;
            $resInfo = $lotteryGoodsModel->getPrizeBatchByLotteryGoodsInfo($lotteryGoodsInfo);
        }
        //查询该奖品是否还有库存
        $stock = $lotteryGoodsInfo[$resInfo['prize_key']]['goods_real_stock'];
        if (!$stock) { //没有库存
            $resp = array(
                'errcode' => 90009,
                'errmsg' => "该奖品" . $lotteryGoodsInfo[$resInfo['prize_key']]['goods_name'] . "已无库存~",
            );
            userActivityLog($resp, $lottery_id);
            return $this->apiReturn(90009, "该奖品" . $lotteryGoodsInfo[$resInfo['prize_key']]['goods_name'] . "已无库存~");
        } else {
            $dayStock = $lotteryGoodsInfo[$resInfo['prize_key']]['goods_day_num'];//根据奖品id查询是否已超每天（送出）抽中数量
            $ltyRecordModel = D('LotteryRecord');
            $ltyRecordMap = array(
                'lottery_id' => $lottery_id,
                'lg_id' => $lgRelation[$resInfo['prize_key']],
                'time' => array('gt', strtotime(date('Y-m-d', time()))),
            );
            $todayCount = $ltyRecordModel->where($ltyRecordMap)->count();//先统计此奖品今天已经中奖的总数

            if (intval($dayStock) <= intval($todayCount)) {
                $resp = array(
                    'errcode' => 90010,
                    'errmsg' => "该中奖奖品" . $lotteryGoodsInfo[$resInfo['prize_key']]['goods_name'] . "数量大于今日送出的抽中数量~",
                );
                userActivityLog($resp, $lottery_id);
                return $this->apiReturn(90010,
                    "该中奖奖品" . $lotteryGoodsInfo[$resInfo['prize_key']]['goods_name'] . "数量大于今日送出的抽中数量~");
            } else {
                $user_info = S_user($user_id);
                if ($user_info['mobile']) {
                    $user_name = $user_info['mobile'];
                } else {
                    $user_name = $user_info['email'];
                }
                //1.插入中奖会员表
                $data = array(
                    'lottery_id' => $lottery_id,
                    'user_id' => $user_id,
                    'user_name' => $user_name,
                    'lg_id' => $lgRelation[$resInfo['prize_key']],
                    'goods_name' => $resInfo['prize_name'],
                    'time' => time(),
                    'is_allow' => $member_type,
                    'create_time' => time(),
                    'mod_time' => time(),
                    'user_type' => 1, //目前只有移动端
                );
                $re = $ltyRecordModel->data($data)->add();
                if ($re) {
                    $where = array(
                        'lg_id' => $lgRelation[$resInfo['prize_key']],
                        'lottery_id' => $lottery_id,
                        'goods_name' => $resInfo['prize_name'],
                    );
                    $res = $lotteryGoodsModel->where($where)->setDec('goods_real_stock');  //奖品表相应奖品库存减少
                    if ($res) {
                        $left_count = intval(C('LOTTERY_ALLOW_NUMBER')) - intval($count);//剩余抽奖次数
                        if ($left_count < 0) {
                            $left_count = 0;
                        }
                        //记录日志
                        $resp = array(
                            'lg_id' => $lgRelation[$resInfo['prize_key']],
                            //奖品表id
                            'prize_site' => intval($resInfo['prize_key']) - 1,
                            //前端页面位置 从0开始，由于前端页面没有无中奖选项，而数组中无中奖的key为0，
                            'prize_name' => $resInfo['prize_name'],
                            //奖品名称
                            'prize_remark' => $lotteryGoodsInfo[$resInfo['prize_key']]['remark'],
                            //中奖弹框文字
                            'count' => $left_count,
                            //剩余次数
                            'errcode' => 0,
                            'errmsg' => 'success~',
                        );
                        userActivityLog($resp, $lottery_id, $user_id);
                        return $this->apiReturn(0, "成功", $resp);
                    } else {
                        $resp = array(
                            'errcode' => 90011,
                            'errmsg' => '奖品库存减少失败~',
                        );
                        userActivityLog($resp, $lottery_id);
                        return $this->apiReturn(90011, "奖品库存减少失败");
                    }
                } else {
                    $resp = array(
                        'errcode' => 90012,
                        'errmsg' => '插入数据库失败~',
                    );
                    userActivityLog($resp, $lottery_id);
                    return $this->apiReturn(90012, "插入数据库失败~");
                }

            }
        }
    }
    //抽奖活动 end===========================================================================================================================
    //1.判断是否已经登录（用于显示弹窗还是页面）
    /**
     * 水军名单展示接口
     */
    public function waterArmyList()
    {
        $WaterModel = D('Water');
        $res = $WaterModel->field('mobile,prize')->order('id DESC')->limit(10)->select();
        foreach ($res as $key => &$value) {
            $value['mobile'] = hideStar($value['mobile']);
        }
        return $this->apiReturn(0, '成功', $res);
    }

    /**
     * 邀约列表展示接口（我的邀请记录）
     * 成功邀约人数统计接口
     */
    public function myInviteList()
    {
        $UserMainModel = D('UserMain');
        $user_id = cookie('uid');
        $userListInfo = $UserMainModel->getInviteInfoByUserId($user_id);
        return $this->apiReturn(0, '成功', $userListInfo);
    }


    /**
     * 分享链接生成接口
     */
    public function getUrlInfo()
    {
        $user_id = cookie('uid');
        if (!$user_id) {
            return $this->apiReturn(90001, '未登录，缺少user_id参数', '');
        }
        if (isMobile()) {
            $pf = 2;
        } else {
            $pf = 1;
        }
        $data['user_id'] = $user_id;
        $data['pf'] = $pf;
        return $this->apiReturn(0, '成功', $data);
    }

    /**
     * [dealInvite 被邀约结果处理接口（是否被邀约成功）
     * @return [type] [description]
     */
    public function dealInvite()
    {
        $invite_uid = I('invite_uid');
        if (!$invite_uid) {
            return $this->apiReturn(90001, '邀请人id不可为空', '');
        }
        $user_id = cookie('uid');
        $user_info = S_user($user_id);
        if (!$user_info) {
            return $this->apiReturn(11009, '网络错误，请您稍后重试！', '');
        }
        if ($user_info['invite_uid']) {
            if ($user_info['invite_uid'] == $invite_uid) {
                return $this->apiReturn(0, '成功', '');
            } else {
                return $this->apiReturn(90002, '该用户已注册，邀请结果失败', '');
            }
        } else {
            return $this->apiReturn(90002, '该用户已注册，邀请结果失败', '');
        }
    }

    /**
     * 该会员优惠券展示（优惠券）
     * @return 优惠券类型，获取时间
     */
    public function showCouponList()
    {
        $user_id = cookie('uid');
        $pf = I('request.pf', -1, 'intval');
        //缺失平台
        if (!C('PLAT_FORM.' . $pf)) {
            return $this->apiReturn(11009, '网络错误，请您稍后重试');
        }
        $list = D('UserMain')->getCouponList($user_id);
        return $this->apiReturn(0, '成功', $list);
    }

    // 获取猎芯用户排行榜
    public function getLxUserRank()
    {
        $data = Sredis('api_lx_activity_user_rank', '', C('REDIS_LIST.search'));

        if (!$data) {
            $UserAmountModel = D('UserAmount');
            $data = $UserAmountModel->getUserRank();
            Sredis('api_lx_activity_user_rank', json_encode($data), C('REDIS_LIST.search'));
        }

        if ($data) {
            foreach ($data as &$v) {
                $v['account'] = $this->hideAccount($v['account']);
                $v['amount'] = number_format($v['amount'], 2);
            }
        }

        return $this->apiReturn(0, '', $data);
    }

    // 隐藏账号
    public function hideAccount($str)
    {
        if (preg_match('/@/', $str)) {
            $sub_1 = substr($str, 0, strpos($str, '@')); // @前字符串
            $sub_rep = substr_replace($sub_1, '****', 2); // 保留2位，其他隐藏

            $sub_2 = substr($str, strpos($str, '@')); // @后字符串
            $exp = explode('.', $sub_2);
            $suffix = substr_replace($exp[0], '****', 0);

            return $sub_rep . '@' . $suffix . '.' . $exp[1];
        }

        return substr_replace($str, '****', 3, 4);
    }

    // 设置猎芯用户排行榜假数据
    public function setAmount()
    {
        $data = I('request.');

        if (!$data['account']) {
            return $this->apiReturn(11010, '参数错误');
        }

        $UserAmountModel = D('UserAmount');

        if ($data['del']) { // del=1，则删除假数据
            $map['user_id'] = 0;
            $map['account'] = $data['account'];

            $UserAmountModel->where($map)->delete();
        } else {
            $arr = array();
            $arr['user_id'] = 0;
            $arr['account'] = $data['account'];
            $arr['amount'] = isset($data['amount']) ? $data['amount'] : 0;
            $arr['create_time'] = time();
            $arr['update_time'] = time();

            $UserAmountModel->addData($arr);
        }

        Sredis('api_lx_activity_user_rank', null, C('REDIS_LIST.search'));

        return $this->apiReturn(0, '', $data);
    }


    /*
        存储用户9月活动资格信息
    */
    public function set_activity_user_info()
    {

        $data = I('request.');

        $user_id = cookie('uid') + 0;

        if ($user_id <= 0) {
            return $this->apiReturn(90001, '未登录，缺少user_id参数');
        }

        //判断是否领取
        $exists_apply = S('activity_user_info_september..' . $user_id);

        if ($exists_apply != $user_id) {

            $result = S('activity_user_info_september..' . $user_id, $user_id);

            if (!$result) {
                return $this->apiReturn(0, 'error', '领取资格失败,请重试');
            }
        }

        return $this->apiReturn(0, 'success', '领取资格成功');


    }


    public function get_activity_user_info()
    {

        $user_id = cookie('uid') + 0;

        $redis_user_id = S('activity_user_info_september..' . $user_id);

        $activity_data = D('mktActivity');

        $id = $_REQUEST['id'] + 0;

        $time = $activity_data->where(array('lie_mkt_activity.activity_id' => $id, 'status' => 1))->find();

        /*
           获取这个用户的指定时间内的消费总额
        */
        //活动开始与结束时间
        $time_start = $time['activity_start'];
        $time_end = $time['activity_end'];

        //支付开始与结束时间
        $pay_start_time = $time['pay_start_time'];
        $pay_end_time = $time['pay_end_time'];

        $activity_model = D('Order/PayLog');
        $pay_money = $activity_model->activity_pay_log($user_id, $time_start, $time_end, $pay_start_time,
            $pay_end_time);


        if (empty($pay_money)) {
            $pay_money = 0;
        }


        //获取这个活动的奖品信息及活动开始结束时间
        $id = $_REQUEST['id'] + 0;

        $data = $activity_data->join('LEFT JOIN lie_mkt_prize on lie_mkt_prize.activity_id= lie_mkt_activity.activity_id')->where(
            array(
                'lie_mkt_activity.activity_id' => $id,
                'lie_mkt_prize.pay_min' => array('elt', $pay_money),
                'lie_mkt_prize.pay_max' => array('egt', $pay_money)
            )
        )->limit(1)->select();

        // echo $activity_data->getLastSql();exit;

        $data['pay_money'] = $pay_money;


        if ($redis_user_id != $user_id) {
            return $this->apiReturn(0, 'error', $data);
        }

        return $this->apiReturn(0, 'success', $data);

    }


    /*
        邀好友活动
    */
    public function invited()
    {

        // 数据统计需求：
        // 1、映射关系不必在后台可视，发奖时excel导给运营即可
        // 2、浏览量：页面浏览量与浏览人数 (查seo)
        // 分享次数：点击立即分享按钮分享并分享成功的次数 (统计每个用户的分享数量后计算)
        // 分享人数：同上 redis:outnumber
        // 分享后链接点击次数与人数
        // 注册用户数 通过会员 搜关键词查找
        // 下单数


        $this->add_view();

        //当前用户id
        $user_id = $_REQUEST['user_id'] + 0;


        //邀请人id
        $invited_uid = !empty($_REQUEST['invited_uid']) ? $_REQUEST['invited_uid'] : 0;

        //增加分享次数的请求
        if ($_REQUEST['share_num']) {
            $invited = S('invited..' . $invited_uid);

            if ($invited == false) {
                S('invited..' . $invited_uid, json_encode(array('invited_num' => 1)));

                //记录分享人数+1
                $this->share_number();

            } else {
                // var_dump($invited_num);
                $invited['invited_num'] = $invited['invited_num'] + 1;

                S('invited..' . $invited_uid, json_encode($invited));
                $result = $invited['invited_num'];
            }

            return $this->apiReturn(0, 'success', $result);
        }

        /*链接点击次数
        如果有invited_uid(邀请人id)
        */
        if (!empty($invited_uid)) {
            $this->add_share_click();
        }


        return $this->apiReturn(0, 'success', $result);


    }

    /*
        增加页面浏览量
    */
    private function add_view()
    {

        $invited = S('invited..' . 'view');


        if ($invited == false) {
            S('invited..' . 'view', 1);
        } else {

            S('invited..' . 'view', ($invited + 1));
        }


    }

    /*
    增加分享人数
    */
    private function share_number()
    {

        $invited = S('invited..' . 'outnumber ');


        if ($invited == false) {
            S('invited..' . 'outnumber ', 1);
        } else {

            S('invited..' . 'outnumber ', ($invited + 1));
        }

    }

    /*
        增加分享的页面点击的人数
    */
    private function add_share_click()
    {

        $invited_uid = !empty($_REQUEST['invited_uid']) ? $_REQUEST['invited_uid'] : 0;

        $invited = S('invited..' . $invited_uid);

        if ($invited == false) {
            S('invited..' . $invited_uid, json_encode(array('click' => 1)));

            //记录分享人数+1
            $this->share_number();

        } else {
            // var_dump($invited_num);
            $invited['click'] = $invited['click'] + 1;

            S('invited..' . $invited_uid, json_encode($invited));

        }
    }


    /*
        轮播消息
    */
    public function rotation()
    {
        /*
            此处轮播三种情况：下单成功，成功邀请 好友下单，获得返利
            格式：136***00下单成功！    xxx成功邀请2个好友下单！ xxx获得返利130元！
            在真数据的基础上，轮播假数据
        */

        //真数据部分
        $msg_model = D('userInvitefriendMsg');

        //统计下单用户
        $order = $msg_model->where(array('msg_type' => 1))->select();

        //统计邀请好友
        $friends = $msg_model->field('invite_user_id,count(invite_user_id) as sum,username')->where(array('msg_type' => 2))->group('invite_user_id')->select();

        $money = $msg_model->field('invite_user_id,sum(goods_num) as goods_num,username')->where(array('msg_type' => 3))->group('invite_user_id')->select();


        foreach ($money as $key => $value) {


            if ($value['goods_num'] == 1) {
                $money[$key]['money'] = 30;
            } else {
                if ($value['goods_num'] >= 2 && $value['goods_num'] <= 4) {
                    $money[$key]['money'] = 100;
                } else {
                    if ($value['goods_num'] >= 5 && $value['goods_num'] <= 9) {
                        $money[$key]['money'] = 300;
                    } else {
                        if ($value['goods_num'] >= 10) {
                            $money[$key]['money'] = 800;
                        }
                    }
                }
            }

        }


        //合并消息
        foreach ($order as $key => $value) {
            $msg_info[] = desensitize($value['username'], 3, 4) . '下单成功';
        }

        foreach ($friends as $key => $value) {
            $msg_info[] = desensitize($value['username'], 3, 4) . '成功邀请' . $value['sum'] . '个好友下单';
        }

        foreach ($money as $key => $value) {
            if ($value['goods_num'] <= 0) {
                continue;
            }
            $msg_info[] = desensitize($value['username'], 3, 4) . '获得返利' . $value['money'] . '元！';
        }


        //假数据部分
        for ($i = 0; $i < 30; $i++) {
            $rand_result = rand(1, 3);

            //获取一个随机的手机号码
            $phone = rand_phone(1);

            $money_rand = rand(1, 10);
            if ($money_rand < 5) {
                $money = 30;
            } else {
                if ($money_rand < 7) {
                    $money = 100;
                } else {
                    if ($money_rand < 9) {
                        $money = 300;
                    } else {
                        $money = 800;
                    }
                }
            }


            switch ($rand_result) {
                case 1:
                    $msg_info[] = desensitize($phone[0], 3, 4) . '下单成功';
                    break;
                case 2:
                    $msg_info[] = desensitize($phone[0], 3, 4) . '成功邀请' . rand(1, 5) . '个好友下单';
                    break;
                case 3:
                    $msg_info[] = desensitize($phone[0], 3, 4) . '获得返利' . $money . '元！';
                    break;

                default:
                    $msg_info[] = desensitize($phone[0], 3, 4) . '下单成功';
                    break;
            }
        }


        shuffle($msg_info);


        return $this->apiReturn(0, 'success', $msg_info);

    }

    /*
     * 获取用户奖品信息
     */
    public function yaoHaoYouJP()
    {
        $user_id = cookie("uid");
        $msg_model = D('userInvitefriendMsg');
        $goodsNums = $msg_model->where([
            "invite_user_id" => $user_id,
            "msg_type" => 3,
            "status" => 1
        ])->sum("goods_num");
        $peopleNums = $msg_model->where(["invite_user_id" => $user_id, "msg_type" => 3, "status" => 1])->count("id");
        $data['goodsNums'] = $goodsNums ? $goodsNums : 0;
        $data['peopleNums'] = $peopleNums ? $peopleNums : 0;
        $data['prize'] = 0;
        $jp = C("YAO_HAOYOU_JP");
        $jpKey = array_keys($jp);
        if ($goodsNums > 0) {
            foreach ($jpKey as $k => $ranking) {
                if ($data['goodsNums'] == $ranking) {
                    $data['prize'] = $jp[$ranking];
                    break;
                } elseif ($data['goodsNums'] < $ranking) {
                    $data['prize'] = isset($jpKey[$k - 1]) ? $jp[$jpKey[$k - 1]] : $jp[0];
                    break;
                }
            }
            return $this->apiReturn(0, 'success', $data);
        } else {
            return $this->apiReturn(500, '您还没有相关奖品数据');
        }

    }

    /*
 * 获取用户奖品信息
 */
    public function getYaoHaoYouGoodId()
    {
        $type = I('request.type_id', 1, 'intval');
        $goodsId = C("YAOHAOYOUAVLIST");
        if (isset($goodsId[$type])) {
            return $this->apiReturn(0, 'success', ["id" => $goodsId[$type]]);
        } else {
            return $this->apiReturn(500, '没有相关的活动');
        }

    }

    //判断是否可以申请国产专题商品
    public function checkCanApplyDomesticGoods()
    {
        $userId = cookie('uid');
        $goodsId = I('goods_id');
        $sampleModel = new SampleModel();
        $canApply = $sampleModel->checkCanApplyDomesticGoods($userId, $goodsId);
        if (!$canApply) {
            return $this->apiReturn(1, "您已申请该样片,去看看其他物料吧");
        } else {
            return $this->apiReturn(0, '可以申请');
        }
    }

    public function getactivityinfo(){
        $code = I('get.sign');
        $Url = CUBE_NEW_DOMAIN . '/sync/activity/getInfoByCode';
        $body = post_curl($Url, ["code"=>$code]);
        $result = json_decode($body, true);
        if ($result['code']!=0){
            throw new \Exception($result['msg']);
        }
        return $this->apiReturn(0, 'success', $result['data']);
    }
}
