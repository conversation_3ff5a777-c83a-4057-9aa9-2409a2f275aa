<?php
/**
 * Created by PhpStorm.
 * User: leo
 * Date: 2018/5/29
 * Time: 14:57
 */

namespace Activity\Controller;

use Activity\Controller\BaseController;

class LotteryController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();

        if (!in_array(strtolower(ACTION_NAME), [
            'increasequalifybyfollow', 'getwinners', 'getlotterypics', 'getlotteryactivity', 'saveoriginurl', 'getoriginurl', 'getinviteuserinfo', 'getinviteurl', 'getwalletpreferentialrule', 'getlasttenordercashback', 'getactivityjoincount', 'getfakewinner', 'getlotterylist', 'drawbyrealuser', 'getuserprizes',
            'increaseuserqualify', 'commitreceiveprizeinfo','getuserdrawcount'
        ])) {
            //检查登录
            if (in_array(strtolower(ACTION_NAME), array('getuserdrawcount', 'getusershareinfo', 'getinviteeorders'))) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    private function Export($errcode = 0, $errmsg = '成功', $data = '')
    {
        echo json_encode(['errcode' => $errcode, 'errmsg' => $errmsg, 'data' => $data]);
        exit();
    }

    private function Export1($errcode = '', $errmsg = '', $data = '')
    {
        $callback = I('callback', '');
//        if(is_array($errcode)){
        if (!empty($callback)) {
            echo $callback . '(' . json_encode(['errcode' => $errcode, 'errmsg' => $errmsg, 'err_code' => $errcode, 'err_msg' => $errmsg, 'data' => $data]) . ')';
        } else {
            echo json_encode(['errcode' => $errcode, 'errmsg' => $errmsg, 'err_code' => $errcode, 'err_msg' =>
                $errmsg, 'data' => $data]);
        }
        exit();
//        }
//        else{
//            if(!empty($callback)){
//                echo $callback.'('.json_encode($this->apiReturn($errcode[0],$errcode[1],empty($errcode[2])?'':$errcode[2])).')';
//            }else{
//                echo json_encode($this->apiReturn($errcode[0],$errcode[1],empty($errcode[2])?'':$errcode[2]));
//            }
//        }
    }


    //获取获奖名单
    public function getWinners()
    {
        $lottery_id = I('lottery_id');
        $res = $this->getWinner($lottery_id);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    //新增抽奖次数
    public function increaseUserQualify()
    {
        $lottery_id = I('lottery_id');
        $user_id = I('user_id');
        $pf = I('pf', 1);
        $org_id = I('org_id', 1);
        $increase_draw_count = I('increase_draw_count', 1);
        $increase_type = I('increase_type', 2);
        if (empty($increase_type)) {
            $this->Export1(90001, '参数错误');
        }

        $res = $this->increaseQualifyToUser($lottery_id, $pf, $user_id, $increase_draw_count, $increase_type, $org_id);
        if (empty($res) || $res['errcode'] != 0) {
            $this->Export1($res['errcode'], $res['errmsg'], '');
        }

        $this->Export1(0, '赠送抽奖资格完成', '');
    }

    //获取抽奖资源
    public function getLotteryPics()
    {
        $lottery_id = I('lottery_id');
        $shape = I('shape', 1);
        $res = $this->getLotteryPic($lottery_id, $shape);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    //前端抽奖
    public function drawByRealUser()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            exit;
        }
        $lottery_id = I('lottery_id');
        $user_id = I('user_id');
        $pf = I('pf', 1);
        $ad_tag = I('ad_tag', '');
        $org_id = I('org_id', 1);
        $res = $this->drawByRealUsers($lottery_id, $user_id, $pf, $ad_tag, $org_id);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    //获取用户抽奖次数
    public function getUserDrawCount()
    {
        $lottery_id = I('lottery_id');
        $user_id = I('user_id');
        $pf = I('pf', 1);
        $res = $this->getUserDrawCounts($lottery_id, $user_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    //获取指定用户中奖列表
    public function getUserPrizes()
    {
        $lottery_id = I('lottery_id');
        $user_id = I('user_id');
        $pf = I('pf', 1);
        $res = $this->getUserPrize($lottery_id, $user_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    //获取抽奖列表
    public function getLotteryList()
    {
        //赠送抽奖资格规则
        $qualifyGetRule = I('qualify_get_rule');
        //组织id
        $orgId = I('org_id', 1);
        $userId = I('user_id');
        $pf = I('pf', 1);
        $data = [
            'qualify_get_rule' => $qualifyGetRule,
            'org_id' => $orgId,
            'user_id' => $userId,
            'pf' => $pf,
        ];
        $data = array_merge($data, authkey($pf));
        $res = post_curl(MARKET_DOMAIN . '/webapi/getLotteryList', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
            $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
        }
        return $res;
    }

    //获取指定用户指定活动获得的红包总额
    public function getUserRedBonus()
    {
        $lottery_id = I('lottery_id');
        $user_id = I('user_id');
        $pf = I('pf', 1);
        $res = $this->getUserRedPocket($lottery_id, $user_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    //获取指定用户地址信息
    public function getSendAddress()
    {
        $user_id = I('user_id');
        $pf = I('pf', 1);
        $res = $this->getSendAddresses($user_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    //提交指定用户地址信息
    public function commitReceivePrizeInfo()
    {
        $tel_prefix = I('tel_prefix');
        $user_id = I('user_id');
        $consignee = I('consignee');
        $mobile = I('mobile');
        $province = I('province');
        $city = I('city');
        $district = I('district');
        $detail_address = I('detail_address');
        $pf = I('pf', 1);
        $res = $this->commitReceivePrizeInfos($tel_prefix, $user_id, $consignee, $mobile, $province, $city, $district, $detail_address, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 获取抽奖活动  如果没传lottery_id 则获取所有当前活动
     */
    public function getLotteryActivity()
    {
        $lottery_id = I('lottery_id');
        $user_id = I('user_id', 0);
        $res = $this->getCurrentActivities($lottery_id, $user_id);
        if ($res) {
            $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
        } else {
            $this->Export1(90000, '获取抽奖活动失败', '');
        }
    }

    /**
     * 分享添加抽奖资格
     */
    public function increaseQualifyByShare()
    {
        $lottery_id = I('lottery_id');
        $user_id = I('user_id');
        $pf = I('pf', 1);

//        $increase_draw_count = I('increase_draw_count',1);

        $increase_type = I('increase_type');
        if (empty($increase_type) || $increase_type != 3) {
            $this->Export1(90001, '参数错误');
        }
        $result = $this->getCurrentActivities($lottery_id);
        if (empty($result) || $result['errcode'] != 0) {
            $this->Export1(90002, '获取抽奖活动失败', '');
        } else {
            $rules_arr = explode(',', $result['data']['qualify_get_rule']);
            if (in_array(3, $rules_arr)) {
                //是否 通过分享还能 送抽奖资格
                $hasReceived = $this->getUserLotteryQualify($lottery_id, $user_id, $pf);
                if ($hasReceived['data']['from_share_count'] == 0) {
                    $res = $this->increaseQualifyToUser($lottery_id, $pf, $user_id, $result['data']['qualify_share'], $increase_type);
                    if (empty($res) || $res['errcode'] != 0) {
                        $this->Export1(90003, '分享赠送抽奖资格失败', '');
                    }
                }
            }
        }

//        $result = $this->getCurrentActivities();
//        if(0==$result['errcode']) {
//            foreach ($result['data'] as $value) {
//                $rules_arr = explode(',', $value['qualify_get_rule']);
//                if (in_array(3, $rules_arr)) {
//                    //是否 通过分享还能 送抽奖资格
//                    $hasReceived = $this->getUserLotteryQualify($lottery_id,$user_id,$pf);
//                    if($hasReceived['data']['from_share_count']==0){
//                        $res = $this->increaseQualifyToUser($lottery_id,$pf,$user_id,$increase_draw_count,$increase_type);
//                        if(empty($res) || $res['errcode']!=0){
//                            $this->Export1(90002, '分享赠送抽奖资格失败','');
//                        }
//                    }
//                }
//            }
//        }
        $this->Export1(0, '分享赠送抽奖资格完成', '');
    }

    /**
     * 关注添加抽奖资格
     */
    public function increaseQualifyByFollow()
    {
        $lottery_id = I('lottery_id');
        $user_id = I('user_id');
        $pf = I('pf', 1);
//        $increase_draw_count = I('increase_draw_count',1);
        $increase_type = 5;
        if (empty($increase_type) || $increase_type != 5) {
            $this->Export1(90001, '参数错误');
        }
        $result = $this->getCurrentActivities($lottery_id);
        if (empty($result) || $result['errcode'] != 0) {
            $this->Export1(90002, '获取抽奖活动失败', '');
        } else {
            $rules_arr = explode(',', $result['data']['qualify_get_rule']);
            if (in_array(5, $rules_arr)) {
                $res = $this->increaseQualifyToUser($lottery_id, $pf, $user_id, $result['data']['qualify_follow'], $increase_type);
                if (empty($res) || $res['errcode'] != 0) {
                    $this->Export1(90003, '关注赠送抽奖资格失败', '');
                }
            }
        }

        $this->Export1(0, '关注赠送抽奖资格完成', '');
    }

    /**
     * 邀请注册送奖品
     */
    public function inviteRegisterSendPrize()
    {
        $invite_uid = I('invite_uid');
        $activity_id = I('activity_id');
        $pf = I('pf', 1);
        $res = $this->inviteRegister($invite_uid, $activity_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 获取用户分享获奖列表
     */
    public function getUserPrizeListBySharing()
    {
        $invite_uid = I('invite_uid');
        $activity_id = I('activity_id');
        $pf = I('pf', 1);
        $res = $this->getUserPrizeBySharing($invite_uid, $activity_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 获取用户邀请信息   20181224迭代  与 之前的分享活动接口分离
     */
    public function getUserShareInfo()
    {
        $invite_uid = I('invite_uid');
        $activity_id = I('activity_id');
        $pf = I('pf', 1);
        $res = $this->getUserShareInfos($invite_uid, $activity_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 获取被邀请人下单详情
     */
    public function getInviteeOrders()
    {
        $invite_uid = I('invite_uid');
        $activity_id = I('activity_id');
        $wpr_id = I('wpr_id');
        $pf = I('pf', 1);
        $res = $this->getInviteeOrder($invite_uid, $activity_id, $wpr_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 获取邀请人微信信息(如已绑定) 头像/昵称
     */
    public function getInviteUserInfo()
    {
        $user_id = I('user_id');
        $pf = I('pf', 1);
        $res = $this->getInviteUserInfos($user_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 获取邀请人 总返现金额
     */
    public function getCashBackAmountById()
    {
        $user_id = I('user_id');
        $wpr_id = I('wpr_id');
        $pf = I('pf', 1);
        $res = $this->getCashBackAmountByIds($user_id, $wpr_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     *  获取钱包优惠规则 通过wpr_id
     */
    public function getWalletPreferentialRuleById()
    {
        $wpr_id = I('wpr_id');
        $pf = I('pf', 1);
        $res = $this->getWalletPreferentialRuleByIds($wpr_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 获取10条最近的用户返现数据
     */
    public function getLastTenOrderCashback()
    {
        $pf = I('pf', 1);
        $res = $this->getLastTenOrderCashbacks($pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 用户红包是否 可提现 及金额
     */
    public function getUserRedPocketAmount()
    {
        $lottery_id = I('lottery_id');
        $user_id = I('user_id');
        $pf = I('pf', 1);
        $res = $this->getUserRedPocketAmounts($lottery_id, $user_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /*****************************-----答题活动接口-----*******************************/
    /**
     * 获取题目 和用户答题Id
     */
    public function getQuestions()
    {
        $user_id = I('user_id');
        $activity_id = I('activity_id');
        $pf = I('pf', 1);
        $res = $this->getQuestionsForQuiz($user_id, $activity_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 用户提交 答题答案
     */
    public function commitAnswer()
    {
        $ques_id = I('ques_id');
        $answer_score_id = I('answer_score_id');
        $answer = I('answer');
        $ques_count = I('ques_count');
        $pf = I('pf', 1);
        $res = $this->commitOption($ques_id, $answer_score_id, $answer, $ques_count, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 获取答题得分及奖品
     */
    public function getUserScoreAndPrize()
    {
        $activity_id = I('activity_id');
        $answer_score_id = I('answer_score_id');
        $user_id = I('user_id');
        $pf = I('pf', 1);
        $res = $this->getUserScorePrize($user_id, $activity_id, $answer_score_id, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 领取参与活动资格
     */
    public function takeActivityQualify()
    {
        $type = I('type', 0);
        $activity_id = I('activity_id');
        $user_id = cookie('uid');
        $pf = I('pf', 1);
        $res = $this->takePartInActivityQualify($user_id, $activity_id, $type, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 是否参与了某个活动
     */
    public function getActivityQualify()
    {
        $activity_id = I('activity_id');
        $user_id = cookie('uid');
        $pf = I('pf', 1);

        $is_join = false;
        $map = [];
        $map['user_id'] = $user_id;
        $map['activity_id'] = $activity_id;
        $AQ = D('ActivityQualify');
        $list = $AQ->where($map)->select();
        if (count($list) > 0) {
            $is_join = true;
        }
        $this->Export1(0, 'success', $is_join);
    }

    /**
     * 存入用户跳转原链接
     */
    public function saveOriginUrl()
    {
        $pf = I('pf', 0);
        $user_id = I('user_id', 0);
        $pull_text = I('pull_text', '');
        $activity_id = I('activity_id', 0);
        $pc_imgurl = I('pc_imgurl', '');
        $h5_imgurl = I('h5_imgurl', '');

        if (!$user_id) {
            $this->Export1(30001, '参数错误');
        }

        $data['pf'] = $pf;
        $data['user_id'] = $user_id;
        $data['pull_text'] = $pull_text;
        $data['activity_id'] = $activity_id;
        $data['pc_imgurl'] = $pc_imgurl;
        $data['h5_imgurl'] = $h5_imgurl;
        S_orgin_url($user_id, $data);
        $this->Export1(0, 'success', '');
    }

    /**
     * 根据user_id获取用户跳转原链接
     */
    public function getOriginUrl()
    {
        $user_id = I('user_id', 0);
        $data = S_orgin_url($user_id);
        $this->Export1(0, 'success', $data);
    }

    /**
     * 获取所有已存入Redis的地址
     */
    public function getInviteUrl()
    {
        $redis = new \Redis();
        $redis->connect('172.18.137.22', C('REDIS_PORT'));
        $redis->auth('icDb29mLy2s');
        $aa = $redis->hGetAll('api_origin_url');
        dump($aa);
        die;
    }

    /*******************************  钱包优惠规则  ********************************/
    /**
     * 获取钱包优惠规则
     */
    public function getWalletPreferentialRule()
    {
        $mall_type = I('mall_type');
        $preferential_type = I('preferential_type');
        $pf = I('pf', 1);
        $res = $this->getWalletPreferentialRules($mall_type, $preferential_type, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }


    /*******************************  金蛋活动  ***********************************/


    /** 获取最近十条中奖用户
     * 使用魔方系统的活动id记录，为了防止与营销系统中的活动重复，从300000开始往上加
     */
    public function getLastTenEggWinners()
    {
        $activity_id = I('activity_id');
        $type = I('type', 0);
        $pf = I('pf', 1);
        $res = $this->getLastTenEggWinner($activity_id, $type, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 是否已参加过金蛋活动
     */
    public function isJoinedActivity()
    {
        $user_id = cookie('uid');
        $activity_id = I('activity_id');
        $type = I('type');
        $pf = I('pf', 1);
        $res = $this->isJoinActivity($user_id, $activity_id, $type, $pf);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /******************************   用户标签   ******************************/
    /**
     * 根据用户标签展示不同弹窗
     */
    public function getUserLabel()
    {
        $params['label_id'] = I('label_id');
        $params['user_id'] = cookie('uid');
        $params['act_id'] = I('act_id');
        $params['time'] = time();
        $salt = C('SUPER_AUTH_KEY');
        ksort($params);
        $params['_sign'] = pwdhash(http_build_query($params), $salt);
        $res = $this->getUserLabels($params);
        $this->Export1($res['errcode'], $res['errmsg'], $res['data']);
    }

    /**
     * 获取指定活动参与人数
     */
    public function getActivityJoinCount()
    {
        $activity_id = I('activity_id');
        $map = [];
        $map['activity_id'] = $activity_id;
        $AQ = D('ActivityQualify');
        $list = $AQ->where($map)->select();
        $count = (count($list) > 0) ? count($list) : 0;
        $this->Export1(0, 'success', $count);
    }

    /**
     * 获取金蛋活动 假数据轮播 固定20条
     */
    public function getFakeWinner()
    {
        $arr = array(
            130, 131, 132, 133, 134, 135, 136, 137, 138, 139,
            144, 147,
            150, 151, 152, 153, 155, 156, 157, 158, 159,
            176, 177, 178,
            180, 181, 182, 183, 184, 185, 186, 187, 188, 189,
        );
        for ($i = 0; $i < 20; $i++) {
            $tmp[] = $arr[array_rand($arr)] . '****' . mt_rand(1000, 9999);
        }
        $accounts = array_unique($tmp);
        foreach ($accounts as $v) {
            if (!empty($v) && ($v % 2 == 0)) {
                $data[] = $v . '获得50元京东E卡+288元红包';
            } else {
                $data[] = $v . '获得50元京东E卡';
            }
        }
        unset($v);
        $this->Export1(0, 'success', $data);
    }

    /**
     * 获取关注二维码
     */
    public function getFollowCode()
    {
        $user_id = intval($_REQUEST['user_id']);
        $lottery_id = intval($_REQUEST['lottery_id']);
        if ($user_id <= 0 || $lottery_id <= 0) {
            $this->Export1(1, '用户id为空');
        }
        $wechatModel = wechat();
        $url = 'https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=' . $wechatModel->checkAuth();
        $arr = [];
        $arr['expire_seconds'] = 604800;
        $arr['action_name'] = "QR_STR_SCENE";
        $arr['action_info']['scene']['scene_str'] = "choujiang-" . $user_id . "-" . $lottery_id;
        $data = json_encode($arr);

        //$data = '{"expire_seconds":604800,"action_name":"QR_SCENE","action_info":{"scene":{"scene_id":"456"}}}';
        $header = array();
        $header[] = 'Content-Type: application/json';
        $res = post_curl($url, $data, $header);
        $res = json_decode($res, true);
        $ticket = $wechatModel->getQRUrl($res['ticket']);
        $qrurl = $wechatModel->getShortUrl($ticket);
        $this->Export1(0, 'success', $qrurl);
    }

}
