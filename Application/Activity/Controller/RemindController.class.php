<?php
/**
 * Created by PhpStorm.
 * User: leo
 * Date: 2018/5/29
 * Time: 14:57
 */

namespace Activity\Controller;

use Activity\Controller\BaseController;

class RemindController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        if (!in_array(strtolower(ACTION_NAME), array('getwinners','getlotterypics'))) {
            //检查登录
            $res = $this->checkLogin();
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    public function ApiRemind(){
        $Mobile=I('mobile');
        $Code=I('code');
        $RemindTime=I('RemindTime');
        $ACID=I('ac_id');
        $MDID=I('md_id');
        if(empty($Mobile)){
            $UserInfo=S_user(cookie('uid'));
            if(empty($UserInfo['mobile'])) return $this->apiReturn(160001,'没有查找到手机号码');
        }else{
            if(empty($Code)) return $this->apiReturn(160002,'请输入验证码');
        }
        //校验短信验证码
        $code = session_sms($Mobile);
        if ($code !== pwdhash($Code, C('SMS_SALT'))) {
            return $this->apiReturn(160003, '短信验证码错误，请重新获取');
        }
        session_sms($Mobile, null);//验证成功，清除短信验证码
        $data['data']=['mobile'=>$Mobile,'RemindTime'=>$RemindTime,'ac_id'=>$ACID,'md_id'=>$MDID];
        $data['data']=urlencode(json_encode($data['data']));
        $data['timestamp']=time();
        $token=WMSencryption($data);
        $Url=GenerateApiUrl().'/WebApi/RemindSetUp?Token='.$token;
        $result=post_curl($Url,$data);
        $result=json_decode($result,true);
        if(!$result) return $this->apiReturn(160004,'设置提醒失败');
        return $this->apiReturn($result['errcode'],$result['errmsg']);
    }
}