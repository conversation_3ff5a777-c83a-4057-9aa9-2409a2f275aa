<?php
/**
 * Created by PhpStorm.
 * User: leo
 * Date: 2018/5/29
 * Time: 14:57
 */

namespace Activity\Controller;

use Activity\Controller\BaseController;

class CubeController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        if (!in_array(strtolower(ACTION_NAME), array('getwinners', 'getlotterypics','getactivitycenterdata'))) {
            //检查登录
            $res = $this->checkLogin();
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    public function ApiHdUserInfo()
    {
        $user_id = cookie('uid');
        $Model = M('PaymentDays');
        $AcID = I('ac_id');
        $find = $Model->where('user_id =' . $user_id . ' AND ac_id=' . $AcID)->field('id')->find();
        if ($find) {
            return $this->apiReturn(170008, '当前账号已经申请过账期');
        }
        $com_info = S_company($user_id);
        $user_info = S_user($user_id);
        $UserAddressModel = D('Address/UserAddress');
        $datas = $UserAddressModel->getUserList($user_id, 0, ['is_default' => 1], 20,
            'is_default DESC, address_id DESC');
        $data['com_name'] = $com_info['com_name'];
        $data['mobile'] = $user_info['mobile'];
        $data['nick_name'] = '';
        foreach ($datas as $k => $v) {
            if ($v['is_default'] == 1) {
                $data['nick_name'] = $v['consignee'];
            }
        }
        return $this->apiReturn(0, '获取成功', $data);
    }

    public function ApiPaymentDays()
    {
        $Mobile = I('mobile');
        $Code = I('code');
        $ComName = I('com_name');
        $NickName = I('nick_name');
        $AcID = I('ac_id');
        if (empty($ComName)) {
            return $this->apiReturn(170001, '请输入公司名字');
        }
        if (empty($NickName)) {
            return $this->apiReturn(170002, '请输入联系人');
        }
        if (empty($Mobile)) {
            return $this->apiReturn(170003, '请输入手机号码');
        }
        if (empty($Code)) {
            return $this->apiReturn(170004, '请输入验证码');
        }

        //校验短信验证码
        $code = session_sms($Mobile);
        if ($code !== pwdhash($Code, C('SMS_SALT'))) {
            return $this->apiReturn(170005, '短信验证码错误，请重新获取');
        }
        session_sms($Mobile, null);//验证成功，清除短信验证码
        $Model = M('PaymentDays');
        $find = $Model->where('user_id =' . cookie('uid') . ' AND ac_id=' . $AcID)->field('id')->find();
        if ($find) {
            return $this->apiReturn(170007, '当前账号已经申请过账期');
        }
        $data = [
            'mobile' => $Mobile,
            'com_name' => $ComName,
            'nick_name' => $NickName,
            'ac_id' => $AcID,
            'user_id' => cookie('uid'),
            'add_time' => time()
        ];
        $result = $Model->add($data);
        if (!$result) {
            return $this->apiReturn(170006, '申请账期失败');
        }
        return $this->apiReturn(0, '申请账期成功');
    }

    public function ApiUserWrite()
    {
        $collert = I('param.');
        if (empty($collert['activity_id'])) {
            return $this->apiReturn(160105, '提交失败');
        }
        if (isset($collert['user_type'])) {
            if ($collert['user_type'] == 1) {
                $collert['is_new_reg'] = 1;
            } else {
                $collert['is_new_reg'] = 2;
            }
        }
        $collert['is_new_reg'] = isset($collert['user_type']) ? $collert['user_type'] : 2;

        $Arr = [
            'is_new_reg',
            'input_1',
            'input_1_value',
            'input_2',
            'input_2_value',
            'input_3',
            'input_3_value',
            'account',
            'user_id',
            'activity_id',
            'company_name',
            'pf',
            'work_function',
            'relate_voucher'
        ];

        $collert['user_id'] = S_account($collert['account']);
        if ($collert['user_id'] != cookie('uid')) {
            return $this->apiReturn(160103, '提交失败，提交用户与登录用户不匹配');
        }
        foreach ($collert as $k => $v) {
            if (!in_array($k, $Arr)) {
                unset($collert[$k]);
            }
        }

        $data['data'] = urlencode(json_encode($collert));
        $data['timestamp'] = time();
        $Token = WMSencryption($data);
        $Url = CUBE_DOMAIN . '/WebApi/ApiUserWrite?Token=' . $Token;
        $result_json = post_curl($Url, $data);
        $result = json_decode($result_json, true);
        if (!isset($result['errcode'])) {
            return $this->apiReturn(160104, '提交失败', $result_json);
        }
        return $this->apiReturn($result['errcode'], $result['errmsg']);
    }

    //获取展示的活动中心数据
    public function getActivityCenterData()
    {
        $activityList = D('Activity/ActivityNew')->getActivityCenterActivityList();
        $couponList = D('Coupon/Coupon')->getActivityCenterCouponList();
        $data = [
            'activity_list' => $activityList,
            'activity_count' => count($activityList),
            'coupon_list' => $couponList,
            'coupon_count' => count($couponList),
        ];
        return $this->apiReturn(0, 'ok', $data);
    }

}
