<?php
/**
 * Created by PhpStorm.
 * User: leo
 * Date: 2018/5/29
 * Time: 14:57
 */

namespace Activity\Controller;

use Activity\Controller\BaseController;

class FootstoneController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        //不用登陆
//        if (!in_array(strtolower(ACTION_NAME), array('getwinners','getlotterypics'))) {
//            //检查登录
//            $res = $this->checkLogin();
//            if ($res['err_code'] != 0) {
//                return $this->apiReturn($res['err_code'], $res['err_msg']);
//            }
//        }
    }

    public function ApiSelfList(){
        $class_id2=I('class_id2');
        $brand_id=I('brand_id');
        $supplier_id=I('supplier_id');
        $data['data']=['class_id2'=>$class_id2,'brand_id'=>$brand_id,'supplier_id'=>$supplier_id];
        $data['data']['Random.class']=rand(10000,99999);//写一个随机数，防止同一时间多次请求
        $data['data']=urlencode(json_encode($data['data']));
        $data['timestamp']=time();
        $token=WMSencryption($data);
        $Url=GenerateApiUrl('','footstone').'/WebApi/RemindSetUp?Token='.$token;
        $result=post_curl($Url,$data);
        $result=json_decode($result,true);
        if(!$result) return $this->apiReturn(160004,'设置提醒失败');
    }
}