<?php
/**
 * 旧订单获取新订单状态名
 * @param  [type]  $order_status    [description]
 * @param  [type]  $pay_status      [description]
 * @param  integer $shipping_status [description]
 * @return [type]                   [description]
 */
function status_to_name($order_status, $pay_status, $shipping_status = 0)
{
    $status_mapping = C('ORDER_STATUS_MAPPING');
    $name = '';
    foreach ($status_mapping as &$v) {
        $order = is_null($v['1']) ? null : explode(',', $v['1']);
        $pay = is_null($v['2']) ? null : explode(',', $v['2']);
        if ((in_array($order_status, $order) || is_null($order)) && (in_array($pay_status, $pay) || is_null($pay))) {
            $name = $v['0'];
            break;
        }
    }
    return $name;
}

/**
 * 旧订单获取新订单状态ID
 * @param  [type]  $order_status    [description]
 * @param  [type]  $pay_status      [description]
 * @param  integer $shipping_status [description]
 * @return [type]                   [description]
 */
function status_to_id($order_status, $pay_status, $shipping_status = 0)
{
    $status_mapping = C('ORDER_STATUS_MAPPING');
    $id = '';
    foreach ($status_mapping as $k => &$v) {
        $order = is_null($v['1']) ? null : explode(',', $v['1']);
        $pay = is_null($v['2']) ? null : explode(',', $v['2']);

        if ((in_array($order_status, $order) || is_null($order)) && (in_array($pay_status, $pay) || is_null($pay))) {
            $id = $k;
            break;
        }
    }
    return $id;
}

