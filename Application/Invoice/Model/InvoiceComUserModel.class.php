<?php 
namespace Invoice\Model;

use Think\Model;

class InvoiceComUserModel extends Model {

	//调用配置文件中的数据库配置1
	protected $connection = 'CRM_DB_CONFIG';
	
	// 检查发票公司ID、用户ID、后台登录ID是否存在关联关系
	public function isRelationExists($inv_com_id, $user_id, $sale_id)
	{
		$map = array (
			'com_id'  => $inv_com_id,
			'user_id' => $user_id,
			'sale_id' => $sale_id,
			'status'  => 0,
		);

		return $this->where($map)->getField('id');
	}

}

