<?php 
namespace Invoice\Model;

use Think\Model;

class InvoiceCompanyModel extends Model {
	//调用配置文件中的数据库配置1
	protected $connection = 'CRM_DB_CONFIG';
	// 添加公司
	public function addCompany($_data=[])
	{
		try{
			$com_name = trim($_data['tax_title']);
            $verify_status = $_data['verify_status'];////验证状态，-1-失败，1-成功'
			// 公司存在则不新增
			$id = $this->where(['com_name' => $com_name])->getField('id');
			if($id) {
				//公司存在则提交审核
//				$this->invoiceComApply($_data);
				return true;
			}

			//新增公司
			$data = [];
			$data['com_name']             = $com_name;
			$data['company_type']         = 1;
			$data['com_nature']           = isset($_data['com_nature']) ? $_data['com_nature'] : 0;
			$data['status']               = 2;//待审核
			$data['com_addr']             = isset($_data['company_address']) ? $_data['company_address'] : '';
			$data['com_tel']              = isset($_data['company_phone']) ? $_data['company_phone'] : '';
			$data['com_tax_registration'] = isset($_data['tax_no']) ? $_data['tax_no'] : '';
			$data['com_bank']             = isset($_data['bank_name']) ? $_data['bank_name'] : '';
			$data['com_bank_num']         = isset($_data['bank_account']) ? $_data['bank_account'] : '';
			$data['com_sn']               = $this->getComSn();
			$data['create_time']          = time();
			$data['update_time']          = time();

			$this->startTrans();

			$com_id = $this->add($data);

			if(!$com_id){
				$this->rollback();
			}

			$uid = I('request.user_id', 0, 'intval');
			if($uid <= 0){
				$uid = cookie('uid');
			}

//			if($uid){
//				$sale_id = D("InvoiceComUser")->where(["user_id"=>$uid])->getField('sale_id');
//				$sale_id = $sale_id ? $sale_id : 0;
//
//				$isExistsNoComInfo = D("InvoiceComUser")->where(["user_id"=>$uid,"com_id"=>0])->find();
//				if($isExistsNoComInfo){
//					D("InvoiceComUser")->where(["id"=>$isExistsNoComInfo["id"]])->save([
//						"com_id"=>$com_id,
//						"user_id"=>$uid,
//						"sale_id"=>$sale_id,
//						"status"=>0,
//						"update_time"=>time(),
//					]);
//				}else{
//					D("InvoiceComUser")->add([
//						"com_id"=>$com_id,
//						"user_id"=>$uid,
//						"sale_id"=>$sale_id,
//						"status"=>0,
//						"sale_type"=>1,
//						"create_time"=>time(),
//						"update_time"=>time(),
//					]);
//				}
//			}

			// 添加新增公司日志
			$ActionLogModel = D('ActionLog');
			$operator_id = I('operator_id', 0);

			if ($operator_id) {
				$log = '后台新增发票';
			} else {
				$log = '用户前台新增发票';
			}

			$ActionLogModel->addCompanyLog($com_id, $operator_id, '新增公司', $log);

			$this->commit();
		}catch(\Exception $e){
			$this->rollback();
		}
	}


	/*
	 * 用户修改公司发票  提交审核
	 */
	public function invoiceComApply($_data=[]){
		try{
			$this->startTrans();
			$data['com_name'] = $_data['tax_title'] ? trim($_data['tax_title']) : "";
			$data["com_addr"] = $_data['company_address'] ? trim($_data['company_address']) : "";
			$data["com_tel"] = $_data['company_phone'] ? trim($_data['company_phone']) : "";
			$data["com_tax_registration"] = $_data['tax_no'] ? trim($_data['tax_no']) : "";
			$data["com_bank"] = $_data['bank_name'] ? trim($_data['bank_name']) : "";
			$data["com_bank_num"] = $_data['bank_account'] ? trim($_data['bank_account']) : "";
			$data["mobile_email_landline"] = "";

			$oldInfo = $this->where(['com_name' => trim($_data['tax_title'])])->find();

			$com_tax_registration_bool = ($oldInfo["com_tax_registration"] != trim($_data['tax_no']));
			$com_bank_bool = ($oldInfo["com_bank"] != trim($_data['bank_name']));
			$com_bank_num_bool = ($oldInfo["com_bank_num"] != trim($_data['bank_account']));
			$com_addr_bool = ($oldInfo["com_addr"] != trim($_data['company_address']));
			$com_tel_bool = ($oldInfo["com_tel"] != trim($_data['company_phone']));

			//查找关联关系
			$uid = I('request.user_id', 0, 'intval');
			if (!$uid || empty($uid)) {
				$uid = cookie('uid');
			}

			$userInfo = S_user($uid);
			if($userInfo){
				$data["mobile_email_landline"] = $userInfo["mobile"] ? $userInfo["mobile"] : $userInfo["email"];
				$data["mobile_email_landline"] = $data["mobile_email_landline"] ? $data["mobile_email_landline"] : "";
			}

			//公司跟进的销售
			$comUserCom = D("InvoiceComUser")->where(["com_id"=>$oldInfo['id']])->getField("sale_id",true);

			//客户跟进的销售
			$comUserUser = D("InvoiceComUser")->where(["user_id"=>$uid])->find();
			$_sale_id = $comUserUser ? $comUserUser["sale_id"] : 0;
			$_sale_type = $comUserUser ? $comUserUser["sale_type"] : 1;
			$saleName = D("Order/Cms")->table("user_info")->where(["userId"=>$_sale_id])->getField("name");

			if($com_tax_registration_bool || $com_bank_bool || $com_bank_num_bool || $com_addr_bool || $com_tel_bool){
				//新增公司申请
				$existsapply = D('InvoiceComApply')->field("id")->where([
					"status"=>-1,
					"apply_type"=>3,//申请类型 1公司 2联系人 3公司发票修改
					"sale_type"=>$_sale_type,
//					"create_type"=>3,//发起方式:1销售申请  2订单系统新增 3平台发起  4管理员导入
					"user_id"=>$uid,
					"com_id"=>$oldInfo['id'],
					"apply_admin_id"=>$_sale_id,
				])->find();

				if($existsapply){
					D('InvoiceComApply')->where(["id"=>$existsapply["id"]])->delete();
				}

				D('InvoiceComApply')->add([
					"status"=>-1,
					"apply_type"=>3,//申请类型 1公司 2联系人 3公司发票修改
					"sale_type"=>$_sale_type,
					"create_type"=>3,//发起方式:1销售申请  2订单系统新增 3平台发起  4管理员导入
					"user_id"=>$uid,
					"com_id"=>$oldInfo['id'],
					"apply_admin"=>$saleName ? $saleName : "",
					"apply_admin_id"=>$_sale_id,
					"content"=>json_encode($data),
					"create_time"=>time(),
					"update_time"=>time(),
				]);
			}


			if(!empty($comUserCom) && !in_array($_sale_id,$comUserCom)){
				$existsapply = D('InvoiceComApply')->field("id")->where([
					"status"=>-1,
					"apply_type"=>2,//申请类型 1公司 2联系人 3公司发票修改
					"sale_type"=>$_sale_type,
//					"create_type"=>3,//发起方式:1销售申请  2订单系统新增 3平台发起  4管理员导入
					"user_id"=>$uid,
					"com_id"=>$oldInfo['id'],
					"apply_admin_id"=>$_sale_id,
				])->find();

				if($existsapply){
					D('InvoiceComApply')->where(["id"=>$existsapply["id"]])->delete();
				}
				//发起联系人申请
				D('InvoiceComApply')->add([
					"status"=>-1,
					"apply_type"=>2,//申请类型 1公司 2联系人 3公司发票修改
					"sale_type"=>$_sale_type,
					"create_type"=>3,//发起方式:1销售申请  2订单系统新增 3平台发起  4管理员导入
					"user_id"=>$uid,
					"com_id"=>$oldInfo['id'],
					"apply_admin"=>$saleName ? $saleName : "",
					"apply_admin_id"=>$_sale_id,
					"content"=>json_encode($data),
					"create_time"=>time(),
					"update_time"=>time(),
				]);


			}


			$this->commit();
		}catch(\Exception $e){
//			dump($e->getMessage());
			$this->rollback();
		}
	}

	// 修改
	public function updateCompany($data=[])
	{

		return true;
	}

	// 设置公司编码，规则：随机六位
	public function getComSn()
	{
		$sn = rand(100000, 999999);

		$id = $this->where(['com_sn' => $sn])->getField('id');

		if (!empty($id)) {
			$sn = $this->getComSn();
		}

		return $sn;
	}



}
