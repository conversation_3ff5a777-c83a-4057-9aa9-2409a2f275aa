<?php
namespace Invoice\Model;

use Think\Model;

class TaxinfoModel extends Model {

    protected $_validate = array(
//        array('tax_title', 'require', '发票抬头不能为空', 1),
//        array('company_address', 'require', '公司注册地址不能为空', 0),
//        array('company_phone', 'require', '公司电话不能为空', 0),
//        array('tax_no', 'require', '税务登记号不能为空', 0),
//        array('bank_name', 'require', '开户银行不能为空', 0),
//        array('bank_account', 'require', '开户银行账号不能为空', 0),
//        array('consignee', 'require', '收票人不能为空', 0),
//        array('consignee_phone', 'require', '收票人联系方式不能为空', 0),
//        array('consignee_province', 'number', '寄送省份不能为空', 0),
//        array('consignee_city', 'number', '寄送城市不能为空', 0),
//        array('consignee_district', 'number', '寄送区不能为空', 1),
//        array('consignee_address', 'require', '详细地址不能为空', 0),
        array('is_default', array(1,0), '是否默认值错误', 2, 'in'),
        array('inv_type', array(2,3,4,5), '发票类型错误', 1, 'in'),
    );

    public static $FirstNatureList = [
        "1"=>"KA终端",
        "2"=>"SMB终端",
        "3"=>"贸易商",
        "4"=>"科研院校",
    ];

    public function getInfo($order_sn, $where = '')
    {
        $map = array(
            'order_sn' => $order_sn,
        );
        $datas = $this->where($map)->field('inv_type, inv_payee, inv_content')->find();
        return $datas;
    }

    /**
     * 获取列表
     * @param  string $map   [description]
     * @param  string $limit [description]
     * @param  string $order [description]
     * @return [type]        [description]
     */
    public function getUserList($user_id, $where = '', $page = '', $order = 'tax_id DESC')
    {
        if(!empty($user_id)){
            if (is_array($user_id)) {
                $map = array(
                    'user_id' => $user_id['user_id'],
                );
                if ($user_id['is_self']) {
                    $map['is_self'] = $user_id['is_self'];
                }
            } else {

                $map = array(
                    'user_id' => $user_id,
                );
            }
        }else{
            if (is_array($user_id)) {
                $map = array(
                );
                if ($user_id['is_self']) {
                    $map['is_self'] = $user_id['is_self'];
                }
            } else {

                $map = array(
                );
            }
        }
        $map["status"] = 1;//显示正常的发票
        list($p, $limit) = explode(',', $page);
        empty($p) && $p = 1;
        empty($limit) && $limit = C('DEFAULT_PAGE_LIMIT');
        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->where($map)->page($p, $limit)->order($order)
                        ->field('is_self','tax_id, tax_title, consignee, consignee_country,consignee_phone, consignee_province, consignee_city, consignee_district, consignee_address, invoice_file, invoice_file_name, is_default, intl_code, inv_type')
                        ->select();
        foreach ($datas as &$v) {
            $v['consignee_province'] = !empty($v['consignee_province']) ? $v['consignee_province'] : '';
            $v['consignee_province_val'] = !empty($v['consignee_province']) ? get_province($v['consignee_province']) : '';
            $v['consignee_city'] = !empty($v['consignee_city']) ? $v['consignee_city'] : '';
            $v['consignee_city_val'] = !empty($v['consignee_city']) ? get_city($v['consignee_city']) : '';
            $v['consignee_district'] = !empty($v['consignee_district']) ? $v['consignee_district'] : '';
            $v['consignee_district_val'] = !empty($v['consignee_district']) ? get_district($v['consignee_district']) : '';
        }
        return $datas;
    }

    /**
     * 获取用户发票信息
     * @param  [type] $user_id [description]
     * @param  [type] $tax_id  [description]
     * @return [type]          [description]
     */
    public function getUserInfo($user_id, $tax_id)
    {
        if(!empty($user_id)){
            $map = array(
                'user_id' => $user_id,
                'tax_id' => $tax_id,
            );
        }else{
            $map = array(
                'tax_id' => $tax_id,
            );
        }

        $datas = $this->where($map)->field('tax_id, tax_title, inv_type, company_address, company_phone, tax_no, bank_name, bank_account, consignee, consignee_phone, consignee_province,consignee_country, consignee_city, consignee_district, consignee_address, invoice_file, invoice_file_name, is_default, intl_code','email')->find();
        if (!empty($datas)) {
            $datas['consignee_province'] = !empty($datas['consignee_province']) ? $datas['consignee_province'] : '';
            $datas['consignee_province_val'] = !empty($datas['consignee_province']) ? get_province($datas['consignee_province']) : '';
            $datas['consignee_city'] = !empty($datas['consignee_city']) ? $datas['consignee_city'] : '';
            $datas['consignee_city_val'] = !empty($datas['consignee_city']) ? get_city($datas['consignee_city']) : '';
            $datas['consignee_district'] = !empty($datas['consignee_district']) ? $datas['consignee_district'] : '';
            $datas['consignee_district_val'] = !empty($datas['consignee_district']) ? get_district($datas['consignee_district']) : '';
        }
        return $datas;
    }

    public function getUserInfoByUcid($user_id, $tax_id,$ucid=0)
    {


        if(!empty($user_id)){
            $map = array(
                'user_id' => $user_id,
                'tax_id' => $tax_id,
                'uc_id' => $ucid,
            );
        }else{
            $map = array(
                'tax_id' => $tax_id,
                'uc_id' => $ucid,
            );
        }
        $datas = $this->where($map)->field('tax_id, tax_title, inv_type, company_address, company_phone, tax_no, bank_name, bank_account, consignee, consignee_phone, consignee_province, consignee_city, consignee_district, consignee_address, invoice_file, invoice_file_name, is_default, intl_code')->find();
        if (!empty($datas)) {
            $datas['consignee_province'] = !empty($datas['consignee_province']) ? $datas['consignee_province'] : '';
            $datas['consignee_province_val'] = !empty($datas['consignee_province']) ? get_province($datas['consignee_province']) : '';
            $datas['consignee_city'] = !empty($datas['consignee_city']) ? $datas['consignee_city'] : '';
            $datas['consignee_city_val'] = !empty($datas['consignee_city']) ? get_city($datas['consignee_city']) : '';
            $datas['consignee_district'] = !empty($datas['consignee_district']) ? $datas['consignee_district'] : '';
            $datas['consignee_district_val'] = !empty($datas['consignee_district']) ? get_district($datas['consignee_district']) : '';
        }
        return $datas;
    }

    /**
     * 根据抬头获取发票ID
     * @param  [type] $user_id   [description]
     * @param  [type] $tax_title [description]
     * @return [type]            [description]
     */
    public function getUserTax($user_id, $tax_title,$uc_id=-999)
    {


        if(!empty($user_id)){
            $map = array(
                'user_id' => $user_id,
                'tax_title' => $tax_title,
            );
        }else{
            $map = array(
                'uc_id' => $uc_id,
                'tax_title' => $tax_title,
            );
        }

        $datas = $this->where($map)->field('tax_id, tax_title, company_address, company_phone, tax_no, bank_name, bank_account, consignee, consignee_phone, consignee_province, consignee_city, consignee_district, consignee_address, invoice_file, invoice_file_name, is_default, intl_code')->find();
        return $datas;
    }

    /**
     * 获取列表数
     * @param  [type] $user_id [description]
     * @param  string $where   [description]
     * @return [type]          [description]
     */
    public function getUserCount($user_id, $where = '')
    {
        if(!empty($user_id)){
            if (is_array($user_id)) {
                $map = array(
                    'user_id' => $user_id['user_id'],
                );
                if ($user_id['is_self']) {
                    $map['is_self'] = $user_id['is_self'];
                }
            } else {
                $map = array(
                    'user_id' => $user_id,
                );
            }
        }else{
            if (is_array($user_id)) {
                $map = array(
                );
                if ($user_id['is_self']) {
                    $map['is_self'] = $user_id['is_self'];
                }
            } else {
                $map = array(
                );
            }
        }

        !empty($where) && $map = array_merge($map, $where);
        $map["status"] = 1;//显示正常的发票
        $datas = $this->where($map)->count();
        return $datas;
    }

    public function getUserCountV2( $where = '')
    {
        $map = [];
        !empty($where) && $map = array_merge($map, $where);
        $map["status"] = 1;//显示正常的发票
        $datas = $this->where($map)->count();
        return $datas;
    }


    /**
     * 设置客户默认发票（后期增加类型）
     * @param [type] $user_id      [description]
     * @param [type] $tax_id       [description]
     */
    public function setUserDefault($user_id, $tax_id)
    {
        $this->startTrans();
        $map = array(
            'user_id' => $user_id,
        );
        $res = $this->where($map)->setField('is_default', 0);
        if ($res === false) {
            return false;
        }
        $map = array(
            'user_id' => $user_id,
            'tax_id' => $tax_id,
        );
        $res = $this->where($map)->setField('is_default', 1);
        if ($res === false) {
            $this->rollback();
            return false;
        }
        $this->commit();
        return $res;
    }

    public function setUserDefaultV2($user_id, $tax_id,$uc_id=-999)
    {
        $this->startTrans();
        $map = array(
            'uc_id' => $uc_id,
        );
        $res = $this->where($map)->setField('is_default', 0);
        if ($res === false) {
            return false;
        }
        $map = array(
            'tax_id' => $tax_id,
            'uc_id' => $uc_id,
        );
        $res = $this->where($map)->setField('is_default', 1);
        if ($res === false) {
            $this->rollback();
            return false;
        }
        $this->commit();
        return $res;
    }

    /**
     * 删除用户发票
     * @param  [type] $user_id    [description]
     * @param  [type] $tax_id [description]
     * @return [type]             [description]
     */
    public function deleteUser($user_id, $tax_id)
    {

        $map = array(
            'user_id' => $user_id,
            'tax_id' => $tax_id,
        );
        // $res = $this->where($map)->delete();

        $save = [
            'status' => -1,
            'update_time' => time(),
        ];
        $res = $this->where($map)->save($save);
        return $res;
    }

    public function deleteUserByOrgid($user_id, $tax_id,$uc_id=1)
    {


        if(!empty($user_id)){
            $map = array(
                'user_id' => $user_id,
                'tax_id' => $tax_id,
                'uc_id' => $uc_id,
            );
        }else{
            $map = array(
                'tax_id' => $tax_id,
                'uc_id' => $uc_id,
            );
        }
        // $res = $this->where($map)->delete();

        $save = [
            'status' => -1,
            'update_time' => time(),
        ];
        $res = $this->where($map)->save($save);
        return $res;
    }

    public function getUserTaxinfoByIdarr($user_id,$uc_id,$tax_ids,$invoce_title){
        $query = $this->where([
            'tax_id'=>array('in',$tax_ids),
             "audit_status"=>2,
             'user_id' => $user_id,
             'uc_id' => $uc_id,
             "inv_type"=>["in",'3,4,5'],
            ]);
        if(!empty($invoce_title)){
            $query->where(array('tax_title'=>trim($invoce_title)));
        }
        return $query->field('tax_id, tax_title,com_id')->select();
    }

    //下单前，该账号，选择的发票-公司名称在CRM中不存在审核通过的数据
    public function getUserTaxinfoPassByTaxtitle($user_id,$uc_id,$tax_title){
        $query = $this->where([
             "audit_status"=>2,//审核通过
             'user_id' => $user_id,
             'uc_id' => $uc_id,
             "tax_title"=>$tax_title,
            ]);
       
        return $query->count();
    }

}