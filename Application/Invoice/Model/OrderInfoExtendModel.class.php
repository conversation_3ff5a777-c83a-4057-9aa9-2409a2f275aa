<?php
namespace Invoice\Model;

use Think\Model;

class OrderInfoExtendModel extends Model {

    public function getInfo($order_sn, $field = '*')
    {
        $map = array(
            'order_sn' => $order_sn,
        );
        $datas = $this->where($map)->field($field)->find();
        return $datas;
    }

    /**
     * 获取列表
     * @param  string $map   [description]
     * @param  string $limit [description]
     * @param  string $order [description]
     * @return [type]        [description]
     */
    public function getUserList($user_id, $where = '', $page = '', $order = 'OE.order_sn DESC')
    {
        $map = array(
            'O.user_id' => $user_id,
        );
        list($p, $limit) = explode(',', $page);
        empty($p) && $p = 1;
        empty($limit) && $limit = C('DEFAULT_PAGE_LIMIT');
        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->alias('OE')->join(C('DB_PREFIX'). 'order_info O ON O.order_sn = OE.order_sn')
                        ->where($map)->page($p, $limit)->order($order)
                        ->field('OE.order_sn, OE.inv_type, OE.inv_status, OE.inv_shipping_id, OE.inv_shipping_name, OE.inv_shipping_no, OE.inv_consignee, O.order_amount, O.order_status, O.pay_status')
                        ->select();
        foreach ($datas as &$v) {
            $v['order_amount_format'] = price_format($v['order_amount'], 1);
        }
        return $datas;
    }

    /**
     * 获取列表数
     * @param  [type] $user_id [description]
     * @param  string $where   [description]
     * @return [type]          [description]
     */
    public function getUserCount($user_id, $where = '')
    {
        $map = array(
            'O.user_id' => $user_id,
        );
        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->alias('OE')->join(C('DB_PREFIX'). 'order_info O ON O.order_sn = OE.order_sn')
                        ->where($map)
                        ->count();
        return $datas;
    }
}