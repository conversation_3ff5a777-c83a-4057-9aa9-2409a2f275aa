<?php 
namespace Invoice\Model;

use Think\Model;

class ActionLogModel extends Model {
	//调用配置文件中的数据库配置1
	protected $connection = 'CRM_DB_CONFIG';

    public function addCompanyLog($com_id, $operator_id, $event='', $remark='', $type=1)
    {
        $CmsModel = D('Order/Cms');

        $log['user_id']     = $com_id;
        $log['id_type']     = 2;
        $log['operator_id'] = $operator_id;
        $log['type']        = $type;
        $log['event']       = $event;
        $log['remark']      = $remark;
        $log['ip']          = get_client_ip(0, true);
        $log['create_time'] = time();

        if ($operator_id) {
            $log['admin'] = $CmsModel->getUserName($operator_id);
        }

        $actionLog = $this->add($log);

        if ($actionLog === false) return false;

        return $actionLog;
    }

}