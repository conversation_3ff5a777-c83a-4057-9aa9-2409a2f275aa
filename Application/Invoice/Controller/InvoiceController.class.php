<?php
namespace Invoice\Controller;

use GuzzleHttp\Exception\ConnectException;
use Invoice\Controller\BaseController;
use Invoice\Model\TaxinfoModel;

class InvoiceController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), array('conf', 'infobyorder'))) {
            // 检查登录
            if (in_array(strtolower(ACTION_NAME), array('lists', 'info'))) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    /**
     * 获取用户常用发票列表
     * @return [type] [description]
     */
    public function lists()
    {
        $p = I('p', 1, 'intval');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        if(empty($uc_id)){
            $uc_id = -99999;
        }
        $TaxinfoModel = D('Taxinfo');
        $page = $p.',1000';
        $count = $TaxinfoModel->getUserCount(0,["uc_id"=>$uc_id]);
        $data = $TaxinfoModel->getUserList(0, ["uc_id"=>$uc_id], $page);
        $datas = page_data($data, $count, $page);
        if (empty($datas)) {
            return $this->apiReturn(21009, '发票获取失败');
        }
        return $this->apiReturn(0, '获取成功', $datas);
    }

    // 后台新增订单获取发票
    public function infoByOrder()
    {
        $user_id = I('uid', '');
        $tax_id = I('tax_id', 0, 'intval');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $TaxinfoModel = D('Taxinfo');
        $info = $TaxinfoModel->getUserInfo(0, $tax_id);
        if (empty($info)) {
            return $this->apiReturn(23022, '未找到相关信息');
        }
        return $this->apiReturn(0, '获取成功', $info);
    }

    /**
     * 获取用户发票信息
     * @return [type] [description]
     */
    public function info()
    {
        $user_id = I('user_id') ? I('user_id') : cookie('uid');
        $org_id      = I('org_id', 1, 'intval');//组织id 1猎芯 3华云
        $tax_id = I('tax_id', 0, 'intval');
        $TaxinfoModel = D('Taxinfo');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $info = $TaxinfoModel->getUserInfo(0, $tax_id);
        if (empty($info)) {
            return $this->apiReturn(23022, '未找到相关信息');
        }
        return $this->apiReturn(0, '获取成功', $info);
    }


    /**
     * 获取用户发票信息
     * @return [type] [description]
     */
    public function getTaxId()
    {
        $tax_title = I('tax_title', 0, 'intval');
        $TaxinfoModel = D('Taxinfo');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $info = $TaxinfoModel->getUserTax(0, $tax_title,$uc_id);
        if (empty($info)) {
            return $this->apiReturn(23022, '未找到相关信息');
        }
        return $this->apiReturn(0, '获取成功', $info);
    }


    /**
     * 新增用户发票
     * @return [type] [description]
     */
    public function create()
    {
        $user_id = I('user_id') ? I('user_id') : cookie('uid');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $org_id = I('org_id') ? I('org_id') : 1;
        $operator_id = I('operator_id', 0);
        $TaxinfoModel = D('Taxinfo');

        $consignee_phone = I('consignee_phone');
        $intl_code = I('intl_code');
        $consignee_phone = get_inte_mobile($consignee_phone, $intl_code);

        $is_self = I('is_self', '', 'trim');
        $tax_info["inv_area"] = I('inv_area', 1);;//国内公司
        if($org_id == 3 && $tax_info["inv_area"] == 2){
            $is_self = 1;
        }
        if (!empty($consignee_phone) && !is_mobile($consignee_phone) && !$is_self) {
            return $this->apiReturn(11006, '手机号码格式错误，请重新输入', '');
        }

        $tax_info = I('request.');

        if(!isset($tax_info["uc_id"])){
            $tax_info["uc_id"] = $uc_id;
        }

        if(!isset($tax_info["org_id"])){
            $tax_info["org_id"] = $org_id;
        }

        isset($tax_info['tax_title']) && $tax_info['tax_title'] = trim($tax_info['tax_title']);
        unset($tax_info['operator_id']);
        unset($tax_info['com_nature']);

        if(!empty($tax_info['tax_title'])){
            $tax_info['tax_title'] = replaceSpace($tax_info['tax_title']);
        }

        //验证税号和公司合法性
        $com_name = I('tax_title', '', 'trim');
        $com_name = replaceSpace($com_name);
        $com_tax_registration = I('tax_no', '', 'trim');
        $comCert = $this->checkTheInnerEyeSevice([
            "com_name"=>$com_name,
            "com_tax_registration"=>$com_tax_registration,
            "company_type"=>1,
        ]);

        if($org_id == 3){
            $tax_info["user_sn"] = sprintf("U%sHY",$user_id);
        }else{
            $tax_info["user_sn"] = sprintf("U%s",$user_id);
        }


        $tax_info["verify_status"] = 0;
        $tax_info["audit_status"] = 2;//审核通过
        $inv_type_temp = I("inv_type",2,"intval");//2普通发票 3增值税专用发票 4增值税普通发票
        if(in_array($inv_type_temp, array(3,4,5))){
            $tax_info["verify_status"] = -1;
            $tax_info["verify_time"] = time();
            //重新验证公司和发票是否一致
            if($comCert["err_code"] == 0 && $comCert["data"]["com_name"] == $com_name && $comCert["data"]["com_tax_registration"] == $com_tax_registration){
                $tax_info["verify_status"] = 1;
                $tax_info["verify_time"] = time();
                $isExistsTaxinfo = $TaxinfoModel->where(["tax_title"=>trim($tax_info["tax_title"]),"audit_status"=>2])->find();
                if($isExistsTaxinfo){
                    //公司如果存在  就是绑定联系人 需要审核
                    // $tax_info["audit_status"] = 0;//待提交
                    $tax_info["com_id"] = $isExistsTaxinfo["com_id"];//审核通过  新增公司 验证通过后 直接审核通过
                }
            }
        }


        $tax_info["com_industry"] = !empty($comCert["data"]["com_industry"]) ? $comCert["data"]["com_industry"] :"";//公司行业
        $tax_info["first_nature"] = !empty($comCert["data"]["company_nature"]) ? $comCert["data"]["company_nature"] :"";//初鉴性质
        $firstNatureList = array_flip(TaxinfoModel::$FirstNatureList);
        $tax_info["first_nature"] = !empty($firstNatureList[$tax_info["first_nature"]]) ? $firstNatureList[$tax_info["first_nature"]] : 0;

        $tax_info["create_time"] = time();
        $tax_info["update_time"] = time();

        $tax_info["inv_source"] = 1;//网站新增
        $tax_info["inv_area"] = I('inv_area', 1);;//国内公司
        if($tax_info["inv_type"] == "2"){//普通发票 个人
            $tax_info["inv_area"] = 3;//个人
            $tax_info["audit_status"] = -2;//无需审核
        }

        if($tax_info["inv_area"] == 1){
                $tax_info["sign_com_id"] = 1;
                $tax_info["sign_com_name"] = "深圳市猎芯科技有限公司";
        }

        $TaxinfoModel->startTrans();
        try{
            if (!$TaxinfoModel->create($tax_info)) {
                return $this->apiReturn(23031, $TaxinfoModel->getError());
            }
            $inv_type = $TaxinfoModel->inv_type;
            if (empty($inv_type)) {
                $inv_type = 3;//默认增值税专用发票
                $TaxinfoModel->inv_type = $inv_type;
            }
            $TaxinfoModel->user_id = $user_id;
            $is_default = $TaxinfoModel->is_default;


            $isExists = $TaxinfoModel->where(["uc_id"=>$uc_id,"status"=>1,"tax_title"=>trim($tax_info['tax_title']),"inv_type"=>$inv_type])->count("tax_id");
            if($isExists){
                return $this->apiReturn(23032, '您的发票列表中已经存在该条信息!~_~');
            }

            $tax_id = $TaxinfoModel->add();
            if ($tax_id === false) {
                return $this->apiReturn(23032, '发票新增失败');
            }
            // 修改CRM公司名称
            $CrmModel = D('Crm/CrmNew');

            $updateTaxinfo=[];
            $crmUser = $CrmModel->table("lie_user")->where(["user_id"=>$user_id,"org_id"=>$org_id])->find();
            if($crmUser){
                $updateTaxinfo["sale_id"] = $crmUser["sale_id"] ?: 0;
            }
            $updateTaxinfo["tax_sn"] = sprintf("B%s",$tax_id);
            $TaxinfoModel->where(["tax_id"=>$tax_id])->save($updateTaxinfo);

            //设置默认地址
            if ($is_default == 1) {
                $res = $TaxinfoModel->setUserDefaultV2(0, $tax_id,$uc_id);
                if ($res === false) {
                    return $this->apiReturn(23030, '设置默认发票失败');
                }
            }
            $TaxinfoModel->commit();
        }catch (\Exception $e){
            $TaxinfoModel->rollback();
            return $this->apiReturn(23030, '新增发票失败');
        }

        try{
            //验证实体名单
            $data = array(
                    "tax_id" => $tax_id,
                    "delivery_place" => 1,
                    "address_id" => 0,
                    "is_get_msgcode" => 1,//获取实体名单具体值 1是 0否
            );
            $url = C("crmDomain");
            $res = post_curl(sprintf("%s%s",rtrim($url,"/"),"/open/checkoutCompanyInfo"),$data);
            $res = json_decode($res,true);
            $updateTaxinfo=[];
            if($res && in_array($res,[-1,-2,-3])){
                $updateTaxinfo["group_verify_result"] = -1;
                $updateTaxinfo["group_verify_type"] = $res;
            }else{
                $updateTaxinfo["group_verify_result"] = 1;
            }
            $TaxinfoModel->where(["tax_id"=>$tax_id])->save($updateTaxinfo);
        }catch (\Exception $e){
        }



        // try{
        //     //创建发票推送一体化风险预警
        //     $data = array(
        //         "tax_id" => $tax_id,
        //         "delivery_place" => 1,
        //         "address_id" => 0,
        //     );
        //     $url = C("crmDomain");
        //     post_curl(sprintf("%s%s",rtrim($url,"/"),"/open/checkoutCompanyInfo"),$data);
        // }catch (\Exception $e){
        // }


        try{
            //创建发票关联的公司信息和推动erp  推送一体化
            $data = array(
                "tax_id" => $tax_id,
            );
            $url = C("crmDomain");
            post_curl(sprintf("%s%s",rtrim($url,"/"),"/open/addCompanyInfoByIchunt"),$data);
        }catch (\Exception $e){
        }

        $com_name = I('tax_title', '');
        if (in_array($inv_type, array(3,4)) && in_array($org_id,[1,2])) {
            $data = array(
                'com_name' => $com_name,
                'com_telphone' => I('company_phone', ''),
            );
            try {

                $this->setDefaultCompany($user_id, $data);
                // 添加抬头到发票公司管理表
//                20220624 ruanxiagnlan 产品规定新增发票不需要同步到公司表 下单审核再同步
                $InvoiceCompanyModel = D('InvoiceCompany');
//                $addCompanyDdata = I('request.');
//                $addCompanyDdata["verify_status"] = $tax_info["verify_status"]; //验证状态，-1-失败，1-成功'
//                $company_type = I("company_type",1);
//                $addCompanyDdata["company_type"] = $company_type ? $company_type : 1;
//                $com_id = $InvoiceCompanyModel->addCompany($addCompanyDdata);

                $msg_text = '新增发票：添加抬头（'.$com_name.'）到发票公司管理表，用户ID：'.$user_id;

                if ($operator_id) {
                    $msg_text .= '，后台操作人ID：'.$operator_id;
                }
                dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.invoice'), $msg_text);
            } catch (\Exception $e) {
                dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.invoice'), sprintf("前台用户新增发票同步公司失败 公司名称:%s",$com_name));
            }
        }

        //如果是无需审核的发票则需要同步到erp 账期需要
        $this->syncTaxInfoToErp($tax_id);

//        $CrmModel->updateComName($user_id, $com_name);

        $operator = $operator_id ? $operator_id : $user_id; // 操作人
        $operator_type = $operator_id ? 2 : 1; // 操作类型

        D('Home/UserActionLog')->addLog($user_id, $operator, $operator_type, '新增发票，ID：'.$tax_id);

        return $this->apiReturn(0, '新增成功');
    }

    protected function syncTaxInfoToErp($tax_id){
        try{
            $url = C("crmDomain");
            $reqData["tax_id"] = $tax_id;
            $res = get_curl(sprintf("%s%s",$url,"/sync/ichuntapi/syncTaxInfoToErpQueue"),$reqData,["Content-Type"=>"application/json"]);
            $res = json_decode($res,true);
        }catch (ConnectException $e){
            return $this->apiReturn(23040,sprintf("请求天眼查超时，请联系客服"));
        }
    }


    /**
     * 编辑用户发票
     * @return [type] [description]
     */
    public function update()
    {
        $user_id         = I('user_id') ? I('user_id') : cookie('uid');
        $tax_id          = I('tax_id', 0, 'intval');
        $uc_id           = I('uc_id') ? I('uc_id') : cookie('ucid');
        $org_id          = I('org_id', 1, 'intval');
        $operator_id     = I('operator_id', 0);
        $consignee_phone = I('consignee_phone');
        $intl_code       = I('intl_code');
        $consignee_phone = get_inte_mobile($consignee_phone, $intl_code);
        $is_self         = I('is_self', '', 'trim');

        $tax_info["inv_area"] = I('inv_area', 1);;//国内公司
        if($org_id == 3 && $tax_info["inv_area"] == 2){
            $is_self = 1;
        }
        if (!empty($consignee_phone) && !is_mobile($consignee_phone) && !$is_self) { // 自营不需要判断手机号
            return $this->apiReturn(11006, '手机号码格式错误，请重新输入', '');
        }

        $TaxinfoModel = D('Taxinfo');
        $info = $TaxinfoModel->getUserInfoByUcid(0, $tax_id,$uc_id);
        if (empty($info)) return $this->apiReturn(23022, '未找到相关发票');

        $tax_info = I('request.');
        //公司名称不让修改
        isset($tax_info['tax_title']) && $tax_info['tax_title'] = trim($tax_info['tax_title']);
        unset($tax_info['operator_id']);
        unset($tax_info['com_nature']);

        //验证税号和公司合法性
        $com_name = I('tax_title', '', 'trim');
        $com_tax_registration = I('tax_no', '', 'trim');
        $comCert = $this->checkTheInnerEyeSevice([
            "com_name"=>$com_name,
            "com_tax_registration"=>$com_tax_registration,
            "company_type"=>1,
        ]);

        $tax_info["verify_status"] = 0;
        $inv_type_temp = I("inv_type",2,"intval");//2普通发票 3增值税专用发票 4增值税普通发票
        if(in_array($inv_type_temp, array(3,4))){
            $tax_info["verify_status"] = -1;
            //重新验证公司和发票是否一致
            if($comCert["err_code"] == 0 && $comCert["data"]["com_name"] == $com_name && $comCert["data"]["com_tax_registration"] == $com_tax_registration){
                $tax_info["verify_status"] = 1;
            }
        }

        $tax_info["update_time"] = time();
        if (!$TaxinfoModel->create($tax_info)) {
            return $this->apiReturn(23031, $TaxinfoModel->getError());
        }
        $is_default = $TaxinfoModel->is_default;
        $res = $TaxinfoModel->save();
        if ($res === false) {
            return $this->apiReturn(23033, '发票编辑失败');
        }
        //设置默认发票
        if ($is_default == 1) {
            $res = $TaxinfoModel->setUserDefaultV2(0, $tax_id,$uc_id);
            if ($res === false) {
                return $this->apiReturn(23030, '设置默认发票失败');
            }
        }

        $com_name = I('tax_title', '', 'trim');
        if (in_array($info['inv_type'], array(3,4))  && in_array($org_id,[1,2])) {
            $data = array(
                'com_name' => $com_name,
                'com_telphone' => I('company_phone', ''),
            );
            try {
                $this->setDefaultCompany($user_id, $data);

                // 更新抬头到发票公司管理表
//                $InvoiceCompanyModel = D('InvoiceCompany');
//                $addCompanyDdata = I('request.');
//                $addCompanyDdata["verify_status"] = $tax_info["verify_status"]; //验证状态，-1-失败，1-成功'
//                    $invoice_com_name = $InvoiceCompanyModel->where(['tax_id' => $tax_id])->getField('com_name');
//                $InvoiceCompanyModel->addCompany($addCompanyDdata);
                $msg_text = '编辑发票：修改抬头（'.$com_name.'）到发票公司管理表，用户ID：'.$user_id;
//                    if ($invoice_com_name && $invoice_com_name != $com_name) {
//                        $InvoiceCompanyModel->addCompany(I('request.'));
//                        $msg_text = '编辑发票：修改抬头（'.$invoice_com_name.' => '.$com_name.'）到发票公司管理表，用户ID：'.$user_id;
//                    } else {
//                        $InvoiceCompanyModel->addCompany(I('request.'));
//                        $msg_text = '编辑发票：添加抬头（'.$com_name.'）到发票公司管理表，用户ID：'.$user_id;
//                    }

                if ($operator_id) $msg_text .= '，后台操作人ID：'.$operator_id;

                dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.invoice'), $msg_text);
            } catch (\Exception $e) {
            }
        }

        // 若编辑最新的发票，则修改CRM公司名称
//        $last_tax_id = $TaxinfoModel->where(['user_id' => $user_id])->order('tax_id desc')->getField('tax_id');
//        if ($last_tax_id == $tax_id) {
//            $CrmModel = D('Crm/Crm');
//            $CrmModel->updateComName($user_id, $com_name);
//        }

        // 编辑前发票信息
        $log_inv_type           = $info['inv_type'] ? C('INVOICE_TYPE')[$info['inv_type']] : '';
        $log_tax_title          = $info['tax_title'];
        $log_consignee          = $info['consignee'];
        $log_consignee_phone    = $info['intl_code'] && $info['intl_code'] != '0086' ? $info['intl_code'].'+'.$info['consignee_phone'] : $info['consignee_phone'];
        $log_consignee_province = !empty($info['consignee_province']) ? get_province($info['consignee_province']) : '';
        $log_consignee_city     = !empty($info['consignee_city']) ? get_city($info['consignee_city']) : '';
        $log_consignee_district = !empty($info['log_consignee_district']) ? get_district($info['log_consignee_district']) : '';
        $log_consignee_address  = $info['consignee_address'];
        $log_is_default         = $info['is_default'] ? '是' : '否';

        $log = '修改前：发票类型：'.$log_inv_type.'，发票抬头：'.$log_tax_title.'，收票人：'.$log_consignee.'，联系电话：'.$log_consignee_phone.'，地址：'.$log_consignee_province.$log_consignee_city.$log_consignee_district.$log_consignee_address.'，是否默认：'.$log_is_default;

        $operator = $operator_id ?: $user_id; // 操作人
        $operator_type = $operator_id ? 2 : 1; // 操作类型

        D('Home/UserActionLog')->addLog($user_id, $operator, $operator_type, '编辑发票，ID：'.$tax_id.'，'.$log);

        return $this->apiReturn(0, '编辑成功');
    }

    /**
     * 设置默认发票
     */
    public function setDefault()
    {
        $tax_id      = I('tax_id', 0, 'intval');
//        $org_id      = I('org_id', 1, 'intval');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');

        $operator_id = I('operator_id', 0);

        $TaxinfoModel = D('Taxinfo');
        $res = $TaxinfoModel->setUserDefaultV2(0, $tax_id,$uc_id);
        if ($res === false) {
            return $this->apiReturn(23030, '设置默认发票失败');
        }
        $user_id     = $this->getUidByAdmin();
        $operator = $operator_id ?: $user_id; // 操作人
        $operator_type = $operator_id ? 2 : 1; // 操作类型

        D('Home/UserActionLog')->addLog($user_id, $operator, $operator_type, '设置默认发票，ID：'.$tax_id);

        return $this->apiReturn(0, '设置成功');
    }

    /**
     * 删除用户发票
     * @return [type] [description]
     */
    public function delete()
    {
        $tax_id      = I('tax_id', 0, 'intval');
//        $org_id      = I('org_id', 1, 'intval');//组织id 1猎芯 2华云
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $user_id     = $this->getUidByAdmin();
        //$uc_id     = $this->getUcidByAdmin();
        $operator_id = I('operator_id', 0);

        $TaxinfoModel = D('Taxinfo');
        $res = $TaxinfoModel->deleteUserByOrgid(0, $tax_id,$uc_id);
        if (!$res) {
            return $this->apiReturn(23034, '删除用户发票失败');
        }

        $operator = $operator_id ?: $user_id; // 操作人
        $operator_type = $operator_id ? 2 : 1; // 操作类型

        D('Home/UserActionLog')->addLog($user_id, $operator, $operator_type, '删除发票，ID：'.$tax_id);

        $data["tax_id"] = $tax_id;
        post_curl(CRM_V2_DOMAIN . '/open/taxinfo/deleteTaxinAddLog', $data);
        return $this->apiReturn(0, '删除成功');
    }

    /*
     * 根据公司名称查找公司相关的信息
     */
    public function getInvoiceComInfo(){
        try{
            $com_name = I('com_name', '', 'trim');
            if(!$com_name){
                return $this->apiReturn(23039, '');
            }
            $info = D("InvoiceCompany")->where(["com_name"=>$com_name,"status"=>1])->field(["com_name","com_addr","com_tel","com_tax_registration","com_bank","com_bank_num"])->find();
//            dump($info);
            if($info){
                return $this->apiReturn(0, '',$info);
            }
            return $this->apiReturn(23039, '');
        }catch(\Exception $e){
            return $this->apiReturn(23039,"", $info);
        }
    }

    /**
     * Notes:天眼查
     * User: sl
     * Date: 2022/6/24 14:58
     */
    public function checkTheInnerEye(){
        $com_name = I('com_name', '', 'trim');
        $com_tax_registration = I('com_tax_registration', '', 'trim');
        $company_type = I('company_type', 1, 'intval');

        $res = $this->checkTheInnerEyeSevice([
            "com_name"=>$com_name,
            "com_tax_registration"=>$com_tax_registration,
            "company_type"=>$company_type,
        ]);
        return $this->apiReturn($res["err_code"],$res["err_msg"],$res["data"]);
    }

    public function checkTheInnerEyeSevice($data){
        $com_name = $data["com_name"];
        $com_tax_registration = $data["com_tax_registration"];
        $company_type = $data["company_type"];
        if(!$com_name && !$com_tax_registration){
            return $this->apiReturn(23040, '公司名称或者发票必须填写一个');
        }

        $reqData = [];
        $company_type = $company_type ? $company_type : 1;
        $reqData["region"] = $company_type;//海外 大陆
        $reqData["company_type"] = 1;//客户  供应商
        if($com_name){
            $reqData["company_name"]=$com_name;
        }
        if($com_tax_registration){
            $reqData["company_tax_no"]=$com_tax_registration;
        }
        try{
            $url = C("eyeCertUrl");
            $res = get_curl($url,$reqData,["Content-Type"=>"application/json"]);
            $res = json_decode($res,true);
        }catch (ConnectException $e){
            return $this->apiReturn(23040,sprintf("请求天眼查超时，请联系客服"));
        }

        $ythComInfo =  $res["data"]["companyInfo"];
        $tycList =  $res["data"]["tycList"];
        $returnData = [
            "com_name" => "",
            "com_addr" => "",
            "com_tel" => "",
            "com_tax_registration" => "",
            "com_nature" => "",//初鉴性质
            "com_industry" => "",//公司行业
        ];
        if(empty($ythComInfo) && empty($tycList)){
            return $this->apiReturn(23040,"天眼查没有找到该公司信息");
        }

        if($ythComInfo){
            $returnData["com_tax_registration"] = $ythComInfo ? $ythComInfo["company_tax_no"] : "";
            $returnData["com_name"] = $ythComInfo["company_name"];
            $returnData["company_nature"] = !empty($ythComInfo["company_nature"]) ? $ythComInfo["company_nature"] : "";
            $returnData["com_industry"] = !empty($ythComInfo["com_industry"]) ? $ythComInfo["com_industry"] : "";
        }


        if($tycList && !empty($tycList["company_info_list"][0])){
            $returnData["com_name"] = $tycList["company_info_list"][0]["com_name"];
            $returnData["com_addr"] = $tycList["company_info_list"][0]["com_address"];
            $tycInfo =!empty($tycList["company_info_list"][0]["tyc_info"]) ? $tycList["company_info_list"][0]["tyc_info"] : [];
            $returnData["com_tel"] = $tycInfo ? $tycInfo["phone_number"] : "";
            if(!$returnData["com_tax_registration"]){
                $returnData["com_tax_registration"] = $tycInfo ? $tycInfo["tax_number"] : "";
            }
        }
        return $this->apiReturn(0,"ok",$returnData);
    }

    /**
     * 企业认证
     * @param mixed $user_id
     * @param mixed $data
     * @return void
     */
    public function addComAuth(){

        $user_id = I('user_id') ? I('user_id') : cookie('uid');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $org_id = I('org_id') ? I('org_id') : 1;


        $com_name = I('com_name', '', 'trim');
        $com_nature = I('com_nature', '', 'intval');
        $com_industry = I('com_industry', '', 'trim');
        $company_size = I('company_size', '', 'intval');
        $contact_person = I('contact_person', '', 'trim');
        $contact_phone = I('contact_phone', '', 'trim');

        $data["uc_id"] = $uc_id?:0;
        $data["user_id"] = $user_id?:0;
        $data["org_id"] = $org_id;
        $data["com_name"] = $com_name;
        $data["com_nature"] = $com_nature;
        $data["com_industry"] = $com_industry;
        $data["company_size"] = $company_size;
        $data["contact_person"] = $contact_person;
        $data["contact_phone"] = $contact_phone;
        $crm = D("Crm/Crm");
        $info = $crm->getCompanyAuth([
            "user_id"=>$data["user_id"],
            "uc_id"=>$data["uc_id"],
            "com_name"=>$data["com_name"],
        ]);
        if(empty($info)){
            $url = C("crmDomain");
            $res = post_curl(sprintf("%s%s",rtrim($url,"/"),"/openApi/addComAuthFromIchunt"),$data);
            $res = json_decode($res,true);
            if(!empty($res) && $res["code"] != 0){
                return $this->apiReturn($res["code"],$res["msg"]);
            }
        }else{
            unset($data["uc_id"]);
            unset($data["user_id"]);
            unset($data["org_id"]);
            D("Crm/Crm")->updateCompanyAuth([
                "id"=>$info["id"],
            ],$data);
        }

        return $this->apiReturn(0,"企业认证设置ok!");
    }

    public function getComAuth(){

        $user_id = I('user_id') ? I('user_id') : cookie('uid');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $org_id = I('org_id') ? I('org_id') : 1;
        $info = D("Crm/Crm")->getCompanyAuth([
            "user_id"=>$user_id,
            "uc_id"=>$uc_id
        ]);
        if(!empty($info)){
            $info["com_industry_format"] = $info["com_nature"];
            $info["com_nature_format"] = !empty(\Crm\Model\CrmModel::$COM_NATURE[$info["com_nature"]]) ?\Crm\Model\CrmModel::$COM_NATURE[$info["com_nature"]] : "";
            $info["company_size_format"] = !empty(\Crm\Model\CrmModel::$COMPANY_SCALES[$info["company_size"]]) ?\Crm\Model\CrmModel::$COMPANY_SCALES[$info["company_size"]] : "";
        }
        return $this->apiReturn(0,"",$info?:null);
    }


    public function upload_user_img(){
        if ($_FILES['file']['error'] > 0) {
            return $this->apiReturn(23043, '文件上传出错: ' . $_FILES['file']['error']);
        }
        $user_id = I('user_id') ? I('user_id') : cookie('uid');

        $fileName = $_FILES['file']['name'];
        $fileTmpName = $_FILES['file']['tmp_name'];
        $fileSize = $_FILES['file']['size'];
        $fileType = $_FILES['file']['type'];

        try{
            // 使用 CURLFile 处理文件
            $url = trim(C("uploda_file_url"),"/"). '/uploadFile?sys_type=6';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            $postFields = [
                // 添加文件字段
                'file' => new \CURLFile(realpath($fileTmpName)),
                // 添加文件名
                'file_name' => $fileName,
            ];
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            // 检查是否有错误
            if (curl_errno($ch)) {
                echo 'Curl error: '. curl_error($ch);
            }
            curl_close($ch);
           if(empty($response)){
                return $this->apiReturn(23043, '文件上传出错');
           }
           $res = json_decode($response,true);
           if($res["code"]!= 0 || empty($res["data"]["oss_file_url"])){
                return $this->apiReturn($res["code"],$res["msg"]);
           }

           D("Home/UserInfo")->saveUserInfo($user_id,["user_head"=>$res["data"]["oss_file_url"]]);

           $userInfo = S_user($user_id);
           if($userInfo && $res["data"]["oss_file_url"]){
                $userInfo["user_head"] = $res["data"]["oss_file_url"];
                S_user($user_id,$userInfo);
           }

           return $this->apiReturn(0,"ok",$res["data"]);
        }catch (\Exception $e){
            return $this->apiReturn(23043, '文件上传出错: '. $e->getMessage());
        }

    }

    public function addCompanyInfo(){
        $user_id = I('user_id') ? I('user_id') : cookie('uid');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $tax_id = I('tax_id',0);
        $com_name = I('com_name',null);
        $company_nature = I('company_nature',null);
        $com_industry  = I('com_industry',null);
        $product_use_classone_sn  = I('product_use_classone_sn',null);
        $product_use_classtwo_sn  = I('product_use_classtwo_sn',null);
        $protocol_url  = I('protocol_url',null);
        $business_license_src  = I('business_license_src',null);


        $data["user_id"] = $user_id;
        $data["uc_id"] = $uc_id;
        $data["tax_id"] = $tax_id;
        $data["company_nature"] = $company_nature;
        $data["com_industry"] = $com_industry;
        $data["product_use_classone_sn"] = $product_use_classone_sn != -1 ? $product_use_classone_sn+1 : -1;
        $data["product_use_classtwo_sn"] = $product_use_classtwo_sn;
        $data["protocol_url"] = $protocol_url;
        $data["business_license_src"] = $business_license_src;
        $data["tax_title"] = $com_name;

        if($tax_id){
            $TaxinfoModel = D('Invoice/Taxinfo');
            $taxinfo = $TaxinfoModel->where(['tax_id' => (int)$tax_id])->find();
            if($taxinfo && $taxinfo["inv_area"] == 1){
                $checkTheInnerEyeData = [
                    "com_name"=>$taxinfo["tax_title"],
                    "com_tax_registration"=>$taxinfo["tax_no"]?:"",
                    "company_type"=>1,
                ];
                //国内公司，该公司是否存在于天眼查中，若不存在，不允许下单，提示语：该公司信息不存在于天眼查中，请检查后重试
                $eseInfo = A("Invoice/Invoice")->checkTheInnerEyeSevice($checkTheInnerEyeData);
                if($eseInfo["err_code"] != 0){
                    return $this->apiReturn(100, "该公司信息不存在于天眼查中，请检查后重试");
                }
            }

        }



        $url = C("crmDomain");
        $res = post_curl(sprintf("%s%s",rtrim($url,"/"),"/open/updateTaxinfoFromichunt"),$data);
        $res = json_decode($res,true);
        if(!empty($res) && $res["code"] != 0){
            return $this->apiReturn(1, $res["msg"]);
        }

        return $this->apiReturn(0, "补充资料成功");

    }

    public function uploadfile(){
        if ($_FILES['file']['error'] > 0) {
            return $this->apiReturn(23043, '文件上传出错: ' . $_FILES['file']['error']);
        }
        $user_id = I('user_id') ? I('user_id') : cookie('uid');

        $fileName = $_FILES['file']['name'];
        $fileTmpName = $_FILES['file']['tmp_name'];
        $fileSize = $_FILES['file']['size'];
        $fileType = $_FILES['file']['type'];
        try{
            // 使用 CURLFile 处理文件
            $url = trim(C("uploda_file_url"),"/"). '/uploadFile?sys_type=6';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            $postFields = [
               'file' => new \CURLFile(
                realpath($fileTmpName),
                $fileType,  // 强制PDF的MIME类型
                $fileName           // 传递原始文件名
            ),
            // 3. 恢复文件名参数传递
            'file_name' => $fileName,
            ];
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            // 检查是否有错误
            if (curl_errno($ch)) {
                echo 'Curl error: '. curl_error($ch);
            }
            curl_close($ch);
           if(empty($response)){
                return $this->apiReturn(23044, '文件上传出错');
           }
           $res = json_decode($response,true);
           if($res["code"]!= 0 || empty($res["data"]["oss_file_url"])){
                return $this->apiReturn($res["code"],$res["msg"]);
           }

           if(empty($res["data"]["oss_file_url"])){
                return $this->apiReturn(1,"上传失败");
           }

           return $this->apiReturn(0,"上传成功",$res["data"]["oss_file_url"]);
        }catch (\Exception $e){
            return $this->apiReturn(23045, '文件上传出错: '. $e->getMessage());
        }
    }

    public function getOrderInvoiceList(){
        $user_id = I('user_id') ? I('user_id') : cookie('uid');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $order_sn = I('order_sn',"","trim");//销售订单号
        $invoce_title = I('invoce_title',"","trim");
        $create_time_start = I('create_time_start',"","trim");
        $create_time_end = I('create_time_end',"","trim");
        $invoice_time_start = I('invoice_time_start',"","trim");
        $invoice_time_end = I('invoice_time_end',"","trim");


        if($invoice_time_start && empty($invoice_time_end)){
            $invoice_time_end = $invoice_time_start;
        }

        if($invoice_time_end && empty($invoice_time_start)){
            $invoice_time_start = $invoice_time_end;
        }


        if($create_time_start && empty($create_time_end)){
            $create_time_end = $create_time_start;
        }

        if($create_time_end && empty($create_time_start)){
            $create_time_start = $create_time_end;
        }

        // $invoice_time_start 2020-05-20   
        // $invoice_time_end 2020-05-27
        if($invoice_time_start && $invoice_time_end){
            try {
                $start = new \DateTime($invoice_time_start);
                $end = new \DateTime($invoice_time_end);
                $interval = $start->diff($end);
                if ($interval->y > 0 || $interval->m >= 1) {
                    // 时间差超过一个月
                    // 可在此添加相应逻辑
                    // 例如返回错误信息
                    return $this->apiReturn(23041, '开票时间差不能超过一个月');
                }
            } catch (\Exception $e) {
                // 处理无效的时间格式
                return $this->apiReturn(23042, '开票时间格式无效');
            }
        }

        if($create_time_start && $create_time_end){
            try {
                $start = new \DateTime($create_time_start);
                $end = new \DateTime($create_time_end);
                $interval = $start->diff($end);
                if ($interval->y > 0 || $interval->m >= 1) {
                    // 时间差超过一个月
                    // 可在此添加相应逻辑
                    // 例如返回错误信息
                    return $this->apiReturn(23041, '下单时间差不能超过一个月');
                }
            } catch (\Exception $e) {
                // 处理无效的时间格式
                return $this->apiReturn(23042, '下单时间格式无效');
            }
        }

        //获取用户的所有订单关联的发票
        $order = D("Order/Order");
        $order_extend = D("Order/OrderExtend");
        $TaxinfoModel = D('Taxinfo');
        $crm = D('Crm/Company');
        $tax_list_arr = $order->getUserOrderCustomerIdArr($user_id,$uc_id,$order_sn,$create_time_start,$create_time_end);
        $erp_sn = "";
        if(!empty($order_sn)){
            foreach($tax_list_arr as $val){
                if($val["order_sn"] = $order_sn){
                    $erp_sn = $order_extend->where(["order_id"=>$val["order_id"]])->getField("erp_sn");
                }
            }
        }

        $tax_id_arr = array_column($tax_list_arr, 'customer_id');
        if(empty($tax_id_arr)){
            return $this->apiReturn(0,"ok",[]);
        }

        $user_order_sn_list = array_column($tax_list_arr, 'order_sn');


        $taxinfoList = $TaxinfoModel->getUserTaxinfoByIdarr($user_id,$uc_id,$tax_id_arr,$invoce_title);
        if(empty($tax_id_arr)){
            return $this->apiReturn(0,"ok",[]);
        }
        $com_name_arr = array_column($taxinfoList, 'tax_title');
        $company_list = $crm->where(["com_name"=>["in",implode(",",$com_name_arr)]])->select();

        if(empty($company_list)){
            return $this->apiReturn(0,"ok",[]);
        }

        $customer_list = [];
        foreach($company_list as $val){
            $customer_list_tmp["customer_erp_number"] = (string)$val["erp_com_sn"];
            $customer_list_tmp["customer"] = $val["com_name"];
            $customer_list[] = $customer_list_tmp;
        }

        $erp_params = [];
        $erp_params["customer_list"] = $customer_list;

        $erp_params["erp_order_sn"] = $erp_sn;
        $erp_params["invoice_time_start"] = $invoice_time_start;
        $erp_params["invoice_time_end"] = $invoice_time_end;
        \Think\Log::write("开票信息erp_params:".json_encode($erp_params,JSON_UNESCAPED_UNICODE),"debug");




        try {
            //https://www.tapd.cn/20210831/markdown_wikis/show/#1120210831001000908
            //生产环境地址：http://119.23.228.186:6868/ormrpc/services/WSCreditPeriodFacade?wsdl
            $orderErp = new \SoapClient(ERP_DOMAIN.'/ormrpc/services/WSCreditPeriodFacade?wsdl');
            $erp_res = $orderErp->getArInvoice($erp_params);
            $erp_res = json_decode($erp_res, true);

            if(empty($erp_res) || empty($erp_res["code"]) || $erp_res["code"] != 0 || empty($return_data)){
                return $this->apiReturn(0,'success', []);
            }
            $erp_return_data = $erp_res["data"] ?:[];
            //$user_order_sn_list
            $return_data = [];
            foreach($erp_return_data as $erpReturnDataItem){
                if(in_array($erpReturnDataItem["order_sn"],$user_order_sn_list)){
                    $erpReturnDataItemTmp = [];
                    $erpReturnDataItemTmp["status"] = $erpReturnDataItem["status"];
                    $erpReturnDataItemTmp["order_sn"] = $erpReturnDataItem["order_sn"];
                    $erpReturnDataItemTmp["invoce_type"] = $erpReturnDataItem["invoce_type"];
                    $erpReturnDataItemTmp["invoce_amount"] = $erpReturnDataItem["invoce_amount"];
                    $erpReturnDataItemTmp["invoce_title"] = $erpReturnDataItem["customer"];
                    $erpReturnDataItemTmp["create_time"] = $erpReturnDataItem["create_time"];
                    $erpReturnDataItemTmp["invoce_img_url"] = $erpReturnDataItem["invoce_img_url"];
                    $return_data[] = $erpReturnDataItemTmp;
                }
            }

            return $return_data?:[];
        } catch (\Exception $e) {
            // p($e->getMessage());exit;
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn(0,'success', []);
        }

        // { "customer_list": [ { "customer_erp_number": "0004220", "customer": "北京新东方迅程网络科技有限公司" } ], "erp_order_sn": "erp44444", "invoice_time_start": "2025-01-01", "invoice_time_end": "2025-01-01" }
        // echo json_encode($erp_params);exit;



        // $list = [];
        // $data["status"] = "已开票";
        // $data["order_sn"] = "sn123456";
        // $data["invoce_type"] = "增值税专用发票";
        // $data["invoce_amount"] = "￥98745";
        // $data["invoce_title"] = "深圳猎芯科技";
        // $data["create_time"] = "2025-01-01-08 15:23:20";
        // $data["invoce_img_url"] = "http://file.liexindev.net/download/557680600102sk91k1r9la";
        // $list[] = $data;
        // return $this->apiReturn(0,"ok",$list?:[]);




    }

    public function testCurl(){
        //个人中心登录,有返回用户信息的话,就直接写入猎芯
        $data = [
            'mobile' => "15600631049",
            'email' => '',
            'password' => "123456",
            'init_org_id' => 1,
        ];
        $res = post_curl(trim(C("UCENTER_URL"), "/") . "/api/auth/login", $data);
        $res = json_decode($res, true);
        $res = $res['data'];
        return $this->apiReturn(0,'success', $res);
    }


}
