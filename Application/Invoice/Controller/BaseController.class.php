<?php
namespace Invoice\Controller;

class BaseController extends \Common\Controller\BaseController
{

    protected function getOrderStatus($order_status, $pay_status)
    {
        $data['order_status'] = $order_status;
        $data['pay_status'] = $pay_status;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN.'/order/transstatus', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    protected function setDefaultCompany($user_id, $data)
    {
        $data['user_id'] = $user_id;
        $data['com_name'] = $data['com_name'];
        if (!empty($data['com_telphone'])) {
            $data['com_telphone'] = $data['com_telphone'];
        }
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN.'/user/updatecompanyinfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;

    }

}