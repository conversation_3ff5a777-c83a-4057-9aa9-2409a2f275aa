<?php
namespace Monitor\Controller;

use Think\Controller;

class IndexController extends Controller
{
    /*
     * 监控统计数据的脚本是否正常统计
     */
    public function index(){
        $res = Sredis('hhs_monitor_warm..getBrandByCat', '', C('REDIS_LIST.search'));
        $now = date('Ymd');
        if($res<$now){
            $check = array();
            $check['touser'] = json_encode(array(1=>'13713025362'));
            $check['data'] = array();
            $check['pf'] = 1;
            $check['keyword'] = 'hhs_monitor_sum_data';
            $check['is_ignore'] = true;
            $check = array_merge($check, authkey());
            $res = post_curl(API_DOMAIN.'/msg/sendMessageByAuto', $check);
        }
    }
    /*
     * 通用脚本，监控所以自动跑的脚本是否正常运行，每分钟执行一次
     */
    public function runCheck(){
        return
        //读取要监控的标记配置，其配置包括标记名(key)、运行频率（秒为单位frequency）、对比时间差（秒time_difference）、消息内容(msg)、发送提醒次数（send_num）
        $user = C('RUN_MONITOR_OBJECT');
        $now_time = time();
        if(count($user)>0){
            foreach($user as $k=>$v){
                $res = Sredis('hhs_monitor_warm..'.$v['key'], '', C('REDIS_LIST.search'));
                $res_num = Sredis('hhs_monitor_warm_num..'.$v['key'], '', C('REDIS_LIST.search'));
                if(($now_time-$res)>$v['time_difference']){
                    if($res_num<=3){
                        $this->sendMsg($v['msg']);
                        Sredis('hhs_monitor_warm_num..'.$v['key'],$res_num+1 , C('REDIS_LIST.search'));
                    }
                        
                }else{
                    Sredis('hhs_monitor_warm_num..'.$v['key'], 0, C('REDIS_LIST.search'));
                }
            }
        }
        
    }
    
    /*
     * 发送消息
     */
    public function sendMsg($msg){
        $check = array();
        $check['touser'] = json_encode(C('MONITOR_PHONE'));
        $check['data'] = json_encode(array('msg'=>$msg),JSON_UNESCAPED_UNICODE);
        $check['pf'] = 1;
        $check['keyword'] = 'RUN_MONITOR_OBJECT';
        $check['is_ignore'] = true;
        $check = array_merge($check, authkey());
        $res = post_curl(API_DOMAIN.'/msg/sendMessageByAuto', $check);
    }
    
}
