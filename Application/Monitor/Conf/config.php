<?php
/**
 * 过滤文件，发布请手动补充修改内容
 */
return array(
    //短信通知电话
    'MONITOR_PHONE'=>array(
        18300070879,
    ),
    //读取要监控的标记配置，其配置包括标记名(key)、运行频率（秒为单位frequency）、对比时间差（秒time_difference）、消息内容(msg)
    'RUN_MONITOR_OBJECT'=>array(
        //array('key'=>'getBrandByCat','frequency'=>86400,'time_difference'=>86800,'msg'=>'分类脚本没有按时执行，请留意'),
        array('key'=>'jd_update_queue_publish','frequency'=>0,'time_difference'=>3600,'msg'=>'京东更新数据推送队列异常停止，请及时检查'),
    ),
);