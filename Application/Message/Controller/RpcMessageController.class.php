<?php
namespace Message\Controller;
use Think\Controller;
use Thrift\Exception\TException;
use Thrift\Protocol\TBinaryProtocol;
use Thrift\Transport\TBufferedTransport;
use Thrift\Transport\THttpClient;
use Thrift\Transport\TPhpStream;
use Thrift\TMultiplexedProcessor;
use Thrift\Protocol\TMultiplexedProtocol;
use Message\Services\MessageApiActionServie;
use Message\Rpc\Message\MessageClient;
use Message\Rpc\Message\MessageProcessor;

class RpcMessageController extends  \Common\Controller\BaseController
{
    public function _initialize(){
//        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), [])) {
//            // 检查登录
//            if (in_array(strtolower(ACTION_NAME), ['local','index'])) {//弱校验
//                $verify_mode = false;
//            } else {//强校验
//                $verify_mode = true;
//            }
//            $res = $this->checkLogin($verify_mode);
//            if ($res['err_code'] != 0) {
//                return $this->apiReturn($res['err_code'], $res['err_msg']);
//            }
//        }
//        parent::_initialize();
    }

    public function message_rpc()
    {
        try {
            ob_end_clean();
            header("Content-type: application/x-thrift; charset=utf-8");
            if (php_sapi_name() == 'cli') {
                echo "\r\n";
                exit();
            }
            $handler = new MessageApiActionServie();
            $processor = new MessageProcessor($handler);
            $transport = new TBufferedTransport(new TPhpStream(TPhpStream::MODE_R | TPhpStream::MODE_W));
            $protocol = new TBinaryProtocol($transport, true, true);
            $transport->open();
            $processor->process($protocol, $protocol);
            $transport->close();
        } catch (TException $tx) {
            \Think\Log::write($tx->getMessage());
        } catch(\Exception $e){
            \Think\Log::write($e->getMessage());
        }
    }


    public function local()
    {
        try {
            ini_set('memory_limit', '1024M');
            $socket = new THttpClient("szapi.ichunt.com", 80, '/rpcmsg/message_rpc');
            $transport = new TBufferedTransport($socket, 1024, 1024);
            $protocol = new TBinaryProtocol($transport);
            $client = new MessageClient($protocol);
            $transport->open();
            $result = $client->MessageApiAction('message api');
            print_r($result);
            $transport->close();
        } catch (TException $tx) {
            print_r($tx->getMessage());
        }

    }





}