<?php

namespace Message\Controller;

class OpenAIController
{
    public function send()
    {

        //校验是否超过次数
        //比如每个ip一天最多请求50次
        $redis = fs_redis_init();
        $ip = get_client_ip();
        $key = 'AIBot_' . $ip . '_Count';
        if ($redis->exists($key)) {
            $count = $redis->get($key);
            if ($count >= 50) {
                http_response_code(400);
                echo json_encode(['error' => 'Call Limit Exceeded']);
                exit;
            }
            $redis->incr($key);
        } else {
            $redis->set($key, 1);
            $redis->expire($key, 86400);
        }

        @ini_set('zlib.output_compression', '0');
// 设置 CORS 头
        header('Access-Control-Allow-Origin: *'); // 允许所有来源（生产环境可限制）
        header('Access-Control-Allow-Methods: POST, OPTIONS'); // 允许的请求方法
        header('Access-Control-Allow-Headers: Content-Type'); // 允许的请求头
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');

        ob_end_clean(); // 丢弃任何遗留的输出缓冲
        ob_implicit_flush(true); // 启

        // 处理 OPTIONS 预检请求
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }

        // 禁用输出缓冲
        if (ob_get_level()) ob_end_clean();
        ini_set('output_buffering', '0');

        //$DEEPSEEK_API_KEY = '***********************************';
        //欧派云
        //$DEEPSEEK_API_KEY = 'sk_VPif0x4q3z3SGNp-MZ8fYS_imGG2Km5Z2SBg54qxIHE';
        //抖音
        $DEEPSEEK_API_KEY = '2a7df976-2b85-4ffd-ba4c-7ba5a58d7318';
        //$DEEPSEEK_API_KEY = 'b4a028fe-347c-4943-900c-95c02289859f';
        //腾讯
        //$DEEPSEEK_API_KEY = 'sk-s4Oqb5H1HdniDrgPP7MXNzJ1JXKCVWLsKw3XMEbrb34ryAje';

        //$DEEPSEEK_API_ENDPOINT = 'https://api.deepseek.com/v1/chat/completions'; // 替换为 Deepseek 的 API Endpoint
        //欧派云
        //$DEEPSEEK_API_ENDPOINT = 'https://api.ppinfra.com/v3/openai/chat/completions';
        //抖音
        $DEEPSEEK_API_ENDPOINT = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
        //腾讯
        //$DEEPSEEK_API_ENDPOINT = 'https://api.lkeap.cloud.tencent.com/v1/chat/completions';

// 获取前端发送的请求数据
        $request_body = file_get_contents('php://input');
        $request_data = json_decode($request_body, true);
        $message = I('message');
        $goodsName = I('goods_name');
// 验证请求数据
        if (!$message) {
            http_response_code(400);
            echo json_encode(['error' => 'Message is required']);
            exit;
        }

// 构建 Deepseek API 请求
        $system_prompt = "你是一个精通电子元器件的ai助手,你只能回答和电子元器件相关的问题,如果问了不相关的问题,请直接回答自己只能回复这个型号: " . $goodsName . "，电子元器件的相关内容.最后,全程用中文回答.切记只回答对话,不要对其他类型的做响应,比如生成图片,生成音频等";
        $deepseek_data = [
            'model' => 'deepseek-v3-250324', //抖音
            //'model' => 'deepseek-v3', // 腾讯
            'messages' => [
                ['role' => 'system', 'content' => $system_prompt],
                [
                    'role' => 'user',
                    'content' => $message
                ]
            ],
            'stream' => true, //  开启流式传输
            'temperature' => 1
            // ... 其他参数，根据 Deepseek API 文档添加
        ];

// 发送 Deepseek API 请求
        $ch = curl_init($DEEPSEEK_API_ENDPOINT);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($deepseek_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $DEEPSEEK_API_KEY,
        ]);

// 配置 curl 流式输出
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($curl, $data) {
            echo $data; // 直接输出 DeepSeek 的 SSE 数据
            ob_flush();
            flush();
            return strlen($data);
        });

// 执行请求
        $response = curl_exec($ch);

// 处理错误
        if (curl_errno($ch)) {
            echo "data: " . json_encode(['error' => curl_error($ch)]) . "\n\n";
            flush();
        }

        curl_close($ch);

    }
}
