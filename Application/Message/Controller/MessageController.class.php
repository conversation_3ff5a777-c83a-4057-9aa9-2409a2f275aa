<?php

namespace Message\Controller;

use Message\Controller\BaseController;
use Think\Exception;
use Thrift\Exception\TException;
use Thrift\Protocol\TBinaryProtocol;
use Thrift\Transport\TBufferedTransport;
use Thrift\Transport\THttpClient;
use Thrift\Transport\TPhpStream;
use Thrift\Protocol\TMultiplexedProtocol;

class MessageController extends BaseController
{
    const USER_ORG_ID_LIEXIN  = 1;//猎芯组织
    const USER_ORG_ID_HUAYUN  = 3;//华云组织

    const  HUAYUN_PF = 31;//华云组织pf值
    public function _initialize()
    {
        parent::_initialize();
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), array('send', 'sendmessagebyhandle', 'adddatabyqueue', 'handlemessagelistsbycrontab', 'changedata', 'sendmessagebyauto', 'onqueuetotest', 'crontabjob', 'test',
            'crontabjobmass', 'longsendmessagebyauto', 'msgconsumesec', 'dinnerpush', 'dingalert', 'sendopenfalconmsg'))) {
            // 检查登录
            if (in_array(strtolower(ACTION_NAME), array('allnum', 'getmessagelistsbyuserid', 'onqueuetotest', 'changemessagestatus', 'sendopenfalconmsg'))) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
        \LogReport::set();
    }

    // 消息2.0定时任务消费
    public function msgConsumeSec()
    {
        header('content-type:text/html;charset=utf-8');
        set_time_limit(0);
        $RBMQModel = D('Common/Rbmq');

        $MsgLogModel = D('MsgLog');
        $msgQueueKey = '';

        // 3个渠道同时触发
        $msg_channel_total = range(1, 3);
        foreach ($msg_channel_total as $key => $value) {
            switch (strval($value)) {
                case '1':
                    $msgQueueKey = C('MSG_QUEUE_KEY');
                    break;
                case '2':
                    $msgQueueKey = C('MSG_QUEUE_SEC_KEY');
                    break;
                case '3':
                    $msgQueueKey = C('MSG_QUEUE_THD_KEY');
                    break;
                default:
                    break;
            }
            $limit = C('MSG_SEND_NUM');
            while ($limit-- > 0) {
                $data = $RBMQModel->connect('RBMQ_MSG_CONFIG')->queue($msgQueueKey)->pull(false);
                if ($data !== false) {
                    $data = json_decode($data, true);
                    // 判断消息是否过期
                    $now_time = time();
                    if ($data['rbmq']['expire_time'] <= $now_time && $data['rbmq']['error_num']) { // 表明该消息已经过期
                        // 插入mysql ，状态表明已过期
                        $MsgLogModel->addMsgLogByStatus($data, '-3');
                    } else {
                        $res = $MsgLogModel->createMsgLogSec($data, $RBMQModel);
                    }
                    $RBMQModel->ack();
                } else {
                    if (strval($value) === '3' && strval($limit) === '0') {
                        echo "end";
                        exit();
                    }
                }
            }
        }
    }

    // 针对后台群发，出队，插入mysql日志列表
    public function crontabjobMass()
    {
        G('begin');
        $this->addDataByQueue(); // 后台手动群发入队
        G('end');
        $time = G('begin', 'end', 6) . 's';
        $data = array(
            'time' => time(),
            'desc' => 'time:' . date("Y/m/d h:i:s") . ' use time:' . $time,
        );
        file_put_contents('./crontabjobmass.json', json_encode($data));
        return $this->apiReturn('0', "success~，执行成功，执行了" . $time . ", 完成时间为" . time(), $time);
    }

    //后台，单条发送
    public function crontabJob()
    {
        G('begin');
        $this->handleMessageListsByCrontab(); // 出队
        G('end');
        $time = G('begin', 'end', 6) . 's';
        $need_time = time();
        $type = $need_time % 5;
        $data = array(
            'time' => time(),
            'desc' => 'time:' . date("Y/m/d h:i:s") . ' use time:' . $time,
            'type' => $type,
        );
        file_put_contents('./crontabjob.json', json_encode($data));
        return $this->apiReturn('0', "success~，执行成功，执行了" . $time . ", 完成时间为" . time(), $time);
    }

    //(不用)
    public function getMsgTplId($title)
    {
        if (strstr($title, '您的采购单')) {
            return 'ldb-purchase';
        } else if (strstr($title, '您的竞标单')) {
            return 'ldb-bidding';
        } else if (strstr($title, '猎芯两周年')) {
            return 'activity-twoyear';
        } else if (strstr($title, '抽奖机会')) {
            return 'activity-lotterychance';
        } else if (strstr($title, '恭喜您获得')) {
            return 'activity-winprize';
        } else if (strstr($title, '97折折扣券一张')) {
            return 'activity-inviter';
        } else if (strstr($title, '98折折扣券一张')) {
            return 'activity-invitee';
        }
    }

    // 会员中心旧数据数据转移（不用）
    public function changeData()
    {
        // 生成新模板
        // 生成新模板渠道
        // 1.猎单宝 您的竞标单 ldb-bidding
        // 2.猎单宝 您的采购单 ldb-purchase
        // 3.猎芯两周年，快来抢十万红包吧 activity-twoyear
        // 4.恭喜您获得 activity-winprize
        // 5.恭喜您获得多一次抽奖机会 activity-lotterychance
        // 6.恭喜您获得97折折扣券一张 activity-inviter
        // 7.恭喜您获得98折新人折扣券一张！ activity-invitee
        $MessageModel = D('Home/Message');
        $MsgTpleModel = D('MsgTpl');
        $MsgLogModel = D('MsgLog');
        $map = array(
            'description' => array('in', 'ldb-bidding,ldb-purchase,activity-twoyear,activity-winprize,activity-lotterychance,activity-inviter,activity-invitee'),
        );
        $msg_tpl_res = $MsgTpleModel->field('tpl_id, description')->where($map)->select();
        $msg_tpl_arr = array();
        foreach ($msg_tpl_res as $key => $value) {
            $msg_tpl_arr[$value['description']] = $value['tpl_id'];
        }
        $res_arr = $MessageModel->field('to_user_id, status, create_time, title, content')->limit(2)->select();
        $pre_count = count($res_arr);
        $count = 0;
        foreach ($res_arr as $key => $value) {
            $str = $this->getMsgTplId($value['title']);
            $tpl_id = $msg_tpl_arr[$str];
            if ($value['status'] == 0) { // 未读
                $addData['status'] = 1;
            } else if ($value['status'] == 1) { // 已读
                $addData['status'] = 2;
            }
            $addData['obj_user'] = $value['to_user_id'];
            $addData['source_type'] = 1;
            $addData['tpl_id'] = $tpl_id;
            $addData['channel_type'] = 1;
            $addData['create_time'] = $value['create_time'];
            $addData['expect_send_time'] = $value['create_time'];
            $addData['actual_send_time'] = $value['create_time'];
            $addData['title'] = $value['title'];
            $addData['content'] = $value['content'];
            $addData['ex_int'] = 1;
            $addData['ex_str'] = 1;
            $res = $MsgLogModel->add($addData);
            if ($res) {
                $count++;
            }
        }
        echo '总共数据条数为' . $pre_count, '，成功转移条数为 ' . $count;
    }

    // 定时任务 出队发送消息
    public function handleMessageListsByCrontab()
    {
        $MsgLogModel = D('MsgLog');
        $MsgLogModel->getHandleLists();
    }

    // 出队列，add存进mysql（仅限于站内信群发）
    public function addDataByQueue()
    {
        set_time_limit(0);
        $MsgTplModel = D('MsgTpl');
        $MsgChannelTplModel = D('MsgChannelTpl');
        $MsgLogModel = D('MsgLog');
        $tpl_id_arr = $MsgTplModel->field('tpl_id, expect_send_time, msg_type')->where(array('obj_user' => 'is_to_all'))->select();
        foreach ($tpl_id_arr as $key => $value) {
            $temp_id = $value['tpl_id'];
            //获取队列长度即这条手动模板消息剩余多少人没有插入数据库mysql
            $len = Q_message($temp_id, '@len');
            if ($len == 0) { // 说明该模板消息群发的会员都已经插入数据库，待发送了
                continue;
            }
            $channel_tpl_map = array(
                'tpl_id' => $temp_id,
                'channel_type' => 1, // 站内信
            );
            $msg_channel_tpl_info = $MsgChannelTplModel->where($channel_tpl_map)->find();
            // 目前暂时只做站内信群发（短信需要更换服务商，邮件也是，微信一个月只能发4次）
            for ($i = 1; $i <= $len; $i++) {
                $user_id = Q_message($temp_id);
                if (!$user_id) {
                    break;
                }
                $msg_type = $value['msg_type']; // 活动类型
                $op_type = $msg_channel_tpl_info['op_type']; // 操作类型
                $url = $msg_channel_tpl_info['url'];
                if ($op_type == 2) {
                    $ex_str = $msg_type . '||' . $url;
                } else if ($op_type == 1) {
                    $ex_str = $msg_type;
                }
                $status = -2;
                if ($value['expect_send_time'] == $_SERVER['REQUEST_TIME']) {
                    $status = 1;
                }
                $addLogData['status'] = $status; // 成功
                $addLogData['obj_user'] = $user_id;
                $addLogData['source_type'] = 2;
                $addLogData['tpl_id'] = $temp_id;
                $addLogData['channel_type'] = 1; // 目前仅支持站内信
                $addLogData['create_time'] = $_SERVER['REQUEST_TIME'];
                $addLogData['expect_send_time'] = $value['expect_send_time'];
                $addLogData['title'] = $msg_channel_tpl_info['title'];
                $addLogData['content'] = $msg_channel_tpl_info['content'];
                $addLogData['wechat_tpl_id'] = '';
                $addLogData['ex_int'] = $op_type;
                $addLogData['ex_str'] = $ex_str;
                $MsgLogModel->startTrans();
                $res = $MsgLogModel->add($addLogData);
                if (!$res) {
                    Q_message($temp_id, $user_id);
                    $MsgLogModel->rollback();
                }
                $MsgLogModel->commit();
            }
        }
    }

    // 会员中心站内信消息列表
    public function getMessageListsByUserId()
    {
        $MsgLogModel = D('UserLetter');
        $user_id = I('user_id') ? I('user_id') : cookie('uid');
        $org_id      = I('org_id', 1, 'intval');//组织id 1猎芯 3华云
        $status = I('status', 0);
        $page = I('p') ? I('p') : 1;
        $_GET['p'] = $page;
        $limit = I("limit", 0, 'intval');

        if ($status) {
            $status = ['in', $status];
        } else {
            $status = ['in', '1,2'];
        }
        $map = array(
            'status' => $status,
            'org_id' => $org_id,
        );

        $count = $MsgLogModel->getMessageCounts($map,$user_id);
        $data = $MsgLogModel->getMessageLists($user_id, $map, $page);
        if ($limit > 0 && $limit <= 100) {
            $pagesize = $limit;
        } else {
            $pagesize = C('DEFAULT_PAGE_LIMIT');
        }

        $page = new \Think\Page($count, $pagesize);
        $page->setConfig('last', '末页');
        $page->setConfig('prev', '<');
        $page->setConfig('next', '>');
        $page->setConfig('theme', '%FIRST% %UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% %HEADER%');
        $show = $page->show_blue();


        $count = $MsgLogModel->getMessageCounts([
            'status' => ['in', '1,2'],
            'org_id' => $org_id,
        ],$user_id);

        //获取未读站内信总数
        $unReadCountMap = array(
            'status' => 1, //含发送成功（未删除）即未读
            'org_id' => $org_id,
        );

        $unReadCount = $MsgLogModel->getMessageCounts($unReadCountMap,$user_id);

//        $allCountMap = [
//            'channel_type' => 1,
//            'status' => ['in', [1, 2]],
//        ];
//        $allCount = $MsgLogModel->getMessageCounts($allCountMap);

        $readCountMap = [
            'status' => 2,
            'org_id' => $org_id,
        ];
        $readCount = $MsgLogModel->getMessageCounts($readCountMap,$user_id);
        switch (I('status')) {
            case 1 :
                $currentCount = $unReadCount;
                break;
            case 2:
                $currentCount = $readCount;
                break;
            default:
                $currentCount = $count;
                break;
        }

        $datas['unreadcount'] = $unReadCount;
        $datas['all_count'] = $count;
        $datas['read_count'] = $readCount;
        $datas['current_count'] = $currentCount;
        $datas['page'] = $show;
        $datas['p'] =  I('p') ? I('p') : 1;
        $datas['list'] = $data;
        return $this->apiReturn(0, 'success', $datas);
    }


    // 会员中心站内信消息列表
    public function getMessageListsByUserId_bak()
    {
        $MsgLogModel = D('MsgLog');
        $user_id = cookie('uid');
        $ucid = cookie('ucid');
        $status = I('status', 0);
        $page = I('p') ? I('p') : 1;
        $_GET['p'] = $page;
        $limit = I("limit", 0, 'intval');
        $type = I("type", 0, "intval");

        if ($status) {
            $status = ['in', $status];
        } else {
            $status = ['in', '1,2'];
        }
        if ($type == 1) {
            $map = array(
                'status' => $status, //含发送成功（未删除）和已读
                'channel_type' => '1',
                'tpl_id' => ["in", '44,45,46,108,101,102,103,109,128,134,135,129,130,139,140,141,142,143,145'],
            );
        } else {
            $map = array(
                'status' => $status, //含发送成功（未删除）和已读
                'channel_type' => '1',
            );
        }
        $count = $MsgLogModel->getMessageCounts($map,$user_id);
        $data = $MsgLogModel->getMessageLists($user_id, $map, $page);
        if ($limit > 0 && $limit <= 100) {
            $pagesize = $limit;
        } else {
            $pagesize = C('DEFAULT_PAGE_LIMIT');
        }

        $page = new \Think\Page($count, $pagesize);
        $page->setConfig('last', '末页');
        $page->setConfig('prev', '<');
        $page->setConfig('next', '>');
        $page->setConfig('theme', '%FIRST% %UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% %HEADER%');
        $show = $page->show_blue();

        //获取未读站内信总数
        $unReadCountMap = array(
            'status' => 1, //含发送成功（未删除）即未读
            'channel_type' => '1',
        );

        $unReadCount = $MsgLogModel->getMessageCounts($unReadCountMap);

//        $allCountMap = [
//            'channel_type' => 1,
//            'status' => ['in', [1, 2]],
//        ];
//        $allCount = $MsgLogModel->getMessageCounts($allCountMap);

        $readCountMap = [
            'status' => 2,
            'channel_type' => 1,
        ];
        $readCount = $MsgLogModel->getMessageCounts($readCountMap);
        switch (I('status')) {
            case 1 :
                $currentCount = $unReadCount;
                break;
            case 2:
                $currentCount = $readCount;
                break;
            default:
                $currentCount = $count;
                break;
        }

        $datas['unreadcount'] = $unReadCount;
        $datas['all_count'] = $count;
        $datas['read_count'] = $readCount;
        $datas['current_count'] = $currentCount;
        $datas['page'] = $show;
        $datas['list'] = $data;
        return $this->apiReturn(0, 'success', $datas);
    }

    // 将消息状态变为已读或删除全部已读消息
    public function changeMessageStatus()
    {
        $status = I('status');
        $msg_id = I('msg_id');
        $org_id      = I('org_id', 1, 'intval');//组织id 1猎芯 3华云

        if (!$status) return $this->apiReturn(120001, '缺少传递参数status');
        if ($status == 1) {
            if (!$msg_id) return $this->apiReturn(120002, '缺少传递参数msg_id');
            $save['status'] = 2; // 更改为已读
            $where['log_id'] = $msg_id;
            $where['org_id'] = $org_id;
        } else if ($status == 2) {
            //删除需要强登录态
//            $res = $this->checkLogin();
//            if ($res['err_code'] != 0) {
//                return $this->apiReturn($res['err_code'], $res['err_msg']);
//            }
            $save['status'] = -4; // 更改为已删除
        } else if ($status == 3) {
            //删除需要强登录态
//            $res = $this->checkLogin();
//            if ($res['err_code'] != 0) {
//                return $this->apiReturn($res['err_code'], $res['err_msg']);
//            }
            $where['status'] = 1;
            $where['org_id'] = $org_id;
            $save['status'] = 2; // 更改为已读
        }

        $MsgLogModel = D('UserLetter');
        $user_id =  I('user_id') ? I('user_id') : cookie('uid');
        if ($status != 3) {
            $where['status'] = $status;
        }
        if ($status != 1) {
            $where['obj_user'] = $user_id;
        }

        if (empty($where['log_id']) && empty($where['obj_user'])) {
            return $this->apiReturn(120001, '缺少必要参数');
        }
        $res = $MsgLogModel->updateMessage($where, $save);
        return $this->apiReturn(0, 'success');
    }

    public function changeMessageStatusByH5()
    {
        $status = I('status', 1, "intval");
        $msg_id = I('msg_id', []);
        $org_id      = I('org_id', 1, 'intval');//组织id 1猎芯 3华云

        if (!$status) return $this->apiReturn(120001, '缺少传递参数status');
        if ($status == 1) {
            if (!$msg_id) return $this->apiReturn(120002, '缺少传递参数msg_id');
            $save['status'] = 2; // 更改为已读
            $where['log_id'] = ["in", $msg_id];
            $where['org_id'] = $org_id;
        } else if ($status == 2) {
            //删除需要强登录态
            $res = $this->checkLogin();
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
            $where['log_id'] = ["in", $msg_id];
            $where['org_id'] = $org_id;
            $save['status'] = -4; // 更改为已删除
        }

        $MsgLogModel = D('UserLetter');
        $user_id = I('user_id') ? I('user_id') : cookie('uid');
//        $where['status'] = $status;
        $where['obj_user'] = $user_id;
        $res = $MsgLogModel->updateMessage($where, $save);
        return $this->apiReturn(0, 'success');
    }
    // 前台自动发送消息（入队mysql）
    /**
     * [sendMessageByAuto description]
     * @return [type] [description]
     * $data = array(
     * 'template_id' => 80,
     * 'touser' => '***********',
     * 'data' => array(
     * 'yyc' => '***********',
     * 'number' => '94543',
     * 'email' => '邮箱email',
     * ),
     * 'channel_type' => 1, // 可选（渠道）
     * 'url' => 'http://www.baidu.com', //微信跳转地址
     * 'wechat_data' => array( //微信数据传送
     * 'first' => array('value'=>'尊敬的用户，您在猎芯网上的订单有了新的进展','color'=>'#173177'),
     * 'accountType' => array('value'=>'accountType账户类型','color'=>'#173177'),
     * 'account' => array('value'=>'account1000元','color'=>'#173177'),
     * 'result' => array('value'=>'result充值成功','color'=>'#173177'),
     * 'remark' => array('value'=>'remark充值成功','color'=>'#173177'),
     * ),
     *
     * );
     */
    /*    public function sendMessageByAuto()
        {
            //json
            $send_data = I('data');
            // $send_data = stripslashes(html_entity_decode($send_data));
            $send_data = html_entity_decode($send_data);

            //json_decode($str,true)的结果为null
            //html_entity_decode() 函数的作用是把 HTML 实体转换为字符。
            //stripslashes() 函数的作用是删除反斜杠。(元素值中间不能有空格和n，必须替换)
            $send_data = json_decode($send_data, true);
            $touser = I('touser');
            $fromuser = I('fromuser');
            $touser = html_entity_decode($touser);
            $touser = json_decode($touser, true);
            $wechat_data = I('wechat_data');
            $wechat_data = html_entity_decode($wechat_data);
            $wechat_data = json_decode($wechat_data, true);
            $keyword = I('keyword');
            $is_oversea = I('is_oversea');
            $template_id = I('template_id');
            $data['is_oversea'] = $is_oversea ? $is_oversea : false; // 是否是海外
            $data['template_id'] = $template_id;
            $data['keyword'] = $keyword;
            $data['channel_type'] = I('channel_type');
            $data['touser'] = $touser;
            $data['data'] = $send_data;
            $data['url'] = I('url');
            $data['wechat_data'] = $wechat_data;
            $data['is_ignore'] = I('is_ignore');
            $data['ex_int'] = I('ex_int') ? I('ex_int') : $send_data['ex_int'];
            $data['ex_str'] = I('ex_str');
            $data['fromuser'] = $fromuser;

            $MsgLogModel = D('Message/MsgLog');

            $res = $MsgLogModel->createMsgLog($data);
            return $this->apiReturn($res[0], $res[1], '');
        }*/
    // 获取关键词对应的过期时间
    public function getMsgExpireTime($keyword = '', $extra = '')
    {
        $time = time();
        $len = strlen($extra);
        if (strval($len) === '32') { // 因为验证码需要md5加密，默认传32位就是验证码类型（或含）
            $time = $time + 60 * 5; // 5分钟
            return $time;
        }
        if ($keyword == 'coupon-expire') { // 优惠券今天到期
            $time = $time + 60 * 60 * 12; //12个小时
            return $time;
        }
        if ($keyword == 'coupon-send') { // 优惠券三天内到期
            $time = $time + 60 * 60 * 24 * 2; //两天
            return $time;
        }
        $time = $time + 60 * 60; // 15分钟 默认
        return $time;
    }

    // 是否发送营销短信
    public function isSendMsg($user, $keyword)
    {
        $user = html_entity_decode($user);
        $user = json_decode($user, true);
        $keywords_tpl = C('KEYWORDS_TPL'); // 订单、优惠券模板

        if ($user == 'INNER_PERSON') return true; // 过滤内部人员

        if ((count($user) == 11 && is_mobile($user)) || is_email($user)) { // 手机或邮箱
            $user_id = S_account($user);
            $user_info = S_user($user_id);
        } else {
            $user_info = S_user($user);
        }

        // is_sendmsg存在且为-1 
        if (isset($user_info['is_sendmsg']) && $user_info['is_sendmsg'] == -1) {
            // 判断是否为订单或优惠券模板
            if (in_array($keyword, $keywords_tpl)) return false;
        }

        return true;
    }

    /**
     * by long
     * sendMessageByAutoTest
     * date ********
     * @return array
     */
    public function sendMessageByAuto()
    {
        \Think\Log::write(print_r($_POST, true));

        $orgId = I('org_id',1);//组织id
        if($orgId == self::USER_ORG_ID_LIEXIN){
            $res = $this->isSendMsg(I('touser'), I('keyword')); // 判断用户是否发送营销短信
            if ($res === false) return $this->apiReturn(0, '成功', '');
        }


        $send_data = I('data');
        $send_data = html_entity_decode($send_data);
        $send_data = json_decode($send_data, true);
        $touser = I('touser');
        $fromuser = I('fromuser');
        $touser = html_entity_decode($touser);
        $touser = json_decode($touser, true);
        $wechat_data = I('wechat_data');
        $wechat_data = html_entity_decode($wechat_data);
        $wechat_data = json_decode($wechat_data, true);
        $keyword = I('keyword');
        $is_oversea = I('is_oversea',false);
        $template_id = I('template_id');
        $data['is_oversea'] = $is_oversea ?: false; // 是否是海外
        $data['template_id'] = $template_id;
        $data['keyword'] = $keyword;
        //渠道类型(1、站内信 2、短信 3、邮箱 4微信通知 5钉钉通知 可多选 用逗号隔开 eg:1,2,3)
        $data['channel_type'] = I('channel_type');
        $data['touser'] = $touser;
        $data['data'] = $send_data;
        $data['url'] = I('url');
        $data['wechat_data'] = $wechat_data;
        $data['is_ignore'] = I('is_ignore',0);
        $data['ex_int'] = I('ex_int') ? I('ex_int') : (isset($send_data['ex_int']) ? $send_data['ex_int'] : 0);
        $data['ex_str'] = I('ex_str');
        $data['pf'] = I('pf', '1'); // 1猎芯 20供应链  30华云
        $data['org_id'] = $orgId;//组织id
        $data['fromuser'] = $fromuser;
        $data['delay'] = I('delay', 0, 'intval');
        switch ($orgId){
            case self::USER_ORG_ID_HUAYUN :
                $data['pf'] = self::HUAYUN_PF;//华云
                break;
        }
        //消息过期时间
        $expire_time = $this->getMsgExpireTime($keyword, $data['ex_str']);
        if ($data['delay'] > 0) {
            $expire_time = $expire_time + $data['delay'];
        }
        $data['rbmq']['expire_time'] = $expire_time;

        if (!$template_id && !$keyword) {
            return $this->apiReturn(120010, '缺失模板');
        }
        if (!$touser) {
            return $this->apiReturn(120008, '接收方不可为空或格式错误');
        }
        //获取发送者名单
        $isok = $this->getTouser($touser, $template_id, $keyword, $data);
        if (!$isok) {
//            sendMsg('msg-send-alarm-rbmq', ['data'=>['msg'=>\GuzzleHttp\json_encode($data)]], 'INNER_PERSON');
            $this->request_by_curl("消息系统:发送消息失败:" . \GuzzleHttp\json_encode($data));
            return $this->apiReturn(-1, '消息发送失败', '');
        }
        return $this->apiReturn(0, '成功', '');
    }



    public function sendMessageByAutoV2($requestParams)
    {
        $org_id = isset($requestParams["org_id"]) ? $requestParams["org_id"] : 1;
        $touser = $requestParams["touser"];
        $keyword = $requestParams["keyword"];
        $dataArr = $requestParams["data"];
        $channel_type = $requestParams["channel_type"];
        $template_id = $requestParams["template_id"];
        $pf = isset($requestParams["pf"]) ? $requestParams["pf"] : 1;
        $is_ignore = $requestParams["is_ignore"];
        $is_oversea = isset($requestParams["is_oversea"]) ? $requestParams["is_oversea"] : false;
        $url = isset($requestParams["url"]) ? $requestParams["url"] : "";
        $ex_int = isset($requestParams["ex_int"]) ? $requestParams["ex_int"] : "";
        $ex_str = isset($requestParams["ex_str"]) ? $requestParams["ex_str"] : "";
        $delay = isset($requestParams["delay"]) ? $requestParams["delay"] : 0;
        $fromuser = isset($requestParams["fromuser"]) ? $requestParams["fromuser"] : "";
        $wechat_data = isset($requestParams["wechat_data"]) ? $requestParams["wechat_data"] : "";

        \Think\Log::write(json_encode($requestParams));

        $orgId = $org_id;//组织id
        if($orgId == self::USER_ORG_ID_LIEXIN){
            $res = $this->isSendMsg($touser, $keyword); // 判断用户是否发送营销短信
            if ($res === false) return $this->apiReturn(0, '成功', '');
        }

        $send_data = $dataArr;
        $send_data = html_entity_decode($send_data);
        $send_data = json_decode($send_data, true);
        $touser = html_entity_decode($touser);
        $touser = json_decode($touser, true);
        $wechat_data = html_entity_decode($wechat_data);
        $wechat_data = json_decode($wechat_data, true);
        $data['is_oversea'] = $is_oversea; // 是否是海外
        $data['keyword'] = $keyword;
        //渠道类型(1、站内信 2、短信 3、邮箱 4微信通知 5钉钉通知 可多选 用逗号隔开 eg:1,2,3)
        $data['channel_type'] = $channel_type;
        $data['touser'] = $touser;
        $data['data'] = $send_data;
        $data['url'] = $url;
        $data['wechat_data'] = $wechat_data;
        $data['is_ignore'] = $is_ignore;
        // $data['ex_int'] = I('ex_int') ? I('ex_int') : (isset($send_data['ex_int']) ? $send_data['ex_int'] : 0);
        // $data['ex_str'] = I('ex_str');
        $data['ex_int'] = $ex_int ? $ex_int :  (isset($send_data['ex_int']) ? $send_data['ex_int'] : 0);
        $data['ex_str'] = $ex_str;
        $data['pf'] = $pf; // 1猎芯 20供应链  30华云
        $data['org_id'] = $orgId;//组织id
        $data['fromuser'] = $fromuser;
        $data['delay'] = intval($delay);
        switch ($orgId){
            case self::USER_ORG_ID_HUAYUN :
                $data['pf'] = self::HUAYUN_PF;//华云
                break;
        }
        //消息过期时间
        $expire_time = $this->getMsgExpireTime($keyword, $data['ex_str']);
        if ($data['delay'] > 0) {
            $expire_time = $expire_time + $data['delay'];
        }
        $data['rbmq']['expire_time'] = $expire_time;

        if (!$template_id && !$keyword) {
            return $this->apiReturn(120010, '缺失模板');
        }
        if (!$touser) {
            return $this->apiReturn(120008, '接收方不可为空或格式错误');
        }
        //获取发送者名单
        $isok = $this->getTouser($touser, $template_id, $keyword, $data);
        if (!$isok) {
//            sendMsg('msg-send-alarm-rbmq', ['data'=>['msg'=>\GuzzleHttp\json_encode($data)]], 'INNER_PERSON');
            $this->request_by_curl("消息系统:发送消息失败:" . \GuzzleHttp\json_encode($data));
            return $this->apiReturn(-1, '消息发送失败', '');
        }
        return $this->apiReturn(0, '成功', '');
    }


    /**
     *
     * @param $touser
     * @param $tpl_id
     * @param $keyword
     * @return bool
     * by long
     * date:********
     *
     *[0] => Array
    (
        [is_oversea] =>
        [template_id] => 286
        [keyword] => change_record_notify_client_mail
        [channel_type] => 2
        [touser] => 132654
        [data] => Array
        (
        [content] => 测试邮件
        [title] => 测试主题
        )

        [url] =>
        [wechat_data] =>
        [is_ignore] => 0
        [ex_int] => 1
        [ex_str] =>
        [pf] => 1
        [fromuser] =>
        [delay] => 0
        [rbmq] => Array
        (
        [expire_time] => 1711445817
        )
    )
     * 消息分开成每一条具体的发送
     */
    protected function getTouser($touser, $tpl_id, $keyword, $data)
    {
        try {
            $touser_arr = $touser;
            if (count($touser) === 1 && !is_array($touser)) {
                    $touser_arr = [$touser];
            }
            if (is_array($touser) && $touser[0] == 'INNER_PERSON') {
                $touser = 'INNER_PERSON';
                $touser_arr = [];
            }
            $MsgTplModel = D('Message/MsgTpl');
            $MsgChannelTplModel = D('Message/MsgChannelTpl');
            if (!$tpl_id) {
                $msg_type_arr = $MsgTplModel->where(array('description' => $keyword))->find();
                if ($msg_type_arr['obj_user'] && $touser == 'INNER_PERSON') {
                    // 内部公司人员 （内推消息）
                    $touser_arr = explode(',', $msg_type_arr['obj_user']); //拆分成数组 (同样适合单个)
                }
                $tpl_id = $msg_type_arr['tpl_id'];
            }
            $msg_channel_tpl_arr = $MsgChannelTplModel->where(array('tpl_id' => $tpl_id))->select();
            $msg_channel_tpl_data = array();
            foreach ($msg_channel_tpl_arr as $value) {
                $msg_channel_tpl_data[$value['channel_type']] = $value;
            }
            //循环发送者*消息类型
            $queue = [];
            $touser_arr = array_unique(array_filter($touser_arr));
            foreach ($touser_arr as $k => $user) {
                $queue[$k] = [];
                $tmp_msg = [];
                //循环渠道
                foreach ($msg_channel_tpl_data as  $value) {
                    if (isset($data['channel_type']) && intval($data['channel_type']) > 0) {
                        if (intval($data['channel_type']) != intval($value['channel_type'])) {
                            continue;
                        }
                    }
                    $arr = $data;
                    $arr['channel_type'] = $value['channel_type'];
                    $arr['template_id'] = $value['tpl_id'];
                    $arr['touser'] = trim(trim($user, '"'), "'");
                    array_push($tmp_msg, $arr);
                }
                $queue[$k] = $tmp_msg;
            }
            if (empty($queue)) throw new  \Exception('消息列表为空');
            //循环发送消息入列 以每个用户为一组  一组有多个类型（站内信 短信 微信 钉钉 邮件） 最多一组4个消息
            //用户*渠道
            if (empty($queue)) throw new  \Exception("没找到匹配的消息类型");
            foreach ($queue as $queueKey => $queueVal) {
                if (!empty($queueVal)) {
                    foreach ($queueVal as $message) {
                        $isok_rpc = $this->onQueueSelect($message, $data['delay']);
                    }
                }
                if (!$isok_rpc) {
                    throw new  \Exception("第{$queueKey}用户以及后面的消息发送失败");
                }
            }
            return true;
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
            $this->request_by_curl("消息系统:发送消息失败:" . $e->getMessage());
            return false;
        } catch (Exception $e) {
            \Think\Log::write($e->getMessage());
            $this->request_by_curl("消息系统:发送消息失败:" . $e->getMessage());
            return false;
        }
    }

    public function request_by_curl($post_string)
    {
        $remote_server = C('MESSAGE_DINGDING');
        $ch = curl_init();
        $data = array('msgtype' => 'text', 'text' => array('content' => $post_string));
        $data_string = json_encode($data);
        curl_setopt($ch, CURLOPT_URL, $remote_server);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json;charset=utf-8'));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 线下环境不用开启curl证书验证, 未调通情况可尝试添加该代码
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }


    //查找数据相关注册登陆的keyword
    public function getLoginOrRegistKeywords()
    {
        try {
            //查找数据相关注册登陆的keyword
            if (S('send_message_find_keywords_123456')) {
                $res = S("send_message_find_keywords_123456");
            } else {
                $res = M()->db("1", "MESSAGE_DB_CONFIG")->query("SELECT b.description FROM lie_msg_classify as a  left join lie_msg_tpl  as b  on a.cls_id = b.cls_id  WHERE 1 and (parent_id = 2 or parent_name='注册登陆')   and b.description is not null and b.description != ''");
                if (!empty($res)) {
                    $keyArr = [];
                    foreach ($res as $item) {
                        $keyArr[] = $item['description'];
                    }
                    $res = $keyArr;
                    S('send_message_find_keywords_123456', $res, ['expire' => 3600 * 12]);
                } else {
                    $res = [];
                    S('send_message_find_keywords_123456', null);
                }
            }
            return $res ? $res : [];
        } catch (\Exception $e) {
            return [];
        }
    }

    //查找营销相关类型的消息的keyword
    public function getYingXiaoKeywords()
    {
        $yingXiaoParentIds = C("YINGXIAO_PARENT_ID");
        if (!$yingXiaoParentIds) return false;
        $whereIn = implode(",", $yingXiaoParentIds);
        try {
            //查找数据相关注册登陆的keyword
            if (S('send_message_find_keywords_789')) {
                $res = S("send_message_find_keywords_789");
            } else {
                $res = M()->db("1", "MESSAGE_DB_CONFIG")->query("SELECT b.description FROM lie_msg_classify as a  left join lie_msg_tpl  as b  on a.cls_id = b.cls_id  WHERE 1 and (parent_id in ({$whereIn}))   and b.description is not null and b.description != ''");
                if (!empty($res)) {
                    $keyArr = [];
                    foreach ($res as $item) {
                        $keyArr[] = $item['description'];
                    }
                    $res = $keyArr;
                    S('send_message_find_keywords_789', $res, ['expire' => 3600 * 12]);
                } else {
                    $res = [];
                    S('send_message_find_keywords_789', null);
                }
            }
            return $res ? $res : [];
        } catch (\Exception $e) {
            return [];
        }
    }


    //判断消息是否为登录或者注册类消息 如果是 则插入登录注册专属队列
    public function onQueueToLoginRegist($data = [])
    {
        try {
            // 入队
            $RbmqModel = D('Common/Rbmq');
            $push_data = array(
                'job' => 'api.message.login.regist',
                'data' => json_encode($data)
            );
            $mq = $RbmqModel->exchange(C("EXCHANGE_NAME_MESSAGE"))->queue(C("QUEUE_MESSAGE_LOGINREGIST"))
                ->exchangeBind(C('EXCHANGE_NAME_MESSAGE'), C('QUEUE_MESSAGE_LOGINREGIST'));
            //        dump($a);
            $bk = $mq->push($push_data, C("QUEUE_MESSAGE_LOGINREGIST"));
            if (!$bk) throw new \Exception('插入队列对失败');
            return true;
        } catch (\Exception $e) {
            throw new \Exception('插入队列对失败');
        }
    }

    //营销相关的消息队列
    public function onQueueToYingXiao($data = [])
    {
        try {
            // 入队
            $RbmqModel = D('Common/Rbmq');
            $push_data = array(
                'job' => 'api.message.yingxiao',
                'data' => json_encode($data)
            );
            $mq = $RbmqModel->exchange(C("EXCHANGE_NAME_MESSAGE"))->queue(C("QUEUE_MESSAGE_YINGXIAO"))
                ->exchangeBind(C('EXCHANGE_NAME_MESSAGE'), C('QUEUE_MESSAGE_YINGXIAO'));
            //        dump($a);
            $bk = $mq->push($push_data, C("QUEUE_MESSAGE_YINGXIAO"));
            if (!$bk) throw new \Exception('插入队列对失败');
            return true;
        } catch (\Exception $e) {
            throw new \Exception('插入队列对失败');
        }
    }

    //普通消息队列
    public function onQueueToNormal($data = [])
    {
        try {
            // 入队
            $RbmqModel = D('Common/Rbmq');
            $push_data = array(
                'job' => 'api.message.all',
                'data' => json_encode($data)
            );
            $mq = $RbmqModel->exchange(C("EXCHANGE_NAME_MESSAGE"))->queue(C("QUEUE_MESSAGE_NORMAL"))
                ->exchangeBind(C('EXCHANGE_NAME_MESSAGE'), C('QUEUE_MESSAGE_NORMAL'));
            //        dump($a);
            $bk = $mq->push($push_data, C("QUEUE_MESSAGE_NORMAL"));
            if (!$bk) throw new \Exception('插入队列对失败');
            return true;
        } catch (\Exception $e) {
            throw new \Exception('插入队列对失败');
        }
    }

    //普通消息队列 专属短信队列
    public function onQueueToNormalDuanXin($data = [])
    {
        try {
            // 入队
            $RbmqModel = D('Common/Rbmq');
            $push_data = array(
                'job' => 'api.message.duanxin',
                'data' => json_encode($data)
            );
            $mq = $RbmqModel->exchange(C("EXCHANGE_NAME_MESSAGE"))->queue(C("QUEUE_MESSAGE_NORMAL_DUANXIN"))
                ->exchangeBind(C('EXCHANGE_NAME_MESSAGE'), C('QUEUE_MESSAGE_NORMAL_DUANXIN'));
            //        dump($a);
            $bk = $mq->push($push_data, C("QUEUE_MESSAGE_NORMAL_DUANXIN"));
            if (!$bk) throw new \Exception('插入队列对失败');
            return true;
        } catch (\Exception $e) {
            throw new \Exception('插入队列对失败');
        }
    }


    /*
     * 消息入队列入口
     * by sunlong
     * date:2019/05/14
     */
    protected function onQueueSelect($data = [], $delay = 0)
    {
//        echo "<pre>";
//        dump($data);
//        echo "</pre>";
        try {
            if ($delay > 0) {
                return $this->delayMessage($data);
            }
            //注册相关的关键字
            //通过模板分类名称 等于 注册登陆
            $keywordsList = $this->getLoginOrRegistKeywords();

            //营销相关的关键字
            $keywordsListYingXiao = $this->getYingXiaoKeywords();

            if ($keywordsList && in_array($data['keyword'], $keywordsList)) {
                //登录注册相关的队列
                return $this->onQueueToLoginRegist($data);
            } else if ($keywordsListYingXiao && in_array($data['keyword'], $keywordsListYingXiao)) {
                //营销类型相关消息单独一个队列
                return $this->onQueueToYingXiao($data);
            } else {
                //普通消息队列
                if ($data['channel_type'] == '2') {
                    //专属短信队列
                    return $this->onQueueToNormalDuanXin($data);
                } else {
                    //普通消息 包括 微信 邮件 钉钉
                    return $this->onQueueToNormal($data);
                }
            }

        } catch (Exception $e) {
            \Think\Log::write($e->getMessage());
            return false;
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
            return false;
        }
    }

    /**
     * by long
     * date:20190112
     * @param $data
     * @param $delay
     */
    protected function onQueue($data = [], $delay = 0)
    {
        //判断消息类型 如果是登录注册的 则更换一个队列
        try {
            if ($delay > 0) {
                return $this->delayMessage($data);
            }
            ini_set('memory_limit', '1024M');
            $rpcserver = C('MESSAGE_QUEUE_RPC');
            $socket = new THttpClient($rpcserver['ip'], $rpcserver['port'], $rpcserver['url']);
            $transport = new TBufferedTransport($socket, 1024, 1024);
            $protocol = new TBinaryProtocol($transport);
            $client = new \Message\Rpc\Event\EventClient($protocol);
            $transport->open();
            $result = $client->emit(json_encode(['job' => 'api.message.all', 'data' => $data]), $delay);
            $transport->close();
            return $result;
        } catch (TException $tx) {
            \Think\Log::write($tx->getMessage());
            return false;
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
            return false;
        }
    }


    /**
     * 延时消息入库
     * 有定时任务去跑
     * @return array
     */
    public function delayMessage($data)
    {
        $DelayMessageModel = D('Message/DelayMessage');
        $Model = M('DelayMessage', "lie_", "MESSAGE_DB_CONFIG");
        $Model->startTrans();
        try {
            $arr = [];
            $arr['queue'] = 'api.message.all';
            $arr['content'] = json_encode($data);
            $arr['create_time'] = time();
            $arr['update_time'] = time();
            $arr['expect_send_time'] = time() + $data['delay'];
            $Model->table("lie_delay_message")->add($arr);
            $Model->commit();
            return true;
        } catch (\Exception $e) {
            $Model->rollback();
            return false;
        }
    }


    // 后台手动发送消息（入队）（所有信）
    public function sendMessageByHandle()
    {
        //1.创建新的模板
        $MsgTplModel = D('MsgTpl');
        $MsgChannelTplModel = D('MsgChannelTpl');
        $MsgLogModel = D('MsgLog');

        $channels = I('channels') ? I('channels') : ''; // 渠道类型(1、站内信 2、短信 3、邮箱 4微信通知 可多选 用逗号隔开 eg:1,2,3)
        $msg_type = I('msg_type') ? I('msg_type') : ''; // 消息类型（1、公告 2、活动 3、新闻）
        $source_type = 2; // 消息来源类型（1、功能性消息 2、营销型消息(后台手动)）
        $creater = I('creater') ? I('creater') : ''; // 创建人
        $obj_user = I('obj_user') ? I('obj_user') : ''; // 目标用户(针对手动发送，存手机号(或邮箱),逗号隔开)
        $description = I('description') ? I('description') : ''; // 消息关键词
        $ex_str = I('ex_str') ? I('ex_str') : ''; // 消息描述（前台展示，加强运营理解使用）

        $create_time = $_SERVER['REQUEST_TIME'];
        $update_time = $_SERVER['REQUEST_TIME'];
        $expect_send_time = I('expect_send_time');
        if ($expect_send_time) {
            if (intval($expect_send_time) < intval($_SERVER['REQUEST_TIME'])) {
                return $this->apiReturn(120004, '预期发送时间不可小于现在时间');
            }
        }
        $expect_send_time = $expect_send_time ? $expect_send_time : intval($_SERVER['REQUEST_TIME']); // 为了防止跟入队有时间差
        $wechat_tpl_id = I('wechat_tpl_id') ? I('wechat_tpl_id') : '';
        $op_type = I('op_type') ? I('op_type') : 0;

        $addData['channels'] = $channels;
        $addData['msg_type'] = $msg_type;
        $addData['source_type'] = $source_type;
        $addData['creater'] = $creater;
        $addData['obj_user'] = $obj_user;
        $addData['description'] = $description;
        $addData['create_time'] = $create_time;
        $addData['update_time'] = $update_time;
        $addData['expect_send_time'] = $expect_send_time;
        $addData['ex_str'] = $ex_str;
        $msg_tpl_id = $MsgTplModel->data($addData)->add();

        if (!$msg_tpl_id) return false;

        //2.创建新的消息渠道模板
        $channelData['tpl_id'] = $msg_tpl_id;
        $channelData['create_time'] = $create_time;
        $channelData['update_time'] = $update_time;
        $needle = ',';
        $channelsArr = explode($needle, $channels); //拆分成数组 (同样适合单个)
        foreach ($channelsArr as $key => $value) {
            $title = 'title_' . $value;
            $content = 'content_' . $value;
            $url = 'url_' . $value;
            $title = I($title);
            $content = I($content);
            $url = I($url);
            if (!$title && !$content) {
                continue;
            }
            $channelData['channel_type'] = $value;
            $channelData['title'] = $title;
            $channelData['content'] = htmlspecialchars_decode($content);

            $channelData['op_type'] = 0;
            $channelData['url'] = '';
            if ($value == 1) { // 站内信
                $channelData['op_type'] = $op_type;
                $channelData['url'] = $url;
            } else if ($value == 4) {
                $channelData['url'] = $url;
            }

            $msg_channel_tpl_id = $MsgChannelTplModel->data($channelData)->add();
            if (!$msg_channel_tpl_id) return false;

            //3.拆分消息渠道（微信，短信，邮箱，站内信，有几个拆几个）
            $msg_log_data['status'] = -2;
            $msg_log_data['source_type'] = $source_type;
            $msg_log_data['tpl_id'] = $msg_tpl_id;
            $msg_log_data['create_time'] = $_SERVER['REQUEST_TIME'];
            $msg_log_data['expect_send_time'] = $expect_send_time;
            $msg_log_data['channel_type'] = $value;
            $msg_log_data['wechat_tpl_id'] = $title;
            $msg_log_data['title'] = $title;
            $msg_log_data['content'] = htmlspecialchars_decode($content);
            $msg_log_data['ex_str'] = '';
            $msg_log_data['ex_int'] = 0;

            if (strval($value) === '1') { // 如果是站内信
                $msg_log_data['ex_int'] = $op_type;
                if ($op_type == 2) {
                    $msg_log_data['ex_str'] = $msg_type . '||' . $url;
                } else if ($op_type == 1) {
                    $msg_log_data['ex_str'] = $msg_type;
                }
            }
            if (strval($value) === '4') {
                $msg_log_data['ex_str'] = $url;
            }

            if ($obj_user != 'is_to_all') { // 群发需要时间，因此不会一次性做日志处理，而是放到队列或redis
                $objUserArr = explode($needle, $obj_user); //拆分成数组 (同样适合单个)
                foreach ($objUserArr as $k => $v) {
                    $msg_log_data['obj_user'] = getRightFormatByChannelType($value, $v); // 目标用户
                    if (!$msg_log_data['obj_user']) continue;
                    $MsgLogModel->data($msg_log_data)->add();
                }
            } else {
                $msg_log_data['obj_user'] = $obj_user;
                $MsgLogModel->data($msg_log_data)->add();

                //入队处理
                $UserMainModel = D('UserMain');
                $maps = array(
                    'status' => array('neq', 10),
                    'password' => array('exp', ' NOT IN ("e10adc3949ba59abbe56e057f20f883e", "a4dd004f38c7a115663c36685600d811")'),
                );
                $user_arr = $UserMainModel->field('user_id')->where($maps)->select();
                $q = Q();
                $q->pipeline();
                foreach ($user_arr as $v) {
                    $user_id = $v['user_id'];
                    Q_message($msg_tpl_id, $user_id);
                }
                $q->exec();
            }
        }
        return $this->apiReturn(0, '提交成功');
    }

    /**
     * 获取所有站内消息数量（展现是否已读的站内信）
     * @return [type] [description]
     */
    public function allNum()
    {
        $MsgLogModel = D('UserLetter');
        $user_id = I('user_id') ? I('user_id') : cookie('uid');
        $org_id      = I('org_id', 1, 'intval');//组织id 1猎芯 3华云
        $where['status'] = array('eq', 1); // 未读
        $where['org_id'] = $org_id;
        $all_num = $MsgLogModel->getMessageCounts($where,$user_id);
        return $this->apiReturn(0, '获取成功', $all_num);
    }

    /**
     * 发送站内信(暂时不用)
     * user_id,type,
     * @return [bool] 是否发送成功
     */
    public function send()
    {
        $user_id = I('user_id');
        $inviter_uid = I('inviter_uid');//邀约人
        $invitee_uid = I('invitee_uid');//被邀约人
        $type = I('type');
        $invitee_info = S_user($invitee_uid);
        $start_time = date("Y.m.d", $invitee_info['create_time']);
        $end_time = date("Y.m.d", intval($invitee_info['create_time']) + 60 * 60 * 24 * 30);
        $info['over_time'] = $start_time . '-' . $end_time;
        if ($type == 1) { //发送给邀约人
            $info['mobile'] = $invitee_info['mobile'];
            $to_user_id = $inviter_uid;
            $desc = 'activity-inviter';
        } else if ($type == 2) {
            $to_user_id = $invitee_uid;
            $desc = 'activity-invitee';
        }
        send_message($to_user_id, $desc, $info);
    }

    // 钉钉晚饭推送
    public function dinnerpush()
    {
        $message = I('message');
        $message = str_replace("\\\\r\\\\n", "
", $message);

        $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件
        \Think\Log::write('晚餐推送消息：' . $message, INFO, '', $path);

        $type = I('type');
        $obj_user = I('obj_user');

        if (!$type) {
            return $this->apiReturn(120007, 'fail', '消息渠道类型不能为空');
        }
        if (!$message) {
            return $this->apiReturn(120006, 'fail', '消息内容不可为空');
        }
        switch (strval($type)) {
            case '1': // 短信
                if (!$obj_user) {
                    return $this->apiReturn(120008, 'fail', '发送目标用户不能为空');
                }
                // $post_data = array(
                //     'mobile' => $obj_user,
                //     'content' => $message,
                //     'type' => 1, // 存类型（1：功能性 ，2：营销性）
                //     'code' => '',
                // );
                // $data = msgCurl($post_data);
                // if (strstr($data, 'OK')) {
                //     $res['errcode'] = 0;
                //     $res['errmsg'] = 'ok';
                // } else {
                //     $res['errcode'] = 120005;
                //     $res['errmsg'] = 'fail';
                // }

                $data['message'] = str_replace(PHP_EOL, ' ', $message);
                $check['touser'] = json_encode($obj_user);
                $check['data'] = json_encode($data, JSON_UNESCAPED_UNICODE); //防止中文乱码
                $check['pf'] = platform();
                $check['keyword'] = 'dinner-order-send';
                $check['is_ignore'] = true;

                $check = array_merge($check, authkey());
                $res = json_decode(post_curl(API_DOMAIN . '/msg/sendMessageByAuto', $check), true);

                if (!$res || $res['err_code'] != 0) return $this->apiReturn(120005, '推送餐单失败', json_encode($check));

                return $this->apiReturn(0, 'success');

                break;
            case '2': // 钉钉
                $res = dinnerpush($message);
                break;
            default:
                # code...
                break;
        }
        if ($res['errcode'] == 0 && $res['errmsg'] == 'ok') {
            return $this->apiReturn(0, 'success');
            die;
        }
        return $this->apiReturn(120005, 'fail', $res['errmsg']);
    }

    // 转发钉钉告警 (供内网项目)
    public function dingAlert()
    {
        $token = I('token', '');
        $text = I('text', '');
        $at = I('at', false);

        if (strpos($at, ',') !== false) {
            $at = explode(',', $at);
        }

        $res = json_decode(dingtalk_robot($token, $text, $at), true);

        return $this->apiReturn($res['errcode'], $res['errmsg']);
    }

    //Open-Falcon发送告警消息
    public function sendOpenFalconMsg()
    {
        $tos = I("tos", "");
        $content = I("content", "");
        $data['pf'] = platform();
        $data['keyword'] = "openfalcon-alert-msg";
        $data['touser'] = $tos;
        $data['data'] = json_encode(["content" => $content], JSON_UNESCAPED_UNICODE);
        $data['is_ignore'] = 1;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/msg/sendMessageByAuto', $data);
        if (!empty($res)) {
            $response = json_decode($res, true);
            if (!isset($response["err_code"]) || $response["err_code"] != 0) {
                echo "fail";
                exit;
            }
        }

        echo "ok";
        exit;


    }

}
