<?php


namespace Message\Controller;

use FontLib\Table\Type\head;
use Think\Controller;

class MsgHproseController extends Controller
{

    public function index(){
        vendor('Hprose.HproseHttpServer');
        $server = new \HproseHttpServer();
        $methods = get_class_methods($this);
        $methods = array_diff($methods, array('__construct', '__call', '_initialize'));
        $server->addMethod('sendWechatMsg',$this);
        $server->handle();
    }

    protected function apiReturn($code = 0, $msg = '', $extend = array())
    {
        $data = array(
            'err_code' => $code,
            'err_msg' => $msg,
            'data' => $extend,
        );
        if ($code != 0) {
            if ($code > 0) {
                unset($data['data']);
            }
            $data['err_code'] = abs($data['err_code']);
        }
        return json_encode($data);
    }

    public function testRpc(){
        $input=file_get_contents("php://input");
        vendor('Hprose.HproseHttpClient');
        $client = new \HproseHttpClient("http://api.liexin.com/Message/MsgHprose/index");
        $bk = $client->sendWechatMsg($input);
        dump($bk);
    }

    public function sendWechatMsg($data){
        try{
            \Think\Log::write('云新发送微信消息-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $dataArray = json_decode($data,true);
            $union_id = isset($dataArray["union_id"]) ? trim($dataArray["union_id"]) : "";
            if(!$union_id) throw new \Exception("参数异常");
            $wechat_oauth = M()->table("lie_wechat_oauth")->where(["union_id"=>$union_id])->field("open_id")->find();
            if(!$wechat_oauth){
                throw new \Exception("该用户暂时未关注公共账号");
            }
            $data["touser"] = $wechat_oauth["open_id"];
            $data["msgtype"] = "text";
            $data['text']["content"] = "你好~~~ 我是测试客服消息";
            $wechatModel = wechatPublic();
            $wechatModel->sendCustomMessage($data);
            return $this->apiReturn('0','成功');
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('10000',$e->getMessage());
        }
    }
}