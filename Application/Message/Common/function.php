<?php
/**
 * 发送站内信
 * @param $to_user_id 用户id（消息接收人）
 * @param $msg_type 信息类型（1-猎单宝，2-系统消息）
 * @param $title 发送标题
 * @param $content 发送内容
 * @param $is_important 是否重要(0-不重要（默认）；1-重要)
 * return bool
 */
function send_message($to_user_id, $desc, $keyword_info)
{
  if (!$to_user_id) {
    return false;
  }
  $MsgTplModel = D('MsgTpl');
  $MsgLogModel = D('MsgLog');
  $MsgChannelTplModel = D('MsgChannelTpl');
  $info = $MsgTplModel->where(array('description' => $desc))->find();
  if (!$info) return false;
  $detail_info = $MsgChannelTplModel->where(array('tpl_id' => $info['tpl_id'], 'channel_type' => 1))->find();
  if (!$detail_info) return false;
  $ex_str = $info['msg_type']; // 站内信消息类型
  if ($detail_info['op_type'] == 2) { // 点击跳转链接
    $ex_str = $info['msg_type'] . '||' . $detail_info['url'];
  }
  $content = getTempletByVariable($keyword_info, $detail_info['content']);
  $addData = array(
    'status' => -2,
    'obj_user' => $to_user_id,
    'title' => $title,
    'content' => $content,
    'source_type' => 1,
    'tpl_id' => $info['tpl_id'],
    'channel_type' => 1,
    'create_time' => $_SERVER['REQUEST_TIME'],
    'expect_send_time' => $_SERVER['REQUEST_TIME'],
    'title' => $detail_info['title'],
    'content' => $content,
    'ex_int' => $detail_info['op_type'],
    'ex_str' => $ex_str,
  );
  if ($MsgLogModel->add($addData) === false) {
      return false;
  }
  return true;

}

