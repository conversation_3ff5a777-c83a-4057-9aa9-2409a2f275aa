<?php
namespace Message\Services;
use Message\Rpc\Message\MessageIf;
use Think\Log;

class MessageApiActionServie implements MessageIf{
    public function MessageApiAction($str){
        $RpcData = json_decode($str,true);
        $action = $RpcData['action'];
        $params = $RpcData['data'];
        \Think\Log::write('rpc请求:'.$action,Log::INFO);
        \Think\Log::write('rpc请求参数:'.$str,Log::INFO);
//        if(isset($params['rbmq']['keyword']) && in_array($params['rbmq']['keyword'],['coupon-send','coupon-expire','coupon-get-new'])){
//            return 1;
//        }
        try{
            if(call_user_func_array([$this,$action],[$params])){
                return 1;
            }
            return 0;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage());
            return 0;
        }

    }


    /**
     * 站内信
     */
    protected function standInsideLetter($data){
        $MsgLogModel = D('MsgLog');
        return $MsgLogModel->sendMessageToUser($data);
    }



    /**
     * 微信
     */
    protected function weChat($data){
        $MsgLogModel = D('MsgLog');
        return $MsgLogModel->sendMessageToUser($data);
    }


    /**
     * 钉钉
     */
    protected function nail($data){
        $MsgLogModel = D('MsgLog');
        return $MsgLogModel->sendMessageToUser($data);
    }


    /**
     * 邮箱
     */
    public function sendEmail($data){
        $MsgLogModel = D('MsgLog');
        return $MsgLogModel->sendMessageToUser($data);
    }


    /**
     * 短信
     */
    protected function sms($data){
        $MsgLogModel = D('MsgLog');
        return $MsgLogModel->sendMessageToUser($data);
    }



    /**
     * 消息处理失败
     */
    protected function failed($data){
        $MsgLogModel = D('MsgLog');
        $MsgLogModel->dealFailMessage($data);
    }




}