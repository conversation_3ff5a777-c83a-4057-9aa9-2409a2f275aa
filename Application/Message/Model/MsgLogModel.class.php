<?php

namespace Message\Model;

use Think\Model;

class MsgLogModel extends Model
{

    const USER_ORG_ID_LIEXIN  = 1;//猎芯组织
    const USER_ORG_ID_HUAYUN  = 3;//华云组织

    //连接撮合数据库
    protected $connection = 'MESSAGE_DB_CONFIG';




    /**
     * @param $data
     * @param $RbmqModel
     * @return array
     * by sunlong
     * 20190118
     */
    public static $channel_type = [
        1 => '站内信',
        2 => '短信',
        3 => '邮箱',
        4 => '微信通知',
        5 => '钉钉',
    ];

    public static $sendMessageAction = [
        1 => 'sendStandInsideLetter',
        2 => 'sendSms',
        3 => 'sendEmail',
        4 => 'SendweChat',
        5 => 'Sendnail',
    ];

    public static $STAND_INSIDE_LETTER = 1;//站内信
    public static $SMS = 2;//短信
    public static $EMAIL = 3;//邮箱
    public static $WEIXIN = 4;//微信通知
    public static $DINGDING = 5;//钉钉


    /*
     * 获取需要发送消息的 接收者
     * 手机号 邮箱 user_id  钉钉  微信
     */
    public function getobjuser($channel_type, $touser, $pf = 1,$orgId=1)
    {
        $obj_user = null;
        switch ($pf) {
            case 1:
            case 20:
            case 99999:
                $obj_user = getRightFormatByChannelType($channel_type, $touser);
                break;
            case 30 :
                $redisPrefixKey = "yunxin_api_";
                $obj_user = getObjUserByMemberLink($channel_type, $touser, $redisPrefixKey);
                break;
            case 31 ://华云
                $obj_user = getUcenterObjUser($channel_type, $touser,$orgId);
                break;
            default:
                $obj_user = getRightFormatByChannelType($channel_type, $touser);
                break;
        }
        return $obj_user;
    }

    /**
     * by long
     * date:20190122
     * 准备发送的数据
     * @param $data
     */
    public function getSendData($data)
    {
        try {
            $tpl_id = $data['template_id'];
            $pf = isset($data['pf']) ? $data['pf'] : "1";
            $keyword = $data['keyword'];
            $touser = $data['touser'];
            $channel_type = $data['channel_type'];
            $orgId = !empty($data['org_id']) ? $data['org_id'] : 1;//组织id
            $url = $data['url'];
            $is_ignore = $data['is_ignore']; // 是否忽略接受方是否是本网站会员 如true，（用于邮箱更换绑定）
            $is_oversea = $data['is_oversea'];//是否是海外
            if ($is_oversea && $keyword) {
                $keyword = $keyword . '-gj';
            }
            $add = [
                'status' => -2,
                'source_type' => 1,
                'create_time' => $_SERVER['REQUEST_TIME'],
                'expect_send_time' => $_SERVER['REQUEST_TIME'],
                'tpl_id' => $tpl_id,
            ];
            if ($data['fromuser']) {
                $add['fromuser'] = $data['fromuser'] ? $data['fromuser'] : '';
            }

            if (!$is_ignore) {
                $obj_user = $this->getobjuser($channel_type, $touser, $pf,$orgId);
                if (!$obj_user) throw  new \Exception('发送失败,没找到接收者信息');
            } else {
                $obj_user = $touser;
            }

            $MsgTplModel = D('Message/MsgTpl');
            $MsgChannelTplModel = D('Message/MsgChannelTpl');
            $msg_channel_tpl_arr = $MsgChannelTplModel->where(['tpl_id' => $tpl_id, 'channel_type' => $channel_type])->find();
            if (!$msg_channel_tpl_arr) throw  new \Exception('发送失败,没找到对应消息模板');
            if ($channel_type == static::$WEIXIN) { // 微信
                $content = $this->createMsgLogSecWeixin($msg_channel_tpl_arr, $data, $keyword);
            } else {
                $content = getTempletByVariable($data['data'], $msg_channel_tpl_arr['content']);
            }

            if (!$content) throw  new \Exception('发送失败,没找到对应消息模板内容');;

            $title = getTempletByVariable($data['data'], $msg_channel_tpl_arr['title']);
            $add['obj_user'] = $obj_user;
            $add['original_obj_user'] = trim($data['touser']);
            $add['title'] = $title;
            $add['content'] = $content;
            $add['wechat_tpl_id'] = '';
            $add['ex_int'] = 0;
            $add['org_id'] =  !empty($data['org_id']) ? $data['org_id'] : 1;
            $add['ex_str'] = "";
            $add['channel_type'] = $channel_type;
            $add['msg_type'] = 1;//站内信消息内省 消息类型（1、公告 2、活动 3、新闻）
            $add['op_type'] = 1;//站内信 点击操作类型（1、打开消息，展示消息内容；2、点击站内信标题，跳转url）
            $add['url'] = "";//站内信 跳转url）


            if ($channel_type == static::$STAND_INSIDE_LETTER) { // 站内信
                $msg_type_arr = $MsgTplModel->where(array('tpl_id' => $tpl_id))->find();
                $add['ex_int'] = $msg_channel_tpl_arr['op_type'];
                if ($msg_channel_tpl_arr['op_type'] == 2) {//站内信跳转
                    $add['ex_str'] = $msg_type_arr['msg_type'] . '||' . $msg_channel_tpl_arr['url'];
                } else if ($msg_channel_tpl_arr['op_type'] == 1) {//打开消息 展示消息内容
                    $add['ex_str'] = $msg_type_arr['msg_type'];
                }
                $add['msg_type'] = $msg_type_arr["msg_type"];
                $add['op_type'] = $msg_channel_tpl_arr['op_type'];
                $add['url'] = $msg_channel_tpl_arr['url'];
            }


            if ($channel_type == static::$SMS) { // 短信
                $add['ex_int'] = $data['ex_int'] ? $data['ex_int'] : '1'; // 短信类型 1 功能性 2 营销性

                $add['ex_str'] = $data['ex_str']; // 短信code
            }

            if ($channel_type == static::$EMAIL) { // 邮箱
                $add['cc'] = $data['ex_str'];
                if(!isValidEmail($add['cc'])){
                    $add['cc'] = "";
                }
            }


            if ($channel_type == static::$WEIXIN) { // 微信
                $url = getTempletByVariable($data['data'], $msg_channel_tpl_arr['url']);
                $add['ex_str'] = $url;
                $add['wechat_tpl_id'] = $msg_channel_tpl_arr['title'];
            }


            if ($channel_type == static::$DINGDING) {//钉钉
//                $contentArr['content'] = $content;
//                $contentJson = json_encode($contentArr);
//                $add['content'] = $contentJson;
            }
            $add['rbmq']['expire_time'] = $data['rbmq']['expire_time'];

            $data['rbmq']['msgSupplierType'] = "";//发短信切换供应商
            if ($channel_type == static::$SMS) {

                //如果是供应链系统pf=20  则更换通道
                if (isset($data['pf']) && $data['pf'] == '20') {
                    $data['rbmq']['msgSupplierType'] = 'MW_GYL';
                }


                if($add['org_id'] == self::USER_ORG_ID_HUAYUN){
                    //华云
                    $data['rbmq']['msgSupplierType'] = 'MW_HUAYUN';
                }
            }


            $add["pf"] = $pf;
            $add['rbmq']['msgSupplierType'] = $data['rbmq']['msgSupplierType'];
            $add['rbmq']['keyword'] = $keyword;
            //开始处理发送逻辑
            return $add;

        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
            return [];
        }
    }


    /**
     * 消息失败后入库
     * @param $data
     * @return array
     */
    public function deleteGetSendData($data)
    {
        try {
            $tpl_id = $data['template_id'];
            $keyword = $data['keyword'];
            $pf = isset($data['pf']) ? $data['pf'] : "1";
            $touser = $data['touser'];
            $channel_type = $data['channel_type'];
            $url = $data['url'];
            $is_ignore = $data['is_ignore']; // 是否忽略接受方是否是本网站会员 如true，（用于邮箱更换绑定）
            $is_oversea = $data['is_oversea'];//是否是海外
            if ($is_oversea && $keyword) {
                $keyword = $keyword . '-gj';
            }
            $add = [
                'status' => -2,
                'source_type' => 1,
                'create_time' => $_SERVER['REQUEST_TIME'],
                'expect_send_time' => $_SERVER['REQUEST_TIME'],
                'tpl_id' => $tpl_id,
            ];
            if ($data['fromuser']) {
                $add['fromuser'] = $data['fromuser'] ? $data['fromuser'] : '';
            }

            $obj_user = $touser;

            $MsgTplModel = D('Message/MsgTpl');
            $MsgChannelTplModel = D('Message/MsgChannelTpl');
            $msg_channel_tpl_arr = $MsgChannelTplModel->where(['tpl_id' => $tpl_id, 'channel_type' => $channel_type])->find();
            if (!$msg_channel_tpl_arr) throw  new \Exception('发送失败,没找到对应消息模板');
            if ($channel_type == static::$WEIXIN) { // 微信
                $content = $this->createMsgLogSecWeixin($msg_channel_tpl_arr, $data, $keyword);
            } else {
                $content = getTempletByVariable($data['data'], $msg_channel_tpl_arr['content']);
            }

            if (!$content) throw  new \Exception('发送失败,没找到对应消息模板内容');;

            $title = getTempletByVariable($data['data'], $msg_channel_tpl_arr['title']);
            $add['obj_user'] = $obj_user;
            $add['original_obj_user'] = trim($data['touser']);
            $add['title'] = $title;
            $add['content'] = $content;
            $add['wechat_tpl_id'] = '';
            $add['ex_int'] = 0;
            $add['ex_str'] = '';
            $add['channel_type'] = $channel_type;
            $add['msg_type'] = 1;//站内信消息内省 消息类型（1、公告 2、活动 3、新闻）
            $add['op_type'] = 1;//站内信 点击操作类型（1、打开消息，展示消息内容；2、点击站内信标题，跳转url）
            $add['url'] = "";//站内信 跳转url）


            if ($channel_type == static::$STAND_INSIDE_LETTER) { // 站内信
                $msg_type_arr = $MsgTplModel->where(array('tpl_id' => $tpl_id))->find();
                $add['ex_int'] = $msg_channel_tpl_arr['op_type'];
                if ($msg_channel_tpl_arr['op_type'] == 2) {
                    $add['ex_str'] = $msg_type_arr['msg_type'] . '||' . $msg_channel_tpl_arr['url'];
                } else if ($msg_channel_tpl_arr['op_type'] == 1) {
                    $add['ex_str'] = $msg_type_arr['msg_type'];
                }
                $add['msg_type'] = $msg_type_arr['msg_type'];//站内信消息类型（1、公告 2、活动 3、新闻）
                $add['op_type'] = $msg_channel_tpl_arr['op_type'];
                $add['url'] = $msg_channel_tpl_arr['url'];
            }


            if ($channel_type == static::$SMS) { // 短信
                $add['ex_int'] = $data['ex_int'] ? $data['ex_int'] : '1'; // 短信类型 1 功能性 2 营销性

                $add['ex_str'] = $data['ex_str']; // 短信code
            }


            if ($channel_type == static::$WEIXIN) { // 微信
                $url = getTempletByVariable($data['data'], $msg_channel_tpl_arr['url']);
                $add['ex_str'] = $url;
                $add['wechat_tpl_id'] = $msg_channel_tpl_arr['title'];
            }


            if ($channel_type == static::$DINGDING) {//钉钉
                $contentArr['content'] = $content;
                $contentJson = json_encode($contentArr);
                $add['content'] = $contentJson;
            }
            $add['rbmq']['expire_time'] = $data['rbmq']['expire_time'];
            $add['rbmq']['msgSupplierType'] = $data['rbmq']['msgSupplierType'];
            $add['rbmq']['keyword'] = $keyword;
            $add["pf"] = $pf;
            //开始处理发送逻辑
            return $add;

        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
            return [];
        }
    }


    /**
     * 发送消息
     * by long
     * date:20190122
     */
    public function sendMessageToUser($data)
    {
        $newData = $this->getSendData($data);
        if (empty($newData)) {
            return false;
        }
        \Think\Log::write(json_encode($newData));
        $queueMessageNormal = C("QUEUE_MESSAGE_NORMAL");
        if($queueMessageNormal == "szliexin_message_tasks" && $data['channel_type'] != self::$STAND_INSIDE_LETTER){
            //本地消息 就不真正去发送消息了 直接写日志
            try{
                $this->addMsgLogByStatus($newData, '1');
                return true;
            }catch (\Exception $e){

            }

        }
        return call_user_func_array([$this, array_get(static::$sendMessageAction, $data['channel_type'])], [$newData]);
    }


    /**
     * 处理失败的消息
     */
    public function dealFailMessage($data)
    {
        $newData = $this->deleteGetSendData($data);
        if (!empty($newData)) {
            \Think\Log::write('--消息处理失败入库---');
            \Think\Log::write(json_encode($newData));
            $this->addMsgLogByStatus($newData, -1);
        }
        {
            return false;
        }
    }




    function addLetter($message_info = [])
    {
//        \Think\Log::write("进入letter日志");
        try {
            $ucId = 0;
            $orgId = !empty($message_info['org_id']) ? $message_info['org_id'] : 0;
            $userId = $message_info['obj_user'];
            $ucenterInfo = S_ucenter(sprintf("%s_%s",intval($userId),intval($orgId)));
            $ucenterInfo = json_decode($ucenterInfo,true);
            if(!empty($ucenterInfo["uc_id"])){
                $ucId = (int)$ucenterInfo["uc_id"];
            }

            $arr = [];
            $arr['obj_user'] = $message_info['obj_user'];
            $arr['msg_type'] = $message_info['msg_type'];
            $arr['title'] = $message_info['title'];
            $arr['content'] = $message_info['content'];
            $arr['op_type'] = $message_info['op_type'];
            $arr['url'] = $message_info['url'];
            $arr['ex_int'] = $message_info['ex_int'];
            $arr['ex_str'] = $message_info['ex_str'];
            $arr['uc_id'] = $ucId;
            $arr['org_id'] = $orgId;
            $arr['create_time'] = time();
            $arr['status'] = 1;
            $arr['pf'] = !empty($message_info["pf"]) ? intval($message_info["pf"]) : 1;
            $bk = D('Message/UserLetter')->add($arr);
            \Think\Log::write(print_r($bk,true));
            return $bk;
        } catch (\Exception $e) {
            \Think\Log::write("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
            \Think\Log::write($e->getMessage());
            return false;
        }
    }

    /**
     * 站内信
     */
    public function sendStandInsideLetter($message_info)
    {
//        $obj_user = $message_info['obj_user'];
//        $content = $message_info['content'];
//        $ex_int = $message_info['ex_int'];
//        $ex_str = $message_info['ex_str'];
//        $title = $message_info['title'];
//        $channel_type = strval($message_info['channel_type']);
//        $source_type = $message_info['source_type'];
        // 判断是否过期
        $now_time = time();
        try {
//            \Think\Log::write(json_encode($message_info));
            if ($message_info['rbmq']['expire_time'] <= $now_time) {
                $this->addMsgLogByStatus($message_info, '-3');
            } else {
//                dump("1111111111111111111111111111111");
                $this->addLetter($message_info);
                $this->addMsgLogByStatus($message_info, '1');
            }
            return true;
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
            return false;
        }

    }

    /**
     * 短信
     */
    public function sendSms($message_info)
    {
        $obj_user = $message_info['obj_user'];
        $content = $message_info['content'];
        $ex_int = $message_info['ex_int'];
        $ex_str = $message_info['ex_str'];
        $title = $message_info['title'];
        $orgId = $message_info['org_id'];
        $channel_type = strval($message_info['channel_type']);
        $source_type = $message_info['source_type'];
        $msgSupplierType = !empty($message_info['rbmq']['msgSupplierType']) ? $message_info['rbmq']['msgSupplierType'] : "MW";
        // 判断是否过期
        $now_time = time();
        try {
            if ($message_info['rbmq']['expire_time'] <= $now_time) {
                $this->addMsgLogByStatus($message_info, '-3');
            } else {
                $post_data = array(
                    'mobile' => $obj_user,
                    'content' => str_replace('¥', '￥', $content),
                    'type' => $ex_int ? $ex_int : 1, // 存类型（1：功能性 ，2：营销性）
                    'code' => $ex_str ? $ex_str : '',
                );
                if (!empty(C('MSG_SUPPLIER_TYPE')) || !empty($msgSupplierType)) {
                    $data = $this->msgSendChannel($post_data, $msgSupplierType);
                } else {
                    $data = msgCurl($post_data);
                }
                if (strstr($data, 'OK')) {
                    $this->addMsgLogByStatus($message_info, '1');
                    $res = true;
                }
            }
            return true;
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
            return false;
        }
        return true;
    }


    /**
     * 邮箱
     */
    public function sendEmail($message_info)
    {
        $obj_user = $message_info['obj_user'];
        $content = $message_info['content'];
        $ex_int = $message_info['ex_int'];
        $exStr = $message_info['ex_str'];
        $cc = !empty($message_info['cc']) ? $message_info['cc'] : "";
        $title = $message_info['title'];
        $channel_type = strval($message_info['channel_type']);
        $source_type = $message_info['source_type'];
        $orgId = $message_info['org_id'];
        // 判断是否过期
        $now_time = time();
        try {
            if ($message_info['rbmq']['expire_time'] <= $now_time) {
                $this->addMsgLogByStatus($message_info, '-3');
            } else {

                switch ($orgId){
                    case self::USER_ORG_ID_LIEXIN:
                        $res = sendMail($obj_user, $title, $content,$cc);
                        break;
                    case self::USER_ORG_ID_HUAYUN:
                        $res = sendHunYunMail($obj_user, $title, $content,$cc);
                        break;
                    default:
                        $res = sendMail($obj_user, $title, $content,$cc);
                        break;
                }

                if ($res) {
                    $this->addMsgLogByStatus($message_info, '1');
                    return true;
                } else {
                    return false;
                }
            }
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
            return false;
        }
        return true;
    }

    /**
     * 微信
     */
    public function SendweChat($message_info)
    {
        $obj_user = $message_info['obj_user'];
        $content = $message_info['content'];
        $ex_int = $message_info['ex_int'];
        $ex_str = $message_info['ex_str'];
        $title = $message_info['title'];
        $channel_type = strval($message_info['channel_type']);
        $source_type = $message_info['source_type'];
        $orgId = $message_info['org_id'];
        // 判断是否过期
        try {
            $now_time = time();
            if ($message_info['rbmq']['expire_time'] <= $now_time) {
                $this->addMsgLogByStatus($message_info, '-3');
            } else {
                $wechat_data = json_decode($content, true);
                \Think\Log::write($wechat_data);
                if (isset($wechat_data['orderProductName']['value']) && $wechat_data['orderProductName']['value']) {
                    $wechat_data['orderProductName']['value'] = str_replace("\/", "/", $wechat_data['orderProductName']['value']);
                }
                $data['data'] = $wechat_data;
                $data['touser'] = $obj_user;
                $data['template_id'] = $message_info['title'];
                $data['url'] = $message_info['ex_str'];
                $data['topcolor'] = '';
                \Think\Log::write(json_encode($data));
                switch ($orgId){
                    case self::USER_ORG_ID_LIEXIN:
                        $resArr = sendWechat($data, true);
                        break;
                    case self::USER_ORG_ID_HUAYUN:
                        $resArr = sendHuaYunWechat($data, true);
                        break;
                    default:
                        $resArr = sendWechat($data, true);
                        break;
                }


                if (empty($resArr['errcode'])) {
                    $this->addMsgLogByStatus($message_info, '1');
                    return true;
                } else {
                    return false;
                }
            }
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
            return false;
        }
        return true;
    }

    /*
     * 钉钉
     */
    public function Sendnail($message_info)
    {
        $obj_user = $message_info['obj_user'];
        $content = $message_info['content'];
//        $ex_int = $message_info['ex_int'];
//        $ex_str = $message_info['ex_str'];
//        $title = $message_info['title'];
//        $channel_type = strval($message_info['channel_type']);
//        $source_type = $message_info['source_type'];
        // 判断是否过期
        $now_time = time();
        try {
            if ($message_info['rbmq']['expire_time'] <= $now_time) {
                $this->addMsgLogByStatus($message_info, '-3');
            } else {
                $data = [];
                $data['touser'] = $obj_user;
                $data['content'] = $content;
//                $resArr = sendDingTalk($data);
                $this->SendComWx($data);
                $this->addMsgLogByStatus($message_info, '1');
                return true;
            }
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
            return false;
        }
        return true;
    }


    public   function SendComWx($data)
    {
        if (!$data['touser']) return false;
        if (!$data['content']) return false;

        try {
            $RbmqModel  = D('Common/Rbmq');

            $queue_message = [];
            $queue_message["__route_key"] = "/open/wx/sendMsg";
            $queue_message['__insert_time'] = time();
            $queue_message['__from'] = "http://api.ichunt.com";
            $queue_message['__type'] = "http";
            $queue_message['__search_key'] = "企业微信推送";
            $queue_message['__trace_id'] = '';
            $queue_message['data'] = $data;
            $queue_message['__uk'] = (string)crc32(json_encode($queue_message) . time());
            $RbmqModel->connect("RBMQ_CONFIG")->queue("lie_queue_crm")->push($queue_message, "lie_queue_crm");
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
            return false;
        }
        return true;
    }


    /**
     * date:20190118
     * by long
     */
    public function createMsgLogSecWeixin($value, $data, $keyword)
    {
        $wechat_content_json = $value['content'];
        $wechat_content_arr = json_decode($wechat_content_json, true);
        $wechat_tpl_head = getTempletByVariable($data['data'], $wechat_content_arr['wechat_tpl_head']); // 微信头部含变量需要替换
        if (strval($keyword) === 'order-remind-pay' || strval($keyword) === 'order-self-first-remind-pay' || strval($keyword) === 'order-self-end-remind-pay') {
            // 仅订单未付款时，需要更改关键词first(坑爹微信) ==
            $data['wechat_data']['frist'] = array(
                'value' => $wechat_tpl_head,
                'color' => '#173177',
            );
        } else {
            $data['wechat_data']['first'] = array(
                'value' => $wechat_tpl_head,
                'color' => '#173177',
            );
        }
        if (strval($keyword) === 'order-self-pay-success' || strval($keyword) === 'order-pay-success') {
            $data['wechat_data']['Remark'] = array(
                'value' => $wechat_content_arr['wechat_tpl_tail'],
                'color' => '#173177',
            );
        } else {
            $data['wechat_data']['remark'] = array(
                'value' => $wechat_content_arr['wechat_tpl_tail'],
                'color' => '#173177',
            );
        }

        $content = json_encode($data['wechat_data']);
        $content = str_replace("\\\\", "\\", $content);
        $content = str_replace("\\/", "\/", $content);
        return $content;
    }

    function addMsgLogByStatus($message_info = array(), $status = '1')
    {
        unset($message_info['rbmq']); // 去掉多余杂质
        $message_info['actual_send_time'] = $_SERVER['REQUEST_TIME'];
        $message_info['status'] = $status;
        $message_info['pf'] = isset($message_info["pf"]) ? intval($message_info["pf"]) : 1;
        $message_info['obj_user'] = isset($message_info['original_obj_user']) ? $message_info['original_obj_user'] : $message_info['obj_user'];
        $this->add($message_info);
    }


//====消息系统改版2.0（引入rbmq消息队列，短信渠道切换自动化）===2018.10.25===end=====================================================================================================================================================
    //短信渠道（统一入口）
    public function msgSendChannel($post_data = array(), $channelType = '',$orgId=1)
    {
        $type = '1';

        if (!$channelType) {
            $channelType = C('MSG_SUPPLIER_TYPE'); // 获取具体短信渠道商
        }
        if (strstr($post_data['mobile'], "+")) { // 国际手机
            $post_data['mobile'] = str_replace('+', '', $post_data['mobile']);
            $type = '2';
        }
        switch (strval($channelType)) {
            case 'MW':
                $data = $this->getMsgByMw($post_data, $type);
                break;
            case 'MW_HUAYUN':
                $data = $this->getMsgByMwHuaYun($post_data, $type);
                break;
            case 'MW_YINGXIAO':
                $data = $this->getMsgByMwYX($post_data, $type);
                break;
            case 'LC':
                $data = $this->getMsgByLC($post_data, $type);
                break;
            case 'MW_GYL':
                $data = $this->getMsgByMwGYL($post_data, $type);
                break;
            default:
                # code...
                break;
        }
        return $data;
    }

    //具体短信渠道（蓝创）
    public function getMsgByLC($post_data = array(), $type = '1')
    {
        if (empty($post_data['mobile']) || empty($post_data['content'])) {
            // 记日志
            // 报警处理
            return false;
        }
        Vendor('MSG/ChuanglanSmsApi');
        $clapi = new \ChuanglanSmsApi($type);
        if (strval($type) === '2') { //海外版 将前面00去掉
            $post_data['mobile'] = ltrim($post_data['mobile'], "00");
            $result = $clapi->sendInternational($post_data['mobile'], $post_data['content']);
        } else {
            //设置您要发送的内容：其中“【】”中括号为运营商签名符号，多签名内容前置添加提交
            $result = $clapi->sendSMS($post_data['mobile'], $post_data['content']);
        }

        if (!is_null(json_decode($result))) {

            $output = json_decode($result, true);

            if (isset($output['code']) && $output['code'] == '0') {
                // echo '发送成功';
            } else {
                $log = $post_data;
                $log['result'] = $output;
                $log['remark'] = 'code 返回不是0';
                $log['channelType'] = '2';
                $log['time'] = date('Y-m-d H:i:s', time());
                $log['channelName'] = 'LC';
                logger($log);
                return false;
                //echo $output['errorMsg'];
            }
        } else {
            $log = $post_data;
            $log['result'] = $result;
            $log['remark'] = 'sendSMS 接口调用失败';
            $log['channelType'] = '2';
            $log['time'] = date('Y-m-d H:i:s', time());
            $log['channelName'] = 'LC';
            logger($log);
            return false;
        }
        saveMsgCodeLog($post_data, $result);
        return 'OK';
    }

    //具体短信渠道（梦网） 签名【猎芯网】
    public function getMsgByMw($post_data = array(), $type = '1')
    {
        if (empty($post_data['mobile']) || empty($post_data['content'])) {
            // 记日志
            // 报警处理
            return false;
        }
        Vendor('MSG/SmsSendConn');
        $MSG_SUPPLIER_TYPE = 'MW';
        $MSG_PWD_INFO = C('MSG_PWD_INFO');

        $supplierChannelIp = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['MSGIP'];
        $num = rand(0, count($supplierChannelIp) - 1);
        $url = $supplierChannelIp[$num];

        $smsSendConn = new \SmsSendConn($url);
        $data = array();
        //设置账号(必填)
        $data['userid'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['USERID'];
        //设置密码（必填.填写明文密码,如:1234567890）
        $data['pwd'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['PWD'];
        if (strval($type) === '2') {
            $data['userid'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['HW']['USERID'];
            $data['pwd'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]["HW"]['PWD'];
        }

        /*
        * 单条发送 接口调用
        */
        // 设置手机号码 此处只能设置一个手机号码(必填)
        $data['mobile'] = $post_data['mobile'];
        //设置发送短信内容(必填)
        $data['content'] = $post_data['content'];
        // 业务类型(可选)
        $data['svrtype'] = '';
        // 设置扩展号(可选)
        $data['exno'] = '';
        //用户自定义流水编号(可选)
        $data['custid'] = '';
        // 自定义扩展数据(可选)
        $data['exdata'] = '';
        try {
            $result = $smsSendConn->singleSend($data);
            if ($result['result'] === 0) {
                // print_r("单条信息发送成功！");
            } else {
                $data['result'] = $result;
                $data['time'] = date('Y-m-d H:i:s', time());
                $data['channelName'] = 'MW';
                logger($data);
                // 报警处理；
                return false;
                // print_r("单条信息发送失败，错误码：" . $result['result']);
            }
        } catch (Exception $e) {
            print_r($e->getMessage());//输出捕获的异常消息，请根据实际情况，添加异常处理代码
        }

        // 记录消息（方便获取验证码（供应链等））
        saveMsgCodeLog($post_data, $result);
        return 'OK';
    }

    //具体短信渠道（梦网） 签名【猎芯网】 营销短信接口
    public function getMsgByMwYX($post_data = array(), $type = '1')
    {
        if (empty($post_data['mobile']) || empty($post_data['content'])) {
            // 记日志
            // 报警处理
            return false;
        }
        Vendor('MSG/SmsSendConn');
        $MSG_SUPPLIER_TYPE = 'MW_YINGXIAO';
        $MSG_PWD_INFO = C('MSG_PWD_INFO');

        $supplierChannelIp = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['MSGIP'];
        $num = rand(0, count($supplierChannelIp) - 1);
        $url = $supplierChannelIp[$num];

        $smsSendConn = new \SmsSendConn($url);
        $data = array();
        //设置账号(必填)
        $data['userid'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['USERID'];
        //设置密码（必填.填写明文密码,如:1234567890）
        $data['pwd'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['PWD'];
        if (strval($type) === '2') {
            $data['userid'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['HW']['USERID'];
            $data['pwd'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]["HW"]['PWD'];
        }

        /*
        * 单条发送 接口调用
        */
        // 设置手机号码 此处只能设置一个手机号码(必填)
        $data['mobile'] = $post_data['mobile'];
        //设置发送短信内容(必填)
        $data['content'] = $post_data['content'];
        // 业务类型(可选)
        $data['svrtype'] = '';
        // 设置扩展号(可选)
        $data['exno'] = '';
        //用户自定义流水编号(可选)
        $data['custid'] = '';
        // 自定义扩展数据(可选)
        $data['exdata'] = '';
        try {
            $result = $smsSendConn->singleSend($data);
            if ($result['result'] === 0) {
                // print_r("单条信息发送成功！");
            } else {
                $data['result'] = $result;
                $data['time'] = date('Y-m-d H:i:s', time());
                $data['channelName'] = 'MW_YINGXIAO';
                logger($data);
                // 报警处理；
                return false;
                // print_r("单条信息发送失败，错误码：" . $result['result']);
            }
        } catch (Exception $e) {
            print_r($e->getMessage());//输出捕获的异常消息，请根据实际情况，添加异常处理代码
        }

        // 记录消息（方便获取验证码（供应链等））
        saveMsgCodeLog($post_data, $result);
        return 'OK';
    }

    //具体短信渠道（梦网） 签名【猎芯】
    public function getMsgByMwGYL($post_data = array(), $type = '1')
    {
        if (empty($post_data['mobile']) || empty($post_data['content'])) {
            // 记日志
            // 报警处理
            return false;
        }
        Vendor('MSG/SmsSendConn');
        $MSG_SUPPLIER_TYPE = 'MW_GYL';
        $MSG_PWD_INFO = C('MSG_PWD_INFO');

        $supplierChannelIp = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['MSGIP'];
        $num = rand(0, count($supplierChannelIp) - 1);
        $url = $supplierChannelIp[$num];

        $smsSendConn = new \SmsSendConn($url);
        $data = array();
        //设置账号(必填)
        $data['userid'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['USERID'];
        //设置密码（必填.填写明文密码,如:1234567890）
        $data['pwd'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['PWD'];
        if (strval($type) === '2') {
            $data['userid'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['HW']['USERID'];
            $data['pwd'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]["HW"]['PWD'];
        }

        /*
        * 单条发送 接口调用
        */
        // 设置手机号码 此处只能设置一个手机号码(必填)
        $data['mobile'] = $post_data['mobile'];
        //设置发送短信内容(必填)
        $data['content'] = $post_data['content'];
        // 业务类型(可选)
        $data['svrtype'] = '';
        // 设置扩展号(可选)
        $data['exno'] = '';
        //用户自定义流水编号(可选)
        $data['custid'] = '';
        // 自定义扩展数据(可选)
        $data['exdata'] = '';
        try {
            $result = $smsSendConn->singleSend($data);
            if ($result['result'] === 0) {
                // print_r("单条信息发送成功！");
            } else {
                $data['result'] = $result;
                $data['time'] = date('Y-m-d H:i:s', time());
                $data['channelName'] = 'MW';
                logger($data);
                // 报警处理；
                return false;
                // print_r("单条信息发送失败，错误码：" . $result['result']);
            }
        } catch (Exception $e) {
            print_r($e->getMessage());//输出捕获的异常消息，请根据实际情况，添加异常处理代码
        }

        // 记录消息（方便获取验证码（供应链等））
        saveMsgCodeLog($post_data, $result);
        return 'OK';
    }


    //具体短信渠道（梦网） 签名【猎芯】
    public function getMsgByMwHuaYun($post_data = array(), $type = '1')
    {
        if (empty($post_data['mobile']) || empty($post_data['content'])) {
            // 记日志
            // 报警处理
            return false;
        }
        Vendor('MSG/SmsSendConn');
        $MSG_SUPPLIER_TYPE = 'MW_HUAYUN';
        $MSG_PWD_INFO = C('MSG_PWD_INFO');

        $supplierChannelIp = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['MSGIP'];
        $num = rand(0, count($supplierChannelIp) - 1);
        $url = $supplierChannelIp[$num];

        $smsSendConn = new \SmsSendConn($url);
        $data = array();
        //设置账号(必填)
        $data['userid'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['USERID'];
        //设置密码（必填.填写明文密码,如:1234567890）
        $data['pwd'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['PWD'];
        if (strval($type) === '2') {
            $data['userid'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]['HW']['USERID'];
            $data['pwd'] = $MSG_PWD_INFO[$MSG_SUPPLIER_TYPE]["HW"]['PWD'];
        }

        /*
        * 单条发送 接口调用
        */
        // 设置手机号码 此处只能设置一个手机号码(必填)
        $data['mobile'] = $post_data['mobile'];
        //设置发送短信内容(必填)
        $data['content'] = $post_data['content'];
        // 业务类型(可选)
        $data['svrtype'] = '';
        // 设置扩展号(可选)
        $data['exno'] = '';
        //用户自定义流水编号(可选)
        $data['custid'] = '';
        // 自定义扩展数据(可选)
        $data['exdata'] = '';
        try {
            $result = $smsSendConn->singleSend($data);
            if ($result['result'] === 0) {
                // print_r("单条信息发送成功！");
            } else {
                $data['result'] = $result;
                $data['time'] = date('Y-m-d H:i:s', time());
                $data['channelName'] = 'MW';
                logger($data);
                // 报警处理；
                return false;
                // print_r("单条信息发送失败，错误码：" . $result['result']);
            }
        } catch (Exception $e) {
            print_r($e->getMessage());//输出捕获的异常消息，请根据实际情况，添加异常处理代码
        }

        // 记录消息（方便获取验证码（供应链等））
        saveMsgCodeLog($post_data, $result);
        return 'OK';
    }
    /**
     * 出队发送消息（通用，站内信，短信，微信）
     * @param [array] 通过msg_log 获取某id的完整信息
     */
    function sendMessageByChannel($message_info = array())
    {
        $log_id = $message_info['log_id'];
        $obj_user = $message_info['obj_user'];
        $content = $message_info['content'];
        $ex_int = $message_info['ex_int'];
        $ex_str = $message_info['ex_str'];
        $title = $message_info['title'];
        $channel_type = strval($message_info['channel_type']);
        $source_type = $message_info['source_type'];

        $savedata['actual_send_time'] = $_SERVER['REQUEST_TIME'];
        $savedata['status'] = 1;

        $res = false;
        switch ($channel_type) {
            case '1': // 站内信
                // 无需再做任何操作，只需更改数据库即可
                $res = true;
                break;
            case '2': // 短信
                $post_data = array(
                    'mobile' => $obj_user,
                    'content' => str_replace('¥', '￥', $content),
                    'type' => $ex_int ? $ex_int : 1, // 存类型（1：功能性 ，2：营销性）
                    'code' => $ex_str ? $ex_str : '',
                );
                if (!empty(C('MSG_SUPPLIER_TYPE'))) {
                    $data = $this->msgSendChannel($post_data);
                } else {
                    $data = msgCurl($post_data);
                }
                if (strstr($data, 'OK')) {
                    if ($source_type == 1) { // 前台来源
                        if (!in_array($obj_user, C('INNER_PERSON'))) {
                            //$count = intval(S_sms($obj_user));
                            //S_sms($obj_user, ++$count);
                        }
                    }
                    $res = true;
                }
                break;
            case '3': // 邮箱
                $res = sendMail($obj_user, $title, $content);
                break;
            case '4': // 微信
                $wechat_data = json_decode($content, true);
                if ($wechat_data['orderProductName']['value']) {
                    $wechat_data['orderProductName']['value'] = str_replace("\/", "/", $wechat_data['orderProductName']['value']);
                }
                $data['data'] = $wechat_data;
                $data['touser'] = $obj_user;
                $data['template_id'] = $message_info['title'];
                $data['url'] = $message_info['ex_str'];
                $data['topcolor'] = '';
                $res = sendWechat($data);
                break;
            case '5': // 钉钉
                $data['touser'] = $obj_user;
                $data['content'] = $content;
                $resArr = sendDingTalk($data);
                if (strval($resArr['result']['ding_open_errcode']) === '0' && $resArr['result']['task_id']) {
                    $res = true;
                } else {
                    $res = false;
                }
                $resJson = json_encode($resArr);
                $savedata['ex_str'] = $resJson;
                break;
            default:
                # code...
                break;
        }
        if (!$res) {
            $savedata['status'] = -1;
        }
        $where['log_id'] = $log_id;
        $where['status'] = -2;
        $this->updateMessage($where, $savedata);
    }

    /**
     * 获取需要处理的发送记录
     */
    public function getHandleLists()
    {
        $MESSAGE_CHANNEL_NUMS = C('MESSAGE_CHANNEL_NUMS');
        foreach ($MESSAGE_CHANNEL_NUMS as $key => $value) {
            $limit = C("MESSAGE_CHANNEL_NUMS.{$key}");
            $map = array(
                'status' => -2,
                'expect_send_time' => array('lt', $_SERVER['REQUEST_TIME']),
                'channel_type' => $key,
            );
            $lists_arr = $this->where($map)->limit($limit)->select();
            foreach ($lists_arr as $key => $value) {
                $this->sendMessageByChannel($value);
            }
        }
    }

    /**
     * 创建消息日志id
     * @param array $data [description]
     * @param int $data ['template_id'] 模板id
     * @param string $data ['touser'] 目标人员（可传user_id,mobile, email）
     * @param int $data ['channel_type'] 只发送某个渠道，可不传
     * @param array $data ['data'] 模板替换变量，以数组形式传递
     * @return [msg_log_id]        [description]
     */
    public function createMsgLog($data)
    {
        $tpl_id = $data['template_id'];
        $keyword = $data['keyword'];
        $touser = $data['touser'];
        $channel_type = $data['channel_type'];
        $url = $data['url'];
        $is_ignore = $data['is_ignore']; // 是否忽略接受方是否是本网站会员 如true，（用于邮箱更换绑定）
        $is_oversea = $data['is_oversea'];
        if ($is_oversea && $keyword) {
            $keyword = $keyword . '-gj';
        }
        if (!$tpl_id && !$keyword) return [120007, '关键词不可为空'];
        if (!$touser) return [120008, '接收方不可为空'];
        $touser_arr = $touser;
        if (intval(count($touser)) === 1) {
            if (!is_array($touser)) {
                $touser_arr = array($touser);
            }
        }
        if ($touser[0] == 'INNER_PERSON') {
            $touser = 'INNER_PERSON';
            $touser_arr = [];
        }

        $MsgTplModel = D('Message/MsgTpl');
        $MsgChannelTplModel = D('Message/MsgChannelTpl');
        if (!$tpl_id) {
            $msg_type_arr = $MsgTplModel->where(array('description' => $keyword))->find();
            $needle = ',';
            if ($msg_type_arr['obj_user'] && $touser == 'INNER_PERSON') {
                // 内部公司人员 （内推消息）
                $touser_arr = explode($needle, $msg_type_arr['obj_user']); //拆分成数组 (同样适合单个)
            }
            $tpl_id = $msg_type_arr['tpl_id'];
        }
        $msg_channel_tpl_arr = $MsgChannelTplModel->where(array('tpl_id' => $tpl_id))->select();

        $msg_channel_tpl_data = array();
        foreach ($msg_channel_tpl_arr as $key => $value) {
            $msg_channel_tpl_data[$value['channel_type']] = $value;
        }

        $add = array(
            'status' => -2,
            'source_type' => 1,
            'create_time' => $_SERVER['REQUEST_TIME'],
            'expect_send_time' => $_SERVER['REQUEST_TIME'],
            'tpl_id' => $tpl_id,
        );
        if ($data['fromuser']) {
            $add['fromuser'] = $data['fromuser'] ? $data['fromuser'] : '';
        }
        $is_flag = false;

        foreach ($touser_arr as $k => $v) {
            $is_flag = true;
            foreach ($msg_channel_tpl_data as $key => $value) {

                if ($channel_type && $channel_type != $key) {
                    continue;
                }
                $obj_user = '';
                if (!$is_ignore) {
                    $obj_user = getRightFormatByChannelType($key, $v);
                    if (!$obj_user) continue;
                } else {
                    $obj_user = $v;
                }
                if ($key == 4) { // 微信
                    $wechat_content_json = $value['content'];
                    $wechat_content_arr = json_decode($wechat_content_json, true);
                    $wechat_tpl_head = getTempletByVariable($data['data'], $wechat_content_arr['wechat_tpl_head']); // 微信头部含变量需要替换
                    if (strval($keyword) === 'order-remind-pay' || strval($keyword) === 'order-self-first-remind-pay' || strval($keyword) === 'order-self-end-remind-pay') { // 仅订单未付款时，需要更改关键词first(坑爹微信) ==
                        $data['wechat_data']['frist'] = array(
                            'value' => $wechat_tpl_head,
                            'color' => '#173177',
                        );
                    } else {
                        $data['wechat_data']['first'] = array(
                            'value' => $wechat_tpl_head,
                            'color' => '#173177',
                        );
                    }
                    if (strval($keyword) === 'order-self-pay-success' || strval($keyword) === 'order-pay-success') {
                        $data['wechat_data']['Remark'] = array(
                            'value' => $wechat_content_arr['wechat_tpl_tail'],
                            'color' => '#173177',
                        );
                    } else {
                        $data['wechat_data']['remark'] = array(
                            'value' => $wechat_content_arr['wechat_tpl_tail'],
                            'color' => '#173177',
                        );
                    }

                    $content = json_encode($data['wechat_data']);
                    $content = str_replace("\\\\", "\\", $content);
                    $content = str_replace("\\/", "\/", $content);

                } else {
                    $content = getTempletByVariable($data['data'], $value['content']);
                }
                if (!$content) continue;
                $title = getTempletByVariable($data['data'], $value['title']);
                $add['obj_user'] = $obj_user;
                $add['title'] = $title;
                $add['content'] = $content;
                $add['wechat_tpl_id'] = '';
                $add['ex_int'] = 0;
                $add['ex_str'] = '';
                $add['channel_type'] = $key;

                if ($key == 1) { // 站内信
                    if (!$msg_type_arr) {
                        $msg_type_arr = $MsgTplModel->where(array('tpl_id' => $tpl_id))->find();
                    }
                    $add['ex_int'] = $value['op_type'];
                    if ($value['op_type'] == 2) {
                        $add['ex_str'] = $msg_type_arr['msg_type'] . '||' . $value['url'];
                    } else if ($value['op_type'] == 1) {
                        $add['ex_str'] = $msg_type_arr['msg_type'];
                    }
                }
                if ($key == 2) { // 短信
                    $add['ex_int'] = $data['ex_int'] ? $data['ex_int'] : '1'; // 短信类型 1 功能性 2 营销性

                    $add['ex_str'] = $data['ex_str']; // 短信code
                }
                if ($key == 4) { // 微信
                    $url = getTempletByVariable($data['data'], $value['url']);
                    $add['ex_str'] = $url;
                    $add['wechat_tpl_id'] = $value['title'];
                }
                if ($key == 5) {
                    $contentArr['content'] = $content;
                    $contentJson = json_encode($contentArr);
                    $add['content'] = $contentJson;
                }
                $this->add($add);
            }
        }
        if (!$is_flag) {
            return ['120009', '接收方格式有问题', $touser_arr];
        }
        return [0, '成功'];
    }

    /**
     * 获取列表
     * @param string $map [description]
     * @param string $limit [description]
     * @param string $order [description]
     * @return [type]        [description]
     */
    public function getMessageLists($user_id, $where = '', $page = '', $order = 'actual_send_time DESC')
    {
        $obj_user = [$user_id];
        $userInfo = S_user($user_id);
        if ($userInfo) {
            array_push($obj_user, $userInfo['mobile']);
        }
        $map = array(
            'obj_user' => ["in", implode(",", $obj_user)],
        );
        is_array($where) && $map = array_merge($map, $where);
        //limit_page($this, $page);
        list($p, $limit) = explode(',', $page);
        empty($p) && $p = 1;
        if (empty($limit) && I("limit")) {
            $limit = I("limit");
            $limit = $limit > 100 ? 100 : $limit;
        }
        empty($limit) && $limit = C('DEFAULT_PAGE_LIMIT');
        $this->page($p, $limit);
        $datas = $this->where($map)
            ->page($p, $limit)
            ->order($order)
            ->field('log_id as msg_id, status, actual_send_time, title, content, ex_int as operate_type, ex_str as operate_url') // 点击操作类型 和 跳转url
            ->select();
        // 如获取站内信列表，需要活动类型（活动，公告等）为了不用连表插库提高执行效率，在插入日志表时ex_str会直接做处理 如 1||http://www.baidu.com
        $needle = '||';
        foreach ($datas as $key => &$value) {
            $value['content'] = htmlspecialchars_decode($value['content']);
            if (strpos($value['operate_url'], strval($needle)) === false) {
                $datas[$key]['msg_type'] = $value['operate_url'];
            } else {
                list($msg_type, $operate_url) = explode($needle, $value['operate_url']);
                $datas[$key]['msg_type'] = $msg_type;
                $datas[$key]['operate_url'] = $operate_url;
            }
            $datas[$key]['actual_send_time_format'] = $value['actual_send_time'] ? date('Y-m-d H:i:s', $value['actual_send_time']) : '';
        }
        return $datas;
    }

    /**
     * 获取单个会员站内信統計数量
     * @param string $map [description]
     * @param string $limit [description]
     * @param string $order [description]
     * @return [type]        [description]
     */
    public function getMessageCounts($where = '', $user_id)
    {
        $obj_user = [$user_id];
        $userInfo = S_user($user_id);
        if ($userInfo) {
            array_push($obj_user, $userInfo['mobile']);
        }
        $map = array(
            'obj_user' => ["in", $obj_user],
        );
        is_array($where) && $map = array_merge($map, $where);
        $datas = $this->where($map)->count();
        return $datas;
    }

    /**
     * 更新数据
     * @param string $map [description]
     * @param string $limit [description]
     * @param string $order [description]
     * @return [type]        [description]
     */
    public function updateMessage($where = array(), $savedata = array())
    {
        $datas = $this->where($where)->data($savedata)->save();
        return $datas;
    }

}