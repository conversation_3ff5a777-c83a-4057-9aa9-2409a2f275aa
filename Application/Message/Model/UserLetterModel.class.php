<?php
namespace Message\Model;

use Think\Model;

class UserLetterModel extends Model
{
    //连接撮合数据库
    protected $connection = 'MESSAGE_DB_CONFIG';








    /**
     * 获取单个会员站内信統計数量
     * @param string $map [description]
     * @param string $limit [description]
     * @param string $order [description]
     * @return [type]        [description]
     */
    public function getMessageCounts($where = '', $user_id)
    {
        $map = array(
            'obj_user' => $user_id,
        );
        is_array($where) && $map = array_merge($map, $where);
        $datas = $this->where($map)->count();
        return $datas;
    }


    /**
     * 获取列表
     * @param string $map [description]
     * @param string $limit [description]
     * @param string $order [description]
     * @return [type]        [description]
     */
    public function getMessageLists($user_id, $where = '', $page = '', $order = 'log_id DESC')
    {
        $userInfo = S_user($user_id);

        $map = array(
            'obj_user' => $user_id,
        );
        is_array($where) && $map = array_merge($map, $where);
        //limit_page($this, $page);
        list($p, $limit) = explode(',', $page);
        empty($p) && $p = 1;
        if (empty($limit) && I("limit")) {
            $limit = I("limit");
            $limit = $limit > 100 ? 100 : $limit;
        }
        empty($limit) && $limit = C('DEFAULT_PAGE_LIMIT');
        $this->page($p, $limit);
        $datas = $this->where($map)
            ->page($p, $limit)
            ->order($order)
            ->field('log_id as msg_id, status, create_time as actual_send_time, title, content, ex_int as operate_type, ex_str as operate_url') // 点击操作类型 和 跳转url
            ->select();
        // 如获取站内信列表，需要活动类型（活动，公告等）为了不用连表插库提高执行效率，在插入日志表时ex_str会直接做处理 如 1||http://www.baidu.com
        $needle = '||';
        foreach ($datas as $key => &$value) {
            $value['content'] = htmlspecialchars_decode($value['content']);
            if (strpos($value['operate_url'], strval($needle)) === false) {
                $datas[$key]['msg_type'] = $value['operate_url'];
            } else {
                list($msg_type, $operate_url) = explode($needle, $value['operate_url']);
                $datas[$key]['msg_type'] = $msg_type;
                $datas[$key]['operate_url'] = $operate_url;
            }
            $datas[$key]['actual_send_time_format'] = $value['actual_send_time'] ? date('Y-m-d H:i:s', $value['actual_send_time']) : '';
        }
        return $datas;
    }


    /**
     * 更新数据
     * @param string $map [description]
     * @param string $limit [description]
     * @param string $order [description]
     * @return [type]        [description]
     */
    public function updateMessage($where = array(), $savedata = array())
    {
        $datas = $this->where($where)->data($savedata)->save();
        return $datas;
    }








}