<?php

namespace Help\Model;

use Think\Model;

class FeedbackModel extends Model
{
    //根据表名table，条件where等获取信息
    public function getData($table, $where, $field = '*', $limit = '', $order = '')
    {
        $datas = $this->table($table)->where($where)->limit($limit)->order($order)->field($field)->select();
        return $datas;
    }

    //插入数据
    public function addData($data)
    {
        $datas = $this->add($data);
        return $datas;
    }

    //获取某天用户反馈条数
    public function getFeedbackCount($time)
    {
        if (S("getFeedbackCount")) {
            return S("getFeedbackCount");
        }
        $dateStr = date('Y-m-d', $time);
        $start_time = strtotime($dateStr);
        $end_time = strtotime($dateStr) + 86400;
        $where = [];
        $where['create_time'] = array('EGT', $start_time);
        $where['create_time'] = array('ELT', $end_time);
        $datas = $this->table('lie_feedback')->where('create_time>=' . $start_time . ' and create_time<' . $end_time)->count("id");
        S("getFeedbackCount", $datas, ["expire" => 3600]);
        return $datas;
    }

    //获取用户的询价列表
    public function getFeedbackList($userId, $status, $page, $limit)
    {
        //询价类型
        $typeInquirePrice = 7;
        $query = $this->table('lie_feedback');
        $where['type'] = $typeInquirePrice;
        $where['user_id'] = $userId;
        $allCount = $this->where($where)->count();
        $todoCount = $this->where($where)->where(['status' => ['in', [0, 2]]])->count();
        $respondedCount = $this->where($where)->where(['status' => 1])->count();
        if ($status != -1) {
            if ($status == 0) {
                $where['status'] = ['in', [0, 2]];
            }
            if ($status == 1) {
                $where['status'] = $status;
            }
        }
        $count = $query->where($where)->count();
        $offset = intval($page - 1) * $limit;
        $list = $query->where($where)
            ->order('id desc')
            ->limit($offset, $limit)
            ->select();
        foreach ($list as $key => &$value) {
            $value['no'] = $key + 1;
            $value['create_time'] = date("Y-m-d H:i:s", $value['create_time']);
            $value['status_name'] = $value['status'] == 1 ? "已答复" : "待处理";
            $fistStr = explode("型号:", $value['content'])[1];
            $value['model'] = explode(",", $fistStr)[0];
        }
        unset($value);
        $data['count'] = (int)$count;
        $data['limit'] = (int)$limit;
        $data['p'] = (int)$page;
        $data['amount'] = [
            'all' => (int)$allCount,
            'todo' => (int)$todoCount,
            'responded' => (int)$respondedCount,
        ];
        $data['total_page'] = ceil($count / $limit);
        $data['list'] = $list ?: [];
        return $data;
    }
}