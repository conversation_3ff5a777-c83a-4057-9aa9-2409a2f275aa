<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace Help\Model;
use Think\Model;
/**
 * Description of CmsModel
 *
 * <AUTHOR>
 */
class CmsModel extends Model{
    protected $autoCheckFields = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.CMS');
    }

    /**
     * 获取供应商的广告营销词
     * @param  [type] $supplier_id [description]
     * @return [type]              [description]
     */
    public function getData($table,$where,$field='*',$limit='',$order=''){
        $datas = $this->table($table)->where($where)->limit($limit)->order($order)->field($field)->select();
        return $datas;
    }
    
    /**
     * 获取专卖信息
     * @param  string $where [description]
     * @param  string $order [description]
     * @param  string $limit [description]
     * @return [type]        [description]
     */
    public function getMonopolyGoods($table,$where=array(),$field='*',$order='id desc', $limit = 20)
    {
        $datas = $this->table($table)->where($where)->order($order)->field($field)->limit($limit)->select();
        return $datas;
    }
}
