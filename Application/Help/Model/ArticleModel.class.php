<?php

namespace Help\Model;

use Think\Model;

class ArticleModel extends Model
{
    public function getCount($where = '')
    {
        $map = array(
            'status' => 1,
        );
        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->where($map)->count();
        return $datas;
    }

    public function getList($where = '', $page = '', $order = 'sort_order DESC,pub_time DESC')
    {
        $map = array(
            'status' => 1,
        );
        list($p, $limit) = explode(',', $page);
        empty($p) && $p = 1;
        empty($limit) && $limit = C('DEFAULT_PAGE_LIMIT');
        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->where($map)->page($p, $limit)->order($order)
            ->field('art_id, title, description, pub_time, is_top')
            ->select();
        return $datas;
    }

    //根据表名table，条件where等获取信息
    public function getData($table, $where, $field = '*', $limit = '', $order = '')
    {
        $datas = $this->table($table)->where($where)->limit($limit)->order($order)->field($field)->select();
        return $datas;
    }

    //插入数据
    public function addData($table, $data)
    {
        $datas = $this->table($table)->add($data);
        return $datas;
    }

    public function getPrevAndNextArticle($artId)
    {
        $artId = intval($artId);
        //获取上一个和下一个的id
        $model = new \Think\Model();
        $prevArtId = $model->query("select art_id from lie_article where art_id = (select art_id from lie_article where status=1 and art_id < {$artId} order by art_id desc limit 1)");
        $prevArtId = !empty($prevArtId[0]) ? $prevArtId[0]['art_id'] : null;
        $prevArtTitle = D('Home/Article')->where(['art_id' => $prevArtId])->getField('title');
        $nextArtId = $model->query("select art_id from lie_article where art_id = (select art_id from lie_article where status=1  and art_id > {$artId} limit 1)");
        $nextArtId = !empty($nextArtId[0]) ? $nextArtId[0]['art_id'] : null;
        $nextArtTitle = D('Home/Article')->where(['art_id' => $nextArtId])->getField('title');
        return [
            'prev_art_id' => $prevArtId,
            'next_art_id' => $nextArtId,
            'prev_art_title' => $prevArtTitle,
            'next_art_title' => $nextArtTitle,
        ];
    }
}