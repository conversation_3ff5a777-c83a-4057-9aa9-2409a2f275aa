<?php

namespace Help\Controller;

use Help\Controller\BaseController;

class HelpController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        // if (!in_array(strtolower(ACTION_NAME), array(''))) {
        //     //检查登录
        //     $res = $this->checkLogin();
        //     if ($res['err_code'] != 0) {
        //         return $this->apiReturn($res['err_code'], $res['err_msg']);
        //     }
        // }
    }

    //h5帮助中心接口数据
    public function more()
    {
        $cid = $_GET['cid'];
        if (empty($cid)) {
            $this->apiReturn(1, 'id不能为空');
        }
        $m = D('Cms');
        $list = array();
        $first_nav = array();
        $data = array();
        $res = $m->getMonopolyGoods('lie_model', array('model_key' => 'common_question'), "*", '', 1);
        if (!empty($res)) {

            $where = array();
            $where['model_id'] = $res[0]['model_id'];
            $where['status'] = 1;
            $where['parent_id'] = $cid;
            $res_cat2 = $m->getMonopolyGoods('lie_help_center_cat', $where, "*", 'sort desc,hccat_id', '');
            if (!empty($res_cat2)) {
                $ids = '';
                foreach ($res_cat2 as $k => $v) {
                    $ids .= ',' . $v['hccat_id'];
                }
                $where = array();
                $where['hccat_id'] = array('in', trim($ids, ','));
                $where['status'] = 1;
                $page_num = 10;
                $page = $_GET['p'] ? $_GET['p'] : 2;
                $start = ($page - 1) * $page_num;
                $data = $m->getMonopolyGoods('lie_help_center', $where, "*", 'sort desc', $start . ',' . $page_num);
            }

        }
        if (empty($data)) {
            $this->apiReturn(1, '暂无数据');
        } else {
            $this->apiReturn(0, '', $data);
        }

    }

    //帮助中心，主要根据title搜索
    public function search()
    {
        $keyword = I('kw', '', 'trim,addslashes,htmlspecialchars');
        if (empty($keyword)) {
            $this->apiReturn(1, '关键词不能为空');
        }
        $m = D('Cms');

        $data = array();
        $res = $m->getMonopolyGoods('lie_model', array('model_key' => 'common_question'), "*", '', 1);
        if (!empty($res)) {
            $where = array();
            $where['model_id'] = $res[0]['model_id'];
            $where['status'] = 1;
            $where['type'] = 2;
            $where['parent_id'] = 0;
            $res_cat = $m->getMonopolyGoods('lie_help_center_cat', $where, "*", 'sort desc,hccat_id', '');
            if (!empty($res_cat)) {
                $cat_ids = '';
                foreach ($res_cat as $k => $v) {
                    $cat_ids .= ',' . $v['hccat_id'];
                }
            }
            $where = array();
            $where['model_id'] = $res[0]['model_id'];
            $where['status'] = 1;
            $where['parent_id'] = array('in', trim($cat_ids, ','));
            $res_cat2 = $m->getMonopolyGoods('lie_help_center_cat', $where, "*", 'sort desc,hccat_id', '');
            if (!empty($res_cat2)) {
                $ids = '';
                foreach ($res_cat2 as $k => $v) {
                    $ids .= ',' . $v['hccat_id'];
                }
                $where = array();
                $where['title'] = array('like', '%' . $keyword . '%');
                $where['hccat_id'] = array('in', trim($ids, ','));
                $where['status'] = 1;
                $page_num = 10;
                $page = $_GET['p'] ? $_GET['p'] : 1;
                $start = ($page - 1) * $page_num;
                $data = $m->getMonopolyGoods('lie_help_center', $where, "*", 'sort desc', $start . ',' . $page_num);
            }

        }

        if (empty($data)) {
            $this->apiReturn(1, '暂无数据');
        } else {
            $this->apiReturn(0, '', $data);
        }

    }

    //用户反馈意见
    public function feedback()
    {
        $arr = $_REQUEST;

        if (!isset($arr['type']) || $arr['type'] <= 0) {
            $this->apiReturn(1, '请选择问题类型');
            exit;
        }
        if (!isset($arr['content']) || empty($arr['content'])) {
            $this->apiReturn(1, '请填写您的意见');
            exit;
        }
        //如果是询价,就要加上下面的判断
        if ($arr['type'] == 7) {
            if (mb_strlen(json_decode($arr['content'], true)['type']) < 2) {
                $this->apiReturn(1, '型号或者参数文字长度需要大于2');
                exit;
            }
        }

        //如果是投书,还要加上图片判断
        if ($arr['type'] == 19) {
            if (empty($arr['images'])) {
                $this->apiReturn(1, '请上传相关图片');
                exit;
            }
        }

        //如果是商品反馈,商品问题和订单号也是必须的
        if ($arr['type'] == 20) {
            if (empty($arr['goods_issue'])) {
                $this->apiReturn(1, '请选择商品问题');
                exit;
            }
            if (empty($arr['order_sn'])) {
                $this->apiReturn(1, '请填写订单号');
                exit;
            }
        }

        if (!isset($arr['source']) || empty($arr['source'])) {
            $this->apiReturn(1, '页面来源不能为空');
            exit;
        }
        if (!isset($arr['pf']) || empty($arr['pf'])) {
            $this->apiReturn(1, '平台标识不能为空');
            exit;
        }

        if (!empty($arr['mobile'])) {
            if (!is_numeric($arr['mobile']) || strlen($arr['mobile']) != 11) {
                if (empty($_COOKIE['Yo4teW_uid'])) {
                    $this->apiReturn(1, '请正确填写您的手机号');
                    exit;
                }
            }

        }

        //如果用户登录，则记录用户的id
        $data = array();
        $data['user_id'] = $_COOKIE['Yo4teW_uid'] > 0 ? $_COOKIE['Yo4teW_uid'] : 0;
        $skey = $_COOKIE['Yo4teW_skey'];
        if (empty($data['user_id']) || empty($skey)) {//判断是否登录
            $data['user_id'] = 0;
        }
        $data['ip'] = ip2long(get_client_ip(0, true));
        $m = D('Feedback');

        $where = array();
        $where['ip'] = $data['ip'];
        $where['create_time'] = array('EGT', strtotime(date('Y-m-d')));
        $where['create_time'] = array('ELT', strtotime(date('Y-m-d')) + 86400);
        $res = $m->getData('lie_feedback', $where);
        $cishu = C('FEEKBACK_CISHU');
        if (!$cishu) {
            $cishu = 5;
        }
        if (is_array($res) && count($res) >= $cishu) {
            $this->apiReturn(1, '您今天的反馈意见次数已用完，明天再来吧！');
            exit;
        }
        $data['content'] = strip_tags($arr['content']);

        if ($arr['type'] == 5) {
            $data['content'] = strip_tags('用户搜索无结果词为：' . $arr['content']);
        } else {
            if ($arr['type'] == 6) {
                $data['content'] = strip_tags('用户最后搜索词为：' . $arr['content']);
            } else {
                if ($arr['type'] == 7) {
                    $content_arr = json_decode($arr['content'], true);
                    //型号
                    $type = '型号:' . (!empty($content_arr['type']) ? $content_arr['type'] : '');
                    //数量
                    $number = '数量:' . (!empty($content_arr['number']) ? $content_arr['number'] : '');
                    //品牌
//            $brand = '品牌:'.(!empty($content_arr['brand'])?$content_arr['brand']:'');

                    $data['content'] = $type . ',' . $number;
                } else {
                    if ($arr['type'] == 8) {
                        $content_arr = json_decode($arr['content'], true);
                        //姓名
                        $name = '姓名:' . (!empty($content_arr['name']) ? $content_arr['name'] : '');
                        //型号
                        $type = '型号:' . (!empty($content_arr['type']) ? $content_arr['type'] : '');
                        //品牌
                        $brand = '品牌:' . (!empty($content_arr['brand']) ? $content_arr['brand'] : '');

                        $data['content'] = $type . ',' . $brand . ',' . $name;
                    } else {
                        //推送信息
                        $this->sendMsg('有新的用户反馈建议给你，赶紧去会员系统查看处理吧~');
                    }
                }
            }
        }

        //投诉
        if ($arr['type'] == 19) {
            $arr['images'] = is_array($arr['images']) ? $arr['images'] : \GuzzleHttp\json_decode($arr['images']);
            $content = "投诉描述 : " . $arr['content'] . PHP_EOL . " 上传图片 : " . implode(' | ', $arr['images']);
            $data['content'] = $content;
        }

        //商品反馈
        if ($arr['type'] == 20) {
            $content = "商品问题 : " . $arr['goods_issue'] . PHP_EOL . " 订单号 : " . $arr['order_sn'] . PHP_EOL . " 描述 : " . $arr['content'];
            $data['content'] = $content;
        }


        $data['type'] = $arr['type'];
        $data['mobile'] = $arr['mobile'] ? $arr['mobile'] : '';
        $data['create_time'] = time();
        $data['source'] = $arr['source'];
        $data['pf'] = $arr['pf'];
        $data['bom_url'] = !empty($arr['bom_url']) ? $arr['bom_url'] : '';
        $a_res = $m->addData($data);

        if ($a_res) {
            $this->apiReturn(0, '谢谢您的反馈');
            exit;
        } else {
            $this->apiReturn(1, '失败');
            exit;
        }
    }

    /**
     * 获取用户反馈条数
     */
    public function getFeedbackCount()
    {
        $m = D('Feedback');
        $count = $m->getFeedbackCount(time());
        $min_count = 450;
        if ($count < $min_count) {
            $count += $min_count;
        }
        $this->apiReturn(0, 'success', $count);
        exit;
    }

    //获取ip
    function remoteIp()
    {
        if (getenv('HTTP_CLIENT_IP')) {
            $onlineip = getenv('HTTP_CLIENT_IP');
        } elseif (getenv('HTTP_X_FORWARDED_FOR')) {
            $onlineip = getenv('HTTP_X_FORWARDED_FOR');
        } elseif (getenv('REMOTE_ADDR')) {
            $onlineip = getenv('REMOTE_ADDR');
        } else {
            $onlineip = $HTTP_SERVER_VARS['REMOTE_ADDR'];
        }
        return $onlineip;
    }

    /**
     * 获取客户端IP地址
     * @param integer $type 返回类型 0 返回IP地址 1 返回IPV4地址数字
     * @param boolean $adv 是否进行高级模式获取（有可能被伪装）
     * @return mixed
     */
    function get_client_ip($type = 0, $adv = false)
    {
        $type = $type ? 1 : 0;
        static $ip = null;
        if (null !== $ip) {
            return $ip[$type];
        }

        if ($adv) {
            if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
                $pos = array_search('unknown', $arr);
                if (false !== $pos) {
                    unset($arr[$pos]);
                }

                $ip = trim($arr[0]);
            } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
                $ip = $_SERVER['HTTP_CLIENT_IP'];
            } elseif (isset($_SERVER['REMOTE_ADDR'])) {
                $ip = $_SERVER['REMOTE_ADDR'];
            }
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        // IP地址合法验证
        $long = sprintf("%u", ip2long($ip));
        $ip = $long ? array($ip, $long) : array('0.0.0.0', 0);
        return $ip[$type];
    }

    /*
      * 发送消息
      */
    public function sendMsg($msg)
    {
        $check = array();
        $check['touser'] = json_encode('INNER_PERSON');
        $check['data'] = json_encode(array('msg' => $msg), JSON_UNESCAPED_UNICODE);
        $check['pf'] = 1;
        $check['keyword'] = 'KEFU_TIP_FEEDBACK';
        $check['is_ignore'] = true;
        $check = array_merge($check, authkey());//dump($check);
        $res = post_curl(API_DOMAIN . '/msg/sendMessageByAuto', $check);
        return $res;
    }

    /*
     * 测试发送短信
     */
    public function test()
    {
        $spuRedis = spu_redis_init();
        $dgk_res = $spuRedis->hMGet("sku_raw_map", [1168906490875066002,1168916711211180147]);
        dd($dgk_res);
    }

    //用户询价列表
    public function feedback_list()
    {
        $userId = I('user_id');
        $status = I('status', -1);
        $status = I('status', -1);
        $page = I('page', 1);
        $limit = I('limit', 10);
        $model = D('feedback');
        $data = $model->getFeedbackList($userId, $status, $page, $limit);
        $this->apiReturn(0, 'success', $data);
    }


    //客服留言接口
    public function add_message()
    {
        $userId = cookie('uid');
        $data['pf'] = I('pf', 2);
        $data['user_id'] = $userId ?: 0;
        if (!$userId) {
            return $this->apiReturn(1, '请先登陆');
        }
        $feedbackMessageType = 11;
        $model = D('Feedback');
        $where['create_time'][] = ['EGT', strtotime(date('Y-m-d'))];
        $where['create_time'][] = ['ELT', strtotime(date('Y-m-d')) + 86400];
        //判断该用户反馈次数
        $count = $model->where([
            'user_id' => $userId,
            'type' => $feedbackMessageType
        ])->where($where)->count();
        if ($count > 10) {
            return $this->apiReturn(1, '您今天提交留言的次数已到达上限');
        }
        if (empty(I('source'))) {
            return $this->apiReturn(1, '页面来源不能为空');
        }

        if (empty(I('content'))) {
            return $this->apiReturn(1, '留言内容不能为空');
        }
        $data['type'] = $feedbackMessageType;
        $data['ip'] = ip2long(get_client_ip(0, true));
        $data['create_time'] = time();
        $user = S_user($userId);
        $data['mobile'] = array_get($user, 'mobile');
        $data['content'] = I('content', '');

        $res = $model->addData($data);

        if ($res) {
            return $this->apiReturn(0, '添加留言成功');
        } else {
            return $this->apiReturn(1, '添加留言失败');
        }
    }

    //询价接口
    public function add_enquiry()
    {
        $userId = cookie('uid') ?: 0;
        if (empty($userId)) {
            return $this->apiReturn(1, "请先登陆!");
        }
        $redis = redis_init();
        $userInfo = $redis->hget('api_user', $userId);
        if ($userInfo) {
            $userInfo = \GuzzleHttp\json_decode($userInfo, true);
        } else {
            //去数据库
            $userInfo = M('user_main')->where(['user_id' => $userId])->find();
        }
        $mobile = $userInfo['mobile'];
        $goodsId = I('goods_id');
        $amount = I('amount');
//        $deliveryTime = I('delivery_time');
        //判断是否已经申请过
        $pf = I('pf');
        $data['user_id'] = $userId;
        $data['source'] = I('source');
        $data['ip'] = ip2long(get_client_ip(0, true));
        $data['type'] = 7;
        $data['mobile'] = $mobile ?: 0;
        $data['create_time'] = time();
        $data['pf'] = $pf;
        $feedbackEnquiryType = 7;
        $model = D('Feedback');
        $where['create_time'][] = ['EGT', strtotime(date('Y-m-d'))];
        $where['create_time'][] = ['ELT', strtotime(date('Y-m-d')) + 86400];
        if (!$userId) {
            return $this->apiReturn(1, '请先登陆');
        }
        //判断该用户反馈次数
        $count = $model->where([
            'user_id' => $userId,
            'type' => $feedbackEnquiryType
        ])->where($where)->count();
        if ($count > 10) {
            return $this->apiReturn(1, '您今天提交留言的次数已到达上限');
        }

        if (empty($pf) || empty(I('source'))) {
            return $this->apiReturn(1, '来源或者pf不能为空');
        }
        $goods = \GuzzleHttp\json_decode(post_curl(GOODS_DOMAIN . '/synchronization', array('goods_id' => $goodsId)),
            true);
        $goods['data'] = array_values($goods['data']);
        if (!empty($goods['data'][0]) && $goods['data'][0] != false) {
            $goods = array_first($goods['data']);
            $goodsName = array_get($goods, 'goods_name', '');
            $brandName = array_get($goods, 'brand_name', '');
            $supplierName = array_get($goods, 'supplier_name');
            $content = "型号:$goodsName,数量:$amount,品牌:$brandName,供应商:$supplierName,SKU_ID:$goodsId,页面来源:搜索结果页";
        } else {
            $content = "型号:不存在的型号,数量:$amount,SKU_ID:$goodsId,页面来源:搜索结果页";
        }
        //组装内容
        $data['content'] = $content;
        $result = $model->addData($data);
        if ($result) {
            return $this->apiReturn(0, '新增询价成功');
        } else {
            return $this->apiReturn(1, '失败');
        }
    }

    public function batch_add_enquiry()
    {
        $userId = cookie('uid') ?: 0;
        if (empty($userId)) {
            return $this->apiReturn(1, "请先登陆!");
        }
        $redis = redis_init();
        $userInfo = $redis->hget('api_user', $userId);
        if ($userInfo) {
            $userInfo = \GuzzleHttp\json_decode($userInfo, true);
        } else {
            //去数据库
            $userInfo = M('user_main')->where(['user_id' => $userId])->find();
        }
        $mobile = I('mobile') ? I('mobile') : $userInfo['mobile'];
        $enquiryList = I('enquiry_list');
        if (empty($enquiryList)) {
            return $this->apiReturn(1, '询价内容不能为空');
        }
        $enquiryList = htmlspecialchars_decode($enquiryList);
        $enquiryList = json_decode($enquiryList, true);
        //拼接数据
        $content = '';
        foreach ($enquiryList as $enquiry) {
            $content .= "型号:{$enquiry['goods_name']},数量:{$enquiry['amount']};";
        }
        //判断是否已经申请过
        $pf = I('pf');
        $data['user_id'] = $userId;
        $data['source'] = I('source');
        $data['ip'] = ip2long(get_client_ip(0, true));
        $data['type'] = 7;
        $data['mobile'] = $mobile ?: 0;
        $data['create_time'] = time();
        $data['pf'] = $pf;
        $feedbackEnquiryType = 7;
        $model = D('Feedback');
        $where['create_time'][] = ['EGT', strtotime(date('Y-m-d'))];
        $where['create_time'][] = ['ELT', strtotime(date('Y-m-d')) + 86400];
        if (!$userId) {
            return $this->apiReturn(1, '请先登陆');
        }
        //判断该用户反馈次数
        $count = $model->where([
            'user_id' => $userId,
            'type' => $feedbackEnquiryType
        ])->where($where)->count();
        if ($count > 10) {
            return $this->apiReturn(1, '您今天提交留言的次数已到达上限');
        }

        if (empty($pf) || empty(I('source'))) {
            return $this->apiReturn(1, '来源或者pf不能为空');
        }
        //组装内容
        $data['content'] = $content;
        $result = $model->addData($data);
        if ($result) {
            return $this->apiReturn(0, '新增询价成功');
        } else {
            return $this->apiReturn(1, '失败');
        }
    }

    //FAE技术支持申请
    public function add_fae_apply()
    {
        $mobile = I('mobile');
        $userId = cookie('uid') ?: 0;
        if (empty($mobile)) {
            $userInfo = $this->getUserInfo($userId);
            $mobile = $userInfo['mobile'];
        }
        $companyName = I('company_name');
        $email = I('email');
        $goodsId = I('goods_id');
        //项目信息
        $projectStage = I('project_stage');
        $applyReason = I('apply_reason');
        $projectName = I('project_name');
        $detail = I('detail');
        $name = I('name');

        //tp3.2的表单验证,真tm难用,不用了,自己一个个去验证,艹

//        if (empty($goodsId)) {
//            return $this->apiReturn(1, "商品id不能为空!");
//        }

        if (strlen($mobile) != 11) {
            return $this->apiReturn(1, "请填写正确的手机号!");
        }

        if (empty($detail)) {
            return $this->apiReturn(1, "需求描述不能为空！");
        }
        if (empty($projectStage)) {
            return $this->apiReturn(1, "项目阶段不能为空！");
        }
        if (empty($applyReason)) {
            return $this->apiReturn(1, "申请理由不能为空！");
        }
        //邮箱不为空的时候要验证邮箱格式
        if (!empty($email)) {
            $check = "/\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/";
            if (!preg_match($check, $email)) {
                return $this->apiReturn(1, "邮箱格式不正确！");
            }
        }

        //判断是否已经申请过
        $pf = I('pf');
        $data['user_id'] = $userId;
        $data['mobile'] = $mobile;
        $data['source'] = I('source');
        $data['ip'] = ip2long(get_client_ip(0, true));
        $data['type'] = 10;
        $data['create_time'] = time();
        $data['pf'] = $pf;
        $goodsName = $brandName = '';
        if (!empty($goodsId)) {
            $goods = \GuzzleHttp\json_decode(post_curl(GOODS_DOMAIN . '/synchronization',
                array('goods_id' => $goodsId)),
                true);
            $goodsName = $brandName = '';
            if (!empty($goods['data'])) {
                $goods = array_first($goods['data']);
                $goodsName = array_get($goods, 'goods_name', '');
                $brandName = array_get($goods, 'brand_name', '');
            }
        }
        //组装内容
        $content = [
            'goods_id' => $goodsId,
            'goods_name' => $goodsName,
            'brand_name' => $brandName,
            'name' => $this->replaceSpecialChar($name),
            'company_name' => $companyName,
            'project_stage' => $projectStage,
            'project_name' => $projectName,
            'apply_reason' => $applyReason,
            'detail' => $detail,
            'email' => $email,
        ];
        $data['content'] = \GuzzleHttp\json_encode($content);

        $m = D('Feedback');
        $result = $m->addData($data);

        if ($result) {
            return $this->apiReturn(0, '提交成功，工作人员尽快联系您。');
        } else {
            return $this->apiReturn(1, '失败');
        }
    }

    //开发板方案资讯,type=13
    public function add_solution_consult()
    {
        //登陆状态去获取手机号
        $userId = cookie('uid') ?: 0;
        if (empty($userId)) {
            return $this->apiReturn(1, "请先登陆!");
        }
        $redis = redis_init();
        $userInfo = $redis->hget('api_user', $userId);
        if ($userInfo) {
            $userInfo = \GuzzleHttp\json_decode($userInfo, true);
        } else {
            //去数据库
            $userInfo = M('user_main')->where(['user_id' => $userId])->find();
        }
        $mobile = $userInfo['mobile'];
        $application = I('application');
        $projectName = I('project_name');
        $projectDescription = I('project_description');
        $name = I('name');
        $email = I('email');
        $companyName = I('company_name');
        if (mb_strlen($application) == 0 || mb_strlen($application) > 30) {
            return $this->apiReturn(1, '应用领域不能为空,且最多30个字');
        }
        if (mb_strlen($projectName) == 0 || mb_strlen($projectName) > 30) {
            return $this->apiReturn(1, '项目名称不能为空,且最多30个字');
        }

        if (mb_strlen($projectDescription) == 0 || mb_strlen($projectDescription) > 100) {
            return $this->apiReturn(1, '项目描述不能为空,且最多30个字');
        }
        if (mb_strlen($companyName) == 0 || mb_strlen($companyName) > 100) {
            return $this->apiReturn(1, '公司名称不能为空,且最多30个字');
        }
        if (mb_strlen($name) == 0 || mb_strlen($name) > 30) {
            return $this->apiReturn(1, '姓名不能为空,且最多10个字');
        }

        //组装内容
        $pf = I('pf');
        $data['user_id'] = $userId;
        $data['mobile'] = $mobile;
        $data['source'] = I('source');
        $data['ip'] = ip2long(get_client_ip(0, true));
        $data['type'] = 13;
        $data['create_time'] = time();
        $data['pf'] = $pf;
        $content = [
            'name' => $this->replaceSpecialChar($name),
            'company_name' => $companyName,
            'email' => $email,
            'application' => $application,
            'project_name' => $projectName,
            'project_description' => $projectDescription,
        ];
        $data['content'] = \GuzzleHttp\json_encode($content);
        $model = D('Feedback');
        $result = $model->addData($data);
        //判断该用户反馈次数
        $where['create_time'][] = ['EGT', strtotime(date('Y-m-d'))];
        $where['create_time'][] = ['ELT', strtotime(date('Y-m-d')) + 86400];
        $count = $model->where([
            'user_id' => $userId,
            'type' => 13
        ])->where($where)->count();
        if ($count > 10) {
            return $this->apiReturn(1, '您今天提交留言的次数已到达上限');
        }
        if ($result) {
            return $this->apiReturn(0, '提交成功，工作人员尽快联系您');
        } else {
            return $this->apiReturn(1, '提交失败');
        }
    }

    private function replaceSpecialChar($str)
    {
        $regex = "/\/|\～|\，|\。|\！|\？|\“|\”|\【|\】|\『|\』|\：|\；|\《|\》|\’|\‘|\ |\·|\~|\!|\@|\#|\\$|\%|\^|\&|\*|\(|\)|\_|\+|\{|\}|\:|\<|\>|\?|\[|\]|\,|\.|\/|\;|\'|\`|\-|\=|\\\|\|/";
        return preg_replace($regex, "", $str);
    }

    private function getUserInfo($userId)
    {
        $redis = redis_init();
        $userInfo = $redis->hget('api_user', $userId);
        if ($userInfo) {
            $userInfo = \GuzzleHttp\json_decode($userInfo, true);
        } else {
            //去数据库
            $userInfo = M('user_main')->where(['user_id' => $userId])->find();
        }
        return $userInfo;
    }

    // 添加bom询价到用户反馈表，用于CRM任务管理
    public function addBomInquiry()
    {
        $user_id = cookie('uid');
        if (!$user_id) return $this->apiReturn(-1, '请先登陆');

        $bom_url = I('bom_url', '');
        if (!$bom_url) return $this->apiReturn(-1, 'bom链接不能为空');

        $data = array(
            'user_id' => $user_id,
            'mobile' => I('mobile', ''),
            'ip' => ip2long(get_client_ip(0, true)),
            'type' => 14,
            'content' => $bom_url,
            'pf' => I('pf', 1),
            'create_time' => time(),
        );

        $FeedbackModel = D('Feedback');
        $res = $FeedbackModel->addData($data);

        if ($res === false) return $this->apiReturn(-2, '添加BOM询价失败');

        return $this->apiReturn(0, '成功');
    }

    // 用户反馈 - 添加数据纠错类型
    public function addDataRepair()
    {
        $user_id = cookie('uid');
        // if (!$user_id) return $this->apiReturn(-1, '请先登陆');

        $sku_id = I('sku_id', 0);
        $goods_name = I('goods_name', '');
        $err_type = I('err_type', '');
        $desc = I('desc', '');
        $brand_name = I('brand_name', '');
        $supplier_name = I('supplier_name', '');

        if (!$sku_id) return $this->apiReturn(-1, '商品ID缺失');
        if (!$goods_name) return $this->apiReturn(-1, '商品型号缺失');
        if (strpos($err_type, '其他') !== false && !$desc) return $this->apiReturn(-1, '类型有其他时，描述不可为空');
        if (!$desc && mb_strlen($desc) > 256) return $this->apiReturn(-1, '描述字数超出限制，最大限制为256个字符');

        $FeedbackModel = D('Feedback');

        // 限制IP提交次数
        $map = [];
        $map['type'] = 15;
        $map['ip'] = ip2long(get_client_ip(0, true));
        $map['create_time'] = ['gt', strtotime(date('Y-m-d', time()))];

        $count = $FeedbackModel->where($map)->count();

        if ($count >= 10) return $this->apiReturn(-1, '今日已提交10次，请明天再提交');

        $content = [
            'sku_id' => $sku_id,
            'goods_name' => $goods_name,
            'err_type' => $err_type,
            'desc' => $desc,
            'brand_name' => $brand_name,
            'supplier_name' => $supplier_name,
        ];

        $data = array(
            'ip' => ip2long(get_client_ip(0, true)),
            'type' => 15,
            'content' => json_encode($content),
            'pf' => platform(),
            'create_time' => time(),
        );

        if ($user_id) {
            $user_info = S_user($user_id);

            $data['user_id'] = $user_id;
            $data['mobile'] = $user_info['mobile'];
        }

        $res = $FeedbackModel->addData($data);

        if ($res === false) return $this->apiReturn(-2, '添加数据纠错失败');

        return $this->apiReturn(0, '成功');
    }

    // 用户反馈 - 添加企业专线申请类型
    public function addCompanyApply()
    {
        $com_name = I('com_name', '');
        $linkman = I('linkman', '');
        $mobile = I('mobile', '');
        $email = I('email', '');

        if (!$com_name) return $this->apiReturn(-1, '公司名称缺失');
        if (!$linkman) return $this->apiReturn(-1, '联系人缺失');
        if (!$mobile) return $this->apiReturn(-1, '联系电话缺失');
        if (!is_mobile($mobile)) return $this->apiReturn(-1, '联系电话格式错误');
        if ($email && !is_email($email)) return $this->apiReturn(-1, '邮箱格式错误');

        $FeedbackModel = D('Feedback');

        // 限制IP提交次数
        $map = [];
        $map['type'] = 16;
        $map['ip'] = ip2long(get_client_ip(0, true));
        $map['create_time'] = ['gt', strtotime(date('Y-m-d', time()))];

        $count = $FeedbackModel->where($map)->count();

        if ($count >= 10) return $this->apiReturn(-1, '今日已提交10次，请明天再提交');

        $user_id = cookie('uid');

        // 判断是否登录
        if ($user_id) {
            $user_info = S_user($user_id);

            $add_mobile = $user_info['mobile'];
        } else {
            $user_id = S_account($mobile); // 手机

            if (!$user_id) {
                $user_id = S_account($email); // 邮箱
            }

            $add_mobile = $mobile;
        }

        $content = [
            'com_name' => $com_name,
            'linkman' => $linkman,
            'mobile' => $mobile,
            'email' => $email,
        ];

        $sale_id = 0;
        if ($user_id) {
            $CrmUserModel = D('Crm/User');
            $sale_id = $CrmUserModel->getSaleId($user_id);
        }

        $data = array(
            'user_id' => $user_id,
            'mobile' => $add_mobile,
            'ip' => ip2long(get_client_ip(0, true)),
            'type' => 16,
            'content' => json_encode($content),
            'pf' => platform(),
            'sale_id' => $sale_id,
            'create_time' => time(),
        );

        $res = $FeedbackModel->addData($data);

        if ($res === false) return $this->apiReturn(-2, '添加企业专线申请失败');

        return $this->apiReturn(0, '成功');
    }

    // 用户反馈 - 添加账期申请
    public function addCreditApply()
    {
        $com_name = I('com_name', '');
        $linkman = I('linkman', '');
        $mobile = I('mobile', '');
        $email = I('email', '');
        $com_nature = I('com_nature', '');
        $pf = I('pf', 1);
        $business_license_src = I('business_license_src', '');

        if (!$com_name) return $this->apiReturn(-1, '公司名称必填');
        if (!$linkman) return $this->apiReturn(-1, '联系人必填');
        if (!$mobile) return $this->apiReturn(-1, '联系电话必填');
        if (!is_mobile($mobile)) return $this->apiReturn(-1, '联系电话格式错误');
        if ($email && !is_email($email)) return $this->apiReturn(-1, '邮箱格式错误');
        if (!$com_nature) return $this->apiReturn(-1, '公司性质必填');
        if (!$business_license_src) return $this->apiReturn(-1, '未上传营业执照');

        $FeedbackModel = D('Feedback');

        // 限制IP提交次数
        $map = [];
        $map['type'] = 17;
        $map['ip'] = ip2long(get_client_ip(0, true));
        $map['create_time'] = ['gt', strtotime(date('Y-m-d', time()))];

        $count = $FeedbackModel->where($map)->count();

        if ($count >= 10) return $this->apiReturn(-1, '今日已提交10次，请明天再提交');

        $user_id = cookie('uid') ?: 0;
        $add_mobile = '';

        // 判断是否登录
        if ($user_id) {
            $user_info = S_user($user_id);

            $add_mobile = $user_info['mobile'];
        }

        $content = [
            'com_name' => $com_name,
            'linkman' => $linkman,
            'mobile' => $mobile,
            'email' => $email,
            'com_nature' => $com_nature,
            'business_license_src' => $business_license_src,
        ];

        // 网站登录用户有销售员跟进，则分配给对应的销售员，无销售员跟进或者未登录用户，则分配给张娟
        $sale_id = 0;
        if ($user_id) {
            $CrmUserModel = D('Crm/User');
            $sale_id = $CrmUserModel->getSaleId($user_id);
        } else {
            $sale_id = C('credit_apply_fixed_sale');
        }

        $data = array(
            'user_id' => $user_id,
            'mobile' => $add_mobile,
            'ip' => ip2long(get_client_ip(0, true)),
            'type' => 17,
            'content' => json_encode($content),
            'pf' => platform(),
            'sale_id' => $sale_id,
            'pf' => $pf,
            'create_time' => time(),
        );

        $res = $FeedbackModel->addData($data);

        if ($res === false) return $this->apiReturn(-2, '添加账期申请失败');

        return $this->apiReturn(0, '成功');
    }
}
