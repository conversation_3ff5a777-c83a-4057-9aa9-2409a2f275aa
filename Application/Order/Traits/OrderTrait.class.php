<?php

namespace Order\Traits;

use Home\Services\InquiryService;
use \Order\Exception\OrderException;
use Order\Model\GiftActivityModel;

/**
 * 备注：订单类扩展 拆分下订单类
 * Class OrderTrait
 * @package Order\Traits
 */
trait OrderTrait
{

    public function abcTrait()
    {
//        $OrderModel = D('Order');
//        $list = $OrderModel->where("status = 2")->select();
////        dump($list);
//        $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.search"));
//        $res = $redis->hget("sku_raw_map","1151032966975902636");
////        dump($res);
//
////        S("sunlong20200326","haha heehe");
//        dump(S("sunlong20200326"));
    }

    /**
     * 判断订单中是否包含djk商品
     */
    public function checkOrderGoodsDjk($datas = [])
    {
        $is_djk = false;

        //查找购物车商品中是否含有extend_type=5  bom单
        //get["type"] 》 0 后台
        $type = I('request.type', 0, 'intval');
        $fromBomDjk = false;
        foreach ($datas["list"] as $k => $goods) {
            if ($goods["extend_type"] == 5) {
                $fromBomDjk = true;
                break;
            }

        }
        if ($type > 0 && $fromBomDjk) {
            return false;
        }
        foreach ($datas["list"] as $k => $goods) {
            if ($goods["supplier_id"] == C("DJK_SUPPLIER_ID") || strtolower($goods["supplier_name"]) == C("DJK_SUPPLIER_NAME")) {
                $is_djk = true;
                break;
            }

        }
        return $is_djk;
    }

    /**
     * 如果没有传终端数据 就清理调djk商品
     */
    public function deleteNoDJKGoods(&$datas = [], &$djkParams = [], &$is_djk = false)
    {
        $djkParams['customer_cn'] = I('request.customer_cn', "", 'trim');       //客户名称（中文）
        $djkParams['customer_en'] = I('request.customer_en', "", 'trim');       //客户名称（英文）
        $djkParams['customer_type'] = I('request.customer_type', 0, 'intval');       //客户类型：1终端 2贸易商 3大学 4个人
        $djkParams['product_use_classone_sn'] = I('request.product_use_classone_sn', -1,
            "intval");       //一级分类中配置文件数组中标示位
        $djkParams['product_use_classtwo_sn'] = I('request.product_use_classtwo_sn', -1,
            "intval");       //二级分类中配置文件数组中标示位
        $djkParams["customer_website"] = I('request.customer_website', "", "trim");//客户网址


        //如果含有djk商品，并且客户类型为终端 所有信息必填
        if ($djkParams['customer_type'] == 1) {
            $bk = $djkParams['customer_cn'] && $djkParams['customer_en'] && $djkParams['customer_type'] > 0
                && $djkParams['product_use_classone_sn'] >= 0 && $djkParams['product_use_classtwo_sn'] >= 0;

            if (!$bk) {
                throw new OrderException("Digikey必须填写终端信息才可下单成功，或返回购物车删除digikey商品", "21029");
            }

            $customer_en = mb_convert_encoding($djkParams['customer_en'], 'UTF-8', 'GB2312');
            if (!preg_match('/^[a-zA-Z0-9\.\-\s\,\(\)]+$/', $customer_en)) {
                throw new OrderException("第二项终端名称必须填写英文", "21029");
            }
            if (!in_array($djkParams['customer_type'], [1, 2, 3, 4])) {
                throw new OrderException("客户类型选择错误", "21029");
            }
            if (in_array($djkParams['customer_type'], [2, 4])) {
                $user_id = $this->auth() ? I('uid') : cookie('uid');

                if ($user_id) {
                    $where = sprintf("user_id = %d and (supplier_id = %d or supplier_name = '%s')",
                        intval($user_id), intval(C("DJK_SUPPLIER_ID")), C("DJK_SUPPLIER_NAME"));
                    M("ShoppingCart")->where($where)->delete();
                }

                throw new OrderException("由于政策问题，digikey限制上述商品的出售 同时删除订单中digikey的商品", "21030");
            }
            $djkClass = C("DJKCONFIG");
            if (!isset($djkClass["class_one"][$djkParams['product_use_classone_sn']])) {
                throw new OrderException("应用类型选择错误", "21029");
            }

            if (!isset($djkClass["class_two"][$djkParams['product_use_classone_sn']][$djkParams['product_use_classtwo_sn']])) {
                throw new OrderException("应用类型选择错误", "21029");
            }

        } else {
            if ($djkParams['customer_type'] > 0 && $djkParams['customer_type'] != 1) {
                if ($djkParams['customer_cn'] == "") {
                    throw new OrderException("请填写终端中文名称", "21029");
                }

                if (!in_array($djkParams['customer_type'], [1, 2, 3, 4])) {
                    throw new OrderException("客户类型选择错误", "21029");
                }

            } else {
                throw new OrderException("Digikey必须填写终端信息才可下单成功，或返回购物车删除digikey商品", "21029");
                //删除djk商品
//            foreach($datas["list"] as $k=>$goods){
//                if($goods["supplier_id"] == C("DJK_SUPPLIER_ID") || strtolower($goods["supplier_name"]) == C("DJK_SUPPLIER_NAME")){
//                    unset($datas["list"][$k]);
//                }
//            }
//            !empty($datas["list"])  &&  $datas["list"] = array_merge($datas["list"]);
//            $is_djk = false;
//            $djkParams=[];
            }
        }

    }

    public function addOrderDJKInfo($is_djk = false, $djkParams = [], $order_id)
    {
        if (!$is_djk) {
            return true;
        }
        $djkParams["order_id"] = $order_id;
        $data = [];
        $data["order_id"] = $order_id;
        $data["customer_cn"] = $djkParams["customer_cn"];
        $data["customer_en"] = $djkParams["customer_en"];

        $customerConfig = C("DJKCONFIG");
        $data["customer_type"] = intval($djkParams["customer_type"]);
        $data["product_use_classone_sn"] = $djkParams["product_use_classone_sn"] !== '' ? intval($djkParams["product_use_classone_sn"]) : '-1';
        $data["product_use_classtwo_sn"] = $djkParams["product_use_classtwo_sn"] !== '' ? intval($djkParams["product_use_classtwo_sn"]) : '-1';

        $djkconfigerp = C("DJKCONFIGERP");
        $data["product_use_classone_erp_sn"] = $data["product_use_classone_sn"] != '-1' ? $djkconfigerp["class_one"][$data["product_use_classone_sn"]] : '';
        $data["product_use_classtwo_erp_sn"] = $data["product_use_classone_sn"] != '-1' ? $djkconfigerp["class_two"][$data["product_use_classone_sn"]][$data["product_use_classtwo_sn"]] : '';
        $data["customer_website"] = $djkParams["customer_website"];
        $bk = D("OrderExtra")->add($data);
        return $bk;
    }


    /*
     * 重新计算订单的优惠信息
     */
    public function getOrderPriceDetailTrait($order_id = 0)
    {
        try {
            if (!$order_id) {
                throw new OrderException("没有找到该订单相关信息", -1);
            }
            $orderInfo = D("Order")->where(["order_id" => intval($order_id)])->field("currency")->find();
            $orderGoodsList = D("OrderItems")->where(["status" => 1, "order_id" => intval($order_id)])->select();
            if (!$orderGoodsList) {
                throw new OrderException("没有找到该订单相关信息", -1);
            }
            $newCustomerPreferential = $this->getNewCustomerPreferential($orderInfo, $orderGoodsList);
            $tuangouPreferential = $this->getTuanGouPreferential($orderInfo, $orderGoodsList);
            return $this->apiReturnTrait(0, '', [
                "newCustomerPreferential" => $newCustomerPreferential,
                "tuangouPreferential" => $tuangouPreferential,
            ]);
        } catch (OrderException $e) {
            return $this->apiReturnTrait($e->getCode(), $e->getMessage());
        } catch (\Exception $e) {
            \Think\Log::write(sprintf("重新计算订单 %d 的优惠信息异常:%s", intval($order_id), $e->getMessage()));
            return $this->apiReturnTrait(-1, $e->getMessage());
        }
    }


    /*
     * 计算新客价优惠总额
     */
    protected function getNewCustomerPreferential($orderInfo = [], $orderGoodsList = [])
    {
        $price = 0;
        foreach ($orderGoodsList as $k => $item) {
            if ($item["ac_type"] == \Order\Model\OrderItemsModel::$AC_TYPE_NEWKJ) {
                $goods_number = intval($item['goods_number']);
                $power = array(
                    'newCustomer' => 1,
                    'member' => true,
                );
                $res = $this->getFinalGoods($item['goods_id'], $goods_number, $orderInfo['currency'], $power);
//                dump($res);
                if ($res['err_code'] !== 0) {
                    continue;
                }
                $res_goods = $res['data'];
                $goods_prices = ladder_final_price($res_goods, $orderInfo['currency'],
                    \Order\Model\OrderItemsModel::$AC_TYPE_NEWKJ);
                $initial_prices = ladder_final_price($res_goods, $orderInfo['currency'], false);
                $price += price_format($initial_prices["price"] * $goods_number, 0,
                        4) - price_format($goods_prices["price"] * $goods_number, 0, 4);
            }
        }
        if ($price > 0) {
            $res = $this->getOrderAcivityLimit();
            if ($res['errcode'] === 0 && $res['data'] < $price) {
                $price = $res['data'];
            }
        }
        return $price && $price > 0 ? -$price : 0;;
    }


    /*
     * 获取订单的团购优惠总额
     */
    protected function getTuanGouPreferential($orderInfo = [], $orderGoodsList = [])
    {
        $price = 0;
        try {
            foreach ($orderGoodsList as $k => $item) {
                if ($item["ac_type"] == \Order\Model\OrderItemsModel::$AC_TYPE_TUANGOU) {
                    $goods_number = intval($item['goods_number']);
                    $power = array(
                        'newCustomer' => 0,
                        'member' => true,
                        'assemble' => 1,
                    );
                    $res = $this->getFinalGoods($item['goods_id'], $goods_number, $orderInfo['currency'], $power);
                    if ($res['err_code'] !== 0) {
                        continue;
                    }
                    $res_goods = $res['data'];
                    $price += $res_goods["goods_info"]["ratio"];
                }
            }
            return $price && $price > 0 ? -$price : 0;
        } catch (\Exception $e) {
            return $price;
        }
    }


    protected function apiReturnTrait($code = 0, $msg = '', $extend = [])
    {
        return array(
            'err_code' => $code,
            'err_msg' => $msg,
            'data' => $extend,
        );

    }

    /*
     * bom下单后  推送相关信息到mq
     */
    public function addBomMQ($order_id, $bom_array)
    {
        $queue_name = C('BOM_CREATE_ORDER');

        $RbmqModel = D('Common/Rbmq');

        $data['job'] = "create_bom_order";
        $bom_array = array_map(function ($v) {
            return trim($v);
        }, $bom_array);
        $data["data"] = ["order_id" => intval($order_id), "matching_ids" => $bom_array];

        $res = $RbmqModel->connect(C("WMS_RBMQ_CONFIG"))->queue($queue_name)->push($data, $queue_name);

        $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件
        \Think\Log::write(sprintf("创建bom订单,订单ID：%s", $order_id), INFO, '', $path);

        if ($res) {
            return true;
        }

        return false;
    }


    public function addUserInvitefriend($inviteFriendId, $order_id)
    {
        try {
            $user_id = cookie("uid");
            if (!$user_id) {
                return true;
            }
            //如果购买用户id和分享用户id一样 则不添加奖品和订单关联的 数据   邀好友活动记录表
            if ($user_id == $inviteFriendId) {
                return true;
            }
            $good_ids = D("OrderItems")->where(["order_id" => $order_id])->field("goods_id")->select();
            $goods_temp = [];
            foreach ($good_ids as $item) {
                array_push($goods_temp, $item["goods_id"]);
            }
            $good_id_str = implode(",", $goods_temp);
            D("UserInvitefriend")->add([
                "user_id" => $user_id,
                "invite_user_id" => $inviteFriendId,
                "type_id" => $order_id,
                "status" => 0,
                "goods_id" => $good_id_str ? $good_id_str : '',
                "create_time" => time(),
            ]);
        } catch (\Exception $e) {

        }
    }


    /*
     * 新增优惠明细
     */
    public function addOrderItemsDiscountDetail($order_id, $datas = [])
    {
        try {
            if (!isset($datas["list"])) {
                return true;
            }
            D("OrderItemsPrice")->where(["order_id" => $order_id])->delete();
            foreach ($datas["list"] as $k => $item) {
                if (!isset($item["discountDetail"]) || !is_array($item["discountDetail"])) {
                    continue;
                }
                $discountDetail = $item["discountDetail"];
                $addSql = [];
                $user_id = $this->auth() ? I('uid') : cookie('uid');
                foreach ($discountDetail as $v) {
                    if (intval($v["amount"]) < 0) {
                        continue;
                    }
                    $addSql[] = [
                        "order_id" => $order_id,
                        "rec_id" => $item["rec_id"],
                        "goods_id" => $item["goods_id"],
                        "activity_id" => isset($v["activity_id"]) && $v["activity_id"] ? $v["activity_id"] : 0,
                        "activity_name" => isset($v["activity_name"]) && $v["activity_name"] ? $v["activity_name"] : "",
                        "type" => $v["type"],
                        "amount" => $v["amount"],
                        "settlement_type" => $v["settlement_type"],
                        "user_id" => $user_id,
                        "create_time" => time(),
                    ];

                }
                if (!empty($addSql)) {
//                    dump($addSql);
                    D("OrderItemsPrice")->addAll($addSql);
                }
            }
        } catch (\Exception $e) {
//            dump($e->getMessage());
            return true;
        }

    }

    /*
     * 静调用户转正
     */
    public function jinDiaoUser($is_type, $user_id, $user_info)
    {
        try {
            if ($is_type == 1) {
                $UserMainModel = D('Home/UserMain');
                $UserMainModel->setUserInfo($user_id, array('is_type' => 0));
                $s_user_info = S_user($user_id);
                $s_user_info['is_type'] = 0;
                S_user($user_id, $s_user_info); // 修改缓存值

                $msg_text = '竞调用户 “' . $user_info['data']['mobile'] . '”发生下单行为，转换为正常用户了';

                $queue_name = C('CRM_PUSH_USER');
                $RbmqModel  = D('Common/Rbmq');

                $data['user_id'] = intval($user_id);
                $resp = $RbmqModel->queue($queue_name)->push($data, $queue_name);

                $msg_text .= $resp ? '，推送CRM新增队列成功' : '，推送CRM新增队列失败';

                dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.jingdiao'), $msg_text);
            }
        } catch (\Exception $e) {
        }
    }

    /*
     * 查找购物车id
     */
    public function findConfirmCartIds($list)
    {
        try {
            $arr = [];
            foreach ($list as $item) {
                $arr[] = $item["cart_id"];
                continue;
            }
            return $arr;
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
        }
    }


    /*
     * 写入满赠信息到订单表
     */
    public function addOrderManZInfo($order_id, $order_sn, $_cartIds)
    {
        try {
            //写入满足数量不足的
            $list = D("OrderItemsGift")->where([
                "type" => 1,
                "status" => 0,
                "cart_id" => ["in", $_cartIds]
            ])->field("id,activity_id,goods_amount")->select();
            $this->addOrderGift($list, $order_id, $order_sn, 0, 0);
            $arr = A("Order/Cart")->getCartGift($_cartIds);
            $this->addOrderGift($arr, $order_id, $order_sn, 1, 1);
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
        }
    }


    protected function addOrderGift($arr, $order_id, $order_sn, $status = 1, $type = 0)
    {
        $data = [];
        foreach ($arr as $k => $item) {
            $data[$k]["order_id"] = $order_id;
            $data[$k]["order_sn"] = $order_sn;
            $data[$k]["user_id"] = $this->auth() ? I('uid') : cookie('uid');
            $data[$k]["activity_id"] = $item["activity_id"];
            $data[$k]["gift_info"] = json_encode($item);
            $data[$k]["status"] = $status;
            $data[$k]["create_time"] = time();
            if ($type) {
                $map = ["activity_id" => $item["activity_id"], "amount" => $item["amount"]];
                D("GiftActivityItem")->where($map)->setDec("total", 1);
                //还要去操作redis
                $model = new GiftActivityModel();
                $model->reduceGiftItemTotal($item['activity_id'], $item['amount'], 1);
            }

        }
        if (!empty($data)) {
            D("OrderGift")->addAll($data);
        }
    }


    public function addActivityInfo($v,$specialActivityIds)
    {
        return [
            "rec_id" => $v['rec_id'],
            "goods_id" => $v['goods_id'],
            "amount" => $v["goods_amount"],
            "activity_id" => $v["activity_info"]["activity_id"],
            "special_activity_ids" => $specialActivityIds
        ];
    }

    public function statisticalOrderActivity($order_id, $data = [])
    {
        if (empty($data)) {
            return true;
        }
        try {
            $arr = [];
            foreach ($data as $k => $item) {
                //if (in_array($item["activity_id"], array_keys($arr))) {
                //    $arr[$item["activity_id"]]["amount"] += $item["amount"];
                //} else {
                //    $arr[$item["activity_id"]]["amount"] = $item["amount"];
                //}

               $statistics["amount"] = $item["amount"];
               $statistics["create_time"] = time();
               $statistics["order_id"] = $order_id;
               $statistics["rec_id"] = $item['rec_id'];
               $statistics["goods_id"] = $item['goods_id'];
               $statistics["activity_id"] = $item["activity_id"];
               $statistics["special_activity_ids"] = $item["special_activity_ids"];
               $statistics["user_id"] = $this->auth() ? I('uid') : cookie('uid');
                $arr[] = $statistics;
            }
            D("OrderActivityCount")->addAll($arr);

        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage());
        }

    }

    /*
 * 給用戶發送微信
 */
    public function cOrderSendWachat($s_user_info,$order_sn,$order_id){
        try{
            $user_id = cookie("uid");
            if(!isset($s_user_info["wechat_oauth"]) || !$user_id)
                return true;
            $open_id = isset($s_user_info["wechat_oauth"]["open_id"]) ? $s_user_info["wechat_oauth"]["open_id"] : null;
            if (!$open_id){
                return true;
            }
            $data["touser"] = $open_id;
            $data["msgtype"] = "text";
            $data['text']["content"] = "您的订单".$order_sn."我们已经收到了，谢谢您的信任！由于订单中有部分物料是来自第三方库存,可能会出现价格、库存数量、运费等调整，如有此情况，客服会第一时间和您联系哦！(´▽`ʃ♡ƪ)"."<a href=\"https://m.ichunt.com/h5/view/#/orderDetail?status=-1&order_id=".$order_id."\">查看详情</a>";
            $wechatModel = wechatPublic();
            $wechatModel->sendCustomMessage($data);

        }catch(\Exception $e){

        }
    }

    // 获取订单运费
    public function getfreight()
    {
        $order_id = I('order_id', 0); // 订单ID

        if (!$order_id) return $this->apiReturn(44041, '订单ID缺失');

        $OrderAddressModel = D('OrderAddress');
        $address_info = $OrderAddressModel->getInfo($order_id);

        $freight = get_shipping_price($address_info['province'], $address_info['city']);

        return $this->apiReturn(0, '', $freight);
    }

    /*
     * 获取djk信息
     */
    public function getDJKInfo($goodsList=[]){
        try{
            $goods_id = [];
            foreach($goodsList as $item){
                if ($item['goods_id'] <= 0 || $item["supplier_id"] != C("DJK_SUPPLIER_ID")) {
                    continue;
                }
                $goods_id[] = $item['goods_id'];
            }
            $Redis = new \Redis();
            $c = C('DJK_MAP_REDIS');
            $Redis->connect($c['host'],$c['port']);
            $Redis->auth($c['password']);
            $spuRedis = spu_redis_init();
            $info = $spuRedis->hMGet("sku_raw_map",$goods_id);
            \Think\Log::write(print_r($info,true));
            $Redis->close();
            return $info;
        }catch(\Exception $e){
            return false;
        }


    }

    /*
 * 处理djkmap字段
 */
    public function  getDjkMapField($v,$djkmap_goods_id){
        $raw_goods_sn = $v["raw_goods_sn"] ? $v["raw_goods_sn"] : "";
        $raw_goods_packing = $v["raw_goods_packing"] ? $v["raw_goods_packing"] : "";
        $raw_brand_name = $v["raw_brand_name"] ? $v["raw_brand_name"] : "";

        $_raw_goods_sn =  !empty($djkmap_goods_id) && isset($djkmap_goods_id['raw_goods_id']) ? $djkmap_goods_id['raw_goods_id'] : '';

        $_raw_goods_packing = !empty($djkmap_goods_id) && isset($djkmap_goods_id['pack']) ? $djkmap_goods_id['pack'] : ''; //DGK原始包装

        $_raw_brand_name =  !empty($djkmap_goods_id) && isset($djkmap_goods_id['raw_brand_name']) ? $djkmap_goods_id['raw_brand_name'] : ''; //DGK原始品牌名称

        $raw_goods_sn = $raw_goods_sn ? $raw_goods_sn : $_raw_goods_sn;
        $raw_goods_packing = $raw_goods_packing ? $raw_goods_packing : $_raw_goods_packing;
        $raw_brand_name = $raw_brand_name ? $raw_brand_name : $_raw_brand_name;
        $arr = [$raw_goods_sn,$raw_goods_packing,$raw_brand_name];
        return $arr;
    }

    /*
     * 获取用户是否是测试用户
     */
    public function getUserIsTest($user_id=0){
        $userInfo = S_user($user_id);
        if(!empty($userInfo) && isset($userInfo["is_test"]) && $userInfo["is_test"] == "1"){
            //测试用户
            return 1;
        }
        //普通用户
        return 0;
    }

    // 前台客户已转账通知销售
    public function sendMsgToSale()
    {
        $user_id = cookie('uid');
        $send_data = S('send_msg_to_sale..' . $user_id);

        if ($send_data) {
            $time = date('Y-m-d', $send_data['last_send_time']);
            $second_zero_time = strtotime($time . '+1 days'); // 第二天0点

            // 当前时间小于第二天零点
            if (time() < $second_zero_time) {
                if ($send_data['send_num'] >= 5) {
                    return $this->apiReturn(-1, '您今天发送次数已达上限，明天再来试试吧~');
                }

                // 更新次数和时间
                $send_data['send_num'] += 1;
                $send_data['last_send_time'] = time();
            } else { // 当前时间大于零点，则重新设置次数和时间
                $send_data['send_num'] = 1;
                $send_data['last_send_time'] = time();
            }
        } else {
            $send_data = [
                'send_num' => 1,
                'last_send_time' => time(),
            ];
        }

        S('send_msg_to_sale..' . $user_id, json_encode($send_data));

        $order_id = I('order_id', '');

        if (!$order_id) {
            return $this->apiReturn(-1, '订单ID参数缺失');
        }

        $OrderModel = D('Order');
        $order_info = $OrderModel->getInfo($order_id);

        if (empty($order_info)) {
            return $this->apiReturn(-2, '未获取到订单数据');
        }

        $order_sn = $order_info['order_pf'] == 1 ? $order_info['order_sn'] : $order_info['sale_order_sn'];

        $UserMainModel = D('Home/UserMain');
        $user_info = $UserMainModel->getUserInfo($order_info['user_id']);
        $user_account = $user_info['mobile'] ?: $user_info['email'];

        // 订货客户
        if ($order_info['customer_id']) {
            $TaxinfoModel = D('Invoice/Taxinfo');
            $customer_name = $TaxinfoModel->where(['tax_id' => $order_info['customer_id']])->getField('tax_title');
        } else {
            $customer_name = $user_account;
        }

        $data = array(
            'data' => array(
                'order_sn' => $order_sn,
                'order_amount' => $order_info['order_amount_format'],
                'user_account' => $user_account,
                'customer_name' => $customer_name,
            )
        );

        $data = json_encode($data);

        $CmsModel = D('Cms');
        $to_user = $CmsModel->getUserWebUserId($order_info['sale_id']); // 后台业务员对应前台uid

        $keyword = 'order_paid_notify_sale_ding';
        $res = $this->sendOrderMsg($keyword, $data, $to_user);

        if ($res['err_code'] != 0) {
            return $this->apiReturn(-3, '发送钉钉失败，原因：' . $res['err_msg']);
        }

        $sale_info = $CmsModel->getUserInfo($order_info['sale_id']);

        $keyword = 'order_paid_notify_sale_mail';
        $res = $this->sendOrderMsg($keyword, $data, $sale_info['email']);

        if ($res['err_code'] != 0) {
            return $this->apiReturn(-3, '发送邮件失败，原因：'.$res['err_msg']);
        }

        $OrderActionLogModel = D('OrderActionLog');
        $OrderActionLogModel->addLog($order_id, $user_id, 1, '客户已转账，推送消息给销售（'.$sale_info['name'].'）');

        return $this->apiReturn(0, '发送成功');
    }

    // 格式化文件大小
    public function size_format($size, $digits = 2)
    {
        if (!$size) return 0;

        $unit = array('', 'K', 'M', 'G', 'T', 'P');
        $base = 1024;

        $i = floor(log($size, $base));
        $n = count($unit);

        if ($i >= $n) {
            $i = $n - 1;
        }

        return round($size / pow($base, $i), $digits) . $unit[$i] . 'B';
    }

    // 上传付款水单
    public function uploadBankReceipt()
    {
        $user_id   = cookie('uid');
        $order_id  = I('order_id', 0);
        $name      = I('name', '');
        $file_url  = I('file_url', '');
        $file_size = I('file_size', 0);

        $OrderModel = D('Order');
        $OrderAttachmentModel = D('OrderAttachment');
        $OrderActionLogModel = D('OrderActionLog');

        if (!$order_id) {
            return $this->apiReturn(-1, '订单ID缺失');
        }

        $order_info = $OrderModel->getInfo($order_id);

        if (empty($order_info)) {
            return $this->apiReturn(-1, '未获取到订单数据');
        }

        if (!$name) {
            return $this->apiReturn(-1, '附件名称缺失');
        }

        if (!$file_url) {
            return $this->apiReturn(-1, '上传地址缺失');
        }

        if (!$file_size) {
            return $this->apiReturn(-1, '附件大小缺失');
        }

        $OrderModel->startTrans();

        $addAttachment = [];
        $addAttachment['order_id']     = $order_id;
        $addAttachment['type']         = 1;
        $addAttachment['name']         = $name;
        $addAttachment['file_url']     = $file_url;
        $addAttachment['file_size']    = $this->size_format($file_size);
        $addAttachment['file_desc']    = '客户在网站前台上传的银行水单';
        $addAttachment['source']       = 2;
        $addAttachment['creator_id']   = $user_id;
        $addAttachment['creator_name'] = '';
        $addAttachment['create_time']  = time();

        $res = $OrderAttachmentModel->add($addAttachment);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(-2, '新增订单附件表失败');
        }

        $OrderActionLogModel->addLog($order_id, $user_id, 1, '网站前台上传银行水单');

        // 发送邮件和钉钉消息给订单对应的销售员
        $order_sn = $order_info['order_pf'] == 1 ? $order_info['order_sn'] : $order_info['sale_order_sn'];

        $UserMainModel = D('Home/UserMain');
        $user_info = $UserMainModel->getUserInfo($order_info['user_id']);
        $user_account = $user_info['mobile'] ?: $user_info['email'];

        // 订货客户
        if ($order_info['customer_id']) {
            $TaxinfoModel = D('Invoice/Taxinfo');
            $customer_name = $TaxinfoModel->where(['tax_id' => $order_info['customer_id']])->getField('tax_title');
        } else {
            $customer_name = $user_account;
        }

        $data = array(
            'data' => array(
                'order_sn' => $order_sn,
                'order_amount' => $order_info['order_amount_format'],
                'user_account' => $user_account,
                'customer_name' => $customer_name,
            )
        );

        $data = json_encode($data);

        $CmsModel = D('Cms');
        $to_user = $CmsModel->getUserWebUserId($order_info['sale_id']); // 后台业务员对应前台uid

        $keyword = 'order_upload_paid_notify';
        $res = $this->sendOrderMsg($keyword, $data, $to_user);

        if ($res['err_code'] != 0) {
            $OrderModel->rollback();
            return $this->apiReturn(-3, '发送邮件失败，原因：' . $res['err_msg']);
        }

        $OrderModel->commit();

        return $this->apiReturn(0, '已通知交易员，请等待交易员确认。');
    }

    public function checkCanalIsAddOrder($return_data_list){
        $inquiryService = new InquiryService();
        $checkCanalIsAddOrder = [];
        foreach($return_data_list as $item){
            if(empty($item["supplier_id"]) && empty($item["canal"])){
                continue;
            }
            $checkCanalIsAddOrderKey = $item["supplier_id"]."_".$item["canal"];
            if(isset($checkCanalIsAddOrder[$checkCanalIsAddOrderKey])){
                $checkCanalIsAddOrder[$checkCanalIsAddOrderKey]["total_amount"] += $item["goods_amount"];
                continue;
            }
            $checkCanalIsAddOrderTemp["supplier_id"] = $item["supplier_id"] ?: 0;
            $checkCanalIsAddOrderTemp["currency"] = $item["currency"] ?: 0;
            $checkCanalIsAddOrderTemp["total_amount"] = $item["goods_amount"] ?: 0;
            $checkCanalIsAddOrderTemp["canal"] = isset($item["canal"]) ? $item["canal"] : "";
            $checkCanalIsAddOrder[$checkCanalIsAddOrderKey] = $checkCanalIsAddOrderTemp;
        }
        try{
            $inquiryService->checkCanalIsAddOrder($checkCanalIsAddOrder);
        }catch(\Exception $e){
            return $this->apiReturn(1, $e->getMessage());
        }

        return $this->apiReturn(0, "ok");
        
    }

    public function getUserOverseasTaxinfo($user_id,$uc_id,$tax_title){
        $data = array(
            "tax_title" => $tax_title,
            "user_id" => $user_id,
            "org_id" => 1,
            "uc_id" => $uc_id,
            "inv_type" => 5, // 形式发票
            "inv_area" => 2, // 海外公司
        );
        $url = C("crmDomain");
        $res = post_curl(sprintf("%s%s",rtrim($url,"/"),"/open/fastCrateTaxinfo"),$data);
        $res = json_decode($res,true);
        if(!empty($res) && $res["code"] != 0){
            return 0;
        }
        return $res["data"]["tax_id"];
    }


    public function checkoutCompanyInfo($user_id,$uc_id,$deliveryPlace,$tax_id,$address_id){
        
        $TaxinfoModel = D('Invoice/Taxinfo');
        $taxinfo = $TaxinfoModel->where(['tax_id' => (int)$tax_id])->find();
        if($taxinfo){
            $checkTheInnerEyeData = [
                "com_name"=>$taxinfo["tax_title"],
                "com_tax_registration"=>$taxinfo["tax_no"]?:"",
                "company_type"=>$deliveryPlace,
            ];
        }else{
            return $this->apiReturn(1, "没找找到相关发票信息");
        }

        

        // 公司名称在CRM中是否存在审核通过的数据
        $isExistsPassTaxinfo = $TaxinfoModel->getUserTaxinfoPassByTaxtitle($user_id,$uc_id,$taxinfo["tax_title"]);

        $isExistsUserProtocol = D("Crm/Crm")->isExistsUserProtocol($tax_id);


        //查询天眼查
        if($taxinfo["inv_area"] != 3 && $deliveryPlace == 1 && $isExistsPassTaxinfo <= 0){
            //国内公司，该公司是否存在于天眼查中，若不存在，不允许下单，提示语：该公司信息不存在于天眼查中，请检查后重试
            $eseInfo = A("Invoice/Invoice")->checkTheInnerEyeSevice($checkTheInnerEyeData);
            if($deliveryPlace == 1 && $eseInfo["err_code"] != 0){
                return $this->apiReturn(100, "该公司信息不存在于天眼查中，请检查后重试");
            }
        }
        
        
        $isLixin = !$taxinfo["first_nature"] || !$taxinfo["com_industry"] || $taxinfo["product_use_classone_sn"] == -1  || $taxinfo["product_use_classtwo_sn"] == -1 ;
        $isShenMao = !$taxinfo["first_nature"] || !$taxinfo["com_industry"] || $taxinfo["product_use_classone_sn"] == -1  || $taxinfo["product_use_classtwo_sn"] == -1   || !$taxinfo["business_license"] || !$isExistsUserProtocol;

        if($taxinfo["inv_area"] != 3  || ($deliveryPlace == 2 && $isShenMao)  ){
            //非个人发票
            if($deliveryPlace == 1 && $isLixin){
                return [
                            "err_code" => 99,
                            "err_msg" => "请完善发票资料",
                            "data"  => [
                                    "com_name"=>$taxinfo["tax_title"],
                                    "company_nature"=>$taxinfo["first_nature"],
                                    "com_industry"=>$taxinfo["com_industry"],
                                    "product_use_classone_sn"=>$taxinfo["product_use_classone_sn"],
                                    "product_use_classtwo_sn"=>$taxinfo["product_use_classtwo_sn"],
                                    "business_license_src"=>1,
                                    "protocol_url"=>1,
                                ]
                    ];
            }elseif($deliveryPlace == 2 && $isShenMao){
                return [
                            "err_code" => 99,
                            "err_msg" => "请完善发票资料",
                            "data"  => [
                                    "com_name"=>$taxinfo["tax_title"],
                                    "company_nature"=>$taxinfo["first_nature"],
                                    "first_nature"=>$taxinfo["first_nature"],
                                    "com_industry"=>$taxinfo["com_industry"],
                                    "product_use_classone_sn"=>$taxinfo["product_use_classone_sn"],
                                    "product_use_classtwo_sn"=>$taxinfo["product_use_classtwo_sn"],
                                    "business_license_src"=>$taxinfo["business_license"]?1:0,
                                    "protocol_url"=>$isExistsUserProtocol ? 1 : 0,
                                ]
                    ];
            }
            
        }

        
        

        $data = array(
            "tax_id" => $tax_id,
            "delivery_place" => $deliveryPlace,
            "address_id" => $address_id,
        );
        $url = C("crmDomain");
        $res = post_curl(sprintf("%s%s",rtrim($url,"/"),"/open/checkoutCompanyInfo"),$data);
        $res = json_decode($res,true);
        if(!empty($res) && $res["code"] != 0){
            return $this->apiReturn(1, $res["msg"]);
        }
    }


}
