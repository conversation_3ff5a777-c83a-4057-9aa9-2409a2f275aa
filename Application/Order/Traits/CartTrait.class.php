<?php
namespace Order\Traits;

use Order\Exception\CartException;

trait CartTrait {



    public function getUserTax($user_id=0){
        //不开发票 tax_id=0
        $tax_id = isset($_REQUEST["tax_id"]) ? intval($_REQUEST["tax_id"]) : -1;
        $arr = [
            "invoice"=>true,
            "special_invoice"=>true,
        ];

        if(!$user_id){
            return ["err_code"=>-1,"data"=>$arr];
        }
        $map=[];
        if($tax_id == -1) {
            //订单确认页面刷新 取默认发票
            //获取用户默认的发票信息 增值税发票
            $map["user_id"] = $user_id;
            $map["is_default"] = 1;
            $map["inv_type"] = ["in", [3, 4]];
        }elseif($tax_id > 0){
            //查找用户发票信息
            $map["user_id"] = $user_id;
            $map["tax_id"] = $tax_id;
            $map["inv_type"] = ["in",[3,4]];
        }else{
            return ["err_code"=>-1,"data"=>$arr];
        }
        $taxinfo = D("Taxinfo")->where($map)->field("tax_title,inv_type")->find();
        if($taxinfo){
            if($taxinfo["inv_type"] == 4){
                $arr["invoice"] = $taxinfo["tax_title"];
                $arr["special_invoice"] = true;
            }else if($taxinfo["inv_type"] == 3){
                $arr["invoice"] = true;
                $arr["special_invoice"] = $taxinfo["tax_title"];
            }
            return ["err_code"=>0,"data"=>$arr];
        }else{
            return ["err_code"=>-1,"data"=>$arr];
        }

    }


    /*
     * 添加购物车后 计算赠品
     */
    public function addShoppingCartGift($cart_id,$goodsInfo=[],$priceInfo=[]){
        try{
            if($goodsInfo["has_gift_activity"] != 1){
                return true;
            }
            $gift_activity = isset($goodsInfo["gift_activity"]) ? $goodsInfo["gift_activity"] :null;
            if (!$gift_activity){
                return true;
            }
//            dump($gift_activity);
            $is_c = I("type",0,"intval");
            if(($gift_activity["can_admin_order"] == 0 && $is_c > 1) ){
                return;
            }
            if(isset($gift_activity["can_admin_order"])){
                D("OrderItemsGift")->updateOrCreateByCart($cart_id,$goodsInfo,$priceInfo);
            }
        }catch(\Exception $e){
//            dump($e->getMessage());
        }
    }


    /*
     * 获取购物车内商品满足的赠品信息
     */
    public function getCartGift($cart=[],$rules=[]){
        try{
            $manz = [];
            if(empty($cart) && empty($rules)){
                return $manz;
            }
            if(empty($rules)){
                $rules = D("OrderItemsGift")->getGiftByCart($cart);
            }


            $giftAmount = [];
            $giftRules = [];
            foreach($rules as $k=>$item){
                $giftAmount[$item["activity_id"]] += $item["goods_amount"];
                if(!in_array($item["activity_id"],$rules)){
                    $giftRules[$item["activity_id"]] = json_decode($item["rules"],true);
                }
            }

//            dump($giftAmount);
//            dump($giftRules);

            $func = function($activityRules,$amount,$activityId,&$manz){
                $bool = true;
                foreach($activityRules as $itemsRuels){
                    if($itemsRuels["num"] <= 0 || $itemsRuels["total"] <= 0) continue;
                    if(floatval($amount) >= floatval($itemsRuels["amount"])){
                        unset($itemsRuels["add_time"]);
                        $manz[] = $itemsRuels;
                        $bool = false;
                        break;
                    }
                }
                return $bool;
            };

            foreach($giftAmount as $activityId => $amount){
               foreach($giftRules as $rulesActivityId=>$activityRules){
                    if ($activityId == $rulesActivityId){
                        $last_names = array_column($activityRules,'amount');
                        array_multisort($last_names,SORT_DESC,$activityRules);
                        if($func($activityRules,$amount,$activityId,$manz) == true){
                            break;
                        }
                   }
               }
            }
            return $manz;

        }catch(\Exception $e){
            return [];
        }
    }


    public  function getUserAddressInfo($user_id, $address_id){
        return D('Address/UserAddress')->getUserInfo( $user_id,$address_id);
    }


}