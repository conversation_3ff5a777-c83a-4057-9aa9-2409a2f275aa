<?php

namespace Order\Traits;

use Exception;

trait SampleTrait
{

    // 校验样片单
    // 30天内 同账号 同一型号可申请一次（不含被取消的样品订单），否则提示：近30天内已申请过该型号，不可重复申请
    // 30天内 同账号 不同型号最多可申请3次（不含被取消的样品订单），否则提示：额度不足，近30天内已申请过3次。
    public function checkSampleOrder($params)
    {
        // 判断样片库存是否满足
        $s_goods_data = S_sample_goods($params['goods_id']);

        if (!$s_goods_data) {
            return $this->apiReturn(26109, '当前样片缓存不存在，请联系技术人员');
        }

        $s_goods_data = json_decode($s_goods_data, true);

        if (!$s_goods_data['sample_stock'] || $s_goods_data['sample_stock'] < $params['apply_num']) {
            return $this->apiReturn(26109, '样片库存不足，当前库存为'. $s_goods_data['sample_stock']);
        }

        $start_time = time() - 30 * 86400;
        $end_time = time();

        $OrderModel = D('Order');

        // 获取当前用户未取消的样片单
        $map = [];
        $map['o.user_id'] = $params['user_id'];
        $map['o.status'] = ['neq', -1]; // 过滤取消的
        $map['o.create_time'] = ['between', [$start_time, $end_time]];
        $map['oe.business_type'] = 1; // 样片单

        $order_ids = $OrderModel->alias('o')
            ->join('INNER JOIN ' . C('DB_PREFIX') . 'order_extend oe ON o.order_id = oe.order_id')
            ->where($map)
            ->getField('o.order_id', true);

        if (count($order_ids) == 0) {
            return $this->apiReturn(0, '校验成功');
        }

        if (count($order_ids) >= 3) {
            return $this->apiReturn(26110, '额度不足，近30天内已申请过3次');
        }

        // 未申请3次，则判断是否有申请过当前型号
        $OrderItemsModel = D('OrderItems');

        $item_map = [];
        $item_map['order_id'] = ['in', $order_ids];
        $item_map['goods_id'] = $params['goods_id'];

        $count = $OrderItemsModel->where($item_map)->count();

        if ($count) {
            return $this->apiReturn(26111, '近30天内已申请过该型号，不可重复申请');
        }

        return $this->apiReturn(0, '校验成功');
    }

    // 创建样片单
    public function createSampleOrder($params)
    {
        $OrderModel           = D('Order');
        $OrderExtendModel     = D('OrderExtend');
        $OrderSampleInfoModel = D('OrderSampleInfo');
        $OrderItemsModel      = D('OrderItems');
        $OrderItemsExtModel   = D('OrderItemsExt');
        $OrderPriceModel      = D('OrderPrice');
        $OrderAddressModel    = D('OrderAddress');
        $OrderInvoiceModel    = D('OrderInvoice');
        $OrderActionLogModel  = D('OrderActionLog');
        $CmsModel             = D('Cms');
        $UserMainModel        = D('UserMain');

        $currency = 1; // rmb
        $freight_fee = 10; // 固定运费 

        $res = $this->getFinalGoods($params['goods_id'], $params['apply_num'], $currency); // 获取最终商品信息

        if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

        $goods = $res['data']['goods_info'];
        $initial_prices = ladder_final_price($res['data'], $currency, false); // 获取优惠前 原始价格
        $order_goods_type = in_array($goods['goods_type'], C('ORDER_GOODS_TYPE_MAP.1')) ? 1 : 2; 

        $order_sn = $OrderModel->findSn(1, 'SZ'); // 生成订单编号
        $_REQUEST['is_liexin'] = 1; // 防止校验CSRF
        $order_source = A('Order/Cart')->order_source();

        $CrmUserModel = D('Crm/User');
        $sale_id = $CrmUserModel->getSaleId($params['user_id']);
        $sale_id = $sale_id ? $sale_id : 0;
        $is_auto_assign_sale = 0; // 是否为自动分配销售

        if (!$sale_id) {
            $sale_id = C('fixed_order_sale_id');
            $is_auto_assign_sale = 1;
        }

        $OrderModel->startTrans();

        // 添加用户标签
        $count_where['user_id'] = $params['user_id'];
        $count_where['status']  = ['neq', -1]; // 过滤已取消订单
        $count_where['is_type'] = 0; // 真实订单
        $order_count = $OrderModel->where($count_where)->count();
        $send_remark = '';

        if ($order_count) {
            $user_tags['is_new'] = 2; // 老用户
        } else {
            $user_tags['is_new'] = 1; // 新用户
            $send_remark = '新用户';
        }

        $res = $UserMainModel->where(array('user_id' => $params['user_id']))->save($user_tags);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(25027, '用户标签更新失败');
        }

        // 添加用户标签到缓存
        $s_user_info = S_user($params['user_id']);
        $s_user_info['is_new'] = $user_tags['is_new'];

        S_user($params['user_id'], $s_user_info);

        $order_amount = $freight_fee;

        /***  订单主表  ***/
        $data = array(
            'order_sn'         => $order_sn,
            'user_id'          => $params['user_id'],
            'order_amount'     => $order_amount,
            'order_goods_type' => 1,
            'order_source'     => $order_source,
            'sale_id'          => $sale_id,
        );

        $order_id = $OrderModel->createOrder($data);
        if ($order_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26107, '生成订单失败', $data);
        }

        /***  扩展信息  ***/
        $data = array(
            'order_id'              => $order_id,
            'client_ip'             => get_client_ip(0, true),
            'order_type'            => 0,
            'business_type'         => 1,
            'sensors_syn'           => 1, //神策推送
            'send_remark'           => $send_remark,
        );

        $res = $OrderExtendModel->createExtent($data);
        if ($res == false) {
            $OrderModel->rollback();
            return $this->apiReturn(26108, '生成订单扩展信息失败', $data);
        }

        // 项目信息
        $params['project_info']['order_id'] = $order_id;
        $params['project_info']['project_production_time'] = strtotime($params['project_info']['project_production_time']);

        $sampleResult = $OrderSampleInfoModel->add($params['project_info']);
        if ($sampleResult == false) {
            $OrderModel->rollback();
            return $this->apiReturn(26109, '生成样片项目信息失败', $params['project_info']);
        }

        /***  订单明细  ***/
        $delivery_time = is_array($goods['delivery_time']) ? $goods['delivery_time'][1] : $goods['delivery_time'];
  
        $data = array(
            'order_id'         => $order_id,
            'user_id'          => $params['user_id'],
            'goods_id'         => $params['goods_id'],
            'brand_id'         => intval($goods['brand_id']),
            'supplier_id'      => $goods['supplier_id'],
            'supplier_name'    => $goods['supplier_name'],
            'goods_name'       => $goods['goods_name'],
            'sku_name'         => $goods['sku_name'],
            'brand_name'       => $goods['brand_name'],
            'goods_number'     => $params['apply_num'],
            'goods_price'      => 0, // 样片单价 0
            'initial_price'    => $initial_prices['price'],
            'goods_type'       => $goods['goods_type'],
            'ac_type'          => $goods['ac_type'],
            'delivery_time'    => $delivery_time,
            'order_goods_type' => $order_goods_type,
            'is_gift'          => 1, // 默认赠品
            'is_purchase'      => -1, // 默认不需要采购
        );

        // 若为自营商品，则默认采购员为平台
        if (in_array($goods['goods_type'], [3, 4])) {
            $data['buyer_id'] = C('SELF_SAMPLE_PUR') ? C('SELF_SAMPLE_PUR') : 0;
        } else {// 联营取商品自带的采购
            $v["buyer_id"] = !empty($info['encoded']) ? $info['encoded'] : 0; 
        }

        // $push_sale_data = [];
        // $push_sale_data['goods_list'][0]['goods_name'] = $goods['goods_name'];
        // $push_sale_data['goods_list'][0]['brand_name'] = $goods['brand_name'];
        // $push_sale_data['goods_list'][0]['uni_key']    = $goods['brand_name'];

        // // 添加到销售商品库
        // $res = post_curl(SALE_DOMAIN . '/inner/goods/createMultiGoods', $push_sale_data);

        $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件
        // \Think\Log::write('样片商品添加到销售商品库，参数：' . json_encode($push_sale_data) . '返回数据：' . $res, 'INFO', '', $path);

        // $res = json_decode($res, true);

        // if ($res && $res['code'] == 0) {
        //     $goods_lib_data = [];

        //     $goods_lib_data[$goods['brand_name']]['sale_goods_id'] = $res['data']['bind_list'][0]['goods_id'];
        //     $goods_lib_data[$goods['brand_name']]['goods_sn'] = $res['data']['bind_list'][0]['goods_sn'];
        //     $goods_lib_data[$goods['brand_name']]['brand_id'] = $res['data']['bind_list'][0]['brand_id'];

        //     $data['sale_goods_id'] = isset($goods_lib_data[$data['brand_name']]) ? $goods_lib_data[$data['brand_name']]['sale_goods_id'] : 0;
        //     $data['brand_id']      = isset($goods_lib_data[$data['brand_name']]) ? $goods_lib_data[$data['brand_name']]['brand_id'] : 0;
        //     $data['goods_sn']      = isset($goods_lib_data[$data['brand_name']]) ? $goods_lib_data[$data['brand_name']]['goods_sn'] : '';
        // }

        $rec_id = $OrderItemsModel->createOrderItems($data);
        if ($rec_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26009, '生成订单明细失败', $data);
        }

        // 明细扩展表
        $goods_ext = array(
            'order_id'      => $order_id,
            'rec_id'        => $rec_id,
            'goods_moq'     => isset($goods['min_buy']) ? $goods['min_buy'] : 1,
            'goods_spq'     => isset($goods['min_mpq']) ? $goods['min_mpq'] : 1,
            'goods_packing' => isset($goods['packing_name']) ? $goods['packing_name'] : '',
            'goods_encap'   => isset($goods['encap']) ? $goods['encap'] : '',
            'goods_class'   => isset($goods['class2_name']) ? $goods['class2_name'] : '',
            'goods_unit'    => isset($goods['goods_unit_name']) ? $goods['goods_unit_name'] : '',
        );

        $res = $OrderItemsExtModel->add($goods_ext);
        if ($rec_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26009, '生成订单明细扩展表失败', $data);
        }

        /***  订单金额  ***/
        $price_id = $OrderPriceModel->createOrderPrice($order_id, 0, 1, $currency, $order_sn); // 商品总额
        if ($price_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26010, '生成订单金额失败');
        }

        $OrderPriceModel->createOrderPrice($order_id, 10, 3, $currency, $order_sn); // 运费

        /***  订单收货地址  ***/
        $data = array();
        $address_info = $this->getAddress($params['address_id']);

        if ($address_info['err_code'] != 0) {
            $OrderModel->rollback();
            return $this->apiReturn($address_info['err_code'], $address_info['err_msg']);
        }

        $data = $address_info['data'];

        $data['area_code']           = $data['zipcode'];
        $data['address']             = $data['detail_address'];
        $data['order_shipping_type'] = 1;
        $data['order_id']            = $order_id;
        $data['order_sn']            = $order_sn;
        $data['address_id']          = $params['address_id'];
        $data['address_type']        = 1;

        $order_address_id = $OrderAddressModel->createOrderAddress($data);
        if ($order_address_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26011, '生成订单收货地址失败');
        }

        /***  订单发票  ***/
        $data = array();
        $data['tax_id']    = 0;
        $data['nike_name'] = !empty($s_user_info['nike_name']) ? $s_user_info['nike_name'] : '';
        $data['order_id']  = $order_id;
        $data['order_sn']  = $order_sn;
        $data['inv_type']  = 1; // 不开票
        $order_invoice_id  = $OrderInvoiceModel->createOrderInvoice($data);
        if ($order_invoice_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26012, '生成订单发票信息失败');
        }

        /***  更新样片库存  ***/
        $res = D('Sample')->decSampleStock($params['goods_id'], $params['apply_num']);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26012, '扣减样片库存失败');
        }

        $s_goods_data = json_decode(S_sample_goods($params['goods_id']), true);
        $s_goods_data['sample_stock'] = $s_goods_data['sample_stock'] - $params['apply_num'];
        $s_goods_data['update_time'] = time();

        S_sample_goods($params['goods_id'], $s_goods_data);

        $OrderModel->commit(); // 提交事务

        try {
            //通知内部人员
            $data = array(
                'data' => array(
                    'order_sn' => $order_sn,
                )
            );

            $event   = '提交样片订单';
            $keyword = 'self-sample-order'; // 自营样片消息模板

            $data = json_encode($data);

            $to_user   = $CmsModel->getUserWebUserId($sale_id); // 后台业务员对应前台uid
            $sale_info = $CmsModel->getUserInfo($sale_id);
 
            $event .= '，订单已分配给客服：' . $sale_info['name'];

            $this->sendOrderMsg($keyword, $data, $to_user); // 推送短信

            $OrderActionLogModel->addLog($order_id, $params['user_id'], 1, $event); // 操作记录

            // 自动分配销售需同步到CRM
            if ($is_auto_assign_sale && strpos($_SERVER['HTTP_REFERER'], 'sz') === false) { // 非测试环境下执行
                $crm_params = [];
                $crm_params['customer_id']   = 0;
                $crm_params['user_id']       = $params['user_id'];
                $crm_params['department_id'] = $sale_info['department_id'];
                $crm_params['sale_id']       = $sale_id;
                $crm_params['sale_name']     = $sale_info['name'];
                $crm_params['operator_id']   = 1000;
                $crm_params['is_add_order']  = 1;

                $res = json_decode(post_curl(CRM_V2_DOMAIN . '/open/updateSaleByOrder', $crm_params), true);

                if (!$res || $res['code'] != 0) {
                    \Think\Log::write('新用户下样片单自动分配销售，推送到CRM失败', 'INFO', '', $path);
                }
            }
        } catch (Exception $e) {
        }

        $datas = [
            'order_id' => $order_id,
            'order_sn' => $order_sn,
        ];
        
        return $this->apiReturn(0, '生成成功', $datas);
    }

}