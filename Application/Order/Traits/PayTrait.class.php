<?php
namespace Order\Traits;

use Order\Model\OrderModel;

trait PayTrait{

    // 接口日志记录
    public function payTraitRecord($info)
    {
        try{
            \Think\Log::write($info, INFO, '', "./Application\Runtime\Logs/Order/payTrait/20_06_01.log");
            $path = C('LOG_PATH')."payTrait".'/'.date('y_m_d').'.log'; // 接口日志文件
            dump($path);
            \Think\Log::write($info, INFO, '', $path);
        }catch(\Exception $e){
//dump($e->getMessage());
        }

    }

    /*
     * 团购订单  支付完成后 修改订单相关状态
     * 团购拼团成功后 修改wms_syn = 1
     *
     */
    public function updateUserGroupOrderInfo($orderInfo = []){
        try{
            if($orderInfo["order_type_extend"] != 1) return true;
            $masterOrder = M("UserGroup")->where(["order_id"=>$orderInfo["order_id"]])->count("id");
            if($masterOrder){
                //主订单   团购发起者
                $current_time = time();
                $limited_time = rand($current_time+24*3600,$current_time+48*3600);
                $redis = new \Redis();
                $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT') ?: 6379);
                $redis->auth(C('REDIS_PASSWORD') ?: '');
                //团购主订单发起  插入订单到期时间 供脚本使用
                $res = $redis->zAdd("user_tuangou_order",$limited_time,$orderInfo["order_id"]);
                $redis->close();
                M("UserGroup")->where(["order_id"=>$orderInfo["order_id"]])->save(["status"=>1,"limited_time"=>$limited_time]);
            }else{
                //分享的团购链接购买  团购拼单  子单
                $order_id = $orderInfo["order_id"];//子订单
                $childOrder = M("UserGroupJoin")->where(["order_id"=>$order_id])->count("id");
                if(!$childOrder) {
                    throw new \Exception(sprintf("没找到团购订单信息  订单id：%s",$order_id));
                }
                //查找主订单
                M("UserGroupJoin")->where(["order_id"=>$order_id])->save(["status"=>1]);
                $childOrder = M("UserGroupJoin")->where(["order_id"=>$order_id])->field("group_id")->find();
                if(!$childOrder) throw new \Exception(sprintf("UserGroupJoin表订单%s,没找到group_id",$order_id));
                $masterOrder = M("UserGroup")->where(["id"=>$childOrder["group_id"]])->find();
                $masterOrderId = $masterOrder["order_id"];
                if(!$masterOrder || !$masterOrderId){
                    throw new \Exception(sprintf("UserGroup表该订单没找到 group_id :%s",$childOrder["group_id"]));
                }
                //更改该订单状态为完成
                $map["order_id"] = ["in",implode(",",[$masterOrderId,$order_id])];
                // $bk = D("Order")->where($map)->save(["status_extend"=>16,"wms_syn"=>1]);
                $bk = D("Order")->where($map)->save(["status_extend"=>16]);

                if($bk === false){
                    throw new \Exception(sprintf("团购完成，修改团购订单状态和wms_syn=1失败,订单id %s ,%s",$order_id,$masterOrderId));
                }


            }
        }catch(\Exception $e){
            $request = json_encode(I("request.",""));
            $content = sprintf("用户团购下单告警:请求参数:%s,user_id:%s,order_id:%s,异常:%s",$request,cookie("uid"),$orderInfo["order_id"],$e->getMessage());
            $data = array(
                'msgtype' => "text",
                'text' => array(
                    'content' => "用户团购下单告警:{$content}",
                ),
            );
            $data = json_encode($data);
            $url = "https://oapi.dingtalk.com/robot/send?access_token=a13d6128de5f918fc0f0a4cef70cab8b1962fca709649f7856aea4b1ca4dddf5";
            $bk = post_curl($url, $data, array('Content-Type:application/json;charset=UTF-8'));
        }
    }


    public function sendTuanGouMsg($orderInfo=[]){
        try{
            if($orderInfo["order_type_extend"] != 1) return true;
            $to_user = [];
            $masterOrder = M("UserGroup")->where(["order_id"=>$orderInfo["order_id"],"status"=>1])->find();
            if($masterOrder) {
                //主订单   团购发起者
                array_push($to_user,$orderInfo['user_id']);
                $childOrder = M("UserGroupJoin")->where(["group_id"=>$masterOrder["id"]])->find();
                if($childOrder){
                    $childOrder["user_id"] && array_push($to_user,$orderInfo['user_id']);
                }
            }else{
                //分享的团购链接购买  团购拼单  子单
                $childOrder = M("UserGroupJoin")->where(["order_id"=>$orderInfo["order_id"],"status"=>1])->find();
                if($childOrder) {
                    $childOrder['user_id'] && array_push($to_user,$childOrder['user_id']);
                }
                $masterOrder = M("UserGroup")->where(["id"=>$childOrder["group_id"],"status"=>1])->find();
                $masterOrder["user_id"] && array_push($to_user,$masterOrder['user_id']);
            }
            if(empty($to_user) || count($to_user) < 2) return true;
            $send_data['data'] = "";
            $send_data['wechat_data'] = "";
            $send_data = json_encode($send_data);
            $this->sendOrderMsg("tuangou_order_ok", $send_data, $to_user);
        }catch(\Exception $e){

        }
    }


    /**添加抽奖资格
     * remark ----->多个活动 根据赠送规则 判断 送哪些活动
     * @param $user_id
     * @param $order_id
     * @return bool
     */
    public function increase_draw_qualify_by_order($user_id, $order_id){


        $draw_res['is_sent_qualify'] = isset($is_sent_qualify) ? $is_sent_qualify : false;
        $draw_res['activity_urls'] = isset($activity_urls) ? $activity_urls : "";
        $draw_res['banner_urls'] = isset($banner_urls) ? $banner_urls : "";
        return $draw_res;


        
        $is_sent_qualify = false;
        $activity_urls = array();
        $banner_urls = array();
        $result = $this->getCurrentActivities();
        //查询订单金额
        $orderInfo = D('Order')->where(["order_id"=>16852])->find();
        $orderInfo = !empty($orderInfo) ? $orderInfo : [];
        $orderAmonut = !empty($orderInfo["order_amount"]) ? $orderInfo["order_amount"] : 0;
        $orderSn = !empty($orderInfo["order_sn"]) ? $orderInfo["order_sn"] : "";
        \Think\Log::write(sprintf("increase_draw_qualify_by_order:订单号:%s,uid:%s,订单金额:%s",$orderSn,$user_id,$orderAmonut),'WARN');
        \Think\Log::write(sprintf("orderinfo:%s",json_encode($orderInfo)),'WARN');
        \Think\Log::write(sprintf("getCurrentActivities:%s",json_encode($result)),'WARN');
        
        if(0==$result['errcode']){
            foreach ($result['data'] as $value){
                if($value['lottery_id'] == 282){
                    // continue;
                }
                // if($value['lottery_id'] == 282 && $orderAmonut < 100 && ($orderInfo["sale_pay_status"] != 2)){
                //     continue;
                // }
                $rules_arr = explode(',',$value['qualify_get_rule']);
                if(in_array(4,$rules_arr)){
                    \Think\Log::write(sprintf("getReceivedQualifyDuringActivity:lottery_id:%s",$value['lottery_id']),'WARN');
                    $hasReceived = json_decode(get_curl(MARKET_DOMAIN . '/webapi/getReceivedQualifyDuringActivity',array('user_id'=>$user_id,'lottery_id'=>$value['lottery_id'])),true);
                    \Think\Log::write(sprintf("getReceivedQualifyDuringActivity:返回结果:%s",json_encode($hasReceived)),'WARN');
                    if($hasReceived['data']['from_order_count']<$value['qualify_order']){
                        //发放活动抽奖机会  下单只有一共送几次  每一单直送一次  所以次数为固定值
                        \Think\Log::write(sprintf("increaseQualifyToUser:请求参数:uid:%s,lottery_id:%s,",$user_id,$value['lottery_id']),'WARN');
                        $send_qualify = json_decode(get_curl(MARKET_DOMAIN . '/webapi/increaseQualifyToUser', array('user_id' => $user_id, 'lottery_id' => $value['lottery_id'], 'increase_draw_count' => 1, 'order_id' => $order_id,'increase_type' => 4)),true);
                        \Think\Log::write(sprintf("increaseQualifyToUser:返回结果:%s",json_encode($send_qualify)),'WARN');
                        if($send_qualify && ($send_qualify['errcode']==0)){
                            $is_sent_qualify = true;
                            // array_push($activity_urls,$value['activity_url']);
                            if(!empty($value['lottery_name']) && !empty($value['activity_url']) && !empty($value['pay_suc_img_url'])){
                                $activity_urls[$value['lottery_name']] = $value['activity_url'];
                                $banner_urls[$value['lottery_id']] = $value['pay_suc_img_url'];
                            }
                        }
                    }
                }
            }
        }
        $draw_res['is_sent_qualify'] = isset($is_sent_qualify) ? $is_sent_qualify : false;
        $draw_res['activity_urls'] = isset($activity_urls) ? $activity_urls : "";
        $draw_res['banner_urls'] = isset($banner_urls) ? $banner_urls : "";
        return $draw_res;
    }

    /*
    手动调试送优惠券方法
*/
    public function send_test(){
        exit;
        $user_id    =  I('request.user_id', 0, 'intval');
        $order_id   =  I('request.order_id', 0, 'intval');

        $this->send_coupon_res($user_id,$order_id);

    }



    /*
     * 处理邀请好友下单后 更改相关状态
     */
    public function updateYaoHaoYouOrder($order_id=0){
        try{

            $RbmqModel = D('Common/Rbmq');
            $a= $RbmqModel->connect('WMS_RBMQ_CONFIG')->queue(C("YAOHAOYOU_ORDER_QUEUE"));
            $push["order_id"] = $order_id;
            $bk = $a->push($push, C("YAOHAOYOU_ORDER_QUEUE"));

//            $bk = D("UserInvitefriend")->where(["type"=>1,"type_id"=>$order_id])->save(["status"=>1]);
//            if($bk === false){
//                throw new \Exception("更新邀请好友表数据失败");
//            }
        }catch(\Exception $e){
            $this->payTraitRecord(sprintf("邀请好友下单处理失败，订单id：%s,异常原因：%s",$order_id,$e->getMessage()));
            $this->dingDingGJ(
                "邀好友下单告警",
                sprintf("邀请好友下单处理失败，订单id：%s,异常原因：%s",$order_id,$e->getMessage())
            );

        }
    }

    /*
     * 查找邀好友活动记录表 是否含有用户相关的订单
     */
    public function getUserInviteFriend($order_id=0){
        $count = D("UserInvitefriend")->where(["type_id"=>$order_id,"type"=>1])->count();
        return $count;
    }

    /*
     * 支付成功给用户发送消息
     */
    public function paySuccessSendMsg($info,$order_amount){
        try {
            $OrderItemsModel = D('OrderItems');
//            $items = $OrderItemsModel->getAllName($info['order_id']);
//            if (intval(count($items)) !== 1) {
//                $items = implode(',', $items);
//            } else {
//                $items = $items[0];
//            }
            $keyword = 'order-pay-success';
            $to_user = $info['user_id']; // 客户
            $wechat_data = C("WECHAT_TEMPLATE.{$keyword}");
            $wechat_data['orderProductName']['value'] = "";
            $wechat_data['orderMoneySum']['value'] = price_format($order_amount, C('PLACE_CURRENCY_MAPPING.'.$info['currency'])); // 订单金额
            $order_res = getOrderSpecialDetailsByOrderId($info['order_id']);
            $order_res['order_sn'] = $info['order_sn'];
            $order_res['order_id'] = $info['order_id'];
            $order_res['url'] = getShortUrl($info['order_id']);
            $order_res['create_time'] = $info['create_time'];
            $order_res['order_amount'] = price_format($info['order_amount'], C('PLACE_CURRENCY_MAPPING.'.$info['currency']));
            $send_data['data'] = $order_res;
            $send_data['wechat_data'] = $wechat_data;
            $send_data = json_encode($send_data);

            if($info["order_type_extend"] == 1){
                //团购订单
//                        $this->sendTuanGouMsg($info);
            }else{
                $this->sendOrderMsg($keyword, $send_data, $to_user);
            }
        } catch (\Exception $e) {
        }
    }

    /*
     * 代付尾款提醒
     */
    public function sendPaidBlancePayMentMsg($info,$pay_type){
        try {
            if($pay_type != 2) return true;
            $OrderPriceModel = D('OrderPrice');
            $topay_amount = $OrderPriceModel->getToPay($info['order_id']);
            $keyword = "order-advance-prepaid";
            $to_user = $info['user_id']; // 客户
            $order_res["order_sn"] = $info['order_sn'];
            $order_res["order_id"] = $info['order_id'];
            $send_data['data'] = $order_res;
            $wechat_data["keyword1"]["value"] = $info['order_sn'];
            $wechat_data["keyword2"]["value"] = $topay_amount;
            $send_data['wechat_data'] = $wechat_data;
            $send_data = json_encode($send_data);
            $this->sendOrderMsg($keyword, $send_data, $to_user);
        } catch (\Exception $e) {
        }
    }

    // 订单支付完成后，推送消息给对应的销售
    public function sendPaidRemindToSales($info)
    {
        try {
            $keyword = "order_paid_remind_sales";

            $CmsModel = D('Cms');
            $to_user  = $CmsModel->getUserWebUserId($info['sale_id']); // 后台业务员对应前台uid

            $order_res["order_sn"] = $info['order_sn'];
            $send_data['data'] = $order_res;
            $send_data = json_encode($send_data);
           
            $this->sendOrderMsg($keyword, $send_data, $to_user);
        } catch (\Exception $e) {
        }
    }

}