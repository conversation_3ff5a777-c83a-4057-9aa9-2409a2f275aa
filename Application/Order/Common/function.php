<?php
/**
 * 旧订单获取新订单状态名
 * @param  [type]  $order_status    [description]
 * @param  [type]  $pay_status      [description]
 * @param  integer $shipping_status [description]
 * @return [type]                   [description]
 */
function status_to_name($order_status, $pay_status, $shipping_status = 0)
{
    $status_mapping = C('ORDER_STATUS_MAPPING');
    $name = '';
    foreach ($status_mapping as &$v) {
        $order = is_null($v['1']) ? null : explode(',', $v['1']);
        $pay = is_null($v['2']) ? null : explode(',', $v['2']);
        if ((in_array($order_status, $order) || is_null($order)) && (in_array($pay_status, $pay) || is_null($pay))) {
            $name = $v['0'];
            break;
        }
    }
    return $name;
}

/**
 * 旧订单获取新订单状态ID
 * @param  [type]  $order_status    [description]
 * @param  [type]  $pay_status      [description]
 * @param  integer $shipping_status [description]
 * @return [type]                   [description]
 */
function status_to_id($order_status, $pay_status, $shipping_status = 0)
{
    $status_mapping = C('ORDER_STATUS_MAPPING');
    $id = '';
    foreach ($status_mapping as $k => &$v) {
        $order = is_null($v['1']) ? null : explode(',', $v['1']);
        $pay = is_null($v['2']) ? null : explode(',', $v['2']);

        if ((in_array($order_status, $order) || is_null($order)) && (in_array($pay_status, $pay) || is_null($pay))) {
            $id = $k;
            break;
        }
    }
    return $id;
}

// /**
//  * 价格阶梯乘以系数转换
//  * @param  [type] $tiered      阶梯数组
//  * @param  [type] $supplier_id 供应商ID
//  * @param  [type] $goods_type  商品类型
//  * @return [type]              [description]
//  */
// function ladder_transform($tiered, $supplier_id, $goods_type, $discount_ratio)
// {
//     $supplier = getSupplier($supplier_id);
//     $coeff = current($supplier['price_json']);
//     foreach ($tiered as $k => &$val) {
//         // dump($tiered);
//         if (isset($val['price_cn']) || isset($val['price_us'])) {//新格式
//             $hk = $val['price_us'];
//             $cn = $val['price_us'];
//             if (in_array($goods_type, array(1,2))) {//联营处理价格系数
//                 if (!empty($coeff)) {
//                     $hk = $hk * $coeff['hk'];
//                     $cn = $cn * $coeff['cn'];
//                 }
//                 if (!empty($discount_ratio) && $discount_ratio != 100) {
//                     $ratio = $discount_ratio / 100;
//                     $hk_ac = $hk * $ratio;
//                     $cn_ac = $cn * $ratio;
//                     // $v['price_us_ac'] = price_format($hk_ac, 0, 4);//目前不支持美元活动
//                     $val['price_ac'] = price_format($cn_ac, 0, 4);
//                 }
//             }

//             $val['price_us'] = price_format($hk, 0, 4);
//             $val['price_cn'] = price_format($cn, 0, 4);

//         } else {//兼容旧格式上线1周后可废除
//             if (in_array($goods_type, array(1,2))) {//联营处理价格系数
//                 if (!empty($coeff)) {
//                     $hk = price_format($val[1] * $coeff['hk'], 0, 4);
//                     $cn = price_format($val[1] * $coeff['cn'], 0, 4);
//                     $val[1] = $hk;
//                     $val[2] = $cn;
//                 }
//             }
//             $val['price_us'] = price_format($val[1], 0, 4);
//             $val['price_cn'] = price_format($val[2], 0, 4);
//             unset($val[1], $val[2]);
//         }
//     }
//     return $tiered;
// }

/**
 * 指定数量从阶梯中选择价格
 * @param  [type]  $ladder   价格阶梯
 * @param  [type]  $num      购买数量
 * @param  integer $currency 币种 1人民币 2美元
 * @return [type]            [description]
 */
function ladder_price($ladder, $num)
{
    $ind = null;
    foreach ($ladder as $k => $v) {
        if (isset($v['purchases'])) {
            $new = true;
            $purchases = 'purchases';
        } else {
            $new = false;
            $purchases = 0;
        }
        if ($num < $v[$purchases]) {
            break;
        }
        $ind = $k;
    }
    if (is_null($ind)) {
        $ladder_info = $ladder[0];
    } else {
        $ladder_info = $ladder[$ind];
    }
    return $ladder_info;
}

/**
 * 根据指定的价格系数和交货地进行换算
 * @param  [type]  $price  基础单价
 * @param  [type]  $supplier_id 供应商id
 * @param  integer $type   换算币种
 * @return [type]          [description]
 */
function transform_price($price, $supplier_id, $type = 1)
{
    static $currency_arr;
    if ($supplier_id == 17) {
        return $price;
    }
    $supplier = getSupplier($supplier_id);
    $rate = current($supplier['price_json']);

    // $rate = C('PRICE_MULTI.'.$dbname);
    if (empty($rate)) {
        return $price;
    }
    $currency = $type == 1 ? 'cn' : 'hk';
    if (empty($rate[$currency])) {
        return $price;
    }
    return $rate[$currency] * $price;
}

/**
 * 获取最终销售价（含利润）
 * @param  [type] $goods_type    商品类型 1专营 2联营
 * @param  [type] $ladder        阶梯
 * @param  [type] $num           数量
 * @param  [type] $currency      币种 1人民币 2美元
 * @param  [type] $supplier_id   供应商id（联营必填）
 * @return [type]                [description]
 */
function final_price($goods_type, $ladder, $num, $currency, $supplier_id = 0, $use_ac = true)
{
    //获取需要的阶梯价格
    $goods_price = ladder_price($ladder, $num, $currency, $supplier_id, $use_ac);//阶梯价

    $goods_price = price_format($goods_price, 0, 4);//小数点格式化
    return $goods_price;
}

/**
 * 新获取最终销售价
 * @param  [type] $goods [description]
 * @return [type]        [description]
 */
function ladder_final_price($goods, $currency = 1, $use_ac = true)
{
    // if ($currency == 1) {
    //     $field = $use_ac ? 'price_ac' : 'price_cn';
    // } else {
    //     $field = $use_ac ? 'price_ac_us' : 'price_us';
    // }

    if ($currency == 1) {
        $field = 'price_cn';
        if ($use_ac) {
            $field = isset($goods['price_ac']) ? 'price_ac' : 'price_cn';
        }
    } else {
        $field = 'price_us';
        if ($use_ac) {
            $field = isset($goods['price_ac_us']) ? 'price_ac_us' : 'price_us';
        }
    }

    $arr = [
        'price' => $goods[$field],
        'price_total' => isset($goods[$field.'_total']) ? $goods[$field.'_total'] : $goods[$field] * $goods['num'],
        'num' => $goods['num'],
    ];
    return $arr;
}

/**
 * 订单金额算法
 * @param  integer $goods_total         商品合计（4位小数）
 * @param  integer $preferential_price  优惠价格（2位小数）（针对商品降价）
 * @param  integer $shipping_price      物流价格（2位小数）
 * @param  integer $free_shipping_price 物流优惠价格（2位小数）（针对运费降价）
 * @param  integer $extend_fee          附加费（2位小数）
 * @return [type]                       [description]
 */
function orderAmount($goods_total = 0, $preferential_price = 0, $shipping_price = 0, $free_shipping_price = 0, $extend_fee = 0)
{
    //商品优惠后价格
    $finally_goods_total = bcsub($goods_total, $preferential_price, 4);
    if ($finally_goods_total < 0) {
        $finally_goods_total = 0;
        $preferential_price = $goods_total;
    }
    //物流优惠后价格
    $finally_shipping_price = bcsub($shipping_price, $free_shipping_price, 2);
    if ($finally_shipping_price < 0) {
        $finally_shipping_price = 0;
        $free_shipping_price = $shipping_price;
    }
    //附加费
    $finally_extend_fee = $extend_fee;

    $order_amount = price_format($finally_goods_total + $finally_shipping_price + $finally_extend_fee);
    $original_order_amount = $finally_goods_total + $finally_shipping_price;
  
    $preferential_price = price_format($preferential_price);
    // $exempt_price = price_format($exempt_price);
    $datas = array(
        'original_order_amount' => $original_order_amount, 
        'order_amount' => $order_amount,
        'goods_total' => $goods_total,
        'preferential_price' => $preferential_price,
        'shipping_price' => $shipping_price,
        'free_shipping_price' => $free_shipping_price,
        'extend_fee' => $extend_fee,
    );
    return $datas;
}
/**
 * 根据订单id{{data.order_sn}}获取相关信息
 * @param  [type]  $order_sn    [订单号]
 * @param  [type]  $create_time      [下单时间]
 * @param  integer $order_amount [订单总额]
 *
 * @param  integer $extra_charge_amount [附加费] 2
 * @param  integer $coupon_amount [优惠金额] -4
 * @param  integer $goods_amount [小计金额(货款)] 1
 *
 * @param  [type]  $search_name    [搜索型号]
 * @param  integer $goods_name [下单型号]
 * @param  integer $brand_name [型号制造商]
 * @param  integer $delivery_time [货期]
 * @param  integer $goods_price [含税单价]
 * @param  integer $goods_number [订货量]
 * @return [type]                   [description]
 */
function getOrderSpecialDetailsByOrderId($order_id)
{
    if (!$order_id) return false;
    $order_info = array();
    $OrderItemsModel = D('OrderItems');
    $OrderPriceModel = D('OrderPrice');

    $maps['order_id'] = array('eq', $order_id);
    $maps['price_type'] = array('in', '-4, 1, 2');
    $re = $OrderPriceModel->field('price, price_type, currency')->where($maps)->select();

    $res = array();
    foreach ($re as $key => $value) {
        $res[$value['price_type']] = price_format(abs($value['price']), C('PLACE_CURRENCY_MAPPING.'.$value['currency']), 4);
        $currency = $value['currency'];
    }

    $map['order_id'] = array('eq', $order_id);
    $info = $OrderItemsModel->where($map)->select();
    $info = $info[0]; // 仅拿一条展示
    parse_str(str_replace(',', '&', $info['order_source']), $keyword);

    $order_info['extra_charge_amount'] = $res['2'] ? $res['2'] : 0;
    $order_info['coupon_amount'] = $res['-4'] ? $res['-4'] : 0;
    $order_info['goods_amount'] = $res['1'] ? $res['1'] : 0;
    $order_info['goods_name'] = $info['goods_name'];
    $order_info['brand_name'] = $info['brand_name'];
    $order_info['delivery_time'] = $info['delivery_time'];
    $order_info['goods_price'] = price_format($info['goods_price'], C('PLACE_CURRENCY_MAPPING.'.$currency), 4);
    $order_info['goods_number'] = $info['goods_number'];
    $order_info['search_name'] = $keyword['k'] ? $keyword['k'] : '';
    $order_info['order_id'] = $order_id;
    $order_info['url'] = getShortUrl($order_id);
    return $order_info ? $order_info : false;
}

/**
 * 获取供应商键名（附加费使用的键名）
 * @param  [type] $supplier_id [description]
 * @param  [type] $canal       [description]
 * @param  [type] $supp_type   专卖联营类型 1专卖2联营
 * @return [type]              [description]
 */
function getSuppKey($supplier_id, $canal, $supp_type = 2) {
    if ($supplier_id == C('ZM_TOP_SUPP_ID') || $supp_type == 1) {
        $key = $supplier_id.'.'.$canal;
    } else {
        $key = $supplier_id;
    }
    return strval($key);
}