<?php 
namespace Order\Model;

use Think\Model;

class OrderRefundModel extends OrderBaseModel
{
    // 获取订单后台退款信息
    public function getInfo($order_id, $refund_type=2)
    {
        $map = array(
            'order_id' => $order_id,
            'refund_type' => $refund_type,
        );

        $field = 'refund_id, order_id, user_id, order_sn, order_goods_type, currency, pay_log_id, serial_number, pay_amount, price_fall, create_uid, create_time, refund_time, remark, status, refund_reason, refund_type';

        $data = $this->where($map)->field($field)->find();

        return $data;
    }

    /**
     * 通过ID获取信息
     * @param  [type] $refund_id [description]
     * @return [type]            [description]
     */
    public function getOrderInfo($refund_id = 0, $refund_sn = '', $field = 'R.*,O.order_pay_type')
    {
        $map = array(
            'R.refund_id' => $refund_id,
        );
        if (!empty($refund_sn)) {
            $map = array(
                'R.refund_sn' => $refund_sn,
            );
        }
        $data = $this->alias('R')->join(C('DB_PREFIX').'order O ON O.order_id = R.order_id')
                     ->field($field)
                     ->where($map)->find();
        return $data;
    }

    public function getSumAmount($order_id, $status = null)
    {
        $map = array(
            'order_id' => $order_id
        );
        if (is_null($status)) {
            $map['status'] = array('egt', '1');
        } else {
            $map['status'] = array('in', $status);
        }
        $amount = $this->where($map)->sum('pay_amount - price_fall');
        return $amount;
    }

    // 获取退货列表
    public function getRefundList($map, $p, $size)
    {
        $p = isset($p) ? $p : 1;

        $first = $size * ($p-1);

        $where = array();

        $search = $this->_refundSearch($map);

        $where = $search['where'];
        $sort  = $search['sort'];
        $join  = $search['join'];
        $field = $search['field'];

        // 添加业务员ID查询
        if ($sale_id) {
            $where['o.sale_id'] = $sale_id;
        }

        // 查询订单总数
        $response['count'] = $this->alias('r')
                            ->join($join)
                            ->where($where)
                            ->count("DISTINCT(r.order_id)"); // 去重

        //分页查询数据
        $response['data'] = $this->alias('r')
                        ->join($join)
                        ->where($where)
                        ->field($field)
                        ->limit($first.','.$size)
                        ->order($sort)
                        ->group('r.order_id')
                        ->select();

        return $response;
    }

    // 搜索条件
    public function _refundSearch($map)
    {
        $where = array();
        $join = '';

        // 退款单号
        if (!empty($map['refund_sn'])) {
            $where['r.refund_sn'] = $map['refund_sn'];
        }

        // 订单编号
        if (!empty($map['order_sn'])) {
            $where['r.order_sn'] = $map['order_sn'];
        }

        // SKU名称
        if (!empty($map['sku_name'])) {
            $where['it.sku_name'] = $map['sku_name'];

            $join .= "LEFT JOIN __ORDER_REFUND_ITEMS__ it ON it.refund_id = r.refund_id";
        }

        // 创建时间查询
        if (!empty($map['time_start']) || !empty($map['time_end'])) {
            if (!empty($map['time_start']) && !empty($map['time_end'])) {
                $where['r.create_time'] = ['between', [$map['time_start'], $map['time_end']]];
            } else if (!empty($map['time_start'])) {
                $where['r.create_time'] = ['gt', $map['time_start']];
            } else {
                $where['r.create_time'] = ['lt', $map['time_end']]; // 结束时间加上一天秒数减1
            }
        }

        // 支付方式
        if (!empty($map['order_payment_mode'])) {
            $pay_name = explode(',', $map['order_payment_mode']);

            foreach ($pay_name as $v) {
                switch ($v) {
                    case '1': $order_payment_mode[] = '微信支付';break;
                    case '2': $order_payment_mode[] = '支付宝';break;
                    case '3': $order_payment_mode[] = '银联支付(B2B)';break;
                    case '4': $order_payment_mode[] = '银联支付(B2C)';break;
                    case '5': $order_payment_mode[] = '账期支付';break;
                    case '6': $order_payment_mode[] = '京东支付';break;
                    case '7': $order_payment_mode[] = '交通银行';break;
                    case '8': $order_payment_mode[] = '恒生银行';break;
                    case '9': $order_payment_mode[] = '钱包支付';break;
                }
            }

            $where['p.pay_name'] = ['in', $order_payment_mode];
            $join .= " LEFT JOIN __PAY_LOG__ p ON r.order_id = p.order_id";
        }

        // 状态
        if (!empty($map['apply_status'])) {
            $where['r.status'] = $map['apply_status'];
        }

        // 是否退款
        if (!empty($map['is_refund'])) {
            $where['r.is_refund'] = ['in', $map['is_refund']];
        }

        // 订单类型及退货类型
        $where['r.order_goods_type'] = $map['order_goods_type'];

        if ($map['order_goods_type'] == 1) {
            $where['r.refund_type'] = 2;
        } else {
            $where['r.refund_type'] = 1;
        }    

        $field = 'r.refund_id, r.order_id, r.user_id, r.create_uid, r.order_sn, r.refund_sn, r.currency, r.pay_amount, r.price_fall, r.create_uid, r.is_refund, r.create_time, r.refund_time, r.update_time, r.status, r.refund_reason';

        $response['where'] = $where;
        $response['join']  = $join;
        $response['field'] = $field;
        $response['sort']  = 'r.create_time DESC, r.refund_id DESC';

        return $response;
    }

    // 订单退款单数量
    public function countOrderRefund($order_id)
    {
        return $this->where(['order_id' => $order_id])->count();
    }

    /**
     * 修改状态
     * @param [type] $refund_id [description]
     * @param [type] $status    [description]
     * @param [type] $time      [description]
     */
    public function setStatus($refund_id, $status, $time = null)
    {
        if (!empty($time)) {
            $time = time();
        }
        $map = array(
            'refund_id' => $refund_id
        );
        $data = array(
            'status' => $status,
            'update_time' => $time,
        );
        if ($status == 10) {
            $data['refund_time'] = $time;
            $data['is_sys'] = 2;//2-同步财务
        }
        $res = $this->where($map)->save($data);
        return $res;
    }

    /**
     * 添加退款单
     * @param  [type] $order_id      [订单ID]
     * @param  [type] $operator_id   [后台操作人ID]
     * @param  [type] $cancel_reason [取消原因]
     * @return [type]                [Array]
     */
    public function createOrderRefund($order_id, $operator_id, $cancel_reason, $status=1) 
    {
        $OrderModel            = D('Order');
        $OrderRefundItemsModel = D('OrderRefundItems');
        $OrderPriceModel       = D('OrderPrice');
        
        $order_info = $OrderModel->getInfo($order_id);
        $count      = $this->countOrderRefund($order_id);

        // 添加退款申请单
        $addData['order_id']         = $order_id;
        $addData['user_id']          = $order_info['user_id'];
        $addData['order_sn']         = $order_info['order_sn'];
        $addData['refund_sn']        = 'T-'.$order_info['order_sn'].'-'.$count;
        $addData['order_goods_type'] = $order_info['order_goods_type'];
        $addData['currency']         = $order_info['currency'];
        $addData['pay_amount']       = abs($OrderPriceModel->getClientPayed($order_id)); // 客户实际付款金额
        $addData['status']           = $status;
        $addData['refund_reason']    = $cancel_reason;

        if ($order_info['order_goods_type'] == 1) {
            $addData['refund_type']      = 2; // 联营退货类型
        }
        
        $addData['create_uid']  = $operator_id; // 操作人
        $addData['create_time'] = time();

        if ($status == 10) {
            $addData['refund_time'] = time();
        }        

        $refund_id = $this->data($addData)->add();    

        return $refund_id;
    }
}