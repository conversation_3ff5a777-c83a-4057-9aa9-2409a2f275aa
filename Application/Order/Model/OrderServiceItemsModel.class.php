<?php 
namespace Order\Model;

use Think\Model;

class OrderServiceItemsModel extends OrderBaseModel
{
   // 获取售后明细
   public function getServiceItems($service_id)
   {
   		return $this->where(['service_id' => $service_id])->select();
   }

   // 获取订单所有的售后单
   public function getAllServiceItems($order_id, $service_status='')
   {
   		$map = [];
		$map['i.order_id'] = $order_id;
		$service_status && $map['s.service_status'] = $service_status;

   		return $this->alias('i')
		   		->join('LEFT JOIN __ORDER_SERVICE__ as s ON i.service_id = s.id')
		   		->where($map)
		   		->field('i.id, i.rec_id, i.order_id, i.erp_rec_id, i.adjust_number')
		   		->select();
   }

}