<?php
namespace Order\Model;

use Think\Model;

class OrderInfoModel extends OrderBaseModel {

    /**
     * 获取列表
     * @param  string $map   [description]
     * @param  string $limit [description]
     * @param  string $order [description]
     * @return [type]        [description]
     */
    public function getUserList($user_id, $where = '', $page = '', $order = 'create_time DESC')
    {
        $map = array(
            'O.user_id' => $user_id,
        );
        if (isset($where['OG.goods_name'])) {
            $OrderGoods = D('OrderGoods');
            $goods_map = array(
                'O.order_sn' => array('exp', ' = OG.order_sn'),
                'OG.goods_name' => $where['OG.goods_name'],
            );
            $sql = $OrderGoods->alias('OG')->where($goods_map)->field('rec_id')->select(false);
            $where['_string'] = "EXISTS ({$sql})";
            unset($where['OG.goods_name']);
        }
        list($p, $limit) = explode(',', $page);
        empty($p) && $p = 1;
        empty($limit) && $limit = C('DEFAULT_PAGE_LIMIT');
        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->alias('O')->where($map)->page($p, $limit)->order($order)
                        ->field('O.order_sn, O.user_id, O.create_time, O.consignee, O.order_amount, O.order_status, O.pay_status, O.shipping_status, O.shipping_fee, O.extra_fee, O.currency, O.order_type')
                        ->select();
        foreach ($datas as &$v) {
            $v['order_type_val'] = C('OLD_ORDER_TYPE.'.$v['order_type']);
            $v['order_amount_format'] = price_format($v['order_amount'], $v['currency']+1);
            $v['extend_fee'] = price_format($v['shipping_fee'] + $v['extra_fee'], $v['currency']+1);
        }
        return $datas;
    }

    /**
     * 获取列表数
     * @param  [type] $user_id [description]
     * @param  string $where   [description]
     * @return [type]          [description]
     */
    public function getUserCount($user_id, $where = '')
    {
        $map = array(
            'O.user_id' => $user_id,
        );
        if (isset($where['OG.goods_name'])) {
            $OrderGoods = D('OrderGoods');
            $goods_map = array(
                'O.order_sn' => array('exp', ' = OG.order_sn'),
                'OG.goods_name' => $where['OG.goods_name'],
            );
            $sql = $OrderGoods->alias('OG')->where($goods_map)->field('rec_id')->select(false);
            $where['_string'] = "EXISTS ({$sql})";
            unset($where['OG.goods_name']);
        }
        !empty($where) && $map = array_merge($map, $where);
        $datas = intval($this->alias('O')->where($map)->count());
        return $datas;
    }

    /**
     * 获取用户订单明细
     * @param  [type] $user_id  [description]
     * @param  [type] $order_sn [description]
     * @return [type]           [description]
     */
    public function getUserInfo($user_id, $order_sn)
    {
        $map = array(
            'user_id' => $user_id,
            'order_sn' => $order_sn,
        );
        $datas = $this->where($map)
                        ->field('order_sn, user_id, shipping_name, shipping_no, consignee, province, city, district, address, order_amount, order_status, pay_status, shipping_id, shipping_name, shipping_no, shipping_status, shipping_fee, extra_fee, create_time, confirm_time, pay_time, shipping_time, done_time, currency, goods_amount, order_type, cancel_time')
                        ->find();
        if (!empty($datas)) {
            $datas['order_amount_format'] = price_format($datas['order_amount'], $datas['currency'] + 1);
            $datas['goods_amount_format'] = price_format($datas['goods_amount'], $datas['currency'] + 1);
            $datas['order_type_val'] = C('OLD_ORDER_TYPE.'.$datas['order_type']);
            $datas['extend_fee'] = price_format($datas['shipping_fee'] + $datas['extra_fee'], $datas['currency'] + 1);
        }
        return $datas;
    }

    /**
     * 获取明细
     * @param  [type] $order_sn [description]
     * @return [type]           [description]
     */
    public function getInfo($order_sn, $field = '*')
    {
        $datas = $this->where($map)->field($field)->find($order_sn);
        if (!empty($datas)) {
            $datas['order_amount_format'] = price_format($datas['order_amount'], $datas['currency'] + 1);
            $datas['goods_amount_format'] = price_format($datas['goods_amount'], $datas['currency'] + 1);
            $datas['order_type_val'] = C('OLD_ORDER_TYPE.'.$datas['order_type']);
            $datas['extend_fee'] = price_format($datas['shipping_fee'] + $datas['extra_fee'], $datas['currency'] + 1);
        }
        return $datas;
    }

    /**
     * 更改订单状态
     * @param [type] $user_id  [description]
     * @param [type] $order_sn [description]
     * @param [type] $status   [description]
     */
    public function setUserStatus($user_id, $order_sn, $status)
    {
        $map = array(
            'user_id' => $user_id,
            'order_sn' => $order_sn,
        );
        $data = array();
        switch ($status) {
            case '-1' : 
                $data['order_status'] = 3;
                $data['pay_status'] = 0;
                $data['cancel_time'] = time();
                break;
            case '5' :
                $data['order_status'] = 1;
                break;
            case '10' :
                $data['order_status'] = 4;
                $data['pay_status'] = 1;
                $data['done_time'] = time();
                break;
            default :
                $status_arr = C('ORDER_STATUS_MAPPING.'. $status);
                $data['order_status'] = $status_arr['1'];
                $data['pay_status'] = $status_arr['2'];
                break;
        }
        $res = $this->where($map)->save($data);
        return $res;
    }
}