<?php 
namespace Order\Model;

use Think\Model;

class OrderContractModel extends OrderBaseModel
{
	/**
    * 获取信息
    * @param  [type] $order_id [description]
    * @param  string $field    [description]
    * @return [type]           [description]
    */
    public function getInfo($order_id, $field = '*')
    {
        $map = array(
            'order_id' => $order_id,
        );

        return $this->where($map)->field($field)->find();
    }

    // 获取用户最近不开发票的大陆订单的乙方合同名称
    public function getLastContractName($user_id)
    {
        $OrderModel = D('Order');
        $OrderInvoiceModel = D('OrderInvoice');

        $order_map = [
            'user_id'  => $user_id,
            'currency' => 1,
        ];

        $order_ids = $OrderModel->where($order_map)->getField('order_id', true);

        if (!$order_ids) return '';

        $inv_map = [];
        $inv_map['order_id'] = ['in', $order_ids];
        $inv_map['inv_type'] = 1;

        $inv_order_ids = $OrderInvoiceModel->where($inv_map)->getField('order_id', true);

        if (!$inv_order_ids) return '';

        $map = [];
        $map['order_id'] = ['in', $inv_order_ids];

        return $this->where($map)->order('sc_id desc')->getField('contract_com_name');
    }

}