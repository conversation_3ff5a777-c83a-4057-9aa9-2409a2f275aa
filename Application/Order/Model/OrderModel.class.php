<?php
namespace Order\Model;

class OrderModel extends OrderBaseModel {

    //销售组织对应不同税率
    static $SALE_COM_ID_TAX = [
        1 => 0.13,
        2 => 0,
        3 => 0.13,
        9 =>0.13,
        10 =>0.13,
        11 =>0.08,
        12 =>0.07,
    ];


    /**
     * 创建订单主信息
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    public function createOrder($data)
    {
        $add = array(
            'order_type' => 1,
            'status' => 1,
            'create_time' => time(),
        );
        if (!isset($data['order_sn'])) {
            $data['order_sn'] = $this->findSn(1);
        }
        $add = array_merge($add, $data);
        $order_id = $this->add($add);
        return $order_id;
    }

    /**
     * 获取唯一订单号
     * @param  string $prefix [description]
     * @param  string $suffix [description]
     * @return [type]         [description]
     */
    public function findSn($prefix = '', $suffix = '')
    {
        $sn = order_sn($prefix, $suffix);
        $order_id = $this->where(array('order_sn' => $sn))->getField('order_id');
        if (!empty($order_id)) {
            $sn = $this->findSn($prefix, $suffix);
        }
        return $sn;
    }


    /**
     * 获取用户订单明细
     * @param  [type] $order_id [description]
     * @param  [type] $order_sn [description]
     * @return [type]           [description]
     */
    public function getInfo($order_id = '', $order_sn = '')
    {
        if (!empty($order_sn) && empty($order_id)) {
            $map['order_sn'] = $order_sn;
        } else {
            $map['order_id'] = $order_id;
        }

        $field = '*';

        $datas = $this->where($map)
                        ->field($field)
                        ->find();
        if (!empty($datas)) {
            $datas['order_amount_format'] = price_format($datas['order_amount'], $datas['currency']);
        }
        return $datas;
    }

    /*
     * 检查订单
     */
    public function checkOrderByUser($user_id,$order_id){
        return  $this->where(["user_id"=>$user_id,"order_id"=>$order_id])->count("order_id");
    }


    /**
     * 获取列表
     * @param  string $map   [description]
     * @param  string $limit [description]
     * @param  string $order [description]
     * @return [type]        [description]
     */
    public function getUserList($user_id, $where = '', $page = '', $order = 'create_time DESC', $field = null)
    {
        $map = array(
            'O.user_id' => $user_id,
            // 'oe.order_type' => array('neq',3),
            'O.order_pf' => 1, // 暂时只显示线上订单
        );
        $where = $this->itemsWhere($where);
        is_array($where) && $map = array_merge($map, $where);
        is_null($field) && $field = 'O.order_id, O.order_sn, O.sale_order_sn, O.user_id, O.order_goods_type, O.sale_type, O.create_time, O.pay_time, O.order_amount, O.status, O.currency, O.order_type, O.order_pay_type, O.customer_sn, oe.zy_delivery_type,oe.is_manager_audit';
        limit_page($this, $page);

        // 过滤后台未审核通过的订单
        if (isset($map['_string'])) {
            $map['_string'] .= ' and ((O.status < 2 and oe.order_type = 0) OR (O.status > 1 and oe.order_type != 3))';
        } else {
            $map['_string'] = '(O.status < 2 and oe.order_type = 0) OR (O.status  > 1  and oe.order_type != 3)';
        }        

        $datas = $this->alias('O')->where($map)->order($order)
                ->join('LEFT JOIN '. C('DB_PREFIX'). 'order_extend oe ON O.order_id = oe.order_id')
                ->field($field)
                ->select();

        if (I("debug") == 1){
            print_r($this->getLastSql());
            die();
        }

        foreach ($datas as &$v) {
            isset($v['order_pay_type']) && $v['order_pay_type_val'] = C('ORDER_PAY_TYPE_NAME.'.$v['order_pay_type']);
            isset($v['order_amount']) && $v['order_amount_format'] = price_format($v['order_amount'], $v['currency']);
        }
        return $datas;
    }

    /**
     * 获取列表数
     * @param  [type] $user_id [description]
     * @param  string $where   [description]
     * @return [type]          [description]
     */
    public function getUserCount($user_id, $where = '')
    {
        $map = array(
            'O.user_id' => $user_id,
            'O.order_pf' => 1, // 暂时只显示线上订单
        );
        $where = $this->itemsWhere($where);
        is_array($where) && $map = array_merge($map, $where);

        // 过滤后台未审核通过的订单
        if (isset($map['_string'])) {
            $map['_string'] .= ' and ((O.status < 2 and oe.order_type = 0) OR (O.status > 1 and oe.order_type != 3))';
        } else {
            $map['_string'] = '(O.status < 2 and oe.order_type = 0) OR (O.status > 1 and oe.order_type != 3)';
        } 

        $datas = intval($this->alias('O')
                ->join('LEFT JOIN '. C('DB_PREFIX'). 'order_extend oe ON O.order_id = oe.order_id')
                ->where($map)
                ->count());

        if (I("debug")){
            print_r($this->getLastSql());
            die();
        }

        // $datas = intval($this->alias('O')->where($map)->count());

        return $datas;
    }

    /**
     * 获取订单明细包括订单详细
     * @return [type] [description]
     */
    public function getOrderItemsList($where, $field = '*', $page = '', $order = 'OI.rec_id')
    {
        limit_page($this, $page);
        $datas = $this->alias('O')->join('LEFT JOIN '. C('DB_PREFIX'). 'order_items OI ON O.order_id = OI.order_id')
                        ->where($where)->order($order)
                        ->field($field)
                        ->select();
        foreach ($datas as &$v) {
            $v['goods_price_format'] = price_format($v['goods_price'], $v['currency'], 4);
            $v['goods_amount'] = price_format($v['goods_price'] * $v['goods_number'], 0, 4);//小计
            $v['goods_amount_format'] = price_format($v['goods_total'], $v['currency'], 4);
            $v['single_pre_price_format'] = price_format($v['single_pre_price'], $v['currency'], 8);
            $v['single_pre_price_amount'] = price_format($v['single_pre_price'] * $v['goods_number'], $v['currency'], 8);
            $v['goods_type'] = strlen($v['goods_id']) == 11 ? 2 : 1;
        }
        return $datas;
    }

    /**
     * 主表查询明细字段条件
     * @param  [type] $where [description]
     * @return [type]        [description]
     */
    private function itemsWhere($where)
    {
        if (isset($where['OI.goods_name'])) {
            $OrderItemsModel = D('OrderItems');
            $goods_map = array(
                'O.order_id' => array('exp', ' = OI.order_id'),
                'OI.goods_name' => array('like', $where['OI.goods_name'].'%'),
                'OI.status' => 1,
            );
            $sql = $OrderItemsModel->alias('OI')->where($goods_map)->field('rec_id')->select(false);
            $where['_string'] = "EXISTS ({$sql})";
            unset($where['OI.goods_name']);
        }
        if (isset($where['OI.sku_name'])) {
            $OrderItemsModel = D('OrderItems');
            $goods_map = array(
                'O.order_id' => array('exp', ' = OI.order_id'),
                'OI.sku_name' => array('like', $where['OI.sku_name'].'%'),
                'OI.status' => 1,
            );
            $sql = $OrderItemsModel->alias('OI')->where($goods_map)->field('rec_id')->select(false);
            $where['_string'] = "EXISTS ({$sql})";
            unset($where['OI.sku_name']);
        }
        if (isset($where['keyword'])) {
            $OrderItemsModel = D('OrderItems');
            $goods_map = array(
                'O.order_id' => array('exp', ' = OI.order_id'),
                'OI.goods_name|O.order_sn' => array('like', $where['keyword'].'%'),
                'OI.status' => 1,
            );

            $sql = $OrderItemsModel->alias('OI')->where($goods_map)->field('rec_id')->select(false);
            $where['_string'] = "EXISTS ({$sql})";
            unset($where['keyword']);
        }
        if (isset($where['goods_sku_name'])) {
            $OrderItemsModel = D('OrderItems');
            $goods_map = array(
                'O.order_id' => array('exp', ' = OI.order_id'),
                'OI.goods_name|OI.sku_name' => array('like', $where['goods_sku_name'] . '%'),
                'OI.status' => 1,
            );

            $sql = $OrderItemsModel->alias('OI')->where($goods_map)->field('rec_id')->select(false);
            $where['_string'] = "EXISTS ({$sql})";
            unset($where['goods_sku_name']);
        }
        return $where;
    }

    /**
     * 获取用户订单明细
     * @param  [type] $user_id  [description]
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function getUserInfo($user_id, $order_id)
    {
        $map = array(
            'user_id' => $user_id,
            'order_id' => $order_id,
        );
        $datas = $this->where($map)
                        ->field('order_id, order_sn, user_id, order_amount, status, create_time, confirm_time, shipping_time, finish_time, currency, cancel_time')
                        ->find();
        if (!empty($datas)) {
            $datas['order_amount_format'] = price_format($datas['order_amount'], $datas['currency']);
        }
        return $datas;
    }


    /**
     * 更改订单状态
     * @param [type] $user_id  [description]
     * @param [type] $order_id [description]
     * @param [type] $status   [description]
     */
    public function setUserStatus($user_id, $order_id, $status)
    {
        $map = array(
            'user_id' => $user_id,
            'order_id' => $order_id,
        );
        if (is_null($user_id)) {
            unset($map['user_id']);
        }
        $data = array();
        switch ($status) {
            case '-1' :
                $data['cancel_time'] = time();
                break;
            case '2' :
                $data['confirm_time'] = time();
                break;
            case '3' :
                $data['advance_time'] = time();
                break;
            case '4' :
                $data['pay_time'] = time();
                break;
            case '7' :
            case '8' :
                $data['shipping_time'] = time();
                break;
            case '10' :
                $data['finish_time'] = time();
                break;
            default :
                break;
        }
        if (empty($data)) {
            return false;
        }
        $data['status'] = $status;
        $res = $this->where($map)->save($data);
        return $res;
    }

    /**
     * 返回所有订单数据
     * @param  [type]  $p        [当前页码]
     * @param  integer $pagesize [每页显示条数]
     * @return [Array]
     */
    public function getAllOrderDetails($p, $map='', $pagesize=10)
    {
        $page = isset($p) ? $p : 1;

        $first = $pagesize * ($page-1);

        $where = array();

        $search = $this->_search($map);

        $where = $search['where'];
        $sort  = $search['sort'];
        $join  = $search['join'];
        $field = $search['field'];

        // 查询订单总数
        $response['count'] = $this->alias('o')
                            ->join($join)
                            ->where($where)
                            ->count("DISTINCT(o.order_id)"); // 去重

        //分页查询数据
        if ($map['checkTime']) { // 订单金额统计
            $response['data'] = $this->alias('o')
                            ->join($join)
                            ->where($where)
                            ->field($field)
                            ->limit($first.','.$pagesize)
                            ->order($sort)
                            ->group('o.order_id')
                            ->select();
        } else {
            $response['data'] = $this->alias('o')
                            ->join($join)
                            ->distinct(true)
                            ->where($where)
                            ->field($field)
                            ->limit($first.','.$pagesize)
                            ->order($sort)
                            ->select();
        }

        // 自营获取会员是否启用账期
        if ($map['order_goods_type'] == 2 && $response['data']) {
            $uids = [];

            foreach ($response['data'] as $v) {
                $uids[] = $v['user_id'];
            }

            $uids = implode(',', array_unique($uids));
            $res = json_decode(post_curl(CREDIT_DOMAIN.'/credit/ishas', ['user_id'=>$uids]), true); // 会员是否启用账期
            
            $response['credit'] = $res ? $res['data'] : '';
        }

        return $response;
    }

    // 订单其他信息：金额、下单用户数、付款人数、付款订单数、未填写取消原因
    public function getAllOrderOtherInfo($map)
    {
        $search = $this->_search($map);

        $where = $search['where'];
        $sort = $search['sort'];
        $join = $search['join'];

        // 统计支付金额
        $response['pay_count'] = $this->checkOrderAmount($join, $where);

        // 下单用户人数
        $response['user_count'] = $this->alias('o')
                                ->join($join)
                                ->where($where)
                                ->count("DISTINCT(o.user_id)"); // 去重

        // 付款用户人数
        $response['paid_user_count'] = $this->checkPaidOrder($join, $where, 'a.user_id');

        // 付款订单数
        $response['paid_order_count'] = $this->checkPaidOrder($join, $where, 'a.order_id');

        // 订单已取消，未填写原因统计
        $response['noreason_count'] = $this->alias('o')
                                    ->join($join)
                                    ->where($where)
                                    ->where(['o.status' => -1, 'o.cancel_reason' => ''])
                                    ->count("DISTINCT(o.order_id)"); // 去重

        return $response;
    }

    // 订单统计---金额
    public function getOrderAmountInfo($where)
    {
        $search = $this->_search($where);

        $map = $search['where'];
        $sort  = $search['sort'];
        $join  = $search['join'];

        $map['o.status'] = ['neq', -1];   // 默认过滤订单取消记录

        // 全部订单实收金额
        $map['o.currency'] = 1;
        $rmb_pay           = $this->amountHandleSql($join, $map);
        
        $map['o.currency'] = 2;
        $usd_pay           = $this->amountHandleSql($join, $map);
        
        // 已完成订单
        $map['o.currency'] = 1;
        $map['o.status']   = ['eq', 10];
        $rmb_pay_finish    = $this->amountHandleSql($join, $map);
        
        $map['o.currency'] = 2;
        $usd_pay_finish    = $this->amountHandleSql($join, $map);
        
        // 未完成订单
        $map['o.currency'] = 1;
        $map['o.status']   = ['not in', [-1, 10]];
        $rmb_pay_unfinish  = $this->amountHandleSql($join, $map);
        
        $map['o.currency'] = 2;
        $usd_pay_unfinish  = $this->amountHandleSql($join, $map);

        $response['rmb_pay']          = $rmb_pay ? $rmb_pay : 0;
        $response['usd_pay']          = $usd_pay ? $usd_pay : 0;
        $response['rmb_pay_finish']   = isset($rmb_pay_finish) ? $rmb_pay_finish : 0;
        $response['usd_pay_finish']   = isset($usd_pay_finish) ? $usd_pay_finish : 0;
        $response['rmb_pay_unfinish'] = isset($rmb_pay_unfinish) ? $rmb_pay_unfinish : 0;
        $response['usd_pay_unfinish'] = isset($usd_pay_unfinish) ? $usd_pay_unfinish : 0;

        return $response;
    }

    /**
     * 筛选搜索条件
     * @param  [Array] $map [搜索条件]
     * @return [Array]
     */
    public function _search($map)
    {
        $where = array();

        // 筛选条件 --- 订单统计金额
        if (isset($map['checkTime'])) {
            // 区分联营和自营订单
            if (isset($map['order_goods_type'])) {
                $where['o.order_goods_type'] = $map['order_goods_type'];
            }

            $where['order_type'] = 1; // 平台订单

            // 非竞调数据
            if (isset($map['is_fake'])) {
                $where['o.is_type'] = $map['is_fake'];
            }

            // 过滤测试订单
            $where['o.user_id'] = ['not in', $this->testMobile()];

            if ($map['checkTime'] == 1) { // 订单付款时间
                $where['p.pay_time'] = ['between', [$map['time_start'], $map['time_end']]];
            } else { // 订单创建时间
                $where['o.create_time'] = ['between', [$map['time_start'], $map['time_end']]];
            }

            $where['o.status'] = ['neq', -1]; // 过滤订单取消状态
            $where['_string'] = 'p.is_paid in (1, 2) OR p.pay_type = 4';
            // $where['p.is_paid'] = ['in', ['1', '2']]; // 2 是针对首款付完后，尾款尚未支付的状态

            if ($map['checkStatus'] == 2) { // 已完成订单
                $where['o.status'] = 10;
            } else if ($map['checkStatus'] == 3) { // 未完成订单
                $where['o.status'] = ['not in', [-1, 10]];
            }  

            $response['where'] = $where;
            $response['join'] = "LEFT JOIN __PAY_LOG__ p ON o.order_id = p.order_id";
            $response['field'] = "o.order_id, o.order_sn, o.order_pay_type, o.order_amount, o.create_time, o.currency, o.finish_time, IFNULL(GROUP_CONCAT(DISTINCT p.pay_name),'') as pay_name, IFNULL(GROUP_CONCAT(DISTINCT p.pay_amount),'') as pay_amount, IFNULL(GROUP_CONCAT(DISTINCT p.pay_time),'') as pay_time, p.pay_type";
            $response['sort'] = 'p.pay_time DESC, p.pay_log_id DESC';

            return $response;
        }

        // 订单列表页面搜索
        if (!empty($map['order_contain'])) {
            if ($map['order_type'] == 3) { // 发票抬头
                $where['i.tax_title'] = $map['order_contain'];
                $join .= " LEFT JOIN __ORDER_INVOICE__ i ON o.order_id = i.order_id";
            } else if ($map['order_type'] == 4) { // 会员ID
                $where['o.user_id'] = $map['order_contain'];
            } else { // 会员账号
                $map['order_contain'] = urldecode($map['order_contain']); // 转url编码
                
                $user_map = [];
                if (preg_match('/@/', $map['order_contain'])) {
                    $user_map['email'] = $map['order_contain'];
                } else {
                    $user_map['mobile'] = $map['order_contain'];
                }    

                $where['o.user_id'] = D('Home/UserMain')->where($user_map)->getField('user_id');
            }
        }

        // 订单编号
        if (!empty($map['order_sn'])) {
            $where['o.order_sn'] = $map['order_sn'];
        }

        // ERP订单号
        if (!empty($map['erp_sn'])) {
            $where['oe.erp_sn'] = $map['erp_sn'];
            $join .= " LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id";
        }

        // 型号
        if (!empty($map['goods_name'])) {
            $where['g.goods_name'] =  $map['goods_name'];
            // $where['g.status']     =  1; // 正常使用的商品
            $join .= " LEFT JOIN __ORDER_ITEMS__ g ON o.order_id = g.order_id";
        }

        // 商品查询
        // if (isset($search_goods_name)) {
        //     $where['g.goods_name'] =  $search_goods_name;
        //     $where['g.status'] =  1; // 正常使用的商品
        //     $join .= " LEFT JOIN __ORDER_ITEMS__ g ON o.order_id = g.order_id";
        // }

        // 时间查询
        if (!empty($map['time_start']) || !empty($map['time_end'])) {
            // 时间类型筛选
            switch ($map['select_time_type']) {
                case 1: $time_type = 'o.confirm_time'; break;
                case 2: $time_type = 'o.pay_time'; $where['o.status'] = ['gt', 2]; break;
                case 3: $time_type = 'o.shipping_time'; break;
                case 4: $time_type = 'o.finish_time'; break;
                default: $time_type = 'o.create_time'; break;
            }

            if (!empty($map['time_start']) && !empty($map['time_end'])) {
                $where[$time_type] = ['between', [$map['time_start'], $map['time_end']]];
            } else if (!empty($map['time_start'])) {
                $where[$time_type] = ['egt', $map['time_start']];
            } else {
                $where[$time_type] = ['elt', $map['time_end']]; // 结束时间加上一天秒数减1
            }
        }

        // 订单状态
        if (!empty($map['order_status'])) {
            if (isset($where['o.status'])) {
                $where['o.status'] = [$where['o.status'], ['in', $map['order_status']], 'and'];
            } else {
                $where['o.status'] = ['in', $map['order_status']];
            }
        }

        // 销售类型
        if (!empty($map['sale_type'])) {
            $where['o.sale_type'] = ['in', $map['sale_type']];
        }

        // 配送方式
        if (!empty($map['shipping_name'])) {
            $where['o.order_shipping_type'] = $map['shipping_name'];
        }

        // 订单推送业务员
        // if ($map['order_send'] != '') {
        //     $where['o.sale_id'] = ['in', $map['order_send']];
        // }

        // 订单类型
        if (!empty($map['order_pay_type'])) {
            $where['o.order_pay_type'] = ['in', $map['order_pay_type']];
        }

        // 订单支付状态
        if (!empty($map['order_pay_status'])) {
            switch ($map['order_pay_status']) {
                case '1' : $where['o.status'] = ['egt', 4]; break; // 已支付
                case '2' : $where['o.status'] = 3; break; // 已付定金
                default  : $where['o.status'] = ['elt', 2]; break;
            }
        }

        // 区分联营和自营订单
        if (isset($map['order_goods_type'])) {
            $where['o.order_goods_type'] = $map['order_goods_type'];
        }

        // 过滤网站、ERP、京东订单
        if (isset($map['order_type_filter'])) {
            $where['o.order_type'] = ['in', $map['order_type_filter']];
        }

        // 订单来源
        if (!empty($map['order_source_pf'])) {
            $order_source_pf = is_string($map['order_source_pf']) ? explode(',', $map['order_source_pf']) : $map['order_source_pf'];     
            $string = '';

            foreach ($order_source_pf as $v) {
                switch ($v) {
                    case '1': 
                    case '2': 
                    case '6': 
                    case '8': 
                    case '9': 
                        $string .= "o.order_type = 1 AND FIND_IN_SET('pf=".$v."', o.order_source) OR "; 
                        break;
                    case '3': 
                        $string .= "oe.order_type != 0 OR ";
                        if (strpos($join, '__ORDER_EXTEND__') === false) { // 之前有搜索发票抬头则不再连接
                            $join .= " LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id";
                        }
                        break;
                    case '4': 
                        $string .= "o.order_type = 3 OR "; 
                        break;
                    case '5': 
                        $string .= "o.order_type = 2 OR "; 
                        break;
                    case '7': 
                        $string .= "o.order_type = 4 OR "; 
                        break;
                }
            }

            $where['_string'] = rtrim($string, ' OR ');
        }

        // 只查看自营京东订单
        if (isset($map['check_jd_order'])) {
            $where['o.order_type'] = 3; 
        }

        // 区分ERP或JD
        if (!empty($map['order_source'])) {
            $where['o.order_type'] = $map['order_source'];
        }

        // 非竞调数据
        if (isset($map['is_fake'])) {
            $where['o.is_type'] = $map['is_fake'];
        }

        // 订单类型
        if ($map['order_type_extend'] != '') {
            $where['o.order_type_extend'] = $map['order_type_extend'];
        }

        // 团购状态
        if ($map['status_extend'] != '') {
            $where['o.status_extend'] = $map['status_extend'];
        }

        // 交货地
        if (!empty($map['delivery_place'])) {
            $where['o.delivery_place'] = $map['delivery_place'];
        }

        // 同步状态
        if (!empty($map['erp_order_id'])) {
            $where['o.erp_order_id'] = ['neq', ''];
        }

        // 竞调账号根据时间展示数据
        if (isset($map['vp_time_set'])) {
            $where['_string'] = "((o.create_time < ".$map['vp_time_set']." AND o.is_type = 1) OR (o.create_time >= ".$map['vp_time_set']." AND o.is_type IN (0, 1)))";

            // if (strpos($join, '__ORDER_EXTEND__') === false) {
                // $join .= " LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id"; 
                // $sort = 'oe.erp_sn desc, o.create_time desc';
            // }
        }

        // 支付方式
        if (!empty($map['order_payment_mode'])) {
            $pay_name = is_string($map['order_payment_mode']) ? explode(',', $map['order_payment_mode']) : $map['order_payment_mode'];

            foreach ($pay_name as $v) {
                switch ($v) {
                    case '1': $order_payment_mode[] = '微信支付';break;
                    case '2': $order_payment_mode[] = '支付宝';break;
                    case '3': $order_payment_mode[] = '银联支付(B2B)';break;
                    case '4': $order_payment_mode[] = '银联支付(B2C)';break;
                    case '5': $order_payment_mode[] = '账期支付';break;
                    case '6': $order_payment_mode[] = '京东支付';break;
                    case '7': $order_payment_mode[] = '交通银行';break;
                    case '8': $order_payment_mode[] = '恒生银行';break;
                    case '9': $order_payment_mode[] = '钱包支付';break;
                }
            }

            $where['p.pay_name'] = ['in', $order_payment_mode];
            $join .= " LEFT JOIN __PAY_LOG__ p ON o.order_id = p.order_id";
        }

        // 发票状态
        if (!empty($map['order_invoice_status'])) {
            if ($map['order_invoice_status'] == 2) {
                $where['i.inv_type'] =  ['in', [2, 4]]; // 普票和增值税普票
            } else {
                $where['i.inv_type'] =  $map['order_invoice_status'];
            }
            
            if (strpos($join, '__ORDER_INVOICE__') === false) { // 之前有搜索发票抬头则不再连接
                $join .= " LEFT JOIN __ORDER_INVOICE__ i ON o.order_id = i.order_id";
            } 
        }

        // 用户标签筛选
        if ($map['is_new'] != '') {
            $where['u.is_new'] = ['in', $map['is_new']];
        }

        // 新订单筛选
        if ($map['is_new_order'] != '') {
            $where['oe.is_new'] = $map['is_new_order'];

            if (strpos($join, '__ORDER_EXTEND__') === false) {
                $join .= " LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id"; 
            }
        }

        // 主管审核
        if ($map['is_manager_audit'] != '') {
            $where['oe.is_manager_audit'] = ['in', $map['is_manager_audit']];

            if (strpos($join, '__ORDER_EXTEND__') === false) {
                $join .= " LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id"; 
            }
        }

        // 自营其他业务类型
        if ($map['business_type'] != '') {
            // $where['oe.order_type'] = 3; // 自营线下
            $where['oe.business_type'] = ['in', $map['business_type']];
            
            if (strpos($join, '__ORDER_EXTEND__') === false) {
                $join .= " LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id"; 
            }
        }

        // 业务员ID
        if ($map['sale_id']) {
            $where['o.sale_id'] = ['in', $map['sale_id']];
        }

        // 过滤测试订单
        if (empty($map['test_order'])) {
            $where['o.user_id'] = isset($where['o.user_id']) ? array(['eq', $where['o.user_id']], ['not in', $this->testMobile()], 'and') : ['not in', $this->testMobile()];
        }

        // 竞调账号搜索公司名称：订单备注、发票抬头、用户公司
        if (!empty($map['order_remark'])) {
            $where['o.order_remark'] = ['like', $map['order_remark'].'%'];

            // if (isset($where['_string'])) {
            //     $where['_string'] .= " and (oi.tax_title like '".$map['order_remark'].'%'."' or uc.com_name like '".$map['order_remark'].'%'."' or o.order_remark like '".$map['order_remark'].'%'."')";
            // } else {
            //     $where['_string'] = "oi.tax_title like '".$map['order_remark'].'%'."' or uc.com_name like '".$map['order_remark'].'%'."' or o.order_remark like '".$map['order_remark'].'%'."'";
            // }       

            // $join .= " LEFT JOIN __ORDER_INVOICE__ oi ON o.order_id = oi.order_id";
            // $join .= " LEFT JOIN __USER_COMPANY__ uc ON o.user_id = uc.user_id";
        }

        // 采购时间
        if (!empty($map['pur_time'])) {
            $exp = explode('/', $map['pur_time']);

            $where['o.pur_time'] = $exp[0].'年'.$exp[1].'月';
        }

        // adtags来源
        if (!empty($map['order_source_adtag'])) {
            // $where['_string'] = "FIND_IN_SET('adtag=".$map['order_source_adtag']."', o.order_source)";
            $where['o.order_source'] = ['like', '%adtag='.trim($map['order_source_adtag']).'%'];
        }

        // ptag来源
        if (!empty($map['order_source_ptag'])) {
            $where['o.order_source'] = ['like', '%ptag='.trim($map['order_source_ptag']).'%'];
        }

        // 关联用户表
        // if ((!empty($map['order_contain']) && $map['order_type'] == '') || $map['is_new'] != '') {
        //     $join .= " LEFT JOIN __USER_MAIN__ u ON o.user_id = u.user_id";
        // }

        // 活动名称
        if ($map['topic_name'] != '') {
            $where['oe.topic_name'] = ['eq', $map['topic_name']];

            if (strpos($join, '__ORDER_EXTEND__') === false) {
                $join .= " LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id";
            }
        }

        $field = "o.order_id, o.order_sn, o.user_id, o.erp_order_id, o.order_type, o.sale_type, o.order_source, o.order_pay_type, o.order_amount, o.advance_amount, o.erp_syn, o.create_time, o.status, o.cancel_reason, o.order_shipping_type,  o.currency, o.advance_pay_time, o.pay_time as order_pay_time, o.finish_time, o.sale_id, o.order_type_extend, o.status_extend, o.is_type, o.order_remark";

        $response['where'] = $where;
        $response['join']  = $join;
        $response['field'] = $field;
        $response['sort']  = isset($sort) ? $sort : 'o.order_id DESC';

        return $response;
    }

    // 测试帐号
    public function testMobile()
    {
        $testId = array();

        $user = D('Home/UserMain')->field('user_id')->where(['is_test' => 1, 'is_type' => 0])->select();

        foreach ($user as $k => $v) {
            $testId[$k] = $v['user_id'];
        }

        return $testId;
    }

    /**
     * 统计订单总金额
     * @return [Array]
     */
    public function checkOrderAmount($join, $map=[])
    {
        $response = array();

        // 默认过滤订单取消记录
        if (isset($map['o.status'])) {
            $map['o.status'] = [['neq', -1], [$map['o.status'][0], $map['o.status'][1]]]; 
        } else {
            $map['o.status'] = ['neq', -1];
        }       

        // 应付金额---人民币
        $map['o.currency'] = 1;
        $subSql = $this->alias('o')->join($join)->where($map)->field('DISTINCT(o.order_id)')->buildSql();
        // $rmb_count = $this->where('order_id in'.$subSql)->sum('order_amount');
        $rmb_count = $this->table($subSql)->alias('b')->join('LEFT JOIN __ORDER__ as a ON a.order_id = b.order_id')->sum('a.order_amount');

        // 应付金额---美元
        $map['o.currency'] = 2;
        $subSql = $this->alias('o')->join($join)->where($map)->field('DISTINCT(o.order_id)')->buildSql();
        // $usd_count = $this->where('order_id in'.$subSql)->sum('order_amount');
        $usd_count = $this->table($subSql)->alias('b')->join('LEFT JOIN __ORDER__ as a ON a.order_id = b.order_id')->sum('a.order_amount');

        // 全部订单实收金额
        $map['o.currency'] = 1;
        $rmb_pay = $this->amountHandleSql($join, $map);

        $map['o.currency'] = 2;
        $usd_pay = $this->amountHandleSql($join, $map);

        $response['rmb_count'] = $rmb_count ? $rmb_count : 0;
        $response['usd_count'] = $usd_count ? $usd_count : 0;
        $response['rmb_pay']   = $rmb_pay ? $rmb_pay : 0;
        $response['usd_pay']   = $usd_pay ? $usd_pay : 0;

        return $response;
    }

    // 金额处理
    public function amountHandleSql($join, $map)
    {
        $whereTime = array();

        // 已付金额统计
        $complex['a.is_paid'] = ['in', ['1', '2']]; // 2 是针对首款付完后，尾款尚未支付的状态
        $complex['a.pay_type'] = 4;
        $complex['_logic'] = 'or';
        $whereTime['_complex'] = $complex;

        $subSql = $this->alias('o')->join($join)->where($map)->field('DISTINCT(o.order_id)')->buildSql();

        if ($map['p.pay_time']) {
            $whereTime['a.pay_time'] = $map['p.pay_time']; // 过滤支付时间
        }
        
        // $amount = $this->table('lie_pay_log')->where('order_id in'.$subSql)->where($whereTime)->sum('pay_amount');
        $amount = $this->table($subSql)->alias('b')->join('LEFT JOIN __PAY_LOG__ as a ON a.order_id = b.order_id')->where($whereTime)->sum('a.pay_amount');

        return $amount;
    }

    // 付款人数、订单数
    public function checkPaidOrder($join, $map, $field)
    {
        $subSql = $this->alias('o')->join($join)->where($map)->field('DISTINCT(o.order_id)')->buildSql();

        // $count = $this->table('lie_pay_log')->where('order_id in'.$subSql)->count("DISTINCT(".$field.")");
        $count = $this->table($subSql)->alias('b')->join('LEFT JOIN __PAY_LOG__ as a ON a.order_id = b.order_id')->count("DISTINCT(".$field.")");

        return $count;
    }

    /**
     * 增加支付尾缀
     * @param [type] $order_id [description]
     */
    public function setSuffix($order_id)
    {
        $map = array('order_id' => $order_id);
        $res = $this->where($map)->setInc('pay_suffix', 1);
        return $res;
    }

    /**
     * 修改订单信息
     * @param [type] $order_id [description]
     * @param [type] $id       [description]
     */
    public function updateOrder($order_id,  $data)
    {
        if (is_array($order_id)) {
            $map = $order_id;
        } else {
            $map = array('order_id' => $order_id);
        }
        $res = $this->where($map)->save($data);
        return $res;
    }

    /**
     * 获取订单列表
     * @param  string $map   [description]
     * @param  string $page  [description]
     * @param  string $order [description]
     * @param  string $field [description]
     * @return [type]        [description]
     */
    public function getList($map = '', $page = '', $order = '', $field = '')
    {
        limit_page($this, $page);
        $this->where($map)->order($order);
        if (!empty($field) && strpos($field, ',') === false) {
            $list = $this->getField($field, true);
        } else {
            $list = $this->field($field)->select();
        }
        return $list;
    }

    /**
     * 获取非测试用户订单
     * @param  string $map   [description]
     * @param  string $field [description]
     * @return [type]        [description]
     */
    public function getNotTestUserList($map = '',  $field = '')
    {
//        if (empty($map['_string'])) {
//            $map['O.is_test'] = 0;
//        }
        $map['O.is_test'] = 0;
//        $this->alias('O')->join('LEFT JOIN '.C('Db_PREFIX').'user_main U ON O.user_id = U.user_id')->where($map);


        $this->alias('O')->where($map);

        if (!empty($field) && strpos($field, ',') === false) {
            $list = $this->getField($field, true);
        } else {
            $list = $this->field($field)->select();
        }
        return $list;
    }

    /**
     * 获取订单和用户信息
     * @param  string $map   [description]
     * @param  string $field [description]
     * @return [type]        [description]
     */
    public function getOrderUser($map = '', $field = '')
    {
        return [];
//        $data = $this->alias('O')->join('LEFT JOIN ' . C('Db_PREFIX').'user_main U ON O.user_id = U.user_id')->where($map)->field($field)->find();
//
//        return $data;
    }

    /*
        查询用户的订单数量
        @param  user_id int  用户id
        @param  type    int  4 已支付订单
        @return bool 

    */
    public function is_one_order($user_id,$type = -1){

        if($type != -1){
            $map['status'] = $type;
        }

        $map['user_id'] = $user_id;

        return $this->where($map)->count();
    }

    /**
     * 
     * 
     * @param mixed $user_id
     * @param mixed $uc_id
     * @param mixed $order_sn
     * @param mixed $create_time_start 2025-02-05
     * @param mixed $create_time_end   2025-03-05
     */
    public function getUserOrderCustomerIdArr($user_id,$uc_id,$order_sn,$create_time_start,$create_time_end){
        $query =  $this->where(['user_id' => $user_id,'uc_id' => $uc_id]);
        if(!empty($order_sn)){
            $query = $query->where(['order_sn' => trim($order_sn)]);
        }
        if($create_time_start){
            $query = $query->where(['create_time' => ['between', [strtotime($create_time_start), strtotime($create_time_end)]]]);
        }
        return $query->group('customer_id')
            ->field("customer_id,order_id,order_sn")
            ->select();
    }

    public function getOrerIdById($orderSn){
        $orderInfo = $this->where(['order_sn' => $orderSn])->find();
        return $orderInfo ? $orderInfo["order_id"] : 0;
    }

}