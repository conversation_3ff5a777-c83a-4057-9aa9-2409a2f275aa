<?php 
namespace Order\Model;

use Think\Model;

class OrderReturnItemsModel extends OrderBaseModel
{
    public function getList($map, $field = 'RI.*', $join = 0)
    {
        $this->where($map)->field($field);
        if ($join == 1) {
            $this->alias('RI')->join('LEFT JOIN '.C('DB_PREFIX').'order_items OI ON OI.rec_id=RI.rec_id');
        }
        $datas = $this->select();
        return $datas;
    }

    public function getInfo($order_id)
    {
        $map = array(
            'order_id' => $order_id,
        );

        $data = $this->where($map)->select();
        return $data;
    }

    public function createOrderRefundItems($refund_id, $order_id)
    {
        $this->where($map)->field($field);
        if ($join == 1) {
            $this->alias('RI')->join('LEFT JOIN '.C('DB_PREFIX').'order_items OI ON OI.rec_id=RI.rec_id');
        }
        $datas = $this->select();
        return $datas;
    }

}