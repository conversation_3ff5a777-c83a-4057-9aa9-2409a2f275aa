<?php
namespace Order\Model;

use Think\Model;

class OrderShippingModel extends OrderBaseModel {

    /**
     * 获取订单配送信息
     * @param  [type]  $order_id      [description]
     * @param  integer $shipping_type [description]
     * @return [type]                 [description]
     */
    public function getInfo($order_id, $shipping_type = 1)
    {
        $map = array(
            'O.order_id' => $order_id,
            'O.shipping_type' => $shipping_type,
        );
//        $data = $this->alias('O')->join(C('DB_PREFIX') . 'shipping S ON S.shipping_id = O.shipping_id')
//            ->where($map)
//            ->field('O.order_id, O.order_shipping_id, O.shipping_id, O.shipping_no, O.info, O.status, O.expire_time, S.shipping_name')
//            ->find();
        $data =  $this->alias('O')->where($map)->field('O.order_id, O.order_shipping_id, O.shipping_id, O.shipping_no, O.info, O.status, O.expire_time')->find();

        if (!empty($data)) {
            $data['shipping_name'] = D("Shipping")->where(["shipping_id" => $data["shipping_id"]])->getField("shipping_name");
        }

        return $data;
    }

    /**
     * 获取订单配送物流列表
     * @param  [type]  $order_id      [description]
     * @param  integer $shipping_type [description]
     * @return [type]                 [description]
     */

    public function getList($order_id, $shipping_type = 1, $shipping_no = '')
    {
        $map = array(
            'O.order_id' => $order_id,
            'O.shipping_type' => $shipping_type,
        );
        if (!empty($shipping_no)) {
            $map['O.shipping_no'] = $shipping_no;
        }
//        $data = $this->alias('O')->join('LEFT JOIN '.C('DB_PREFIX') . 'shipping S ON S.shipping_id = O.shipping_id')
//            ->where($map)
//            ->field('O.order_shipping_id, O.shipping_id, O.shipping_no, O.info, O.status, O.expire_time, S.shipping_name')
//            ->select();

        $data = $this->alias('O')
            ->where($map)
            ->field('O.order_shipping_id, O.shipping_id, O.shipping_no, O.info, O.status, O.expire_time')
            ->select();

        if (empty($data)) {
            return $data;
        }

        $shipping_ids = array_unique(array_column($data, 'shipping_id'));

        $where = [];
        $where['shipping_id'] = ['in', $shipping_ids];
        $shipInfo = D("Shipping")->where($where)->getField("shipping_id,shipping_name", true);

        foreach($data as &$v){
            $v["shipping_name"] = !empty($shipInfo[$v['shipping_id']]) ? $shipInfo[$v['shipping_id']] : "";
        }

        return $data;
    }

    public function getOnly($erp_removal_id, $order_id) {
        $map = array(
            'erp_removal_id' => $erp_removal_id,
            'order_id' => $order_id,
        );
        $data = $this->where($map)->find();
        return $data;
    }

}