<?php 
namespace Order\Model;

use Think\Model;

class OrderItemsGiftModel extends OrderBaseModel
{
    public static $STATUSOK = 1;
    public static $STATUSNONE = -1;


    /*
     * 更新赠品表信息
     */
    public function updateOrCreateByCart($cart_id, $goodsInfo = [], $priceInfo = [])
    {
        $oldInfo = $this->where(["status"=>["in",[1]], "cart_id" => $cart_id, "goods_id" => $goodsInfo["goods_id"]])->field("id")->find();
        $status = 1;
//        if($goodsInfo["gift_activity"]["total"] <= 0){
//            $status = -1;
//        }
//        dump($goodsInfo);
        if ($oldInfo) {
            //修改
            $this->where(["cart_id" => intval($cart_id),"goods_id" => $goodsInfo["goods_id"],])->save([
                "activity_id" => intval($goodsInfo["gift_activity"]['activity_id']),
                "rules" => json_encode($goodsInfo["gift_activity"]['items']),
                "type" => 1,
                "status" => $status,
                "update_time" => time(),
                "goods_amount" => round($priceInfo["num"] * $priceInfo['price'], 2),
            ]);
        } else {
            //新增
            $this->add([
                "cart_id" => intval($cart_id),
                "goods_id" => $goodsInfo["goods_id"],
                "activity_id" => intval($goodsInfo["gift_activity"]['activity_id']),
                "rules" => json_encode($goodsInfo["gift_activity"]['items']),
                "type" => 1,
                "status" => $status,
                "create_time" => time(),
                "goods_amount" => round($priceInfo["num"] * $priceInfo['price'], 2),
            ]);
        }

    }

    /*
     * 删除赠品表信息
     */
    public function deleteOrCreateByCart($cart_id){
        try{
            if(is_array($cart_id)){
                $this->where(["cart_id"=>["in",$cart_id]])->delete();
            }else if (is_string($cart_id)){
                $this->where(["cart_id"=>$cart_id])->delete();
            }
        }catch(\Exception $e){

        }
    }

    /*
     * 获取赠品表信息
     */
    public function getGiftByCart($cart=[]){
        return $this->where(["type"=>1,"status"=>1,"cart_id"=>["in",$cart]])->field("activity_id,rules,goods_amount")->select();
    }



}