<?php
namespace Order\Model;

class CmsModel extends \Common\Model\CmsModel
{
    /**
     * 获取cms用户信息
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    public function getUserInfo($id)
    {
        $map = array(
            'userId' => $id,
        );
        return $this->table('user_info')->where($map)->find();
    }

    /**
     * 获取cms名字
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    public function getUserName($id)
    {
        $map = array(
            'userId' => $id,
        );
        $data = $this->table('user_info')->where($map)->getField('name');
        return $data;
    }

    /**
     * 获取用户编码
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    public function getCodeSn($id)
    {
        $map = array(
            'userId' => $id,
        );
        $data = $this->table('user_info')->where($map)->getField('code_sn');
        return $data;
    }

    /**
     * 获取cms账号绑定的网站user_id
     * @return [type] [description]
     */
    public function getUserWebUserId($id)
    {
        $map = array(
            'admin_id' => $id,
        );
        $data = $this->table('lie_intracode')->where($map)->getField('user_id');
        return $data;
    }

    // 获取sale_id
    public function getSaleId($email='', $name='')
    {
        $map = [];
        
        $email && $map['email'] = $email;
        $name && $map['name'] = $name;

        $data = $this->table('user_info')->where($map)->getField('userId');
        return $data;
    }

    // 获取客服主管ID
    public function getKefuMgrId($id)
    {
        $map = array(
            'userId' => $id,
        );

        // 获取客服部门
        $department_id = $this->table('user_info')->where($map)->getField('department_id');

        if (!$department_id) return false;

        $where = array(
            'status' => 0,
            'department_id' => $department_id,
            'position_name' => ['like', '%经理'], //销售经理职位
        );

        $user_id = $this->table('user_info')->where($where)->getField('userId');

        return $user_id;
    }

}