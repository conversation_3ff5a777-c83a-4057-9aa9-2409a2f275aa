<?php 
namespace Order\Model;

use Think\Model;

class OrderShippingInsideModel extends OrderBaseModel
{
	/**
    * 操作记录
    * @param  [Integer] $order_id      [订单ID]
    * @return [type]           [description]
    */
    public function getInfo($order_id)
    {
        $data = $this->where(['order_id'=>$order_id])->field('order_id, info, create_time')->order('inside_type DESC, create_time DESC')->select();

        return $data;
    }

    public function getInfoType($order_id, $inside_type)
    {
        $map = array(
            'order_id' => $order_id,
            'inside_type' => $inside_type,
        );
        $res = $this->where($map)->find();
        return $res;
    }

    /**
     * 获取内部物流轨迹
     * @param  [type]  $order_id [description]
     * @param  boolean $filter   过滤人名
     * @return [type]            [description]
     */
    public function getInsideInfo($order_id, $filter = true)
    {
        $list = $this->getInfo($order_id);
        foreach ($list as $i) {
            if ($filter) {
                $i['info'] = preg_replace('/(由)?([a-zA-Z]+)/', '', $i['info']);
            }
            $data[] = array(
                'AcceptTime' => date('Y-m-d H:i:s', $i['create_time']),
                'AcceptStation' => $i['info']
            );

        }
        return $data;
    }
}