<?php 
namespace Order\Model;

use Think\Model;

class OrderRefundLogModel extends OrderBaseModel
{
    /**
     * 获取日志已退金额(含正在退款)
     * @return [type] [description]
     */
    public function getRefundedAmount($pay_log_id)
    {
        $map = array(
            'pay_log_id' => $pay_log_id,
            'refund_status' => array('in', array(1,10))
        );
        $data = $this->where($map)->sum('pay_amount');
        return $data;
    }

    /**
     * 设置退款流水状态
     * @param [type] $serial_number [description]
     */
    public function setSerialStatus($serial_number, $status, $time = null, $reason = '')
    {
        $map = array(
            'serial_number' => $serial_number,
        );
        $save = array(
            'refund_status' => $status,
        );
        $time = is_null($time) ? time() : $time;
        if ($status == 10) {//完成
            $save['refund_time'] = $time;
        } elseif ($status == -1) {//失败
            $save['fail_time'] = $time;
            $save['fail_reason'] = $reason;
        }
        $res = $this->where($map)->save($save);
        return $res;
    }

    /**
     * 设置退款单退款流水状态
     * @param [type] $serial_number [description]
     */
    public function setRefundStatus($refund_id, $status, $serial_number, $time = null, $reason = '')
    {
        $map = array(
            'refund_id' => $refund_id,
        );
        $save = array(
            'serial_number' => $serial_number,
            'refund_status' => $status,
        );
        $time = is_null($time) ? time() : $time;
        if ($status == 10) {//完成
            $save['refund_time'] = $time;
        } elseif ($status == -1) {//失败
            $save['fail_time'] = $time;
            $save['fail_reason'] = $reason;
        }
        $res = $this->where($map)->save($save);
        return $res;
    }

    /**
     * 根据退款单号获取流水日志列表
     * @param  [type] $refund_sn [description]
     * @param  string $field     [description]
     * @return [type]            [description]
     */
    public function getListBySn($refund_sn, $field = 'RL.*')
    {
        $map = array(
            'R.refund_sn' => $refund_sn
        );
        $data = $this->alias('RL')->join(C('DB_PREFIX').'order_refund R ON R.refund_id = RL.refund_id')
                    ->where($map)
                    ->field($field)
                    ->select();
        return $data;
    }

    /**
     * 获取列表
     * @param  integer $refund_id [description]
     * @param  string  $where     [description]
     * @param  string  $page      [description]
     * @param  string  $order     [description]
     * @return [type]             [description]
     */
    public function getList($refund_id = 0, $where = '', $page = '', $order = 'refund_log_id DESC', $field = 'RL.*')
    {
        $map = array(
            'RL.refund_id' => $refund_id,
        );
        if (!empty($where)) {
            $map = $where;
        }
        limit_page($this, $page);
        $data = $this->alias('RL')
                    ->join(C('DB_PREFIX').'order_refund R ON R.refund_id = RL.refund_id')
                    ->join(C('DB_PREFIX').'pay_log P ON P.pay_log_id = RL.pay_log_id')
                    ->where($map)->field($field)->order($order)->select();
        return $data;
    }
}