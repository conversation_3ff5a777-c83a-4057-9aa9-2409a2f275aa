<?php
namespace Order\Model;

use Order\Service\CartService;
use Think\Model;

class ShoppingCartModel extends Model
{

    protected $tableName = 'shipping';
    // protected $connection = 'PCB_DB_CONFIG';

    protected $_validate = array(
        // array('delivery_place', array(1,2), '交货地只能是大陆或香港', 2, 'in')
    );

    protected $_auto = array(
        array('create_time', 'time', 1, 'function'),
        array('update_time', 'time', 3, 'function'),
    );

    /**
     * 获取购物车列表
     * @param  string $map   [description]
     * @param  string $page  [description]
     * @param  string $order [description]
     * @param  string $field [description]
     * @return [type]        [description]
     */
    public function getList($map = '', $page = '', $order = 'create_time DESC', $field = '*')
    {
        if (!is_null($page)) {
            list($p, $limit) = explode(',', $page);
            empty($p) && $p = 1;
            empty($limit) && $limit = C('DEFAULT_PAGE_LIMIT');
            $this->page($p, $limit);
        }
        $datas = $this->where($map)->order($order)->field($field)->select();
        return $datas;
    }


    /**
     * 获取购物车合计
     * @param  string $map [description]
     * @return [type]      [description]
     */
    public function getCount($map = '')
    {
        $data = $this->where($map)->count();
        return $data;
    }

    /**
     * 获取指定购物车信息
     * @param  [type] $cart_id [description]
     * @return [type]          [description]
     */
    public function getInfo($cart_id)
    {
        if (is_array($cart_id)) {
            // $map = [];
            // $map['cart_id'] = $cart_id;

            return CartService::getCartListByCarIds($cart_id);
            // return $this->where($map)->select();
        }

        return CartService::getCartById($cart_id);
    }

    /**
     * 获取用户某个商品信息
     * @param  [type] $goods_id  [description]
     * @param  string $user_sign [description]
     * @param  string $user_id   [description]
     * @param  string $where     [description]
     * @return [type]            [description]
     */
    public function getUserGoods($goods_id, $user_sign = '', $user_id = '', $where = '')
    {
        $map = array(
            'goods_id' => $goods_id,
        );
        if (!empty($user_id)) {
            $map['user_id'] = $user_id;
        } else {
            $map['user_sign'] = $user_sign;
        }
        is_array($where) && $map = array_merge($map, $where);
        $data = $this->where($map)->find();
        return $data;
    }


    /**
     * 通过id获取用户购物车是否有交货地一样的某个商品
     * @param  [type] $user_id        [description]
     * @param  [type] $goods_id       [description]
     * @param  [type] $delivery_place [description]
     * @param  [type] $type           [区分前后台添加商品]
     * @return [type]                 [description]
     */
    public function getUserHasById($user_id, $goods_id, $delivery_place, $type, $org_id = 1)
    {

        $userCartList = CartService::getUserHasById($user_id);
        if (empty($userCartList)) {
            return [];
        }
        $datas=[];
        foreach($userCartList as $userCart) {
            if($goods_id == $userCart['goods_id'] && $delivery_place == $userCart['delivery_place'] && $type == $userCart['type']) {
                $datas = $userCart;
                break;
            }
        }
        return $datas;


        // $map = array(
        //     // 'org_id' => $org_id,
        //     'user_id' => $user_id,
        //     'goods_id' => $goods_id,
        //     'delivery_place' => $delivery_place,
        //     'type' => $type,
        // );

        // $datas = $this->where($map)->field('cart_id, goods_number, goods_price, sale_type')->find();
        // return $datas;
    }

    // 针对批量添加的联营物料，通过商品信息来获取购物车是否存在同一商品
    public function getUserHasByGoods($user_id, $goods, $delivery_place, $type, $org_id = 1)
    {
        $map = array(
            // 'org_id'         => $org_id,
            'user_id'        => $user_id,
            'goods_name'     => htmlspecialchars_decode($goods['goods_name']),
            'brand_name'     => htmlspecialchars_decode($goods['brand_name']),
            'goods_price'    => $goods['goods_price'],
            'supplier_name'  => $goods['supplier_name'] ?: '',
            'delivery_time'  => $goods['delivery_time'],
            'delivery_place' => $delivery_place,
            'type'           => $type,
        );
        $datas = $this->where($map)->field('cart_id, goods_number, goods_price, sale_type')->find();
        return $datas;
    }

    /**
     * 通过sign获取用户购物车是否有交货地一样的某个商品
     * @return [type] [description]
     */
    public function getUserHasBySign($user_sign, $goods_id, $delivery_place)
    {

        $userCartList = CartService::getUserHasBySign($user_sign);
        if (empty($userCartList)) {
            return [];
        }
        $datas=[];
        foreach($userCartList as $userCart) {
            if($goods_id == $userCart['goods_id'] && $delivery_place == $userCart['delivery_place']) {
                $datas = $userCart;
                break;
            }
        }
        return $datas;


        // $map = array(
        //     'user_sign' => $user_sign,
        //     'goods_id' => $goods_id,
        //     'delivery_place' => $delivery_place,
        // );
        // $datas = $this->where($map)->field('cart_id, goods_number, goods_price, sale_type')->find();
        // return $datas;
    }

    /**
     * 清空用户标志
     * @param  [type] $user_sign [description]
     * @return [type]            [description]
     */
    public function clearUserSign($user_sign)
    {
        return CartService::clearUserSign($user_sign);
        // $map = array(
        //     'user_sign' => $user_sign,
        //     'user_id' => array('gt', 0),
        // );
        // $res = $this->where($map)->setField('user_sign', '');
        // return $res;
    }

    /**
     * 查询指定用户ID和用户标识的重复商品列表
     * @param  [type] $uid [description]
     * @param  [type] $gid [description]
     * @return [type]      [description]
     */
    public function getRepeatGoods($uid, $gid)
    {
        $uidCartIdArr = CartService::getUserIdCartIds($uid);
        $gidCartIdArr = CartService::getGidCartIds($gid);
       
        if(empty($uidCartIdArr) || empty($gidCartIdArr)) {
            return [];
        }

        $uidCartList = CartService::getCartListByCarIds($uidCartIdArr);
        $gidCartList = CartService::getCartListByCarIds($gidCartIdArr);
        


        $arr = [];
        foreach($uidCartList as $uidCartId => $uidCart) {
            foreach($gidCartList as $gidCartId => $gidCart) {
                if($uidCartId == $gidCartId) {
                    continue;
                }
                if($uidCart["goods_id"] == $gidCart["goods_id"] && $uidCart["delivery_place"] == $gidCart["delivery_place"]) {
                    $tmpCartArr = $uidCart;
                    $tmpCartArr["c1_cart_id"] = $uidCart["cart_id"];
                    $tmpCartArr["c1_goods_id"] = $uidCart["goods_id"];
                    $tmpCartArr["c1_delivery_place"] = $uidCart["delivery_place"];
                    $tmpCartArr["c1_goods_number"] = $uidCart["goods_number"];
                    $tmpCartArr["c1_user_sign"] = $uidCart["user_sign"];
                    $tmpCartArr["c1_user_id"] = $uidCart["user_id"];
                    $tmpCartArr["c1_goods_data"] = $uidCart["goods_data"];
                    $tmpCartArr["c1_update_time"] = $uidCart["update_time"];

                    $tmpCartArr["c2_cart_id"] = $gidCart["cart_id"];
                    $tmpCartArr["c2_goods_id"] = $gidCart["goods_id"];
                    $tmpCartArr["c2_delivery_place"] = $gidCart["delivery_place"];
                    $tmpCartArr["c2_goods_number"] = $gidCart["goods_number"];
                    $tmpCartArr["c2_user_sign"] = $gidCart["user_sign"];
                    $tmpCartArr["c2_user_id"] = $gidCart["user_id"];
                    $tmpCartArr["c2_goods_data"] = $gidCart["goods_data"];
                    $tmpCartArr["c2_update_time"] = $gidCart["update_time"];
                    $tmpCartArr["goods_type"] = $gidCart["goods_type"];
                    $arr[] = $tmpCartArr;
                }
            }
        }
        return $arr;



        // $map = array(
        //     'C1.user_id' => $uid,
        //     'C2.user_sign' => $gid,
        //     'C1.cart_id' => array('exp', '<> C2.cart_id'),
        //     'C1.delivery_place' => array('exp', '= C2.delivery_place'),
        // );
        // $datas = $this->alias('C1')->join(C('DB_PREFIX').'shopping_cart C2 ON C1.goods_id = C2.goods_id')
        //                         ->where($map)
                                // ->field('C1.cart_id AS c1_cart_id, C1.goods_number AS c1_goods_number, C1.user_sign AS c1_user_sign, C1.user_id AS c1_user_id, C1.goods_data AS c1_goods_data, C1.update_time AS c1_update_time,
                                //     C2.cart_id AS c2_cart_id, C2.goods_number AS C2_goods_number, C2.user_sign AS c2_user_sign, C2.user_id AS c2_user_id, C2.goods_data AS c2_goods_data, C2.update_time AS c2_update_time, C2.goods_type')
        //                         ->select();
        // return $datas;
    }
}