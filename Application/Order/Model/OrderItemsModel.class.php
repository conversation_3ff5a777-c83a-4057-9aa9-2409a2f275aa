<?php
namespace Order\Model;

use Think\Model;

class OrderItemsModel extends OrderBaseModel {
    public static $AC_TYPE_NEWKJ = 5;//新客价
    public static $AC_TYPE_TUANGOU = 7;//团购价
    public static $AC_TYPE_ZHEKOU = 8;//折扣价
    public static $AC_TYPE_ACTIVITY = 2;//活动特价
    public static $AC_TYPE_HUIYUAN = 3;//会员价

    /**
     * 创建订单明细信息
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    public function createOrderItems($data, $extends = array())
    {
        foreach ($data as $k => &$v) {
            if (!in_array($k, $this->fields)) {
                unset($data[$k]);
            }
        }
        $data = array_merge($data, $extends);
        $res = $this->add($data);
        if ($res === false) {
            return false;
        }
        return $res;
    }

    /**
     * 获取信息
     * @param  [type] $rec_id [description]
     * @return [type]         [description]
     */
    public function getInfo($rec_id)
    {
        $map = array(
            'rec_id' => $rec_id,
        );
        $datas = $this->where($map)->find();
        return $datas;
    }

    /**
     * 获取订单全部商品名
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function getAllName($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'status' => 1,
        );
        $datas = $this->where($map)->getField('sku_name', true);
        return $datas;
    }

    /**
     * 获取订单sku-库存键值对
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function getItemsStockMap($order_id, $type='')
    {
        $map = array(
            'order_id' => $order_id,
            'status' => 1,
        );

        if ($type && $type == 2) { // 预售订单只获取已锁的明细
            $map['is_lock'] = 1;
        }
        
        $datas = $this->where($map)->getField('goods_id,goods_number', true);
        return $datas;
    }

    /**
     * 获取订单sku数组
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function getItemsMap($order_id, $type='')
    {
        $map = array(
            'order_id' => $order_id,
            'status' => 1,
        );

        if ($type && $type == 2) { // 预售订单只获取已锁的明细
            $map['is_lock'] = 1;
        }

        $datas = $this->where($map)->getField('goods_id', true);
        return $datas;
    }


    /*
     * 获取订单中是否有自营商品
     */
    public function checkoutOrderByZi($where=[]){
        if (!empty($where)){
            $where["order_goods_type"] = 2;
            $bk = $this->where($where)->count();
            return !!$bk;
        }
        return true;
    }

    /**
     * 获取订单商品明细
     * @param  [type] $order_id [description]
     * @param  string $where    [description]
     * @param  string $limit    [description]
     * @param  string $order    [description]
     * @return [type]           [description]
     */
    public function getOrderList($order_id, $where = '', $page = '', $order = 'rec_id')
    {
        $OrderModel = D('Order');
        $currency = $OrderModel->getFieldByOrderId($order_id, 'currency');
        $map = array(
            'OI.order_id' => $order_id,
            'OI.status' => 1,
        );
        !empty($where) && $map = $where;
        limit_page($this, $page);
        $datas = $this->alias('OI')->where($map)
                ->join("LEFT JOIN " .C('DB_PREFIX'). 'order_items_ext OIT ON OIT.rec_id = OI.rec_id')
                ->field('OI.rec_id,
                        OI.brand_area,
                         OI.order_id,
                         OI.goods_id,
                         OI.goods_name,
                         OI.sku_name,
                         OI.brand_id,
                         OI.brand_name,
                         OI.standard_brand_id,
                         OI.standard_brand_name,
                         OI.supplier_id,
                         OI.supplier_name,
                         OI.goods_sn,
                         OI.goods_number,
                         OI.goods_price,
                         OI.paid_amount,
                         OI.delivery_time,
                         OIT.goods_packing,
                         OI.original_price,
                         OI.extend_price,
                         OI.single_pre_price,
                         OI.canal,
                         OI. STATUS,
                         OI.is_lock,
                         OI.ac_type,
                         OI.contract_remark,
                         OI.extra_price,
                         OI.goods_discount_amount,
                         OI.buyer_id,
                         OI.material_number,
                         OI.erp_rec_id,
                         OI.order_goods_type,
                         OI.fqty,
                         OI.is_gift,
                         OI.is_purchase,
                         OI.is_vacuo,
                         OI.customer_material_number,
                         OIT.goods_moq,
                         OIT.goods_spq,
                         OIT.goods_class,
                         OIT.goods_encap,
                         OIT.raw_goods_sn,
                         OIT.raw_goods_packing,
                         OIT.raw_brand_name,
                         OIT.batch,
                         OIT.remarks,
                         OIT.contract_remark,
                         OIT.inquiry_item_id,
                         OIT.inquiry_id,
                         OIT.inquiry_sn,
                         OIT.quote_id,
                         OIT.quote_sn,
                         OIT.is_provide_dc,
                         OIT.is_provide_producer,
                         OIT.supplier_type,
                         OIT.pm_uid,
                         OIT.ability_level
                ')
                ->select();

        $redis  = redis_init();
        foreach ($datas as &$v) {
            $goods_id = $v["goods_id"];
            $v['goods_price_format'] = price_format($v['goods_price'], $currency, 4);
            $v['goods_amount'] = price_format($v['goods_price'] * $v['goods_number'], 0, 2);//小计
            $v['goods_amount_format'] = price_format($v['goods_amount'], $currency, 2);
            $v['single_pre_price_format'] = price_format($v['single_pre_price'], $currency, 6);
            $v['single_pre_price_amount'] = price_format($v['single_pre_price'] * $v['goods_number'], $currency, 2);
            $v['paid_amount'] = price_format($v['paid_amount'], $currency, 2); //实收金额
            // $v['goods_type'] = strlen($v['goods_id']) == 11 ? 2 : 1;
            $v['admin_goods_type'] = $v['goods_type'];
            $v['goods_type'] = in_array($v['goods_type'], [1, 2, 4, 6]) ? 1 : 2;

            // if ($v["standard_brand_id"] > 0){ //todo 2023.2.1 标准品牌
            //     $v["brand_id"] = $v["standard_brand_id"];
            //     $v["brand_name"] = $v["standard_brand_name"];
            // }else{  //订单没有标准品牌新增
            //     $standInfo = getStardarBrand($goods_id);
            //     $v["brand_id"] = $standInfo["brand_id"];
            //     $v["brand_name"] = $standInfo["brand_name"];
            // }
        }


        return $datas;
    }

    public function getList($map = '', $field = '*', $page = '', $order = 'rec_id')
    {
        !empty($where) && $map = $where;
        limit_page($this, $page);
        $datas = $this->where($map)->order($order)->field($field)->select();
        return $datas;
    }

    /**
     * 获取用户某个ERP绑定明细
     * @param  [type] $user_id    [description]
     * @param  [type] $erp_rec_id [description]
     * @return [type]             [description]
     */
    public function getUserRecId($user_id, $erp_rec_id)
    {
        $map = array(
            'user_id' => $user_id,
            'erp_rec_id' => $erp_rec_id,
        );
        $data = $this->where($map)->find();
        return $data;
    }

    /**
     * 获取ERPID的明细
     * @param  [type] $erp_rec_id [description]
     * @return [type]             [description]
     */
    public function getErpInfo($erp_rec_id)
    {
        $map = array(
            'erp_rec_id' => $erp_rec_id,
        );
        $data = $this->where($map)->find();
        return $data;
    }

    /**
     * 设置ERPID
     * @param [type] $rec_id     [description]
     * @param [type] $erp_rec_id [description]
     */
    public function setErpId($rec_id, $erp_rec_id)
    {
        $map = array(
            'rec_id' => $rec_id
        );
        $data = $this->where($map)->setField('erp_rec_id', $erp_rec_id);
        return $data;
    }

    /**
     * 获取订单商品数量合计
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function getSumNum($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'status' => 1,
        );
        $data = $this->where($map)->sum('goods_number');
        return $data;
    }

    /**
     * 商品种类
     */
    public function getSumCount($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'status' => 1,
        );
        $data = $this->where($map)->count();
        return $data;
    }



    /**
     * 获取预售订单商品
     * @param  [type] $goods_id  [description]
     * @param  string $status    [description]
     * @param  [type] $wms_order [description]
     * @return [type]            [description]
     */
    public function getPreSell($goods_id, $status = '4,7', $wms_order = null)
    {
        //筛选明细
        if (!empty($goods_id)) {
            if (is_int($goods_id)) { 
                $goods_id = strval($goods_id);
            }
            $map = array(
                'T.goods_id' => array('in', $goods_id),
                'T.order_id' => array('exp', '= OI.order_id')
            );
            $sql = $this->alias('T')->field('T.order_id')->where($map)->select(false);
        }
        $map = array(
            'O.sale_type' => 2,
            'O.status' => array('in', $status),
            'OI.status' => 1,
        );
        if (!empty($sql)) {
            $map['OI.order_id'] = array('exp', 'IN (' . $sql . ')');
        }
        $field = 'OI.rec_id, OI.goods_id, OI.goods_number, OI.order_id';
        $res = $this->alias('OI')
                    ->join(C('DB_PREFIX'). 'order O ON O.order_id = OI.order_id');
        if (!is_null($wms_order)) {
            $map['E.wms_order'] = $wms_order;
            $res = $res->join(C('DB_PREFIX'). 'order_extend E ON E.order_id = OI.order_id');
        }
        $data = $res->where($map)->field($field)->order('OI.order_id')->select();
        return $data;
    }
}