<?php
namespace Order\Model;

use Think\Model;

class OrderShippingItemsModel extends OrderBaseModel {

    public function deleteShipping($order_shipping_id)
    {
        $map = array(
            'order_shipping_id' => $order_shipping_id,
        );
        $res = $this->where($map)->delete();
        return $res;
    }

    /**
     * 获取订单商品数量合计
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function getSumNum($order_id)
    {
        $map = array(
            'order_id' => $order_id,
        );
        $data = $this->where($map)->sum('num');
        return $data;
    }

    /**
     * 获取发货单明细
     * @param  integer $order_shipping_id [description]
     * @param  integer $order_id          [description]
     * @return [type]                     [description]
     */
    public function getList($order_shipping_id = 0, $order_id = 0)
    {
        $map = array();
        if (!empty($order_id)) {
            $map['order_id'] = $order_id;
        }
        if (!empty($order_shipping_id)) {
            $map['order_shipping_id'] = $order_shipping_id;
        }
        if (empty($map)) {
            return null;
        }
        $data = $this->where($map)->select();
        return $data;
    }
}