<?php 
namespace Order\Model;

use Think\Model;

class OrderServiceModel extends OrderBaseModel
{
    
    // 售后单数量
    public function getOrderServiceCount($order_id)
    {
    	$map = [];
        $map['order_id'] = $order_id;
        return $this->where($map)->count();
    }    

    // 获取售后单
    public function getInfo($id)
    {
    	return $this->where(['id' => $id])->find();
    }

    // 获取未出库售后数量（排除取消状态）,按商品明细分组
    public function getUnremovalNum($rec_ids)
    {
        $map = [];
        $map['s.service_type'] = 1;
        $map['s.service_status'] = ['neq', -1];
        $map['i.rec_id'] = ['in', $rec_ids];

        return $this->alias('s')
                ->join('LEFT JOIN __ORDER_SERVICE_ITEMS__ as i ON s.id = i.service_id')
                ->where($map)
                ->group('i.rec_id')
                ->getField('i.rec_id, i.id, sum(i.adjust_number) as adjust_number');
    }

}