<?php
namespace Order\Model;

use Think\Model;

class KefuModel extends Model 
{
    // 从客服池获取待分配客服，删除后再重新插入并指定首条客服为待分配状态
    public function getKefu()
    {
        $kefu = $this->where(['status'=>1])->field('id, sale_id, sale_name, email, operator_id, operator_name')->find();

        if (!$kefu) return false;

        $this->where(['id'=>$kefu['id']])->delete();

        unset($kefu['id']);
        $kefu['create_time'] = time();
        $kefu['update_time'] = time();
        $this->add($kefu);

        // 重新指定待分配客服
        $data = $this->field('id')->order('id asc')->find();
        $this->where(['id'=>$data['id']])->save(['status'=>1]);

        return $kefu['sale_id'];
    }

}