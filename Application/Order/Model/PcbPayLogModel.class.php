<?php
namespace Order\Model;

use Think\Model;
/**
 * PCB订单表
 */
class PcbPayLogModel extends Model
{
    protected $tableName = 'pay_log';
    protected $connection = 'PCB_DB_CONFIG';
    
    /**
     * 获取订单信息
     * @param  integer $order_id [description]
     * @param  string  $order_sn [description]
     * @param  string  $field    [description]
     * @return [type]            [description]
     */
    public function getInfo($order_id = 0, $order_sn = '', $field = '*')
    {
        if (!empty($order_id)) {
            $map['order_id'] = $order_id;
        } elseif (!empty($order_sn)) {
            $map['order_sn'] = $order_sn;
        } else {
            return false;
        }
        $data = $this->where($map)->field($field)->find();
        return $data;
    }
}