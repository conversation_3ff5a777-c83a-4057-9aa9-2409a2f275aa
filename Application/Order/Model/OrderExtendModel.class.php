<?php 
namespace Order\Model;

use Think\Model;

class OrderExtendModel extends OrderBaseModel
{
	/**
    * 获取信息
    * @param  [type] $order_id [description]
    * @param  string $field    [description]
    * @return [type]           [description]
    */
    public function getInfo($order_id, $field = '*')
    {
        $map = array(
            'order_id' => $order_id,
        );

        $data = $this->where($map)->field($field)->find();

        if (!empty($data) && $data['status'] == 1) {
            $data['change_info'] = json_decode($data['change_info'], true);
        }

        if ($data['temp_save_info']) {
            $data['temp_save_info'] = json_decode($data['temp_save_info'], true);

            $config = C('DJKCONFIG');
            $data['temp_save_info']['product_use'] = $config['class_one'][$data['temp_save_info']['product_use_classone_sn']].'; '.$config['class_two'][$data['temp_save_info']['product_use_classone_sn']][$data['temp_save_info']['product_use_classtwo_sn']];
        }

        //因为订单拓展的样片信息字段给分出去独立的表了,所以还要单独查一次
        $sampleInfoModel = new SampleInfoModel();
        $sampleInfo = $sampleInfoModel->getSampleInfo($order_id);
        $data = array_merge($data, $sampleInfo);
        return $data;
    }

    /**
     * 修改或新增
     * @param  [type] $order_id [description]
     * @param  array  $data     [description]
     * @return [type]           [description]
     */
    public function updateOrInsert($order_id, $data = array())
    {
        $map = array(
            'order_id' => $order_id,
        );
        $info = $this->where($map)->find();
        if (empty($info)) {
            $data['order_id'] = $order_id;
            $res = $this->add($data);
        } else {
            unset($data['order_id']);
            $res = $this->where($map)->save($data);
        }
        return $res;
    }

    /**
     * 新增
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    public function createExtent($data)
    {
        $res = $this->add($data);
        return $res;
    }

    /**
     * 获取最近订单的兑人民币汇率
     * @return [type] [description]
     */
    public function getLastExchangeRate()
    {
        return $this->where(['exchange_rate' => ['gt', 1]])->order('order_id DESC')->getField('exchange_rate');
    }
}