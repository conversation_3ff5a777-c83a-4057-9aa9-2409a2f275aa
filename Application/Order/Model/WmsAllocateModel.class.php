<?php
namespace Order\Model;

use Think\Model;

class WmsAllocateModel extends Model
{
    protected $connection = 'WMS_DB_CONFIG';
    protected $tableName = 'allocate';

    // 获取当前订单的调拨单
    public function getAllocateByOrderId($order_id)
    {
        $map = [];
        $map['upstream_order_id'] = $order_id;
        $map['allocate_status'] = ['neq', 7];

        return $this->where($map)->order('allocate_id desc')->find();
    }
}