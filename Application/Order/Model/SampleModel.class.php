<?php

namespace Order\Model;

use Think\Model;

class SampleModel extends Model
{

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.ZYGOODS');
    }

    // 增加样片库存
    public function addSampleStock($goods_id, $num)
    {
        return $this->where(['goods_id' => $goods_id])->setInc('sample_stock', $num);
    }

    // 扣减样片库存
    public function decSampleStock($goods_id, $num)
    {
        return $this->where(['goods_id' => $goods_id])->setDec('sample_stock', $num);
    }
    
}