<?php
namespace Order\Model;

use Think\Model;

class OrderInvoiceModel extends OrderBaseModel {

    /**
     * 添加订单发票
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    public function createOrderInvoice($data)
    {
        if (!$this->create($data)) {
            return false;
        }
        $res = $this->add();
        return $res;
    }

    /**
     * 获取信息
     * @param  [type] $order_id [description]
     * @param  string $field    [description]
     * @return [type]           [description]
     */
    public function getInfo($order_id, $field = '*')
    {
        $map = array(
            'order_id' => $order_id,
        );
        $datas = $this->where($map)->field($field)->find();
        return $datas;
    }

    /**
     * 获取列表
     * @param  string $map   [description]
     * @param  string $limit [description]
     * @param  string $order [description]
     * @return [type]        [description]
     */
    public function getUserList($user_id, $where = '', $page = '', $order = 'I.order_id DESC')
    {
        $map = array(
            'O.user_id' => $user_id,
        );
        limit_page($this, $page);
        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->alias('I')->join(C('DB_PREFIX'). 'order O ON O.order_id = I.order_id')
                        ->where($map)->order($order)
                        ->field('I.order_id, I.order_sn, I.inv_type, I.invoice_status, O.order_amount, O.status')
                        ->select();
        foreach ($datas as &$v) {
            $v['order_amount_format'] = price_format($v['order_amount'], 1);
        }
        return $datas;
    }

    /**
     * 获取列表数
     * @param  [type] $user_id [description]
     * @param  string $where   [description]
     * @return [type]          [description]
     */
    public function getUserCount($user_id, $where = '')
    {
        $map = array(
            'O.user_id' => $user_id,
        );
        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->alias('I')->join(C('DB_PREFIX'). 'order O ON O.order_id = I.order_id')
                        ->where($map)
                        ->count();
        return $datas;
    }
}