<?php
namespace Order\Model;

use Think\Model;

class RemovalItemsModel extends OrderBaseModel
{
    // 获取出库明细
    public function getInfo($order_id, $field='*')
    {
        $map = array(
            'order_id' => $order_id,
        );

        $datas = $this->where($map)->field($field)->select();
        
        return $datas;
    }

    /**
     * 获取订单商品出库数量合计
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function getSumNum($order_id)
    {
        $map = array(
            'order_id' => $order_id,
        );
        $data = $this->where($map)->sum('removal_number');
        return $data;
    }

    public function getList($map = array())
    {
        $data = $this->where($map)->select();
        return $data;
    }
}