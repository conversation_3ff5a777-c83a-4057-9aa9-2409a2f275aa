<?php

namespace Order\Model;

use Think\Log;
use Think\Model;

class SampleInfoModel extends OrderBaseModel
{
    public function addSampleInfo($data)
    {
        $data['create_time'] = time();
        return $this->add($data);
    }

    public function getSampleInfo($orderId)
    {
        $map['order_id'] = $orderId;
        $sampleInfo = $this->where($map)
            ->field('sample_project_name,sample_demand_desc,sample_project_stage,sample_project_reason')
            ->find();
        if (empty($sampleInfo)) {
            return [
                'sample_project_name' => '',
                'sample_demand_desc' => '',
                'sample_project_stage' => '',
                'sample_project_reason' => '',
            ];
        }
        return $sampleInfo;
    }
}