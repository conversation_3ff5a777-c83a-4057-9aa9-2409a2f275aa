<?php
namespace Order\Model;

use Think\Model;

class OrderGoodsModel extends OrderBaseModel
{
    public function getOrderList($order_sn, $where = '', $limit = '', $order = 'rec_id')
    {
        $map = array(
            'order_sn' => $order_sn,
        );
        !empty($where) && $map = $where;
        $datas = $this->where($map)->limit($limit)->order($order)
                        ->field('rec_id, order_sn, goods_id, goods_name, goods_number, currency, goods_price, discount_rate, shipping_status, goods_attr')
                        ->select();
        foreach ($datas as &$v) {
            $attr = json_decode($v['goods_attr'], true);
            unset($v['goods_attr']);
            $v['brand_name'] = empty($attr['brand_name']) ? '' : $attr['brand_name'];
            $v['goods_price_format'] = price_format($v['goods_price'], C('CURRENCY_MAPPING.'. $v['currency']), 4);
            $v['goods_total'] = price_format($v['goods_price'] * $v['goods_number'], 0, 4);//小计
            $v['goods_total_format'] = price_format($v['goods_total'], C('CURRENCY_MAPPING.'. $v['currency']), 4);
        }
        return $datas;
    }
}