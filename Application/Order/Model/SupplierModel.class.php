<?php
namespace Order\Model;

use Think\Model;

class SupplierModel extends Model
{
    protected $tableName = 'supplier';

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.SPU');
    }

    // 获取供应商
    public function getSupplierInfo($supplier_id = '', $supplier_name = '')
    {
        $map = [];

        if ($supplier_id) {
            $map['supplier_id'] = $supplier_id;
        }

        if ($supplier_name) {
            $map['supplier_name'] = $supplier_name;
        }

        return $this->where($map)->find();
    }

    public function getChannelTypeByName($supplier_name)
    {
        if (is_array($supplier_name)) {
            $map = [];
            $map['supplier_name'] = ['in', $supplier_name];

            return $this->where($map)->getField('supplier_name, channel_type');
        }

        return self::where(['supplier_name' => $supplier_name])->getField('channel_type');
    }


}