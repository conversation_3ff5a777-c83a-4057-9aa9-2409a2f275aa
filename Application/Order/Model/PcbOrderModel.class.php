<?php
namespace Order\Model;

use Think\Model;
/**
 * PCB订单表
 */
class PcbOrderModel extends Model
{
    protected $tableName = 'order';
    protected $connection = 'PCB_DB_CONFIG';

    /**
     * 获取订单信息
     * @param  integer $order_id [description]
     * @param  string  $order_sn [description]
     * @param  string  $field    [description]
     * @return [type]            [description]
     */
    public function getInfo($order_id = 0, $order_sn = '', $field = '*')
    {
        if (!empty($order_id)) {
            $map['order_id'] = $order_id;
        } elseif (!empty($order_sn)) {
            $map['order_sn'] = $order_sn;
        } else {
            return false;
        }
        $data = $this->where($map)->field($field)->find();
        return $data;
    }

    /**
     * 修改信息
     * @param  [type] $order_id [description]
     * @param  array  $save     [description]
     * @return [type]           [description]
     */
    public function updateInfo($order_id, $save = array())
    {
        $map = array(
            'order_id' => $order_id
        );
        $res = $this->where($map)->save($save);
        return $res;
    }
}