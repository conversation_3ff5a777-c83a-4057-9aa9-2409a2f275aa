<?php 
namespace Order\Model;

use Think\Model;

class OrderRefundItemsModel extends OrderBaseModel
{
    // 退款明细
    public function getInfo($order_id)
    {
        $map = array(
            'order_id' => $order_id,
        );

        $field = 'refund_rec_id, refund_id, order_id, rec_id, goods_id, goods_name, brand_id, brand_name, supplier_id, supplier_name, sku_name, goods_price, single_pre_price, refund_num, create_time';

        $data = $this->where($map)->field($field)->select();

        return $data;
    }

    // 添加退款明细
    public function createOrderRefundItems($refund_id, $order_id)
    {
        $OrderItemsModel = D('orderItems');

        // 退款明细
        $orderItems = $OrderItemsModel->getOrderList($order_id);

        $addItemsData = [];

        foreach ($orderItems as $k=>$v) {
            $addItemsData[$k]['refund_id']        = $refund_id;
            $addItemsData[$k]['order_id']         = $order_id;
            $addItemsData[$k]['rec_id']           = $v['rec_id'];
            $addItemsData[$k]['goods_id']         = $v['goods_id'];
            $addItemsData[$k]['goods_name']       = $v['goods_name'];
            $addItemsData[$k]['brand_id']         = $v['brand_id'];
            $addItemsData[$k]['brand_name']       = $v['brand_name'];
            $addItemsData[$k]['supplier_id']      = $v['supplier_id'];
            $addItemsData[$k]['supplier_name']    = $v['supplier_name'];
            $addItemsData[$k]['sku_name']         = $v['sku_name'];
            $addItemsData[$k]['goods_price']      = $v['goods_price'];
            $addItemsData[$k]['single_pre_price'] = $v['single_pre_price'];
            $addItemsData[$k]['refund_num']       = $v['goods_number'];
            $addItemsData[$k]['create_time']      = time();            
        }

        $res = $this->addAll($addItemsData);

        return $res;
    }

}