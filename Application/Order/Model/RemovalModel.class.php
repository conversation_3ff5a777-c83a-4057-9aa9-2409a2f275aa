<?php
namespace Order\Model;

use Think\Model;

class RemovalModel extends OrderBaseModel
{

    /**
     * 创建出库记录
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    public function createRemoval($data)
    {
        if (!empty($data['order_id']) && (empty($data['order_sn']) || empty($data['user_id']))) {
            $OrderModel = D('Order');
            $info = $OrderModel->getInfo($data['order_id']);
            $data['order_sn'] = $info['order_sn'];
            $data['user_id'] = $info['user_id'];
        }
        if (empty($data['shipping_type']) && !empty($data['shipping_sn'])) {
            $data['shipping_type'] = 1;//默认快递
        }
        $res = $this->add($data);
        return $res;
    }

    public function getInfo($removal_id)
    {
        if (is_array($removal_id)) {
            $map = $removal_id;
        } else {
            $map = array(
                'removal_id' => $removal_id,
            );
        }
        $data = $this->where($map)->find();
        return $data;
    }

    public function getInfoWithOrder($removal_id, $field = '*')
    {
        $map = array(
            'removal_id' => $removal_id
        );
        $data = $this->alias('R')->join(C('DB_PREFIX').'order O ON O.order_id = R.order_id')->where($map)
                        ->field($field)
                        ->find();
        return $data;
    }
}