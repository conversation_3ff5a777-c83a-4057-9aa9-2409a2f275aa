<?php

namespace Order\Model;

use Common\Model\RedisModel;
use Think\Model;

/**
 * 魔方
 */
class GiftActivityModel extends Model
{
    protected $tableName = 'gift_activity';
    protected $connection = 'SPECIAL_DB_CONFIG';

    //减少满赠活动redis数据的发行量
    //$amount对应的是价格阶梯
    public function reduceGiftItemTotal($activityId, $amount, $reduceNum = 1)
    {
        //先找出对应的活动信息
        $activity = $this->where(['id' => $activityId])->find();
        $redisKey = 'lie_gift_activity';
        //将data里面的supplier_ids拆分出来,比如supplier_ids是1,2,3,则在redis存3条数据代表3个供应商
        $supplierIdList = explode(',', $activity['supplier_ids']);
        $redis = redis_init();
        foreach ($supplierIdList as $supplierId) {
            $activities = $redis->hget($redisKey, $supplierId);
            $activities = $activities ? \GuzzleHttp\json_decode($activities, true) : [];
            foreach ($activities as $key => $activity) {
                if ($activity['activity_id'] == $activityId) {
                    //去修改发行量
                    if ($activity['item_list']) {
                        foreach ($activity['item_list'] as $k => &$item) {
                            if ($item['amount'] == $amount) {
                                $item['total'] = $item['total'] - $reduceNum;
                                $item['total'] = $item['total'] >= 0 ? $item['total'] : 0;
                            }
                        }
                        unset($item);
                    }
                    $activities[$key] = $activity;
                }
            }
            $redis->hset($redisKey, $supplierId, json_encode($activities));
        }
    }

}