<?php
namespace Order\Model;

use Think\Model;

class OrderAddressModel extends OrderBaseModel {

    public function createOrderAddress($data)
    {
        if (!$this->create($data)) {
            return false;
        }
        $res = $this->add();
        return $res;
    }

    /**
     * 获取订单地址信息
     * @param  [type]  $order_id     订单ID
     * @param  integer $address_type 地址类型 1订单 2发票
     * @return [type]                [description]
     */
    public function getInfo($order_id, $address_type = 1)
    {
        $map = array(
            'order_id' => $order_id,
            'address_type' => $address_type,
        );
        $res = $this->where($map)->find();
        if (!empty($res)) {
            $nation = get_nation($res['nation_id']);
            $res['nation_cn'] = $nation['name_cn'];
            $res['nation_en'] = $nation['name_en'];

            $res['province_val'] = '';
            $res['city_val'] = '';
            $res['district_val'] = '';
            if (!empty($res['province'])) {
                $res['province_val'] = get_province($res['province']);
            }
            if (!empty($res['city'])) {
                $res['city_val'] = get_city($res['city']);
            }
            if (!empty($res['district'])) {
                $res['district_val'] = get_district($res['district']);
            }
        }
        return $res;
    }

}