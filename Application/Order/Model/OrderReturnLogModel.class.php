<?php 
namespace Order\Model;

use Think\Model;

class OrderReturnLogModel extends OrderBaseModel
{
    /**
     * 创建退货记录
     * @param [type] $return_id   [退货单ID]
     * @param [type] $operator_id [操作人ID]
     * @param [type] $event       [操作事件]
     */
    public function addLog($return_id, $operator_id, $event)
    {
    	$operator_name = D('Cms')->getUserName($operator_id); // 操作人名称 
            
        $return_log['return_id']     = $return_id;
        $return_log['operator_id']   = $operator_id;
        $return_log['operator_name'] = $operator_name;
        $return_log['event']         = $event;
        $return_log['ip']            = get_client_ip(0, true);
        $return_log['create_time']   = time();

        return $this->add($return_log);
    }

}