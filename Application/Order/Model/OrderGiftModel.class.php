<?php 
namespace Order\Model;

use Think\Model;

class OrderGiftModel extends OrderBaseModel
{
    
    // 获取订单赠品
    public function getOrderGift($order_id, $status='')
    {
        $map = [];
        $map['order_id'] = $order_id;

        if (is_array($status)) $map['status'] = ['in', $status];
        if ($status && is_string($status)) $map['status'] = $status;

        $data = $this->where($map)->field("gift_info,status")->select();

        if (empty($data)) return false;

        foreach ($data as &$v) {
            $v['gift_info'] = json_decode($v['gift_info'], true);
        } 

        return $data;
    }

    /*
     * 判断订单是否含有赠品
     */
    public function countOrderGift($order_id){
        $count = $this->where(['order_id' => $order_id,"status"=>["gt",0]])->count("id");
        return ($count > 0) ? 1 : 0;
    }

}