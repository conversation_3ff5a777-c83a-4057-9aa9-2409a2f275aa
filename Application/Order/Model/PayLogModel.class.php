<?php
namespace Order\Model;

use Think\Model;

class PayLogModel extends OrderBaseModel
{
    /**
     * 获取信息
     * @param  [type] $order_id [description]
     * @param  string $field    [description]
     * @return [type]           [description]
     */
    public function getInfo($order_id, $field = '*', $find = false)
    {
        if (is_array($order_id)) {
            $map = $order_id;
        } else {
            $map = array(
                'order_id' => $order_id,
            );
        }

        $data = $this->where($map)->field($field);
        if ($find) {
            $data = $data->find();
        } else {
            $data = $data->select();
        }

        return $data;
    }

    /**
     * 获取最后客户实际付款金额
     * @param  [type] $order_id [description]
     * @param  string $field    [description]
     * @return [type]           [description]
     */
    public function getLastPay($order_id, $field = '*')
    {
        if (is_array($order_id)) {
            $map = $order_id;
        } else {
            $map = array(
                'order_id' => $order_id,
            );
        }
        $data = $this->where($map)->field($field)->order('pay_time DESC')->find();
        return $data;
    }

    /**
     * 通过日志获取实际收钱金额（账期付款,未实际付款不算进收款金额）
     */
    public function getPayedAmount($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'is_paid'  => 1,
        );
        $data = $this->where($map)->sum('pay_amount');
        return $data;
    }

    /**
     * 获取最后支付金额
     */
    public function getLastPayed($order_id, $field = '*')
    {
        $map = array(
            'order_id' => $order_id,
            // 'is_paid' => 1,
        );
        $data = $this->where($map)->order('pay_time DESC')->field($field)->find();
        return $data;
    }

    /**
     * 根据退款 设置记录退款金额
     * @param [type]  $pay_log_id    [description]
     * @param [type]  $refund_amount [description]
     * @param boolean $diff          [description]
     */
    public function setRefundAmount($pay_log_id, $refund_amount, $diff = false)
    {
        if (is_array($pay_log_id)) {
            $map = $pay_log_id;
        } else {
            $map = array(
                'pay_log_id' => $pay_log_id,
            );
        }

        if ($diff) {
            $res = $this->where($map)->setInc('refund_amount', $refund_amount);
        } else {
            $res = $this->where($map)->setField('refund_amount', $refund_amount);
        }

        return $res;
    }

    /*
    获取某个用户在运营活动期间的支付金额
     */
    public function activity_pay_log($user_id, $start_time = 1567958401, $end_time = 1569859199 ,$pay_start_time = 0,$pay_end_time = 0)
    {

        //下单时间与支付时间均要在指定的时间内才行

        //没有配置支付时间  支付时间用活动时间 否则用指定的支付时间
        if($pay_start_time != 0 && $pay_end_time !=0 ){
            $map['lie_pay_log.pay_time'] = array(array('gt',$pay_start_time),array('lt',$pay_end_time)) ;
        }else{
             $map['lie_pay_log.pay_time'] = array(array('gt',$start_time),array('lt',$end_time)) ;
        }
        
        
        $map['lie_order.create_time'] = array(array('gt',$start_time),array('lt',$end_time)) ;

        $map['lie_pay_log.is_paid']       = 1;
        $map['lie_pay_log.refund_amount'] = 0; //退款金额
        $map['lie_pay_log.user_id']       = $user_id;
        $map['lie_pay_log.pay_type']      = array('neq', 4); //不等于账期(账期订单不参与活动)
        $map['lie_order.status']        = array('gt',2);//必须要是付款后才算参与活动了

        return $this->join('lie_order ON lie_order.order_sn = lie_pay_log.order_sn')->where($map)->sum('pay_amount');
    }

    /*
        获取用户付款次数和时间
    */
    public function order_count($user_id){
        
        $map['lie_pay_log.is_paid']       = 1;
        $map['lie_pay_log.refund_amount'] = 0; //退款金额
        $map['lie_pay_log.user_id']       = $user_id;
        $map['lie_pay_log.pay_type']      = array('neq', 4); //不等于账期(账期订单不参与活动)
        $map['lie_order.status']        = array('gt',2);//必须要是付款后才算参与活动了

        return $this->join('lie_order ON lie_order.order_sn = lie_pay_log.order_sn')
                    ->where($map)->field('lie_pay_log.pay_log_id,lie_pay_log.pay_time')
                    ->order('lie_pay_log.pay_log_id desc')->select();
       
    }

}
