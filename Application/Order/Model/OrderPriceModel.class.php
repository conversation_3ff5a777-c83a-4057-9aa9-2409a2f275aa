<?php
namespace Order\Model;

use Think\Model;

class OrderPriceModel extends OrderBaseModel {

    /**
     * 创建订单金额信息
     * @param  [type]  $order_id 订单ID
     * @param  mix     $price    数组时为批量创建多类型金额 type => price, 字符串时为金额，配合type参数
     * @param  [type]  $type     金额类型
     * @param  integer $currency 币种
     * @param  string  $order_sn 订单号（冗余）
     * @return [type]            [description]
     */
    public function createOrderPrice($order_id, $price, $type, $currency = 1, $order_sn = '', $time = '')
    {
        if (empty($order_sn)) {
            $OrderModel = D('Order');
            $order_sn = $OrderModel->getFieldByOrderId($order_id, 'order_sn');
        }
        empty($time) && $time = time();
        if (is_array($price)) {
            foreach ($price as $k => $v) {
                if (is_array($v)) {
                    $price_data = $v[0];
                    $time = $v[1];
                } else {
                    $price_data = $v;
                }
                if (in_array($price_data, array(0, '0.00'))) {
                    continue;
                }
                $data = array(
                    'order_id' => $order_id,
                    'order_sn' => $order_sn,
                    'price' => $price_data,
                    'price_type' => $k,
                    'currency' => $currency,
                    'create_time' => $time,
                );
                $res = $this->add($data);
            }
        } else {
            $data = array(
                'order_id' => $order_id,
                'order_sn' => $order_sn,
                'price' => $price,
                'price_type' => $type,
                'currency' => $currency,
                'create_time' => $time,
            );
            $res = $this->add($data);
        }
        return $res;
    }

    /**
     * 获取支付优惠金额
     * @return [type] [description]
     */
    public function getPayPreferential($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => -7,
        );
        $price = $this->where($map)->sum('price');
        return $price;
    }

    /**
     * 获取客户已付金额
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function getClientPayed($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => array('in', C('ORDER_CLIENT_PAYED_STATUS')),
        );
        $price = $this->where($map)->sum('price');
        return $price;
    }

    /**
     * 获取已付金额
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function getPayed($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => array('in', C('ORDER_PAYED_STATUS')),
        );
        $price = $this->where($map)->sum('price');
        return $price;
    }

    /**
     * 获取附加费
     * @param  [type] $order_id [description]
     * @return [type] [description]
     */
    public function getExtPrice($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => array('in', C('ORDER_EXT_STATUS')),
        );
        $price = $this->where($map)->sum('price');
        return $price;
    }

    /**
     * 获取订单总金额
     * @param  [type] $order_id [description]
     * @return [type] [description]
     */
    public function getOrderTotalPrice($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => array('in', C('ORDER_PRICE_TOTAL_STATUS')),
        );
        $price = $this->where($map)->sum('price');
        return $price;
    }

    /**
     * 获取总商品价格
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function getGoodsPrice($order_id, $mulit = false)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => 1,
        );
        if ($mulit) {
            $map['order_id'] = array('in', $order_id);
            $price = $this->where($map)->group('order_id')->getField('order_id, price', true);
            $price = $this->mulit_return($order_id, $price);
        } else {
            $price = floatval($this->where($map)->getField('price'));
        }
        return $price;
    }

    /**
     * 获取优惠券金额
     * @return [type] [description]
     */
    public function getPreferentialPrice($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => -4,
        );
        $price = floatval($this->where($map)->getField('price'));
        return $price;
    }

    /**
     * 获取新客价优惠金额
     * @return [type] [description]
     */
    public function getNewClientPrice($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => -8,
        );
        $price = floatval($this->where($map)->getField('price'));
        return $price;
    }

    /**
     * 获取活动优惠金额
     * @return [type] [description]
     */
    public function getActivityPrice($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => -8,
        );
        $price = floatval($this->where($map)->getField('price'));
        return $price;
    }

    /**
     * 获取所有优惠金额
     * @param  [type]  $order_id [description]
     * @param  boolean $mulit    [description]
     * @return [type]            [description]
     */
    public function getAllPreferentialPrice($order_id, $mulit = false)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => array('in', '-4,-7'),
        );
        if ($mulit) {
            $map['order_id'] = array('in', $order_id);
            $price = $this->where($map)->group('order_id')->getField('order_id, SUM(price)', true);
            $price = $this->mulit_return($order_id, $price);

        } else {
            $price = floatval($this->where($map)->sum('price'));
        }
        return $price;
    }

    /**
     * 获取运费
     * @return [type]  [description]
     */
    public function getShippingPrice($order_id, $mulit = false)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => array('in', '-6,3'),
        );
        if ($mulit) {
            $map['order_id'] = array('in', $order_id);
            $price = $this->where($map)->group('order_id')->getField('order_id, SUM(price)', true);
            $price = $this->mulit_return($order_id, $price);

        } else {
            $price = floatval($this->where($map)->sum('price'));
            if ($price < 0) {
                $price = 0;
            }
        }
        return $price;
    }

    /**
     * 获取退款金额
     * @return [type] [description]
     */
    public function getRefundPrice($order_id)
    {
        $map = array(
            'order_id' => $order_id,
            'price_type' => 4,
        );
        $price = floatval($this->where($map)->getField('price'));
        return $price;
    }

    /**
     * 获取当前应付金额
     * @param  [type] $order_id       订单id
     * @param  string $order_pay_type 支付类型（可选）
     * @param  string $status         状态（可选）
     * @param  string $advance_price  预付金额（可选）
     * @return [type]                 [description]
     */
    public function getToPay($order_id, $order_pay_type = null, $status = null, $advance_price = null)
    {
        if (is_null($status) || is_null($order_pay_type) || is_null($advance_price)) {
            $OrderModel = D('Order');
            $info = $OrderModel->getInfo($order_id);
            $status = $info['status'];
            $order_pay_type = $info['order_pay_type'];
            $advance_price = $info['advance_amount'];
        }

        $payed_price = $this->getPayed($order_id);
        if ($status <= 2 && $order_pay_type == 2) {
            $topay_price = $advance_price + $payed_price;
        } else {
            $map = array(
                'order_id' => $order_id,
                'price_type' => array('in', C('ORDER_TOPAY_STATUS')),
            );
            $topay_price = $this->where($map)->sum('price');
        }

        if ($topay_price == 0){ //订单日志查不到收款金额改成查订单明细收款信息
            $map = array(
                'order_id' => $order_id,
                'status' => ['in', [1]],
            );
            $topay_price = D("OrderItems")->where($map)->sum('paid_amount');
        }
        return $topay_price;
    }

    /**
     * 批量查询是返回
     * @param  [type] $order_ids [description]
     * @param  [type] $res       [description]
     * @return [type]            [description]
     */
    private function mulit_return($order_ids, $res)
    {
        $id_arr = array_fill_keys($order_ids, 0);
        if (!empty($res)) {
            foreach ($id_arr as $k => &$v) {
                if (isset($res[$k])) {
                    $v = $res[$k];
                }
            }
            unset($v);
        }
        return $id_arr;
    }
}