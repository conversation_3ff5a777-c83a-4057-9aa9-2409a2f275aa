<?php 
namespace Order\Model;

use Think\Model;

class OrderReturnModel extends OrderBaseModel
{
    /**
     * 获取同步列表
     * @param  integer $syn_sign [description]
     * @param  [type]  $status   [description]
     * @param  string  $field    字段
     * @return [type]            [description]
     */
    public function getSynList($syn_sign = 1, $status = null, $field = '*')
    {
        $map = array(
            'syn_sign' => $syn_sign,
        );
        if (!is_null($status)) {
            $map['status'] = $status;
        }
        $datas = $this->where($map)->field($field)->select();
        return $datas;
    }

    public function setSyn($return_id, $syn_sign)
    {
        $map = array(
            'return_id' => array('in', $return_id),
        );
        $save = array(
            'syn_sign' => $syn_sign,
            'syn_time' => time(),
        );
        $res = $this->where($map)->save($save);
        return $res;
    }

    // 退货单数量
    public function countReturn($order_id)
    {
        $map['order_id'] = $order_id;
        return $this->where($map)->count();
    }

    // 获取退货列表
    public function getReturnList($map, $p, $size)
    {
        $p = isset($p) ? $p : 1;

        $first = $size * ($p-1);

        $where = array();

        $search = $this->_returnSearch($map);

        $where = $search['where'];
        $sort  = $search['sort'];
        $join  = $search['join'];
        $field = $search['field'];

        // 添加业务员ID查询
        if ($sale_id) {
            $where['o.sale_id'] = $sale_id;
        }

        // 查询订单总数
        $response['count'] = $this->alias('r')
                            ->join($join)
                            ->where($where)
                            ->count(); // 去重

        //分页查询数据
        $response['data'] = $this->alias('r')
                        ->join($join)
                        ->where($where)
                        ->field($field)
                        ->limit($first.','.$size)
                        ->order($sort)
                        ->select();

        return $response;
    }

    // 搜索条件
    public function _returnSearch($map)
    {
        $where = array();
        $join = '';

        // 订单编号
        if (!empty($map['order_sn'])) {
            $where['r.order_sn'] = $map['order_sn'];
        }

        // SKU名称
        if (!empty($map['sku_name'])) {
            $where['it.sku_name'] = $map['sku_name'];

            $join .= "LEFT JOIN __ORDER_REFUND_ITEMS__ it ON it.refund_id = r.refund_id";
        }

        // 创建时间查询
        if (!empty($map['time_start']) || !empty($map['time_end'])) {
            if (!empty($map['time_start']) && !empty($map['time_end'])) {
                $where['r.create_time'] = ['between', [$map['time_start'], $map['time_end']]];
            } else if (!empty($map['time_start'])) {
                $where['r.create_time'] = ['gt', $map['time_start']];
            } else {
                $where['r.create_time'] = ['lt', $map['time_end']]; // 结束时间加上一天秒数减1
            }
        }

        // 支付方式
        if (!empty($map['order_payment_mode'])) {
            $pay_name = explode(',', $map['order_payment_mode']);

            foreach ($pay_name as $v) {
                switch ($v) {
                    case '1': $order_payment_mode[] = '微信支付';break;
                    case '2': $order_payment_mode[] = '支付宝';break;
                    case '3': $order_payment_mode[] = '银联支付(B2B)';break;
                    case '4': $order_payment_mode[] = '银联支付(B2C)';break;
                    case '5': $order_payment_mode[] = '账期支付';break;
                    case '6': $order_payment_mode[] = '京东支付';break;
                    case '7': $order_payment_mode[] = '交通银行';break;
                    case '8': $order_payment_mode[] = '恒生银行';break;
                    case '9': $order_payment_mode[] = '钱包支付';break;
                }
            }

            $where['p.pay_name'] = ['in', $order_payment_mode];
            $join .= " LEFT JOIN __PAY_LOG__ p ON r.order_id = p.order_id";
        }

        // 状态
        if (!empty($map['apply_status'])) {
            $where['r.status'] = ['in', $map['apply_status']];
        }

        // 是否退款
        if (!empty($map['is_refund'])) {
            $where['r.is_refund'] = ['in', $map['is_refund']];
        }

        // 客服查看自己的退货单
        if (isset($map['create_uid'])) {
            $where['r.create_uid'] = $map['create_uid'];
        }

        // 订单类型
        $where['r.order_goods_type'] = $map['order_goods_type'];   

        $field = 'r.return_id, r.return_sn, r.order_id, r.order_sn, r.removal_sn, r.user_id, r.mobile, r.email, r.company_name, r.currency, r.return_way, r.pay_amount, r.return_amount, r.return_reason, r.status, r.is_refund, r.cancel_reason, r.refuse_reason, r.create_uid, r.audit_uid, r.create_time, r.audit_time';

        $response['where'] = $where;
        $response['join']  = $join;
        $response['field'] = $field;
        $response['sort']  = 'r.create_time DESC, r.return_id DESC';

        return $response;
    }

}