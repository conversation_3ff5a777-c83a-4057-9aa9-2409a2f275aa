<?php 
namespace Order\Model;

use Think\Model;

class OrderServiceLogModel extends OrderBaseModel
{
    /**
     * 创建售后单日志
     * @param [type] $service_id  [售后单ID]
     * @param [type] $operator_id [操作人ID]
     * @param [type] $event       [操作事件]
     */
    public function addLog($service_id, $operator_id, $event)
    {
    	$operator_name = D('Order/Cms')->getUserName($operator_id); // 操作人名称 
            
        $log['service_id']    = $service_id;
        $log['operator_id']   = $operator_id;
        $log['operator_name'] = $operator_name;
        $log['event']         = $event;
        $log['ip']            = get_client_ip(0, true);
        $log['create_time']   = time();

        return $this->add($log);
    }

}