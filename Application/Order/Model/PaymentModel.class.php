<?php
namespace Order\Model;

use Think\Model;

class PaymentModel extends Model {

    public function getList($where = '')
    {
        $map = array(
            'pay_type' => 1,
            'enabled' => 1,
        );
        is_array($where) && $map = array_merge($map, $where);
        $datas = $this->where($map)->field('pay_id, pay_code, pay_name, pay_desc, pay_config')->order('sort DESC')->select();
        return $datas;
    }

    public function getInfoByCode($pay_code)
    {
        $map = array(
            'pay_code' => $pay_code,
            'enabled' => 1,
        );
        $datas = $this->where($map)->field('pay_id, pay_name')->find();
        return $datas;
    }
}