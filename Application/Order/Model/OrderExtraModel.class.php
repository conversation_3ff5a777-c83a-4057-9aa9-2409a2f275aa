<?php 
namespace Order\Model;

use Think\Model;

class OrderExtraModel extends OrderBaseModel
{
	/**
    * 获取信息
    * @param  [type] $order_id [description]
    * @param  string $field    [description]
    * @return [type]           [description]
    */
    public function getInfo($order_id, $field = '*')
    {
        $map = array(
            'order_id' => $order_id,
        );

        $data = $this->where($map)->field($field)->find();

        if (!$data) return false;

        $config = C('DJKCONFIG');

        $data['product_use'] = $config['class_one'][$data['product_use_classone_sn']].'; '.$config['class_two'][$data['product_use_classone_sn']][$data['product_use_classtwo_sn']];

        return $data;
    }
}