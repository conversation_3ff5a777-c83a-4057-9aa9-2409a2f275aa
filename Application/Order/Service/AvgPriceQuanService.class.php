<?php

namespace Order\Service;


/**
 * 优惠券
 * Class AvgPriceZheKouService
 * @package Order\Controller
 */
class AvgPriceQuanService extends AvgPriceService
{
    public $datas;

    protected $art = null;
    public function __construct(AvgPriceService $avgPriceService){
        $this->avgPrice = $avgPriceService;

    }


    /**
     * @return mixed
     * 计算优惠券 目前优惠券 全部商品均摊
     */
    public function action(){
        $this->datas = $this->avgPrice->action();


        if(!isset($this->datas['coupon_price']) || $this->datas['coupon_price'] <= 0){
            return $this->datas;
        }

        $amount = 0;
        $c = 0;
        foreach($this->datas['list'] as $k => $item){
            $amount += $item['goods_price'] * $item['goods_number'];
            $c ++;
        }
        unset($item);
        $rate = $this->datas['coupon_price'] / $amount;

        $pCount = 0;
        foreach($this->datas['list'] as $k => &$item){
            $c --;

            if($c <= 0){
                $p = $this->datas['coupon_price'] - $pCount;
            }else{
                $p = price_format($rate*($item['goods_price'] * $item['goods_number']),0,2);

            }

            if ($p <= 0){
                continue;
            }

            $pCount += $p;
            $item['goods_discount_amount'] += $p;
            $item['preferential_price'] += $p;
            //添加均摊明细
            $item["discountDetail"][] = [
                "goods_id"=>$item["goods_id"],
                "type"=>3,//1附加费 2运费   3优惠券  4活动
                "amount"=>$p,
                "settlement_type"=>2,
            ];
        }

        //重新计算合计
        foreach($this->datas['list'] as $k =>&$item){
            $item['goods_total_amount'] = price_format($item['goods_amount']+$item['extra_price']-$item['goods_discount_amount'],0,2);
        }

        return $this->datas;
    }
}
