<?php

namespace Order\Service;




/**
 * 附加费
 * 明细费用权重（n1） =  （物流费或者附加费）/小计之和
 * n1 = 20/(10+40+90)
 * 费用 = n1*小计
 * Class AvgPriceFeiYongService
 * @package Order\Controller
 */
class AvgPriceFJFService extends AvgPriceService
{
    public $datas;

    protected $art = null;
    public function __construct(AvgPriceService $avgPriceService){
        $this->avgPrice = $avgPriceService;

    }


    /**
     * @return mixed
     * 计算附加费明细
     */
    public function action(){
        $this->datas = $this->avgPrice->action();


        $supplier_key = [];//所有的供应商key
        $jingdu = [];//处理精度问题

        //计算出每个供应商key下的附加费比率rate
        //明细费用权重（n1） =  （物流费或者附加费）/小计之和
        $supplier_rate = [];


        foreach($this->datas['extend_items'] as $supplierkey=>$supplierInfo){
            $rate = $supplierInfo['extend_fee']/$supplierInfo['amount'];
            $supplier_rate[$supplierkey] = $rate;
            $supplier_key[] = $supplierkey;
            $jingdu[$supplierkey]+=$supplierInfo["count"];

        }
//        print_r($this->datas['extend_items']);
//        print_r($jingdu);
//        exit;
        $getSupplierExtendFee = function($key=""){
            $extend_fee = 0;
            foreach($this->datas['extend_items'] as $supplierkey=>$supplierInfo){
                if($supplierkey == $key){
                    $extend_fee = $supplierInfo['extend_fee'];
                    break;
                }
            }
            return $extend_fee;
        };
//        print_r($this->datas['extend_items']);


//        \Think\Log::write(print_r($this->datas,true));
        //开始计算费用明细
        $jingduAmountCount = [];//处理精度问题
        foreach($this->datas['list'] as $k =>&$item){
            $goods_type = isset($item["admin_goods_type"]) ? $item["admin_goods_type"] : $item['goods_type'];
            $spukey = getSuppKey($item['supplier_id'], $item['canal'], $goods_type);
//            \Think\Log::write(print_r($spukey,true));
            if (!isset($item['extend_price'])) {
                $item['extend_price'] = 0;
            }
            if (!in_array($spukey,$supplier_key)){
                continue;
            }

            foreach($this->datas['extend_items'] as $supplierkey=>$supplierInfo){

                if ($spukey != $supplierkey){
                    continue;
                }


                if(isset($jingdu[$spukey])){
                    //处理精度问题
                    $jingdu[$spukey]--;
                }


                if($jingdu[$spukey] <= 0 ){
                    $jingduAmountCount[$spukey] =  isset($jingduAmountCount[$spukey]) ? $jingduAmountCount[$spukey] :0;
                    $extend_price = $getSupplierExtendFee($spukey) - $jingduAmountCount[$spukey];
                }else{
                    $extend_price = price_format($supplier_rate[$spukey] * ($item['goods_price'] * $item['goods_number']),0,2);
                }
                if ($extend_price <= 0){
                    break;
                }

                $jingduAmountCount[$spukey] += $extend_price;//处理精度问题
                $item['extend_price'] = $extend_price;
                $item['extra_price'] += $extend_price;

//                dump(sprintf("key:%s,goods_name:%s,goods_id:%s,附加费：%s",$spukey,$item["goods_name"],$item["goods_id"],$item['extend_price']));
                //添加均摊明细
                $item["discountDetail"][]= [
                    "goods_id"=>$item["goods_id"],
                    "type"=>1,//1附加费 2运费   3优惠券  4活动
                    "amount"=>$extend_price,
                    "settlement_type"=>1,
                ];

            }
        }


        //重新计算合计
        foreach($this->datas['list'] as $k =>&$item){
            $item['goods_total_amount'] = price_format($item['goods_amount']+$item['extra_price']-$item['goods_discount_amount'],0,2);
        }



//        throw new \Exception("hehe");
        return $this->datas;
    }
}

