<?php

namespace Order\Service;


/**
 * 运费
 * Class AvgPriceZheKouService
 * @package Order\Controller
 */
class AvgPriceYunFeiService extends AvgPriceService
{
    public $datas;
    protected $art = null;
    public function __construct(AvgPriceService $avgPriceService){
        $this->avgPrice = $avgPriceService;

    }


    /**
     * @return mixed
     * 计算运费
     */
    public function action(){
        $this->datas = $this->avgPrice->action();

        if(!isset($this->datas['shipping_price']) || $this->datas['shipping_price'] <= 0){
            return $this->datas;
        }
        $shipping_price = abs($this->datas['shipping_price']);
        if(isset($this->datas['free_shipping_price'])){
            $shipping_price = $shipping_price -  abs($this->datas['free_shipping_price']);
        }

        $amount = 0;
        $count_need_yunfei = 0;//需要收运费的商品种类统计
        foreach($this->datas['list'] as $k => $item){
//            if ($item["order_goods_type"] == 1 && $item["sale_type"] == 1) {
//                //自营现卖收取运费
//                continue;
//            }
            $count_need_yunfei++;
            $amount += $item['goods_price'] * $item['goods_number'];
        }
        unset($item);
        $rate = $shipping_price / $amount;
        $pCount = 0;
        foreach($this->datas['list'] as $k => &$item){
//            if ($item["order_goods_type"] == 1 && $item["sale_type"] == 1) {
//                //自营现卖收取运费
//                continue;
//            }
            $count_need_yunfei--;
            if($count_need_yunfei <= 0){
                $tempAmount  = $shipping_price - $pCount;
            }else{
                $tempAmount = price_format($rate*($item['goods_price'] * $item['goods_number']),0,2);
            }

            $pCount += $tempAmount;

            $item['extra_price'] += $tempAmount;
            //添加均摊明细
            if ($tempAmount <= 0) {
                continue;
            }
            $item["discountDetail"][] = [
                "goods_id"=>$item["goods_id"],
                "type"=>2,//1附加费 2运费   3优惠券  4活动
                "amount"=>$tempAmount,
                "settlement_type"=>1,
            ];

        }

        //重新计算合计
        foreach($this->datas['list'] as $k =>&$item){
            $item['goods_total_amount'] = price_format($item['goods_amount']+$item['extra_price']-$item['goods_discount_amount'],0,2);
        }

        return $this->datas;
    }
}
