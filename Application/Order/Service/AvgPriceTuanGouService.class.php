<?php

namespace Order\Service;


/**
 * 团购
 * Class AvgPriceZheKouService
 * @package Order\Controller
 */
class AvgPriceTuanGouService extends AvgPriceService
{
    public $datas;
    protected $art = null;
    public function __construct(AvgPriceService $avgPriceService){
        $this->avgPrice = $avgPriceService;

    }


    /**
     * @return mixed
     *团购优惠
     */
    public function action(){
        $this->datas = $this->avgPrice->action();

        if(!isset($this->datas['activity_price']) || $this->datas['activity_price'] <= 0){
            return $this->datas;
        }

        $amount = 0;
        $c = 0;
        foreach($this->datas['list'] as $k => $item){
            if($item['ac_type'] != \Order\Model\OrderItemsModel::$AC_TYPE_TUANGOU) continue;
            $amount += $item['goods_price'] * $item['goods_number'];
            $c++;
        }
        unset($item);
        $rate = 0;
        $rate = $this->datas['activity_price'] / $amount;
        $pcount = 0;
        foreach($this->datas['list'] as $k => &$item){
            if($item['ac_type'] != \Order\Model\OrderItemsModel::$AC_TYPE_TUANGOU) continue;
            $c --;
            if ($c <= 0){
                $p = $this->datas['activity_price'] - $pcount;
            }else{
                $p = price_format($rate*($item['goods_price'] * $item['goods_number']),0,2);
            }

            if ($p <= 0) continue;
            $pcount += $p;
            $item['goods_discount_amount'] += $p;
            $item['preferential_price'] +=  $p;
            $activity_info = isset($item["activity_info"]) && !empty($item["activity_info"])  ? $item["activity_info"] : [];
            $item["discountDetail"][] = [
                "goods_id"=>$item["goods_id"],
                "activity_id"=>$activity_info && isset($activity_info["id"]) ? $activity_info["id"] : 0,
                "activity_name"=>$activity_info && isset($activity_info["activity_name"]) && $activity_info["activity_name"]  ? $activity_info["activity_name"] : "团购",
                "type"=>4,//1附加费 2运费   3优惠券  4活动
                "amount"=>$p,
                "settlement_type"=>2,
            ];
        }

        //重新计算合计
        foreach($this->datas['list'] as $k =>&$item){
            $item['goods_total_amount'] = price_format($item['goods_amount']+$item['extra_price']-$item['goods_discount_amount'],0,2);
        }

        return $this->datas;
    }
}
