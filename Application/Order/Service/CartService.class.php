<?php

namespace Order\Service;

class CartService
{
    //购物车自增id
    const CART_ID = "ichunt_cart_id";
    //购物车列表
    const CART_REDIS_KEY = "api_ichunt_cart_list";
    //用户和cartid关联的列表
    const UER_CART_REDIS_KEY = "api_ichunt_user_carts";
    //gid和cartid关联的列表
    const GID_CART_REDIS_KEY = "api_ichunt_gid_carts";
    //goods_id和cartid关联的列表
    const GOODS_CART_REDIS_KEY = "api_ichunt_goods_id_carts";

    public static function getNewRedis()
    {
        $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.user_new"));
        return $redis;
    }

    /**
     * 获取用户购物车内容 
     * Summary of getCartById
     * @param mixed $carId
     */
    public static function getCartById($carId)
    {
        $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.user_new"));
        $res = $redis->hget(self::CART_REDIS_KEY, $carId);
        return $res ? json_decode($res, true) : [];
    }


    /**
     * 批量获取用户购物车内容
     * Summary of getCartListByCarIds
     * @param mixed $carIds
     */
    public static function getCartListByCarIds($carIds)
    {
        $redis = self::getNewRedis();
        $carts = $redis->hmget(self::CART_REDIS_KEY, $carIds);
        if (empty($carts)) {
            return [];
        }
        $result = [];
        foreach ($carts as $cartId => $cart) {
            if ($cart) {
                $result[$cartId] = json_decode($cart, true,512, JSON_BIGINT_AS_STRING);
                // $result[$cartId]["goods_id"] = (string)$result[$cartId]["goods_id"];
            }
        }
        return $result;
    }


    /**
     * 获取购物车自增id
     * Summary of getCartId
     * @return mixed
     */
    public static function getCartId()
    {
        $redis = self::getNewRedis();
        //        dd($redis->set(self::CART_ID,11050));
        //        dd($redis->incr(self::CART_ID));
        return $redis->incr(self::CART_ID);

        //        dd($redis->get(self::CART_ID));
    }


    /**
     * 添加购物车
     * Summary of addCart
     * @param mixed $data
     */
    public static function addCart($data = [])
    {
        $redis = self::getNewRedis();
        $cartId = self::getCartId();
        $data["cart_id"] = $cartId;
        $data = self::formatCartData($data);
        $res = $redis->hset(CartService::CART_REDIS_KEY, $cartId, \GuzzleHttp\json_encode($data));
        self::bindUserCart($data["user_id"], $cartId);
        if ($data["user_sign"]) {
            self::bindGidCart($data["user_sign"], $cartId);
        }
        if ($data["goods_id"]) {
            self::bindGoodsIdCart($data["goods_id"], $cartId);
        }
        if ($res) {
            return $cartId;
        }
        return $cartId ?: false;
    }

    public static function formatCartData($data)
    {
        $fields = [
            'user_sign' => 'string',  // varchar(32)
            'org_id' => 'int',        // tinyint(1)
            'user_id' => 'int',       // int(10)
            'goods_id' => 'string',   // bigint(20) 转为字符串防止大整数溢出
            'brand_id' => 'int',      // mediumint(8)
            'standard_brand_id' => 'int', // mediumint(8)
            'supplier_id' => 'int',   // mediumint(8)
            'goods_sn' => 'string',   // varchar(64)
            'goods_name' => 'string',  // varchar(128)
            'sku_name' => 'string',   // varchar(255)
            'brand_name' => 'string', // varchar(128)
            'standard_brand_name' => 'string', // varchar(128)
            'supplier_name' => 'string', // varchar(128)
            'warehouse' => 'string',  // varchar(64)
            'goods_number' => 'int',  // mediumint(8)
            'goods_price' => 'float', // decimal(12,6)
            'create_goods_price' => 'float', // decimal(12,6)
            'initial_price' => 'float', // decimal(12,6)
            'order_source' => 'string', // varchar(255)
            'delivery_time' => 'string', // varchar(32)
            'goods_data' => 'string', // text
            'currency' => 'string',   // tinyint(3) 转为字符串处理枚举值
            'delivery_place' => 'int', // tinyint(3)
            'change_place' => 'int',   // tinyint(4)
            'sale_type' => 'int',     // tinyint(3)
            'goods_type' => 'int',    // tinyint(3)
            'status' => 'int',         // tinyint(4)
            'bom_id' => 'int',        // mediumint(8)
            'extend_type_id' => 'string', // varchar(50)
            'extend_type' => 'int',   // tinyint(1)
            'type' => 'int',           // tinyint(1)
            'self_supplier_type' => 'int', // tinyint(1)
            'is_remind' => 'int',      // tinyint(1)
            'buyer_id' => 'int',       // smallint(8)
            'batch' => 'string',       // varchar(255)
            'remarks' => 'string',     // varchar(255)
            'raw_goods_sn' => 'string', // varchar(128)
            'raw_goods_packing' => 'string', // varchar(128)
            'raw_brand_name' => 'string', // varchar(128)
            'is_gift' => 'int',        // tinyint(1)
            'is_purchase' => 'int',    // tinyint(1)
            'is_provide_dc' => 'int',  // tinyint(4)
            'is_provide_producer' => 'int', // tinyint(4)
            'is_vacuo' => 'int',       // tinyint(1)
            'customer_material_number' => 'string', // varchar(128)
            'contract_remark' => 'string', // varchar(128)
            'create_time' => 'int',    // int(10)
            'update_time' => 'int',    // int(10)
            'cart_id' => 'int'        // int(11)
        ];

        $dataDefault = [
            "user_sign" => '',
            "org_id" => 1,
            "user_id" => 0,
            "goods_id" => "0",
            "brand_id" => 0,
            "standard_brand_id" => 0,
            "supplier_id" => 0,
            "goods_sn" => '',
            "goods_name" => '',
            "sku_name" => '',
            "brand_name" => '',
            "standard_brand_name" => '',
            "supplier_name" => '',
            "warehouse" => '',
            "goods_number" => 0,
            "goods_price" => 0,
            "create_goods_price" => 0,
            "initial_price" => 0,
            "order_source" => '',
            "delivery_time" => '',
            "goods_data" => '',
            "currency" => 0,
            "delivery_place" => 1,
            "change_place" => 1,
            "sale_type" => 1,
            "goods_type" => 0,
            "status" => null, // 此列没有默认值
            "bom_id" => 0,
            "extend_type_id" => '',
            "extend_type" => 0,
            "type" => 1,
            "self_supplier_type" => 0,
            "is_remind" => 0,
            "buyer_id" => 0,
            "batch" => '',
            "remarks" => '',
            "raw_goods_sn" => '',
            "raw_goods_packing" => '',
            "raw_brand_name" => '',
            "is_gift" => -1,
            "is_purchase" => 1,
            "is_provide_dc" => -1,
            "is_provide_producer" => -1,
            "is_vacuo" => -1,
            "customer_material_number" => '',
            "contract_remark" => '',
            "create_time" => 0,
            "update_time" => 0
        ];

        $formatted = [];
        foreach ($fields as $field => $type) {
            if (isset($data[$field])) {
                switch ($type) {
                    case 'string':
                        $formatted[$field] = (string)$data[$field];
                        break;
                    case 'int':
                        $formatted[$field] = (int)$data[$field];
                        break;
                    case 'float':
                        $formatted[$field] = (float)$data[$field];
                        break;
                    default:
                        $formatted[$field] = (string)$data[$field];
                        break;
                }
            } else {
                $formatted[$field] = isset($dataDefault[$field]) ? $dataDefault[$field] : "";
            }
        }
        return $formatted;
    }
    /**
     *
     * 修改购物车里面的用户id
     * Summary of getUserCartList
     */
    public static function bindUserCart($userId, $cartId)
    {
        if (empty($userId) || empty($cartId)) {
            return false;
        }
        $redis = self::getNewRedis();
        $res = $redis->hget(CartService::UER_CART_REDIS_KEY, $userId);
        $cartList = [];
        if (empty($res)) {
            //不存在
            $cartList = [];
        } else {
            try {
                $cartList = \GuzzleHttp\json_decode($res);
            } catch (\Exception $e) {
                $cartList = [];
            }
        }
        // p($cartList);exit;
        // p($userId);
        // p($cartId);
        array_push($cartList, $cartId);
        $cartList = array_unique(array_filter($cartList));
        $cartList = array_values($cartList);
        // 将 $cartList 中的每个值转换为整数
        $cartList = array_map('intval', $cartList);
        $result = $redis->hset(CartService::UER_CART_REDIS_KEY, (string)$userId, \GuzzleHttp\json_encode($cartList));
        return $result === false ? false : $cartList;
    }

    /**
     * 
     * 获取gid对应的购物车id列表
     * Summary of getGidCartIds
     * @param mixed $gid
     */
    public static function getGidCartIds($gid)
    {
        if (empty($gid)) {
            return [];
        }
        $redis = self::getNewRedis();
        $res = $redis->hget(CartService::GID_CART_REDIS_KEY, $gid);
        return $res ? json_decode($res, true) : [];
    }

    /**
     * 
     * 获取uid对应的购物车id列表
     * Summary of getGidCartIds
     * @param mixed $gid
     */
    public static function getUserIdCartIds($uid)
    {
        if (empty($uid)) {
            return [];
        }
        $redis = self::getNewRedis();
        $res = $redis->hget(CartService::UER_CART_REDIS_KEY, $uid);
        return $res ? json_decode($res, true) : [];
    }

    public static function getGoodsIdIdCartIds($goodsId)
    {
        if (empty($goodsId)) {
            return [];
        }
        $goodsIdArr = [];
        if (is_array($goodsId)) {
            $goodsIdArr = $goodsId;
        } else {
            $goodsIdArr[] = $goodsId;
        }


        $redis = self::getNewRedis();
        $res = $redis->hmget(CartService::GOODS_CART_REDIS_KEY, $goodsIdArr);

        $result = [];
        foreach ($res as $cartId => $cart) {
            if ($cart) {
                $result[$cartId] = json_decode($cart, true);
            }
        }
        return $result;
    }

    public static function bindGidCart($gid, $cartId)
    {
        if (empty($gid) || empty($cartId)) {
            return false;
        }
        $redis = self::getNewRedis();
        $res = $redis->hget(CartService::GID_CART_REDIS_KEY, $gid);
        $cartList = [];
        if (!$res) {
            //不存在
            $cartList = [];
        } else {
            try {
                $cartList = \GuzzleHttp\json_decode($res);
            } catch (\Exception $e) {
                $cartList = [];
            }
        }
        // p($cartList);exit;
        // p($userId);
        // p($cartId);
        array_push($cartList, $cartId);
        $cartList = array_unique(array_filter($cartList));
        $cartList = array_values($cartList);
        $result = $redis->hset(CartService::GID_CART_REDIS_KEY, (string)$gid, \GuzzleHttp\json_encode($cartList));
        return $result === false ? false : $cartList;
    }

    public static function bindGoodsIdCart($goodsId, $cartId)
    {
        if (empty($goodsId) || empty($cartId)) {
            return false;
        }
        $redis = self::getNewRedis();
        $res = $redis->hget(CartService::GOODS_CART_REDIS_KEY, $goodsId);
        $cartList = [];
        if (!$res) {
            //不存在
            $cartList = [];
        } else {
            try {
                $cartList = \GuzzleHttp\json_decode($res);
            } catch (\Exception $e) {
                $cartList = [];
            }
        }
        // p($cartList);exit;
        // p($userId);
        // p($cartId);
        array_push($cartList, $cartId);
        $cartList = array_unique(array_filter($cartList));
        $cartList = array_values($cartList);
        $result = $redis->hset(CartService::GOODS_CART_REDIS_KEY, (string)$goodsId, \GuzzleHttp\json_encode($cartList));
        return $result === false ? false : $cartList;
    }


    public static function updateCart($data = [])
    {
        $cartId = $data["cart_id"] ?: 0;
        if (empty($cartId)) {
            return false;
        }

        $redis = self::getNewRedis();
        $res = $redis->hget(CartService::CART_REDIS_KEY, $cartId);
        if (!$res) {
            return false;
        }
        $res = \GuzzleHttp\json_decode($res, true);
        $res = array_merge($res, $data);
        // 更新Redis中的购物车数据
        $res = self::formatCartData($res);
        $bk = $redis->hset(CartService::CART_REDIS_KEY, $cartId, \GuzzleHttp\json_encode($res));

        self::bindUserCart($data["user_id"], $cartId);

        return  $bk === false ? false : $res;
    }

    public static function getUserHasById($userId)
    {
        $redis = self::getNewRedis();
        $cartIds = $redis->hget(CartService::UER_CART_REDIS_KEY, $userId);
        if (!$cartIds) {
            return [];
        }
        $cartIdArr = json_decode($cartIds, true);
        $carts = $redis->hmget(self::CART_REDIS_KEY, $cartIdArr);
        $result = [];
        foreach ($carts as $cartId => $cart) {
            if ($cart) {
                $result[$cartId] = json_decode($cart, true);
            }
        }
        return $result;
    }

    public static function getUserHasBySign($userSign)
    {
        $redis = self::getNewRedis();
        $cartIds = $redis->hget(CartService::GID_CART_REDIS_KEY, $userSign);
        if (!$cartIds) {
            return [];
        }
        $cartIdArr = json_decode($cartIds, true);
        $carts = $redis->hmget(self::CART_REDIS_KEY, $cartIdArr);
        $result = [];
        foreach ($carts as $cartId => $cart) {
            if ($cart) {
                $result[$cartId] = json_decode($cart, true);
            }
        }
        return $result;
    }
    public static function getUserGoods($where = [])
    {
        $goodsId = array_get($where, "goods_id");
        $userId = array_get($where, "user_id");
        $userSign = array_get($where, "user_sign");
        $deliveryPlace = array_get($where, "delivery_place");
        $cartIds = [];
        if ($userId) {
            $uidCcartIds = self::getUserIdCartIds($userId);
            $cartIds = array_unique(array_merge($cartIds, $uidCcartIds));
        }
        if ($userSign) {
            $gidCartIds = self::getGidCartIds($userSign);
            $cartIds = array_unique(array_merge($cartIds, $gidCartIds));
        }
        if (empty($cartIds)) {
            return [];
        }
        $cartList = self::getCartListByCarIds($cartIds);

        if (empty($cartList)) {
            return [];
        }

        foreach ($cartList as $k => $cart) {
            if (!empty($deliveryPlace) && $cart["delivery_place"] != $deliveryPlace) {
                unset($cartList[$k]);
            }
            if (!empty($goodsId) && $goodsId != $cart["goods_id"]) {
                unset($cartList[$k]);
            }
        }
        return !empty($cartList) ? reset($cartList) : [];
    }

    public static function bindUserIdSign($gid = '', $uid = '')
    {
        $res = true;
        if (empty($uid) || empty($uid)) {
            return true;
        }
        $cartIdArr = self::getGidCartIds($gid);
        if (empty($cartIdArr)) {
            return true;
        }
        foreach ($cartIdArr as $cartId) {
            $cartInfo = self::getCartById($cartId);
            // p($cartInfo);exit;
            if (!empty($cartInfo)) {
                $cartInfo["user_id"] = (int)$uid;
                self::updateCart($cartInfo);
            }
        }

        // self::delGidCart($gid);

        return true;
    }

    public static function delGidCart($gid)
    {
        $redis = self::getNewRedis();
        $res = $redis->hdel(CartService::GID_CART_REDIS_KEY, $gid);
        return $res;
    }

    public static function delCartByCartId($cartId)
    {
        $redis = self::getNewRedis();
        $cartInfo = self::getCartById($cartId);
        if (empty($cartInfo)) {
            return false;
        }

        //清除用户绑定的购物车id
        $goodsId = $cartInfo["goods_id"] ?: "";
        self::delBindJsonCartId($goodsId, $cartId, CartService::GOODS_CART_REDIS_KEY);

        //清除gid绑定的购物车id
        $gid = $cartInfo["user_sign"] ?: "";
        self::delBindJsonCartId($gid, $cartId, CartService::GID_CART_REDIS_KEY);

        //清除user_id绑定的购物车id
        $userId = $cartInfo["user_id"] ?: "";
        self::delBindJsonCartId($userId, $cartId, CartService::UER_CART_REDIS_KEY);

        $redis->hdel(CartService::CART_REDIS_KEY, $cartId);
        return true;
    }

    public static function delBindJsonCartId($hashKeyId, $cartId, $mapHashKey = CartService::GOODS_CART_REDIS_KEY)
    {
        if (empty($hashKeyId) || empty($cartId)) {
            return true;
        }
        $redis = self::getNewRedis();
        $res = $redis->hget($mapHashKey, $hashKeyId);
        if (!$res) {
            return true;
        }
        $cartIdArrList = [];
        try {
            $cartIdArrList = \GuzzleHttp\json_decode($res);
        } catch (\Exception $e) {
            $cartIdArrList = [];
        }
        if (empty($cartIdArrList)) {
            $redis->hdel($mapHashKey, $hashKeyId);
            return true;
        }
        // 从数组中移除指定的cartId
        $cartIdArrList = array_filter($cartIdArrList, function ($item) use ($cartId) {
            return $item != $cartId;
        });
        $cartIdArrList = array_values($cartIdArrList);
        if (empty($cartIdArrList)) {
            $redis->hdel($mapHashKey, $hashKeyId);
        } else {
            // 更新Redis中的购物车ID列表
            $redis->hset($mapHashKey, $hashKeyId, \GuzzleHttp\json_encode($cartIdArrList));
        }
        return true;
    }

    /**
     * 
     * 清空gid对应的购物车id列表
     * @param mixed $gid
     * @return void
     */
    public static function clearUserSign($gid)
    {
        $cartIdArr = self::getGidCartIds($gid);
        $cartList = self::getCartListByCarIds($cartIdArr);
        foreach ($cartList as $cart) {
            if (intval($cart["user_id"]) > 0) {
                $cart["user_sign"] = "";
                self::updateCart($cart);
            }
        }
        self::delGidCart($gid);
    }
    public static function getUserCartGoodsIdArr($userId)
    {
        $cartIds = self::getUserIdCartIds($userId);
        if (empty($cartIds)) {
            return [];
        }
        $cartList = self::getCartListByCarIds($cartIds);
        $goodsIdArr = [];
        foreach ($cartList as $cart) {
            if (intval($cart["user_id"]) == intval($userId) && !empty($cart["goods_id"])) {
                $goodsIdArr[] = $cart["goods_id"];
            }
        }
        return array_filter(array_unique($goodsIdArr));
    }

    public static function getCartCount($where = [])
    {
        $userId = array_get($where, "user_id");
        $userSign = array_get($where, "user_sign");
        $goodsType = array_get($where, "goods_type");
        $type = array_get($where, "type");
        $deliveryPlace = array_get($where, "delivery_place");
        $status = array_get($where, "status");
        $cartIdArr = [];
        if ($userId) {
            $cartIds = self::getUserIdCartIds($userId);
            $cartIdArr = array_merge($cartIdArr, $cartIds);
        }
        if ($userSign) {
            $cartIds = self::getGidCartIds($userSign);
            $cartIdArr = array_merge($cartIdArr, $cartIds);
        }
        $cartIdArr = array_filter(array_unique($cartIdArr));
        $cartList = self::getCartListByCarIds($cartIdArr);
        $cartCount = 0;
        foreach ($cartList as $cart) {
            if ($status && intval($cart["status"]) != intval($status)) {
                continue;
            }
            if ($goodsType && !in_array($cart["goods_type"], $goodsType)) {
                continue;
            }
            if ($type && $cart["type"] != $type) {
                continue;
            }
            if ($deliveryPlace && $cart["delivery_place"] != $deliveryPlace) {
                continue;
            }
            $cartCount++;
        }


        return $cartCount;
    }

    public static function getCartIdsByWheres($map = [])
    {
        $cart_ids = array_get($map, "cart_id");
        $user_id = array_get($map, "user_id", null);
        $user_sign = array_get($map, "user_sign", null);
        $status = array_get($map, "status", null);
        if (empty($cart_ids)) {
            return [];
        }
        if (!is_array($cart_ids)) {
            $cart_ids = [$cart_ids];
        }
        $cartList = self::getCartListByCarIds($cart_ids);
        foreach ($cartList as $k => $cart) {
            if (!empty($user_id) && !empty($user_sign) && ($cart["user_id"] != $user_id || $cart["user_sign"] != $user_sign)) {
                unset($cartList[$k]);
            }
            if ($user_id !== null  && $cart["user_id"] != $user_id) {
                unset($cartList[$k]);
            }
            if ($user_sign !== null && $cart["user_sign"] != $user_sign) {
                unset($cartList[$k]);
            }
            if ($status !== null && $cart["status"] != $status) {
                unset($cartList[$k]);
            }
        }
        $cartIdArray = [];
        foreach ($cartList as $cart) {
            if (isset($cart['cart_id'])) {
                $cartIdArray[] = $cart['cart_id'];
            }
        }
        return $cartIdArray;
    }

    public static function getCartList($map = '', $order = 'create_time DESC', $field = '*')
    {
        $cartIds = [];
        if (!empty($map['cart_id'])) {
            array_push($cartIds, $map['cart_id']);
        }
        if (!empty($map['cart_id_arr'])) {
            $cartIds = array_unique(array_merge($cartIds, $map['cart_id_arr']));
        }
        if (!empty($map['user_id'])) {
            $uidCcartIds = self::getUserIdCartIds($map['user_id']);
            if (!empty($cartIds)) {
                $cartIds = array_intersect($cartIds, $uidCcartIds);
            } else {
                $cartIds = array_unique(array_merge($cartIds, $uidCcartIds));
            }
        }
        if (!empty($map['user_sign'])) {
            $gidCartIds = self::getGidCartIds($map['user_sign']);
            if (!empty($cartIds)) {
                $cartIds = array_intersect($cartIds, $gidCartIds);
            } else {
                $cartIds = array_unique(array_merge($cartIds, $gidCartIds));
            }
        }

        if (!empty($map['goods_id_arr'])) {
            $goodsCartIdArr = CartService::getGoodsIdIdCartIds($map['goods_id_arr']);
            $goodsCartIdArr  = call_user_func_array('array_merge', array_values($goodsCartIdArr));
            $goodsCartIdArr = !empty($goodsCartIdArr) ? array_filter(array_unique($goodsCartIdArr)) : [];
            if (!empty($cartIds)) {
                $cartIds = array_intersect($cartIds, $goodsCartIdArr);
            } else {
                $cartIds = array_unique(array_merge($cartIds, $goodsCartIdArr));
            }
        }

        if (empty($cartIds)) {
            return [];
        }

        $cartList = self::getCartListByCarIds($cartIds);

        if (empty($cartList)) {
            return [];
        }
        // p($cartList,$map);exit;
        $status = array_get($map, "status", null);
        $goodsNumberGt0 = array_get($map, "goods_number_gt_0", null);
        foreach ($cartList as $k => $cart) {
            if (!empty($map["delivery_place"]) && $cart["delivery_place"] != $map["delivery_place"]) {
                unset($cartList[$k]);
            }
            if (!empty($map["type"]) && is_array($map["type"]) && !in_array($cart["type"], $map["type"])) {
                unset($cartList[$k]);
            }
            if (!empty($map["type"]) && !is_array($map["type"]) &&  $cart["type"] != $map["type"]) {
                unset($cartList[$k]);
            }
            if (!empty($map["update_time_lt"]) && $cart["update_time"] && $cart["update_time"] >= $map["update_time_lt"]) {
                unset($cartList[$k]);
            }
            if (!empty($map["currency"]) && $cart["currency"] != $map["currency"]) {
                unset($cartList[$k]);
            }
            if ($status !== null && $cart["status"] != $status) {
                unset($cartList[$k]);
            }
            if ($cart["goods_number"] && $cart["goods_number"] <= 0) {
                unset($cartList[$k]);
            }
            if ($goodsNumberGt0 && $cart["goods_number"] <= 0) {
                unset($cartList[$k]);
            }
        }

        if (trim($order) == "create_time DESC") {
            // 按照create_time倒序排序
            usort($cartList, function ($a, $b) {
                return $b['create_time'] - $a['create_time'];
            });
        } elseif (trim($order) == "update_time") {
            // 按照update_time正序排序
            usort($cartList, function ($a, $b) {
                return $a['update_time'] - $b['update_time'];
            });
        } elseif (trim($order) == "create_time DESC, cart_id DESC") {
            // 按照create_time倒序，然后按照cart_id倒序排序
            usort($cartList, function ($a, $b) {
                if ($b['create_time'] == $a['create_time']) {
                    return $b['cart_id'] - $a['cart_id'];
                }
                return $b['create_time'] - $a['create_time'];
            });
        } elseif (trim($order) == 'status DESC,create_time DESC') {
            // 按照status倒序，然后按照create_time倒序排序
            usort($cartList, function ($a, $b) {
                if ($b['status'] == $a['status']) {
                    return $b['create_time'] - $a['create_time'];
                }
                return $b['status'] - $a['status'];
            });
        } elseif (trim($order) == 'cart_id ASC') {
            // 按照cart_id正序排序
            usort($cartList, function ($a, $b) {
                return $a['cart_id'] - $b['cart_id'];
            });
        }

        return $cartList;
    }


}
