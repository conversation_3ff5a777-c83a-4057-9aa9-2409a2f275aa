<?php

namespace Order\Service;


/**
 * Created by PhpStorm.
 * User: sunlolng
 * Date: 2019/12/16
 * Time: 10:18
 */
class AvgPriceService
{
    public $datas;
    protected $art = null;
    public function __construct($datas){
        $this->datas = $datas;
    }

    public function action(){
        foreach($this->datas['list'] as $k =>&$item){
            $item['preferential_price'] = 0;//优惠金额小计（金额比例平摊优惠券）
            $item['extra_price'] = 0;//费用
            $item['goods_total_amount'] = 0;//合计
            $item['goods_discount_amount'] = 0;//折扣价
        }
        return $this->datas;
    }

    public function getPrice(){
        /*
         * 1.增加费用折扣单价字列
                  费用折扣单价=（费用+折扣）/ 数量

            2.均摊单价=采购单价+费用折扣单价
         */
        foreach($this->datas['list'] as $k =>&$item){
//            $item['single_pre_price'] = price_format($item['goods_total_amount'] / $item['goods_number'],0,6);
            //费用折扣单价
            $feiyongZhekouUnitPrice = ($item['extra_price'] - $item['goods_discount_amount']) / $item['goods_number'];
            $item['single_pre_price'] = price_format($feiyongZhekouUnitPrice+$item["goods_price"],0,6);
        }
        return $this->datas;
    }




}

