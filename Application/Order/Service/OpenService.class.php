<?php

namespace Order\Service;

use \Think\Log;

class OpenService
{

    

    // 获取开放平台token
    public static function getAccessToken()
    {
        $redis = redis_init();
        $access_token = $redis->get('open_ichunt_access_token');
        if ($access_token) {
            return $access_token;
        }

        $params = [
            "timestamp" => time(),
            'user_name' => 'ichunt',
            'apikey' => 'RMXCUDOSLFIGQCELLVYLXSXZGQVEESFS',
        ];

        $url = OPENPLATFORM_DOMAIN . '/token/get';
        $header = [
            'Content-Type: application/json',
        ];

        $res = post_curl($url, $params, $header);
        $res = json_decode($res, true);

        if (!$res) {
            apiReturn(1, '请求开放平台token接口失败');
        }

        if ($res['code'] != 0) {
            apiReturn('获取token失败，失败原因：' . $res['msg']);
        }

        $access_token = $res['data']['access_token'];

        $expire_time = strtotime($res['data']['access_expires_in']) - time();
        $redis->set('open_ichunt_access_token', $access_token, $expire_time);

        return $access_token; 
    }

    // 请求开放平台接口
    public static function requestOpenApi($url, $params, $method = 'GET')
    {
        $access_token = self::getAccessToken();

        $header = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $access_token,
        ]; 
        // echo json_encode($params);exit;
        if ($method == 'GET') {
            $res = get_curl_new($url, $params, $header);
        } else {
            $res = post_curl($url, $params, $header);
        }
        // p($res);exit;
     
        $res = json_decode($res, true);
        if (!$res) {
            apiReturn(1, '请求开放平台接口失败'); 
        }

        if ($res['code'] != 0) {
            Log::record('请求开放平台接口失败，失败原因：' . $res['msg']);
            apiReturn($res['code'], $res['msg']);
        }

        return $res['data'];
    }


}