<?php
namespace Order\Controller;

use Order\Controller\BaseController;

class ReturnController extends BaseController
{
    // 退货申请
    public function returnGoods()
    {
        $order_id          = I('order_id');
        $return_info       = I('return_info'); // 退货信息
        $return_items_info = I('return_items_info'); // 退货明细
        $operator_id       = I('operator_id'); // 操作人ID

        if (!$order_id || !$return_info || !$return_items_info || !$operator_id) return $this->apiReturn(45001, '参数缺失');

        $OrderReturnModel      = D('OrderReturn');
        $RemovalModel          = D('Removal');
        $OrderReturnItemsModel = D('OrderReturnItems');
        $OrderReturnLogModel   = D('OrderReturnLog');
        $OrderActionLogModel   = D('OrderActionLog');
        $OrderReturnModel->startTrans();

        try {
            $count = $OrderReturnModel->countReturn($order_id);
            $return_sn = 'K-'.$return_info['order_sn'].'-'.$count; // 生成退货单号
            $removal_sn = $RemovalModel->where(['order_id' => $order_id])->getField('removal_sn'); // 获取出库单号
            
            // 退货单
            $return_info['return_sn']   = $return_sn;
            $return_info['removal_sn']  = $removal_sn;
            $return_info['create_uid']  = $operator_id;
            $return_info['create_time'] = time();  
            $return_id = $OrderReturnModel->add($return_info);

            // 退货单明细
            foreach ($return_items_info as $k=>$v) {
                if (!$v['return_num']) continue; // 过滤退货数量为空

                $v['order_id']  = $order_id;
                $v['rec_id']    = $k;
                $v['return_id'] = $return_id;
                $v['return_sn'] = $return_sn;

                // $cost = array();
                // $cost[$removal_sn] = $v['goods_id'];
                // $cost_price = $this->getCostPrice($cost); // 获取采购成本

                $v['cost_price'] = 1000; // 默认采购成本1000 --- 桥均定

                $OrderReturnItemsModel->add($v);
            }

            // 退货记录
            $OrderReturnLogModel->addLog($return_id, $operator_id, '申请退货');
            $OrderActionLogModel->addLog($order_id, $operator_id, 2, '申请退货');

            $OrderReturnModel->commit();

            return $this->apiReturn(0, '退货申请成功');
        } catch (Exception $e) {
            $OrderReturnModel->rollback();
            return $this->apiReturn(45002, '退货申请失败');
        }
    }

    // 获取采购成本
    public function getCostPrice($data)
    {
        $res = post_curl(FINANCE_DOMAIN.'/webapi/getSkuPrice', $data);

        if (!empty($res)) {
            $res = json_decode($res, true);

            if ($res['err_code'] == 0) return $res['data'];
        }

        return false;
    }

    // 退货申请列表
    public function getReturnList()
    {
        $p    = I('p', '');
        $size = I('size', 10);
        $map  = I('map', '');
        $OrderReturnModel = D('OrderReturn');

        //  获取订单
        $data = $OrderReturnModel->getReturnList($map, $p, $size);

        return $this->apiReturn(0, '', $data);
    }

    // 编辑退货单
    public function editReturnGoods()
    {
        $return_id         = I('return_id');
        $return_info       = I('return_info'); // 退货信息
        $return_items_info = I('return_items_info'); // 退货明细
        $operator_id       = I('operator_id'); // 操作人ID

        if (!$return_id || !$return_info || !$return_items_info || !$operator_id) return $this->apiReturn(45001, '参数缺失');

        $OrderReturnModel      = D('OrderReturn');
        $OrderReturnItemsModel = D('OrderReturnItems');
        $OrderReturnLogModel   = D('OrderReturnLog');
        $OrderReturnModel->startTrans();

        try {
            // 退货单
            $return_info['status'] = 1; // 待审核状态
            $OrderReturnModel->where(['return_id'=>$return_id])->save($return_info);

            // 退货明细
            foreach ($return_items_info as $k=>$v) {
                $OrderReturnItemsModel->where(['return_items_id'=>$k])->save($v);
            }

            $OrderReturnLogModel->addLog($return_id, $operator_id, '编辑退货单'); // 记录

            $OrderReturnModel->commit();

            return $this->apiReturn(0, '编辑退货申请成功');
        } catch(Exception $e) {
            $OrderReturnModel->rollback();
            return $this->apiReturn(45003, '编辑退货申请失败');
        }
    }
    
}