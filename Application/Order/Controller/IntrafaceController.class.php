<?php
namespace Order\Controller;

use Order\Controller\BaseController;
use Payment\Cpcn;

class IntrafaceController extends BaseController
{
    use \Order\Traits\PayTrait;
    /**
     * 重写初始化，使用接口服务加密方式非api内部加密方式
     * @return [type] [description]
     */
    public function _initialize()
    {
        $token = I('get.token', '', 'trim');
        if (CONTROLLER_NAME == 'Intraface') {
            $res = service_auth(I('request.'), $token);
            if ($res !== true) {
                return $this->apiReturn(-1, $res);
            }
        }

        $data = I('post.data', '', 'trim');
        $this->data = json_decode(urldecode($data), true);
    }

    /*
  * hcy 2022.12.22
  * 添加抽奖资格
  * @param order_id int 订单id
  * @param user_id int 用户id
  */
    public function addUserReward(){
        $order_id = I('order_id', 0);
        $user_id     = I('user_id', 1);

        $data =  $this->increase_draw_qualify_by_order($user_id,$order_id);
        return $this->apiReturn(0,"请求成功",$data);
    }


    /**
     * 获取预售至采购
     * @return [type] [description]
     */
    public function orderPurMark()
    {
        $orders = array();
        $res = array();
        $map = array(
            'E.pur_syn' => 1,
        );
        $OrderModel = D('Order');
        $OrderItemsModel = D('OrderItems');
        $datas = $OrderModel->alias('O')->join(C('DB_PREFIX').'order_extend E ON E.order_id = O.order_id')->where($map)->field('O.order_id,O.order_sn,O.currency,O.sale_id')->select();
        foreach ($datas as $data) {
            $items = array();
            $lists = $OrderItemsModel->getOrderList($data['order_id']);
            foreach ($lists as $v) {
                // if (!empty($v['xxxx'])) {
                // 查询锁定库存
                $goods = $this->getLockSku($data['order_id'], array($v['goods_id']));
                if ($goods['err_code'] != 0) {
                    break;
                }
                $stock = $v['goods_number'] - intval($goods['data'][$v['goods_id']]);
                // } else {
                //     $stock = $v['goods_number'];
                // }
                $items[] = array(
                    'order_id' => $data['order_id'],
                    'order_sn' => $data['order_sn'],
                    'currency' => $data['currency'],
                    'sale_id' => $data['sale_id'],
                    'rec_id' => $v['rec_id'],
                    'sku_id' => $v['goods_id'],
                    'goods_name' => $v['goods_name'],
                    'supplier_id' => $v['supplier_id'],
                    'encap' => $v['goods_encap'],
                    'packing_name' => !empty($v['goods_packing']) ? $v['goods_packing'] : '',
                    'mpq' => !empty($v['goods_spq']) ? $v['goods_spq'] : 1,
                    'need_number' => $stock,
                    'buy_price' => $v['goods_price'],
                    'brand_id' => $v['brand_id'],
                    'brand_name' => $v['brand_name'],
                );
            }
            $res[] = $items;
        }
        return $this->apiReturn(0, '', $res);
    }

    /**
     * 修改采购标记
     */
    public function setMarkOrderPur()
    {
        $res = isset_field($this->data, array('order_id'));
        if (0 !== $res) {
            return $this->apiReturn(10001, '数据缺失字段' . $res);
        }
        $OrderExtendModel = D('OrderExtend');
        $map = array(
            'order_id' => $this->data['order_id'],
        );
        $save = array(
            'pur_syn' => -1,
            'pur_syn_last_time' => time(),
        );
        $res = $OrderExtendModel->where($map)->save($save);
        if ($res === false) {
            $this->apiReturn(10010, '标记预售已同步推送失败');
        }
        return $this->apiReturn(0, '');
    }

    /**
     * 获取预售订单的商品
     * @return [type] [description]
     */
    public function getPreSell()
    {
        $goods_id = $this->data['goods_id'];
        $wms_order = $this->data['wms_order'];
        $OrderModel = D('Order');
        $OrderItemsModel = D('OrderItems');
        $list = $OrderItemsModel->getPreSell($goods_id, '4,7', $wms_order);
        foreach ($list as &$v) {
            $order_id = $v['order_id'];
            unset($v['order_id']);
            $data[$order_id]['order_id'] = $order_id;
            $data[$order_id]['items'][] = $v;
        }
        if (!empty($data)) {
            sort($data);
        }
        return $this->apiReturn(0, '成功', $data);
    }

    /**
     * 设置订单明细已基石锁定标记
     */
    public function setLockItems()
    {
        $order_id = $this->data['order_id'];
        $sku_ids = $this->data['sku_ids'];
        $OrderItemsModel = D('OrderItems');
        $map = array(
            'order_id' => $order_id,
            'goods_id' => array('in', $sku_ids),
        );
        $save = array(
            'is_lock' => 1,
        );
        $res = $OrderItemsModel->where($map)->save($save);
        if ($res === false) {
            return $this->apiReturn(10014, '标记锁定失败');
        }
        return $this->apiReturn(0, '标记成功');
    }

    /**
     * 预售使用wms预分配
     * @return [type] [description]
     */
    public function wmsOrder()
    {
        $order_id = $this->data['order_id'];
        $res = $this->makeOrder($order_id);
        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
        }
        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }

    /**
     * 通知wms订单作业
     * @return [type] [description]
     */
    public function wmsWork()
    {
        $order_id = $this->data['order_id'];

        $res = $this->makeOrder($order_id); // 推送wms队列
        if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

        // $OrderModel = D('Order');
        // $save['wms_syn'] = 1;
        // $res = $OrderModel->updateOrder($order_id, $save);
        // if ($res === false) {
        //     return $this->apiReturn(10013, '标记作业失败');
        // }
        
        return $this->apiReturn(0, '成功');
    }

    /**
     * 提现结算
     * @return [type] [description]
     */
    public function doWithdraw()
    {
        Vendor('payment.cpcn.cpcn');
        $wallet_id = $this->data['wallet_id'];
        $log_type = $this->data['log_type'];
        $wallet = $this->getWallet($wallet_id);
        $info = $wallet['data'];
        if (empty($info)) {
            return $this->apiReturn(41051, '找不到相关单据');
        } elseif ($info['wallet_type'] != 2) {
            return $this->apiReturn(41052, '提现单才能提现');
        } elseif ($info['status'] != 1) {
            return $this->apiReturn(41053, '当前状态无法提现');
        }
        try{
            $this->apiRecord(sprintf("python异步任务请求api doWithdraw方法：python传递的用户提现结算参数 %s",json_encode($info)));
        }catch(\Exception $e){

        }

        $Pay = new Cpcn(Cpcn::MARKET_MODE);
        foreach ($info['freeze_list'] as $v) {
            if (!empty($log_type) && $v['log_type'] != $log_type) {
                continue;
            }
            $data = array(
                'serial_number' => $info['wallet_sn'].'_'.$v['log_type'],
                'order_sn' => get_wallet($info['user_id']),
                'refund_amount' => $v['freeze_amount'],
                'remark' => $info['wallet_sn'],
                'account_type' => $info['account_type'] ? '11' : '12',
                'bank_id' => array_search($info['bank_name'], C('CPCN_BANK_B2C')),
            );
            $info = array_merge($info, $data);
            if ($v['log_type'] == 322) {
                $data = array(
                    'order_sn' => C('LIEXIN_WALLET_ACCOUNT'),
                );
                $info = array_merge($info, $data);
            }
            try{
                $this->apiRecord(sprintf("python异步任务请求api doWithdraw方法,api准备好结算数据后请求中金结算，请求中金结算参数 %s",json_encode($info)));
            }catch(\Exception $e){

            }

            $res = $Pay->sellet($info);
            if ($res === false) {
                try{
                    $this->apiRecord(sprintf("api中doWithdraw方法准备好结算参数 %s，请求中金结算，返回结果异常 error : %s",json_encode($info),json_encode($Pay->error)));
                    $this->pushReportMonitorLog([
                        "interface_type"=>"8",
                        "err_msg"=>"用户提现结算异常",
                        "err_code"=>41054,
                        "remark"=>sprintf("api中doWithdraw方法准备好结算参数 %s，请求中金结算，返回结果异常 error : %s",json_encode($info),json_encode($Pay->error)),

                    ]);
                }catch(\Exception $e){
                }

                $this->setWalletSyn($wallet_id, $v['log_type'].':'.$Pay->error);
                return $this->apiReturn(41054, '提现失败');
            }
        }
        $this->setWalletSyn($wallet_id);        
        return $this->apiReturn(0, '提现通知成功');
    }

    /**
     * 支付结算
     * @return [type] [description]
     */
    public function doPaySellet()
    {
        Vendor('payment.cpcn.cpcn');
        $Pay = new Cpcn(Cpcn::MARKET_MODE);
        //固定结算到猎芯账户
        $data = array(
            'account_type' => '12',
            'bank_id' => array_search(C('ICHUNT_ACCOUNT.bank_name'), C('CPCN_BANK_B2C')),
            'bank_user' => C('ICHUNT_ACCOUNT.company_name'),
            'bank_account' => C('ICHUNT_ACCOUNT.bank_number'),
            'bank_address' => C('ICHUNT_ACCOUNT.bank_address'),
            'bank_province' => C('ICHUNT_ACCOUNT.bank_province'),
            'bank_city' => C('ICHUNT_ACCOUNT.bank_city'),
        );
        $data = array_merge($this->data, $data);
        $res = $Pay->sellet($data);
        try{
            $this->apiRecord(sprintf("python服务异步任务请求doPaySellet方法：支付结算请求参数 %s,中金结算返回结果 %s",json_encode($data),json_encode($res)));
        }catch(\Exception $e){}

        if ($res === false) {
            try{
                $this->pushReportMonitorLog([
                    "interface_type"=>"7",
                    "err_msg"=>"钱包支付结算失败",
                    "err_code"=>41057,
                    "remark"=>sprintf("钱包支付后异步任务结算失败:请求参数%s,中金计算返回结果：%s",json_encode($data),json_encode($res)),

                ]);
            }catch(\Exception $e){}

            return $this->apiReturn(41057, '支付结算通知失败');
        }
        return $this->apiReturn(0, '支付结算通知成功');
    }

    /**
     * 获取退款中的记录
     * @return [type] [description]
     */
    public function getRefunding()
    {
        //只获取昨天之前的
        $OrderRefundLogModel = D('OrderRefundLog');
        $map = array(
            'RL.refund_status' => 1,
            'RL.serial_number' => array('neq', ''),
            'R.create_time' => array('lt', mktime(0, 0, 0, date('m'), date('d'), date('Y'))),
        );
        $list = $OrderRefundLogModel->getList(0, $map, null, '', 'R.refund_sn,RL.serial_number,P.pay_id');
        return $this->apiReturn(0, '获取成功', $list);
    }



    /**
     * 退款钱包充值完成回调
     * @return function [description]
     */
    public function refundDone()
    {
        $this->apiRecord(sprintf("python脚本中金退款检测 请求参数 %s ",json_encode($this->data)));
        $refund_sn = $this->data['refund_sn'];
        $serial_number = $this->data['serial_number'];
        $pay_id = $this->data['pay_id'];

        //主动查询状态
        Vendor('payment.cpcn.cpcn');
        $is_aggregate_pay = false;
        if (in_array($pay_id, array(7, 8))) {//支付宝，微信-》聚合支付
            $is_aggregate_pay = true;
        }
        $this->Pay = new Cpcn(Cpcn::BUSINESS_MODE);
        $data = $this->Pay->queryRefund($serial_number, $is_aggregate_pay);
        try{
            $this->apiRecord(sprintf("查询退款情况：退款流水号refund_sn %s 返回结果 ：%s",$refund_sn,json_encode($data)));
        }catch(\Exception $e){}

        // 本地、测试环境中金退款状态标记为1
        if (API_DOMAIN != 'http://api.ichunt.com') {
            $data['status'] = 1;
        }

        $time = $_SERVER['REQUEST_TIME'];
        if ($data['status'] == 1) {//已确认退款
            $status = 10;
            $reason = '';
        } elseif ($data['status'] == -1) {//已确认失败
            $status = -1;
            $reason = $data['message'];
        } else {
            $this->apiRecord(sprintf("退款流水号refund_sn %s 状态0 等待处理退款",$refund_sn));
            return $this->apiReturn(0, '等待处理退款');
        }
        $OrderRefundModel = D('OrderRefund');
        $OrderRefundLogModel = D('OrderRefundLog');
        //获取退款单
        $info = $OrderRefundModel->getOrderInfo(0, $refund_sn);
        if (empty($info)) {
            $this->apiRecord(sprintf("错误41071， 未找到相关退款单据  %s",$refund_sn));
            return $this->apiReturn(41071, '未找到相关退款单据：'.$refund_sn);
        }
        if ($info['status'] >= 5) {
            $this->apiRecord(sprintf("状态0，未找到相关退款单据  %s",$refund_sn));
            return $this->apiReturn(0, '已修改，无需重复请求');
        }
        $OrderRefundModel->startTrans();
        //修改流水数据状态
        $res = $OrderRefundLogModel->setSerialStatus($serial_number, $status, $time, $reason);
        if ($res === false) {
            $OrderRefundModel->rollback();
            $this->apiRecord(sprintf("错误：41069，修改失败refund_sn  %s",$refund_sn));
            return $this->apiReturn(41069, '修改失败');
        }
        $list = $OrderRefundLogModel->getListBySn($refund_sn, 'RL.refund_status');
        if (!empty($list)) {
            $success = [];
            $fail = [];
            $wait = [];
            foreach ($list as $v) {
                if ($v['refund_status'] == 10) {
                    $success[] = $v;
                } elseif ($v['refund_status'] == -1) {
                    $fail[] = $v;
                } else {
                    $wait[] = $v;
                }
            }

            if (count($success) == count($list)) {//退款单全部流水已退
                $save['status'] = 10;//联营由ERP更新至10
                $save['is_sys'] = 2;  //同步财务
                $save['is_refund'] = 1;
                $save['refund_time'] = $time;
                $save['update_time'] = $time;
            } elseif (empty($wait)) {//全部流水处理完
                $sum_amount = 0;
                foreach ($fail as $v) {//失败的通知
                    $sum_amount += $v['pay_amount'];
                }
                if ($sum_amount > 0) {
                    //通知
                    $this->apiRecord(sprintf("错误：41073，%s 退款失败金额  %s",$refund_sn,$sum_amount));
                    return $this->apiReturn(41073, $refund_sn.'，退款失败金额：'.$sum_amount);
                }
            }
            if (!empty($save)) {
                //判断是否修改退款主单
                $map = array(
                    'refund_sn' => $refund_sn,
                );
                $res = $OrderRefundModel->where($map)->save($save);
                if ($res === false) {
                    $OrderRefundModel->rollback();
                    $this->apiRecord(sprintf("错误：41070，退款状态修改失败  %s",$refund_sn));
                    return $this->apiReturn(41070, '修改失败');
                }
            }
        }
        D("OrderService")->where(["refund_sn"=>$refund_sn])->save(["refund_status"=>3,"update_time"=>time()]);
        $OrderRefundModel->commit();
        $this->pushOrderRefundDoneToErp(["order_id"=>intval($info["order_id"])]);
        $this->apiRecord(sprintf("检测退款 状态0，退款状态修改成功  退款单号： %s",$refund_sn));
        return $this->apiReturn(0, '修改成功');
    }

    /**
     * 检查中金退款进度
     * @return [type] [description]
     */
    public function checkRefund()
    {
        $out_refund_no = $this->data['out_refund_no'];
        $is_aggregate_pay = boolval($this->data['is_aggregate_pay']);
        $res = A('Order/Refund')->query($out_refund_no, $is_aggregate_pay);
        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }

    /**
     * 记录支付流水的退款金额
     */
    public function setPayLogRefundAmount()
    {
        $payment_no = $this->data['payment_no'];
        $refund_amount = $this->data['refund_amount'];
        $res = A('Order/Refund')->setPayLogRefundAmount($refund_amount, null, $payment_no);
        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }

    /**
     * 提供财务系统退款调用
     *  order_sn        订单号
     *  refund_sn       退款单号
     *  refund_amount   退款金额
     *  force_offline   强制线下转账
     * @return [type] [description]
     */
    public function refund()
    {
        $order_sn = $this->data['order_sn'];
        $refund_sn = $this->data['refund_sn'];
        $refund_amount = $this->data['refund_amount'];
        $force_offline = $this->data['force_offline'];//是否强制线下转账
        $refund_surplus = $refund_amount;

        $PayLogModel = D('PayLog');
        $datas = $PayLogModel->getInfo(array('order_sn' => $order_sn), 'pay_log_id, pay_id, pay_type, pay_amount, refund_amount');
        $count = count($datas);
        //汇总可退金额
        foreach ($datas as $data) {
            $total_usable_refund_amount += ($data['pay_amount'] - $data['refund_amount']);
        }
        if ($total_usable_refund_amount <= 0) {
            return $this->apiReturn(-1, '订单仅剩余0元可退金额');
        }
        if ($refund_amount > $total_usable_refund_amount) {
            return $this->apiReturn(-1, '订单仅剩余'.$total_usable_refund_amount.'元可退金额');
        }

        foreach ($datas as $data) {
            //剩余可退金额
            $usable_refund_amount = bcsub($data['pay_amount'], $data['refund_amount'], 2);
            if ($data['pay_type'] == 2 && $count > 1) { //预付款 多次支付
                return $this->apiReturn(41068, '不支持多笔付款订单自动退款操作');
            } elseif ($data['pay_type'] == 4) {
                return $this->apiReturn(41046, '账期订单不支持在线退款，请联系财务处理');
            } elseif ($data['pay_id'] == -1) {//0元付款，无需退款
                return $this->apiReturn(0, '0元无需退款');
            } elseif ($usable_refund_amount <= 0) {
                continue;
            }
            //需要注意多次付款时，不能混合钱包支付
            //兼容多次支付各自退款 但暂时不用 上面已经拦截多次付款订单
            if ($refund_surplus > $usable_refund_amount) {
                $refund_amount = $usable_refund_amount;
            } elseif ($refund_surplus <= $usable_refund_amount) {
                $refund_amount = $refund_surplus;
            }
            $act[] = array(
                'pay_log_id' => $data['pay_log_id'],
                'refund_amount' => $refund_amount,
                'refund_sn' => $refund_sn,
            );
            $refund_surplus = bcsub($refund_surplus, $usable_refund_amount, 2);//计算下笔需退金额
        }
        // dump($act);
        foreach ($act as $v) {
            $res = A('Order/Refund')->payLogId($v['pay_log_id'], $v['refund_amount'], $v['refund_sn'], $force_offline);
            if ($res['err_code'] !== 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
            }
        }
        //返回最后一条申请退款情况
        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }

    /**
     * 获取需同步至wms的退货来料通知
     * @return [type] [description]
     */
    public function pushReturnSyn()
    {
        $OrderReturnModel = D('OrderReturn');
        $OrderReturnItemsModel = D('OrderReturnItems');
        $data = $OrderReturnModel->getSynList(1, null, 'return_id, return_sn, order_sn, shipping_sn, mobile, email, currency, create_time, create_uid');
        if (empty($data)) {
            return $this->apiReturn(0, '未查询到需推送单据');
        }
        foreach ($data as $v) {
            $ids[] = $v['return_id'];
            $sn[$v['return_id']] = array(
                'return_sn' => $v['return_sn'],
                'order_sn' => $v['order_sn'],
            );
        }
        $list = $OrderReturnItemsModel->getList(array('return_id' => array('in', $ids)), 'return_items_id, return_id, goods_id, cost_price, return_num');

        $parseUrl = parse_url(API_DOMAIN);
        list($protocl) = explode('.', $parseUrl['host'], 2);
        foreach ($list as $v) {
            $return_id = $v['return_id'];
            unset($v['return_id']);
            $detail[$return_id][] = array(
                'delivery_sn' => $sn[$return_id]['return_sn'],
                'purchase_sn' => $sn[$return_id]['order_sn'],
                'delivery_items_id' => $v['return_items_id'],
                'goods_id' => $v['goods_id'],
                'picking_price' => $v['cost_price'],//$v['cost_price'] == 0 && $protocl == 'szapi' ? 1.16 : $v['cost_price'],//sz调试用
                'shipping_number' => $v['return_num'],
            );
        }

        $RbmqModel = D('Common/Rbmq');
        foreach ($data as $v) {
            $datas = array(
                'type' => 'order.customerreturn',
                'data' => array(
                    'delivery_sn' => $v['return_sn'],
                    'consignee' => !empty($v['mobile']) ? $v['mobile'] : $v['email'],
                    'supplier_sn' => $v['shipping_sn'],
                    'currency' => C('CURRENCY_CODE.'.$v['currency']),
                    'order_time' => $v['create_time'],
                    'order_userid' => $v['create_uid'],
                    'total_rows' => count($detail[$v['return_id']]),
                    'warehouse_code' => '01',
                    'detail' => $detail[$v['return_id']],
                )
            );
            try {
                $res = $RbmqModel->queue(C('SERVICE_QUEUE_NAME'))->push($datas, C('SERVICE_QUEUE_NAME'));
            } catch (\Exception $e) {
                continue;
            }
            //取消推送标记
            $res = $OrderReturnModel->setSyn($v['return_id'], -1);
            if ($res === false) {
                return $this->apiReturn(-1, $v['return_sn'].'推送成功，标记已推送失败');
            }
        }
        return $this->apiReturn(0, '推送成功');
    }

    /**
     * 获取需同步至财务退货单、退款单
     * @return [type] [description]
     */
    public function pushReturnInstock()
    {
        $OrderModel = D('Order');
        $CmsModel = D('Common/Cms');
        $PayLogModel = D('PayLog');
        $ShippingModel = D('shipping');
        $OrderReturnModel = D('OrderReturn');
        $OrderReturnItemsModel = D('OrderReturnItems');
        $OrderInvoiceModel = D('OrderInvoice');
        $OrderExtendModel = D('OrderExtend');
        $data = $OrderReturnModel->getSynList(2, null);
        if (empty($data)) {
            return $this->apiReturn(0, '未查询到需推送单据');
        }
        //获取批量查询条件
        foreach ($data as $v) {
            $ids[] = $v['return_id'];
            $create_uids[] = $v['create_uid'];
            $order_ids[] = $v['order_id'];
            $shipping_ids[] = $v['shipping_id'];
        }

        $map = array(
            'order_id' => array('in', $order_ids)
        );
        //批量查询明细
        $field = 'RI.return_items_id AS rec_id, RI.return_id, RI.goods_id, RI.goods_name, RI.brand_id, RI.brand_name, RI.supplier_id, RI.supplier_name, RI.sku_name, RI.single_price, RI.return_num AS send_num, RI.return_num, OI.goods_packing, OI.goods_class, OI.goods_unit';
        $list = $OrderReturnItemsModel->getList(array('return_id' => array('in', $ids)), $field, 1);
        //批量查询创建人名
        $create_user = $CmsModel->table('user_info')->where(array('userId' => array('in', $create_uids)))->getField('userId, name', true);
        //批量查询订单金额
        $orders = $OrderModel->where($map)->getField('order_id,order_amount,sale_id', true);
        //批量查询支付信息
        $paylogs = $PayLogModel->where($map)->order('pay_time,pay_log_id')->getField('order_id, pay_id, pay_name', true);
        //批量查询物流公司名
        $shippings = $ShippingModel->where(array('shipping_id' => array('in', $shipping_ids)))->getField('shipping_id, shipping_name', true);
        //批量查询发票信息
        $invoices = $OrderInvoiceModel->where($map)->getField('order_id,inv_type,invoice_status', true);
        //批量查询订单附加信息
        $extends = $OrderExtendModel->where($map)->getField('order_id,settle_period_type,settle_period_day', true);

        foreach ($list as $v) {
            $detail[$v['return_id']][] = $v;
            $items[$v['return_id']][] = array(
                'goods_id' => $v['goods_id'],
                'goods_name' => $v['goods_name'],
                'brand_id' => $v['brand_id'],
                'brand_name' => $v['brand_name'],
                'supplier_id' => $v['supplier_id'],
                'supplier_name' => $v['supplier_name'],
                'packing' => $v['goods_packing'],
                'currency' => 0,
                'goods_price' => 0,
                'single_price' => $v['single_price'],
                'order_quantity' => $v['return_num'],
                'goods_class' => $v['goods_class'],
                'goods_unit' => $v['goods_unit'],
                'deliver_quantity' => $v['return_num'],
            );
        }
        unset($list);
        $error = array();
        $pay_type_map = array(
            '0' => '微信支付',
            '1' => '支付宝',
            '2' => '银联支付(B2B)',
            '3' => '银联支付(B2C)',
            '4' => '账期支付',
            '5' => '京东支付',
            '6' => '交通银行',
            '7' => '恒生银行',
            '8' => '钱包支付',
        );

        foreach ($data as $v) {
            //取消推送标记
            $res = $OrderReturnModel->setSyn($v['return_id'], -1);
            if ($res === false) {
                return $this->apiReturn(-1, $v['return_sn'].'标记已推送失败');
            }

            $pay_type = array_search($paylogs[$v['order_id']]['pay_name'], $pay_type_map);
            if ($pay_type != '4') {//非账期
                //退款生成
                $datas = array(
                    'order_type' => $v['order_goods_type'],
                    'return_id' => $v['return_id'],
                    'return_sn' => $v['return_sn'],
                    'pay_type' => !is_null($pay_type) ? $pay_type : -1,
                    'currency' => $v['currency'],
                    'user_id' => $v['user_id'],
                    'mobile' => $v['mobile'],
                    'email' => $v['email'],
                    'order_id' => $v['order_id'],
                    'order_sn' => $v['order_sn'],
                    'sale_id' => $orders[$v['order_id']]['sale_id'],
                    'order_amount' => $orders[$v['order_id']]['order_amount'],
                    'order_pay_amount' => $v['pay_amount'],
                    'refund_amount' => $v['return_amount'],
                    'create_name' => $create_user[$v['create_uid']],
                    'refund_reason' => $v['return_reason'],
                    'refund_shipping' => !empty($shippings[$v['shipping_id']]) ? $shippings[$v['shipping_id']] : '',
                    'refund_shipping_no' => $v['shipping_sn'],
                    'company_name' => $v['company_name'],
                    'bank_account' => $v['bank_account'],
                    'bank_name' => $v['bank_name'],
                    'bank_sub_name' => $v['bank_sub_name'],
                    'bank_user' => $v['bank_user'],
                    'items' => $detail[$v['return_id']],
                );
                $res = $this->refundSyn($datas);
                //推送成功或已推送过的
                if ($res['err_code'] !== 0 && $res['err_code'] !== 1004) {
                    return $this->apiReturn(-1, $v['return_sn'].'推送生成退款单失败');
                }
            }

            $currency = $v['currency'];
            //退货信息同步
            $datas = array(
                'delivered_sn' => $v['return_sn'],
                'user_id' => $v['user_id'],
                'mobile' => $v['mobile'],
                'email' => $v['email'],
                'order_id' => $v['order_id'],
                'order_sn' => $v['order_sn'],
                'deliver_time' => $v['putaway_time'],
                'invoice_status' => -1,
                'invoice_type' => -1,
                'deliver_fee' => 0,
                'delivered_type' => 2,#1发货 2退货
                'settle_type' => $pay_type == '4' ? 1 : 0,
                'settle_period_type' => $extends[$v['order_id']]['settle_period_type'],
                'settle_period_day' => $extends[$v['order_id']]['settle_period_day'],
                'items' => array_map(function($v) use ($currency){
                    $v['currency'] = $currency;
                    return $v;
                }, $items[$v['return_id']]),
            );
            $res = $this->returnsyn($datas);
            if ($res['err_code'] !== 0) {
                return $this->apiReturn(-1, $v['return_sn'].'同步至财务退货单失败');
            }
        }
        return $this->apiReturn(0, '推送成功');
    }

    /**
     * 获取待神策上报订单
     * @return [type] [description]
     */
    public function waitSensors()
    {
        $OrderExtendModel = D('OrderExtend');
        $map = array(
            'sensors_syn' => array('gt', -1),
        );
        $order_ids = $OrderExtendModel->where($map)->getField('order_id', true);
        return $this->apiReturn(0, '', $order_ids);
    }


    /**
     * 订单相关神策上报
     * @return [type] [description]
     */
    public function reportSensors()
    {
        $order_id = $this->data['order_id'];
        $OrderModel = D('Order');
        $OrderItemsModel = D('OrderItems');
        $OrderExtendModel = D('OrderExtend');
        $OrderPriceModel = D('OrderPrice');
        $OrderInvoiceModel = D('OrderInvoice');
        $OrderAddressModel = D('OrderAddress');
        $OrderShippingModel = D('OrderShipping');
        $PayLogModel = D('PayLog');
        $info = $OrderModel->getInfo($order_id);
        cookie('uid', $info['user_id']);

        $address = $OrderAddressModel->getInfo($order_id, 1);
        $invoice = $OrderInvoiceModel->getInfo($order_id);
        $shipping = $OrderShippingModel->getInfo($order_id, 1);
        $extend = $OrderExtendModel->getInfo($order_id);
        $user_address = $this->getAddress($address['address_id']);
        $user_invoice = $this->getUserInvoice($invoice['tax_id']);
        $shipping_fee = price_format($OrderPriceModel->getShippingPrice($order_id));
        $extra_fee = price_format($OrderPriceModel->getExtPrice($order_id));
        $goods_amount = price_format($OrderPriceModel->getGoodsPrice($order_id));
        $coupon = $this->getOrderCoupon($order_id);

        $map = array(
            'order_id' => $order_id,
        );
        $save = array(
            'sensors_last_time' => time(),
        );
        //订单提交
        if ($extend['sensors_syn'] == 1) {
            parse_str(str_replace(',', '&', $info['order_source']), $order_source);
            $properties = array(
                'event' => 'submitOrder',
                'user_id' => $info['user_id'],
                'order_id' => $order_id,
                'order_ptag' => !empty($order_source['ptag']) ? $order_source['ptag'] : '',
                'order_amount' => $info['order_amount'],
                'consignee' => $address['consignee'],
                'province' => $address['province_val'],
                'city' => $address['city_val'],
                'district' => $address['district_val'],
                'address' => $address['address'],
                'shipping_fee' => $shipping_fee,
                // 'is_use_integral' => false,
                // 'integral_number' => 0,
                // 'integral_money' => 0,
                'order_shipping_type' => $info['order_shipping_type'] == 1 ? '快递配送' : '自提',
                'is_default_address' => $user_address['data']['is_default'] == 1,
                'is_default' => $user_invoice['data']['is_default'] == 1,
                'inv_type' => C('ORDER_INVOICE_TYPE.'.$invoice['inv_type']),
                'extra_fee' => $extra_fee,
                'order_remark' => $info['order_remark'],
                'is_use_bonus' => !empty($coupon['data']),
                'coupon_id' => intval($coupon['data']['coupon_id']),
                'use_integral' => 0,
                'order_source' => $extend['order_type'] > 0 ? '后台' : '前端',
            );

            $res = $this->report_data($properties);
            if ($res !== false) {
                $save['sensors_syn'] = 2;
                // $extend['sensors_syn'] = 2;
                $OrderExtendModel->where($map)->save($save);
            }

            $coupon = $coupon['data'];
            //神策数据上报 (使用券)
            $properties = array(
                'event'         =>  'useDiscount',
                'user_id'       =>  $info['user_id'],
                'coupon_id'     =>  $coupon['coupon_id'],
                'order_id'      =>  $order_id,
                'coupon_name'   =>  $coupon['coupon_name'],
                'coupon_amount' =>  (1==$coupon['coupon_type'])?$coupon['sale_amount']:0,
                'discount'      =>  (2==$coupon['coupon_type'])?$coupon['sale_amount']:0,
                'coupon_goods_range'  =>  C('COUPON_GOODS_RANGE.'.strval($coupon['coupon_goods_range'])),
                'coupon_mall_type'  =>  C('COUPON_MALL_TYPE.'.strval($coupon['coupon_mall_type'])),
                'selected_brand_id' =>  $coupon['selected_brand_id'],
                'coupon_type'   =>  C('COUPON_TYPE.'.strval($coupon['coupon_type'])),
                'issue_type'    =>  C('ISSUE_TYPE.'.strval($coupon['issue_type'])),
                'order_amount'  =>  $info['order_amount']
            );
            $this->report_data($properties);

        }

        //订单明细
        if ($extend['sensors_syn'] == 2) {
            $data = $OrderItemsModel->getOrderList($order_id, '', null);
            foreach ($data as $v) {
                $goods = $this->getGoods($v['goods_id']);
                $has_coupon = $this->getHasCoupon($v['supplier_id'], $v['brand_id'], 2);
                $properties = array(
                    'event' => 'submitOrderDetail',
                    'user_id' => $info['user_id'],
                    'order_id' => $order_id,
                    'goods_id' => $v['goods_id'],
                    'goods_name' => $v['goods_name'],
                    'class_name1' => $goods['data']['class1_name'],
                    'class_name2' => $goods['data']['class2_name'],
                    'goods_price' => $v['goods_price'],
                    'goods_number' => $v['goods_number'],
                    'preferential_price' => price_format($v['preferential_price'], 0, 4),
                    'price' => price_format($v['goods_price'] * $v['goods_number'], 0, 4),
                    'brand_name' => $v['brand_name'],
                    'supplier_name' => $v['supplier_name'],
                    'encap' => $v['goods_encap'],
                    'batch_sn' => '',
                    'moq' => $v['goods_moq'],
                    'mpq' => $v['goods_spq'],
                    'delivery_place' => $info['delivery_place'],
                    'ac_type' => $v['ac_type'] > 0,
                    'has_activity' => strpos($v['order_source'], 'activity') !== false,
                    'has_bonus' => $has_coupon['err_code'] == 0 && !empty($has_coupon['data']),
                    'submit_the_first_order' =>$OrderModel->is_one_order($info['user_id'])>1 ? false:true,//是否为第一笔订单
                );
                $this->report_data($properties);
            }
            if ($res !== false) {
                $save['sensors_syn'] = -1;
                $OrderExtendModel->where($map)->save($save);
            }
        }

        //支付订单
        if ($extend['sensors_syn'] == 3) {
            $last = $PayLogModel->getLastPayed($order_id);
            $map_p = array(
                'order_id' => $order_id,
                'create_time' => $last['pay_time'],
                'price_type' => -7
            );
            $pay_preferential = floatval($OrderPriceModel->where($map_p)->getField('price'));
            $properties = array(
                'event' => 'payOrder',
                'user_id' => $info['user_id'],
                'order_id' => $order_id,
                'order_amount' => $info['order_amount'],
                'pay_amount' => price_format($last['pay_amount']),
                'pay_preferential' => price_format($pay_preferential),
                'pay_type' => C('PAY_LOG_PAY_TYPE.'. $last['pay_type']),
                'pay_name' => $last['pay_name'],
                'price_update_type' => $info['adjust_count'] > 0 ? '后台调价' : '后台未调价',
                'coupon_id' => intval($coupon['data']['coupon_id']),
                'pay_the_first_order' => $OrderModel->is_one_order($info['user_id'],4)> 1 ? false:true,//是否为首笔付款订单
                'get_integral' => 0,
            );
            $res = $this->report_data($properties);
            if ($res !== false) {
                $save['sensors_syn'] = -1;
                $OrderExtendModel->where($map)->save($save);
            }
        }

        //取消订单
        if ($extend['sensors_syn'] == 4) {
            $log = $PayLogModel->where($map)->select();
            $map_p = array(
                'order_id' => $order_id,
                'price_type' => -7
            );
            $pay_preferential = $OrderPriceModel->where($map_p)->sum('price');
            $pay_amount = 0;
            foreach ($log as $v) {
                $pay_type[] = C('PAY_LOG_PAY_TYPE.'.$v['pay_type']);
                $pay_name[] = $v['pay_name'];
                $pay_amount += $v['pay_amount'];
            }
            $properties = array(
                'event' => 'cancelPayOrder',
                'user_id' => $info['user_id'],
                'order_id' => $order_id,
                'order_shipping_type' => $info['order_shipping_type'] == 1 ? '快递配送' : '自提',
                'order_amount' => $info['order_amount'],
                'pay_type' => implode(',', $pay_type),
                'pay_name' => implode(',', $pay_name),
                'pay_preferential' => price_format($pay_preferential),
                'pay_amount' => price_format($pay_amount),
                'coupon_id' => intval($coupon['data']['coupon_id']),
                'price_update_type' => $info['adjust_count'] > 0 ? '后台调价' : '后台未调价',
                'refund_integral' => 0,
                'cancel_reason' => $info['cancel_reason'],
            );
            $res = $this->report_data($properties);
            if ($res !== false) {
                $save['sensors_syn'] = -1;
                $OrderExtendModel->where($map)->save($save);
            }
        }
        return $this->apiReturn(0);
    }

    // 入库后同步退货单
    public function sysOrderReturn()
    {
        $return_sn    = $this->data['delivery_sn']; // 退货单号
        $return_items = $this->data['detail']; // 退货明细

        if (!$return_sn || empty($return_items)) return $this->apiReturn(10012, '未获取到退货单号或退货明细');

        $OrderReturnModel      = D('OrderReturn');
        $OrderReturnItemsModel = D('OrderReturnItems');

        $removal_sn = $OrderReturnModel->where(['return_sn'=>$return_sn])->getField('removal_sn');

        if (!$removal_sn) return $this->apiReturn(10013, '未获取到出库单号');

        $fs_items = [];

        // 更新入库单号
        $res = $OrderReturnModel->where(['return_sn' => $return_sn])->save(['putaway_sn' => $return_items[0]['instock_sn']]); 

        if ($res === false) return $this->apiReturn(10015, '更新入库单号失败');

        foreach ($return_items as $k=>$v) {
            $map = [];
            $map['return_sn'] = $return_sn;
            $map['goods_id']  = $v['goods_id'];
            $return_num = $OrderReturnItemsModel->where($map)->getField('return_num');

            // 若入库数量不等于退货数量，则返回错误提示
            if ($v['instock_number'] != $return_num) return $this->apiReturn(10014, '入库数量与退货数量不匹配');

            $cost = array();
            $cost[$removal_sn] = $v['goods_id'];
            $picking_price = $this->getCostPrice($cost);

            if (!$picking_price) {
                \Think\Log::record('参数：'.json_encode($cost));
                \Think\Log::record('采购成本：'.json_encode($picking_price));
                return $this->apiReturn(10015, '无采购成本');
            }

            $fs_items[$k]['cost']       = $picking_price[$v['goods_id']]; // 获取采购成本  $v['picking_price'];  //入库金额
            $fs_items[$k]['num']        = $v['instock_number']; //入库数量
            $fs_items[$k]['picking_sn'] = $v['purchase_sn'];    //采购单号
            $fs_items[$k]['putaway_sn'] = $v['instock_sn'];     //入库单号
            $fs_items[$k]['goods_id']   = $v['goods_id']; 
        }

        $data['data']      = urlencode(json_encode($fs_items));
        $data['timestamp'] = time();
        $data['type']      = 'sku.instock';

        $token = $this->WMSencryption($data);

        if (strpos(API_DOMAIN, 'sz') === false) {
            $fs_url = STONE_DOMAIN;
        } else {
            $fs_url = SZ_STONE_DOMAIN;
        }

        $url = $fs_url.'/wmsapi/WebApiStockAuthPush?token='.$token;
        $res = post_curl($url, $data);
        $res = json_decode($res, true);
        
        if (!$res || $res['errcode'] != 0) {
            \Think\Log::record('推送基石接口返回：'.json_encode($res));
            return $this->apiReturn(10016, '基石推送入库失败');
        }

        // 调整退货单
        $update['status']       = 10;
        $update['syn_sign']     = 2; // 标记同步财务系统
        $update['putaway_time'] = $this->data['instock_time']; // 入库时间

        $return = $OrderReturnModel->where(['return_sn' => $return_sn])->save($update);

        if (!$return) return $this->apiReturn(10017, '同步退货单失败'); 

        return $this->apiReturn(0, '同步退货单成功');  
    }

    // wms token
    public function WMSencryption($Data)
    {
        $Key = 'j9q##VRhaXBEtznIEeDiR@1Hvy0sW3wp'; // WMS加密密钥
        if (empty($Data['timestamp'])) return false;
        $Token = md5($Data['data'].$Data['timestamp'].$Key);
        return $Token;
    }

    // 获取采购成本
    public function getCostPrice($data)
    {
        $res = post_curl(FINANCE_DOMAIN.'/webapi/getSkuPrice', $data);

        if (!empty($res)) {
            $res = json_decode($res, true);

            if ($res['err_code'] == 0) return $res['data'];
        }

        return false;
    }

}
