<?php
namespace Order\Controller;

use Order\Controller\BaseController;
use Payment\Cpcn;

class PayController extends BaseController
{

    use \Order\Traits\PayTrait;
    const WX = 'wxpay';
    const ALI = 'alipay';
    const UNION = 'unionpay';
    const UNIONB2B = 'unionpay_b2b';
    const CPCN = 'cpcn';
    const WALLET = 'wallet';

    static $topay_name = array(
        '1' => '全款',
        '2' => '预付款',
        '3' => '尾款',
    );

    /**
     * 统一支付接口
     * 银联、支付宝、垃圾微信
     * @return [type] [description]
     */
    public function _initialize()
    {
        Vendor('payment.cpcn.cpcn');//中金支付sdk引入
        parent::_initialize();
        //来源
        // $_SERVER['HTTP_REFERER'] = 'http://m.liexin.com';
        if (preg_match('/^http(?:s)?:\/\/([^\/?]+)/', $_SERVER['HTTP_REFERER'], $args)) {
            $this->fullreferer = $args[0];// 例子http://www.liexin.com
            $this->referer = $args[1];// 例子www.liexin.com
        }

    }

    /**
     * 获取支付方式的实例
     * @param  [type] $pay_code 支付方式
     * @param  [type] $rescue   救援模式，true使用旧支付对接方式，false使用中金对接
     * @return [type]           [description]
     */
    private function _pay($pay_code = null, $rescue = null, $cpcn_mode = null)
    {
        $this->opentracingJager->init();
        $this->opentracingJager->simpleInject(__FUNCTION__);
        $this->opentracingJager->setTag(__FUNCTION__,"action","_pay");
        $this->opentracingJager->setTag(__FUNCTION__,"remark","获取支付方式的实例");
        $this->opentracingJager->log(__FUNCTION__,sprintf("pay_code:%s,rescue:%s,cpcn_mode:%s",$pay_code,$rescue,$cpcn_mode));
        is_null($pay_code) && $pay_code = I('request.pay_code', '');
        if (empty($pay_code)) {
            $this->opentracingJager->finish(__FUNCTION__);
            return $this->apiReturn(41028, '无效付款方式');
        }
        $this->pay_code = $pay_code;

        $payment = S('payment');
        if (empty($payment[$pay_code])) {
            $PaymentModel = D('Payment');
            $payment[$pay_code] = $PaymentModel->getInfoByCode($pay_code);
            if (!empty($payment[$pay_code])) {
                S('payment', $payment);
            }
        }

        is_null($rescue) && $rescue = I('request.rescue');//备用方案
        if ($rescue) {
            //旧版有银联支付B2B\B2C、微信、支付宝
            if (empty($payment[$pay_code])) {
                $this->opentracingJager->finish(__FUNCTION__);
                return $this->apiReturn(41028, '无效付款方式');
            }
            Vendor('payment.'.$pay_code.'.'.$pay_code);
            $this->Pay = new $pay_code();

        } else {
            //新版中金银联支付B2B\B2C、微信、支付宝
            $cpcn = self::CPCN;
            is_null($cpcn_mode) && $cpcn_mode = Cpcn::BUSINESS_MODE;
            $this->Pay = new Cpcn($cpcn_mode);
            //非直通车 不支持 前置选择B2B还是B2C，故无法得知是B2B还是B2C
            if ($pay_code == self::UNIONB2B) {
                $this->pay_code = self::UNION;
            }
        }
        $this->opentracingJager->finish(__FUNCTION__);
        return $this->apiReturn(0, '获取成功', $pay_code);
    }

    /*
     * 验证支付订单和用户绑定关系
     */
    protected function checkOrderByUser($order_id=0){
        $user_id = cookie("uid");
        if (!$user_id) return false;
        return D("Order")->checkOrderByUser($user_id,$order_id);
    }

    //前往支付页
    public function toDo($act = '')
    {
        $this->apiRecord(sprintf("请求参数:%s",json_encode($_REQUEST)));
        $this->apiRecord(sprintf("HTTP_USER_AGENT:%s",$_SERVER['HTTP_USER_AGENT']));
        //支付单据类型 0订单 1充值 2PCB
        $types = I('request.types', 0, 'intval');
        //单据ID
        $order_id = I('request.order_id', 0, 'intval');
        //钱包支付
        $pay_password = I('request.pay_password', '', 'trim');

        //充值 暂时不用
        $bank_id = I('request.bank_id', 0, 'intval');
        $account_type = I('request.account_type', 1, 'intval');//1:B2C 或 2:B2B

        //共用
        $pay_mode = I('request.pay_mode', 0, 'intval');//支付宝 0前置二维码 2跳转 || 微信 0扫码支付 1H5支付 2公众号支付
        $site_type = I('request.site_type', 1, 'intval');//1PC 2H5
        $open_id = I('open_id', '', 'trim');//小程序传入
        $miniProgram = cookie('miniProgram');

        switch ($site_type) {
            case '1' :
                $this->domain = WWW_DOMAIN;
                break;
            case '2' :
                $this->domain = M_DOMAIN;
                break;
            default :
                exit();
        }
        $pf = platform();
        if($pf != 6){
            if($types == 0 && !$this->checkOrderByUser($order_id)){
                // return $this->apiReturn(41028, "无效的订单");
            }
        }

        $pushReportMonitorLogData["interface_type"]=5;
        $pushReportMonitorLogData["remark"]="";

        //选择支付单据
        switch(C('PAY_ORDER_ID_TYPE.'. $types)) {
            case 'SALE_ORDER'://订单支付
                $res = $this->_pay();
                if ($res['err_code'] != 0) {
                    $this->pushReportMonitorLog([
                        "interface_type"=>"5",
                        "err_msg"=>$res['err_msg'],
                        "err_code"=>$res['err_code'],
                        "remark"=>sprintf("前往支付页面异常,获取支付方式的实例错误:%s",json_encode($res)),

                    ]);
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
                $info = $this->orderToDo($order_id);

                $pushReportMonitorLogData["interface_type"]=5;
                $pushReportMonitorLogData["remark"]=" 订单支付 ";
                break;
            case 'RECHARGE_ORDER'://充值
                $res = $this->_pay(null, null, Cpcn::MARKET_MODE);
                if ($res['err_code'] != 0) {
                    $this->pushReportMonitorLog([
                        "interface_type"=>"7",
                        "err_msg"=>$res['err_msg'],
                        "err_code"=>$res['err_code'],
                        "remark"=>sprintf("钱包充值失败:%s",json_encode($res)),

                    ]);
                    return $this->apiReturn($res['err_code'], $res['err_code']);
                }
                if ($this->pay_code == self::WALLET) {//钱包充值钱包
                    $this->pushReportMonitorLog([
                        "interface_type"=>"7",
                        "err_msg"=>"无效付款方式",
                        "err_code"=>"41028",
                        "remark"=>sprintf("pay_code = %s",self::WALLET),

                    ]);
                    return $this->apiReturn(41028, '无效付款方式');
                }
                $param['bank_id'] = isset($bank_id) ? $bank_id : 0;
                $param['account_type'] = isset($account_type) ? $account_type : 1;
                $info = $this->rechargeToDo($order_id, $bank_id, $account_type);
                $pushReportMonitorLogData["interface_type"]=7;
                $pushReportMonitorLogData["remark"]=" 钱包充值 ";
                break;
            case 'PCB_SALE_ORDER'://PCB
                // $res = $this->_pay();
                // if ($res['err_code'] != 0) {
                //     return $this->apiReturn($res['err_code'], $res['err_code']);
                // }
                // $info = $this->pcbToDo($order_id);
                // $pushReportMonitorLogData["interface_type"]=5;
                // $pushReportMonitorLogData["remark"]=" pcb支付 ";
                return;
                break;
            default:
                return;
                break;
        }


        if ($info['err_code'] != 0) {
            $this->pushReportMonitorLog([
                "interface_type"=>$pushReportMonitorLogData['interface_type'],
                "err_msg"=>$info['err_msg'],
                "err_code"=>$info['err_code'],
                "remark"=>sprintf(" %s 异常:%s",$pushReportMonitorLogData["remark"],json_encode($info)),

            ]);
            return $this->apiReturn($info['err_code'], $info['err_msg']);
        }



        //微信授权回调参数
        $param['order_id'] = $order_id;
        $param['pay_code'] = $this->pay_code;
        $param['site_type'] = $site_type;
        $param['types'] = $types;
        $param['pf'] = platform();

        $behavior_push = false;
        $behavior_param = [];

        //由于坑爹微信，所以单独写个处理微信的多支付方式
        if ($this->pay_code == self::WX) {
            //判断是否是微信浏览器
            $agent = getAgentInfo();
            $pf = platform();
            
            if ($agent['bro'] == 9 && $pf == 6 || !in_array($miniProgram, array('false', null))) {
                $pay_mode = 3;//小程序支付
            } elseif ($agent['bro'] == 9) { //微信内打开 请求授权 获取open_id
                $pay_mode = 2;//公众号支付
            } elseif ($this->pay_code == self::WX && $site_type == 2) {
                $pay_mode = 1;//H5支付
            } elseif ($this->pay_code == self::WX) {
                $pay_mode = 0;//扫码支付
            }
            if ($pay_mode == 2) {
                if ($open_id == "undefined"  || empty($open_id)){
                    $open_id = session('open_id');
                }
                $url = strtolower(rtrim(API_DOMAIN, '/').'/'.CONTROLLER_NAME.'/'.ACTION_NAME.'?'.http_build_query($param));
                if ($open_id == "undefined" ||  empty($open_id) ) {
                    $code = I('code', '');
                    $res = $this->getWxInfo($url, $code);
                    \Think\Log::write(sprintf("getWxInfo:%s",json_encode($res)));
                    if ($res['data']['status'] == 2) { //用户确认授权页面
                        return $this->apiReturn(0, 'oauth', $res['data']['data']);
                    }
                }
            }

            \Think\Log::write(sprintf("open_id:%s",$open_id));
            $str = $this->wxMulitPay($info['data'], $pay_mode, $open_id);
            try {
                \Think\Log::write(sprintf("wxMulitPay:%s",json_encode($str)));
                $this->behavior_push(7, 0, $behavior_param);
            } catch (\Exception $e) {
            }
            
            if ($act == 'reload') {//用于微信刷新二维码使用
                return $str;
            }elseif ($pay_mode == 3) {
                return $this->apiReturn(0, 'program', $str);
            } elseif ($pay_mode != 2) { //微信内打开 请求授权 获取unionId
                return $this->apiReturn(0, 'QR', $str);
            } elseif (isset($_REQUEST["weixintag"]) || IS_AJAX || strpos($_SERVER['HTTP_ACCEPT'], 'json') !== false) {
                return $this->apiReturn(0, 'js', $url);
            }

        } elseif ($this->pay_code == self::ALI){
            $str = $this->aliMulitPay($info['data'], $pay_mode);
            try {
                $this->behavior_push(7, 0, $behavior_param);
            } catch (\Exception $e) {
            }
            $now_pay_type = explode('\\', strtolower(get_class($this->Pay)));//当前支付实例名
            if ($act == 'reload') {
                return $str;
            } elseif ($now_pay_type[1] == self::CPCN && $pay_mode == 0) {//中金支付宝二维码
                return $this->apiReturn(0, 'QR', $str);
            } else {
                $behavior_push = true;
            }

        } elseif (in_array($this->pay_code, array(self::UNION, self::UNION, self::CPCN))) {
            //网银
            $str = $this->bankMulitPay($info['data'], $pay_mode);
            $behavior_push = true;

        } else {
            //钱包
            $res = $this->checkLogin();//强登录校验
            if ($res['err_code'] != 0) {
                $this->pushReportMonitorLog([
                    "interface_type"=>"5",
                    "err_msg"=>$res['err_msg'],
                    "err_code"=>$res['err_code'],
                    "remark"=>sprintf("钱包支付异常异常:%s",json_encode($res)),

                ]);

                return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
            }
            $res = $this->walletMulitPay($info['data'], $pay_password);
            try {
                $this->behavior_push(7, 0, $behavior_param);
            } catch (\Exception $e) {
            }

            if ($res['err_code'] != 0) {
                $this->pushReportMonitorLog([
                    "interface_type"=>"5",
                    "err_msg"=>$info['err_msg'],
                    "err_code"=>$info['err_code'],
                    "remark"=>sprintf("钱包支付异常异常:walletMulitPay方法 异常%s, ordertodo方法返回 %s",json_encode($res),json_encode($info)),

                ]);
            }
            return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
        }

        echo $str;
        if ($behavior_push) {
            try {
                $this->behavior_push(7, 0, $behavior_param);
            } catch (\Exception $e) {
            }
        }
        // dump($str, 1,'',0);
    }

    /**
     * 订单
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    private function orderToDo($order_id)
    {
        $err_url = $this->domain.'/v3/user/orderdetail?order_id='.$order_id;

        //订单信息
        $OrderModel = D('Order');
        $OrderPriceModel = D('OrderPrice');
        $info = $OrderModel->getInfo($order_id);
        
        //查找订单是否含有自营现卖商品
//        $hasZiYi = D("OrderItems")->checkoutOrderByZi(["order_id"=>$order_id,"sale_type"=>1]);
        if (empty($info)) {
            return $this->apiReturn(41036, '未找到相关订单');
        }
        if ($info['delivery_place'] == 2) {
            !empty($this->fullreferer) && redirect($err_url);
            return $this->apiReturn(41042, '暂不支持该付款方式', $info);
        }
        if ($info['status'] < 2) {
            !empty($this->fullreferer) && redirect($err_url);
            return $this->apiReturn(41040, '订单尚未审核，无法付款', $info);
        } elseif ($info['status'] > 3) {
            !empty($this->fullreferer) && redirect($err_url);
            return $this->apiReturn(41041, '订单已付款，无法再付款', $info);
        }
//        elseif ($hasZiYi) {//自营现卖
//            //强制调用WMS预分配接口，不影响用户支付(2018-7-10张文确认)
//            try {
//                $res = $this->makeOrder($order_id);
//                if ($res['code'] != 0) {
//                    $send_data['data'] = array(
//                        'msg' => '订单：' . $info['order_sn'] . '准备付款，wms预分配失败'
//                    );
//                    $send_data = json_encode($send_data);
//                    $this->sendOrderMsg('wms_error_warning', $send_data, '', true);
//                }
//            } catch (\Exception $e) {}
//        }

        $behavior_param = array('order_id' => $info['order_id'], 'order_sn' => $info['order_sn'], 'pay_code' => $this->pay_code);

        //当前支付行为
        $topay_type = 1;//全款
        if ($info['order_pay_type'] == 2 && $info['status'] == 2) {
            //预付款+待付款
            $topay_type = 2;
        } elseif ($info['order_pay_type'] == 2) {
            $topay_type = 3;//尾款
        }

        //该付金额
        $price = $OrderPriceModel->getToPay($info['order_id'], $info['order_pay_type'], $info['status'], $info['advance_amount']);

        //订单号——订单支付类型——时分秒7位随机码  包分隔符共30位
        //银联40位 、 支付宝64位 、 微信32位
        $info['serial_arr'] =  array($info['order_sn'], $topay_type, date('His').hash_key(7,2));
        $info['serial_arr2'] =  array($info['order_sn'], $topay_type, date('His').hash_key(7,2));
        $info['serial_remark'] = $info['order_sn'].'|'.C('ORDER_GOODS_TYPE.'."1");
        $info['topay_name'] = self::$topay_name[$topay_type];
        $info['topay_type'] = $topay_type;
        $info['pay_amount'] = $price;
        $info['wallet_preferential'] = 0;
        // p($info);exit;
        // $info = $this->walletPreferential($info);
        return $this->apiReturn(0, '', $info);
    }

    /**
     * 充值
     * @return [type] [description]
     */
    private function rechargeToDo($wallet_id, $bank_id, $account_type)
    {
        $err_url = $this->domain.'/v3/user/wallet?wallet_id='.$wallet_id;
        // $bank_list = C('CPCN_BANK_B2C');
        // if (!empty($bank_id) && !isset($bank_list[$bank_id])) {
        //     return $this->apiReturn(41056, '暂不支持该银行支付');
        // }

        //订单信息
        $wallet = $this->getWallet($wallet_id);
        $info = $wallet['data'];
        if (empty($info) || $info['wallet_type'] != 1) {
            return $this->apiReturn(41048, '未找到相关订单');
        } elseif ($info['status'] == -1) {
            !empty($this->fullreferer) && redirect($err_url);
            return $this->apiReturn(41049, '充值单已取消，无法再付款', $info);
        } elseif ($info['status'] != 1) {
            !empty($this->fullreferer) && redirect($err_url);
            return $this->apiReturn(41050, '充值单已完成，无法再付款', $info);
        }

        //订单号——时分秒9位随机码  包分隔符共30位
        //银联40位 、 支付宝64位 、 微信32位
        $info['serial_arr'] = array($info['wallet_sn'], date('His').hash_key(9,2));
        $info['card_type'] = $account_type == 1 ? '01' : '';//网银支付限制信用卡类型,企业账号不需要传
        $info['limit_pay'] = '20';//聚合支付限制信用卡类型 10可用，20不可用
        $info['serial_remark'] = $info['wallet_sn'].'|充值';
        $info['topay_name'] = '充值钱包';
        $info['bank_id'] = $bank_id;
        $info['account_type'] = $account_type;
        $info['pay_amount'] = $info['amount'];
        return $this->apiReturn(0, '', $info);
    }

    /**
     * PCB订单
     * @param  [type] $pcb_id [description]
     * @return [type]         [description]
     */
    private function pcbToDo($pcb_id)
    {
        $err_url = $this->domain.'/v3/pcbdetail?order_id='.$pcb_id;
        $PcbOrderModel = D('PcbOrder');
        //订单信息
        $info = $PcbOrderModel->getInfo($pcb_id);
        if (empty($info)) {
            return $this->apiReturn(41060, '未找到相关订单');
        } elseif ($info['status'] == -1) {
            !empty($this->fullreferer) && redirect($err_url);
            return $this->apiReturn(41061, '订单已取消，无法再付款');
        } elseif ($info['status'] > 10) {
            !empty($this->fullreferer) && redirect($err_url);
            return $this->apiReturn(41062, '订单已付款，无法再付款');
        }

        //订单号——订单支付类型——时分秒7位随机码  包分隔符共30位
        //银联40位 、 支付宝64位 、 微信32位
        $info['serial_arr'] =  array($info['order_sn'], date('His').hash_key(9,2));
        $info['serial_remark'] = $info['order_sn'].'|PCB';
        $info['pay_amount'] = $info['order_amount'];
        return $this->apiReturn(0, '', $info);
    }

    /**
     * 支付宝 应对中金 多处理方式
     * @param  [type]  $info     [description]
     * @param  [type]  $pay_mode [description]
     * @param  boolean $cache    [description]
     * @return [type]            [description]
     */
    private function aliMulitPay($info, $pay_mode)
    {
        if ($pay_mode == 2) {//H5支付使用原生
            $this->_pay($this->pay_code, true);
        }

        $info['serial_number'] = implode('_', $info['serial_arr']);
        $order = $this->orderPayInfo($info);

        //获取支付信息
        $config = array(
            'pay_code' => $this->pay_code,
            'type' => $pay_mode,//旧使用
            'pay_mode' => $pay_mode,//新使用
            'qr_pay_mode' => 2,//支付宝好看模式
        );
        $str = $this->Pay->get_code($order, $config);
        return $str;
    }

    /**
     * 网银支付
     * @param  [type]  $info     [description]
     * @param  [type]  $pay_mode [description]
     * @param  boolean $cache    [description]
     * @return [type]            [description]
     */
    private function bankMulitPay($info, $pay_mode)
    {
        if ($pay_mode == 2) {//H5支付使用原生
            $this->_pay($this->pay_code, true);
        }
        $now_pay_type = explode('\\', strtolower(get_class($this->Pay)));//当前支付实例名
        //交易流水号（唯一）
        if ($now_pay_type[1] == self::CPCN) { // 支付宝长度64位 字母数字下划线
            $serial_number = implode('_', $info['serial_arr']);
        } else { // 银联长度32位 字母数字
            $serial_number = implode('X', $info['serial_arr']);
        }
        $info['serial_number'] = $serial_number;
        $order = $this->orderPayInfo($info);

        //获取支付信息
        $config = array(
            'pay_code' => $this->pay_code,
            'pay_mode' => $pay_mode,//新使用
        );
        $str = $this->Pay->get_code($order, $config);
        return $str;
    }

    /**
     * 针对微信多支付方式处理（PC扫码、H5支付、公众号支付）
     * 微信一个订单号绑定一种支付方式、多种支付方式需要多个订单号
     * 微信订单只有2个小时有效期，过期需重新换个后缀生成
     * @return [type] [description]
     */
    private function wxMulitPay($info, $pay_mode, $open_id = '')
    {
        $info['serial_number'] = implode('_', $info['serial_arr']);
        $order = $this->orderPayInfo($info);

        //获取支付信息
        $config = array(
            'pay_code' => $this->pay_code,
            'pay_mode' => $pay_mode,
            'open_id' => $open_id,//$open_id,
        );
        if (isset($info['wallet_sn']) && $pay_mode != 0) {
            $config['return_url'] = API_DOMAIN . '/pay/returnurl/wxpay/2?order_sn='.$info['wallet_sn'];
        } elseif ($pay_mode != 0) {
            //H5支付使用原始微信支付
            $config['return_url'] = API_DOMAIN . '/pay/returnurl/wxpay/2?order_sn='.$info['order_sn'];
            $this->_pay($this->pay_code, true);
        }
        $str = $this->Pay->get_code($order, $config);
        return $str;
    }

    /**
     * 支付钱包
     * @param  [type] $info         [description]
     * @param  [type] $pay_password [description]
     * @return [type]               [description]
     */
    private function walletMulitPay($info, $pay_password)
    {
        $info['serial_number'] = implode('_', $info['serial_arr']);
        $info['serial_number2'] = implode('_', $info['serial_arr2']);
        $order = $this->orderPayInfo($info);
        $order['pay_password'] = $pay_password;
        //支付操作
        $res = $this->payWallet($order);
        if ($res['err_code'] != 0 && $res['err_code'] != 180059) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

//        D('OrderPrice')->where(["order_id"=>172297,"price_type"=>-7])->delete();
//        dump(666);exit;

        if ($info['wallet_preferential'] > 0) {
            D('OrderPrice')->startTrans();
            //订单由于支付优惠 补充 订单优惠构成记录
            $OrderPriceModel = D('OrderPrice');
            $OrderItemsModel = D('OrderItems');

            $res = $OrderPriceModel->createOrderPrice($info['order_id'], -1 * $info['wallet_preferential'], -7, $info['currency'], $info['order_sn']);
            if ($res === false) {
                $this->apiReturn(41075, '支付优惠记录失败');
            }

            //货款
            $goods_amount = $OrderPriceModel->getGoodsPrice($info['order_id']);
            //平摊支付优惠金额
            $list = $OrderItemsModel->getOrderList($info['order_id'], '', null);
            $last = end($list);
            $sum_wallet = 0;
            foreach ($list as &$v) {
                $save = array(
                    'rec_id' => $v['rec_id'],
                );
                if ($last['rec_id'] == $v['rec_id']) {
                    $save['pay_preferential_price'] = $info['wallet_preferential'] - $sum_wallet;
                } else {
                    $rate = $v['goods_amount'] / $goods_amount;
                    $save['pay_preferential_price'] = price_format($rate * $info['wallet_preferential'], 0, 4);
                    $sum_wallet += $save['pay_preferential_price'];
                }
                $OrderItemsModel->save($save);
            }
        }
        //订单操作
        $data = array(
            'notify_way' => 1,
            'pay_code' => self::WALLET,
            'order_sn' => $order['order_sn'],
            'order_amount' => $order['order_amount'],
            'serial_number' => $order['serial_number'],
            'pay_type' => $info['topay_type'],
            'pay_order_sn' => $order['serial_number'],
            'create_time' => time(),
        );
        $res = $this->pay_success($data);
        if ($res['err_code'] != 0) {
            D('OrderPrice')->rollback();
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }else{
            D('OrderPrice')->commit();
        }
        return $this->apiReturn(0, '支付成功');
    }

    /**
     * 支付所需相关订单信息
     * @param  [type] $info       [description]
     * @param  string $topay_name [description]
     * @return [type]             [description]
     */
    private function orderPayInfo($info)
    {
        $order = array(
            'order_id' => $info['order_id'],
            'order_sn' => $info['order_sn'],
            'wallet_id' => get_wallet($info['user_id']),
            'user_id' => $info['user_id'],
            'bank_id' => $info['bank_id'],
            'account_type' => $info['account_type'] == 1 ? 11 : 12,
            'card_type' => $info['card_type'],
            'limit_pay' => $info['limit_pay'],
            'serial_number' => $info['serial_number'],
            'serial_number2' => $info['serial_number2'],
            'remark' => $info['serial_remark'],
            'create_time' => $info['create_time'],
            'order_amount' => $info['pay_amount'],
            'topay_name' => $info['topay_name'],
            'goods_items' => '电子元器件',//implode(',', $names),
            'usage' => '销售订单款项',
        );
        return $order;
    }

    /**
     * 钱包支付金额优惠活动 计算实际优惠金额
     * @return [type] [description]
     */
    private function walletPreferential($info)
    {
        $preferential = 0;
        if ($this->pay_code == self::WALLET) {
            //只有联营概念
//            if ($info['order_goods_type'] == 2) {
//                $mall_type = 2;
//            } else {
//                $mall_type = 3;
//            }
            $mall_type = 3;
            $res = $this->walletActive($mall_type, 1);
            //符合最低支付金额 或 无限制
            if ($res['errcode'] === 0 &&
                ($res['data']['require_amount'] == 0 || $res['data']['require_amount'] <= $info['pay_amount'])) {
                if ($res['data']['preferential_type'] == 1) {
                    $preferential = $info['pay_amount'] - price_format($info['pay_amount'] * $res['data']['preferential_value']);
                    //最大优惠限制
                    if ($res['data']['max_preferential_amount'] > 0 && $preferential > $res['data']['max_preferential_amount']) {
                        $preferential = $res['data']['max_preferential_amount'];
                    }
                } else {
                    $preferential = $info['preferential_value'];
                }
            }
            $info['pay_amount'] -= $preferential;//最终支付金额
        }
        $info['wallet_preferential'] = $preferential;//钱包优惠金额
        return $info;
    }

    /**
     * 获取支付方式
     * @return [type] [description]
     */
    public function payment()
    {
        $PaymentModel = D('Payment');
        $datas = $PaymentModel->getList();
        $group = array(
            'unionpay',
        );
        $data = array();
        foreach ($datas as $v) {
            unset($v['pay_config']);
            foreach ($group as $val) {
                if (strpos($v['pay_code'], $val) !== false) {
                    $data[$val][] = $v;
                } else {
                    $data[$v['pay_code']][] = $v;
                }
            }
        }
        return $this->apiReturn(0, '获取成功', $data);
    }

    /**
     * 刷新微信二维码
     * @return [type] [description]
     */
    public function reload()
    {
        $res = $this->_pay();
        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_code']);
        }
         if (in_array($this->pay_code, array(self::WX, self::ALI))) {
            $str = $this->toDo('reload');
        } else {
            return $this->apiReturn(41035, '当前支付方式无需刷新');
        }
        if (!empty($str)) {
            return $this->apiReturn(0, '刷新成功', $str);
        } else {
            return $this->apiReturn(41034, '支付码刷新失败，请稍后重试');
        }
    }

    /**
     * 检查订单某个状态是否已付款
     * @return [type] [description]
     */
    public function check()
    {
        $order_id = I('request.order_id', 0, 'intval');
        $type = I('type', 0, 'intval');
        $types = I('types', 0, 'intval');//单据识别
        $OrderModel = D('Order');
        if (empty($order_id)) {
            return $this->apiReturn(41036, '未找到相关订单');
        }
        $user_id = cookie('uid');
        switch (C('PAY_ORDER_ID_TYPE.'. $types)) {
            case 'SALE_ORDER':
                $info = $OrderModel->getInfo($order_id);
                if ($info['user_id'] != $user_id) {
                    return $this->apiReturn(41037, '未找到用户相关订单');
                }
                //检查订单是否全部已付款
                if ($type == 0) {
                    if ($info['status'] < 4) {
                        return $this->apiReturn(41038, '未付款');
                    }

                //检查订单是否付完预付款
                } else {
                    if ($info['status'] < 3) {
                        return $this->apiReturn(41038, '未付款');
                    }
                }
                break;
            case 'RECHARGE_ORDER':
                $info = $this->getWalletOrderInfo($order_id);
                if ($info['err_code'] != 0) {
                    return $this->apiReturn($info['err_code'], $info['err_msg']);
                }
                //检查订单是否全部已付款
                if ($info['data']['status'] < 10) {
                    return $this->apiReturn(41038, '未付款');
                }
                break;
            case 'PCB_SALE_ORDER':
                $PcbOrderModel = D('PcbOrder');
                $info = $PcbOrderModel->getInfo($order_id);
                if ($info['user_id'] != $user_id) {
                    return $this->apiReturn(41037, '未找到用户相关订单');
                }
                //检查订单是否全部已付款
                if ($info['status'] < 11) {
                    return $this->apiReturn(41038, '未付款');
                }
                break;
        }
        header('Content-Type:application/json; charset=utf-8');
        echo json_encode([
            "err_code" => 0,
            "err_msg" => "已支付",
            "data"=>[
                "rand_str"=>md5_32((string)$order_id)
            ]
        ]);exit; 
        return $this->apiReturn(0, '已支付');

    }

    /**
     * 服务器点对点
     * @return [type] [description]
     */
    public function notifyUrl(){
        //埋点 记录请求日志 和 链路追踪日志
        $this->opentracingJager->init("notifyUrl");
        $this->opentracingJager->setTag("notifyUrl","remark","支付异步回调");
        $this->opentracingJager->setTag("notifyUrl","action","notifyUrl");
        $this->apiRecord("-----------支付异步回调----------");
        $this->apiRecord(sprintf("支付或者充值 第三方请求参数 %s",json_encode($_REQUEST)));
        //埋点
        //sz做日志记录
        $parseUrl = parse_url(API_DOMAIN);
        list($protocl) = explode('.', $parseUrl['host'], 2);
        if ($protocl == 'szapi') {
            file_put_contents('./paylog.json', date('Y-m-d H:i:s').': '.json_encode(I('request.'))."\n", FILE_APPEND);
        }

        $res = $this->_pay();
        if ($res['err_code'] != 0) {
            $this->pushReportMonitorLog([
                "interface_type"=>"6",
                "err_msg"=>$res['err_msg'],
                "err_code"=>$res['err_code'],
                "remark"=>sprintf("支付或者结算回调异步回调异常 %s",json_encode($res)),

            ]);
            $this->opentracingJager->log("notifyUrl",sprintf("%s",json_encode($res)));
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        //判断是否中金请求过来的
        $cpcn_ids = array(C('CPCN_BUSINESS_CONFIG.institution_id'), C('CPCN_MARKET_CONFIG.institution_id'));
        if (!in_array($this->pay_code, $cpcn_ids)) {
            $this->_pay($this->pay_code, true);
            if ($res['err_code'] != 0) {
                $this->opentracingJager->log("notifyUrl",sprintf("%s",json_encode($res)));
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
        //判断回调是否支付成功
        $arr = $this->Pay->response();
        if ($arr && $arr !== true) {
            $this->opentracingJager->simpleInject("payment");
            $this->opentracingJager->log("payment","Pay->response()返回的数据",sprintf("%s",json_encode($arr)));
            $this->apiRecord(sprintf("判断回调是否支付成功,%s",json_encode($arr)));
            switch ($arr['notify_way']) {
                case Cpcn::PAY_WAY:
                    $this->opentracingJager->log("payment","支付行为");
                    $res = $this->pay_success($arr);
                    if ($res['err_code'] != 0) {
                        $this->opentracingJager->log("payment","pay_success返回",sprintf("付款成功,后续操作失败 %s",json_encode($res)));
                        $this->apiRecord(sprintf("付款成功后续操作失败 %s",json_encode($res)));
                        $this->opentracingJager->finish("payment");
                        return $this->Pay->responseOut('fail');
                    }
                    break;
                // case Cpcn::REFUND_WAY:
                //     $res = $this->refund_act($arr);
                //     if ($res['err_code'] != 0) {
                //         return $this->Pay->responseOut('fail');
                //     }
                //     break;
                case Cpcn::SETTLE_WAY:
                    $this->opentracingJager->log("payment","结算行为");
                    if (substr($arr['serial_number'], 0, 1) == '1') {//支付结算
                        $this->opentracingJager->log("payment","支付结算");
                    } else {//提现结算
                        $this->opentracingJager->log("payment","提现结算");
                        $res = $this->setWithdrawFinish(null, $arr['order_sn'], $arr['serial_number'], $arr['transfer_time'], $arr['settle_amount']);
                        if ($res['err_code'] != 0) {
                            $notice['data'] = array(
                                'msg' => $arr['serial_number'].'回调处理失败,code:'.$res['err_code']
                            );
                            sendMsg('settle_error', $notice, 'INNER_PERSON');

                            $this->apiRecord(sprintf("提现失败 %s",json_encode($res)));

                            $this->opentracingJager->log("payment",sprintf("提现结算失败 %s",json_encode($res)));

                            $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
                            $this->opentracingJager->finish("payment");
                            return $this->Pay->responseOut('fail');
                        }
                    }
                    break;
                default:
                    break;
            }
            $this->opentracingJager->finish("payment");
            return $this->Pay->responseOut('success');
        } elseif ($arr === true) {//微信内部已执行
            $this->opentracingJager->log("notifyUrl","微信内部已执行");
        } else {
            $this->apiRecord(sprintf("支付异步回调失败,参数 %s",json_encode(I('request.'))));
            $this->opentracingJager->log("notifyUrl","支付或者结算异步回调失败");
            return $this->Pay->responseOut('fail');
        }

        $this->opentracingJager->finish("notifyUrl");
    }

    /**
     * 支付页面跳转
     * @return [type] [description]
     */
    public function returnUrl(){
        //记录日志
        $this->apiRecord("-----------支付、充值同步回调returnUrl----------");
        $this->apiRecord(json_encode($_REQUEST));
        //链路追踪
        $this->opentracingJager->init(__FUNCTION__);
        $this->opentracingJager->setTag(__FUNCTION__,"action","returnUrl");
        $this->opentracingJager->setTag(__FUNCTION__,"remark",sprintf("--支付同步回调--"));
        //记录日志
        $res = $this->_pay();
        if ($res['err_code'] != 0) {
            $this->pushReportMonitorLog([
                "interface_type"=>"6",
                "err_msg"=>$res['err_msg'],
                "err_code"=>$res['err_code'],
                "remark"=>sprintf("支付、充值同步回调异常：%s",json_encode($res)),

            ]);
            $this->opentracingJager->log(__FUNCTION__,sprintf("支付、充值同步回调异常：%s",json_encode($res)));
            $this->opentracingJager->finish(__FUNCTION__);
            return $this->apiReturn($res['err_code'], $res['err_code']);
        }
        //判断是否中金请求过来的
        $cpcn = I('request.cpcn', 0, 'intval');
        if (!$cpcn) {//非中金
            $this->_pay($this->pay_code, true);
            if ($res['err_code'] != 0) {
                $this->opentracingJager->log(__FUNCTION__,"错误信息",json_encode($res));
                $this->opentracingJager->finish(__FUNCTION__);
                return $this->apiReturn($res['err_code'], $res['err_code']);
            }
        }

        //判断回调是否支付成功
        $arr = $this->Pay->response();
        if ($arr) {
            $this->apiRecord(json_encode($arr));
            $this->opentracingJager->log(__FUNCTION__,"pay->response()返回参数arr",sprintf("%s",json_encode($arr)));
            $result = 'success';
            //保证本地测试可以支付成功
            switch ($arr['notify_way']) {
                case Cpcn::PAY_WAY:
                    $res = $this->pay_success($arr);
                    if ($res['err_code'] != 0) {
                        $this->opentracingJager->log(__FUNCTION__,"pay_success返回",sprintf("%s",json_encode($res)));
                        $this->pushReportMonitorLog([
                            "interface_type"=>"6",
                            "err_msg"=>$res['err_msg'],
                            "err_code"=>$res['err_code'],
                            "remark"=>sprintf("支付,充值同步回调失败,付款成功后续操作失败 %s",json_encode($res)),

                        ]);

                        $result = 'fail';
                    }
                    break;
                default:
                    break;
            }
        } else {
            $this->opentracingJager->log(__FUNCTION__,sprintf("%s","同步支付回调失败 line 998"));
            $this->pushReportMonitorLog([
                "interface_type"=>"6",
                "err_msg"=>"支付同步回调失败",
                "err_code"=>"41076",
                "remark"=>"",

            ]);
            $result = 'fail';
        }
        $site_type = I('site_type',1);// //1PC 2H5
        $order_sn = I('order_sn') ? I('order_sn', '') : $arr['order_sn'];
        $first = substr($order_sn, 0, 1);
        $first = 1;//订单号规则变更了
        if ($first == 1) {//订单
            $types = 0;
        } elseif ($first == "2_xxxxxxxxxxxxx") {//充值 2
            $types = 1;
        } elseif ($first == "4_xxxxxxxxxxxxx") {//PCB 4
            $types = 2;
        }
        //不送抽奖机会就显示正常的
        switch(I('site_type', 1)) {
            case '1' :
                $domain = WWW_DOMAIN;
                break;
            case '2' :
                $domain = M_DOMAIN;
                break;
            default :
                exit($result);
                break;
        }
        if ($types == 1) {
            if ($domain == M_DOMAIN) {
                $url = sprintf('%s/h5/view/#/wallet', $domain);
            } else {
                $url = sprintf('%s/v3/user/moneypack.html', $domain);
            }
        } else {
            if($site_type == 2){
                $orderId = D("Order")->getOrerIdById($arr['order_sn']);
                $url = sprintf('https://m.ichunt.com/#/pay/successdraw?order_id=%s&types=%s',  $orderId, $types);
            }else{
                $url = sprintf('%s/v3/pay/%s?order_sn=%s&types=%s', $domain, $result, $arr['order_sn'], $types);
            }
            
        }
        $this->opentracingJager->finish(__FUNCTION__);
        redirect($url);
    }

    /**
     * 付款成功后续操作
     * @param  [type] $notify_way    [description]
     * @param  [type] $pay_code      [description]
     * @param  [type] $order_sn      [description]
     * @param  [type] $order_amount  [description]
     * @param  [type] $serial_number [description]
     * @param  [type] $pay_type      [description]
     * @param  [type] $pay_order_sn  [description]
     * @param  [type] $create_time   [description]
     * @return [type]                [description]
     */
    public function pay_success($call_data = array())
    {
        /**
         * @var  $order_sn
         * @var  $pay_type
         * @var  $order_amount
         * @var  $pay_order_sn
         * @var  $serial_number
         * @var  $pay_code
         * @var  $create_time
         */
        try{
            $this->apiRecord(sprintf("pay_success call_data : %s, file_get_contents: %s ",json_encode($call_data),file_get_contents("php://input")));
        }catch(\Exception $e){}

        $this->opentracingJager->init();
        $this->opentracingJager->simpleInject(__FUNCTION__);
        $this->opentracingJager->setTag(__FUNCTION__,"remark","付款成功后续操作");
        $this->opentracingJager->setTag(__FUNCTION__,"action","pay_success");
        $this->opentracingJager->log(__FUNCTION__,"call_data参数:",json_encode($call_data));

        extract($call_data);
        $payment = S('payment');
        $type = substr($order_sn, 0, 1);
        $type = 1;
        switch ($type) {
            //订单
            case 1:
                $this->opentracingJager->log(__FUNCTION__,"订单支付");
                $OrderModel = D('Order');
                $info = $OrderModel->getInfo('', $order_sn);
                $draw_res = false;
                if (empty($info)) {
                    $this->opentracingJager->log(__FUNCTION__,"41033","未找到相关订单信息");
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn(41033, '未找到相关订单信息', $order_sn);
                }
                if ( (in_array($pay_type, array(1,3)) && $info['status'] >= 4) || ($pay_type == 2 && $info['status'] == 3)) {//已付款
                    //下单成功用户添加一次抽奖资格
                    $draw_res = $this->increase_draw_qualify_by_order($info['user_id'],$info['order_id']);
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn(0, '成功',$draw_res);
                } elseif (!in_array($info['status'], array(2,3))) {//不是可支付的状态
                    D('OrderActionLog')->addLog($info['order_id'], $info['user_id'], 1, '付款失败');
                    $this->opentracingJager->log(__FUNCTION__,"41029","当前状态无法支付");
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn(41029, '当前状态无法支付', $info);
                }
                //获取当前状态应付金额
                $OrderPriceModel = D('OrderPrice');
                $topay_amount = $OrderPriceModel->getToPay($info['order_id'], $info['order_pay_type'], $info['status'], $info['advance_price']);
            //    p($topay_amount);exit;
                if (bccomp($topay_amount, $order_amount, 2) != 0) {
                    D('OrderActionLog')->addLog($info['order_id'], $info['user_id'], 1, '付款失败');
                    $this->opentracingJager->log(__FUNCTION__,"41030","当前应付金额与实际金额不相等");
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn(41030, '当前应付金额与实际金额不相等');
                }
                $OrderModel->startTrans();
                //订单状态修改
                $update_info = array(
                    'pay_type' => $pay_type,
                    'price' => $order_amount,
                    'pay_order_sn' => $pay_order_sn,
                    'serial_number' => $serial_number,
                    'pay_id' => $payment[$pay_code]['pay_id'],
                    'pay_name' => $payment[$pay_code]['pay_name'],
                );
                $res = $this->setPayOrder($info, $update_info);
                if ($res['err_code'] != 0) {
                    $OrderModel->rollback();
                    D('OrderActionLog')->addLog($info['order_id'], $info['user_id'], 1, '付款失败');
                    $this->opentracingJager->log(__FUNCTION__,$res['err_code'],$res['err_msg']);
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
                }
                $OrderModel->commit();

                //记录修改订单后的订单日志  事务之外查询订单结果
                $info = $OrderModel->getInfo($info['order_id']);
                \Think\Log::record("订单付款成功后再次查询订单结果:".json_encode($info));

                // 若支付，则需要同步到ERP
                $push_sale_data = [
                    'order_id' => $info['order_id'],
                ];

                $res = post_curl(SALE_DOMAIN.'/open/apiLastPaidPushErp', $push_sale_data);

                //邀好友下单 会写信息 先注释  其他人不要删除这行代码
                if($info["order_type_extend"] == 3){
                    //查找邀好友活动记录表 如果没记录则不需要添加任务 跑奖品
//                    if($this->getUserInviteFriend($info['order_id'])){
//                        $this->updateYaoHaoYouOrder($info['order_id']);
//                    }
                }
                //支付成功
                $this->paySuccessSendMsg($info,$order_amount);
                $this->sendPaidBlancePayMentMsg($info,$pay_type);

                // 非预付款时，推送消息给对应的业务员
                if ($pay_type != 2) {
                    $this->sendPaidRemindToSales($info);
                }
                break;

            //充值单
            case "2xxxxxxxx":
                $this->opentracingJager->log(__FUNCTION__,"充值单");
                $data = array(
                    'wallet_sn' => $order_sn,
                    'price' => $order_amount,
                    'pay_order_sn' => $pay_order_sn,
                    'serial_number' => $serial_number,
                    'pay_id' => $payment[$pay_code]['pay_id'],
                    'pay_name' => $payment[$pay_code]['pay_name'],
                    'pay_time' => $create_time,
                );
                $res = $this->setRecharge($data);
                if ($res['err_code'] != 0 && $res['err_code'] != 180015) {
                    $this->apiRecord(sprintf("钱包充值失败 %s",json_encode($res)));
                    $this->opentracingJager->log(__FUNCTION__,$res['err_code'],sprintf("钱包充值失败:%s",json_encode($res)));
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
                break;

            //PCB订单
            case "4xxxxxxxxxxxxx":
                $this->opentracingJager->log(__FUNCTION__,"pcb");
                $PcbOrderModel = D('PcbOrder');
                $PcbPayLogModel = D('PcbPayLog');
                $info = $PcbOrderModel->getInfo('', $order_sn, 'status, order_amount, user_id, order_id, order_sn');
                if (empty($info)) {
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn(41060, '未找到相关订单信息', $order_sn);
                }

                if ($info['status'] != 10) {
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn(41063, '当前状态无法支付');
                }

                if (bccomp($info['order_amount'], $order_amount, 2) != 0) {
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn(41064, '当前应付金额与实际金额不相等');
                }
                $data = array(
                    'status' => 11,
                    'pay_time' => $create_time,
                );
                $res = $PcbOrderModel->updateInfo($info['order_id'], $data);
                if ($res === false) {
//                    \LogReport::write(\LogReport::anlyError('支付失败:'.json_encode(I('request.')), __FILE__, __LINE__, 41065, __METHOD__, get_client_ip(0, true)));
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn(41065, '更新状态失败');
                }
                $data = array(
                    'user_id' => $info['user_id'],
                    'order_id' => $info['order_id'],
                    'order_sn' => $info['order_sn'],
                    'pay_order_sn' => $pay_order_sn,
                    'serial_number' => $serial_number,
                    'pay_id' => $payment[$pay_code]['pay_id'],
                    'pay_name' => $payment[$pay_code]['pay_name'],
                    'pay_amount' => $order_amount,
                    'create_time' => $create_time,
                    'pay_time' => $create_time,
                    'is_paid' => 1,
                );
                $res = $PcbPayLogModel->add($data);
                if ($res === false) {
//                    \LogReport::write(\LogReport::anlyError('支付失败:'.json_encode(I('request.')), __FILE__, __LINE__, 41066, __METHOD__, get_client_ip(0, true)));
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn(41066, '支付记录失败');
                }
                $RbmqModel = D('Common/Rbmq');
                $RbmqModel->exchange(C('EXCHANGE_NAME_PCB'))->queue(C('QUEUE_PCB_FINISH_PAY'))->exchangeBind(C('EXCHANGE_NAME_PCB'), C('QUEUE_PCB_FINISH_PAY'));
                $push = array(
                    'job' => 'pcb.pay.finish',
                    'data' => array('order_id' => $info['order_id'])
                );
                $RbmqModel->push($push, C('QUEUE_PCB_FINISH_PAY'));
                break;

            //退款充值，往退款用户账户充钱
            case 'Txxxxxxxxxxxxxx':
                $this->opentracingJager->log(__FUNCTION__,"退款充值，往退款用户账户充钱");
                // $info = explode('-', $order_sn);//获取退款订单 例如T-1201812131234-0
                // $order_sn = $info[1];
                // $OrderModel = D('Order');
                // $info = $OrderModel->getInfo('', $order_sn);
                $data = array(
                    // 'user_id' => $info['user_id'],
                    // 'order_id' => $info['order_id'],
                    'refund_sn' => $order_sn,//退款单号
                    'price' => $order_amount,
                    'pay_order_sn' => $pay_order_sn,
                    'serial_number' => $serial_number,
                    'pay_id' => $payment[$pay_code]['pay_id'],
                    'pay_name' => $payment[$pay_code]['pay_name'],
                    'pay_time' => $create_time,
                );
                $res = $this->setRefund($data);
                if ($res['err_code'] != 0) {
                    $this->opentracingJager->log(__FUNCTION__,$res['err_code'],$res['err_msg']);
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
                break;

            //运营充值，往猎芯账户充钱
            case 'Fxxxxxxxxxxxxxxxxx':
                $this->opentracingJager->log(__FUNCTION__,"运营充值，往猎芯账户充钱");
                $data = array(
                    'order_sn' => $order_sn,
                    'pay_order_sn' => $pay_order_sn,
                    'serial_number' => $serial_number,
                    'order_amount' => $order_amount,
                    'pay_time' => $create_time,
                );
                $res = $this->setOperationRecharge($data);
                if ($res['errcode'] != 0) {
                    $this->opentracingJager->log(__FUNCTION__,$res['errcode'],$res['errmsg']);
                    $this->opentracingJager->finish(__FUNCTION__);
                    return $this->apiReturn($res['errcode'], $res['errmsg']);
                }
                break;

            default:
                break;
        }
        $this->opentracingJager->finish(__FUNCTION__);
        return $this->apiReturn(0, '成功', $draw_res);
    }


    /**
     * 检查是否免单订单并跳过订单状态(联营使用，自营创建单时已处理)
     * @return [type] [description]
     */
    public function payOrder()
    {
        $order_id = I('request.order_id', 0, 'intval');
        $OrderModel = D('Order');
        $info = $OrderModel->getInfo($order_id);
        if (in_array($info['status'], array(2,3)) && $info['order_amount'] <= 0) {
            $save = array(
                'pay_type' => 1,
                'price' => 0,
                'serial_number' => '-',
                'pay_order_sn' => '',
                'pay_id' => -1,
                'pay_name' => '',
            );
            $res = $this->setPayOrder($info, $save);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }

            // 自营现卖推送wms队列
//            if ($info['order_goods_type'] == 2 && $info['sale_type'] != 2) {
//                $res = $this->makeOrder($order_id);
//                if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);
//            }

            return $this->apiReturn(0, '支付成功');
        }
        return $this->apiReturn(41045, '不符合支付条件');
    }

    /**
     * 账期付款
     * @return [type] [description]
     */
    public function billPay()
    {
        $order_id = I('request.order_id', 0, 'intval');
        $order_sn = I('request.order_sn', '', 'trim');
        $settle_period_day = I('settle_period_day', 0, 'intval');//自营，结算周期方式
        $settle_period_type = I('settle_period_type', 0, 'intval');//自营，结算周期
        $OrderModel = D('Order');
        $info = $OrderModel->getInfo($order_id, $order_sn);
        if ($info['status'] == 2 && $info['order_pay_type'] == 3) {
            $save = array(
                'pay_type' => 4,
                'price' => $info['order_amount'],
                'serial_number' => '-',
                'pay_order_sn' => '',
                'pay_id' => 0,
                'pay_name' => '账期支付',
                'settle_period_day' => $settle_period_day,
                'settle_period_type' => $settle_period_type,
            );
            $res = $this->setPayOrder($info, $save);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }

            // 自营现卖推送wms队列
//            if ($info['order_goods_type'] == 2 && $info['sale_type'] != 2) {
//                $res = $this->makeOrder($order_id);
//                if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);
//            }

            return $this->apiReturn(0, '支付成功');
        } elseif ($info['status'] > 2) {
            return $this->apiReturn(0, '订单状态为已支付，支付成功');
        }
        return $this->apiReturn(41045, '不符合支付条件');
    }

    /**
     * 设置订单已付款
     * @param array|int $order_id 订单ID 或 订单主表数据
     * @param array     $datas    pay_type,price,serial_number,pay_id,pay_name,[settle_period_day,settle_period_type]
     * @param int       $is_erp_order 是否为ERP下自营单，默认0
     */
    public function setPayOrder($order_id, $datas, $is_erp_order=0)
    {
        $this->opentracingJager->init(__FUNCTION__);
        $this->opentracingJager->simpleInject(__FUNCTION__);
        $this->opentracingJager->setTag(__FUNCTION__,"remark","设置订单已付款");
        $this->opentracingJager->log(__FUNCTION__,"请求参数",sprintf("%s,%s,%s",json_encode($order_id),json_encode($datas),$is_erp_order));


        $OrderModel        = D('Order/Order');
        $OrderItemsModel   = D('Order/OrderItems');
        $OrderInvoiceModel = D('Order/OrderInvoice');
        $OrderExtendModel  = D('Order/OrderExtend');
        $OrderPriceModel   = D('Order/OrderPrice');
        $PayLogModel       = D('Order/PayLog');

        if (is_array($order_id)) {
            $info = $order_id;
            $order_id = $info['order_id'];
        } else{
            $info = $OrderModel->getInfo($order_id);
        }
        $now = &$_SERVER['REQUEST_TIME'];
        $status = 4;
        $save['sale_pay_status'] = 2; // 新单据支付状态，全部收款
        switch ($datas['pay_type']) {
            case '2' ://预付款
                $status = 3;
                $save['advance_pay_time'] = $now;
                $save['sale_pay_status'] = 1; // 新单据支付状态，部分收款
                $price_type = -2;
                break;
            case '3' ://尾款
                // $save['pay_time'] = $now;
                $price_type = -3;
                break;
            default : //全款
                // $save['pay_time'] = $now;
                $price_type = -1;
                break;
        }
        //订单状态修改
        $save['order_id'] = $order_id;
        $save['status'] = $status;

        //自营预售推送采购 由erp推送
//        if ($info['order_goods_type'] == 2 && $info['sale_type'] == 2) {//预售
//            //当全款 或 预付首款的时候同步
//            if ($info['order_pay_type'] != 2 || ($info['order_pay_type'] == 2 && $status == 3)) {
//                $extend['pur_syn'] = 1;
//            }
//        }
        if ($info['order_pay_type'] != 3) {//账期不需要标记
            //  $save['erp_syn'] = 1; //开启队列同步标记
        }
        //杜绝重复支付问题
        $where = array(
            'order_id' => $order_id,
            'status' => array('in', array(2,3)),
        );
        $res = $OrderModel->where($where)->save($save);
        if ($res === false) {
            $this->opentracingJager->log(__FUNCTION__,"订单支付状态更变失败");
            $this->opentracingJager->finish(__FUNCTION__);
            return $this->apiReturn(41031, '订单支付状态更变失败', $save);
        } elseif ($res === 0) {
            $this->opentracingJager->finish(__FUNCTION__);
            return $this->apiReturn(0, '完成');
        }

        //自动开票开关 并且 金额大于0  转移到erp开票
//        if (C('AUTO_OPEN_INVOICE') && $info['order_goods_type'] == 2 && $datas['price'] > 0) {
//            $inv_type = $OrderInvoiceModel->getFieldByOrderId($order_id, 'inv_type');
//            if (in_array($inv_type, array(2, 4))) {//普通发票
//                $extend['fms_syn'] = 1;
//            }
//        }

        // $extend['sensors_syn'] = 3;//神策支付同步
        //记录自营账期支付的还款结算方式
        // if (!empty($datas['settle_period_type'])) {
        //     // $extend['settle_period_type'] = $datas['settle_period_type'];
        //     // $extend['settle_period_day'] = $datas['settle_period_day'];
        // }
        // //扩展表修改
        // if (!empty($extend)) {
        //     $res = $OrderExtendModel->where(array('order_id' => $order_id))->save($extend);
        //     if ($res === false) {
        //         $this->opentracingJager->log(__FUNCTION__,"修改同步标记失败");
        //         $this->opentracingJager->finish(__FUNCTION__);
        //         return $this->apiReturn(41044, '修改同步标记失败');
        //     }
        // }

        //记录支付订单金额
        $res = $OrderPriceModel->createOrderPrice($order_id, -1 * $datas['price'], $price_type, $info['currency'], $info['order_sn']);
        if ($res === false) {
            $this->opentracingJager->log(__FUNCTION__,"记录付款金额失败");
            $this->opentracingJager->finish(__FUNCTION__);
            return $this->apiReturn(41039, '记录付款金额失败');
        }

        // 修改明细已支付金额
        $order_items = $OrderItemsModel->getOrderList($order_id, '', null);
        $total_part_paid_amount = 0; // 累计已支付金额（除最后一条明细）

        foreach ($order_items as $ik => $it) {
            if ($datas['pay_type'] == 2) { // 预付款
                $pre_ratio = $datas['price'] / $info['order_amount']; // 预付比例
                $paid_amount = price_format($pre_ratio * $it['goods_number'] * $it['single_pre_price']);

                if ($ik == (count($order_items) - 1)) { // 最后一条明细的已支付金额 = 预付金额 - 其他明细累计的已支付金额
                    $paid_amount = $datas['price'] - $total_part_paid_amount;
                } else {
                    $total_part_paid_amount += $paid_amount;
                }
            } else {
                $paid_amount = price_format($it['goods_number'] * $it['single_pre_price']);
            }

            $res = $OrderItemsModel->where(['rec_id' => $it['rec_id']])->save(['paid_amount' => $paid_amount]);
            if ($res === false) {
                $this->opentracingJager->log(__FUNCTION__, "修改明细已支付金额失败");
                $this->opentracingJager->finish(__FUNCTION__);
                return $this->apiReturn(41039, '修改明细已支付金额失败');
            }
        }

        //支付记录登记
        $data = array(
            'order_id'      => $order_id,
            'order_sn'      => $info['order_sn'],
            'pay_order_sn'  => !empty($datas['pay_order_sn']) ? $datas['pay_order_sn'] : '',
            'user_id'       => $info['user_id'],
            'pay_id'        => $datas['pay_id'],
            'pay_name'      => $datas['pay_name'],
            'serial_number' => !empty($datas['serial_number']) ? $datas['serial_number'] : '',
            'pay_type'      => $datas['pay_type'],
            'pay_amount'    => $datas['price'],
            'is_paid'       => $datas['pay_type'] == 4 ? -1 : 1,
            'create_time'   => $now,
            'pay_time'      => $datas['pay_type'] == 4 ? 0 : $now,
        );
        $res = $PayLogModel->add($data);
        if ($res === false) {
            $this->opentracingJager->log(__FUNCTION__,"订单日志记录失败");
            $this->opentracingJager->finish(__FUNCTION__);
            return $this->apiReturn(41032, '订单日志记录失败', $data);
        }

//        if ($info['order_goods_type'] == 2 && $info['sale_type'] != 2) {//现卖
//            if($info["order_type_extend"] == 1){
//                //团购订单
//                $this->updateUserGroupOrderInfo($info);
//            }
//        }

        //自营现卖 团购
//        $hasZiYi = D("Order/OrderItems")->checkoutOrderByZi(["order_id"=>$order_id,"sale_type"=>1]);
//        if($hasZiYi && $info["order_type_extend"] == 1){
//            //团购订单
//            $this->updateUserGroupOrderInfo($info);
//        }


        try {
            // $user_id = S_account(C('OFFLINE_ACCOUNT')); // 自营内部采购账号

            // if (!$user_id) $user_id = D('Home/UserMain')->isHasAccount(C('OFFLINE_ACCOUNT'));

            // if ($user_id == $info['user_id']) {
            if ($is_erp_order) {
                $msg     = '自动付款成功，付款金额为：'.$datas['price'];
                $op_type = 2; // 内部用户
                $op_user = 1000; // 管理员ID
            } else {
                //下单成功后送优惠券
                $coupon_res = $this->send_coupon_res($info['user_id'],$info['order_id']);

                //推送用户支付任务 实付
                if ($datas['pay_type'] != 4 && $datas['price'] > 0) {
                    $RbmqModel = D('Common/Rbmq');
                    $RbmqModel->connect('RBMQ_MSG_CONFIG')->queue(C('MEMBER_TASK_SYSTEM_LIST'))->push(array('user_id' => $info['user_id'], 'pay_amount' => $datas['price'], 'task_type' => 3), C('MEMBER_TASK_SYSTEM_LIST'));
                }
                if (isset($datas['sale_id'])) { // 预付订单审核通过时
                    $msg = '审核时设置预付0元，默认已付首款';
                    $op_type = 2; // 内部用户
                    $op_user = $datas['sale_id'];
                } else {
                    $msg = '付款成功，付款金额为：'.$datas['price'];
                    if ($datas['pay_type'] == 4) {
                        $msg = '账期'.$msg;
                    }
                    $op_type = 1; // 前台用户
                    $op_user = $info['user_id'];

                    //下单成功用户添加一次抽奖资格
                    $draw_res = $this->increase_draw_qualify_by_order($info['user_id'],$info['order_id']);
                    if($draw_res['is_sent_qualify']===true) {
                        if(!empty($draw_res['activity_urls'])){
                            $arr_str = json_encode($draw_res['activity_urls']);
                            cookie('act_name_url',$arr_str);
                        }
                    }
                }
            }

            D('Order/OrderActionLog')->addLog($info['order_id'], $op_user, $op_type, $msg);

            // 联营单 异步转发，设置付款用户标签
            if ($info['order_goods_type'] == 1) {
                $url = C('async_url');

                $params = [];
                $params['callbackDomain'] = CRM_DOMAIN;
                $params['callbackUri']    = 'tagapi/checkUserTag';
                $params['requestType']    = 'http';
                $params['method']         = 'post';
                $params['data']           = json_encode(['id' => $info['user_id']]);
                $params['serviceType']    = 8;
                // $params['header']      = '{"api-key":"crm a1b2c3d4e5f6g7h8i9jk"}';
                $params['remark']         = '用户付款后设置标签';
                $params['is_delay']       = 1;
                $params['delay_time']     = 30;

                $client = new \GuzzleHttp\Client();
                $res = $client->request("post",$url,[
                    "form_params"=>$params,
                    "headers"=>["api-key"=>'async task qwert12345'],
                ]);
            }


        } catch (\Exception $e) {}


        $this->opentracingJager->finish(__FUNCTION__);
        return $this->apiReturn(0, '完成');
    }



    /*
        下单成功送优惠券
        @param $user_id     int 用户id
        @param $order_id    int 订单id
    */
    private function send_coupon_res($user_id,$order_id){

        $pf = platform();

        //支付后送优惠券
        $res = regIssueCoupon($user_id,$pf,$order_id);
    }

    /**
     * 支付优惠信息
     * @return [type] [description]
     */
    public function payPreferential()
    {
        //支付单据类型 0订单 1充值 2PCB
        $types = I('request.types', 0, 'intval');
        //单据ID
        $order_id = I('request.order_id', 0, 'intval');
        switch (C('PAY_ORDER_ID_TYPE.'. $types)) {
            case 'SALE_ORDER' :
                $OrderModel = D('Order');
                $info = $OrderModel->getInfo($order_id);
                //订单不区分联营自营
//                if ($info['order_goods_type'] == 2) {
//                    $mall_type = 2;
//                } else {
//                    $mall_type = 3;
//                }
                $mall_type = 3;
                $res = $this->walletActive($mall_type, 1);
//                print_r($info);
//                print_r($res);

                if ($res['errcode'] !== 0) {
                    return $this->apiReturn(41074, '无优惠活动');
                }
                if ($info["order_amount"] <  $res["data"]["require_amount"]){
                    return $this->apiReturn(41074, '无优惠活动');
                }
                break;
            default:
                return $this->apiReturn(41074, '无优惠活动');
                break;
        }
        return $this->apiReturn(0, '', $res['data']);
    }

    public function successdraw(){
       
        $order_id = I("order_id");
        $serial_number = I("s_n");
        \Think\Log::write("successdraw:".$serial_number);
        Vendor("payment.wxpay.wxpay");
        $Api = new \wxpay();
        sleep(6);
        try{
            $res = $Api->query("",$serial_number);
        }catch(\Exception $e){
            $redirectUrl ="https://m.ichunt.com/#/pay/successdraw?order_id=".$order_id;
            ob_clean(); // 清理输出缓冲区
            header("Location: {$redirectUrl}");
            exit;
        }
       
        if(!$res || $res["status"] != 1){
            echo "支付失败";exit;
            $redirectUrl ="https://m.ichunt.com/#/pay/successdraw?order_id=".$order_id;
            ob_clean(); // 清理输出缓冲区
            header("Location: {$redirectUrl}");
            exit;
        }
        $order_id_str = strval($order_id);
        $redirectUrl ="https://m.ichunt.com/#/pay/successdraw?order_id=".$order_id."&rand_str=".md5_32($order_id_str);
        ob_clean(); // 清理输出缓冲区
        header("Location: {$redirectUrl}");
        exit;

    }


}
