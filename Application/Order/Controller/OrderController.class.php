<?php

namespace Order\Controller;

use Dompdf\Adapter\CPDF;
use Home\Services\InquiryService;
use Order\Controller\BaseController;
use Order\Model\OrderModel;
use \Order\Service\AvgPriceService;
use \Order\Service\AvgPriceFJFService;
use \Order\Service\AvgPriceQuanService;
use \Order\Service\AvgPriceXKJService;
use \Order\Service\AvgPriceTuanGouService;
use \Order\Service\AvgPriceYunFeiService;
use \Order\Service\AvgPriceWalletService;
use  \Order\Exception\OrderException;
use  Order\Traits\PayTrait;
use Order\Service\OpenService;

class OrderController extends BaseController
{
    use \Order\Traits\OrderTrait;
    use \Order\Traits\SampleTrait;

    public function _initialize()
    {
        parent::_initialize();

        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), array('status', 'transstatus', 'getallorder', 'getorderdetails', 'admincheck', 'applyadjust', 'changeorder', 'deletegoods', 'test', 'ordercount', 'cancelpay', 'send', 'invshipping', 'crontabconfirmsend', 'alllist', 'contractinfo', 'sampleinfo', 'selfcancel', 'crontaborderpoint', 'updateusercreatetime', 'handleorderrefund', 'handlepaylog', 'sysiteminfo', 'checktuangouorderisok', 'lockskuaction', 'getorderactivityinfo'))) {
            // 检查登录
            if (in_array(strtolower(ACTION_NAME), array('lists', 'info', 'count', 'invoice', 'shipping', 'lastshipping'))) { //弱校验
                $verify_mode = false;
            } else { //强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    // 寄送的发票
    public function invShipping()
    {
        $order_id = I('order_id');
        $inv_shipping_no = I('inv_shipping_no'); // 物流单号
        $inv_shipping_id = I('inv_shipping_id'); // 物流id
        $operator_id = I('operator_id'); // 操作人ID

        // 订单不存在
        if (!$order_id) {
            return $this->apiReturn(44003, '该订单不存在');
        }
        $OrderModel = D('Order');
        $order_info = $OrderModel->getInfo($order_id);
        // 订单状态不是完成状态，无法寄送发票
        if ($order_info['status'] != 10) {
            return $this->apiReturn(44014, '订单状态不是完成状态，无法寄送发票');
        }
        // 当前订单不需要发票
        $OrderInvoiceModel = D('OrderInvoice');
        $OrderShippingModel = D('OrderShipping');
        $TaxinfoModel = D('Invoice/Taxinfo');
        $OrderAddressModel = D('OrderAddress');
        $order_invoice_info = $OrderInvoiceModel->getInfo($order_id);
        if ($order_invoice_info['inv_type'] == 1) {
            return $this->apiReturn(44015, '当前订单不需要发票');
        }
        if ($order_invoice_info['invoice_status'] == 2 || $order_invoice_info['invoice_status'] == 3) {
            return $this->apiReturn(44016, '当前订单发票已经寄送，无法再改');
        }
        // lie_order_invoice
        $save = [];
        $save['invoice_status'] = 2;
        $where = [];
        $where['order_id'] = $order_id;
        $OrderInvoiceModel->where($where)->data($save)->save();
        // lie_order_shipping
        $where_shipping['order_id'] = $order_id;
        $where_shipping['status'] = -1;
        $add_save['order_shipping_type'] = 1;
        $add_save['shipping_no'] = $inv_shipping_no;
        $add_save['shipping_id'] = $inv_shipping_id;
        $add_save['shipping_type'] = 2;
        $add_save['status'] = 1;
        $OrderShippingModel->where($where_shipping)->data($add_save)->save();
        $wheres_shipping['order_id'] = $order_id;
        $wheres_shipping['status'] = 1;
        $count = $OrderShippingModel->where($wheres_shipping)->count();
        if (!$count) {
            $add = [];
            $add['order_id'] = $order_id;
            $add['order_shipping_type'] = 1;
            $add['shipping_no'] = $inv_shipping_no;
            $add['shipping_id'] = $inv_shipping_id;
            $add['shipping_type'] = 2;
            $add['status'] = 1;
            $OrderShippingModel->data($add)->add();
        }

        // 添加发票地址
        if (strval($order_invoice_info['inv_type']) !== '1') { // 如果需要开发票
            // 发票地址 order_address 插入
            if ($order_invoice_info['tax_id']) {
                $tax_info = $TaxinfoModel->getUserInfo($order_info['user_id'], $order_invoice_info['tax_id']);
                $order_address_data = array(
                    'order_id' => $order_id,
                    'address_id' => $order_invoice_info['tax_id'], // 关联用户地址ID
                    'order_sn'  => $order_info['order_sn'],
                    'address_type' => 2,
                    'province' => $tax_info['consignee_province'],
                    'city' => $tax_info['consignee_city'],
                    'district' => $tax_info['consignee_district'],
                    'address' => $tax_info['consignee_address'],
                    'consignee' => $tax_info['consignee'],
                    'mobile' => $tax_info['consignee_phone'],
                    'telphone' => $tax_info['company_phone'],
                );
                $OrderAddressModel->add($order_address_data);
            }
        }


        // 操作记录
        $shipping_name = D('Shipping')->where(['shipping_id' => $inv_shipping_id])->getField('shipping_name');
        $event = '寄送发票，快递公司：' . $shipping_name . '，单号：' . $inv_shipping_no;

        D('OrderActionLog')->addLog($order_id, $operator_id, 2, $event);

        return $this->apiReturn(0, '成功');
    }

    // 确认发货
    public function send()
    {
        $order_id = I('order_id');
        $shipping_no = I('shipping_no'); // 物流单号
        $shipping_type = I('shipping_type'); // 1.订单 2.发票
        $shipping_id = I('shipping_id');
        $operator_id = I('operator_id');

        if (!$order_id) {
            return $this->apiReturn(44003, '该订单不存在');
        }
        if (!$shipping_no) {
            return $this->apiReturn(44013, '物流单号不可为空');
        }

        $OrderModel = D('Order');
        $OrderShippingModel = D('OrderShipping');
        $OrderExtendModel = D('OrderExtend');
        $OrderItemsModel = D('OrderItems');
        $OrderInvoiceModel = D('OrderInvoice');
        $OrderAddressModel = D('OrderAddress');
        $TaxinfoModel = D('Invoice/Taxinfo');
        $OrderModel->startTrans();

        $order_info = $OrderModel->getInfo($order_id);

        // 获取快递公司
        $shipping_name = D('Shipping')->where(['shipping_id' => $shipping_id])->getField('shipping_name');

        $event = '填写订单发票，快递公司：' . $shipping_name . '，单号：' . $shipping_no;

        if ($shipping_type == 1) { // 快递单
            $save = [];
            $save['status'] = 8;
            $save['shipping_time'] = time();

            $save_res = $OrderModel->where(array('order_id' => $order_id))->data($save)->save();

            if ($save_res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44031, '订单状态更新失败');
            }

            $event = '填写订单快递，快递公司：' . $shipping_name . '，单号：' . $shipping_no;

            if ($order_info['order_goods_type'] == 2) { //自营订单
                $reduce = $order_info['sale_type'] == 1 ? false : true;
                // 锁定基石库存
                $skus = $OrderItemsModel->getItemsStockMap($order_id);
                $res = $this->unlockSku($order_id, $skus, $reduce);
                if ($res['errcode'] != 0) {
                    $OrderModel->rollback();
                    return $this->apiReturn($res['errcode'], $res['errmsg']);
                }

                $event .= '，锁定库存成功！';
            }
        }

        // 更改订单表状态
        // 如果状态为-1，更改状态
        $adds = [];
        $adds['order_shipping_type'] = 1;
        $adds['shipping_no'] = $shipping_no; // 物流单号
        $adds['shipping_id'] = $shipping_id;
        $adds['shipping_type'] = $shipping_type;
        $adds['status'] = 1;
        $adds['update_time'] = time();
        $where_order['order_id'] = $order_id;
        $where_order['shipping_type'] = $shipping_type;
        $where_order['status'] = -1;
        $update = $OrderShippingModel->where($where_order)->data($adds)->save();
        if ($update === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44031, '订单物流表更新失败');
        }

        //假如本身已经在运输途中，不需再添加
        $where['order_id'] = $order_id;
        $where['shipping_type'] = $shipping_type;
        $where['status'] = 1;
        $count = $OrderShippingModel->where($where)->count();
        if (!$count) {
            // 新增物流表，该订单的物流信息
            $add = [];
            $add['order_id'] = $order_id;
            $add['order_shipping_type'] = 1;
            $add['shipping_no'] = $shipping_no; // 物流单号
            $add['shipping_id'] = $shipping_id;
            $add['shipping_type'] = $shipping_type;
            $add['status'] = 1;
            $add['update_time'] = time();
            $insert = $OrderShippingModel->add($add);
            if ($insert === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44031, '订单物流表新增失败');
            }
        }

        if ($shipping_type == 2) { // 发票确认发货时，更改发票状态
            $inv_info = $OrderInvoiceModel->getInfo($order_id, 'inv_type, tax_id');
            if (strval($inv_info['inv_type']) !== '1') { // 如果需要开发票
                // 发票状态更改
                $maps['order_id'] = array('eq', $order_id); // 开票状态-1待确认 1已开票 2已发货
                $save_invoice['invoice_status'] = 2;
                $OrderInvoiceModel->where($maps)->save($save_invoice);

                $order_address_data = array(
                    'order_id' => $order_id,
                    'address_id' => $inv_info['tax_id'], // 关联用户地址ID
                    'order_sn'  => $order_info['order_sn'],
                    'address_type' => 2,
                );
                // 发票地址 order_address 插入
                if ($inv_info['tax_id']) {
                    $tax_info = $TaxinfoModel->getUserInfo($order_info['user_id'], $inv_info['tax_id']);
                    $order_address_data['province'] = $tax_info['consignee_province'];
                    $order_address_data['city'] = $tax_info['consignee_city'];
                    $order_address_data['district'] = $tax_info['consignee_district'];
                    $order_address_data['address'] = $tax_info['consignee_address'];
                    $order_address_data['consignee'] = $tax_info['consignee'];
                    $order_address_data['mobile'] = $tax_info['consignee_phone'];
                    $order_address_data['telphone'] = $tax_info['company_phone'];
                } else {
                    //没有地址使用订单地址
                    $tax_info = $OrderAddressModel->getInfo($order_id, 1);
                    $order_address_data['province'] = $tax_info['province'];
                    $order_address_data['city'] = $tax_info['city'];
                    $order_address_data['district'] = $tax_info['district'];
                    $order_address_data['address'] = $tax_info['address'];
                    $order_address_data['consignee'] = $tax_info['consignee'];
                    $order_address_data['mobile'] = $tax_info['mobile'];
                    $order_address_data['telphone'] = $tax_info['telphone'];
                }
                $OrderAddressModel->add($order_address_data);
            }
        }
        $OrderModel->commit();

        try {
            //消息通知客户
            $order_info = $OrderModel->getInfo($order_id);
            if ($order_info['status'] == 8 && $shipping_type == 1) { // 待收货，表明已经发货
                $OrderAddressModel = D('OrderAddress');
                $order_address_info = $OrderAddressModel->getInfo($order_id); //收货信息
                $mobile = $order_address_info['mobile']; // 收货联系方式
                $consignee = $order_address_info['consignee']; // 收货人姓名
                $province_val = $order_address_info['province_val'];
                $city_val = $order_address_info['city_val'];
                $district_val = $order_address_info['district_val'];
                $address = $province_val . ' ' . $city_val . ' ' . $district_val . ' ' . $order_address_info['address']; // 收货人地址

                $shipping = $OrderShippingModel->getInfo($order_id, 1); // 订单物流信息
                switch (strval($order_info['order_goods_type'])) {
                    case '1':
                        $keyword = 'order-confirm-send';
                        break;
                    case '2':
                        $keyword = 'order-self-confirm-send';
                        break;
                    default:
                        break;
                }
                $to_user = $order_info['user_id']; // 客户
                $wechat_data = C("WECHAT_TEMPLATE.{$keyword}");
                $wechat_data['delivername']['value'] = $shipping['shipping_name']; // 快递公司
                $wechat_data['ordername']['value'] = $shipping_no; // 快递单号

                $order_res = getOrderSpecialDetailsByOrderId($order_id);
                $order_res['order_sn'] = $order_info['order_sn'];
                $order_res['create_time'] = $order_info['create_time'];
                $order_res['order_amount'] = price_format($order_info['order_amount'], C('PLACE_CURRENCY_MAPPING.' . $order_info['currency']));
                $send_data['data'] = $order_res;
                $send_data['data']['address'] = $address;
                $send_data['data']['mobile'] = $mobile;
                $send_data['data']['consignee'] = $consignee;
                $send_data['data']['order_id'] = $order_id;
                $send_data['data']['url'] = getShortUrl($order_id);
                $send_data['url'] = '';
                $send_data['wechat_data'] = $wechat_data;
                $send_data = json_encode($send_data);
                $this->sendOrderMsg($keyword, $send_data, $to_user);
            }
        } catch (Exception $e) {
        }

        // 操作记录
        D('OrderActionLog')->addLog($order_id, $operator_id, 2, $event);

        return $this->apiReturn(0, '成功');
    }

    //  订单状态
    public function orderCount()
    {
        $type = I('type', 1);
        $time_today_start = strtotime(date('Y-m-d'));
        $time_week_start = strtotime(date('Y-m-d', strtotime('-7 days')));
        $time_month_start = strtotime(date('Y-m-d', strtotime('-30 days')));
        $time_end = strtotime(date('Y-m-d', time())) - 1;
        $today_time = ['egt', $time_today_start]; // 今日
        $week_time = [
            'between',
            [
                $time_week_start,
                $time_end,
            ]
        ]; // 7日
        $month_time = [
            'between',
            [
                $time_month_start,
                $time_end,
            ]
        ]; // 30日
        $map = [
            'today' => [
                'all' => ['create_time' => $today_time], // 提交订单
                'waitCheck' => ['create_time' => $today_time, 'status' => 1], // 待审核
                'checkPass' => ['confirm_time' => $today_time, 'status' => 2], // 审核通过
                'done' => ['finish_time' => $today_time, 'status' => 10], // 已完成通过
                'cancel' => ['cancel_time' => $today_time, 'status' => -1], // 已取消
                'paidPre' => ['advance_pay_time' => $today_time, 'status' => 3], // 预付款已支付首款
                'paid' => ['pay_time' => $today_time, 'status' => 4], // 已支付
                'send' => ['shipping_time' => $today_time, 'status' => 8], // 已发货（待收货）
            ],
            'week' => [
                'all' => ['create_time' => $week_time],
                'waitCheck' => ['create_time' => $week_time, 'status' => 1],
                'checkPass' => ['confirm_time' => $week_time, 'status' => 2],
                'done' => ['finish_time' => $week_time, 'status' => 10],
                'cancel' => ['cancel_time' => $week_time, 'status' => -1],
                'paidPre' => ['advance_pay_time' => $week_time, 'status' => 3],
                'paid' => ['pay_time' => $week_time, 'status' => 4], // 已支付
                'send' => ['shipping_time' => $week_time, 'status' => 8], // 已发货（待收货）
            ],
            'month' => [
                'all' => ['create_time' => $month_time],
                'waitCheck' => ['create_time' => $month_time, 'status' => 1],
                'checkPass' => ['confirm_time' => $month_time, 'status' => 2],
                'done' => ['finish_time' => $month_time, 'status' => 10],
                'cancel' => ['cancel_time' => $month_time, 'status' => -1],
                'paidPre' => ['advance_pay_time' => $month_time, 'status' => 3],
                'paid' => ['pay_time' => $month_time, 'status' => 4], // 已支付
                'send' => ['shipping_time' => $month_time, 'status' => 8], // 已发货（待收货）
            ],
        ];

        $UserMainModel = D('UserMain'); // 订单状态统计排除测试人员订单数量
        $wheremap = array(
            'is_test' => array('eq', 1),
        );
        $userArr = $UserMainModel->field('user_id')->where($wheremap)->select();
        foreach ($userArr as $key => $value) {
            $userStr .= $value['user_id'] . ',';
        }
        $userStr = rtrim($userStr, ',');

        $count = [];
        $OrderModel = D('Order');
        foreach ($map as $time => $item) {
            foreach ($item as $key => $value) {
                $value['user_id'] = array('not in', $userStr);
                $value['order_goods_type'] = $type;
                $value['is_type'] = 0; // 真实订单
                $count[$key][$time] = $OrderModel->where($value)->count();
            }
        }
        // p($count);die;
        return $this->apiReturn(0, '成功', $count);
    }
    // 删除订单中的单个商品
    public function deleteGoods()
    {
        $rec_id      = I('rec_id'); // 订单商品id
        $reason      = I('reason');
        $operator_id = I('operator_id');

        $OrderItemsModel     = D('OrderItems');
        $OrderPriceModel     = D('OrderPrice');
        $OrderExtendModel    = D('OrderExtend');
        $OrderActionLogModel = D('OrderActionLog');
        $OrderModel          = D('Order');

        $OrderModel->startTrans();

        if (!$rec_id) return $this->apiReturn(44010, '该订单商品不存在');
        if (!$reason) return $this->apiReturn(44011, '删除订单商品的原因没有填写');

        $order_goods_info = $OrderItemsModel->where(['rec_id' => $rec_id])->find(); // 获取该订单商品对应的订单id及其信息

        if (!$order_goods_info) return $this->apiReturn(44010, '该订单商品不存在');
        if ($order_goods_info['status'] == -1) return $this->apiReturn(44012, '该订单商品已经删除了，删除失败');

        $order_id = $order_goods_info['order_id'];
        $event = '删除商品：' . $order_goods_info['goods_name'] . '，删除原因：' . $reason;

        $saveItem = [];

        // 自营解锁库存
        if ($order_goods_info['order_goods_type'] == 2 && $order_goods_info['is_lock']) {
            $unlock_items[$order_goods_info['goods_id']] = $order_goods_info['goods_number'];
            $res = $this->unlockSku($order_id, $unlock_items);

            if ($res['errcode'] != 0) return $this->apiReturn(44007, '解锁库存失败，原因：' . $res['errmsg']);

            $event .= '，已解锁库存';

            $saveItem['is_lock'] = 0; // 标记解锁
        }

        $saveItem['status'] = -1; // 软删除
        $res = $OrderItemsModel->where(['rec_id' => $rec_id])->data($saveItem)->save();

        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44100, '删除商品失败');
        }

        // 重新获取总货款
        $goods_data = $OrderItemsModel->getOrderList($order_id, '', null);
        $goods_total_price = '';
        foreach ($goods_data as $key => $value) {
            $goods_total_price += $value['goods_amount'];
        }

        // 更改订单价格表中的货款金额
        $order_price_where = [];
        $order_price_where['price_type'] = 1;
        $order_price_where['order_id']   = $order_id;
        $order_save = [];
        $order_save['price'] = floatval($goods_total_price);
        $res = $OrderPriceModel->where($order_price_where)->data($order_save)->save();

        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44101, '更新货款金额失败');
        }

        $order_info = $OrderModel->getInfo($order_id);

        /* 临时新客价优惠方案：涉及删除或修改数量的都删除新客价优惠 2020-3-13 */
        $new_client_price = $OrderPriceModel->getNewClientPrice($order_id); // 新客价优惠

        if ($new_client_price) {
            $res = $OrderPriceModel->where(['order_id' => $order_id, 'price_type' => -8])->delete();

            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44102, '删除新客价优惠失败');
            }

            switch ($order_info['order_type_extend']) {
                case 1:
                    $msg_text = '团购';
                    break;
                case 2:
                    $msg_text = '秒杀';
                    break;
                case 3:
                    $msg_text = '邀好友订单';
                    break;
                default:
                    $msg_text = '新客价';
                    break;
            }

            $event .= '，删除' . $msg_text . '优惠（' . $new_client_price . '）';
        }

        // 更改订单总价
        $total_order_price = $OrderPriceModel->getOrderTotalPrice($order_id); // 先查总订单金额

        $save_data = [];
        $save_data['order_amount'] = $total_order_price;
        $res = $OrderModel->where(array('order_id' => $order_id))->data($save_data)->save();

        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44102, '更新订单总额失败');
        }

        // 调整明细均摊价格
        $orderItemsList    = $OrderItemsModel->getOrderList($order_id, '', null); // 明细列表
        $extra_fee         = $OrderPriceModel->getExtPrice($order_id); // 附加费
        $activity_price    = $OrderPriceModel->getActivityPrice($order_id); // 活动优惠
        $coupon_price      = $OrderPriceModel->getPreferentialPrice($order_id); // 优惠券
        $change_extend_fee = $OrderExtendModel->where(array('order_id' => $order_id))->getField('extend_fee_items');
        $shipping_fee      = $OrderPriceModel->getShippingPrice($order_id); // 运费

        $avg_need_amount = [
            'order_amount'   => $total_order_price,
            'coupon_price'   => $coupon_price,
            'activity_price' => $activity_price,
            'extend_fee'     => $extra_fee,
            'shipping_price' => $shipping_fee,
        ];

        $this->countItemsPrice($order_id, $avg_need_amount, $orderItemsList, json_decode($change_extend_fee, true));

        $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event); // 操作记录

        $OrderModel->commit();

        return $this->apiReturn(0, '删除成功');
        // 二期============================
    }

    public function test()
    {
        // 调整删除商品后的均摊价格
        // $OrderItemsModel = D('OrderItems');
        // $OrderPriceModel = D('OrderPrice');
        // $OrderExtendModel = D('OrderExtend');
        // $OrderModel = D('Order');

        // $order_id = 243398;
        // $total_order_price = $OrderModel->where(array('order_id' => $order_id))->getField('order_amount');
        // $goods_total_price = $OrderPriceModel->getGoodsPrice($order_id);
        // $orderItemsList = $OrderItemsModel->getOrderList($order_id, '', null); // 明细列表
        // $extra_fee = $OrderPriceModel->getExtPrice($order_id); // 附加费
        // $change_extend_fee = $OrderExtendModel->where(array('order_id' => $order_id))->getField('extend_fee_items');
        // $del_extend_fee = $OrderItemsModel->where(array('order_id' => $order_id, 'status' => -1))->sum('extend_price');
        // $this->countItemsPrice($order_id, $total_order_price, $goods_total_price, $orderItemsList, $extra_fee, json_decode($change_extend_fee, true), $del_extend_fee);

        /*$change_order_id = F('change_order_id');
        p($change_order_id);
        $extra_fee = F('extra_fee');
        p($extra_fee);
        $goods_amount = F('goods_amount');
        p($goods_amount);
        $deposit_amount = F('deposit_amount');
        p($deposit_amount);
        $change_info = F('change_info');
        p($change_info);
        $order_amount = F('order_amount');
        p($order_amount);die;*/
    }

    // 临时保存审单信息接口
    public function tempSaveOrder()
    {
        $order_id = I('request.order_id', 0);
        $operator_id = I('request.operator_id', 0);

        if (!$order_id) return $this->apiReturn(44110, '参数缺失');

        $data = I('request.');

        $OrderExtendModel = D('OrderExtend');
        $OrderActionLogModel = D('OrderActionLog');

        $res = $OrderExtendModel->where(['order_id' => $order_id])->save(['temp_save_info' => json_encode($data)]);

        if ($res === false) return $this->apiReturn(44112, '临时保存审单信息失败');

        $OrderActionLogModel->addLog($order_id, $operator_id, 2, '临时保存审单信息'); // 操作记录

        return $this->apiReturn(0, '成功');
    }

    // 联营新增商品
    public function jointAddGoods()
    {
        $goods_id    = I('request.goods_id');
        $goods_num   = I('request.goods_num');
        $order_id    = I('request.order_id');
        $is_gift     = I('request.is_gift', -1);
        $is_vacuo     = I('request.is_vacuo', -1); //是否真空包装
        $is_purchase = I('request.is_purchase', 1);

        if (!$goods_id || !$order_id) return $this->apiReturn(44110, '参数缺失');

        $OrderItemsModel = D('OrderItems');

        // 检查新增商品是否存在
        $exists = $OrderItemsModel->where(['order_id' => $order_id, 'goods_id' => $goods_id, 'status' => 1])->getField('rec_id');

        if ($exists) return $this->apiReturn(44111, '新增商品已存在');

        //获取商品
        $res = $this->getGoodsInfo($goods_id, $goods_num, $order_id);

        if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

        $new_item = $res['data'];

        if ($new_item['order_goods_type'] == 1) { // 专营商品根据供应商获取采购员
            $SupplierModel = D('Supplier');
            $IntracodeModel = D('Cms/Intracode');

            $supplier = $SupplierModel->getSupplierInfo('', $new_item['supplier_name']);

            if ($supplier['purchase_uid']) {
                $encode = $supplier['purchase_uid'];
            } else {
                $encode = $new_item["encoded"];
            }

            if (!empty($encode)) {
                $intracode = $IntracodeModel->getIntracodeInfo($encode);
                $new_item['buyer_id'] = !empty($intracode) ? $intracode['admin_id'] : 0;
            }
        }

        $new_item["sale_type"] = intval($new_item['goods_number']) != 0 ? 1 : 2; //销售类型 1现卖 2预售
        $new_item['is_gift'] = $is_gift;
        $new_item['is_purchase'] = $is_purchase;
        $new_item['is_vacuo'] = $is_vacuo;

        if (strlen($goods_id) != 19) { // 自营商品，默认采购员为平台
            $new_item['buyer_id'] = C('CREATE_ORDER_BUYER_ID');
        }

        $order_items = $OrderItemsModel->getOrderList($order_id, '', null);
        $order_items[] = $new_item;

        $arr = []; // 暂时无用
        $extend_fee_items = A('Order/Cart')->getItemsGroupFee($order_items, $arr, true);

        $new_item['raw_goods_sn'] = '';
        $new_item['raw_goods_packing'] = '';
        $new_item['raw_brand_name'] = '';

        if (strtolower($new_item['supplier_name']) == 'digikey') {
            $spuRedis = spu_redis_init();
            $dgk_res = $spuRedis->hget("sku_raw_map", $goods_id);

            if ($dgk_res) {
                $dgk_info = json_decode($dgk_res, true);
                $new_item['raw_goods_sn']      = $dgk_info['raw_goods_id'];
                $new_item['raw_goods_packing'] = $dgk_info['pack'];
                $new_item['raw_brand_name']    = $dgk_info['raw_brand_name'];
            }
        }

        $data['item']             = $new_item;
        $data['extend_fee_items'] = $extend_fee_items;

        return $this->apiReturn(0, '成功', json_encode($data));
    }

    // 获取商品信息---返回明细字段
    public function getGoodsInfo($goods_id, $goods_num, $order_id)
    {
        $OrderModel = D('Order');

        $order_info = $OrderModel->getInfo($order_id);

        $use_ac = $this->isCanAc($order_info['user_id']);
        $res = $this->getFinalGoods($goods_id, $goods_num, $order_info['currency'], $use_ac);

        if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

        $goods = $res['data']['goods_info'];
        $goods_prices = ladder_final_price($res['data'], $order_info['currency'], $goods['ac_type']);
        $initial_prices = ladder_final_price($res['data'], $order_info['currency'], false);

        // 交货时间
        $delivery_time = is_array($goods['delivery_time']) ? $goods['delivery_time'][$order_info['delivery_place']] : $goods['delivery_time'];
        $delivery_time = !empty($delivery_time) ? $delivery_time : '';

        $new_item = array(
            'order_id'            => $order_id,
            'user_id'             => $order_info['user_id'],
            'goods_id'            => $goods['goods_id'],
            'brand_id'            => intval($goods['brand_id']),
            'brand_name'          => $goods['brand_name'],
            'supplier_id'         => $goods['supplier_id'],
            'supplier_name'       => $goods['supplier_name'],
            'goods_sn'            => $goods['goods_sn'],
            'goods_name'          => $goods['goods_name'],
            'sku_name'            => $goods['sku_name'],
            'goods_number'        => $goods_num,
            'goods_price'         => $goods_prices['price'],
            'initial_price'       => $initial_prices['price'],
            'order_source'        => A('Order/Cart')->order_source(),
            'delivery_place'      => $order_info['delivery_place'],
            'delivery_time'       => $delivery_time,
            'goods_type'          => $goods['goods_type'],
            'bom_id'              => !empty($goods['bom_id']) ? $goods['bom_id'] : 0,
            'self_supplier_type'  => isset($goods['self_supplier_type']) ? $goods['self_supplier_type'] : 0, //1是自采， 2是立创
            'goods_moq'           => $goods['min_buy'],
            'goods_spq'           => $goods['min_mpq'],
            'goods_encap'         => !empty($goods['encap']) ? $goods['encap'] : '',
            'goods_class'         => !empty($goods['class2_name']) ? $goods['class2_name'] : '',
            'goods_packing'       => !empty($goods['packing_name']) ? $goods['packing_name'] : '',
            'ac_type'             => $goods['ac_type'],
            'canal'               => $goods['canal'],
            'standard_brand_id'   => !empty($goods['standard_brand_id']) ? $goods['standard_brand_id'] : 0,
            'standard_brand_name' => !empty($goods['standard_brand_name']) ? $goods['standard_brand_name'] : '',
            // 'goods_type'          => $goods['goods_type'],
            'order_goods_type'    => in_array($goods['goods_type'], [3, 4]) ? 2 : 1,
            'encoded'             => $goods['encoded'],
        );

        $order_goods_type = C('GOODS_ORDER_TYPE_MAP.' . $goods['goods_type']);
        $new_item["order_goods_type"] = $order_goods_type; //商品类型1联营 2自营

        if ($goods['source'] == 12) { // 寄售
            $new_item['goods_type'] = 4;
        }

        return $this->apiReturn(0, '', $new_item);
    }

    // 申请主管审核
    public function applyManagerAudit()
    {
        $order_id = I('order_id', 0);
        $reason = I('reason', '');
        $not_enough_quote_items = I('not_enough_quote_items', '');
        $controlled_goods = I('controlled_goods', '');
        $operator_id = I('operator_id', 0); // CMS操作人ID
        $is_over_credit = I('is_over_credit', '');

        if (!$order_id) return $this->apiReturn(44110, '参数缺失');

        $OrderModel          = D('Order');
        $OrderItemsModel     = D('OrderItems');
        $OrderItemsExtModel  = D('OrderItemsExt');
        $OrderExtendModel    = D('OrderExtend');
        $OrderActionLogModel = D('OrderActionLog');

        $order_info = $OrderModel->getInfo($order_id);
        $order_extend = $OrderExtendModel->getInfo($order_id);

        if ($order_info['status'] == -1) return $this->apiReturn(44113, '订单已取消');
        if ($order_info['status'] > 4) return $this->apiReturn(44114, '审核失败，订单已发货');
        if ($order_extend['is_manager_audit'] > 1) return $this->apiReturn(44116, '已申请主管审核，请刷新页面');

        $order_items = $OrderItemsModel->getOrderList($order_id, '', null);

        foreach ($order_items as $v) {
            if ($v['status'] == -1) continue;

            $order_items_ext = $OrderItemsExtModel->where(['rec_id' => $v['rec_id']])->find();

            if (empty($order_items_ext['batch'])) return $this->apiReturn(44115, '商品：' . $v['goods_name'] . '缺少批次');
            if ($v['is_purchase'] == 1 && empty($v['buyer_id'])) return $this->apiReturn(44115, '商品：' . $v['goods_name'] . '缺少采购人员');
        }

        $extend = [];
        $extend['is_manager_audit'] = 2; // 待主管审核

        $OrderExtendModel->where(['order_id' => $order_id])->save($extend);

        if ($is_over_credit && $is_over_credit != '否') {
            $log = '提交' . $is_over_credit . '订单，申请主管审核';
        } else {
            $log = '申请主管审核';
        }

        if ($not_enough_quote_items) {
            $log .= '，强制提交原因： ' . $reason . '。';

            foreach ($not_enough_quote_items as $val) {
                $log .= '明细行ID：' . $val['rec_id'] . '，商品型号：' . $val['goods_name'] . '；';
            }

            $log = trim($log, '；');
            $log .= '，未完成3条以上（包含3条）采购报价';
        }

        if (!empty($controlled_goods)) { // 存在管控物料提交时
            $log .= '，提交的订单里存在【管控物料】商品型号：';

            foreach ($controlled_goods as $c) {
                $log .=  $c['goods_name'] . '、';
            }

            $log = trim($log, '、');
            $log .= '，此订单需要经过CEO梁总审核';
        }

        $OrderActionLogModel->addLog($order_id, $operator_id, 2, $log); // 操作记录

        // 获取客服主管
        $CmsModel = D('Cms');
        $mgr_id = $CmsModel->getKefuMgrId($order_info['sale_id']);
        $web_mgr_id = 0;

        if ($mgr_id) {
            $web_mgr_id = $CmsModel->getUserWebUserId($mgr_id); // 主管对应的前台用户ID
        }

        // 主管前台ID存在
        if ($web_mgr_id) {
            $data['data']['sale_name'] = $CmsModel->getUserName($order_info['sale_id']);
            $data['data']['order_sn']  = $order_info['order_sn'];

            if (strpos(API_DOMAIN, 'liexin') !== false) {
                $order_url = 'order.liexin.net/details/' . $order_id;
            } else if (strpos(API_DOMAIN, 'sz') !== false) {
                $order_url = 'szorder.ichunt.net/details/' . $order_id;
            } else {
                $order_url = 'order.ichunt.net/details/' . $order_id;
            }

            $data['data']['order_url'] = $order_url;
            $this->sendOrderMsg('order_apply_mgr_check', json_encode($data), $web_mgr_id, true); // 推送钉钉通知
        }

        return $this->apiReturn(0, '成功');
    }

    // 主管审核
    public function managerAudit()
    {
        $order_id    = I('order_id', 0);
        $remark      = I('remark', ''); // 主管备注信息
        $operator_id = I('operator_id', 0); // CMS操作人ID

        if (!$order_id) return $this->apiReturn(44110, '参数缺失');

        $OrderModel          = D('Order');
        $OrderExtendModel    = D('OrderExtend');
        $OrderActionLogModel = D('OrderActionLog');

        $order_info = $OrderModel->getInfo($order_id);
        $order_extend = $OrderExtendModel->getInfo($order_id);

        if ($order_info['status'] == -1) return $this->apiReturn(44113, '订单已取消');
        // if ($order_info['status'] > 2) return $this->apiReturn(44114, '审核失败，订单已付款');
        if ($order_extend['is_manager_audit'] == 3) return $this->apiReturn(44117, '主管已审核，请刷新页面');

        $OrderModel->startTrans();

        // 标记待付款，ERP同步字段、审核通过时间
        $save = [];
        // $save['status']       = 2;
        $save['confirm_time'] = time();
        // $save['erp_syn'] = 1;
        $save['sale_order_status'] = 3; // 销售单变更为已审核状态

        $OrderModel->where(['order_id' => $order_id])->save($save);

        // 写入主管ID
        $extend = [];
        $extend['is_manager_audit'] = 3;
        $extend['manager_id']       = $operator_id;

        $OrderExtendModel->where(['order_id' => $order_id])->save($extend);

        if ($remark) {
            $msg_log = '超期超额主管特批审核通过，备注：' . $remark;
        } else {
            $msg_log = '主管审核通过';
        }

        $OrderActionLogModel->addLog($order_id, $operator_id, 2, $msg_log); // 操作记录

        $OrderModel->commit();

        $CmsModel = D('Cms');
        $web_kefu_id = $CmsModel->getUserWebUserId($order_info['sale_id']); // 客服对应的前台用户ID

        // 客服前台ID存在
        if ($web_kefu_id) {
            $user_info    = S_user($order_info['user_id']);
            $company_info = S_company($order_info['user_id']);
            $account      = $user_info['mobile'] ? $user_info['mobile'] : $user_info['email'];

            $data['data']['com_name'] = $company_info['com_name'] ? $company_info['com_name'] : $account;
            $data['data']['order_sn'] = $order_info['order_sn'];

            if (strpos(API_DOMAIN, 'liexin') !== false) {
                $order_url = 'order.liexin.net/details/' . $order_id;
            } else if (strpos(API_DOMAIN, 'sz') !== false) {
                $order_url = 'szorder.ichunt.net/details/' . $order_id;
            } else {
                $order_url = 'order.ichunt.net/details/' . $order_id;
            }

            $data['data']['order_url'] = $order_url;

            $this->sendOrderMsg('order_mgr_check', json_encode($data), $web_kefu_id, true); // 推送钉钉通知
        }

        return $this->apiReturn(0, '成功');
    }

    // 反审---判断ERP是否允许delete，若能删除，则可以反审
    public function reAudit()
    {
        $order_id    = I('order_id', 0);
        $operator_id = I('operator_id', 0); // CMS操作人ID

        if (!$order_id) return $this->apiReturn(44110, '参数缺失');

        $OrderModel          = D('Order');
        $OrderItemsModel     = D('OrderItems');
        $OrderExtendModel    = D('OrderExtend');
        $PayLogModel         = D('PayLog');
        $OrderActionLogModel = D('OrderActionLog');

        $order_info = $OrderModel->getInfo($order_id);
        if ($order_info['status'] > 2) return $this->apiReturn(44114, '反审失败，订单已付款');

        // $delErpOrder['NUMBER'] = $order_info['order_sn'];
        // $res = A('Server/Consume')->pushDeleteOrderStatus($delErpOrder);

        // if ($res === false) return $this->apiReturn(44115, 'ERP已生成下游单据，无法反审，请到ERP查看');

        $order_extend = $OrderExtendModel->getInfo($order_id);

        $event = '反审通过';

        // 如果已同步到ERP，则需删除ERP订单
        $order_items = $OrderItemsModel->getOrderList($order_id, '', null);

        // 联营订单，已同步到ERP，非测试环境下执行
        if ($order_info['erp_order_id'] && strpos($_SERVER['HTTP_REFERER'], 'sz') === false) {
            $erp_params = [];
            $erp_params['TYPE']    = 1;
            $erp_params['ORDERID'] = $order_info['erp_order_id'];
            $erp_params['NUMBER']  = $order_info['order_sn'];

            // 删除采购需求单
            foreach ($order_items as $v) {
                if ($v['order_goods_type'] == 2 || $v['is_purchase'] == -1) continue; // 过滤自营商品或不需采购的

                $rec_ids[] = $v['rec_id'];
            }

            if (!empty($rec_ids)) {
                $push_pur_data = [
                    'type' => 1, // 1反审核删 10取消关闭
                    'rec_ids' => $rec_ids,
                    'erp_params' => $erp_params,
                ];

                $res = post_curl(PURCHASE_DOMAIN . '/sync/frq/delFrq', $push_pur_data);
                $res = json_decode($res, true);

                if (!$res) return $this->apiReturn(44104, '删除采购需求单失败，请查看接口');
                if ($res['code'] != 0) return $this->apiReturn($res['code'], $res['msg']);

                $event .= '，已删除ERP订单，已删除采购需求单';
            } else {
                $res = A('Server/Consume')->pushDeleteOrder($erp_params); // 删除ERP上订单

                if ($res === false) return $this->apiReturn(44103, 'ERP删除订单失败');

                $event .= '，已删除ERP订单';
            }

            $unlock_items = [];
            $unlock_goods = [];

            foreach ($order_items as $k => $v) {
                if ($v['order_goods_type'] == 1) continue;

                $unlock_items[$v['goods_id']] = $v['goods_number'];

                $unlock_goods[$k]['goods_name']   = $v['goods_name'];
                $unlock_goods[$k]['goods_number'] = $v['goods_number'];
            }

            // 自营解锁库存
            if (!empty($unlock_items)) {
                $order_extend = $OrderExtendModel->getInfo($order_id);

                $reduce_wms = $order_extend['wms_order_last_time'] > 0 ? true : false; // 若大于0，则自营商品已同步到WMS，需删除WMS订单

                $res = $this->unlockSku($order_id, $unlock_items, false, $reduce_wms);

                if (!$res || $res['errcode'] != 0) return $this->apiReturn(44007, '解锁库存失败 ' . $res['errmsg']);

                $event .= '，已解锁库存的型号：';

                foreach ($unlock_goods as $v) {
                    $event .= '[' . $v['goods_name'] . '；' . $v['goods_number'] . '] ';
                }
            }
        }

        $OrderModel->startTrans();

        // 账期付款需删除掉支付记录
        if ($order_info['order_pay_type'] == 3) {
            $PayLogModel->where(['order_id' => $order_id])->delete();
        }

        // 若能反审，标记订单状态为待审核，ERP同步字段标记为-1，清空审核时间
        $save = [];
        $save['erp_order_id']      = '';
        $save['order_pay_type']    = 1;
        $save['status']            = 1;
        $save['erp_syn']           = -1;
        $save['confirm_time']      = '';
        $save['sale_order_status'] = 1; // 销售单变更为保存状态

        $OrderModel->where(['order_id' => $order_id])->save($save);

        $event .= '，状态已更新为待审核';

        // 清空ERP订单ID、单号
        $OrderItemsModel->where(['order_id' => $order_id])->save(['erp_rec_id' => '']);

        $extend = [];
        $extend['erp_sn']           = '';
        $extend['is_manager_audit'] = 0;
        $extend['manager_id']       = 0;

        $OrderExtendModel->where(['order_id' => $order_id])->save($extend);

        $OrderModel->commit();

        $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event); // 操作记录

        return $this->apiReturn(0, '成功');
    }

    // 调价
    public function changeOrder()
    {

        $user_id                 = I('user_id'); // 用户id
        $order_id                = I('order_id', 0, 'intval'); // 订单id
        $status                  = I('status'); // 订单状态
        $sale_id                 = I('sale_id'); // 业务员id
        $extra_fee               = I('extra_fee'); // 附加费
        $change_info             = I('change_info'); // 更改的商品数量和价格数组
        $change_extend_fee       = I('change_extend_fee'); // 供应商附加费数组
        $goods_amount            = I('goods_amount'); // 货款
        $deposit_amount          = I('deposit_amount'); // 预付款
        $order_pay_type          = I('order_pay_type'); // 付款类型（1-全款、2-预付款、3-账期）
        $pay_time_limit          = I('pay_time_limit'); // 付款时间限制
        $operator_id             = I('operator_id'); // CMS操作人ID
        $client_source           = I('client_source'); // 用户来源
        $check_failed            = I('check_failed'); // 检查调价是否失败
        $check_failed_info       = I('check_failed_info'); // 调价信息
        $change_pay_type         = I('change_pay_type'); // 设置预付款
        $kefu_remark             = I('kefu_remark'); // 客服备注
        $freight_fee             = I('freight_fee'); // 运费
        $customer_id             = I('customer_id');
        $customer_cn             = I('customer_cn');
        $customer_en             = I('customer_en');
        $customer_type           = I('customer_type');
        $product_use_classone_sn = I('product_use_classone_sn');
        $product_use_classtwo_sn = I('product_use_classtwo_sn');
        $customer_website        = I('customer_website');
        $zy_delivery_type        = I('zy_delivery_type');
        $event                   = '订单审核通过'; // 日志记录

        $OrderPriceModel     = D('OrderPrice');
        $OrderModel          = D('Order');
        $OrderItemsModel     = D('OrderItems');
        $OrderItemsExtModel  = D('OrderItemsExt');
        $OrderExtendModel    = D('OrderExtend');
        $UserMainModel       = D('UserMain');
        $OrderInvoiceModel   = D('OrderInvoice');
        $OrderActionLogModel = D('OrderActionLog');
        $OrderExtraModel     = D('OrderExtra');

        $order_info   = $OrderModel->getInfo($order_id);
        $order_extend = $OrderExtendModel->getInfo($order_id);

        if ($order_info['status'] == -1) return $this->apiReturn(44017, '订单已取消');
        if ($order_info['status'] > 2) return $this->apiReturn(44017, '订单已支付');
        if ($order_extend['is_manager_audit'] == 3) return $this->apiReturn(44017, '主管已审核通过，如需审核请联系主管反审');

        // 联营待审核时校验客服
        if ($order_info['order_goods_type'] == 1 && $status == 1) {
            if ($sale_id != $operator_id) {
                $CmsModel = D('Cms');
                $sale_name = $CmsModel->getUserName($sale_id);
                return $this->apiReturn(44017, '审核失败，订单已推送给' . $sale_name);
            }
        }

        // 自营 或 10楼库存商品需要到中间表查找关联的IC编码，若不存在则阻止审单
        $res = $this->checkOrderSpotGoods($change_info);
        if ($res !== true) {
            return $this->apiReturn("21029", $res);
        }

        $OrderModel->startTrans();

        // 终端客户信息
        if ($customer_cn || $customer_en || $customer_type || $product_use_classone_sn || $product_use_classtwo_sn) {
            $customer_info['customer_cn']             = $customer_cn;
            $customer_info['customer_en']             = $customer_en;
            $customer_info['customer_type']           = $customer_type;
            $customer_info['product_use_classone_sn'] = $product_use_classone_sn;
            $customer_info['product_use_classtwo_sn'] = $product_use_classtwo_sn;
            $customer_info['customer_website']        = $customer_website;

            if ($customer_id) { // 更新
                $djkconfigerp = C("DJKCONFIGERP");

                $customer_info['product_use_classone_erp_sn'] = $djkconfigerp['class_one'][$product_use_classone_sn];
                $customer_info['product_use_classtwo_erp_sn'] = $djkconfigerp['class_two'][$product_use_classone_sn][$product_use_classtwo_sn];

                $res = $OrderExtraModel->where(['id' => $customer_id])->save($customer_info);
                $action_txt = '更新';
            } else {
                $res = $this->addOrderDJKInfo(1, $customer_info, $order_id);
                $action_txt = '新增';
            }

            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44022, $action_txt . '终端客户信息失败');
            }

            $event .= '，' . $action_txt . '终端客户信息';

            // 更新会员公司管理表
            // $InvoiceCompanyModel = D('Invoice/InvoiceCompany');

            $djkInfo = array(
                'customer_en'             => $customer_info['customer_en'],
                'customer_type'           => $customer_info['customer_type'],
                'product_use_classone_sn' => $customer_info['product_use_classone_sn'],
                'product_use_classtwo_sn' => $customer_info['product_use_classtwo_sn'],
                'customer_website'        => $customer_info['customer_website'],
            );

            // $res = $InvoiceCompanyModel->where(['com_name' => $customer_info['customer_cn']])->save($djkInfo);

            $CompanyTerminalInfoModel = D('Crm/CompanyTerminalInfo');

            $exists = $CompanyTerminalInfoModel->where(['com_name' => I('customer_cn')])->find();

            if ($exists) {
                $djkInfo['update_time'] = time();

                $res = $CompanyTerminalInfoModel->where(['com_name' => I('customer_cn')])->save($djkInfo);
            } else {
                $djkInfo['create_time'] = time();
                $djkInfo['update_time'] = time();

                $res = $CompanyTerminalInfoModel->add($djkInfo);
            }

            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(25029, '会员公司管理信息更新失败');
            }
        }

        // 自营订单且已预分配 审核时取消WMS预分配 --- ********
        // if ($order_info['order_goods_type'] == 2 && $order_extend['wms_order'] == 1) {
        //     $cancel_wms_order = $this->cancelOrder($order_id);

        //     if (!empty($cancel_wms_order) && $cancel_wms_order['entity'][0]['Status'] = 1) {
        //         $cancel = $OrderExtendModel->where(['order_id' => $order_id])->data(['wms_order' => -1])->save();

        //         if ($cancel === false) {
        //             $OrderModel->rollback();
        //             return $this->apiReturn(44018, '取消WMS预分配更新wms_order失败');
        //         }

        //         $OrderActionLogModel->addLog($order_id, $operator_id, 2, '自营订单审核时取消WMS预分配成功');
        //     }
        // }

        // 判断是否为账期订单  20210706更新 ---  发票抬头 > 订单合同公司名称 > 手机号码
        if ($order_pay_type == 3) {
            // if ($order_info['order_goods_type'] == 1) { // 联营
            // $orderInvoice = $OrderInvoiceModel->getInfo($order_id);
            // $param['CURRENCY'] = $order_info['currency'] == 1 ? '人民币' : '美元';
            // $param['CUSTOMER'] = '';

            // if ($orderInvoice['tax_title']) {
            //     $param['CUSTOMER'] = stripslashes($orderInvoice['tax_title']);
            // } else {
            //     $userCompanyInfo = S_company($user_id);

            //     if ($userCompanyInfo['com_name']) {
            //         $param['CUSTOMER'] = stripslashes($userCompanyInfo['com_name']);
            //     } else {
            //         $userInfo = S_user($user_id);

            //         if ($userInfo['mobile']) {
            //             $param['CUSTOMER'] = $userInfo['mobile'];
            //         }
            //     }
            // }

            /* 暂时注释 2021-7-7 */
            // if ($orderInvoice['tax_title']) {
            //     $param['CUSTOMER'] = stripslashes($orderInvoice['tax_title']);
            // } else if ($order_extend['contract_com_name']) {
            //     $param['CUSTOMER'] = stripslashes($order_extend['contract_com_name']);
            // } else {
            //     $userInfo = S_user($user_id);

            //     if ($userInfo['mobile']) {
            //         $param['CUSTOMER'] = $userInfo['mobile'];
            //     }
            // }

            // if (strpos($_SERVER['HTTP_REFERER'], 'sz') === false) { // 测试站不请求下面ERP接口
            //     $res = A('Server/Consume')->isAccountOrder($param);

            //     if ($res !== true) return $this->apiReturn(44018, '无法设置账期订单，原因：'.$res);

            //     $param = [];
            //     $param['TYPE']    = 1;
            //     $param['ORDERID'] = $order_info['erp_order_id'];
            //     $param['NUMBER']  = $order_info['order_sn'];

            //     A('Server/Consume')->pushDeleteOrder($param); // 删除ERP订单
            // }
            // } else { // 自营  --- ********
            // $res = json_decode(post_curl(CREDIT_DOMAIN.'/credit/surplus', ['user_id'=>$user_id]), true); // 获取会员账期额度

            // if (!$res) return $this->apiReturn(44101, '会员账期额度接口异常');
            // if (!$res['data']) return $this->apiReturn(44102, '无法设置账期订单，请检查客户额度是否存在，去<a href="'.FINANCE_DOMAIN.'/web/userCreditList" target="_blank">&nbsp;财务系统&nbsp;</a>看看吧');

            // $credit_amount = $res['data']; // 会员账期额度
            // }

            // $status = 4; // 账期订单设置为待发货状态
            $event .= '，设置为账期订单';
        } else if ($order_pay_type == 2) {
            $event .= '，设置为预付款';
        } else if ($order_pay_type == 4) {
            $event .= '，设置为货到猎芯付款';
        }

        // 判断商品单价调价后是否下降20%及以下
        if ($change_pay_type || $check_failed) {
            // 调价失败，保存信息到订单表临时字段
            $temp_info['order_id']         = $order_id;
            $temp_info['sale_id']          = $sale_id;
            $temp_info['extra_fee']        = floatval($extra_fee);
            $temp_info['freight_fee']      = floatval($freight_fee);
            $temp_info['change_info']      = json_encode($change_info);
            $temp_info['goods_amount']     = floatval($goods_amount);
            $temp_info['deposit_amount']   = floatval($deposit_amount);
            $temp_info['order_pay_type']   = $order_pay_type;
            $temp_info['pay_time_limit']   = $pay_time_limit;
            $temp_info['status']           = 1; // 待审核
            $temp_info['extend_fee_items'] = json_encode($change_extend_fee); // 回写供应商附加费
            $temp_info['kefu_remark']      = $kefu_remark;

            if ($order_extend) {
                $extend = $OrderExtendModel->where(array('temp_id' => $order_extend['temp_id']))->data($temp_info)->save();
            } else {
                $temp_info['create_time'] = time();

                $extend = $OrderExtendModel->data($temp_info)->add();
            }

            if ($extend === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44018, '更新订单临时信息失败');
            }

            // 用户来源
            if ($client_source) {
                $user_info['client_source'] = $client_source;
                $userMain = $UserMainModel->where(array('user_id' => $user_id))->data($user_info)->save();

                if ($userMain === false) {
                    $OrderModel->rollback();
                    return $this->apiReturn(44018, '更新用户来源失败');
                }
            }

            $OrderModel->commit();

            if ($change_pay_type && $check_failed) {
                $event = '设置预付款和调价超限，预付款金额：' . floatval($deposit_amount) . '，尾款金额：' . ($temp_info['goods_amount'] - floatval($deposit_amount)) . '，需经理审核后才能处理，' . rtrim($check_failed_info, ' | ');
                $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event);

                return $this->apiReturn(44018, '设置预付款和调价失败！商品单价调价幅度低于20%及以下，需经理审核后才能处理');
            } else if ($change_pay_type) {
                $event = '设置预付款，预付款金额：' . floatval($deposit_amount) . '，尾款金额：' . ($temp_info['goods_amount'] - floatval($deposit_amount)) . '，需经理审核后才能处理';

                $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event);

                return $this->apiReturn(44018, $event);
            } else if ($check_failed) {
                $event .= '调价超限，需经理审核，' . rtrim($check_failed_info, ' | ');

                $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event);

                return $this->apiReturn(44018, '调价失败！商品单价调价幅度低于20%及以下，请及时联系经理审核');
            }
        }

        $order_items = $OrderItemsModel->getOrderList($order_id, '', null);

        $old_items_info   = ''; // 记录修改前的商品信息
        $origin_item_info = []; // 修改前明细

        foreach ($order_items as $v) {
            $old_items_info .= ' [' . $v['goods_name'] . '; ' . $v['goods_number'] . '; ' . $v['goods_price'] . ']';

            $origin_item_info[$v['rec_id']] = $v;
        }

        $lock_item_ids        = array(); // 需要锁定的商品ID
        $lock_goods           = array(); // 需锁定的型号
        $unlock_goods         = array(); // 需解锁的型号
        $unlock_items         = array(); // 自营需解锁商品
        $new_client_price_tag = false; // 新客价修改数量标记
        $push_sale_data = [];
        $pingtai_id = C('SELF_SAMPLE_PUR'); // 平台采购员ID
        $joint_lock_item_ids = array(); // 需要锁定的专营商品ID
        $joint_unlock_items = array(); // 需解锁的专营商品ID

        // 若有新增的商品，则需要添加到销售商品库，获取商品编码
        foreach ($change_info as $key => &$item) {
            if ($item['status'] == -1) continue;

            $item['goods_name'] = strConv($item['goods_name']);
            $item['brand_name'] = strConv($item['brand_name']);

            if (!$item['standard_brand_name']) return $this->apiReturn(44019, '当前商品' . $item['goods_name'] . '的标准品牌为空');
            if (isset($item['goods_sn']) && $item['goods_sn']) continue;

            $push_sale_data['goods_list'][$key]['goods_name'] = $item['goods_name'];
            $push_sale_data['goods_list'][$key]['brand_name'] = $item['brand_name'];
            $push_sale_data['goods_list'][$key]['uni_key'] = md5($item['brand_name'] . $item['goods_name']);
            $push_sale_data['goods_list'][$key]['standard_brand_name'] = $item['standard_brand_name'];
            $push_sale_data['goods_list'][$key]['standard_brand_id'] = $item['standard_brand_id'];
        }

        $goods_lib_data = [];

        if (!empty($push_sale_data)) {
            $res = post_curl(SALE_DOMAIN . '/inner/goods/createMultiGoods', $push_sale_data);

            $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件
            \Think\Log::write('审单添加到销售商品库，参数：' . json_encode($push_sale_data) . '返回数据：' . $res, INFO, '', $path);

            $res = json_decode($res, true);

            if ($res && $res['code'] == 0) {
                foreach ($res['data']['bind_list'] as $list) {
                    $goods_lib_data[$list['uni_key']]['sale_goods_id'] = $list['goods_id'];
                    $goods_lib_data[$list['uni_key']]['goods_sn'] = $list['goods_sn'];
                    $goods_lib_data[$list['uni_key']]['brand_id'] = $list['brand_id'];
                }
            }
        }

        // 获取供应商渠道类型
        $supplier_names = array_unique(array_column($change_info, 'supplier_name'));

        $SupplierModel = D('Supplier');
        $channel_types = $SupplierModel->getChannelTypeByName($supplier_names);

        // 修改订单商品明细
        foreach ($change_info as $k => $v) {
            if ($order_extend['business_type'] == 0) { // 正常订单校验数量、单价
                if (!$v['goods_number']) return $this->apiReturn(44019, $v['goods_name'] . '数量不能为0或空');
                // if ($v['goods_price'] == '' || $v['goods_price'] == 0) return $this->apiReturn(44019, $v['goods_name'].'单价不能为0或空');
            }

            // 联营订单，若没标准品牌，则不能审核通过
            // if ($order_info['order_goods_type'] == 1 && $v['status'] == 1 && !$v['standard_brand_id'] && !$v['standard_brand_name']) {
            //     return $this->apiReturn(44019, $v['goods_name'].'没有选择标准品牌');
            // }


            // if (isset($v['is_lock'])) { // 预售锁定库存，is_lock为商品ID
            //     $lock_item_ids[$v['is_lock']] = $v['goods_number'];
            //     $lock_goods[]                 = $v['goods_name'];
            //     $v['is_lock']                 = 1;
            // }

            $v['buyer_id'] = $v['buyer_id'] ?: 0;

            // 若自营商品未锁，则需锁定数量
            if ($v['order_goods_type'] == 2 && $v['buyer_id'] == $pingtai_id && $v['status'] == 1 && $v['is_lock'] == 0) {
                $lock_item_ids[$v['goods_id']] = $v['goods_number'];

                $lock_goods[$k]['goods_name']   = $v['goods_name'];
                $lock_goods[$k]['goods_number'] = $v['goods_number'];
            }

            // 若寄售商品未锁，则需锁定数量
            if ($v['goods_type'] == 4 && $v['status'] == 1 && $v['is_lock'] == 0) {
                $joint_lock_item_ids[$k]['goods_id'] = $v['goods_id'];
                $joint_lock_item_ids[$k]['num'] = $v['goods_number'];

                $lock_goods[$k]['goods_name']   = $v['goods_name'];
                $lock_goods[$k]['goods_number'] = $v['goods_number'];
            }

            // 若自营商品修改商品数量，先解锁，再锁定最新数量
            if ($v['order_goods_type'] == 2 && $v['buyer_id'] == $pingtai_id && $v['status'] == 1 && $v['is_lock'] == 1 && $origin_item_info[$k]['goods_number'] != $v['goods_number']) {
                $unlock_items[$v['goods_id']]  = $origin_item_info[$k]['goods_number'];
                $lock_item_ids[$v['goods_id']] = $v['goods_number'];

                $lock_goods[$k]['goods_name']     = $v['goods_name'];
                $lock_goods[$k]['goods_number']   = $v['goods_number'];
                $unlock_goods[$k]['goods_name']   = $v['goods_name'];
                $unlock_goods[$k]['goods_number'] = $origin_item_info[$k]['goods_number'];
            }

            // 若寄售商品修改商品数量，先解锁，再锁定最新数量
            if ($v['goods_type'] == 4 && $v['status'] == 1 && $v['is_lock'] == 1 && $origin_item_info[$k]['goods_number'] != $v['goods_number']) {
                $joint_unlock_items[$k]['goods_id']  = $v['goods_id'];
                $joint_unlock_items[$k]['num']  = $origin_item_info[$k]['goods_number'];

                $joint_lock_item_ids[$k]['goods_id'] = $v['goods_id'];
                $joint_lock_item_ids[$k]['num'] = $v['goods_number'];

                $lock_goods[$k]['goods_name']     = $v['goods_name'];
                $lock_goods[$k]['goods_number']   = $v['goods_number'];
                $unlock_goods[$k]['goods_name']   = $v['goods_name'];
                $unlock_goods[$k]['goods_number'] = $origin_item_info[$k]['goods_number'];
            }

            // 若自营商品修改采购员，先解锁，再变更商品类型
            if ($v['order_goods_type'] == 2 && $v['buyer_id'] != $pingtai_id && $v['status'] == 1) {
                if ($v['is_lock'] == 1) {
                    $unlock_items[$v['goods_id']] = $origin_item_info[$k]['goods_number'];

                    $unlock_goods[$k]['goods_name']   = $v['goods_name'];
                    $unlock_goods[$k]['goods_number'] = $origin_item_info[$k]['goods_number'];
                }

                $v['order_goods_type'] = 1;
            }

            // 若删除已锁定的自营商品，则需解锁
            if ($v['order_goods_type'] == 2 && $v['buyer_id'] == $pingtai_id && $v['status'] == -1 && $v['is_lock'] == 1) {
                $unlock_items[$v['goods_id']] = $v['goods_number'];

                $unlock_goods[$k]['goods_name']   = $v['goods_name'];
                $unlock_goods[$k]['goods_number'] = $v['goods_number'];
            }

            // 若删除已锁定的寄售商品，则需解锁
            if ($v['goods_type'] == 4 && $v['status'] == -1 && $v['is_lock'] == 1) {
                $joint_unlock_items[$k]['goods_id'] = $v['goods_id'];
                $joint_unlock_items[$k]['num'] = $v['goods_number'];

                $unlock_goods[$k]['goods_name']   = $v['goods_name'];
                $unlock_goods[$k]['goods_number'] = $v['goods_number'];
            }

            // 检查是否修改商品数量
            if ($origin_item_info[$k]['goods_number'] != $v['goods_number']) {
                $new_client_price_tag = true;
            }

            $add_data_ext = []; // 明细扩展表

            $add_data_ext['order_id']        = $order_id;
            $add_data_ext['batch']           = $v['batch'];
            $add_data_ext['contract_remark'] = !empty($v['contract_remark']) ? $v['contract_remark'] : '';
            $add_data_ext['raw_goods_sn']    = isset($v['raw_goods_sn']) ? strConv($v['raw_goods_sn']) : '';
            $add_data_ext['raw_brand_name']  = isset($v['raw_brand_name']) ? strConv($v['raw_brand_name']) : '';
            $add_data_ext['is_provide_dc']  = isset($v['is_provide_dc']) ? $v['is_provide_dc'] : -1;
            $add_data_ext['is_provide_producer']  = isset($v['is_provide_producer']) ? $v['is_provide_producer'] : -1;
            $add_data_ext['supplier_type'] = isset($channel_types[$v['supplier_name']]) ? $channel_types[$v['supplier_name']] : 0;
            $add_data_ext['inquiry_item_id']  = isset($v['inquiry_item_id']) ? $v['inquiry_item_id'] : 0;
            $add_data_ext['inquiry_id']  = isset($v['inquiry_id']) ? $v['inquiry_id'] : 0;
            $add_data_ext['inquiry_sn']  = isset($v['inquiry_sn']) ? $v['inquiry_sn'] : '';
            $add_data_ext['quote_id']  = isset($v['quote_id']) ? $v['quote_id'] : 0;
            $add_data_ext['quote_sn']  = isset($v['quote_sn']) ? $v['quote_sn'] : '';
            $add_data_ext['pm_uid']  = isset($v['pm_uid']) ? $v['pm_uid'] : 0;
            $add_data_ext['ability_level']  = isset($v['ability_level']) ? $v['ability_level'] : -1;
            unset($v['batch']);
            unset($v['contract_remark']);
            unset($v['raw_goods_sn']);
            unset($v['raw_brand_name']);
            unset($v['is_provide_dc']);
            unset($v['is_provide_producer']);
            unset($v['inquiry_item_id']);
            unset($v['inquiry_id']);
            unset($v['inquiry_sn']);
            unset($v['quote_id']);
            unset($v['quote_sn']);
            unset($v['pm_uid']);
            unset($v['ability_level']);
            if (isset($v["brand_area_cn"])) {
                unset($v['brand_area_cn']);
            }


            // 获取digikey编码
            if (strtolower($v['supplier_name']) == 'digikey' && $v['goods_id'] && !$add_data_ext['raw_goods_sn'] && !$add_data_ext['raw_brand_name']) {
                $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.search"));
                $spuRedis = spu_redis_init();
                $dgk_res = $spuRedis->hget("sku_raw_map", $v['goods_id']);

                if ($dgk_res) {
                    $dgk_info = json_decode($dgk_res, true);
                    $add_data_ext['raw_goods_sn']      = $dgk_info['raw_goods_id'];
                    $add_data_ext['raw_goods_packing'] = $dgk_info['pack'];
                    $add_data_ext['raw_brand_name']    = $dgk_info['raw_brand_name'];
                }
            }

            if (isset($v['is_add']) && $v['status'] == -1) continue; // 跳过后台新增、删除的

            $add_data = [];
            $uni_key = md5($v['brand_name'] . $v['goods_name']);

            // 新增
            if ($v['is_add'] && $v['add_type']) { // 新增无SKUID商品
                $add_data['order_id']            = $order_id;
                $add_data['user_id']             = $user_id;
                $add_data['goods_id']            = !empty($v['goods_id']) ? $v['goods_id'] : 0;
                $add_data['goods_name']          = $v['goods_name'];
                $add_data['brand_name']          = $v['brand_name'];
                $add_data['standard_brand_id']   = !empty($v['standard_brand_id']) ? $v['standard_brand_id'] : 0;
                $add_data['standard_brand_name'] = !empty($v['standard_brand_name']) ? $v['standard_brand_name'] : '';
                $add_data['supplier_name']       = $v['supplier_name'];
                $add_data['goods_number']        = $v['goods_number'];
                $add_data['goods_price']         = $v['goods_price'];
                $add_data['delivery_time']       = $v['delivery_time'];
                $add_data['buyer_id']            = $v['buyer_id'];
                $add_data['goods_source']        = 2; // 明细来源，1-前台，2-后台
                $add_data['tax_rate']            = OrderModel::$SALE_COM_ID_TAX[$order_info['sale_com_id']];  // 添加固定税率
                $add_data['is_gift']             = $v['is_gift'];
                $add_data['is_purchase']         = $v['is_purchase'];
                $add_data['is_vacuo']            = $v['is_vacuo'];
                $add_data['brand_area']          = $v['brand_area'];
                $add_data['customer_material_number'] = !empty($v['customer_material_number']) ? $v['customer_material_number'] : '';

                if (empty($v['goods_sn'])) {
                    $add_data['goods_sn'] = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['goods_sn'] : '';
                    $add_data['sale_goods_id'] = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['sale_goods_id'] : '';
                    $add_data['brand_id'] = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['brand_id'] : '';
                } else {
                    $add_data['goods_sn'] = $v['goods_sn'];
                    $add_data['sale_goods_id'] = $v['sale_goods_id'] ?: 0;
                    $add_data['brand_id'] = $v['brand_id'] ?: 0;
                }
                // D('Common/Spu')->get_brand_standard_area(20000)
                $rec_id = $OrderItemsModel->add($add_data);

                $add_data_ext['rec_id'] = $rec_id;
                if ($add_data["supplier_name"] == "立创") {
                    $add_data_ext["ability_level"] = 0; //弱履约
                }
                $res = $OrderItemsExtModel->add($add_data_ext);

                if (!empty($v['goods_id'])) { // 批量导入时可能存在skuid
                    $event .= '，新增商品（' . $v['goods_name'] . '）';
                } else {
                    $event .= '，新增无SKUID商品（' . $v['goods_name'] . '）';
                }
                continue;
            } else if ($v['is_add']) {
                $res = $this->getGoodsInfo($v['goods_id'], $v['goods_number'], $order_id);

                if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

                if ($res['data']['order_goods_type'] == 2) { // 若是自营商品，则放入待锁库数组
                    $lock_item_ids[$v['goods_id']] = $v['goods_number'];

                    $lock_goods[$k]['goods_name']   = $v['goods_name'];
                    $lock_goods[$k]['goods_number'] = $v['goods_number'];
                }

                $add_data = $res['data'];

                $add_data['delivery_time'] = !empty($v['delivery_time']) ? $v['delivery_time'] : '';
                $add_data['buyer_id']      = $v['buyer_id'];
                $add_data['goods_source']  = 2;
                $add_data['standard_brand_id']   = !empty($v['standard_brand_id']) ? $v['standard_brand_id'] : 0;
                $add_data['standard_brand_name'] = !empty($v['standard_brand_name']) ? $v['standard_brand_name'] : '';
                $add_data['brand_area']          = $v['brand_area'];
                $add_data['tax_rate'] = OrderModel::$SALE_COM_ID_TAX[$order_info['sale_com_id']];  // 添加固定税率
                $add_data['customer_material_number'] = !empty($v['customer_material_number']) ? $v['customer_material_number'] : '';

                if (empty($v['goods_sn'])) {
                    $add_data['goods_sn'] = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['goods_sn'] : '';
                    $add_data['sale_goods_id'] = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['sale_goods_id'] : '';
                    $add_data['brand_id'] = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['brand_id'] : '';
                } else {
                    $add_data['goods_sn'] = $v['goods_sn'];
                    $add_data['sale_goods_id'] = $v['sale_goods_id'] ?: 0;
                    $add_data['brand_id'] = $v['brand_id'] ?: 0;
                }

                if ($v['is_gift'] == 1) { // 赠品调整单价为0
                    $add_data['goods_price'] = 0;
                }

                $add_data['is_gift']     = $v['is_gift'];
                $add_data['is_purchase'] = $v['is_purchase'];
                $add_data['is_vacuo'] = $v['is_vacuo'];

                $add_data_ext['goods_moq']     = $add_data['goods_moq'];
                $add_data_ext['goods_spq']     = $add_data['goods_spq'];
                $add_data_ext['goods_encap']   = $add_data['goods_encap'];
                $add_data_ext['goods_class']   = $add_data['goods_class'];
                $add_data_ext['goods_packing'] = $add_data['goods_packing'];
                unset($add_data['goods_moq']);
                unset($add_data['goods_spq']);
                unset($add_data['goods_encap']);
                unset($add_data['goods_class']);
                unset($add_data['goods_packing']);



                $rec_id = $OrderItemsModel->add($add_data);

                $add_data_ext['rec_id'] = $rec_id;
                $res = $OrderItemsExtModel->add($add_data_ext);

                $event .= '，新增商品（' . $v['goods_name'] . '）';
                continue;
            } else if ($v['status'] == -1) { // 删除
                $res = $OrderItemsModel->where(['rec_id' => $k])->save(['status' => -1]);

                $event .= '，删除商品（' . $v['goods_name'] . '），删除原因：' . $v['del_reason'];
                continue;
            }

            if (empty($v['goods_sn'])) {
                $v['goods_sn']      = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['goods_sn'] : '';
                $v['sale_goods_id'] = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['sale_goods_id'] : '';
                $v['brand_id']      = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['brand_id'] : '';
            }

            $map = [];
            $map['order_id'] = $order_id;
            $map['rec_id']   = $k;
            $orderItems = $OrderItemsModel->where($map)->save($v);


            if ($orderItems === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44019, '更新商品明细失败');
            }

            $orderItemsExt = $OrderItemsExtModel->where($map)->save($add_data_ext);

            if ($orderItemsExt === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44019, '更新商品明细扩展失败');
            }
        }

        // 自营解锁库存
        if (!empty($unlock_items)) {
            $res = $this->unlockSku($order_id, $unlock_items);

            if (!$res || $res['errcode'] != 0) {
                $OrderModel->rollback();
                return $this->apiReturn(44007, '解锁自营库存失败 ' . $res['errmsg']);
            }

            $event .= '，已解锁自营库存的型号：';

            foreach ($unlock_goods as $v) {
                $event .= '[' . $v['goods_name'] . '；' . $v['goods_number'] . '] ';
            }

            // 更新商品锁定状态
            foreach ($unlock_items as $k => $v) {
                $OrderItemsModel->where(['order_id' => $order_id, 'goods_id' => $k])->save(['is_lock' => 0]);
            }
        }

        // 专营解锁库存
        if (!empty($joint_unlock_items)) {
            $res = $this->jointSkuAction($order_id, $joint_unlock_items, 2); // 专营

            if (!$res || $res['code'] != 0) {
                $OrderModel->rollback();
                return $this->apiReturn(44007, '解锁专营库存失败 ' . $res['errmsg']);
            }

            $event .= '，已解锁专营库存的型号：';

            foreach ($unlock_goods as $v) {
                $event .= '[' . $v['goods_name'] . '；' . $v['goods_number'] . '] ';
            }

            // 更新商品锁定状态
            foreach ($joint_unlock_items as $k => $v) {
                $OrderItemsModel->where(['order_id' => $order_id, 'goods_id' => $v['goods_id']])->save(['is_lock' => 0]);
            }
        }

        // 锁定选中的自营商品库存
        if (!empty($lock_item_ids)) {
            $res = $this->lockSku($order_id, $lock_item_ids);

            if (!$res || $res['errcode'] != 0) {
                $OrderModel->rollback();
                return $this->apiReturn(44019, '锁定自营库存失败 ' . $res['errmsg']);
            }

            $event .= '，已锁定自营库存的型号：';

            foreach ($lock_goods as $v) {
                $event .= '[' . $v['goods_name'] . '；' . $v['goods_number'] . '] ';
            }

            // 更新商品锁定状态
            foreach ($lock_item_ids as $k => $v) {
                $OrderItemsModel->where(['order_id' => $order_id, 'goods_id' => $k])->save(['is_lock' => 1]);
            }
        }

        // 锁定选中的专营商品库存
        if (!empty($joint_lock_item_ids)) {
            $res = $this->jointSkuAction($order_id, $joint_lock_item_ids);

            if (!$res || $res['code'] != 0) {
                $OrderModel->rollback();
                return $this->apiReturn(44019, '锁定寄售库存失败 ' . $res['errmsg']);
            }

            $event .= '，已锁定专营库存的型号：';

            foreach ($lock_goods as $v) {
                $event .= '[' . $v['goods_name'] . '；' . $v['goods_number'] . '] ';
            }

            // 更新商品锁定状态
            foreach ($joint_lock_item_ids as $v) {
                $OrderItemsModel->where(['order_id' => $order_id, 'goods_id' => $v['goods_id']])->save(['is_lock' => 1]);
            }
        }

        // 根据商品明细获取商品总额
        $order_items_info = $OrderItemsModel->getOrderList($order_id, '', null);

        $goods_amount       = 0; // 商品总额
        $joint_goods_amount = 0; // 联营商品总额
        $self_goods_amount  = 0; // 自营商品总额

        foreach ($order_items_info as $v) {
            $goods_amount += $v['goods_amount'];

            if ($v['order_goods_type'] == 1) {
                $joint_goods_amount += $v['goods_amount'];
            } else {
                $self_goods_amount += $v['goods_amount'];
            }
        }

        // 检查赠品
        $res = $this->checkOrderGift($order_info, $order_items_info);
        if ($res['err_code'] == 0 && $res['err_msg'] != '') $event .= '，' . $res['err_msg'];

        /* 临时新客价优惠方案：涉及删除或修改数量的都删除新客价优惠 2020-3-13 */
        $new_client_price = $OrderPriceModel->getNewClientPrice($order_id); // 新客价优惠

        if ($new_client_price_tag && $new_client_price) {
            $res = $OrderPriceModel->where(['order_id' => $order_id, 'price_type' => -8])->delete();

            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44102, '删除新客价优惠失败');
            }
        }

        // 附加款
        if ($extra_fee != '') {
            $extra_fee_where = [];
            $extra_fee_where['order_id']   = array('eq', $order_id);
            $extra_fee_where['price_type'] = array('eq', 2);

            $price_id = $OrderPriceModel->where($extra_fee_where)->getField('price_id');

            $saveData = [];
            $saveData['price']       = abs(floatval($extra_fee));
            $saveData['create_time'] = time();
            if ($price_id) { // 保存
                $orderPriceExtendFee = $OrderPriceModel->where(array('price_id' => $price_id))->data($saveData)->save();
            } else { // 新增
                $orderPriceExtendFee = $OrderPriceModel->createOrderPrice($order_id, $saveData['price'], 2);
            }

            if ($orderPriceExtendFee === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44019, '更新附加费失败');
            }
        }

        // 运费
        if ($freight_fee != '') {
            $OrderPriceModel->where(['order_id' => $order_id, 'price_type' => -6])->delete(); // 先删除运费优惠

            $freight_fee_where = [];
            $freight_fee_where['order_id']   = array('eq', $order_id);
            $freight_fee_where['price_type'] = array('eq', 3);

            $price_id = $OrderPriceModel->where($freight_fee_where)->getField('price_id');

            $saveData                = [];
            $saveData['price']       = abs(floatval($freight_fee));
            $saveData['create_time'] = time();
            if ($price_id) { // 保存
                $orderPriceFreightFee = $OrderPriceModel->where(array('price_id' => $price_id))->data($saveData)->save();
            } else { // 新增
                $orderPriceFreightFee = $OrderPriceModel->createOrderPrice($order_id, $saveData['price'], 3);
            }

            if ($orderPriceFreightFee === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44019, '更新附加费失败');
            }
        }

        // 货款价格变动，需要更改该订单的货款价格记录
        $saveData = [];
        $saveData['price'] = floatval($goods_amount);
        $goods_amount_where = [];
        $goods_amount_where['price_type'] = array('eq', 1);
        $goods_amount_where['order_id']   = array('eq', $order_id);

        $goods_price_id = $OrderPriceModel->where($goods_amount_where)->getField('price_id');

        if ($goods_price_id) {
            $orderPriceGoodsAmount = $OrderPriceModel->where($goods_amount_where)->data($saveData)->save();
        } else {
            $orderPriceGoodsAmount = $OrderPriceModel->createOrderPrice($order_id, $saveData['price'], 1);
        }

        if ($orderPriceGoodsAmount === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44019, '更新货款价格失败');
        }

        // 订单扩展表更改
        $saveTemp = [];
        $saveTemp['order_id']         = $order_id;
        $saveTemp['sale_id']          = $sale_id;
        $saveTemp['order_pay_type']   = $order_pay_type;
        $saveTemp['pay_time_limit']   = $pay_time_limit;
        $saveTemp['status']           = 2;
        $saveTemp['kefu_remark']      = $kefu_remark;
        $saveTemp['zy_delivery_type'] = $zy_delivery_type;
        $saveTemp['is_print_tag']     = I('is_print_tag', -1);
        $saveTemp['print_tag_id']     = I('print_tag_id', 0);
        $saveTemp['print_tag_name']   = I('print_tag_name', '');
        $saveTemp['hy_order_source']  = I('hy_order_source', 0);

        if (!empty($change_extend_fee)) {
            $saveTemp['extend_fee_items'] = json_encode($change_extend_fee); // 回写供应商附加费
        } else {
            $change_extend_fee = json_decode($order_extend['extend_fee_items'], true);
        }

        // $is_manager = I('is_manager', 0); // 0-客服，1-主管

        // if ($order_info['order_goods_type'] == 1 && !$is_manager) { // 联营客服标记申请主管审核，并清空临时保存信息
        //     $saveTemp['is_manager_audit'] = 1;
        //     $saveTemp['temp_save_info']   = '';
        // }

        // if ($order_info['order_goods_type'] == 1 && $is_manager) { // 联营主管标记为3，写入主管ID
        //     $saveTemp['is_manager_audit'] = 3;
        //     $saveTemp['manager_id']       = $operator_id;
        // }

        if ($order_info['order_goods_type'] == 1) { // 联营订单标记申请主管审核，并清空临时保存信息 --- 20200716
            $saveTemp['is_manager_audit'] = 1;
            $saveTemp['temp_save_info']   = '';
        }

        $orderExtend = $OrderExtendModel->where(array('temp_id' => $order_extend['temp_id']))->data($saveTemp)->save();

        if ($orderExtend === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44019, '更新供应商附加费明细失败');
        }

        // 用户来源
        if ($client_source) {
            $user_info['client_source'] = $client_source;
            $userSource = $UserMainModel->where(array('user_id' => $user_id))->data($user_info)->save();

            if ($userSource === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44019, '更新用户来源失败');
            }
        }

        // 订单总额 或 订单预付款金额
        // if ($order_extend['order_type'] == 3) { // 内部采购下单
        //     $order_amount = $goods_amount * C('SELF_OFFLIINE_ORDER_DISCOUNT_VAL');
        //     $discount_amount = price_format($goods_amount - $order_amount); // 95折后的优惠金额

        //     // 更新优惠金额
        //     $saveDiscount['price'] = $discount_amount;
        //     $discount_price_where['price_type'] = array('eq', -4);
        //     $discount_price_where['order_id'] = array('eq', $order_id);
        //     $orderPriceDiscountAmount = $OrderPriceModel->where($discount_price_where)->data($saveDiscount)->save();

        //     if ($orderPriceDiscountAmount === false) {
        //         $OrderModel->rollback();
        //         return $this->apiReturn(44019, '更新内部采购优惠金额失败');
        //     }
        // } else {
        $order_amount = $OrderPriceModel->getOrderTotalPrice($order_id);
        // }

        // 若会员账期额度小于订单总额，则不能设置为账期订单 --- ********
        // if (isset($credit_amount) && $credit_amount < $order_amount) {
        //     $OrderModel->rollback();
        //     return $this->apiReturn(44103, '用户账期额度不足，去<a href="'.FINANCE_DOMAIN.'/web/userCreditList" target="_blank">&nbsp;财务系统&nbsp;</a>看看吧');
        // }

        if ($order_pay_type == 2) { // 预付款
            $save_order['advance_amount'] = floatval($deposit_amount);
        }

        $save_order['order_amount']   = floatval($order_amount);
        $save_order['pay_suffix']     = $order_info['pay_suffix'] + 1; // 修改支付尾缀，第三方支付下单流水号需要生成不同订单号
        $save_order['order_pay_type'] = $order_pay_type;
        $save_order['status']         = $status;
        $save_order['sale_id']        = $sale_id;
        $save_order['confirm_time']   = time();
        $save_order['customer_sn']    = I('customer_sn', '');
        $save_order['pay_time']       = time() + $pay_time_limit * 86400; // 添加付款时间

        // 订单运费
        $shipping_fee = $OrderPriceModel->getShippingPrice($order_id);

        // 优惠金额
        $coupon_price = $OrderPriceModel->getPreferentialPrice($order_id);

        $event .= '，商品总额：' . $goods_amount . '，附加费：' . abs(floatval($extra_fee)) . '，运费：' . $shipping_fee;
        $success = '操作成功';

        if ($new_client_price_tag && $new_client_price) {
            switch ($order_info['order_type_extend']) {
                case 1:
                    $msg_text = '团购';
                    break;
                case 2:
                    $msg_text = '秒杀';
                    break;
                default:
                    $msg_text = '新客价';
                    break;
            }
            $event .= '，删除' . $msg_text . '优惠（' . $new_client_price . '）';
        }

        /*** 订单优惠信息调整  ***/
        $coupon = D('UserCoupon')->where(['order_id' => $order_id, 'status' => 1])->find();

        if ($coupon) {
            $couponCondition = D('Coupon')->where(['coupon_id' => $coupon['coupon_id']])->find();

            // 适用商城：1全站 2自营商城 3联营商城
            switch ($couponCondition['coupon_mall_type']) {
                case 1:
                    $compare_amount = $goods_amount;
                    break;
                case 2:
                    $compare_amount = $self_goods_amount;
                    break;
                case 3:
                    $compare_amount = $joint_goods_amount;
                    break;
            }

            // 若调价后的商品总额小于优惠条件金额，则退回优惠券，否则使用
            if ($compare_amount < $couponCondition['require_amount']) {
                // 优惠券状态更改为未使用
                $this->returnCoupon($order_id, $order_info['user_id']);

                $order_amount = floatval($order_amount - $coupon_price);
                $save_order['order_amount'] = $order_amount;

                $orderPriceCoupon = $OrderPriceModel->where(['order_id' => $order_id, 'price_type' => -4])->delete();

                if ($orderPriceCoupon === false) {
                    $OrderModel->rollback();
                    return $this->apiReturn(44019, '删除优惠金额失败');
                }

                $coupon_type = $couponCondition['coupon_type'] == 1 ? '优惠券' : '折扣券';

                // 操作记录
                $event .= '，优惠金额：' . $coupon_price . ' [' . $couponCondition['coupon_name'] . ']（优惠券已退回）';

                $success .= '，' . $coupon_type . '已退回！';
                $coupon_price = 0;
            } else {
                $event .= '，优惠金额：' . $coupon_price . ' [' . $couponCondition['coupon_name'] . ']';
            }
        }

        $event .= '，付款截止时间：' . date('Y-m-d H:i:s', $save_order['pay_time']) . '，审核前型号、数量、价格：' . $old_items_info;

        // 单个商品优惠金额
        $orderItemsList = $OrderItemsModel->getOrderList($order_id, '', null);
        $activity_price = $OrderPriceModel->getActivityPrice($order_id); // 活动优惠

        $avg_need_amount = [
            'order_amount'   => $order_amount,
            'coupon_price'   => $coupon_price,
            'activity_price' => $activity_price,
            'extend_fee'     => $extra_fee,
            'shipping_price' => $shipping_fee,
        ];

        $this->countItemsPrice($order_id, $avg_need_amount, $orderItemsList, $change_extend_fee);

        // if (isset($resArr) || ($order_info['order_goods_type'] == 1 && $is_manager)) {
        // if ($order_info['order_goods_type'] == 1 && $is_manager) {
        //     $save_order['erp_syn'] = 1; // 联营设置为账期订单或待付款状态，标记ERP同步字段
        // }

        // 自营设置为账期订单后，修改会员账期额度 --- ********
        // if ($order_info['order_goods_type'] == 2 && $order_pay_type == 3) {
        //     $zq_params['user_id']     = $user_id;
        //     $zq_params['diff_amount'] = $order_amount;

        //     $res = json_decode(post_curl(CREDIT_DOMAIN.'/credit/setoccupy', $zq_params), true);

        //     if (!$res) return $this->apiReturn(44104, '修改会员账期额度接口异常');

        //     if ($res['errcode'] != 0) {
        //         $OrderModel->rollback();
        //         return $this->apiReturn(44105, '修改会员账期占用额度失败');
        //     }

        //     // $save_order['status'] = 4; // 订单状态设置为待发货
        // }

        $updateOrder = $OrderModel->where(array('order_id' => $order_id))->data($save_order)->save();

        if ($updateOrder === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44019, '更新订单信息失败');
        }

        // 联营订单不限制审核次数
        if ($order_info['order_goods_type'] != 1) {
            $OrderModel->where(array('order_id' => $order_id))->setInc('adjust_count'); // 只要点击审核通过，算一次调价，总共只可点击两次
        }

        $actionLog = $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event);

        // 预付款订单 首款为0时调用支付方法
        // if ($order_pay_type == 2 && $deposit_amount == 0) {
        //     $datas['pay_type'] = 2;
        //     $datas['price'] = 0;
        //     $datas['serial_number'] = '-';
        //     $datas['pay_id'] = 0;
        //     $datas['pay_order_sn'] = '';
        //     $datas['pay_name'] = '微信支付';
        //     $datas['sale_id'] = $operator_id; // 审核标记 操作人ID

        //     $res = A('Order/Pay')->setPayOrder($order_id, $datas);

        //     if ($res['err_code'] != 0) {
        //         $OrderModel->rollback();
        //         return $this->apiReturn($res['err_code'], '设置预付0元，支付接口错误信息：'.$res['err_msg']);
        //     }
        // }

        $OrderModel->commit(); // 提交事务

        // 订单总额0 且 自营样片付款
        if ($order_amount == 0 && $order_extend['business_type'] == 1) {
            $save = array(
                'pay_type'      => 1,
                'price'         => 0,
                'pay_order_sn'  => '',
                'serial_number' => '-',
                'pay_id'        => -1,
                'pay_name'      => '',
            );

            $res = A('Order/Pay')->setPayOrder($order_id, $save);
            if (empty($res) || $res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

            // $res = $this->makeOrder($order_id); // 发送wms
            // if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        // 自营取消wms后再同步到wms 或 自营、现卖、账期 --- ********
        // if ((isset($cancel_wms_order) && $cancel_wms_order['entity'][0]['Status'] = 1) || ($order_info['order_goods_type'] == 2 && $order_info['sale_type'] == 1 && $order_pay_type == 3)) {
        //     $res = $this->makeOrder($order_id); // 发送wms

        //     if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);
        // }

        // 自营账期添加支付记录 --- ********
        // if ($order_info['order_goods_type'] == 2 && $order_pay_type == 3) {
        //     $pay_params['order_id'] = $order_id;
        //     $pay_params['order_sn'] = $order_info['order_sn'];
        //     $pay_params = array_merge($pay_params, authkey());
        //     $res = json_decode(post_curl(API_DOMAIN.'/pay/billPay', $pay_params), true);

        //     if (!$res) return $this->apiReturn(44106, '自营账期添加支付记录接口异常');
        //     if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);
        // }

        /*** END ***/
        $admincheck = $this->sendMsgAtCheck($order_id, $sale_id); // 发送短信

        if ($admincheck['err_code'] != 0) return $this->apiReturn(0, $success . $admincheck['err_msg'], '');

        return $this->apiReturn(0, $success, '');
    }

    // 修改明细 - 供自研销售系统调用
    public function setOrderItems()
    {
        $order_id    = I('order_id', 0);
        $update_type = I('update_type', 1);
        $order_items = I('order_items', '');
        $operator_id = I('operator_id', 0);

        if (!$order_id) return $this->apiReturn(-1, '订单ID缺失');
        if (!$order_items) return $this->apiReturn(-1, '订单明细缺失');

        $event = $update_type == 1 ? '修改明细数量' : '修改明细单价';
        $this->apiRecord($event . '，请求数据：' . json_encode(I('request.'))); // 记录接口访问信息

        $OrderModel          = D('Order');
        $OrderExtendModel    = D('OrderExtend');
        $OrderItemsModel     = D('OrderItems');
        $OrderPriceModel     = D('OrderPrice');
        $OrderActionLogModel = D('OrderActionLog');
        $OrderServiceModel = D('OrderService');
        $OrderDeliveryItemsModel = D('OrderDeliveryItems');

        $order_info = $OrderModel->getInfo($order_id);

        $OrderModel->startTrans();

        // 更新明细表
        foreach ($order_items as $item) {
            $save_item = [];

            if ($update_type == 1) {
                $save_item['goods_number'] = $item['goods_number'];

                $event .= '，明细ID（' . $item['rec_id'] . '）修改前数量：' . $item['old_goods_number'];
            } else {
                $save_item['goods_price'] = $item['goods_price'];

                $event .= '，明细ID（' . $item['rec_id'] . '）修改前单价：' . $item['old_goods_price'];
            }

            $map = [
                'rec_id' => $item['rec_id'],
            ];

            $OrderItemsModel->where($map)->data($save_item)->save();
        }

        $orderItemsList = $OrderItemsModel->getOrderList($order_id, '', null);

        if ($update_type == 1) {
            $rec_ids = array_column($orderItemsList, 'rec_id');

            $service_nums = $OrderServiceModel->getUnremovalNum($rec_ids); // 获取明细未出库售后数量
            $removal_nums = $OrderDeliveryItemsModel->getRemovalNum($rec_ids); // 获取明细已出库数量
        }

        $goods_amount       = 0; // 商品总额
        $joint_goods_amount = 0; // 联营商品总额
        $self_goods_amount  = 0; // 自营商品总额
        $goods_num_total = 0; // 订单数量合计
        $actual_num_total = 0; // 应发数量合计
        $removal_num_total = 0; // 出库数量合计

        foreach ($orderItemsList as $v) {
            $goods_amount += $v['goods_amount'];

            if ($v['order_goods_type'] == 1) {
                $joint_goods_amount += $v['goods_amount'];
            } else {
                $self_goods_amount += $v['goods_amount'];
            }

            if ($update_type == 1) {
                // 应发数量：订单数量  减去  未出库售后数量（排除售后状态为取消状态的合计数量）
                $service_num = isset($service_nums[$v['rec_id']]) ? $service_nums[$v['rec_id']]['adjust_number'] : 0;
                $actual_number = $v['goods_number'] - $service_num;

                // 库房已出库数量
                $removal_num = isset($removal_nums[$v['rec_id']]) ? $removal_nums[$v['rec_id']]['removal_number'] : 0;

                $goods_num_total += $v['goods_number'];
                $actual_num_total += $actual_number;
                $removal_num_total += $removal_num;
            }
        }

        /*
            订单数量合计为0，调整订单状态为取消；
            订单数量合计不为0，应发数量合计与出库数量合计相等，线下订单状态修改成“完成”、“已全部发货”状态，线上订单状态修改成“待收货”状态；
            订单数量合计不为0，应发数量合计大于出库数量合计，线下订单状态修改成“部分已发货”状态，线上订单状态修改成“部分发货”状态；
        */
        $update_order = [];

        if ($update_type == 1) {
            $update_order['status'] = $order_info['status'];

            if ($goods_num_total == 0) {
                $update_order['status'] = -1;
                $event .= '，订单数量合计为0，状态变更为已取消';
            }

            // 应发数量合计与出库数量合计相等，则订单状态修改成“待收货”状态
            if ($goods_num_total != 0 && $removal_num_total && $actual_num_total == $removal_num_total) {
                $update_order['status'] = 8;
                $event .= '，订单应发数量合计等于出库数量合计，状态变更为待收货';
            }

            // // 应发数量合计大于出库数量合计，则订单状态修改成“部分发货”状态
            if ($goods_num_total != 0 && $removal_num_total && $actual_num_total > $removal_num_total) {
                $update_order['status'] = 7;
            }
        }

        // 货款价格变动，需要更改该订单的货款价格记录
        $saveData = [];
        $saveData['price'] = floatval($goods_amount);
        $res = $OrderPriceModel->where(['order_id' => $order_id, 'price_type' => 1])->save($saveData);

        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44019, '更新货款价格失败');
        }

        $order_amount = $OrderPriceModel->getOrderTotalPrice($order_id);
        $coupon_price = $OrderPriceModel->getPreferentialPrice($order_id); // 优惠金额

        /*** 订单优惠信息调整  ***/
        $coupon = D('UserCoupon')->where(['order_id' => $order_id, 'status' => 1])->find();

        if ($coupon) {
            $couponCondition = D('Coupon')->where(['coupon_id' => $coupon['coupon_id']])->find();

            // 适用商城：1全站 2自营商城 3联营商城
            switch ($couponCondition['coupon_mall_type']) {
                case 1:
                    $compare_amount = $goods_amount;
                    break;
                case 2:
                    $compare_amount = $self_goods_amount;
                    break;
                case 3:
                    $compare_amount = $joint_goods_amount;
                    break;
            }

            // 若调价后的商品总额小于优惠条件金额，则退回优惠券，否则使用
            if ($compare_amount < $couponCondition['require_amount']) {
                // 优惠券状态更改为未使用
                $this->returnCoupon($order_id, $order_info['user_id']);

                $order_amount = floatval($order_amount - $coupon_price);
                $save_order['order_amount'] = $order_amount;

                $orderPriceCoupon = $OrderPriceModel->where(['order_id' => $order_id, 'price_type' => -4])->delete();

                if ($orderPriceCoupon === false) {
                    $OrderModel->rollback();
                    return $this->apiReturn(44019, '删除优惠金额失败');
                }

                $event .= '，优惠金额：' . $coupon_price . ' [' . $couponCondition['coupon_name'] . ']（优惠券已退回）'; // 操作记录

                $coupon_price = 0;
            }
        }

        // 更新订单总额
        $update_order['order_amount'] = $order_amount;
        $res = $OrderModel->where(['order_id' => $order_id])->save($update_order);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44019, '更新订单失败');
        }

        $extend_fee_items = $OrderExtendModel->where(['order_id' => $order_id])->getField('extend_fee_items'); // 附加费明细
        $extra_fee        = $OrderPriceModel->where(['order_id'  => $order_id, 'price_type' => 2])->getField('price'); // 附加费
        $activity_price   = $OrderPriceModel->getActivityPrice($order_id); // 活动优惠
        $shipping_fee     = $OrderPriceModel->getShippingPrice($order_id); // 订单运费

        $datas['list']           = $orderItemsList;
        $datas['extend_items']   = json_decode($extend_fee_items, true);
        $datas['extend_fee']     = $extra_fee;
        $datas['coupon_price']   = abs($coupon_price);
        $datas['activity_price'] = abs($activity_price);
        $datas['shipping_price'] = $shipping_fee;
        $datas['order_amount']   = $order_amount;

        $res = $this->getAvgPrice($datas);

        foreach ($res['list'] as $v) {
            $save = [
                'extend_price'          => $v['extend_price'],
                'preferential_price'    => $v['preferential_price'],
                'single_pre_price'      => $v['single_pre_price'],
                'extra_price'           => $v['extra_price'],
                'goods_discount_amount' => $v['goods_discount_amount'],
            ];

            $map = [
                'order_id' => $order_id,
                'rec_id' => $v['rec_id'],
            ];

            D('OrderItems')->where($map)->save($save);
        }

        // 推送到采购或ERP
        $erp_type = $update_type == 1 ? 25 : 10;
        $pur_type = $update_type == 1 ? 5 : 4;

        $erp_params = [ // 推送ERP数据
            'TYPE' => $erp_type,
            'ORDERID' => $order_info['erp_order_id'],
        ];

        $pur_params = [ // 推送采购数据
            'type' => $pur_type,
        ];

        // 更新后明细
        $newOrderItemsList = $OrderItemsModel->getOrderList($order_id, '', null);

        foreach ($newOrderItemsList as $it) {
            $no_tax_price = $order_info['currency'] == 1 ? price_format($it['single_pre_price'] / (1 + C('fixed_tax_rate')), 0, 6) : $it['single_pre_price'];

            $erp_tmp = [];
            $erp_tmp['ERP_REC_ID'] = $it['erp_rec_id'];

            if ($update_type == 1) {
                $erp_tmp['TAXPRICE'] = $it['single_pre_price']; // 修改后的含税价
                $erp_tmp['PRICE'] = $no_tax_price; // 修改后的未税价
                $erp_tmp['QTY'] = $it['goods_number'];
                $erp_params['ENTRYS'][] = $erp_tmp;
            } else {
                $erp_tmp['TAXPRICE'] = $it['single_pre_price'];
                $erp_tmp['PRICE'] = $no_tax_price;
                $erp_params['ENTRYS'][] = $erp_tmp;
            }

            if ($it['order_goods_type'] == 1 && $it['is_purchase'] == 1) { // 联营商品且需要采购的推送到采购系统
                $pur_tmp = [];
                $pur_tmp['order_item_id'] = $it['rec_id'];

                if ($update_type == 1) {
                    $pur_tmp['frq_qty'] = $it['goods_number'];
                    $pur_tmp['sales_price'] = $it['single_pre_price'];
                    $pur_tmp['sales_without_tax_price'] = $no_tax_price;
                } else {
                    $pur_tmp['sales_price'] = $it['single_pre_price'];
                    $pur_tmp['sales_without_tax_price'] = $no_tax_price;
                }
            }

            !empty($pur_tmp) && $pur_params['items'][] = $pur_tmp;
        }

        // 同步到采购需求管理、采购订单、发货通知单（发货通知单数量大于修改后的数量才更新）
        if ($order_info["erp_order_id"]) {  //存在erpid 才需要操作erp跟采购
            if (!empty($pur_params['items'])) {
                $pur_params['erp_params'] = $erp_params;

                try {
                    $res = post_curl(PURCHASE_DOMAIN . '/sync/frq/editFrq', $pur_params);
                    $this->apiRecord($event . '，请求采购接口返回：' . $res . '，请求数据：' . json_encode($pur_params)); // 记录接口访问信息
                } catch (\Exception $e) {
                    $OrderModel->rollback();
                    $this->apiRecord($event . '，请求采购接口失败，原因：' . $e->getMessage()); // 记录接口访问信息
                    return $this->apiReturn(44104, '修改明细同步到采购失败，请查看接口');
                }

                $res = json_decode($res, true);
                if (!$res || $res['code'] != 0) {
                    $OrderModel->rollback();
                    return $this->apiReturn(44104, '修改明细同步到采购失败，原因：' . $res['msg']);
                }
            } else {
                try {
                    $res = A('Server/Consume')->updateErpItems($erp_params); // 更新订单明细
                    $this->apiRecord($event . '，请求ERP接口返回：' . $res . '，请求数据：' . json_encode($erp_params)); // 记录接口访问信息
                } catch (\Exception $e) {
                    $OrderModel->rollback();
                    $this->apiRecord($event . '，请求ERP接口失败，原因：' . $e->getMessage()); // 记录接口访问信息
                    return $this->apiReturn(44104, '修改明细同步到ERP失败，请查看接口');
                }

                if ($res['code'] == '4444') {
                    $OrderModel->rollback();
                    return $this->apiReturn(44104, '修改明细同步到ERP失败，原因：' . $res['msg']);
                }
            }
        }

        $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event);

        $OrderModel->commit();

        return $this->apiReturn(0, '修改成功');
    }

    /**
     * 审单校验赠品
     * @param  [type] $order_info  [订单信息]
     * @param  [type] $goods_items [商品明细]
     * @return [type]              [description]
     */
    public function checkOrderGift($order_info, $goods_items)
    {
        $OrderGiftModel = D('OrderGift');

        $goods_ids        = []; // 商品ID集合（非0）
        $goods_amount_arr = []; // 商品对应的商品小计金额

        foreach ($goods_items as $v) {
            if (!$v['goods_id']) continue;

            $goods_ids[] = $v['goods_id'];
            $goods_amount_arr[$v['goods_id']] = $v['goods_amount'];
        }

        if (empty($goods_ids)) return $this->apiReturn(40401, '商品ID不存在');

        $all_goods_info = $this->getGoodsArr($goods_ids); // 获取商品信息

        if (!isset($all_goods_info) || empty($all_goods_info['data'])) return $this->apiReturn(40402, '未获取到商品信息');

        $rules_arr = [];

        foreach ($all_goods_info['data'] as $k => $v) {
            if ($v['has_gift_activity'] == 0) continue; // 无赠品

            $rules_arr[$k]['activity_id']  = $v['gift_activity']['activity_id'];
            $rules_arr[$k]['rules']        = json_encode($v['gift_activity']['items']);
            $rules_arr[$k]['goods_amount'] = $goods_amount_arr[$k];
        }

        if (empty($rules_arr)) return $this->apiReturn(40403, '当前商品不存在赠品信息');

        /**
         * 1. 若返回无赠品且数据库已存在赠品，则取消数据库已存在的赠品;
         * 2. 若返回有赠品且数据库已存在赠品：
         *     2.1 根据activity_id，判断数据库赠品是否存在于当前返回的赠品中，不存在则取消，存在则判断amount金额是否一致，一致且状态为已取消则更新状态为1，不一致则取消并新增一条赠品信息;
         *     2.2 根据返回的赠品信息activity_id，判断当前返回的赠品是否存在于数据库赠品中，不存在则新增;
         *
         * 3. 若返回有赠品且数据库不存在赠品，则新增
         */

        $all_gift_info   = A('Order/Cart')->getCartGift([], $rules_arr); // 获取赠品信息
        $exists_activity = $OrderGiftModel->where(['order_id' => $order_info['order_id']])->field('id, activity_id, gift_info, status')->select(); // 获取已存在赠品

        if (empty($all_gift_info) && !empty($exists_activity)) { // 若返回无赠品，则取消已存在的
            $OrderGiftModel->where(['order_id' => $order_info['order_id']])->save(['status' => -1, 'update_time' => time()]);
            return $this->apiReturn(0, '所有赠品已取消');
        }

        $msg_text = ''; // 赠品操作记录

        if (!empty($exists_activity)) {
            $activity_ids        = array_column($all_gift_info, 'activity_id'); // 赠品activity_id集合
            $exists_activity_ids = array_column($exists_activity, 'activity_id'); // 已存在的赠品activity_id集合

            $gift_add = [];
            foreach ($exists_activity as $act) {
                $gift_info = json_decode($act['gift_info'], true);

                if (!in_array($act['activity_id'], $activity_ids)) { // 若数据库中的activity_id不存在于当前赠品信息，则更新状态为已取消
                    if ($act['status'] == -1) continue;

                    $OrderGiftModel->where(['id' => $act['id']])->save(['status' => -1, 'update_time' => time()]);
                    $msg_text .= '赠品（' . $gift_info['item_name'] . '）已取消，';
                    continue;
                }

                foreach ($all_gift_info as $gift) {
                    if ($act['activity_id'] != $gift['activity_id']) continue;


                    if ($gift_info['amount'] == $gift['amount']) {
                        if ($act['status'] != -1) continue;

                        $OrderGiftModel->where(['id' => $act['id']])->save(['status' => 1, 'update_time' => time()]);
                        $msg_text .= '更新赠品（' . $gift['item_name'] . '），';
                        continue;
                    }

                    // 金额不一致，取消并新增
                    if ($act['status'] != -1) {
                        $OrderGiftModel->where(['id' => $act['id']])->save(['status' => -1, 'update_time' => time()]);
                        $msg_text .= '赠品（' . $gift_info['item_name'] . '）已取消，';
                    }

                    // 判断该赠品是否存在于表中，存在则不新增
                    $res = array_filter($exists_activity, function ($v) use ($gift) {
                        $rule = json_decode($v['gift_info'], true);
                        return $v['activity_id'] == $gift['activity_id'] && $rule['amount'] == $gift['amount'];
                    });

                    if ($res) continue;

                    $temp = [];
                    $temp['order_id']    = $order_info['order_id'];
                    $temp['order_sn']    = $order_info['order_sn'];
                    $temp['user_id']     = $order_info['user_id'];
                    $temp['activity_id'] = $gift['activity_id'];
                    $temp['gift_info']   = json_encode($gift);
                    $temp['create_time'] = time();

                    $gift_add[] = $temp;

                    $msg_text .= '新增赠品（' . $gift['item_name'] . '），';
                }
            }

            foreach ($all_gift_info as $gift) {
                if (in_array($gift['activity_id'], $exists_activity_ids)) { // 存在则过滤
                    continue;
                }

                $temp = [];
                $temp['order_id']    = $order_info['order_id'];
                $temp['order_sn']    = $order_info['order_sn'];
                $temp['user_id']     = $order_info['user_id'];
                $temp['activity_id'] = $gift['activity_id'];
                $temp['gift_info']   = json_encode($gift);
                $temp['create_time'] = time();

                $gift_add[] = $temp;

                $msg_text .= '新增赠品（' . $gift['item_name'] . '），';
            }

            !empty($gift_add) && $OrderGiftModel->addAll($gift_add);
        } else {
            $gift_add = [];
            foreach ($all_gift_info as $gift) {
                $temp = [];
                $temp['order_id']    = $order_info['order_id'];
                $temp['order_sn']    = $order_info['order_sn'];
                $temp['user_id']     = $order_info['user_id'];
                $temp['activity_id'] = $gift['activity_id'];
                $temp['gift_info']   = json_encode($gift);
                $temp['create_time'] = time();

                $gift_add[] = $temp;

                $msg_text .= '新增赠品（' . $gift['item_name'] . '），';
            }

            !empty($gift_add) && $OrderGiftModel->addAll($gift_add);
        }

        return $this->apiReturn(0, rtrim($msg_text, '，'));
    }

    /**
     * 计算每条明细商品优惠单价
     * @param  [type] $order_id     [订单ID]
     * @param  array  $amount       [各项金额]
     *                order_amount  [订单总额]
     *                coupon_amount [优惠券]
     *                extend_fee    [附加费]
     * @param  [type] $items        [商品明细]
     * @param  array  $extend_items [附加费明细]
     * @return [type]               [description]
     */
    public function countItemsPrice($order_id, $amount,  $items, $extend_items = [])
    {
        //附加费按供应商平摊
        // $count = array();
        // foreach ($items as &$v) {
        //     $key = getSuppKey($v['supplier_id'], $v['canal']);

        //     if (isset($extend_items[$key])) {
        //         //分组供应商最后的附加费
        //         if ($extend_items[$key]['count'] != $count[$key]) {
        //             $v['extend_price'] = price_format(($v['goods_price'] * $v['goods_number'] / $extend_items[$key]['amount']) * $extend_items[$key]['extend_fee'], 0, 4);
        //             $extend_items[$key]['surplus_amount'] += $v['extend_price'];
        //         } else {
        //             $v['extend_price'] = $extend_items[$key]['extend_fee'] - $extend_items[$key]['surplus_amount'];
        //         }

        //         $count[$key] += 1;
        //     } else {
        //         $v['extend_price'] = 0;
        //     }
        // }

        // $sum_item_pay_amount = 0;//累加除最后一行实付金额
        // $item_pay_amount = 0;

        // $end_key = end(array_keys($items));

        // 订单总额 - 附加费 + 已删除的商品附加费总额
        // $pre_amount = $order_amount - $extend_fee + $del_extend_fee;

        //附加费按供应商平摊
        // $items = $this->avgPriceToItems($amount['extend_fee'], $items, $extend_items, 'extend_price');
        // //优惠券均摊
        // $items = $this->avgPriceToItems(abs($amount['coupon_price']), $items);
        // //活动优惠均摊
        // $items = $this->avgPriceToItems(abs($amount['activity_price']), $items, 5, 'ac_type');
        // //均摊单个商品优惠单价
        // $items = $this->avgSinglePrice($amount['order_amount'], $items);

        $datas['list']           = $items;
        $datas['extend_items']   = $extend_items;
        $datas['extend_fee']     = $amount['extend_fee'];
        $datas['coupon_price']   = abs($amount['coupon_price']);
        $datas['activity_price'] = abs($amount['activity_price']);
        $datas['shipping_price'] = $amount['shipping_price'];
        $datas['order_amount']   = $amount['order_amount'];

        $res = $this->getAvgPrice($datas);

        // dump($items);
        // die();
        foreach ($res['list'] as $k => $item) {
            // if ($k == $end_key) { //最后一条商品明细
            //     $single_pre_price = price_format(($item['extend_price'] / $item['goods_number']) + (($pre_amount - $sum_item_pay_amount) / $item['goods_number']), 0, 8); // 将对应供应商附加费分摊到单个商品单价 + 除附加费外单个商品实际单价
            // } else {
            //     $item_pay_amount = ($item['goods_price'] * $item['goods_number'] / $goods_amount) * $pre_amount;
            //     $single_pre_price = price_format(($item['extend_price'] / $item['goods_number']) + ($item_pay_amount / $item['goods_number']), 0, 8);
            // }

            // $item['single_pre_price'] = $single_pre_price;
            $save = [
                'extend_price'          => $item['extend_price'],
                'preferential_price'    => $item['preferential_price'],
                'single_pre_price'      => $item['single_pre_price'],
                'extra_price'           => $item['extra_price'],
                'goods_discount_amount' => $item['goods_discount_amount'],
            ];
            $map = array(
                'order_id' => $order_id,
                'rec_id' => $item['rec_id'],
            );

            D('OrderItems')->where($map)->save($save);

            // $sum_item_pay_amount += $item_pay_amount;
        }
    }

    // 后台订单 申请再次调价
    public function applyAdjust()
    {
        $order_id = I('request.order_id', 0, 'intval'); // 订单id
        $operator_id = I('request.operator_id', 0, 'intval'); // 操作人id
        $OrderModel = D('Order');
        $OrderItemsModel = D('OrderItems');
        $OrderExtendModel = D('OrderExtend');
        $OrderActionLogModel = D('OrderActionLog');
        $OrderModel->startTrans();

        $order_info = $OrderModel->getInfo($order_id);
        $order_items_info = $OrderItemsModel->getOrderList($order_id, '', null);

        if (!$order_info || !$order_id) {
            return $this->apiReturn(44003, '该订单不存在');
        }

        if ($order_info['status'] == 3 || $order_info['status'] == 4) {
            return $this->apiReturn(44005, '客户已经支付了，无法进行调价了', '');
        }

        // 更新订单表
        $data['confirm_time'] = time();
        $data['pay_time'] = 0; // 审核时填写的限制付款时间
        $data['status'] = 1;

        $update = $OrderModel->where(array('order_id' => $order_id))->data($data)->save();

        if ($update === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44007, '再次调价，更新订单表失败', '');
        }

        // 更新明细表
        if ($order_info['sale_type'] == 2) { // 销售类型为预售时，清空交期
            $items['delivery_time'] = '';
        }

        $items['is_lock'] = 0;

        $updateItems = $OrderItemsModel->where(array('order_id' => $order_id))->data($items)->save();

        if ($updateItems === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44007, '再次调价，更新订单明细表失败', '');
        }

        $extend['status'] = 0;

        $updateExtend = $OrderExtendModel->where(array('order_id' => $order_id))->data($extend)->save();

        if ($updateExtend === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44007, '再次调价，更新订单扩展表失败', '');
        }

        // 解锁明细库存
        $lock_item_ids = array();
        $lock_goods = array();

        foreach ($order_items_info as $v) {
            if ($v['is_lock']) {
                $lock_item_ids[$v['goods_id']] = $v['goods_number'];
                $lock_goods[] = $v['goods_name'];
            }
        }

        $event = '再次调价';

        if (!empty($lock_item_ids)) {
            $reduce = $order_info['sale_type'] == 1 ? false : true;

            $res = $this->unlockSku($order_id, $lock_item_ids, $reduce);

            if ($res['errcode'] != 0) {
                $OrderModel->rollback();
                return $this->apiReturn(44007, '解锁库存失败');
            } else {
                $event .= '，已解锁库存的型号：' . implode('、', $lock_goods);
                $actionLog = $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event);

                $OrderModel->commit();
            }
        } else {
            $OrderModel->commit();

            $actionLog = $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event);
        }

        return $this->apiReturn(0, '成功', '');
    }

    /**
     * [adminCheck description]
     * 用于取消订单、后台订单审核不通过 (整合联营、自营)
     * @return [type] [description]
     */
    public function cancel()
    {
        $sale_id       = I('request.sale_id', 0, 'intval'); // 已分配业务员ID
        $cancel_reason = I('request.cancel_reason', ''); // 取消原因
        $operator_id   = I('request.operator_id', 0, 'intval'); // 后台操作人ID
        $type          = I('request.type', 1, 'intval'); // 1-前台取消订单，2-后台取消订单，3-后台审核不通过，4-后台填写取消原因，5-售后取消

        if ($type == 1) {
            $order_id = I('request.id', 0, 'intval');
        } else {
            $order_id = I('request.order_id', 0, 'intval'); // 订单ID
        }

        $OrderModel            = D('Order');
        $OrderItemsModel       = D('OrderItems');
        $OrderPriceModel       = D('OrderPrice');
        $OrderExtendModel      = D('OrderExtend');
        $UserMainModel         = D('UserMain');
        $OrderRefundModel      = D('OrderRefund');
        $OrderRefundItemsModel = D('OrderRefundItems');
        $OrderActionLogModel   = D('OrderActionLog');
        $OrderServiceModel     = D('OrderService');

        $user_id = $this->getUidByAdmin();
        if (!$OrderModel->checkOrderByUser($user_id, $order_id)) {
            return $this->apiReturn(21004, '未找到相关订单');
        }

        $order_info = $OrderModel->getInfo($order_id);

        if (empty($order_info)) return $this->apiReturn(21004, '未找到相关订单');
        if ($order_info['status'] == -1 && $type != 4) return $this->apiReturn(21005, '当前订单已取消');
        if ($order_info['status'] > 4) return $this->apiReturn(21007, '当前状态无法取消订单');

        // 记录类型区分
        switch ($type) {
            case 1:
                $event = '前台取消订单';
                $log_user_id = $order_info['user_id'];
                $log_type = 1;
                break;
            case 2:
                $event = '后台取消订单';
                $log_user_id = $operator_id;
                $log_type = 2;
                break;
            case 3:
                $event = '后台审核不通过';
                $log_user_id = $operator_id;
                $log_type = 2;
                break;
            case 4:
                $event = '后台填写订单取消原因';
                $log_user_id = $operator_id;
                $log_type = 2;
                break;
            case 5:
                $event = '售后取消订单';
                $log_user_id = $operator_id;
                $log_type = 2;
                break;
        }

        $saveData = [];

        if ($type == 4) { // 后台填写取消原因
            $event .= $cancel_reason ? '，原因：' . $cancel_reason : '';

            $saveData['cancel_reason'] = $cancel_reason;

            $OrderModel->where(array('order_id' => $order_id))->data($saveData)->save();
            $OrderActionLogModel->addLog($order_id, $log_user_id, $log_type, $event);

            return $this->apiReturn(0, '成功', '');
        }

        // if ($type != 5) { // 非售后取消，判断是否存在售后单，存在则不能直接取消
        //     $count = $OrderServiceModel->where(['order_id' => $order_id])->count();
        //     if ($count) return $this->apiReturn('21008', '取消失败，已存在售后单');
        // }

        $this->cancel_coupon($order_id); //取消付款后增送的优惠券

        // 订单若使用优惠券，则返还
        $user_coupon_id = D('UserCoupon')->where(['order_id' => $order_id, 'status' => 1])->getField('user_coupon_id');

        if ($user_coupon_id) {
            $res = $this->returnCoupon($order_id, $order_info['user_id']); // 优惠券状态更改为未使用

            if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], '返还优惠券失败');

            $coupon_price = $OrderPriceModel->getPreferentialPrice($order_id); // 优惠金额

            $event .= '，退回优惠券，优惠券金额：' . abs($coupon_price);
        }

        $OrderModel->startTrans();

        $saveData['cancel_time']   = time();
        $saveData['status']        = -1;
        $saveData['cancel_reason'] = $cancel_reason;

        $order_extend = $OrderExtendModel->getInfo($order_id);

        if ($order_info['order_goods_type'] == 1) {
            $order_items = $OrderItemsModel->getOrderList($order_id, '', null);

            // 联营订单，已同步到ERP，非测试环境下执行
            if ($type != 5 && strpos($_SERVER['HTTP_REFERER'], 'sz') === false) {
                if ($order_info['erp_order_id']) {
                    $erp_params = [];
                    $erp_params['TYPE']    = 10; // 取消ERP订单
                    $erp_params['ORDERID'] = $order_info['erp_order_id'];
                    $erp_params['NUMBER']  = $order_info['order_sn'];

                    // 删除采购需求单
                    foreach ($order_items as $v) {
                        if ($v['order_goods_type'] == 2 || $v['is_purchase'] == -1 || $v['admin_goods_type'] == 4) {
                            continue; // 过滤自营、寄售商品或不需采购的
                        }

                        $rec_ids[] = $v['rec_id'];
                    }

                    $FrqModel = D('Common/Frq');
                    $count = $FrqModel->isExistsFrq($rec_ids); // 为兼容历史订单，需先判断是否存在采购需求单

                    if (!empty($rec_ids) && $count) {
                        // if (!empty($rec_ids) ) {
                        $push_pur_data = [
                            'type' => 10, // 1反审核删 10取消关闭
                            'rec_ids' => $rec_ids,
                            'erp_params' => $erp_params,
                        ];

                        $res = post_curl(PURCHASE_DOMAIN . '/sync/frq/delFrq', $push_pur_data);

                        if ($res === false) return $this->apiReturn(44104, '删除采购需求单失败，请查看接口');

                        $res = json_decode($res, true);

                        if ($res['code'] != 0) return $this->apiReturn($res['code'], $res['msg']);

                        $event .= '，已删除ERP订单，已删除采购需求单';
                    } else {
                        $res = A('Server/Consume')->pushDeleteOrder($erp_params); // 删除ERP上订单

                        if ($res === false) return $this->apiReturn(44103, 'ERP删除订单失败');

                        $event .= '，已删除ERP订单';
                    }
                }

                $unlock_items = [];
                $unlock_goods = [];
                $joint_unlock_skus   = []; // 专营现货需加回库存商品
                $joint_unlock_goods = [];
                $consignment_skus = []; // 寄售商品
                $consignment_pur_params = [ // 自营优先发货的寄售商品需通知采购系统走逆向退货流程
                    'is_stock_out' => 0,
                    'rma_reason' => '反审核销售订单',
                    'remark' => '',
                    'is_self_return' => 1,
                ];
                $unlock_stock_up_goods = []; // 备货商品需解锁WMS库存

                foreach ($order_items as $k => $v) {
                    // if ($v['order_goods_type'] == 1) continue;

                    if ($v['admin_goods_type'] == 4) {
                        $consignment_skus[] = $v['rec_id'];
                    }

                    if ($v['is_lock'] == 0) continue;

                    // 专营现货 或 寄售
                    if ($v['canal'] == C('joint_supplier_code') || $v['admin_goods_type'] == 4) {
                        $joint_unlock_skus[$k]['goods_id'] = $v['goods_id'];
                        $joint_unlock_skus[$k]['num'] = $v['goods_number'];

                        $joint_unlock_goods[$k]['goods_name']   = $v['goods_name'];
                        $joint_unlock_goods[$k]['goods_number'] = $v['goods_number'];
                    }

                    if ($v['order_goods_type'] == 2) {
                        $unlock_items[$v['goods_id']] = $v['goods_number'];

                        $unlock_goods[$k]['goods_name']   = $v['goods_name'];
                        $unlock_goods[$k]['goods_number'] = $v['goods_number'];
                    }

                    // 猎芯科技 & 猎芯备货 || 香港深贸 & 香港备货
                    if (($order_info['sale_com_id'] == 1 && $v['supplier_id'] == 1689) || ($order_info['sale_com_id'] == 2 && $v['supplier_id'] == 1690)) {
                        $unlock_stock_up_goods[$k]['order_items_id'] = $v['rec_id'];
                        $unlock_stock_up_goods[$k]['goods_sn'] = $v['goods_sn'];
                        $unlock_stock_up_goods[$k]['lock_num'] = $v['goods_number'];
                        $unlock_stock_up_goods[$k]['order_sn'] = $order_info['order_sn'];
                    }
                }

                // 拼单发货查询调拨单
                if ($order_extend['zy_delivery_type'] == 2) {
                    $WmsAllocateModel = D('WmsAllocate');
                    $allocate = $WmsAllocateModel->getAllocateByOrderId($order_id);

                    if ($allocate && !in_array($allocate['allocate_status'], [1, 5])) {
                        $allocate_status_val = data_get(C('ALLOCATE_STATUS_ALL'), $allocate['allocate_status']);
                        return $this->apiReturn(44007, '取消失败，当前订单的调拨单状态为' . $allocate_status_val);
                    }
                }

                // 已同步 且 （自营优先发货 或 拼单发货且无调拨单） 需推送采购系统，生成退货单
                if ($order_extend['erp_sn'] && ($order_extend['zy_delivery_type'] == 1 || ($order_extend['zy_delivery_type'] == 2 && empty($allocate))) && $consignment_skus) {
                    foreach ($consignment_skus as $sku) {
                        $tmp = [
                            'order_item_id' => $sku,
                        ];

                        $consignment_pur_params['return_material_items'][] = $tmp;

                        // 异步推送采购系统
                        $this->pushAsynQueue('/sync/returnMaterial/addReturnMaterialByOrderItemId', $consignment_pur_params, 'lie_queue_pur');
                    }

                    $event .= '，寄售商品生成退货单，明细ID：' . implode('，', $consignment_skus);
                }

                // 自营解锁库存
                if ($order_info['status'] > 1 && !empty($unlock_items)) {
                    $reduce_wms = $order_extend['wms_order_last_time'] > 0 ? true : false; // 若大于0，则自营商品已同步到WMS，需删除WMS订单

                    $res = $this->unlockSku($order_id, $unlock_items, false, $reduce_wms);

                    if (!$res || $res['errcode'] != 0) return $this->apiReturn(44007, '解锁库存失败 ' . $res['errmsg']);

                    $event .= '，已解锁库存的型号：';
                    foreach ($unlock_goods as $v) {
                        $event .= '[' . $v['goods_name'] . '；' . $v['goods_number'] . '] ';
                    }
                }

                // 专营现货解库
                if (!empty($joint_unlock_skus)) {
                    $res = $this->jointSkuAction($order_id, $joint_unlock_skus, 2);

                    if (!$res || $res['code'] != 0) {
                        return $this->apiReturn(44007, '专营现货解库失败，' . $res['msg']);
                    }

                    $event .= '，已解锁专营库存的型号：';
                    foreach ($joint_unlock_goods as $v) {
                        $event .= '[' . $v['goods_name'] . '；' . $v['goods_number'] . '] ';
                    }
                }

                // 取消调拨单
                if (!empty($allocate) && in_array($allocate['allocate_status'], [1, 5])) {
                    $CmsModel = D('Order/Cms');
                    $sale_name = $CmsModel->getUserName($operator_id);

                    $wms_params = [
                        'order_id' => $order_id,
                        'operator_id' => $operator_id,
                        'operator_name' => $sale_name,
                    ];

                    $wms_url = WMS_DOMAIN . '/sync/allocate/cancelFromOrder';
                    $res = post_curl($wms_url, $wms_params);
                    $res = json_decode($res, true);

                    if (!$res) {
                        return $this->apiReturn(44201, '取消WMS调拨单接口请求失败');
                    }

                    if ($res['code'] != 0) {
                        return $this->apiReturn(44201, '取消WMS调拨单失败，原因：' . $res['msg']);
                    }

                    $event .= '，已取消WMS调拨单';
                }

                // 备货商品解锁库存
                if (!empty($unlock_stock_up_goods)) {
                    $wms_unlock_params = [
                        'company_id' => $order_info['sale_com_id'],
                        'create_uid' => $operator_id,
                        'create_name' => $sale_name,
                        'un_lock_type' => 1,
                        'un_lock_list' => $unlock_stock_up_goods
                    ];

                    $wms_url = WMS_DOMAIN . '/open/hyUnLockStock';
                    $res = post_curl($wms_url, $wms_unlock_params);
                    $res = json_decode($res, true);

                    if (!$res) {
                        return $this->apiReturn(44202, '解锁WMS备货商品接口请求失败');
                    }

                    if ($res['code'] != 0) {
                        return $this->apiReturn(44202, '解锁WMS备货商品失败，原因：' . $res['msg']);
                    }

                    $event .= '，已解锁WMS备货商品';
                }
            }

            // 判断是否存在自营单
            // $order_extend = $OrderExtendModel->getInfo($order_id);

            // if ($order_extend['erp_sn']) {
            //     $map = [];
            //     $map['o.order_goods_type'] = 2;
            //     $map['o.status']           = ['neq', -1];
            //     $map['o.is_type']          = 0;
            //     $map['oe.erp_sn']          = $order_extend['erp_sn'];

            //     $self_order_id = $OrderModel->alias('o')
            //                    ->join('LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id')
            //                    ->where($map)
            //                    ->getField('o.order_id');

            //     // 自营订单
            //     if ($self_order_id) {
            //         $res = $this->cancelSelfLock($self_order_id);
            //         if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], '取消对应的自营订单，'.$res['err_msg']);

            //         $event .= '，已取消对应的自营订单';
            //         $self_event = '联营取消订单，解锁库存成功';

            //         $OrderModel->where(array('order_id' => $self_order_id))->data($saveData)->save();
            //         $OrderActionLogModel->addLog($self_order_id, $log_user_id, $log_type, $self_event);
            //     }
            // }

            // 联营取消临时保存信息
            $extendTemp = [];
            $extendTemp['erp_sn']           = '';
            $extendTemp['is_manager_audit'] = 0;
            $extendTemp['manager_id']       = 0;
            $extendTemp['temp_save_info']   = '';

            $OrderExtendModel->where(array('order_id' => $order_id))->save($extendTemp);

            // 非售后取消操作，生成售后单 (已付款、未发货)  --- 订单系统付款后不能取消，则这里不用生成售后单 20210126
            // if ($type != 5 && in_array($order_info['status'], [3, 4])) {
            //     $res = A('Order/OrderService')->createOrderService($order_id, $operator_id);
            //     if ($res['err_code'] != 0) {
            //         $OrderModel->rollback();
            //         return $this->apiReturn(44105, '生成售后单失败');
            //     }

            //     $event .= '，生成售后单成功，售后单号：'.$res['data'];
            // }
        } else {
            $res = $this->cancelSelfLock($order_id);
            if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

            $event .= '，解锁库存成功';

            // 样片取消，退回领取机会
            $order_extend = $OrderExtendModel->getInfo($order_id);

            if ($order_extend['business_type'] == 1) {
                $sample_result = D('userSampleApply')->where(array('order_id' => $order_id))->find();

                $quota = !empty($sample_result) ? $sample_result['quota'] : 0;

                $res = D('UserInfo')->where(['user_id' => $order_info['user_id']])->setInc('apply_count', $quota);

                if ($res === false) {
                    $OrderModel->rollback();
                    return $this->apiReturn(44104, '样片订单退回领取机会失败');
                }
            }
        }

        // 更新订单表
        $saveData['erp_order_id'] = '';
        $saveData['sale_order_status'] = -2;
        $updateOrder = $OrderModel->where(array('order_id' => $order_id))->data($saveData)->save();

        if ($updateOrder === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44101, $event . '更新订单表失败', '');
        }

        // 清空ERP订单ID
        $OrderItemsModel->where(['order_id' => $order_id])->save(['erp_rec_id' => '']);

        $event .= $cancel_reason ? '，原因：' . $cancel_reason : '';

        // 订单取消时记录操作
        $actionLog = $OrderActionLogModel->addLog($order_id, $log_user_id, $log_type, $event);

        if ($actionLog === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44102, $event . '新增记录操作失败', '');
        }

        // 取消审核流程
        $push_sale_data = [
            'order_id' => $order_id,
        ];
        $res = json_decode(post_curl(SALE_DOMAIN . '/open/cancelApprove', $push_sale_data), true);
        if (!$res || $res['code'] != 0) {
            $OrderModel->rollback();
            return $this->apiReturn(44103, '取消审核流程失败');
        }

        /***  更新样片库存  ***/
        if ($order_extend['business_type'] == 1) {
            $order_items = $OrderItemsModel->where(['order_id' => $order_id])->find();

            $res = D('Sample')->addSampleStock($order_items['goods_id'], $order_items['goods_number']);
            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(26012, '新增样片库存失败');
            }

            $s_goods_data = json_decode(S_sample_goods($order_items['goods_id']), true);
            $s_goods_data['sample_stock'] = $s_goods_data['sample_stock'] + $order_items['goods_number'];
            $s_goods_data['update_time'] = time();

            S_sample_goods($order_items['goods_id'], $s_goods_data);
        }

        $OrderModel->commit();

        // 添加用户标签
        $count_where['user_id'] = $order_info['user_id'];
        $count_where['status']  = ['neq', -1]; // 过滤取消订单
        $count_where['is_type'] = 0; // 真实订单

        $order_count = $OrderModel->where($count_where)->count();

        $first_order_id = 0;

        // 判断用户订单数（不包含已取消订单）
        if ($order_count == 1) {
            $user_tags['is_new'] = 1; // 新用户

            $count_where['order_goods_type'] = 1; // 联营订单
            $first_order_id = $OrderModel->where($count_where)->order('create_time')->getField('order_id'); // 获取用户未取消的第一笔订单
        } else if ($order_count > 1) {
            $user_tags['is_new'] = 2; // 老用户

            $count_where['order_goods_type'] = 1; // 联营订单
            $first_order_id = $OrderModel->where($count_where)->order('create_time')->getField('order_id'); // 获取用户未取消的第一笔订单
        } else {
            $user_tags['is_new'] = 0; // 用户
        }

        // 更新用户标签
        $updateUser = $UserMainModel->where(array('user_id' => $order_info['user_id']))->save($user_tags);
        if ($updateUser === false) return $this->apiReturn(44102, $event . '更新用户标签失败', '');

        // 添加用户标签到缓存
        $s_user_info = S_user($order_info['user_id']);
        $s_user_info['is_new'] = $user_tags['is_new'];
        S_user($order_info['user_id'], $s_user_info);

        // 联营更新第一笔订单
        if ($first_order_id) {
            $updateNewExtend = $OrderExtendModel->where(array('order_id' => $first_order_id))->save(['is_new' => 1]);
            if ($updateNewExtend === false) return $this->apiReturn(44102, $event . '更新第一笔订单是否为新订单失败', '');
        }

        // $this->sendMsgAtCheck($order_id, $sale_id); // 发送短信
        // 神策同步标记
        $OrderExtendModel->where(array('order_id' => $order_id))->setField('sensors_syn', 4);
        //限购 取消
        if (date("Y-m-d", $order_info["create_time"]) == date("Y-m-d")) {
            $this->returnBackUserGoodsLimit($order_id, $order_info["user_id"]);
        }
        return $this->apiReturn(0, '取消成功', '');
    }

    // 取消自营锁库（或删除WMS订单）
    public function cancelSelfLock($order_id)
    {
        $OrderModel       = D('Order');
        $OrderItemsModel  = D('OrderItems');
        $OrderExtendModel = D('OrderExtend');

        $order_info = $OrderModel->getInfo($order_id);

        //判断是否需要请求wms取消订单
        $wms_order  = $OrderExtendModel->getFieldByOrderId($order_id, 'wms_order');
        $reduce_wms = $wms_order > 0 ? true : false;

        //获取基石是否已锁库存
        $skus = $OrderItemsModel->getItemsMap($order_id, $order_info['sale_type']);
        if (empty($skus)) $this->apiReturn(0, '');

        $lock = $this->getLockSku($order_id, $skus);
        if ($lock['errcode'] != 0) return $this->apiReturn($lock['errcode'], '获取基石库存：' . $lock['errmsg']);

        $lock_num = array_sum($lock['data']);
        if ($lock_num <= 0) $this->apiReturn(0, '');

        //解锁基石库存
        $skus = $OrderItemsModel->getItemsStockMap($order_id, $order_info['sale_type']);
        $res  = $this->unlockSku($order_id, $skus, false, $reduce_wms);
        if ($res['errcode'] != 0) return $this->apiReturn($res['errcode'], $res['errmsg']);

        return $this->apiReturn(0, '成功');
    }

    /*
     * ******** 备份
     * 废弃
     */
    private function __cancel__()
    {
        exit;
        $sale_id       = I('request.sale_id', 0, 'intval'); // 已分配业务员ID
        $cancel_reason = I('request.cancel_reason', ''); // 取消原因
        $operator_id   = I('request.operator_id', 0, 'intval'); // 后台操作人ID
        $type          = I('request.type', 1, 'intval'); // 1-前台取消订单，2-后台取消订单，3-后台审核不通过，4-后台填写取消原因

        if ($type == 1) {
            $user_id  = cookie('uid');
            $order_id = I('request.id', 0, 'intval');
        } else {
            $order_id = I('request.order_id', 0, 'intval'); // 订单ID
        }

        $OrderModel            = D('Order');
        $OrderItemsModel       = D('OrderItems');
        $OrderPriceModel       = D('OrderPrice');
        $OrderExtendModel      = D('OrderExtend');
        $UserMainModel         = D('UserMain');
        $OrderRefundModel      = D('OrderRefund');
        $OrderRefundItemsModel = D('OrderRefundItems');
        $OrderActionLogModel   = D('OrderActionLog');

        $order_info = $OrderModel->getInfo($order_id);

        if (empty($order_info)) return $this->apiReturn(21004, '未找到相关订单');
        if ($order_info['status'] == -1 && $type != 4) return $this->apiReturn(21005, '当前订单已取消');
        if ($order_info['status'] > 4) return $this->apiReturn(21007, '当前状态无法取消订单');

        // 记录类型区分
        switch ($type) {
            case 1:
                $event = '前台取消订单';
                $log_user_id = $order_info['user_id'];
                $log_type = 1;
                break;
            case 2:
                $event = '后台取消订单';
                $log_user_id = $operator_id;
                $log_type = 2;
                break;
            case 3:
                $event = '后台审核不通过';
                $log_user_id = $operator_id;
                $log_type = 2;
                break;
            case 4:
                $event = '后台填写订单取消原因';
                $log_user_id = $operator_id;
                $log_type = 2;
                break;
        }

        $param = [];
        $param['TYPE']    = 1;
        $param['ORDERID'] = $order_info['erp_order_id'];
        $param['NUMBER']  = $order_info['order_sn'];

        // 联营订单，状态小于4，已同步到ERP，非测试环境下执行
        if ($order_info['order_goods_type'] == 1 && $order_info['status'] < 4 && $order_info['erp_order_id'] && strpos($_SERVER['HTTP_REFERER'], 'sz') === false) {
            $res = A('Server/Consume')->pushDeleteOrder($param); // 删除ERP上订单

            if ($res === false) return $this->apiReturn(44103, 'ERP删除订单失败');

            $event .= '，已删除ERP订单';
        }

        $OrderModel->startTrans();

        // 联营订单且待发货状态，请求ERP退款申请接口
        if ($order_info['order_goods_type'] == 1 && $order_info['status'] == 4) {
            $refund_id = $OrderRefundModel->where(['order_id' => $order_id, 'refund_type' => 2])->getField('refund_id');

            if ($refund_id) return $this->apiReturn(44002, '已存在退款单，不能取消订单');

            $user_info = S_user($order_info['user_id']);

            // 测试环境、测试账号过滤ERP接口
            if (strpos($_SERVER['HTTP_REFERER'], 'sz') === false && $user_info['is_test'] != 1) {
                // 若为账期订单或预付款为0
                //订单付款方式1全款支付2预付款支付3账期支付
                if ($order_info['order_pay_type'] == 3 || ($order_info['order_pay_type'] == 2 && $order_info['advance_amount'] == 0)) {
                    $res = A('Server/Consume')->pushDeleteOrder($param);

                    if ($res === false) return $this->apiReturn(44003, '请求ERP删除订单接口失败');
                } else {
                    $refund_param = [];
                    $refund_param['NUMBER'] = $order_info['order_sn'];
                    $refund_param['PAYAMOUNT'] = $order_info['order_amount'];

                    $res = A('Server/Consume')->pushOrderRefundApply($refund_param);

                    if ($res === false) return $this->apiReturn(44005, '请求ERP订单退款申请接口失败');
                }
            }

            // 添加退款单
            $refund_id = $OrderRefundModel->createOrderRefund($order_id, $operator_id, $cancel_reason);

            if ($refund_id === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44007, '新增订单退款表失败');
            }

            // 添加退款明细
            $res = $OrderRefundItemsModel->createOrderRefundItems($refund_id, $order_id);

            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44009, '新增订单退款明细表失败');
            }
        }

        //取消付款后增送的优惠券
        $this->cancel_coupon($order_id);

        $saveData = [];

        if ($type != 4) {
            // 优惠券
            $coupon = D('UserCoupon')->where(['order_id' => $order_id, 'status' => 1])->find();

            // 返还优惠券
            if ($coupon) {
                $res = $this->returnCoupon($order_id, $order_info['user_id']); // 优惠券状态更改为未使用

                if ($res['err_code'] != 0) {
                    $OrderModel->rollback();
                    return $this->apiReturn($res['err_code'], '返还优惠券失败');
                }

                $coupon_price = $OrderPriceModel->getPreferentialPrice($order_id); // 优惠金额

                $event .= '，并退回优惠券，优惠券金额：' . abs($coupon_price);
            }

            $saveData['cancel_time'] = time();
            $saveData['status'] = -1;
        }

        $saveData['cancel_reason'] = $cancel_reason;

        // 更新订单表
        $updateOrder = $OrderModel->where(array('order_id' => $order_id))->data($saveData)->save();

        if ($updateOrder === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44101, $event . '更新订单表失败', '');
        }

        if ($type == 4) { // 后台填写取消原因
            $event .= $cancel_reason ? '，原因：' . $cancel_reason : '';

            // 订单取消时记录操作
            $actionLog = $OrderActionLogModel->addLog($order_id, $log_user_id, $log_type, $event);

            if ($actionLog === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44102, $event . '新增记录操作失败', '');
            }

            $OrderModel->commit();

            return $this->apiReturn(0, '成功', '');
        }

        // 预售订单且待收货状态，更改订单明细状态
        if ($order_info['status'] == 4 && $order_info['sale_type'] == 2) {
            $items = $OrderItemsModel->getOrderList($order_id, '', null);

            foreach ($items as $v) {
                $temp['rec_id'] = $v['rec_id'];
                $temp['status'] = -1; // 已取消
                $temp['deal_time'] = time();

                $postData[] = $temp;
            }

            $data['data'] = urlencode(json_encode($postData));
            $data['timestamp'] = time();

            $res = post_curl(PUR_DOMAIN . '/wmsapi/WebApiPreSaleOrderUpdate?token=' . service_token($data['data'], $data['timestamp']), $data);
            $res = json_decode($res, true);

            if ($res['errcode'] != 0) {
                $OrderModel->rollback();
                return $this->apiReturn($res['errcode'], $res['errmsg']);
            }
        }

        // 自营订单
        if ($order_info['order_goods_type'] == 2) {
            //判断是否需要请求wms取消订单
            $wms_order = $OrderExtendModel->getFieldByOrderId($order_id, 'wms_order');
            $reduce_wms = $wms_order > 0 ? true : false;
            //获取基石是否已锁库存
            $skus = $OrderItemsModel->getItemsMap($order_id, $order_info['sale_type']);

            if ($skus) { // 存在已锁明细
                $lock = $this->getLockSku($order_id, $skus);
                if ($lock['errcode'] != 0) {
                    $OrderModel->rollback();
                    return $this->apiReturn($lock['errcode'], '获取基石库存：' . $lock['errmsg']);
                }
                $lock_num = array_sum($lock['data']);
                if ($lock_num > 0) {
                    //解锁基石库存
                    $skus = $OrderItemsModel->getItemsStockMap($order_id, $order_info['sale_type']);
                    $res = $this->unlockSku($order_id, $skus, false, $reduce_wms);

                    if ($order_info['create_time'] > 1528077600 && $res['errcode'] != 0) { //2018-6-5前 订单没锁定固取消不了订单
                        $OrderModel->rollback();
                        return $this->apiReturn($res['errcode'], $res['errmsg']);
                    }

                    $event .= '，解锁库存成功';
                }
            }

            // 样片取消，退回领取机会
            $extend_info = $OrderExtendModel->getInfo($order_id);

            if ($extend_info['business_type'] == 1) {
                $sample_result = D('userSampleApply')->where(array('order_id' => $order_id))->find();

                $quota = !empty($sample_result) ? $sample_result['quota'] : 0;
                //根据类型来判断,如果是类型为1就代表是普通样片,需要对apply_count增减,否则不需要
                $sample_type = $sample_result['type'];
                if ($sample_type == 1) {
                    $res = D('UserInfo')->where(['user_id' => $order_info['user_id']])->setInc('apply_count', $quota);
                    if ($res === false) {
                        $OrderModel->rollback();
                        return $this->apiReturn(44104, '样片订单退回领取机会失败');
                    }
                }
            }
        } else {
            // 联营取消临时保存信息
            $extendTemp = [];
            $extendTemp['is_manager_audit'] = 0;
            $extendTemp['manager_id']       = 0;
            $extendTemp['temp_save_info']   = '';

            $OrderExtendModel->where(array('order_id' => $order_id))->save($extendTemp);
        }

        $event .= $cancel_reason ? '，原因：' . $cancel_reason : '';

        // 订单取消时记录操作
        $actionLog = $OrderActionLogModel->addLog($order_id, $log_user_id, $log_type, $event);

        if ($actionLog === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44102, $event . '新增记录操作失败', '');
        }

        //自营退款 + 后台取消订单
        if ($order_info['status'] > 2 && $order_info['order_goods_type'] == 2 && $this->auth() && $type == 2) {
            $self_refund_id = $OrderRefundModel->where(['order_id' => $order_id])->getField('refund_id');

            if ($self_refund_id) {
                $OrderModel->rollback();
                return $this->apiReturn(44002, '已存在退款单');
            }

            // 添加退款单
            $refund_id = $OrderRefundModel->createOrderRefund($order_id, $operator_id, $cancel_reason);

            if ($refund_id === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44007, '新增订单退款表失败');
            }

            // 添加退款明细
            $res = $OrderRefundItemsModel->createOrderRefundItems($refund_id, $order_id);

            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44009, '新增订单退款明细表失败');
            }

            // 自营账期订单调整会员账期占用额度
            if ($order_info['order_pay_type'] == 3) {
                $zq_params['user_id']     = $order_info['user_id'];
                $zq_params['diff_amount'] = '-' . $order_info['order_amount'];

                $res = post_curl(CREDIT_DOMAIN . '/credit/setoccupy', $zq_params);
                $res = json_decode($res, true);

                if ($res['errcode'] != 0) {
                    $OrderModel->rollback();
                    return $this->apiReturn(44010, '修改会员账期占用额度失败');
                }
            }

            $OrderModel->commit();

            // 将退款信息推送到财务系统
            $refund_info = $OrderRefundModel->getOrderInfo($refund_id);
            $order_info  = $OrderModel->getInfo($order_id);

            $PayLogModel = D('PayLog');
            $pay_log = $PayLogModel->getInfo($order_id, '*', true);

            $pay_type_map = array(
                '0' => '微信支付',
                '1' => '支付宝',
                '2' => '银联支付(B2B)',
                '3' => '银联支付(B2C)',
                '4' => '账期支付',
                '5' => '京东支付',
                '6' => '交通银行',
                '7' => '恒生银行',
                '8' => '钱包支付',
            );

            $pay_type = array_search($pay_log['pay_name'], $pay_type_map);

            // 用户信息
            $user_info = S_user($order_info['user_id']);

            $datas = array(
                "refund_type"        => 2, // 退款类型：1 退货退款  2 售前退款 3 调整退款
                "order_type"         => 2, // 订单类型 2-自营
                "return_id"          => $refund_info['refund_id'],
                "return_sn"          => $refund_info['refund_sn'],
                "pay_type"           => !is_null($pay_type) ? $pay_type : -1,
                "currency"           => $refund_info['currency'],
                "user_id"            => $refund_info['user_id'],
                "mobile"             => !empty($user_info['mobile']) ? $user_info['mobile'] : '',
                "email"              => !empty($user_info['email']) ? $user_info['email'] : '',
                "order_id"           => $refund_info['order_id'],
                "order_sn"           => $refund_info['order_sn'],
                "sale_id"            => "",
                "order_amount"       => $order_info['order_amount'],
                "order_pay_amount"   => abs($OrderPriceModel->getClientPayed($order_id)),
                "refund_amount"      => $refund_info['pay_amount'] - $refund_info['price_fall'],
                "create_name"        => "",
                "refund_reason"      => $refund_info['refund_reason'],
                "refund_shipping"    => "",
                "refund_shipping_no" => "",
                "company_name"       => "",
                "bank_account"       => "",
                "bank_name"          => "",
                "bank_sub_name"      => "",
                "bank_user"          => "",
                "status"             => 1, // 默认1，线上退款成功标记为3
            );

            $refund_items_info = $OrderRefundItemsModel->getInfo($order_id);

            $datas['items'] = [];

            foreach ($refund_items_info as $k => $v) {
                $datas['items'][$k]['rec_id']        = $v['rec_id'];
                $datas['items'][$k]['return_id']     = $v['refund_id'];
                $datas['items'][$k]['goods_id']      = $v['goods_id'];
                $datas['items'][$k]['goods_name']    = $v['goods_name'];
                $datas['items'][$k]['brand_id']      = $v['brand_id'];
                $datas['items'][$k]['brand_name']    = $v['brand_name'];
                $datas['items'][$k]['supplier_id']   = $v['supplier_id'];
                $datas['items'][$k]['supplier_name'] = $v['supplier_name'];
                $datas['items'][$k]['sku_name']      = $v['sku_name'];
                $datas['items'][$k]['single_price']  = $v['single_pre_price'];
                $datas['items'][$k]['send_num']      = 0;
                $datas['items'][$k]['return_num']    = $v['refund_num'];
                $datas['items'][$k]['goods_packing'] = '';
                $datas['items'][$k]['goods_class']   = '';
                $datas['items'][$k]['goods_unit']    = '';
            }

            // 退款
            $res = A('Order/Refund')->order($refund_id);
            if ($res['err_code'] != 0) {
                $CmsModel = D('Order/Cms');
                $web_user_id = $CmsModel->getUserWebUserId($operator_id);
                if (!empty($web_user_id)) {
                    try {
                        $this->sendOrderMsg('order_refund_notice', json_encode(array('msg' => str_replace('</br>', '；', $res['err_msg']))), array($web_user_id));
                    } catch (Exception $e) {
                    }
                }
                D('OrderActionLog')->addLog($order_id, $log_user_id, $log_type, $res['err_msg']);
                $OrderExtendModel->where(array('order_id' => $order_id))->setField('sensors_syn', 4);
                //财务退款申请
                $res = $this->refundSyn($datas);
                if ($res['err_code'] !== 0 && $res['err_code'] !== 1004) {
                    return $this->apiReturn(-1, $refund_info['refund_sn'] . '推送生成退款单失败');
                }

                return $this->apiReturn($res['err_code'], '后台取消成功，退款异常<br/>' . $res['err_msg']);
            }

            // 线上退款成功 --- 中金支付的退款另外有脚本跑
            $datas['status'] = 3;
            //财务退款申请
            $res = $this->refundSyn($datas);
            if ($res['err_code'] !== 0 && $res['err_code'] !== 1004) {
                return $this->apiReturn(-1, $refund_info['refund_sn'] . '推送生成退款单失败');
            }
        } else {
            $OrderModel->commit();
        }

        // 添加用户标签
        $count_where['user_id'] = $order_info['user_id'];
        $count_where['status']  = ['neq', -1]; // 过滤取消订单
        $count_where['is_type'] = 0; // 真实订单

        $order_count = $OrderModel->where($count_where)->count();

        $first_order_id = 0;

        // 判断用户订单数（不包含已取消订单）
        if ($order_count == 1) {
            // $new_order['is_new'] = 0; // 非新订单
            $user_tags['is_new'] = 1; // 新用户

            $count_where['order_goods_type'] = 1; // 联营订单
            $first_order_id = $OrderModel->where($count_where)->order('create_time')->getField('order_id'); // 获取用户未取消的第一笔订单
        } else if ($order_count > 1) {
            // $new_order['is_new'] = 0; // 非新订单
            $user_tags['is_new'] = 2; // 老用户

            $count_where['order_goods_type'] = 1; // 联营订单
            $first_order_id = $OrderModel->where($count_where)->order('create_time')->getField('order_id'); // 获取用户未取消的第一笔订单
        } else {
            // $new_order['is_new'] = 0; // 非新订单
            $user_tags['is_new'] = 0; // 用户
        }

        // 更新用户标签
        if ($user_tags['is_new'] !== '') {
            $updateUser = $UserMainModel->where(array('user_id' => $order_info['user_id']))->save($user_tags);

            if ($updateUser === false) return $this->apiReturn(44102, $event . '更新用户标签失败', '');

            // 添加用户标签到缓存
            $s_user_info = S_user($order_info['user_id']);
            $s_user_info['is_new'] = $user_tags['is_new'];
            S_user($order_info['user_id'], $s_user_info);
        }

        // 更新是否新订单
        // $updateExtend = $OrderExtendModel->where(array('order_id' => $order_id))->save($new_order);

        // if ($updateExtend === false) {
        //     $OrderModel->rollback();
        //     return $this->apiReturn(44102, $event.'更新是否为新订单失败', '');
        // }

        // 联营更新第一笔订单
        if ($first_order_id) {
            $updateNewExtend = $OrderExtendModel->where(array('order_id' => $first_order_id))->save(['is_new' => 1]);

            if ($updateNewExtend === false) return $this->apiReturn(44102, $event . '更新第一笔订单是否为新订单失败', '');
        }

        // $this->sendMsgAtCheck($order_id, $sale_id); // 发送短信
        // 神策同步标记
        $OrderExtendModel->where(array('order_id' => $order_id))->setField('sensors_syn', 4);
        //限购 取消
        if (date("Y-m-d", $order_info["create_time"]) == date("Y-m-d")) {
            $this->returnBackUserGoodsLimit($order_id, $order_info["user_id"]);
        }
        return $this->apiReturn(0, '取消成功', '');
    }

    /*
     * 自营订单 取消订单判断是否含有限购商品，有限购商品恢复限购数量
     *
     */
    protected function returnBackUserGoodsLimit($order_id, $user_id = 0)
    {
        try {
            $user_id = cookie('uid') ? cookie('uid') : $user_id;
            if (!$user_id) {
                return ture;
            }
            $OrderItemsModel       = D('OrderItems');
            $goods = $OrderItemsModel->where(["order_id" => $order_id])->field("goods_number,goods_id")->select();
            $userLimitGoodsNumsList = S(C("ichuntUserLimitGoodsNumsList") . $user_id);
            if ($userLimitGoodsNumsList && is_array($userLimitGoodsNumsList)) {
                foreach ($goods as $good) {
                    $exists = array_key_exists($good["goods_id"], $userLimitGoodsNumsList);
                    if ($exists) {
                        $nums = $userLimitGoodsNumsList[$good["goods_id"]] - $good["goods_number"];
                        if ($nums <= 0) {
                            unset($userLimitGoodsNumsList[$good["goods_id"]]);
                        } else {
                            $userLimitGoodsNumsList[$good["goods_id"]] = $nums;
                        }
                    }
                }
                $currentTime = time();
                $endTime = strtotime(date("Y-m-d") . " 23:59:59 ");
                $expire = $endTime - $currentTime;
                if (!empty($userLimitGoodsNumsList)) {
                    S(C("ichuntUserLimitGoodsNumsList") . $user_id, $userLimitGoodsNumsList, ["expire" => $expire]);
                } else {
                    S(C("ichuntUserLimitGoodsNumsList") . $user_id, null, ["expire" => -1]);
                }
            }
        } catch (\Exception $e) {
        }
    }

    /*
        根据订单id取消送出去的券

    */
    private function cancel_coupon($order_id = -1)
    {
        try {

            $result = D('UserCoupon')->where(['source_order_id' => $order_id, 'status' => -1])->select();

            if (!empty($result)) {
                $UserCouponModel = D('Coupon/UserCoupon');

                foreach ($result as $key => $value) {
                    //删除优惠券
                    $UserCouponModel->setDisable($value['user_coupon_id']);
                }
            }
        } catch (Exception $e) {
        }
    }

    // 手动生成退款订单
    public function handleOrderRefund()
    {
        $order_id = I('order_id', '');
        $operator_id = 1000;
        $cancel_reason = '手动生成退款单';
        $OrderModel = D('Order');
        $OrderRefundModel = D('OrderRefund');
        $OrderRefundItemsModel = D('OrderRefundItems');
        $OrderActionLogModel = D('OrderActionLog');

        if (!$order_id) {
            echo '参数缺失';
            die;
        }

        $OrderModel->startTrans();

        // 添加退款单 (标记已处理状态)
        $refund_id = $OrderRefundModel->createOrderRefund($order_id, $operator_id, $cancel_reason, 10);

        if ($refund_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44007, '新增订单退款表失败');
        }

        // 添加退款明细
        $res = $OrderRefundItemsModel->createOrderRefundItems($refund_id, $order_id);

        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44009, '新增订单退款明细表失败');
        }

        $OrderActionLogModel->addLog($order_id, $operator_id, 2, $cancel_reason);

        $OrderModel->commit();

        echo '手动生成退款单已完成！';
    }

    // 审核时 消息通知客户
    public function sendMsgAtCheck($order_id, $sale_id)
    {
        $OrderModel = D('Order');
        $order_info = $OrderModel->getInfo($order_id);

        if ($order_info['status'] == 2) { // 待付款，表明订单审核通过
            $OrderAddressModel = D('OrderAddress');
            $order_address_info = $OrderAddressModel->getInfo($order_id);
            $OrderItemsModel = D('OrderItems');
            $items = $OrderItemsModel->getAllName($order_id);
            if (intval(count($items)) !== 1) {
                $items = implode(',', $items);
            } else {
                $items = $items[0];
            }
            $data = getOrderSpecialDetailsByOrderId($order_id);
            $data['order_id'] = $order_id;
            $data['url'] = getShortUrl($order_id);
            $data['order_sn'] = $order_info['order_sn'];
            $data['create_time'] = $order_info['create_time'];
            $data['order_amount'] = price_format($order_info['order_amount'], C('PLACE_CURRENCY_MAPPING.' . $order_info['currency']));

            $data['sale_name'] = D('Cms')->getUserName($sale_id);;
            $data['ex_int'] = 1;
            $send_data['data'] = $data;
            $keyword = 'order-remind-pay';
            $to_user = $order_info['user_id']; // 客户
            //            $wechat_data = C("WECHAT_TEMPLATE.{$keyword}");
            $wechat_data['keyword2']['value'] = $order_info['order_amount_format']; // 订单金额
            $wechat_data['keyword1']['value'] = $order_info['order_sn'];
            //            $wechat_data['orderProductName']['value'] = $items;
            //            $wechat_data['orderAddress']['value'] = $order_address_info['consignee'].'， '.$order_address_info['province_val'].' '.$order_address_info['city_val'].' '.$order_address_info['district_val'];
            $send_data['wechat_data'] = $wechat_data;
            $send_data = json_encode($send_data);
            $this->sendOrderMsg($keyword, $send_data, $to_user);

            // 审核通过后推送消息给业务员
            $newKeyword = 'order-send-salesman';

            $newToUser = D('Cms')->getUserWebUserId($sale_id); // 后台业务员对应前台uid

            // 给分配的业务员推送信息
            if (!$newToUser) {
                return $this->apiReturn(-1, '给指定的业务员发送短信失败，请到“用户系统”内部编码里绑定账号', '');
            }

            $this->sendOrderMsg($newKeyword, $send_data, $newToUser);
        }

        return $this->apiReturn(0, '成功', '');
    }

    // 订单系统--推送订单给业务员
    public function sendSales()
    {
        $order_id         = I('request.order_id', 0, 'intval'); // 订单号
        $sale_id          = I('request.sale_id', 0, 'intval'); // 必须有订单业务员
        $send_remark      = I('request.send_remark', ''); // 推送备注信息
        $operator_id      = I('request.operator_id', 0, 'intval');

        $OrderModel          = D('Order');
        $OrderExtendModel    = D('OrderExtend');
        $OrderActionLogModel = D('OrderActionLog');
        $CmsModel            = D('Cms');

        $order_info = $OrderModel->getInfo($order_id);

        if ($order_info['sale_id'] == $sale_id) {
            return $this->apiReturn(-1, '推送失败，当前跟进人未变更');
        }

        $OrderModel->startTrans();

        try {
            $saveData = [];
            $saveData['sale_id'] = $sale_id;
            $saveData['creator_uid'] = $sale_id;
            $OrderModel->where(['order_id' => $order_id])->data($saveData)->save(); // 修改订单表

            // $sale_name = $CmsModel->getUserName($sale_id); // 获取业务员名称
            $sale_info = $CmsModel->getUserInfo($sale_id);

            // 添加备注信息到订单扩展表
            if ($send_remark) {
                $OrderExtendModel->where(array('order_id' => $order_id))->data(['send_remark' => $send_remark])->save();
                $event = '推送订单给 ' . $sale_info['name'] . '，备注信息：' . $send_remark;
            } else {
                $event = '推送订单给 ' . $sale_info['name'];
            }

            $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event); // 操作记录

            // if ($order_info['status'] >= 8) { // 已发货状态，推送到财务系统
            //     $params['order_id'] = $order_id;
            //     $params['sale_id']  = $sale_id;
            //     $res = post_curl(FINANCE_DOMAIN.'/webapi/updateOrderSale', $params);
            //     $res = json_decode($res, true);

            //     if ($res['err_code'] != 0) {
            //         $OrderModel->rollback();
            //         return $this->apiReturn(44020, '推送到财务系统失败');
            //     }
            // }

            if (strpos($_SERVER['HTTP_REFERER'], 'sz') === false) { // 非测试环境下执行
                // 用户新订单推送给CRM
                // $map['user_id'] = $order_info['user_id'];
                // $map['is_type'] = 0;
                // $count = $OrderModel->where($map)->count();

                // if ($count == 1) {
                // $crm_params['user_id']     = $order_info['user_id'];
                // $crm_params['sale_id']     = $sale_id;
                // $crm_params['operator_id'] = $operator_id;
                // $crm_params['type']        = 1;
                // $crm_header = array('api-key: crm a1b2c3d4e5f6g7h8i9jk');
                // $res = json_decode(post_curl(CRM_DOMAIN.'/api/updateUserOrderStatus', $crm_params, $crm_header), true);

                // if ($res['err_code'] != 0) return $this->apiReturn(44020, '新订单用户绑定客服推送到CRM系统失败');
                // }

                $crm_params = [];
                $crm_params['customer_id']   = $order_info['customer_id'];
                $crm_params['org_id']        = $order_info['sale_com_id'];
                $crm_params['user_id']       = $order_info['user_id'];
                $crm_params['department_id'] = $sale_info['department_id'];
                $crm_params['sale_id']       = $sale_id;
                $crm_params['sale_name']     = $sale_info['name'];
                $crm_params['operator_id']   = $operator_id;

                $res = json_decode(post_curl(CRM_V2_DOMAIN . '/open/updateSaleByOrder', $crm_params), true);

                if (!$res) {
                    $OrderModel->rollback();
                    return $this->apiReturn(44020, '推送到CRM接口请求失败');
                }

                if ($res['code'] != 0) {
                    $OrderModel->rollback();
                    return $this->apiReturn(44020, '推送到CRM失败，原因：' . $res['msg']);
                }
            }

            $OrderModel->commit();

            $newKeyword = 'new-order-send-salesman'; // 消息模板
            $newData['data']['order_sn'] = $order_info['order_sn']; // 订单编号

            $to_user = $CmsModel->getUserWebUserId($sale_id); // 后台业务员对应的前台uid

            if (!$to_user) return $this->apiReturn(0, '推送成功，但给指定的业务员发送短信失败，请到“用户系统”内部编码里绑定账号', '');

            $this->sendOrderMsg($newKeyword, json_encode($newData), $to_user);

            return $this->apiReturn(0, '推送成功', '');
        } catch (Exception $e) {
            $OrderModel->rollback();
            return $this->apiReturn(44021, '推送失败', $e->getMessage());
        }
    }

    /**
     * [getOrderDetails description]
     * 用于订单后台获取订单详细
     * @return [type] [description]
     */
    public function getOrderDetails()
    {
        $data = [];
        $order_id = I('request.order_id', 0, 'intval'); // 订单号
        $type = I('request.type', '0', 'strval');
        switch (strval($type)) {
            case '0': // 全部
                $data['order_info'] = D('Order')->getInfo($order_id);

                if (empty($data['order_info'])) return $this->apiReturn(0, '');

                $data['order_invoice_info'] = D('OrderInvoice')->getInfo($order_id);
                $data['order_items_info']   = D('OrderItems')->getOrderList($order_id, ['OI.order_id' => $order_id], null); // ['order_id' => $order_id]加这个是为防止接口里去掉明细已取消的

                if ($data['order_info']['is_type'] == 1) {
                    $data['order_address_info'] = D('OtherAddress')->getInfo($order_id);
                } else {
                    $data['order_address_info'] = D('OrderAddress')->getInfo($order_id);
                }

                $data['order_invoice_address_info']           = D('OrderAddress')->getInfo($order_id, 2);
                $data['order_shipping_info']                  = D('OrderShipping')->getInfo($order_id); //订单物流
                $data['order_invoice_shipping_info']          = D('OrderShipping')->getInfo($order_id, 2); //订单发票物流
                $data['order_price_info']['goods_price']      = D('OrderPrice')->getGoodsPrice($order_id); // 货款
                $data['order_price_info']['ext_price']        = D('OrderPrice')->getExtPrice($order_id); // 附加款
                $data['order_price_info']['discount_amount']  = D('OrderPrice')->getPreferentialPrice($order_id); // 优惠金额
                $data['order_price_info']['new_client_price'] = D('OrderPrice')->getNewClientPrice($order_id); // 新客价优惠
                $data['order_price_info']['pay_preferential'] = D('OrderPrice')->getPayPreferential($order_id); // 支付优惠
                $data['order_price_info']['shipping_price']   = D('OrderPrice')->getShippingPrice($order_id); // 运费
                // $data['order_price_info']['refund_price']  = D('OrderPrice')->getRefundPrice($order_id); // 退款金额
                $data['order_pay_log']                        = D('PayLog')->getInfo($order_id);
                $data['order_temp_info']                      = D('OrderExtend')->getInfo($order_id);
                $data['order_contract_info']                  = D('OrderContract')->getInfo($order_id);
                $data['order_shipping_inside']                = D('OrderShippingInside')->getInfo($order_id);
                // $data['order_refund_info']                 = D('OrderRefund')->getInfo($order_id);
                // $data['order_refund_items']                = D('OrderRefundItems')->getInfo($order_id);
                $data['order_extra']                          = D('OrderExtra')->getInfo($order_id);
                $data['order_gift']                           = D('OrderGift')->getOrderGift($order_id, [1, 2]);
                $data['order_sample_info']                    = D('OrderSampleInfo')->getSampleInfo($order_id);

                // 判断是否存在自营单，若存在且已发货，则查询物流信息
                if ($data['order_info']['order_goods_type'] == 1 && $data['order_info']['status'] > 2 && $data['order_temp_info']['erp_sn']) {
                    $map = [];
                    $map['o.order_goods_type'] = 2;
                    $map['o.status']           = ['gt', 4];
                    $map['oe.erp_sn']          = $data['order_temp_info']['erp_sn'];

                    $res = D('Order')->alias('o')
                        ->join('LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id')
                        ->where($map)
                        ->field('o.order_id, o.user_id')
                        ->find();

                    if ($res) {
                        $data['self_user_id'] = $res['user_id'];
                        $data['self_order_shipping_info']         = D('OrderShipping')->getInfo($res['order_id']); // 自营订单物流
                        $data['self_order_invoice_shipping_info'] = D('OrderShipping')->getInfo($res['order_id'], 2); // 自营订单发票物流
                    }
                }
            case '1': //
                // 订单信息
                $data['order_info'] = D('Order')->getInfo($order_id);
                break;
            case '2':
                // 开票信息
                $data['order_invoice_info'] = D('OrderInvoice')->getInfo($order_id);
                break;
            case '3':
                // 订单型号信息
                $data['order_items_info'] = D('OrderItems')->getOrderList($order_id, ['OI.order_id' => $order_id], null);
                break;
            case '4':
                // 订单收货地址相关信息
                $data['order_address_info'] = D('OrderAddress')->getInfo($order_id);
                break;
            case '5':
                // 订单物流
                $data['order_shipping_info']         = D('OrderShipping')->getInfo($order_id);
                $data['order_invoice_shipping_info'] = D('OrderShipping')->getInfo($order_id, 2); //订单发票物流
                break;
            case '6':
                // 订单价格
                $data['order_price_info']['goods_price'] = D('OrderPrice')->getGoodsPrice($order_id); // 货款
                $data['order_price_info']['ext_price']   = D('OrderPrice')->getExtPrice($order_id); // 附加款
                break;
            case '7':
                $data['order_pay_log'] = D('PayLog')->getInfo($order_id);
                break;
            default:
                break;
        }

        if ($data['order_info']) {
            if (in_array($data['order_info']['sale_com_id'], [1, 2])) {
                $user_info = D('Home/UserMain')->getUserInfo($data['order_info']['user_id']);

                if (empty($user_info['mobile']) && empty($user_info['email'])) {
                    $user_info = D('Home/UserMain')->find($data['order_info']['user_id']);
                    S_user($data['order_info']['user_id'], $user_info);
                }

                $data['order_info']['user_info'] = $user_info;
                $data['order_info']['company_info'] = S_company($data['order_info']['user_id']);
            } else {
                $UserModel = D('Crm/User');
                $userComId = $data['order_info']['sale_com_id'];
                if (in_array($userComId, [3, 6, 11, 12])) {
                    $userComId = 3;
                }

                $data['order_info']['user_info'] = $UserModel->getInfoByUserId($data['order_info']['user_id'], $userComId);
                $data['order_info']['company_info'] = [];
            }
        }

        if ($data['order_invoice_info']) {
            $ORDER_INVOICE_STATUS = C('ORDER_INVOICE_STATUS'); // 发票状态
            $ORDER_INVOICE_TYPE = C('ORDER_INVOICE_TYPE'); // 发票类型

            $data['order_invoice_info']['invoice_status_val'] = $ORDER_INVOICE_STATUS[$data['order_invoice_info']['invoice_status']];
            $data['order_invoice_info']['inv_type_val'] = $ORDER_INVOICE_TYPE[$data['order_invoice_info']['inv_type']];
        }

        return $this->apiReturn(0, '成功', $data);
    }

    // 后台新增订单---传递参数ERP
    public function getParamsToErp($user_id, $tax_id, $sale_id)
    {
        $param = array();
        $param['SALEUSER'] = D('Cms')->getUserName($sale_id);

        $user_info = S_user($user_id);
        $user_company = S_company($user_id);

        if ($tax_id) {
            $inv_info = $this->getUserInvoiceByOrder($user_id, $tax_id);

            if ($inv_info['data']['inv_type'] == 1) { // 不开票
                if ($user_company['com_name']) {
                    $param['CUSTOMER'] = $user_company['com_name'];
                } else {
                    $param['CUSTOMER'] = $user_info['mobile'] ? $user_info['mobile'] : $user_info['email'];
                }
            } else {
                $param['CUSTOMER'] = $inv_info['data']['tax_title'];
            }
        } else {
            if ($user_company['com_name']) {
                $param['CUSTOMER'] = $user_company['com_name'];
            } else {
                $param['CUSTOMER'] = $user_info['mobile'] ? $user_info['mobile'] : $user_info['email'];
            }
        }

        return $param;
    }


    public function getAvgPrice($datas)
    {
        $IocObj = new AvgPriceYunFeiService(
            new AvgPriceQuanService(
                new AvgPriceFJFService(
                    new AvgPriceService($datas)
                )
            )
        );
        $obj = new AvgPriceService($IocObj->action());
        $datas = $obj->getPrice();
        //        dump($IocObj->SQL);
        return $datas;
    }

    public function getAvgPriceBywallt($datas)
    {
        $tmp = new AvgPriceYunFeiService(
            new AvgPriceQuanService(
                new AvgPriceFJFService(
                    new AvgPriceService($datas)
                )
            )
        );
        $tmp1 = new AvgPriceWalletService($tmp);
        $obj = new AvgPriceService($tmp1->action());
        $datas = $obj->getPrice();
        return $datas;
    }

    // 检查是否存在品牌黑名单
    public function checkBrandBlackList($user_id, $tax_id, $items)
    {
        $company_name = '';
        $company_info = S_company($user_id);

        // 获取公司名称
        if ($tax_id) {
            $inv_info = $this->getUserInvoiceByOrder($user_id, $tax_id);

            if ($inv_info['data']['inv_type'] > 1) {
                $company_name = $inv_info['data']['tax_title'];
            } else {
                $company_name = $company_info['com_name'];
            }
        } else {
            $company_name = $company_info['com_name'];
        }

        if (!$company_name) return false;

        $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.coupon"));
        $res = $redis->hgetAll("brand_blacklist");

        if (!$res) return false;

        $all_brand_name = []; // 所有品牌
        $all_brand_com_name = []; // 品牌对应的公司

        foreach ($res as $v) {
            $v = json_decode($v, true);
            $v['brand_name'] = strtolower($v['brand_name']); // 转化为小写

            $all_brand_name[] = $v['brand_name'];
            $all_brand_com_name[$v['brand_name']][] = $v['com_name'];
        }

        $is_blacklist = false;
        foreach ($items as $item) {
            $item['brand_name'] = strtolower($item['brand_name']); // 转化为小写

            if (!in_array($item['brand_name'], $all_brand_name)) continue;

            if (!in_array($company_name, $all_brand_com_name[$item['brand_name']])) continue;

            $is_blacklist = true;
        }

        return $is_blacklist;
    }

    /*
     * 下单前判断  关小黑屋
     */
    protected function blackHouse($user_id)
    {
        if ($this->checkUserCompany($user_id)) {
            $this->pushReportMonitorLog(
                [
                    "interface_type" => "4",
                    "err_msg" => "该用户是黑名单",
                    "err_code" => "21024",

                ]
            );
            return false;
        }
        return true;
    }

    /*
     * //检查商品中是否全都是自营现卖的商品
     */
    protected function checkOrderListIsZYXM($datas)
    {
        $zy_xianmai = true;
        foreach ($datas['list'] as $v) {
            //判断是否全都是自营现卖
            $checkzixm = intval($v["order_goods_type"]) != 2 || (intval($v["order_goods_type"]) == 2 && intval($v["sale_type"]) != 1);
            if ($checkzixm) {
                $zy_xianmai = false;
                break;
            }
        }
        return $zy_xianmai;
    }

    /*
     * //检查商品中是否含有预售商品
     */
    protected function checkOrderExistsPresell($datas)
    {
        $hasYuShou = false;
        foreach ($datas['list'] as $v) {
            if (intval($v["order_goods_type"]) == 2  && intval($v["sale_type"]) == 2) {
                $hasYuShou = true;
                break;
            }
        }
        return $hasYuShou;
    }

    /*
     * //检查商品中是否都是MRO商品
     */
    protected function isAllMroGoods($datas)
    {
        $all_mro = true;
        foreach ($datas['list'] as $v) {
            if ($v['goods_type'] != 6) {
                $all_mro = false;
                break;
            }
        }
        return $all_mro;
    }


    /*
     * 自营现卖  修改相关字段 推送erp
     */
    protected function ziYinXianMaiPushErp($order_id)
    {
        $bk = D('Order')->where(["order_id" => $order_id])->save(["erp_syn" => 1]);
        $bk2 = D('OrderExtend')->where(["order_id" => $order_id])->save(["is_manager_audit" => 3]);
        if ($bk !== false && $bk2 !== false) {
            return true;
        } else {
            return false;
        }
    }

    // 检查现货商品是否存在IC编码
    public function checkOrderSpotGoods(&$list)
    {
        $spot_goods_ids = []; // 现货商品ID集合

        foreach ($list as $it) {
            if ($it['order_goods_type'] == 2 || $it['canal'] == C('joint_supplier_code') || $it['goods_type'] == 4) {
                $spot_goods_ids[] = $it['goods_id'];
            }
        }

        if (empty($spot_goods_ids)) {
            return true;
        }

        $GoodsModel = D('Goods');
        $GoodsMappingModel = D('GoodsMapping');

        $goods_mapping = $GoodsMappingModel->getGoodsMappingBySkuIds($spot_goods_ids);

        $goods_info = [];
        if (!empty($goods_mapping)) {
            $goods_sns = array_values($goods_mapping);
            $goods = $GoodsModel->getGoodsByStandardGoodsSns($goods_sns);

            foreach ($goods as $g) {
                $goods_info[$g['standard_goods_sn']] = $g;
            }
        }

        foreach ($list as $i => &$v) {
            if (!in_array($v['goods_id'], $spot_goods_ids)) {
                continue;
            }

            if (empty($goods_mapping[$v['goods_id']])) {
                return '第' . ($i + 1) . '行，SKUID：' . $v['goods_id'] . '，商品IC编码不存在，请联系技术检查数据';
            }

            $goods_sn = $goods_mapping[$v['goods_id']];

            $list[$i]['goods_sn'] = $goods_sn;
            $list[$i]['sale_goods_id'] = !empty($goods_info[$goods_sn]) ? $goods_info[$goods_sn]['goods_id'] : 0;
            $list[$i]['brand_id'] = !empty($goods_info[$goods_sn]) ? $goods_info[$goods_sn]['brand_id'] : 0;
        }

        return true;
    }


    public function create()
    {
        $user_id   = $this->auth() ? I('uid') : cookie('uid');
        // $user_info = $this->getUserCache($user_id);
        $pf = I('pf') ? I('pf') : platform();
        $from_frq = I('from_frq', null); //询报价
        $uc_id   = I('uc_id') ? I('uc_id') : cookie('ucid');
        $shipping_type      = I('request.shipping_type', 1, 'intval');  // 配送方式
        $self_consignee      = I('request.self_consignee', "");
        $self_mobile      = I('request.self_mobile', "");
        $remark      = I('request.remark', "");
        $address_id         = I('request.address_id', 0, 'intval');     // 配送地址
        $tax_id             = I('request.tax_id', 0, 'intval');         // 用户发票ID
        $user_coupon_id     = I('request.user_coupon_id', 0, 'intval'); // 优惠券ID
        $inquiry_item_ids       = I('request.inquiry_item_ids', ''); //询价单转订单
        $contract_com_name       = I('request.contract_com_name', ''); //合同PI_公司名称


        //先校验公司合法性


        $inquiryService = new InquiryService();

        if (!empty($inquiry_item_ids)) {
            try {
                $inquiryService->checkIsHasSameOrder(explode(",", $inquiry_item_ids));
            } catch (\Exception $e) {
                return $this->apiReturn("21024", sprintf("提交订单失败:%s", $e->getMessage()));
            }
        }

        $datas     = A('Order/Cart')->confirm(false, $inquiry_item_ids);
        if ($datas['code'] != 0 || empty($datas["data"])) {
            return $this->apiReturn("21024", sprintf("提交订单失败:%s", $datas['err_msg']));
        }
        $return_data = $datas["data"] ?: [];
        $return_data_list = $return_data["list"] ?: [];

        $deliveryPlace = isset($datas["data"]["list"][0]["delivery_place"]) ? $datas["data"]["list"][0]["delivery_place"] : 1;
        $sale_com_id = $deliveryPlace == 1 ? 1 : 2;


        $checkCanalIsAddOrder = $this->checkCanalIsAddOrder($return_data_list);
        if ($checkCanalIsAddOrder["err_code"] != 0) {
            return $this->apiReturn("21024", sprintf("提交订单失败:%s", $checkCanalIsAddOrder['err_msg']));
        }

        if (intval($deliveryPlace) == 2 && empty($contract_com_name)) {
            return $this->apiReturn("21024", sprintf("提交订单失败:%s", "合同PI必填"));
        }

        if (intval($deliveryPlace) == 2) {
            $tax_id  = $this->getUserOverseasTaxinfo($user_id, $uc_id, $contract_com_name);
            if (intval($tax_id) <= 0) {
                return $this->apiReturn("21024", sprintf("提交订单失败:%s", "创建合同PI失败"));
            }
        }

        $isCheckoutCompanyInfo = $this->checkoutCompanyInfo($user_id, $uc_id, $deliveryPlace, $tax_id, $address_id);
        if ($isCheckoutCompanyInfo["err_code"] != 0) {
            header('Content-Type:application/json; charset=utf-8');
            echo json_encode($isCheckoutCompanyInfo);
            exit;
        }

        $orderPfSecondLevel = $pf;
        if ($inquiry_item_ids) {
            $orderPfSecondLevel = 8; //询报价
        }
        $openRequestParams = [];
        $openRequestParams["items"] = [];
        $openRequestParams["sale_com_id"] = $sale_com_id;
        $openRequestParams["order_pf"] = 1;
        $openRequestParams["order_pf_second_level"] = $orderPfSecondLevel;
        $openRequestParams["order_source"] = A('Order/Cart')->order_source();
        $openRequestParams["uc_id"] = $uc_id;
        $openRequestParams["user_id"] = $user_id;
        $openRequestParams["order_pay_type"] = 1; //订单付款方式 
        $openRequestParams["currency"] = $deliveryPlace == 1 ? 1 : 2;
        $openRequestParams["delivery_place"] = $deliveryPlace;
        $openRequestParams["order_shipping_type"] = $shipping_type;
        $openRequestParams["self_pickup_consignee"] = $self_consignee;
        $openRequestParams["self_pickup_telphone"] = $self_mobile;
        $openRequestParams["order_remark"] = $remark;
        $openRequestParams["zy_delivery_type"] = 2; //// 发货方式，1-自营优先发货，2-拼单发货
        $openRequestParams["address_id"] = $address_id;
        $openRequestParams["invoice_id"] = $tax_id;
        $openRequestParams["user_coupon_id"] = $user_coupon_id > 0 ? $user_coupon_id : 0;
        $openRequestParams["extra_fee"] = $return_data["extend_fee"];
        $openRequestParams["shipping_price"] = price_format($return_data["mro_shipping_fee"] + $return_data["shipping_price"], 0, 2);
        $openRequestParams["coupon_price"] = $return_data["coupon_price"];
        $openRequestParams["order_amount"] = $return_data["order_amount"];
        $openRequestParams["advance_amount"] = 0; //预付金额
        $openRequestParams["terminal_info"] = [
            "customer_cn" => I('request.customer_cn', "", 'trim'), // 客户名称（中文）
            "customer_en" => I('request.customer_en', "", 'trim'), // 客户名称（英文）
            "customer_type" => I('request.customer_type', "", 'intval'), // 终端客户类型，1终端 2贸易商 3大学 4个人
            "product_use_classone_sn" => I('request.product_use_classone_sn', "", 'intval'), // 一级分类
            "product_use_classtwo_sn" => I('request.product_use_classtwo_sn', "", 'intval'), // 二级分类
            "customer_website" => "", // 客户网址
        ]; //终端信息

        //明细
        foreach ($return_data_list as $items) {
            $orderItems = [];
            $orderItems["goods_id"] = $items["goods_id"];
            $orderItems["sku_name"] = $items["sku_name"];
            $orderItems["goods_name"] = $items["goods_name"];
            $orderItems["brand_id"] = $items["brand_id"];
            $orderItems["brand_name"] = $items["brand_name"];
            $orderItems["standard_brand_id"] = $items["standard_brand_id"];
            $orderItems["standard_brand_name"] = $items["standard_brand_name"];
            $orderItems["goods_number"] = $items["goods_number"];
            $orderItems["goods_price"] = $items["goods_price"];
            $orderItems["extend_price"] = $items["extend_price"];
            $orderItems["extend_price_items"] = $items["extend_price_items"];
            $orderItems["delivery_time"] = $items["delivery_time"];
            $orderItems["buyer_id"] = $items["buyer_id"];
            $orderItems["customer_material_number"] = $items["customer_material_number"];
            $orderItems["customer_po_sn"] = $items["customer_po_sn"];
            $orderItems["batch"] = $items["batch"];
            $orderItems["remarks"] = $items["remarks"];
            $orderItems["contract_remark"] = $items["contract_remark"];
            $orderItems["supplier_id"] = $items["supplier_id"];
            $orderItems["supplier_name"] = $items["supplier_name"];
            $orderItems["raw_goods_sn"] = $items["raw_goods_sn"];
            $orderItems["raw_goods_packing"] = $items["raw_goods_packing"];
            $orderItems["raw_brand_name"] = $items["raw_brand_name"];
            $orderItems["canal"] = isset($items["canal"]) ? $items["canal"] : "";
            $orderItems["inquiry_item_id"] = isset($items["inquiry_item_id"]) ? $items["inquiry_item_id"] : 0;
            $orderItems["inquiry_id"] = isset($items["inquiry_id"]) ? $items["inquiry_id"] : 0;
            $orderItems["inquiry_sn"] = isset($items["inquiry_sn"]) ? $items["inquiry_sn"] : "";
            $orderItems["quote_id"] = isset($items["quote_id"]) ? $items["quote_id"] : 0;
            $orderItems["quote_sn"] = isset($items["quote_sn"]) ? $items["quote_sn"] : "";
            $orderItems["buyer_id"] = isset($items["buyer_id"]) ? $items["buyer_id"] : 0;
            $openRequestParams["items"][] = $orderItems;
        }
        // echo json_encode($openRequestParams);exit;
        $openReturn = OpenService::requestOpenApi(OPENPLATFORM_DOMAIN . '/order/create', $openRequestParams, "POST");

        if (empty($inquiry_item_ids)) {
            A('Order/Cart')->delete();
        } else {
            (new InquiryService)->delInquiryItem(explode(",", $inquiry_item_ids));
        }

        return $this->apiReturn(0, '创建订单成功!', $openReturn["order_id"]);
    }

    /**
     * 创建新订单
     * @return [type] [description]
     */
    public function create_bak()
    {
        $user_id   = $this->auth() ? I('uid') : cookie('uid');
        $user_info = $this->getUserCache($user_id);



        // 非测试账号不能下单 - 临时
        // if ($user_info['is_test'] == 0) return $this->apiReturn("21024","系统维护中，请稍后再试");

        //关小黑屋
        if (!$this->blackHouse($user_id)) {
            return $this->apiReturn("21024", "系统繁忙，请稍后再试");
        }
        // $this->jinDiaoUser($user_info['data']['is_type'],$user_id,$user_info); /* 现在订单后台需要CRM绑定关系才能下单，这块可以注释 */

        $datas     = A('Order/Cart')->confirm(false);
        p($datas);
        exit;
        if ($datas['err_code'] != 0) {
            $this->pushReportMonitorLog(
                [
                    "interface_type" => "4",
                    "remark" => sprintf("下单 获取购物车商品失败,%s", json_encode($datas)),
                ]
            );
            return $this->apiReturn($datas['err_code'], $datas['err_msg'], $datas['data']);
        }
        $_cartIds = $this->findConfirmCartIds($datas["data"]["list"]); //获取下单商品中购物车的id
        $datas = $datas['data'];

        $type = I('request.type', 0, 'intval');  // 下单来源：0-前台, 1.联营, 2.自营线上, 3.自营线下, 4.自营其他业务订单

        //判断商品中是否含有djk商品
        $is_djk = $this->checkOrderGoodsDjk($datas);
        $djkParams = [];
        $djkMap = [];
        //如果没有传终端数据 就清理调djk商品
        if ($is_djk) {
            try {
                //不能删除此代码 以后回启动
                //                $this->deleteNoDJKGoods($datas,$djkParams,$is_djk);
                $djkMap = $this->getDJKInfo($datas["list"]);
            } catch (OrderException $e) {
                return $this->apiReturn($e->getCode(), $e->getMessage());
            }
        }

        if (empty($datas["list"])) {
            if ($is_djk) {
                return $this->apiReturn("21028", "订单中仅有digikey商品，必须填写终端信息");
            }
            return $this->apiReturn("21028", "订单中没有符合购买的商品~_~");
        }

        // 后台下单，自营 或 10楼库存、寄售商品需要到中间表查找关联的IC编码，若不存在则阻止下单
        if ($type) {
            $res = $this->checkOrderSpotGoods($datas['list']);
            if ($res !== true) {
                return $this->apiReturn("21029", $res);
            }
        }

        //        dump($djkMap);exit;
        $pay_type           = I('request.pay_type', 1, 'intval');       // 支付方式
        $shipping_type      = I('request.shipping_type', 1, 'intval');  // 配送方式
        $address_id         = I('request.address_id', 0, 'intval');     // 配送地址
        $order_remark       = I('request.remark', '');                  // 订单备注
        $self_consignee     = I('request.self_consignee', '');          // 自提联系人
        $self_mobile        = I('request.self_mobile', '');             // 自提联系电话
        $tax_id             = I('request.tax_id', 0, 'intval');         // 用户发票ID
        $user_coupon_id     = I('request.user_coupon_id', 0, 'intval'); // 优惠券ID
        //        $is_tuangou         = I('request.tuangou', 0, 'intval'); // 是否来自于团购
        $inviteFriendId     = I("request.invite_friend_id", 0, "intval"); //邀请好友下单 邀请人id
        //        $tuantou_order_id   = I('request.order_id', 0, 'intval'); // 分享的团购订单id

        // 订单后台新增订单参数
        $operator_id        = I('request.sale_id', 0, 'intval'); // 后台人员ID
        $business_type      = I('request.business_type', 0, 'intval'); // 自营订单特殊业务类型
        $role               = I('request.role', ''); // 自营线上下单角色 1-管理员，10-自营客服主管，11-自营客服主管助理
        $inv_com_id         = I('request.inv_com_id', 0); // 发票公司ID
        $is_free_ship       = I('request.is_free_ship', 0); // 是否免运费（后台）
        $contract_com_name  = I('request.contract_com_name', ''); // 合同乙方公司 （后台）
        $customer_sn        = I('request.customer_sn', ''); // 客户订单号 （后台）

        $OrderModel          = D('Order');
        $OrderExtendModel    = D('OrderExtend');
        $OrderContractModel  = D('OrderContract');
        $OrderItemsModel     = D('OrderItems');
        $OrderItemsExtModel  = D('OrderItemsExt');
        $OrderPriceModel     = D('OrderPrice');
        $OrderShippingModel  = D('OrderShipping');
        $OrderAddressModel   = D('OrderAddress');
        $OrderInvoiceModel   = D('OrderInvoice');
        $UserMainModel       = D('Home/UserMain');
        $TaxinfoModel        = D('Invoice/Taxinfo');
        // $InvoiceCompanyModel = D('Invoice/InvoiceCompany');
        // $InvoiceComUserModel = D('Invoice/InvoiceComUser');

        // 含MRO商品时，不支持上门自提
        if ($shipping_type == 2 && $datas['is_exists_mro']) {
            return $this->apiReturn(21029, '存在MRO商品时，不支持上门自提~');
        }

        // 检查品牌黑名单
        $res = $this->checkBrandBlackList($user_id, $tax_id, $datas['list']);
        if ($res) return $this->apiReturn(21029, 'TE品牌对您的公司限购哦~');

        //团购订单  限制自己不能购买自己的团购
        //        if($is_tuangou && $tuantou_order_id  && !$this->checkTuangouBySelf($tuantou_order_id)){
        //            return $this->apiReturn(21027,"自己和自己不能拼团哦~");
        //        }

        $OrderModel->startTrans();

        // 后台新增联营订单先判断客户与当前业务员是否绑定，若无绑定，则阻止订单生成并提示
        if ($type == 1 && strpos($_SERVER['HTTP_REFERER'], 'sz') === false) { // 非测试环境下执行
            $code_sn = D('Cms')->getCodeSn($operator_id);

            if (empty($code_sn)) {
                return $this->apiReturn(25001, '当前销售未设置员工编码');
            }

            $param = [];
            $param['SALEUSER'] = $code_sn;
            $param['CUSTOMER'] = html_entity_decode(I('customer_name'));

            $res = A('Server/Consume')->isBindSalesAndCustomer($param);

            if ($res['code'] == 1) { // ERP接口异常
                return $this->apiReturn(25001, $res['msg']);
            } else if ($res['code'] == 2) { // 未关联
                $log_txt = '订货客户：' . $param['CUSTOMER'] . '，ERP不存在该客户或该客户与当前业务员无绑定关系，请确认CRM存在绑定关系，如仍有问题请联系产品处理后再进行录单';
                return $this->apiReturn(25001, $log_txt);
            }
        }

        // 存在dgk商品时，将终端信息(外文名称、类型、产品用途和网址)保存到会员公司管理表里
        if ($is_djk && I('customer_cn')) {
            $djkInfo = array(
                'customer_en'             => I('customer_en', ''),
                'customer_type'           => I('customer_type', 0),
                'product_use_classone_sn' => I('product_use_classone_sn', -1),
                'product_use_classtwo_sn' => I('product_use_classtwo_sn', -1),
                'customer_website'        => I('customer_website', ''),
            );

            // $res = $InvoiceCompanyModel->where(['com_name' => I('customer_cn')])->save($djkInfo);

            $CompanyTerminalInfoModel = D('Crm/CompanyTerminalInfo');

            $exists = $CompanyTerminalInfoModel->where(['com_name' => I('customer_cn')])->find();

            if ($exists) {
                $djkInfo['update_time'] = time();

                $res = $CompanyTerminalInfoModel->where(['com_name' => I('customer_cn')])->save($djkInfo);
            } else {
                $djkInfo['create_time'] = time();
                $djkInfo['update_time'] = time();

                $res = $CompanyTerminalInfoModel->add($djkInfo);
            }

            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(25029, '会员公司管理信息更新失败');
            }
        }

        if (empty($address_id) && !$type) { // 后台下单不校验地址
            $msg = $shipping_type == 1 ? '请填写收货地址' : '请选择自提地址';
            return $this->apiReturn(25007, $msg);
        }

        if (!in_array($pay_type, array(1, 2))) {
            return $this->apiReturn(25008, '请选择有效付款方式');
        }

        if ($shipping_type == 2 && empty($self_consignee) && empty($self_mobile) && !$type) { // 后台下单不校验自提联系方式
            return $this->apiReturn(25006, '请填写联系方式');
        }

        // 原因：优惠券已被使用、优惠券未达到金额要求
        if ($user_coupon_id != -1 && $datas['user_coupon_id'] != $user_coupon_id) {
            $this->pushReportMonitorLog(
                [
                    "interface_type" => "4",
                    "err_msg" => "指定优惠券无法被使用，请重新选择优惠",
                    "err_code" => "25017",
                    "remark" => sprintf("下单,%s", json_encode($datas)),

                ]
            );
            return $this->apiReturn(25017, '指定优惠券无法被使用，请重新选择优惠券');
        }
        // 商品最后一条记录
        $end = end($datas['list']);

        if ($type) {
            $currency = I('currency', 1);
        } else {
            $currency = C('PLACE_CURRENCY_MAPPING.' . $end['delivery_place']);
        }

        $pay_mark = false; //已付款标记

        // 深贸的只允许下猎芯专营和猎芯备货的渠道
        // if ($currency == 2) {
        //     $hk_allowed_supplier_names = C('hk_allowed_supplier_names');

        //     foreach ($datas['list'] as $it) {
        //         if (!in_array($it['supplier_name'], $hk_allowed_supplier_names)) {
        //             return $this->apiReturn(25018, '请剔除代购型号再提交订单，当前型号：' . $it['goods_name'] . '，渠道：' . $it['supplier_name']);
        //         }
        //     }
        // }

        // 可预付的条件
        // if ($pay_type == 2 && ($datas['order_amount'] < C('ADVANCE_MIN_LIMIT.' . $currency) || $datas['order_amount'] > C('ADVANCE_MAX_LIMIT.' . $currency))) {
        //     $this->pushReportMonitorLog(
        //         [
        //             "interface_type"=>"4",
        //             "err_msg"=>"订单金额达不到预付条件，请使用全额付款方式",
        //             "err_code"=>"25007",
        //             "remark"=>sprintf("下单,%s",json_encode($datas)),

        //         ]
        //     );
        //     return $this->apiReturn(25007, '订单金额达不到预付条件，请使用全额付款方式');
        // }

        // 添加用户标签
        $count_where['user_id'] = $user_id;
        $count_where['status']  = ['neq', -1]; // 过滤已取消订单
        $count_where['is_type'] = 0; // 真实订单
        $order_count = $OrderModel->where($count_where)->count();
        $send_remark = '';

        if ($order_count) {
            $user_tags['is_new'] = 2; // 老用户
        } else {
            $user_tags['is_new'] = 1; // 新用户
            $send_remark = '新用户';
        }

        $res = $UserMainModel->where(array('user_id' => $user_id))->save($user_tags);

        if ($res === false) {
            $OrderModel->rollback();
            $this->pushReportMonitorLog(
                [
                    "interface_type" => "4",
                    "err_msg" => "用户标签更新失败",
                    "err_code" => "25027",
                    "remark" => sprintf("%s", json_encode($user_tags)),

                ]
            );
            return $this->apiReturn(25027, '用户标签更新失败');
        }

        // 添加用户标签到缓存
        $s_user_info = S_user($user_id);
        $s_user_info['is_new'] = $user_tags['is_new'];
        S_user($user_id, $s_user_info);

        /***                      订单分配sale_id                            ***/
        // $new_count_where = [];
        // $new_count_where['user_id'] = $user_id;
        // $new_count_where['is_type'] = 0; // 真实订单
        // $new_order_count = $OrderModel->where($new_count_where)->count(); // 查看用户订单数

        // $is_test = $user_info['data']['is_test'];
        // $res = $this->orderAssign($user_id, $new_order_count, $is_test, $type, $operator_id, $inv_com_id); // 订单分配

        // if ($res['err_code'] != 0) {
        //     $OrderModel->rollback();
        //     $this->pushReportMonitorLog(
        //         [
        //             "interface_type"=>"4",
        //             "err_msg"=>$res['err_msg'],
        //             "err_code"=>$res['err_code'],
        //             "remark"=>sprintf("下单，订单分配失败"),
        //         ]
        //     );
        //     return $this->apiReturn($res['err_code'], $res['err_msg']);
        // }

        // $sale_id = $res['data'];

        $order_nature      = -1; // 订单性质，默认是个人
        $customer_id       = 0; // 订货客户ID
        $front_customer_id = 0; // 前台订货客户ID，默认从发票获取
        $front_com_name    = ''; // 前台公司名称，大陆订单取增值税发票抬头，香港订单取合同乙方公司名称
        $com_nature        = I('com_nature', 0); // 后台传送过来的公司性质

        if ($tax_id) { // 根据发票抬头获取公司信息
            $tax_info = $TaxinfoModel->where(['tax_id' => $tax_id])->find();

            if (in_array($tax_info['inv_type'], [3, 4])) { // 增值税专票、增值税普票
                $front_com_name = $tax_info['tax_title'];
            } else {
                $order_nature = 0;
            }

            $contract_com_name = $tax_info['tax_title']; // 赋值合同乙方公司名称
        } else {
            // 不开票，取本会员账号最新的不开发票的大陆订单公司名称，如无下单记录，则留空
            if ($currency == 1 && !$contract_com_name) {
                $contract_com_name = $OrderContractModel->getLastContractName($user_id);
            }

            $order_nature = $com_nature;
        }

        // 香港订单 合同乙方公司 前台过来的需要获取CRM公司信息，后台过来的非个人性质的也需要
        if ($end['delivery_place'] == 2 && $contract_com_name && (!$type || $type && $com_nature != 0)) {
            $front_com_name = $contract_com_name;
        }

        if ($front_com_name) {
            $map = [];
            $map['tax_title'] = $front_com_name;
            $map['user_id'] = $user_id;
            $map['status'] = ['neq', -1];

            $tax_info = $TaxinfoModel->where($map)->find();

            if (!empty($tax_info)) {
                $front_customer_id = $tax_info['tax_id'];
                $order_nature = $tax_info['first_nature'];

                if (!empty($tax_info['com_id'])) {
                    $CompanyModel = D('Crm/Company');
                    $com_info = $CompanyModel->getCompanyInfo($tax_info['com_id']);

                    $order_nature = !empty($com_info['com_actual_nature']) ? $com_info['com_actual_nature'] : $com_info['com_nature'];
                }

                $order_nature = $order_nature != 0 ? $order_nature : -1; // 若发票性质为0，则订单性质默认为-1，需销售在后台选择
            }
        }

        if ($type) { // 后台下单
            $order_source = $end['order_source'] . ',ptag=ordersystem'; // 订单后台新增标记
            $customer_id  = I('customer_id', 0);
        } else {
            $order_source = $end['order_source']; // 使用最后的购物车来源作为订单来源
            $customer_id  = $front_customer_id;
        }

        $isAllZiYiXianMai = $this->checkOrderListIsZYXM($datas); //检查商品中是否全都是自营现卖的商品

        if ($this->checkOrderExistsPresell($datas)) {
            return $this->apiReturn(25009, "本网站暂不支持预售商品下单~_~");
        }

        $sale_com_id = I('sale_com_id', '');

        $org_id = 1; // 默认猎芯组织
        if ($sale_com_id && !in_array($sale_com_id, [1, 2])) {
            $org_id = 3; // 华云组织
        }

        $CrmUserModel = D('Crm/User');
        $sale_id = $CrmUserModel->getSaleId($user_id, $org_id);
        $sale_id = $sale_id ? $sale_id : 0;

        $order_create_time = time(); // 订单创建时间

        $is_all_mro_goods = $this->isAllMroGoods($datas); // 是否都是MRO商品，若是，则自动审核通过

        $order_status = 1;
        $is_manager_audit = 0;
        $sale_order_status = 1;
        $goods_lib_data = [];

        if ($is_all_mro_goods) {
            $order_status = $sale_id ? 2 : 1; // 2-待付款 - 存在销售ID则为已审核，否则待审核
            $is_manager_audit = $sale_id ? 3 : 0; // 3-主管审核通过
            $sale_order_status = $sale_id ? 3 : 2; // 3-已审核

            // 提前推送商品库，生成goods_sn
            $push_sale_data = [];
            foreach ($datas['list'] as $key => $item) {
                $push_sale_data['goods_list'][$key]['goods_name'] = $item['goods_name'];
                $push_sale_data['goods_list'][$key]['brand_name'] = $item['brand_name'];
                $push_sale_data['goods_list'][$key]['uni_key'] = md5($item['brand_name'] . $item['goods_name']);
                $push_sale_data['goods_list'][$key]['standard_brand_name'] = $item['standard_brand_name'];
                $push_sale_data['goods_list'][$key]['standard_brand_id'] = $item['standard_brand_id'];
            }

            if ($is_manager_audit == 3) {
                $res = post_curl(SALE_DOMAIN . '/inner/goods/createMultiGoods', $push_sale_data);

                $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件
                \Think\Log::write('添加到销售商品库，参数：' . json_encode($push_sale_data) . '返回数据：' . $res, INFO, '', $path);

                $res = json_decode($res, true);

                if ($res && $res['code'] == 0) {
                    foreach ($res['data']['bind_list'] as $list) {
                        $goods_lib_data[$list['uni_key']]['sale_goods_id'] = $list['goods_id'];
                        $goods_lib_data[$list['uni_key']]['goods_sn'] = $list['goods_sn'];
                        $goods_lib_data[$list['uni_key']]['brand_id'] = $list['brand_id'];
                    }
                }
            }
        }

        $is_auto_assign_sale = 0; // 是否自动分配销售
        if (!$sale_id) {
            $sale_id = C('fixed_order_sale_id'); // 固定分配给张娟
            $is_auto_assign_sale = 1;
        }

        $sale_com_id_sn = [
            1 => "SZ",
            2 => "HK",
            3 => "HY",
            9 => 'YF',
            10 => 'GP',
            11 => 'TG',
            12 => 'YN',
        ];
        $suffix = dataGet($sale_com_id_sn, $sale_com_id, "");  //增加不同组织的前后缀

        if (!$sale_com_id || in_array($sale_com_id, [1, 2])) {
            $sale_com_id = $end['delivery_place'];
            $suffix = $end['delivery_place'] == 1 ? 'SZ' : 'HK';
        } else if ($sale_com_id == 3) {
            $suffix = 'HY';
        }

        $sign_com_id = I("request.sign_com_id", 1);
        $sign_com_name = I("request.sign_com_name", '深圳市猎芯科技有限公司');

        // 前台香港订单
        if (!$type && $end['delivery_place'] == 2) {
            $sign_com_id = 2;
            $sign_com_name = '深贸电子有限公司';

            // 订货客户ID存在，则取发票上指定的签约公司
            if ($customer_id) {
                $sign_tax_info = $TaxinfoModel->where(['tax_id' => $customer_id])->find();

                $sign_com_id = $sign_tax_info['sign_com_id'];
                $sign_com_name = $sign_tax_info['sign_com_name'];
            }
        }

        $order_sn = $OrderModel->findSn(1, $suffix);

        $uc_id = I('uc_id', 0);
        if (!$uc_id) {
            $uc_id = $user_info['data']['uc_id'];
        }

        $data = array(
            'order_sn'              => $order_sn,
            'order_source'          => $order_source,
            'order_pay_type'        => $pay_type,
            'uc_id'                 => $uc_id,
            'user_id'               => $user_id,
            'order_amount'          => price_format($datas['order_amount']),
            'currency'              => $currency,
            'delivery_place'        => $end['delivery_place'],
            'order_shipping_type'   => $shipping_type,
            'order_remark'          => $order_remark,
            'sale_id'               => $sale_id,
            'order_goods_type'      => 1, //默认联营订单
            'status'                => $order_status, //待审核   如果都是自营现卖：代付款  其它：待审核
            'is_test'               => $this->getUserIsTest($user_id),
            'order_pf'              => 1, // 销售订单来源，1-猎芯网站，2-手工制单
            'order_pf_second_level' => $this->getSaleOrderSource($order_source),
            'sale_com_id'           => $sale_com_id,
            'creator_uid'           => $sale_id,
            'customer_id'           => $customer_id, // 订货公司ID
            'order_date'            => $order_create_time,
            'sale_order_status'     => $sale_order_status,
            'customer_sn'           => $customer_sn,
            'sign_com_id'           => $sign_com_id,
            'sign_com_name'         => $sign_com_name,
        );

        if ($pay_type == 2) {
            $data['advance_amount'] = price_format($data['order_amount'] * 0.3);
        }

        //团购订单
        if ($inviteFriendId > 0) {
            //邀好友订单  过滤掉自己分享的下单
            $data['order_type_extend'] = 3;
        }
        $order_id = $OrderModel->createOrder($data);
        if ($order_id === false) {
            $OrderModel->rollback();
            $this->pushReportMonitorLog(
                [
                    "interface_type" => "4",
                    "err_msg" => "创建订单主表数据表失败",
                    "err_code" => "25001",
                    "remark" => sprintf("下单 创建订单参数：%s", json_encode($data)),

                ]
            );
            return $this->apiReturn(25001, '生成订单失败', $data);
        }

        //兑人民币汇率
        // $exchange_rate = $this->getErpExchangeRate($currency, time());
        $exchange_rate = 1;

        if ($currency != 1) {
            $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.spu"));
            // $exchange_rate = $redis->hget('erp_rate', '美元');
            $exchange_rate = $redis->hget('erp_rate', $currency);

            if (empty($exchange_rate)) {
                $exchange_rate = $OrderExtendModel->getLastExchangeRate();
            }
        }

        // 扩展信息
        $data = array(
            'order_id'         => $order_id,
            'client_ip'        => get_client_ip(0, true),
            'order_type'       => $type, // 0-前台下单，后台下单：1-联营、2-自营线上、3-自营线下
            'send_remark'      => $send_remark,
            "zy_delivery_type" => $isAllZiYiXianMai ? 1 : I("send_type", 2, "intval"), //1 自营优先发货  2拼单发货
            //            'is_new' => $is_new_order, // 是否新订单
            'exchange_rate'    => $exchange_rate,
            'sensors_syn'      => 1, //神策推送
            'is_free_ship'     => $is_free_ship,
            'order_nature'     => $order_nature != 0 ? $order_nature : -1,
            'is_print_tag'     => I('is_print_tag', -1), // 是否打印标签
            'print_tag_id'     => I('print_tag_id', 0),
            'print_tag_name'   => I('print_tag_name', ''),
            'is_manager_audit' => $is_manager_audit,
            'hy_order_source'  => I('hy_order_source', 0), // 来源
        );

        if ($type == 4) {
            $data['order_type']    = 3;
            $data['business_type'] = $business_type;
        }

        if ($datas['extend_fee'] > 0) {
            $data['extend_fee_items'] = json_encode($datas['extend_items']);
        }
        $res = $OrderExtendModel->createExtent($data);
        if ($res == false) {
            $OrderModel->rollback();
            $this->pushReportMonitorLog(
                [
                    "interface_type" => "4",
                    "err_msg" => "创建订单扩展表数据表失败",
                    "err_code" => "25028",
                    "remark" => sprintf("下单 创建订单扩展数据失败参数：%s", json_encode($data)),

                ]
            );
            return $this->apiReturn(25028, '生成订单失败', $data);
        }

        // 合同乙方公司
        if ($contract_com_name) {
            $data = array(
                'order_id'          => $order_id,
                'contract_com_name' => $contract_com_name,
            );

            $res = $OrderContractModel->add($data);

            if ($res === false) {
                $OrderModel->rollback();

                return $this->apiReturn(25029, '生成订单合同表失败', $data);
            }
        }

        //优惠券占货款比例
        $rate = $datas['coupon_price'] / $datas['goods_total'];
        $preferential_sub_price = 0;
        //自营现卖
        $is_lock = 0; //默认不需要锁库

        //计算均摊价格
        $datas["order_id"] = $order_id;
        $datas = $this->getAvgPrice($datas);
        //        dump($datas);
        //商品限购
        $limitGoodsList = [];

        //bom下单
        $bom_array = [];

        //统计订单活动
        $statisticalActivity = [];
        $no_exist_standard_brand = []; // 未匹配到标准品牌
        $sku_pur_msg_info = []; // 猎芯专营的SKU推送采购邮件

        $CmsModel = D('Cms');
        $sale_info = $CmsModel->getUserInfo($sale_id);

        // 获取供应商渠道类型
        $supplier_names = array_unique(array_column($datas['list'], 'supplier_name'));

        $SupplierModel = D('Supplier');
        $channel_types = $SupplierModel->getChannelTypeByName($supplier_names);

        // 明细生成
        foreach ($datas['list'] as &$v) {
            $uni_key = md5($v['brand_name'] . $v['goods_name']);

            if (empty($v['goods_sn'])) {
                $v['goods_sn'] = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['goods_sn'] : '';
                $v['sale_goods_id'] = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['sale_goods_id'] : '';
                $v['brand_id'] = isset($goods_lib_data[$uni_key]) ? $goods_lib_data[$uni_key]['brand_id'] : '';
            }

            $_is_lock = 0;
            if ($v['goods_price'] < 0 && !$business_type) {
                $OrderModel->rollback();
                $this->pushReportMonitorLog([
                    "interface_type" => "4",
                    "err_msg" => "订单数据异常，请将商品重新加入购物车",
                    "err_code" => "25009",
                    "remark" => sprintf("下单 创建订单商品明细数据失败 参数：%s", json_encode($v)),
                ]);
                return $this->apiReturn(25009, '订单数据异常，请将商品重新加入购物车');
            }

            if (!$v['standard_brand_name']) {
                $no_exist_standard_brand[] = $v['brand_name'];
            }

            // 前台下单 且 供应商为猎芯专营 且 采购员存在
            if ($type == 0 && $v['supplier_name'] == '猎芯专营' && $v['buyer_id']) {
                $tmp = [];
                $tmp['supplier_name'] = $v['supplier_name'];
                $tmp['goods_name']    = $v['goods_name'];
                $tmp['brand_name']    = $v['brand_name'];
                $tmp['goods_number']  = $v['goods_number'];
                $tmp['goods_price']   = $v['goods_price'];
                $tmp['sale_name']     = $sale_info['name'];
                $tmp['create_time']   = date('Y-m-d H:i:s', $order_create_time);

                $sku_pur_msg_info[$v['buyer_id']][] = $tmp;
            }

            //采购员id(内部账户系统id)
            $buyer_id = 0;
            if ($v['sale_type'] == 1 && $v['order_goods_type'] == 2) {
                $buyer_id = C("CREATE_ORDER_BUYER_ID");
            } else {
                $buyer_id = isset($v["buyer_id"]) ? $v["buyer_id"] : 0;
            }

            $djkmap_goods_id = [];
            if (!empty($djkMap) && isset($djkMap[$v['goods_id']])) {
                try {
                    $djkmap_goods_id = json_decode($djkMap[$v['goods_id']], true);
                } catch (\Exception $e) {
                }
            }

            list($raw_goods_sn, $raw_goods_packing, $raw_brand_name) = $this->getDjkMapField($v, $djkmap_goods_id);
            $data = array(
                'order_id'              => $order_id,
                'user_id'               => $user_id,
                'ac_type'               => $v['ac_type'],
                'sale_type'             => $v['sale_type'], //销售类型1现卖 2预售
                'order_goods_type'      => $v['order_goods_type'], //商品类型1联营 2自营
                'original_price'        => $v['goods_price'], //记录添加时销售单价
                'preferential_price'    => $v['preferential_price'],
                'extend_price'          => $v['extend_price'],
                'single_pre_price'      => $v['single_pre_price'], // 单个商品优惠价格
                'bom_id'                => $v['bom_id'],
                'is_lock'               => $_is_lock, //是否锁定自营库存，默认0，1-锁定
                'self_supplier_type'    => isset($v['self_supplier_type']) ? $v['self_supplier_type'] : 0,
                "extra_price"           => $v['extra_price'], //费用之合（均摊后）包含（附加费和运费）
                "goods_discount_amount" => $v['goods_discount_amount'], //优惠之合(均摊后的) 优惠券
                'buyer_id'              => $buyer_id,   // 采购员id(内部账户系统id)
                'goods_source'          => $type ? 2 : 1, // 商品来源，1-前台，2-订单后台
                'tax_rate'              => OrderModel::$SALE_COM_ID_TAX[$sale_com_id], // 添加固定税率
            );

            $v['goods_name'] = strtoupper($v['goods_name']); // 型号调整为大写

            $res_id = $OrderItemsModel->createOrderItems($v, $data);
            if ($res_id === false) {
                $OrderModel->rollback();
                $this->pushReportMonitorLog([
                    "interface_type" => "4",
                    "err_msg" => "生成订单明细失败",
                    "err_code" => "25002",
                    "remark" => sprintf("下单 创建订单商品明细数据失败 参数：%s,%s", json_encode($v), json_encode($data)),
                ]);
                return $this->apiReturn(25002, '生成订单明细失败:' . $OrderItemsModel->getLastSql(), $v);
            }

            //生成明细扩展信息
            $orderItemsExtData = [];
            $orderItemsExtData["order_id"] = $order_id;
            $orderItemsExtData["rec_id"] = $res_id;
            $orderItemsExtData["remarks"] = $v['remarks']; // 商品备注
            $orderItemsExtData["batch"] = $v['batch']; // 批次
            $orderItemsExtData["raw_goods_sn"] = $raw_goods_sn; //DGK原始编码
            $orderItemsExtData["raw_goods_packing"] = $raw_goods_packing; //DGK原始包装
            $orderItemsExtData["raw_brand_name"] = $raw_brand_name; //DGK原始品牌名称
            $orderItemsExtData["goods_moq"] = $v['min_mpq'] ? $v['min_mpq'] : 1;
            $orderItemsExtData["goods_spq"] = $v['goods_spq'] ? $v['goods_spq'] : 1;
            $orderItemsExtData["goods_packing"] = $v['packing_name'] ? $v['packing_name'] : "";
            $orderItemsExtData["goods_encap"] = $v['encap'] ? $v['encap'] : "";
            $orderItemsExtData["goods_class"] = $v['class2_name'];
            $orderItemsExtData["goods_unit"] = $v['goods_unit'] ? $v['goods_unit'] : '';
            $orderItemsExtData["is_provide_dc"] = isset($v['is_provide_dc']) ? $v['is_provide_dc'] : -1;
            $orderItemsExtData["is_provide_producer"] = isset($v['is_provide_producer']) ? $v['is_provide_producer'] : -1;
            $orderItemsExtData["contract_remark"] = !empty($v['contract_remark']) ? $v['contract_remark'] : '';
            $orderItemsExtData['supplier_type'] = isset($channel_types[$v['supplier_name']]) ? $channel_types[$v['supplier_name']] : 0;

            $ext_id = $OrderItemsExtModel->add($orderItemsExtData);
            if ($ext_id === false) {
                $OrderModel->rollback();
                return $this->apiReturn(25002, '生成订单明细扩展信息失败', $v);
            }


            //限购的商品
            if (isset($v["is_quota"]) && $v["is_quota"] == 1) {
                $limitGoodsList[$v['goods_id']] = $v['goods_number'];
            }
            //bom下单
            if ($v["extend_type"] == 5) {
                array_push($bom_array, $v["extend_type_id"]);
            }
            $v["rec_id"] = $res_id;

            //添加活动信息
            $checkPriceActivity = $v["ac_type"] == 10 && !empty($v["activity_info"]);
            $specialActivityIds = cookie('activity_ids');
            if ($checkPriceActivity || $specialActivityIds) {
                $statisticalActivity[] = $this->addActivityInfo($v, $specialActivityIds);
            }
        }
        //        dump($statisticalActivity);
        //新增优惠明细
        $this->addOrderItemsDiscountDetail($order_id, $datas);
        //        $OrderModel->rollback();exit;

        // 金额生成
        if ($type == 3) { // 订单后台新增自营线下订单取消运费
            $price = array(
                '1'  => price_format($datas['goods_total']), // 货款
                '2'  => price_format($datas['extend_fee']), //附加费
                '-4' => price_format(-1 * $datas['coupon_price']), //优惠券
                '-8' => price_format(-1 * $datas['activity_price']), //活动优惠
            );
        } else {
            $price = array(
                '1'  => price_format($datas['goods_total']), // 货款
                '2'  => price_format($datas['extend_fee']), //附加费
                '3'  => price_format($datas['shipping_price']), // 运费
                '-4' => price_format(-1 * $datas['coupon_price']), //优惠券
                '-6' => price_format(-1 * $datas['free_shipping_price']), // 运费优惠
                '-8' => price_format(-1 * $datas['activity_price']), //活动优惠
            );
        }

        $price_id = $OrderPriceModel->createOrderPrice($order_id, $price, null, $currency, $order_sn);
        if ($price_id === false) {
            $OrderModel->rollback();
            $this->pushReportMonitorLog([
                "interface_type" => "4",
                "err_msg" => "生成订单金额明细失败",
                "err_code" => "25002",
                "remark" => sprintf("下单 生成订单金额明细失败 参数：%s", json_encode($price)),
            ]);
            return $this->apiReturn(25003, '生成订单金额失败');
        }

        //使用了优惠券
        //todo::优惠券
        if (!empty($datas['user_coupon_id'])) {
            if ($type) { // 订单后台新增
                $res = $this->useCouponByOrder($user_id, $datas['user_coupon_id'], $order_id, $order_sn);
            } else {
                $res = $this->useCoupon($datas['user_coupon_id'], $order_id, $order_sn);
            }

            if ($res['err_code'] != 0) {
                $OrderModel->rollback();
                $this->pushReportMonitorLog([
                    "interface_type" => "4",
                    "err_msg" => $res['err_msg'],
                    "err_code" => $res['err_code'],
                    "remark" => sprintf("下单 使用优惠券失败 参数：%s,%s", json_encode($res), json_encode($datas)),
                ]);
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }

        // 配送地址生成
        $data = array();
        if ($address_id) { // 地址ID存在时
            if ($shipping_type == 1) { // 快递配送
                if ($type) { // 订单后台新增
                    $address_info = $this->getAddressByOrder($user_id, $address_id);
                } else {
                    $address_info = $this->getAddress($address_id);
                }

                $data = $address_info['data'];

                // 若存在MRO，则禁止选择海外地址
                if ($datas['is_exists_mro'] && $data['nation_id'] != 36) {
                    return $this->apiReturn(25004, '存在MRO商品时，禁止选择海外地址');
                }

                $data['area_code'] = $data['intl_code'];
                $data['address']   = $data['detail_address'];
                $order_address     = $data; //记录订单相关地址，使用在发票处

                if ($type == 3) { // 订单后台--自营线下订单
                    $data['consignee'] = I('request.address_name');
                    $data['mobile']    = I('request.address_mobile');
                }
            } else { // 自提
                $address_info = $currency == 1 ? C('SELF_PICK_ADDRESS.' . $address_id) : C('HK_SELF_PICK_ADDRESS.' . $address_id);
                $address_id = 0;
                list($area_code, $telphone) = explode('-', $address_info['1'], 2);

                $data['address']   = $address_info[0];
                $data['consignee'] = $self_consignee;
                $data['mobile']    = $self_mobile;
            }
            $data['order_shipping_type'] = $shipping_type;
            $data['order_id']            = $order_id;
            $data['order_sn']            = $order_sn;
            $data['address_id']          = $address_id;
            $data['address_type']        = 1;
            $order_address_id = $OrderAddressModel->createOrderAddress($data);
            if ($order_address_id === false) {
                $OrderModel->rollback();
                $this->pushReportMonitorLog([
                    "interface_type" => "4",
                    "err_msg" => "生成订单收货地址失败",
                    "err_code" => 25004,
                    "remark" => sprintf("新增收货地址信息%s", json_encode($data)),

                ]);
                !empty($datas['user_coupon_id']) && $this->returnCoupon($order_id, $user_id);
                return $this->apiReturn(25004, '生成订单收货地址失败');
            }
        }

        //djk额外字段新增  不能删除此代码
        //        if(!$this->addOrderDJKInfo($is_djk,$djkParams,$order_id)){
        //            $OrderModel->rollback();
        //            return $this->apiReturn(21031, '生成digikey相关信息失败');
        //        }

        // 发票信息生成
        $data = array();
        if (!empty($tax_id)) {
            if ($type) { // 订单后台新增
                $info = $this->getUserInvoiceByOrder($user_id, $tax_id);
            } else {
                $info = $this->getUserInvoice($tax_id);
            }

            $invoice = $info['data'];
            $inv_type = $invoice['inv_type'];
            if ($inv_type == 2) {
                $data['tax_title'] = $invoice['tax_title'];
            } elseif ($inv_type == 4) {
                $data['tax_title'] = $invoice['tax_title'];
                $data['tax_no']    = $invoice['tax_no'];
            } else {
                $data = $invoice;
            }
        } else {
            $inv_type = 1;
        }
        $data['tax_id']    = $tax_id;
        $data['nike_name'] = !empty($user_info["data"]['nike_name']) ? $user_info["data"]['nike_name'] : '';
        $data['order_id']  = $order_id;
        $data['order_sn']  = $order_sn;
        $data['inv_type']  = $inv_type;
        //判断发票是否进入了小黑屋
        if (isset($invoice['tax_no']) && in_array($invoice['tax_no'], C("BCAK_HOUSE_INVOICE"))) {
            $OrderModel->rollback();
            $this->pushReportMonitorLog([
                "interface_type" => "4",
                "err_msg" => "该用户是黑名单",
                "err_code" => 21024,
                "remark" => sprintf("发票信息%s", json_encode($data)),

            ]);
            return $this->apiReturn("21024", "系统繁忙，请稍后再试");
        }
        if (isset($invoice['tax_title']) && in_array($invoice['tax_title'], C("BCAK_HOUSE_COMPANYNAME"))) {
            $OrderModel->rollback();
            $this->pushReportMonitorLog([
                "interface_type" => "4",
                "err_msg" => "该用户是黑名单",
                "err_code" => 21024,
                "remark" => sprintf("发票信息%s", json_encode($data)),

            ]);
            return $this->apiReturn("21024", "系统繁忙，请稍后再试");
        }

        $order_invoice_id  = $OrderInvoiceModel->createOrderInvoice($data);
        if ($order_invoice_id === false) {
            $OrderModel->rollback();
            $this->pushReportMonitorLog([
                "interface_type" => "4",
                "err_msg" => "生成订单发票信息失败",
                "err_code" => 25005,
                "remark" => sprintf("发票信息%s", json_encode($data)),

            ]);
            !empty($datas['user_coupon_id']) && $this->returnCoupon($order_id, $user_id);
            return $this->apiReturn(25005, '生成订单发票信息失败');
        }

        //发票收货地址
        if ($inv_type != 1) {
            $data = array();
            if (!empty($order_address)) {
                //发票地址空时直接使用订单地址
                if (empty($invoice['consignee_address']) || empty($invoice['consignee_phone'])) {
                    $data['province']  = $order_address['province'];
                    $data['city']      = $order_address['city'];
                    $data['district']  = $order_address['district'];
                    $data['address']   = $order_address['address'];
                    $data['consignee'] = $order_address['consignee'];
                    $data['mobile']    = $order_address['mobile'];
                } else {
                    $data['province']  = $invoice['consignee_province'];
                    $data['city']      = $invoice['consignee_city'];
                    $data['district']  = $invoice['consignee_district'];
                    $data['address']   = $invoice['consignee_address'];
                    $data['consignee'] = $invoice['consignee'];
                    $data['mobile']    = $invoice['consignee_phone'];
                }
            }
            $data['order_id']            = $order_id;
            $data['order_sn']            = $order_sn;
            $data['address_id']          = 0;
            $data['address_type']        = 2;
            $order_address_id = $OrderAddressModel->createOrderAddress($data);
            if ($order_address_id === false) {
                $OrderModel->rollback();
                $this->pushReportMonitorLog([
                    "interface_type" => "4",
                    "err_msg" => "生成发票收货地址失败",
                    "err_code" => 25031,
                    "remark" => sprintf("发票信息%s", json_encode($data)),

                ]);
                !empty($datas['user_coupon_id']) && $this->returnCoupon($order_id, $user_id);
                return $this->apiReturn(25031, '生成发票收货地址失败');
            }
        }

        //自营现卖下单 推送erp
        //20201130 lxb产品说自营现卖不需要推送erp 需要付款后推送
        //默认审核通过 方便付款后 推送erp
        //        if($isAllZiYiXianMai){
        ////            D('OrderExtend')->where(["order_id"=>$order_id])->save(["is_manager_audit"=>3]);
        ////            if(!$this->ziYinXianMaiPushErp($order_id)){
        ////                $OrderModel->rollback();
        ////                return $this->apiReturn(25032,"订单创建失败");
        ////            }
        //        }
        $OrderModel->commit();

        if (!empty($bom_array)) {
            //bom下单
            $this->addBomMQ($order_id, $bom_array);
        }
        //0元订单 状态改为 已付款
        // if ($datas['order_amount'] <= 0) {
        //     $save = array(
        //         'pay_type'      => 1,
        //         'price'         => 0,
        //         'pay_order_sn'  => '',
        //         'serial_number' => '-',
        //         'pay_id'        => -1,
        //         'pay_name'      => '',
        //     );
        //     $res = A('Order/Pay')->setPayOrder($order_id, $save);
        //     if (empty($res) || $res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

        //     //发送wms
        //     $res = $this->makeOrder($order_id);
        //     if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);
        // }

        //团购订单处理
        //        if($is_tuangou){
        //            $this->addTuangouOrderInfo($order_id,$order_sn,$tuantou_order_id);
        //        }

        //添加邀请好友的记录
        //此处本该写入事务，由于不想让订单牵扯太多事务，此处失败通过钉钉告警提醒即可
        if ($inviteFriendId) {
            $this->addUserInvitefriend($inviteFriendId, $order_id);
        }

        //限购商品写入用户缓存中
        $this->saveUserLimitGoods($limitGoodsList);

        //满赠商品 写入满赠信息到关联表
        $this->addOrderManZInfo($order_id, $order_sn, $_cartIds);

        //新增订单关联活动统计的信息
        $this->statisticalOrderActivity($order_id, $statisticalActivity);



        // 清除购物车，不影响流程
        try {
            A('Order/Cart')->delete();

            $event = ''; // 日志记录

            if ($type) $event = '订单后台';

            $event .= '提交订单，商品总额：' . $datas['goods_total_format'] . '，附加费：' . $datas['extend_fee_format'] . '，运费：' . $datas['finally_shipping_price_format'] . '，优惠券：' . $datas['preferential_price_format'] . '，订单总额：' . $datas['order_amount_format'];

            //通知内部人员
            $data = array(
                'data' => array(
                    'order_sn' => $order_sn,
                    'order_amount' => price_format($datas['order_amount'], C('PLACE_CURRENCY_MAPPING.' . $end['delivery_place'])),
                )
            );
            $data = json_encode($data);

            // $keyword = $datas['order_goods_type'] == 1 ? 'order-check' : 'order-new-self';
            $keyword = "order-check";
            $to_user = $CmsModel->getUserWebUserId($sale_id); // 后台业务员对应前台uid

            $event .= '，订单已分配给客服：' . $sale_info['name'];

            $this->sendOrderMsg($keyword, $data, $to_user); // 推送短信

            $is_test = $user_info['data']['is_test'];

            // 联营 且 无客服跟进 且 非测试订单，推送短信给郑家锋等
            // if (!$sale_id && !$is_test) {
            //     $keyword = 'order-send-to-manager';
            //     $to_user = C('SEND_MSG_TO_MANAGER');

            //     $this->sendOrderMsg($keyword, $data, $to_user); // 推送短信
            // }

            // 推送给内部人员
            $to_inner_user = $is_test ? 'test' : ''; // 测试单不用通知业务人员
            $this->sendOrderMsg($keyword, $data, $to_inner_user);

            // 未匹配到标准品牌，推送消息给指定的运营
            if (!empty($no_exist_standard_brand)) {
                $standard_keyword = 'order_add_standard_brand';
                $mobile = C('add_standard_brand_operator_mobile');
                $msg['content'] = implode('，', $no_exist_standard_brand) . '制造商不存在标准品牌映射关系，请帮忙添加';

                if ($operator_id) {
                    $operator_name = D('Cms')->getUserName($operator_id); // 订单后台操作人名称
                    $msg['content'] .= '，销售：' . $operator_name;
                }

                $this->sendOrderMsg($standard_keyword, json_encode($msg), $mobile, true);
            }

            //給用戶推送微信
            if (intval($type) <= 0) {
                $this->cOrderSendWachat($s_user_info, $order_sn, $order_id);
            }

            // 操作记录
            if ($type) { // 订单后台下单
                D('OrderActionLog')->addLog($order_id, $operator_id, 2, $event);
            } else { // 前台下单
                D('OrderActionLog')->addLog($order_id, $user_id, 1, $event);
            }

            if (!empty($datas['user_coupon_id'])) {
                $this->couponCount();
            }

            //提供首页滚动数据
            $coupon_info = $this->getCouponInfo($datas['user_coupon_id'], $user_id);
            $this->setRollData(2, $user_id, array(
                'order_amount'     => $datas['order_amount'],
                'order_goods_type' => $datas['order_goods_type'],
                'currency'         => $currency,
                'coupon_type'      => $coupon_info['data']['coupon_type'],
                'sale_amount'      => $coupon_info['data']['sale_amount']
            ));

            // 非测试环境下执行: 新订单 且 sale_id存在
            // if (strpos(API_DOMAIN, 'sz') === false && !$new_order_count && $sale_id) {
            //    $crm_params['user_id'] = intval($user_id);
            //    $crm_params['sale_id'] = intval($sale_id);
            //    $this->UpdateUserSales($crm_params); // 推送到crm队列 --- 20200407
            // }

            // 自动分配销售需同步到CRM
            if ($is_auto_assign_sale && strpos($_SERVER['HTTP_REFERER'], 'sz') === false) { // 非测试环境下执行
                $crm_params = [];
                $crm_params['customer_id']   = $customer_id;
                $crm_params['user_id']       = $user_id;
                $crm_params['department_id'] = $sale_info['department_id'];
                $crm_params['sale_id']       = $sale_id;
                $crm_params['sale_name']     = $sale_info['name'];
                $crm_params['operator_id']   = 1000;
                $crm_params['is_add_order']  = 1;

                $res = json_decode(post_curl(CRM_V2_DOMAIN . '/open/updateSaleByOrder', $crm_params), true);

                if (!$res || $res['code'] != 0) {
                    $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件
                    \Think\Log::write('新用户下单自动分配销售，推送到CRM失败', INFO, '', $path);
                }
            }

            // 推送采购邮件
            if (!empty($sku_pur_msg_info)) {
                $this->pushAsynQueue('/open/sendSkuPurMail', ['data' => $sku_pur_msg_info]);
            }

            // 若自动审核通过，则推送ERP
            if ($is_manager_audit == 3) {
                $this->pushAsynQueue('/open/sysErp', ['data' => ['order_id' => $order_id]]);
            }
        } catch (\Exception $e) {
        }

        return $this->apiReturn(0, '生成成功', $order_id);
    }


    /*
     * 团购订单 生成团购信息表
     */
    protected function addTuangouOrderInfo($order_id = 0, $order_sn = "", $tuantou_order_id = 0)
    {
        try {
            $user_id = cookie("uid");
            if (!$user_id) return true;
            $accouts = S_user($user_id);
            $current_time = time();
            $limited_time = rand($current_time + 24 * 3600, $current_time + 48 * 3600);
            //创建团购主订单
            if (!$tuantou_order_id) {
                $bk = D("UserGroup")->add([
                    "user_id" => cookie("uid"),
                    "account" => $accouts["mobile"] ? $accouts["mobile"] : ($accouts["user_name"] ? $accouts["user_name"] : ""),
                    "order_id" => $order_id,
                    "order_sn" => $order_sn,
                    "status" => -1,
                    "is_assign" => -1,
                    "limited_time" => 0,
                    "create_time" => time(),
                ]);
                if (!$bk) {
                    throw new \Exception("创建UserGroup团购信息表失败");
                }
            } else {
                $main_order = D("UserGroup")->field("id")->where(["order_id" => $tuantou_order_id])->find();
                $group_id = 0;
                if ($main_order) {
                    $group_id = $main_order["id"];
                }
                $bk = D("UserGroupJoin")->add([
                    "group_id" => $group_id,
                    "user_id" => cookie("uid"),
                    "account" => $accouts["mobile"] ? $accouts["mobile"] : ($accouts["user_name"] ? $accouts["user_name"] : ""),
                    "order_id" => $order_id,
                    "order_sn" => $order_sn,
                    "join_time" => time(),
                ]);
            }
        } catch (\Exception $e) {
            $request = json_encode(I("request.", ""));
            $content = sprintf("用户团购下单告警:请求参数:%s,user_id:%s,order_id:%s,异常:%s", $request, $user_id, $order_id, $e->getMessage());
            $data = array(
                'msgtype' => "text",
                'text' => array(
                    'content' => "用户团购下单告警:{$content}",
                ),
            );
            $data = json_encode($data);
            $url = "https://oapi.dingtalk.com/robot/send?access_token=a13d6128de5f918fc0f0a4cef70cab8b1962fca709649f7856aea4b1ca4dddf5";
            $bk = post_curl($url, $data, array('Content-Type:application/json;charset=UTF-8'));
        }
    }


    /*
     * 写入限购商品数据到缓存
     *
     */
    protected function saveUserLimitGoods($arr = [])
    {
        if (!cookie('uid')) return true;
        $goods = S(C("ichuntUserLimitGoodsNumsList") . cookie('uid'));
        if (empty($goods)) return;
        foreach ($arr as  $goods_id => $nums) {
            if (isset($goods[$goods_id])) {
                $goods[$goods_id] = $nums + intval($goods[$goods_id]);
            } else {
                $goods[$goods_id] = $nums;
            }
        }
        $currentTime = time();
        $endTime = strtotime(date("Y-m-d") . " 23:59:59 ");
        $expire = $endTime - $currentTime;
        if ($goods) {
            S(C("ichuntUserLimitGoodsNumsList") . cookie('uid'), $goods, ["expire" => $expire]);
        }
    }

    /**
     * 判断用户订单是否是团购主订单
     */
    public function checkTuangouOrder()
    {
        $order_id = I("request.order_id", 0, "intval");
        if (!$order_id) {
            return $this->apiReturn(21025, '没找到该订单');
        }
        $masterOrder = M("UserGroup")->where(["order_id" => $order_id])->count("id");
        if (!$masterOrder) {
            return $this->apiReturn(21026, '该订单无法分享');
        }

        return $this->apiReturn(0, '');
    }

    /**
     * 判断用户订单类型
     */
    public function check_order_type_extend()
    {
        $order_id = I("request.order_id", 0, "intval");
        $order_sn = I("request.order_sn", '', "trim");

        if (!$order_id && !$order_sn) {
            return $this->apiReturn(21025, '没找到该订单');
        }
        $where["order_id"] = $order_id;
        $where["order_sn"] = $order_sn;
        $where['_logic'] = 'or';
        $order = M("order")->where($where)->field("order_type_extend")->find();
        if (!$order) {
            return $this->apiReturn(21026, '该订单无法分享');
        }

        return $this->apiReturn(0, '', ['order_type_extend' => $order['order_type_extend']]);
    }


    /**
     * 订单推送业务员
     * @param  [type]  $user_id             [用户ID]
     * @param  [type]  $new_order_count     [是否为新订单，0为新订单]
     * @param  integer $is_test             [是否为测试人员，1为测试]
     * @param  integer $type                [前后下单，0为前台下单，其他为后台]
     * @param  [type]  $operator_id         [后台操作人ID]
     * @param  [type]  $inv_com_id          [发票公司ID]
     * @return [type]                       [sale_id]
     */
    public function orderAssign($user_id, $new_order_count, $is_test = 0, $type = 0, $operator_id = 0, $inv_com_id = 0)
    {
        $UserModel = D('Crm/User');
        $KefuModel = D('Kefu');
        $CmsModel  = D('Cms');

        $sale_id = 0;

        // 测试环境下直接返回
        if (strpos($_SERVER['HTTP_REFERER'], 'sz') !== false) {
            $sale_id = $type ? $operator_id : 0;
            return $this->apiReturn(0, '', $sale_id);
        }

        // 若发票公司ID存在，则判断公司、用户、后台登录用户的绑定关系
        if ($inv_com_id) {
            $InvoiceComUserModel = D('Invoice/InvoiceComUser');

            $res = $InvoiceComUserModel->isRelationExists($inv_com_id, $user_id, $operator_id);

            if (!$res) return $this->apiReturn(25008, '该公司、联系用户与当前后台登录人无绑定关系');

            return $this->apiReturn(0, '', $operator_id);
        }

        $crm_sale_id = $UserModel->getSaleId($user_id); // 获取CRM绑定客服ID
        $crm_sale_id = $crm_sale_id ? $crm_sale_id : 0;

        // 测试账号
        // 新用户订单：后台下单则直接推送给该客服，前台默认0（无推送人）
        // 老用户订单：后台下单则直接推送给该客服，前台推送给CRM绑定客服
        if ($is_test) {
            if (!$new_order_count) { // 新订单
                $sale_id = $type ? $operator_id : 0;
            } else {
                $sale_id = $type ? $operator_id : $crm_sale_id;
            }

            return $this->apiReturn(0, '', $sale_id);
        }

        // 后台下单
        if ($type) {
            if ($crm_sale_id && $crm_sale_id != $operator_id) { // 检查当前操作人与CRM绑定客服是否一致
                $crm_sale_name = $CmsModel->getUserName($crm_sale_id);
                return $this->apiReturn(25009, '该用户在CRM系统中，是由' . $crm_sale_name . '在跟进，请联系' . $crm_sale_name . '与主管吧~');
            }

            return $this->apiReturn(0, '', $operator_id);
        }

        // 前台下单
        if ($crm_sale_id) return $this->apiReturn(0, '', $crm_sale_id);

        $kefu = $KefuModel->getKefu(); // 若CRM无绑定，从客服池获取
        $sale_id = $kefu ? $kefu : 0;

        // 非测试环境下执行: 老用户订单 且 sale_id存在
        if (strpos(API_DOMAIN, 'sz') === false && $new_order_count && $sale_id) {
            $crm_params['user_id'] = intval($user_id);
            $crm_params['sale_id'] = intval($sale_id);

            $this->UpdateUserSales($crm_params); // 推送到crm队列 --- 20200407
        }

        return $this->apiReturn(0, '', $sale_id);
    }




    /**
     * 均摊除货款外订单各项小计 到 明细上
     * 目前可用
     * 优惠券、活动优惠、附加费
     * @param  string       $amount     需均摊金额
     * @param  array        $items      明细
     * @param  string|array $comp_value 提供比较数据
     * @param  string       $condition  比较条件
     * @return [type]                   [description]
     */
    public function avgPriceToItems($amount, $items, $comp_value = '', $condition = '')
    {
        switch ($condition) {
            case 'ac_type':
                $comp_field = 'ac_type';
                $avg_field = 'preferential_price';
                break;
            case 'extend_price':
                $avg_field = 'extend_price';
                break;
            default:
                $avg_field = 'preferential_price';
                break;
        }
        //可摊商品的总货款
        $comp_amount = 0;
        //附加费时计算供应商所属商品已处理数量
        $supplier_count = [];
        //计算可摊明细金额比例
        foreach ($items as $k => &$i) {
            //附加费针对供应商
            if ($condition == 'extend_price') {
                $key = getSuppKey($i['supplier_id'], $i['canal']);
                $supplier_key[$k] = $key;
                $supplier_count[$key] = isset($supplier_count[$key]) ?  $supplier_count[$key] + 1 : 1;
                if (isset($comp_value[$key])) {
                    $comp_key[] = $k;
                }

                //指定字段分类
            } elseif (!is_null($condition)) {
                if ($i[$comp_field] == $comp_value) {
                    $comp_key[] = $k;
                    $comp_amount += $i['goods_amount'];
                }

                //全摊
            } else {
                $comp_key[] = $k;
                $comp_amount += $i['goods_amount'];
            }
        }
        unset($i);
        //金额 占 可摊商品货款比例
        $rate = $amount / $comp_amount;
        $last_key = end($comp_key);
        $sum_price = 0; //累计均摊金额
        foreach ($items as $k => &$i) {
            //没字段初始化0
            if (!isset($i[$avg_field])) {
                $i[$avg_field] = 0;
            }
            //不在均摊范围内明细
            if (!in_array($k, $comp_key)) {
                continue;
            }

            if ($condition == 'extend_price') {
                //供应商附加费均摊 只均摊 供应商自己的所属商品的附加费
                //通过计数来确定最后一个
                $key = $supplier_key[$k];
                if (0 < --$supplier_count[$key]) {
                    $i[$avg_field] = price_format(($i['goods_price'] * $i['goods_number'] / $comp_value[$key]['amount']) * $comp_value[$key]['extend_fee'], 0, 4);
                } else {
                    $i[$avg_field] = $comp_value[$key]['extend_fee'] - $comp_value[$key]['sum_supplier_amount'];
                }
                $comp_value[$key]['sum_supplier_amount'] += $i[$avg_field];
            } else {
                //普通均摊
                $avg_price = price_format($rate * $i['goods_price'] * $i['goods_number'], 0, 4);
                if ($k == $last_key) {
                    $i[$avg_field] += $amount - $sum_price;
                } else {
                    $i[$avg_field] += $avg_price;
                }
                $sum_price += $avg_price;
            }
        }
        unset($i);
        return $items;
    }

    /**
     * 计算均摊后单价
     * @param  string $order_amount 订单金额
     * @param  array  $items        明细
     *                goods_amount  商品小计
     *                preferential_price  优惠小计（优惠券+活动优惠）
     *                extend_price  附加费小计
     *                goods_number  数量
     *                goods_price   单价
     * @return [type]               [description]
     */
    public function avgSinglePrice($order_amount, $items)
    {
        //总附加费
        $extend_fee = 0;
        //含优惠货款 = 货款 - 优惠金额（优惠券、活动优惠）
        $pre_goods_amount = 0;
        foreach ($items as &$i) {
            $extend_fee += $i['extend_price'];
            $pre_goods_amount += $i['goods_amount'] - $i['preferential_price'];
        }
        $last_key = array_pop(array_keys($items));
        unset($i);
        // 订单总额减去附加费
        $pre_amount = $order_amount - $extend_fee;
        //累计明细金额
        $sum_item_pay_amount = 0;
        foreach ($items as $k => &$i) {
            //附加费单价
            $extend_price = $i['extend_price'] / $i['goods_number'];
            // 计算减去附加费后的优惠单价
            if ($k == $last_key) { //最后一条摊优惠金额
                $pre_price = ($pre_amount - $sum_item_pay_amount) / $i['goods_number'];
            } else {
                //不含附加费的 均摊 订单金额
                $avg_amount = (($i['goods_price'] * $i['goods_number'] - $i['preferential_price']) / $pre_goods_amount) * $pre_amount;
                $pre_price = $avg_amount / $i['goods_number'];
            }

            $i['single_pre_price'] = price_format($extend_price + $pre_price, 0, 6);
            $sum_item_pay_amount += $avg_amount;
        }
        unset($i);
        return $items;
    }

    // 查看用户最近的一条订单
    public function findLastedSaleId($user_id)
    {
        $map['user_id']          = $user_id;
        $map['order_type']       = 1; // 平台订单
        $map['is_type']          = 0; // 过滤尽调数据
        $map['sale_id']          = ['neq', 0];

        $sale_id = D('Order')->where($map)->order('order_id DESC')->getField('sale_id');

        return $sale_id ? $sale_id : 0;
    }

    /**
     * 创建订单
     * @return [type] [description]
     */
    public function createOrder()
    {
        $order_sn = I('request.order_sn', '');
        $pay_type = I('request.pay_type', 1, 'intval');
        $order_amount = I('request.order_amount', '');
        $delivery_place = I('request.delivery_place', 1, 'intval');
        $shipping_type = I('request.shipping_type', 1, 'intval');
        $order_type = I('request.order_type', 2, 'intval');
        $status = I('request.status', 0, 'intval');
        $create_time = I('request.create_time', 0, 'intval');
        $confirm_time = I('request.confirm_time', 0, 'intval');
        $pay_time = I('request.pay_time', 0, 'intval');
        $finish_time = I('request.finish_time', 0, 'intval');
        $user_id = cookie('uid');

        $OrderModel = D('Order');
        $OrderPriceModel = D('OrderPrice');
        $data = array(
            'order_sn' => $order_sn,
            'order_pay_type' => $pay_type,
            'order_type' => $order_type,
            'user_id' => $user_id,
            'order_amount' => $order_amount,
            'currency' => C('PLACE_CURRENCY_MAPPING.' . $delivery_place),
            'delivery_place' => $delivery_place,
            'order_shipping_type' => $shipping_type,
            'status' => $status,
            'create_time' => $create_time,
            'confirm_time' => $confirm_time,
            'pay_time' => $pay_time,
            'finish_time' => $finish_time,
        );
        if ($pay_type == 2) {
            $data['advance_amount'] = price_format($data['order_amount'] * 0.3);
        }
        $order_id = $OrderModel->createOrder($data);
        if ($order_id === false) {
            return $this->apiReturn(25001, '生成订单失败', $data);
        }

        // 金额生成
        $price['1'] = array(price_format($data['order_amount']), $create_time); // 货款
        if ($status > 4) {
            $price['-1'] = array(price_format($data['order_amount']), $pay_time); // 付款
        }
        $price_id = $OrderPriceModel->createOrderPrice($order_id, $price, null, $data['currency'], $order_sn);
        if ($price_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(25003, '生成订单金额失败');
        }

        return $this->apiReturn(0, '生成成功', $order_id);
    }

    /**
     * 创建明细
     * @return [type] [description]
     */
    public function createItems()
    {
        $OrderItemsModel = D('OrderItems');
        $order_id = I('request.order_id', 0, 'intval');
        $goods_name = I('request.goods_name', '');
        $sku_name = I('request.sku_name', '');
        $brand_name = I('request.brand_name', '');
        $goods_number = I('request.goods_number', 1, 'intval');
        $goods_price = I('request.goods_price', '');
        $erp_rec_id = I('request.erp_rec_id', '');
        $user_id = cookie('uid');

        $item = $OrderItemsModel->getUserRecId($user_id, $erp_rec_id);
        if (empty($item)) {
            $data = array(
                'order_id' => $order_id,
                'user_id' => $user_id,
                'erp_rec_id' => $erp_rec_id,
                'goods_name' => $goods_name,
                'sku_name' => !empty($sku_name) ? $sku_name : $goods_name,
                'brand_name' => $brand_name,
                'goods_number' => $goods_number,
                'goods_price' => $goods_price,
            );
            $rec_id = $OrderItemsModel->createOrderItems($data);
            if ($rec_id === false) {
                $OrderModel->rollback();
                return $this->apiReturn(25002, '生成订单明细失败', $v);
            }
        } else {
            $rec_id = $item['rec_id'];
        }
        return $this->apiReturn(0, '生成成功', $rec_id);
    }

    /**
     * 获取订单列表接口
     * @return [type] [description]
     */
    public function lists()
    {
        $p = I('request.p', 1, 'intval');
        $filter = array('O.order_sn', 'O.order_goods_type', 'OI.goods_name', 'OI.sku_name', 'O.status', 'O.currency', 'O.customer_sn', 'O.stime', 'O.etime', "keyword", 'goods_sku_name');
        $user_id = cookie('uid');
        $OrderModel = D('Order');
        $OrderItemsModel = D('OrderItems');
        $OrderItemsExtModel = D('OrderItemsExt');
        $OrderPriceModel = D('OrderPrice');
        $OrderAddressModel = D('OrderAddress');
        $OrderInvoiceModel = D('OrderInvoice');
        $OrderExtendModel = D('OrderExtend');
        // 条件
        $map = map_filter($filter);
        $map['O.is_type'] = 0;

        $count = $OrderModel->getUserCount($user_id, $map);

        if ($count == 0) {
            return $this->apiReturn(21004, '未找到相关订单');
        }
        $data = $OrderModel->getUserList($user_id, $map, $p);

        foreach ($data as &$v) {
            $v['order_sn'] = !empty($v['order_sn']) ? $v['order_sn'] : $v['sale_order_sn'];

            $shipping_price = $OrderPriceModel->getShippingPrice($v['order_id']);
            $extend_fee     = $OrderPriceModel->getExtPrice($v['order_id']);
            $coupon_price = $OrderPriceModel->getActivityPrice($v['order_id']);
            $activity_price = $OrderPriceModel->getPreferentialPrice($v['order_id']);
            $address        = $OrderAddressModel->getInfo($v['order_id'], 1);
            $invoice        = $OrderInvoiceModel->getInfo($v['order_id']);
            $extend_info    = $OrderExtendModel->getInfo($v['order_id']);

            if (C('MKT_POINT_TIME') < $v['create_time']) {
                $mkt_point = conver_mkt_point($v['order_amount'], $v['order_goods_type'], $v['currency'], $v['order_pay_type']);
            } else {
                $mkt_point = 0;
            }


            $v['mkt_point'] = $mkt_point;
            $v['inv_type'] = $invoice['inv_type'];
            $v['extend_fee_format'] = price_format($extend_fee, $v['currency']);
            $v['coupon_price_format'] = price_format($coupon_price, $v['currency']);
            $v['activity_price_format'] = price_format($activity_price, $v['currency']);
            $v['shipping_price'] = price_format($shipping_price);
            $v['shipping_price_format'] = price_format($shipping_price, $v['currency']);
            $v['consignee'] = $address['consignee'];
            $v['mobile'] = $address['mobile'];
            $v['address_val'] = $address['province_val'] . $address['city_val'] . $address['district_val'] . $address['address'];
            $v['sale_type_val'] = C('SALE_TYPE_NAME.' . $v['sale_type']);
            $v['order_goods_type_val'] = C('ORDER_GOODS_TYPE.' . $v['order_goods_type']);
            $v['status_val'] = C('ORDER_STATUS.' . $v['status']);
            $v['create_time'] = date('Y-m-d H:i', $v['create_time']);
            $v['business_type'] = $extend_info['business_type'];
            $v["exist_gift"] = D("OrderGift")->countOrderGift($v['order_id']);

            $v['list'] = $OrderItemsModel->getOrderList($v['order_id'], '', null);

            $goods_ids = array_filter(array_column($v['list'], 'goods_id'));
            $res = $this->getGoods($goods_ids);
            $goods_info = $res['data'];

            $isAllZiYinXianMai = true;
            foreach ($v['list'] as $key => $goodsItems) {
                $goods_id = $goodsItems['goods_id'];
                $v['list'][$key]['goods_images'] = 'http://static.ichunt.com/dist/res/home/<USER>/goods_default.png';

                if (!empty($goods_info[$goods_id]['goods_images'])) {
                    $v['list'][$key]['goods_images'] = $goods_info[$goods_id]['goods_images'];
                }

                if ($goodsItems["order_goods_type"] != 2 || $goodsItems["sale_type"] != 1) {
                    $isAllZiYinXianMai = false;
                    // break;
                }

                if (in_array($goodsItems['supplier_name'], ['立创', '云汉'])) {
                    $v['list'][$key]['supplier_id'] = 17;
                    $v['list'][$key]['supplier_name'] = '猎芯专营';
                }
            }

            // 待付款状态显示剩余时间
            if ($v['status'] == 2) {
                $rest_time_day = 0;
                $rest_time_hour = 0;

                if ($v['pay_time']) {
                    if ($v['pay_time'] > time() && $v['pay_time'] > $v['confirm_time']) {
                        $rest_time_day = floor(($v['pay_time'] - time()) / (60 * 60 * 24));
                        $rest_time_hour = floor((($v['pay_time'] - time()) / (60 * 60)) - ($rest_time_day * 24));
                    }
                } else {
                    if (($v['confirm_time'] + 30 * 60 * 60 * 24) > time()) {
                        $rest_time_day = floor((($v['confirm_time'] + 30 * 60 * 60 * 24) - time()) / (60 * 60 * 24));
                        $rest_time_hour = floor(((($v['confirm_time'] + 30 * 60 * 60 * 24) - time()) / (60 * 60)) - $rest_time_day * 24);
                    }
                }

                $rest_day = $rest_time_day > 0 ? sprintf('%2d', $rest_time_day) : 0;
                $rest_hour = $rest_time_hour > 0 ? sprintf('%2d', $rest_time_hour) : 0;

                $v['rest_time'] = $rest_day . '天' . $rest_hour . '时';

                // 自营倒计时
                if ($isAllZiYinXianMai) {
                    // 如果pay_time存在，则使用pay_time
                    if ($v['pay_time']) {
                        $over_time = $v['pay_time']; // 默认过期时间
                    } else {
                        $over_time = $v['create_time'] + C('self_rest_time') * 86400; // 默认过期时间
                    }

                    $c_week_val = date('w', $v['create_time']); // 创建时间所处的星期
                    $o_week_val = date('w', $over_time); // 过期时间所处的星期

                    // 过滤周六、日，默认加两天
                    if (in_array($o_week_val, [0, 6])) {
                        $over_time = $over_time + 172800;
                    } else if (in_array($c_week_val, [0, 6])) {
                        $over_time = $over_time + 172800;
                    }


                    $rest_time_day = floor(($over_time - time()) / (60 * 60 * 24));
                    $rest_time_hour = floor((($over_time - time()) / (60 * 60)) - ($rest_time_day * 24));

                    $remain_time_day = floor(($over_time - time()) / (60 * 60 * 24));

                    $remain_day = $remain_time_day > 0 ? sprintf('%2d', $remain_time_day) : 0;
                    $info['remain_day'] = $remain_day . '天';

                    $rest_day = $rest_time_day > 0 ? sprintf('%2d', $rest_time_day) : 0;
                    $rest_hour = $rest_time_hour > 0 ? sprintf('%2d', $rest_time_hour) : 0;

                    $v['rest_time'] = $rest_day . '天' . $rest_hour . '时';
                }
            }
        }
        $datas = page_data($data, $count, $p);

        return $this->apiReturn(0, '获取成功', $datas);
    }

    // 会员中心 - 我的订单 - 添加自定义订单号
    public function addCustomerSn()
    {
        $order_id = I('order_id', 0);
        $customer_sn = I('customer_sn', '');
        $user_id = cookie('uid');

        if (!$order_id) {
            return $this->apiReturn(-1, '订单ID缺失');
        }

        // if (!$customer_sn) {
        //     return $this->apiReturn(-1, '自定义订单号不能为空');
        // }

        $OrderModel = D('Order');
        $order = $OrderModel->checkOrderByUser($user_id, $order_id);

        if (!$order) {
            return $this->apiReturn(-1, '未找到相关订单');
        }

        $update = [
            'customer_sn' => $customer_sn,
            'update_time' => time(),
        ];

        $res = $OrderModel->where(['order_id' => $order_id])->save($update);

        if ($res === false) {
            return $this->apiReturn(-1, '更新自定义订单号失败');
        }

        return $this->apiReturn(0, '成功');
    }

    // PC会员中心 - 个人资料 - 订单跟踪
    // 显示状态为待付款、待付尾款、待发货、待收货、部分发货的订单号，并按订单创建时间正序排列，默认显示最早创建的订单号
    public function track()
    {
        $order_id = I('order_id', 0);
        $user_id = cookie('uid');

        $OrderModel = D('Order');
        $OrderPriceModel = D('OrderPrice');

        $datas = [];
        $datas['order_sns'] = [];
        $datas['info'] = [];

        $map = [];
        $map['O.status'] = ['in', [2, 3, 4, 7, 8]];

        $order = $OrderModel->getUserList($user_id, $map, '', 'create_time ASC');

        if (empty($order)) {
            return $this->apiReturn(0, '', $datas);
        }

        $order_ids = array_column($order, 'order_id');

        foreach ($order as $k => $v) {
            $datas['order_sns'][$k]['order_id'] = $v['order_id'];
            $datas['order_sns'][$k]['order_sn'] = $v['order_sn'] ?: $v['sale_order_sn'];
        }

        // 待付款、待付尾款展示支付信息：商品总金额、附加费、运费、已支付金额、待支付金额
        // 待发货、待收货、部分发货展示物流信息
        if ($order_id && in_array($order_id, $order_ids)) { // 传递过来的订单ID是否在限制的状态内
            $order_info = $OrderModel->getInfo($order_id);

            $status = $order_info['status'];
            $order_id = $order_info['order_id'];
            $order_amount = $order_info['order_amount'];
            $pay_time = $order_info['pay_time'];
            $confirm_time = $order_info['confirm_time'];
            $currency = $order_info['currency'];

            $datas['order_id'] = $order_id;
            $datas['info']['order_pay_type'] = $order_info['order_pay_type'];
        } else {
            $status = $order[0]['status'];
            $order_id = $order[0]['order_id'];
            $order_amount = $order[0]['order_amount'];
            $pay_time = $order[0]['pay_time'];
            $confirm_time = $order[0]['confirm_time'];
            $currency = $order[0]['currency'];

            $order_info = $OrderModel->getInfo($order[0]['order_id']);

            $datas['order_id'] = $order[0]['order_id'];
            $datas['info']['order_pay_type'] = $order_info['order_pay_type'];
        }

        if (in_array($status, [2, 3])) {
            $goods_amount = $OrderPriceModel->getGoodsPrice($order_id); // 商品总额
            $ext_amount = $OrderPriceModel->getExtPrice($order_id); // 附加费
            $shipping_amount = $OrderPriceModel->getShippingPrice($order_id); // 运费
            $paid_amount = $OrderPriceModel->getPayed($order_id); // 已支付金额
            $no_pay_amount = $order_amount - $paid_amount; // 待支付金额

            $datas['info']['is_show_shipping'] = 0; // 是否展示物流
            $datas['info']['goods_amount'] = price_format($goods_amount, $currency);
            $datas['info']['ext_amount'] = price_format($ext_amount, $currency);
            $datas['info']['shipping_amount'] = price_format($shipping_amount, $currency);
            $datas['info']['paid_amount'] = price_format($paid_amount, $currency);
            $datas['info']['no_pay_amount'] = price_format($no_pay_amount, $currency);
            $datas['info']['order_amount'] = price_format($order_amount, $currency);
            $datas['info']['rest_time'] = ''; // 支付剩余时间

            if ($status == 2) {
                if ($pay_time && $pay_time > time() && $pay_time > $confirm_time) {
                    $rest_time_day = floor(($pay_time - time()) / 86400);
                    $rest_time_hour = floor((($pay_time - time()) / 3600) - ($rest_time_day * 24));
                } else {
                    $rest_time_day = floor((($confirm_time + 30 * 86400) - time()) / 86400);
                    $rest_time_hour = floor(((($confirm_time + 30 * 86400) - time()) / 3600) - $rest_time_day * 24);
                }

                $rest_day = $rest_time_day > 0 ? sprintf('%2d', $rest_time_day) : 0;
                $rest_hour = $rest_time_hour > 0 ? sprintf('%2d', $rest_time_hour) : 0;

                $datas['info']['rest_time'] = $rest_day . '天' . $rest_hour . '时';
            }
        } else {
            $datas['info']['is_show_shipping'] = 1;

            $params = [];
            $params['id']  = $order_id;
            $params['uid'] = $user_id;
            $params['k1']  = time();
            $params['k2']  = pwdhash($params['k1'], C('SUPER_AUTH_KEY'));

            $res = post_curl(API_DOMAIN . '/order/shipping', $params);
            $res = json_decode($res, true);

            if ($res['err_code'] == 0) {
                $datas['info']['shipping'] = $res['data'];
            }
        }

        return $this->apiReturn(0, '', $datas);
    }

    /**
     * 订单列表导出
     * @return [type] [description]
     */
    public function exportList()
    {
        $filter = array('O.order_sn', 'O.order_goods_type', 'OI.goods_name', 'OI.sku_name', 'O.status', 'O.currency', 'O.stime', 'O.etime');
        $user_id = cookie('uid');
        $OrderModel = D('Order');
        $OrderPriceModel = D('OrderPrice');
        $OrderItemsModel = D('OrderItems');
        $order_ids = array();
        $goods_total_sum = 0;
        // 条件
        $map = map_filter($filter);
        if (isset($map['O.create_time'])) { //必须划分时间
            $datas = $OrderModel->getUserList($user_id, $map, null, 'O.order_id DESC', 'O.order_id, O.order_sn, O.create_time, O.currency');
            if (!empty($datas)) {
                $order = array();
                for ($i = 0; $i < count($datas); $i++) {
                    $order[$datas[$i]['order_id']] = $datas[$i];
                    $order_ids[] = $datas[$i]['order_id'];
                }
                $goods_total = $OrderPriceModel->getGoodsPrice($order_ids, true);
                $shipping_price = $OrderPriceModel->getShippingPrice($order_ids, true);
                $all_preferential_price = $OrderPriceModel->getAllPreferentialPrice($order_ids, true);
                $goods_total_sum = array_sum($goods_total);
            }
            unset($datas);
        }
        if (!empty($order_ids)) {
            $map = array(
                'order_id' => array('in', $order_ids)
            );
            $items = $OrderItemsModel->getList($map, 'order_id, goods_name, brand_name, goods_price, goods_number', null);
        } else {
            $items = array();
        }

        //数据归类
        $order_sn = '';
        foreach ($items as &$v) {
            if ($order[$v['order_id']]['order_sn'] != $order_sn) {
                $v['order_sn'] = $order[$v['order_id']]['order_sn'];
                $v['create_time'] = date('Y-m-d H:i', $order[$v['order_id']]['create_time']);
                $v['shipping_fee'] = $shipping_price[$v['order_id']];
                $v['all_preferential_price'] = $all_preferential_price[$v['order_id']];
            } else {
                $v['order_sn'] = '';
                $v['create_time'] = '';
                $v['shipping_fee'] = '';
                $v['all_preferential_price'] = '';
            }
            $v['currency'] = C('CURRENCY_CODE.' . $order[$v['order_id']]['currency']);
            $v['goods_amount'] = price_format($v['goods_price'] * $v['goods_number'], 0, 4);
            $order_sn = $order[$v['order_id']]['order_sn'];
        }
        unset($v);

        //抬头
        $field = array(
            'order_sn' => '订单号',
            'goods_name' => '产品型号',
            'brand_name' => '品牌',
            'currency' => '币种',
            'goods_price' => '购买时单价',
            'goods_number' => '数量',
            'goods_amount' => '小计',

            'shipping_fee' => '运费',
            'all_preferential_price' => '减免及优惠',
            'create_time' => '下单时间',
            'goods_total' => '订单商品总金额'
        );
        $row[] = '"' . implode('","', $field) . '"';
        //数据
        foreach ($items as $data) {
            foreach ($field as $k => $v) {
                if (!isset($data[$k])) {
                    continue;
                }
                if (in_array($k, array('order_sn'))) {
                    $cell[] = !empty($data[$k]) ? '"\'' . addslashes($data[$k]) . '"' : '""';
                } else {
                    $cell[] = '"' . addslashes($data[$k]) . '"';
                }
            }
            $row[] = implode(',', $cell);
            unset($cell);
        }

        //商品总金额行
        foreach ($field as $k => $v) {
            if ($k == 'goods_total') {
                $cell[] = '"' . $goods_total_sum . '"';
            } else {
                $cell[] = '""';
            }
        }
        $row[] = implode(',', $cell);
        $content = iconv('utf-8', 'GBK', implode("\n", $row));
        download($content, '订单导出' . date('Y-m-d') . '.csv');
    }

    /**
     * 获取订单列表接口
     * @return [type] [description]
     */
    public function allList()
    {
        if (!$this->auth()) {
            exit();
        }
        $filter = array('stime', 'etime');
        $is_type = I('is_type', '');
        $OrderModel = D('Order');
        $OrderItemsModel = D('OrderItems');

        // 条件
        $map = map_filter($filter);
        if ($is_type !== '') {
            $map['is_type'] = $is_type; //是否假数据
        }
        $map['order_goods_type'] = 1;
        $data = $OrderModel->getList($map, null, 'order_id', 'order_id,currency,create_time');

        foreach ($data as &$v) {
            $v['list'] = $OrderItemsModel->getOrderList($v['order_id'], '', null);
        }
        return $this->apiReturn(0, '获取成功', $data);
    }

    /**
     * 获取订单基本信息
     * @return [type] [description]
     */
    public function findOrder()
    {
        $order_id = I('request.order_id', 0, 'intval');
        $order_sn = I('request.order_sn', '');
        $OrderModel = D('Order');
        $info = $OrderModel->getInfo($order_id, $order_sn);
        if (empty($info)) {
            return $this->apiReturn(21004, '未找到相关订单');
        }
        return $this->apiReturn(0, '获取成功', $info);
    }

    /**
     * 获取订单最后付款金额及 单号信息
     * @return [type] [description]
     */
    public function lastPayInfo()
    {
        $order_id = I('request.order_id', 0, 'intval');
        $order_sn = I('request.order_sn', '');
        $OrderModel = D('Order');
        $PayLogModel = D('PayLog');
        $info = $OrderModel->getInfo($order_id, $order_sn);
        if (empty($info) || ($sample === false && $info['user_id'] != $user_id)) {
            return $this->apiReturn(21004, '未找到相关订单');
        }
        // if (C('MKT_POINT_TIME') < $info['create_time']) {
        //     $mkt_point = conver_mkt_point($info['order_amount'], $info['order_goods_type'], $info['currency']);
        // } else {
        //     $mkt_point = 0;
        // }
        $last = $PayLogModel->getLastPay($info['order_id'], 'pay_amount');
        $data = array(
            'order_id' => $info['order_id'],
            'order_sn' => $info['order_sn'],
            // 'mkt_point' => $mkt_point,
            'pay_amount_format' => price_format($last['pay_amount'], $info['currency']),
        );
        return $this->apiReturn(0, '', $data);
    }

    /**
     * 获取订单明细
     * @return [type] [description]
     */
    public function info($sample = false)
    {
        $order_id           = I('request.order_id', 0, 'intval');
        $order_sn           = I('request.order_sn', '');
        $user_id            = cookie('uid');
        $OrderModel         = D('Order');
        $OrderItemsModel    = D('OrderItems');
        $OrderPriceModel    = D('OrderPrice');
        $OrderInvoiceModel  = D('OrderInvoice');
        $OrderAddressModel  = D('OrderAddress');
        $OrderShippingModel = D('OrderShipping');
        $PayLogModel        = D('PayLog');
        $OrderExtendModel   = D('OrderExtend');
        $OrderGiftModel     = D('OrderGift');
        $OrderAttachmentModel = D('OrderAttachment');

        $info = $OrderModel->getInfo($order_id, $order_sn);
        if (empty($info) || ($sample === false && $info['user_id'] != $user_id)) {
            return $this->apiReturn(21004, '未找到相关订单');
        }
        $order_id = $info['order_id'];
        $last_pay = $PayLogModel->getLastPay($order_id, 'pay_name');

        // 金额相关
        $extend_fee             = $OrderPriceModel->getExtPrice($order_id);
        $goods_total            = $OrderPriceModel->getGoodsPrice($order_id);
        $topay_amount           = $OrderPriceModel->getToPay($order_id);
        // $topay_amount           = abs($OrderPriceModel->getPayed($order_id)); // 调整为实际收款
        $preferential_price     = $OrderPriceModel->getAllPreferentialPrice($order_id);
        $coupon_price           = $OrderPriceModel->getPreferentialPrice($order_id);
        $activity_price         = $OrderPriceModel->getActivityPrice($order_id);
        $shipping_price         = $OrderPriceModel->getShippingPrice($order_id);
        $pay_preferential_price = $OrderPriceModel->getPayPreferential($order_id);

        // 订单扩展信息
        $extend_info = $OrderExtendModel->getInfo($order_id);

        if (C('MKT_POINT_TIME') < $info['create_time']) {
            $mkt_point = conver_mkt_point($info['order_amount'], $info['order_goods_type'], $info['currency'], $info['order_pay_type']);
        } else {
            $mkt_point = 0;
        }

        $info['mkt_point']                     = $mkt_point;
        $info['preferential_price']            = price_format($preferential_price);
        $info['preferential_price_format']     = price_format($preferential_price, $info['currency']);
        $info['coupon_price']                  = price_format($coupon_price);
        $info['coupon_price_format']           = price_format($coupon_price, $info['currency']);
        $info['activity_price']                = price_format($activity_price);
        $info['activity_price_format']         = price_format($activity_price, $info['currency']);
        $info['pay_preferential_price']        = price_format($pay_preferential_price);
        $info['pay_preferential_price_format'] = price_format($pay_preferential_price, $info['currency']);
        $info['shipping_price']                = price_format($shipping_price);
        $info['shipping_price_format']         = price_format($shipping_price, $info['currency']);
        $info['extend_fee_format']             = price_format($extend_fee, $info['currency']);
        $info['goods_total_format']            = price_format($goods_total, $info['currency']);
        $info['topay_amount_format']           = price_format($topay_amount, $info['currency']);

        // 商品明细
        $list = $OrderItemsModel->getOrderList($order_id, '', null);

        $goods_ids = array_filter(array_column($list, 'goods_id'));
        $res = $this->getGoodsData($goods_ids);
        $goods_info = $res['data'];

        $isAllZiYinXianMai = true;
        foreach ($list as $k => $v) {
            $goods_id = $v['goods_id'];
            $list[$k]['goods_images'] = 'http://static.ichunt.com/dist/res/home/<USER>/goods_default.png';

            if (!empty($goods_info[$goods_id]['goods_images'])) {
                $list[$k]['goods_images'] = $goods_info[$goods_id]['goods_images'];
            }

            if ($v["order_goods_type"] != 2 || $v["sale_type"] != 1) {
                $isAllZiYinXianMai = false;
                // break;
            }

            if (in_array($v['supplier_name'], ['立创', '云汉'])) {
                $list[$k]['supplier_id'] = 17;
                $list[$k]['supplier_name'] = '猎芯专营';
            }
        }

        // if ($extend_info['order_type'] == 1) { // 后台联营下单
        //     foreach ($list as &$v) {
        //         $v['supplier_name'] = $v['supplier_name'] == '平台' ? '猎芯自营' : '猎芯联营';
        //     }
        // }

        $info['goods_count'] = count($list);
        $info['list'] = $list;

        // 收货地址
        $address = $OrderAddressModel->getInfo($order_id, 1);
        $info = array_merge($info, $address);

        // 快递信息
        $shipping = $OrderShippingModel->getInfo($order_id, 1);
        $info['shipping_name'] = !empty($shipping['shipping_name']) ? $shipping['shipping_name'] : '';

        // 订单发票信息
        $inv = $OrderInvoiceModel->getInfo($order_id);
        $inv['inv_type_val'] = $this->getConf('invoice', 'INVOICE_TYPE', $inv['inv_type']);
        $info['inv'] = $inv;

        $info['order_pay_type_val'] = C('ORDER_PAY_TYPE_NAME.' . $info['order_pay_type']);
        $info['sale_type_val']      = C('SALE_TYPE_NAME.' . $info['sale_type']);
        $info['status_val']         = C('ORDER_STATUS.' . $info['status']);

        if (in_array($last_pay['pay_name'], array('交通银行', '恒生银行'))) {
            $info['client_pay_type_val'] = '线下支付';
        } elseif (!empty($last_pay['pay_name'])) {
            $info['client_pay_type_val'] = '线上支付';
        } else {
            $info['client_pay_type_val'] = '';
        }

        // 待付款状态下显示剩余时间
        if ($info['status'] == 2) {
            $rest_time_day = 0;
            $rest_time_hour = 0;
            $remain_time_day = 0;

            if ($info['pay_time']) {
                if ($info['pay_time'] > time() && $info['pay_time'] > $info['confirm_time']) {
                    $rest_time_day = floor(($info['pay_time'] - time()) / (60 * 60 * 24));
                    $rest_time_hour = floor((($info['pay_time'] - time()) / (60 * 60)) - ($rest_time_day * 24));

                    $remain_time_day = floor(($info['pay_time'] - $info['confirm_time']) / (60 * 60 * 24));
                }
            } else {
                if (($info['confirm_time'] + 30 * 60 * 60 * 24) > time()) {
                    $rest_time_day = floor((($info['confirm_time'] + 30 * 60 * 60 * 24) - time()) / (60 * 60 * 24));
                    $rest_time_hour = floor(((($info['confirm_time'] + 30 * 60 * 60 * 24) - time()) / (60 * 60)) - $rest_time_day * 24);

                    $remain_time_day = floor((($info['confirm_time'] + 30 * 60 * 60 * 24) - time()) / (60 * 60 * 24));
                }
            }

            $remain_day = $remain_time_day > 0 ? sprintf('%2d', $remain_time_day) : 0;
            $info['remain_day'] = $remain_day . '天';

            $rest_day = $rest_time_day > 0 ? sprintf('%2d', $rest_time_day) : 0;
            $rest_hour = $rest_time_hour > 0 ? sprintf('%2d', $rest_time_hour) : 0;

            $info['rest_time'] = $rest_day . '天' . $rest_hour . '时';

            // 自营倒计时
            if ($isAllZiYinXianMai) {
                // 如果pay_time存在，则使用pay_time
                if ($info['pay_time']) {
                    $over_time = $info['pay_time']; // 默认过期时间
                } else {
                    $over_time = $info['create_time'] + C('self_rest_time') * 86400; // 默认过期时间
                }

                $c_week_val = date('w', $info['create_time']); // 创建时间所处的星期
                $o_week_val = date('w', $over_time); // 过期时间所处的星期

                // 过滤周六、日，默认加两天
                if (in_array($o_week_val, [0, 6])) {
                    $over_time = $over_time + 172800;
                } else if (in_array($c_week_val, [0, 6])) {
                    $over_time = $over_time + 172800;
                }

                $rest_time_day = floor(($over_time - time()) / (60 * 60 * 24));
                $rest_time_hour = floor((($over_time - time()) / (60 * 60)) - ($rest_time_day * 24));

                $remain_time_day = floor(($over_time - time()) / (60 * 60 * 24));

                $remain_day = $remain_time_day > 0 ? sprintf('%2d', $remain_time_day) : 0;
                $info['remain_day'] = $remain_day . '天';

                $rest_day = $rest_time_day > 0 ? sprintf('%2d', $rest_time_day) : 0;
                $rest_hour = $rest_time_hour > 0 ? sprintf('%2d', $rest_time_hour) : 0;

                $info['rest_time'] = $rest_day . '天' . $rest_hour . '时';
            }
        } elseif ($info['status'] == 10) {
            if ($info['order_pay_type'] == 3) { //账期已完成
                //检查是否已收款
                $is_paid = $PayLogModel->getFieldByOrderId($info['order_id'], 'is_paid');
            } else {
                $is_paid = 1;
            }
            $info['is_paid'] = $is_paid;
        }

        $info['create_time']      = !empty($info['create_time']) ? date('Y-m-d H:i:s', $info['create_time']) : '';
        $info['confirm_time']     = !empty($info['confirm_time']) ? date('Y-m-d H:i:s', $info['confirm_time']) : '';
        $info['pay_time']         = !empty($info['pay_time']) ? date('Y-m-d H:i:s', $info['pay_time']) : '';
        $info['shipping_time']    = !empty($info['shipping_time']) ? date('Y-m-d H:i:s', $info['shipping_time']) : '';
        $info['cancel_time']      = !empty($info['cancel_time']) ? date('Y-m-d H:i:s', $info['cancel_time']) : '';
        $info['finish_time']      = !empty($info['finish_time']) ? date('Y-m-d H:i:s', $info['finish_time']) : '';
        $info['advance_pay_time'] = !empty($info['advance_pay_time']) ? date('Y-m-d H:i:s', $info['advance_pay_time']) : '';
        $info['business_type']    = $extend_info['business_type'];
        $info['is_manager_audit']    = $extend_info['is_manager_audit'];
        $info['order_gift']       = $OrderGiftModel->getOrderGift($order_id); // 订单赠品信息

        // 是否存在银行水单
        $bank_receipt = $OrderAttachmentModel->isExistsBankReceipt($order_id);
        $info['bank_receipt'] = $bank_receipt ? $bank_receipt : 0;

        return $this->apiReturn(0, '获取成功', $info);
    }

    /**
     * 获取简单订单明细（无需登录）
     * @return [type] [description]
     */
    public function sampleInfo()
    {
        //微信小程序传的值
        if (!empty($GLOBALS['HTTP_RAW_POST_DATA']) && empty($_POST)) {
            global $_POST;
            global $_REQUEST;
            try {
                $_POST = json_decode($GLOBALS['HTTP_RAW_POST_DATA'], true);
                $_REQUEST = array_merge($_REQUEST, $_POST);
            } catch (\Exception $e) {
            }
        }
        $res = $this->info(true);
        if ($res['err_code'] == 0) {
            $filter = array(
                'order_id',
                'order_sn',
                'order_goods_type',
                'currency',
                'delivery_place',
                'status',
                'create_time',
                'mkt_point',
                'order_amount_format',
                'topay_amount_format',
                'goods_count',
                'list',
                'province_val',
                'city_val',
                'district_val',
                'address',
                'consignee',
                'mobile',
                'area_code',
                'telphone',
                'order_pay_type_val',
                'remain_day',
                'rest_time',
                'self_rest_time',
            );
            foreach ($res['data'] as $k => $v) {
                if (!in_array($k, $filter)) {
                    unset($res['data'][$k]);
                }
            };
        }
        return $this->apiReturn(0, '获取成功', $res['data']);
    }

    /**
     * 订单信息供合同使用
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    public function contractInfo($id)
    {
        $OrderModel         = D('Order');
        $OrderPriceModel    = D('OrderPrice');
        $OrderExtendModel   = D('OrderExtend');
        $OrderContractModel = D('OrderContract');
        $OrderItemsModel    = D('OrderItems');
        $OrderAddressModel  = D('OrderAddress');
        $OrderInvoiceModel  = D('OrderInvoice');
        // $CmsModel           = D('Common/Cms');
        $TaxinfoModel       = D('Invoice/Taxinfo');

        $info = $OrderModel->getInfo($id);

        if (empty($info)) {
            return $this->apiReturn(-1, '未获取到订单信息');
        }

        $user = S_user($info['user_id']);
        $company = S_company($info['user_id']); // 用户公司信息
        $info['company_fax'] = $company['com_fax'];

        // 发票上公司信息
        $invoice = $OrderInvoiceModel->getInfo($id);

        $info['is_internal'] = 1; // 发票主体是否为国内：1-是，-1-否，用于香港合同条款展示

        if ($info['customer_id'] != 0) {
            $inv_area = $TaxinfoModel->where(['tax_id' => $info['customer_id']])->getField('inv_area'); // 发票主体

            $info['is_internal'] = $inv_area == 2 ? -1 : 1;
        }

        if (!empty($invoice)) {
            $info['inv_type'] = $invoice['inv_type'];
            if (in_array($info['inv_type'], array(1, 2, 4))) { // 除增值税发票从发票获取外其他从用户信息获取
                $province = !empty($company['com_province_id']) ? get_province($company['com_province_id']) : '';
                $city = !empty($company['com_city_id']) ? get_city($company['com_city_id']) : '';
                $area = !empty($company['com_area_id']) ? get_district($company['com_area_id']) : '';
                $info['nick_name'] = $user['nike_name'];
                $info['company_name'] = $company['com_name'];
                $info['company_address_full'] = $province . $city . $area . $company['com_address'];
                $info['company_address'] = $company['com_address']; //单纯验证是否有填详细地址
                $info['company_phone'] = $company['com_telphone'];
                $info['company_name'] = isset($invoice['tax_title']) && $invoice['tax_title'] ? $invoice['tax_title'] : $info['company_name'];
            } else { //增值税专用发票
                $info['nick_name'] = !empty($invoice['nike_name']) ? $invoice['nike_name'] : $user['nike_name'];
                $info['company_name'] = $invoice['tax_title'];
                $info['company_address_full'] = $invoice['company_address'];
                $info['company_address'] = $invoice['company_address'];
                $info['company_phone'] = $invoice['company_phone'];
            }
        }

        if ($info['order_goods_type'] == 1) {
            $extend = $OrderExtendModel->getInfo($id, 'kefu_remark, contract_com_name, contract_com_addr, contract_link_name, contract_link_tel');
            $orderContract = $OrderContractModel->getInfo($id);
            $address = $OrderAddressModel->getInfo($info['order_id'], 1);
            if ($info['currency'] == 1) {
                $info['order_address_full'] = $address['province_val'] . $address['city_val'] . $address['district_val'] . $address['address']; //收货地址
            } else {
                $info['order_address_full'] = $address['address'];
            }
            //显示优先级最高的 contract_com_name, contract_com_addr, contract_link_name, contract_link_tel
            if (!empty($orderContract['contract_com_name'])) {
                $info['company_name'] = $orderContract['contract_com_name'];
            } elseif (!empty($invoice['tax_title'])) {
                $info['company_name'] = $invoice['tax_title'];
            } elseif (!empty($company['com_name'])) {
                $info['company_name'] = $company['com_name'];
            } elseif (!empty($user['mobile'])) {
                $info['company_name'] = $user['mobile'];
            } else {
                $info['company_name'] = $user['email'];
            }
            if (!empty($orderContract['contract_com_addr'])) {
                $info['company_address_full'] = $orderContract['contract_com_addr'];
            } else {
                $info['company_address_full'] = !empty($info['company_address_full']) ? $info['company_address_full'] : $info['order_address_full'];
            }
            if (!empty($orderContract['contract_link_name'])) {
                $info['consignee'] = $orderContract['contract_link_name'];
            } else {
                $info['consignee'] = !empty($address['consignee']) ? $address['consignee'] : '';
            }
            if (!empty($orderContract['contract_link_tel'])) {
                $info['mobile'] = $orderContract['contract_link_tel'];
            } elseif (!empty($user['mobile'])) {
                $info['mobile'] = $user['mobile'];
            } else {
                $info['mobile'] = !empty($address['mobile']) ? $address['mobile'] : '';
            }
        }

        //联营跟单员
        $sale_name = '';
        $sale_phone = '';
        if (!empty($info['sale_id'])) {
            // $sale_name = $CmsModel->table('user_info')->getFieldByUserid($info['sale_id'], 'name');
            $CmsModel = D('Cms');
            $sale_info = $CmsModel->getUserInfo($info['sale_id']);

            $sale_name = $sale_info['name'];
            $sale_phone = $sale_info['mobile'];
        }

        //联营合同
        switch ($info['order_pay_type']) {
            case '1':
                $pay_type_label = '预付全款';
                break;
            case '2':
                $yuFuKuanstr = "";
                if ($info['currency'] == 1) {
                    $yuFuKuanstr = "元";
                } elseif ($info['currency'] == 2) {
                    $yuFuKuanstr = "美元";
                }
                $pay_type_label = '预付' . $info['advance_amount'] . $yuFuKuanstr;
                break;
            case '3':
                $pay_type_label = '月结';
                break;
            case '4':
                $pay_type_label = '货到猎芯付款';
                break;
            default:
                $pay_type_label = '预付全款';
                break;
        }

        // $info['is_invoice'] = $info['inv_type'] != 1 ? '是' : '否';

        $info['goods_amount'] = $OrderPriceModel->getGoodsPrice($id);
        $info['shipping_price'] = price_format($OrderPriceModel->getShippingPrice($id));
        $info['extend_price'] = price_format($OrderPriceModel->getExtPrice($id));
        $info['preferential_price'] = price_format(abs($OrderPriceModel->getPreferentialPrice($id)));
        $info['new_client_price'] = price_format(abs($OrderPriceModel->getNewClientPrice($id)));
        // 商品列表
        $list = $OrderItemsModel->getOrderList($id, '', null);

        $total_goods_number = 0;
        foreach ($list as $it) {
            $total_goods_number += $it['goods_number'];
        }

        $info['total_goods_number'] = $total_goods_number;
        $info['delivery_time'] = $list[0]['delivery_time'];
        $info['list'] = $list;
        $create_time = $info['create_time'];
        $info['create_time'] = date('Y-m-d', $info['create_time']);
        $info['confirm_time'] = intval($info['confirm_time']) > 0 ? date('Y-m-d', $info['confirm_time']) : date('Y-m-d', $create_time);
        $info['order_amount_super'] = cny_number($info['order_amount']);
        $info['goods_amount_format'] = price_format($info['goods_amount'], $info['currency']);
        $info['extend_fee_format'] = price_format($info['extend_price'], $info['currency']);
        $info['shipping_price_format'] = price_format($info['shipping_price'], $info['currency']);
        $info['preferential_format'] = price_format($info['preferential_price'], $info['currency']);
        $info['new_client_price_format'] = price_format($info['new_client_price'], $info['currency']);
        $info['kefu_remark'] = !empty($extend['kefu_remark']) ? $extend['kefu_remark'] : ''; //客服备注
        $info['sale_name'] = $sale_name;
        $info['sale_phone'] = $sale_phone;
        $info['pay_type_label'] = $pay_type_label;

        return $this->apiReturn(0, '获取成功', $info);
    }

    /**
     * 确认收货
     * @return [type] [description]
     */
    public function recive()
    {
        $user_id = cookie('uid');
        $order_id = I('request.order_id', '');
        $OrderModel = D('Order');
        $OrderInvoiceModel = D('OrderInvoice');
        $OrderAddressModel = D('OrderAddress');
        $info = $OrderModel->getUserInfo($user_id, $order_id);
        if (empty($info)) {
            return $this->apiReturn(21004, '未找到相关订单');
        }
        if ($info['status'] <= 2) {
            return $this->apiReturn(21007, '当前状态无法取消订单');
        }
        $res = $OrderModel->setUserStatus($user_id, $order_id, 10);
        if ($res === false) {
            return $this->apiReturn(21010, '确认收货失败');
        }

        try {
            if (C('MKT_POINT_TIME') <= $info['create_time']) {
                $RbmqModel = D('Common/Rbmq');
                $push_data = array(
                    'user_id' => $user_id, //用户id
                    'flow_type' => 1, //积分流向 订单完成固定为1
                    'flow_reason_type' => 3, //积分流向原因 订单固定为3
                    'flow_pf' => platform(), //平台'1PC 2H5 3小程序 4后台人工调整'
                    'flow_extra_id' => $order_id, //order_id
                );
                $RbmqModel->queue(C('QUEUE_MKT_POINT'))->push($push_data, C('QUEUE_MKT_POINT'));
            }

            $order_invoice = $OrderInvoiceModel->getInfo($order_id, 'tax_id,inv_type,order_sn');
            if ($order_invoice['tax_id'] > 0 && $order_invoice['inv_type'] == 3) {
                //创建订单发票地址
                $info = $this->getUserInvoice($order_invoice['tax_id']);
                $info = $info['data'];
                $data['order_id'] = $order_id;
                $data['order_sn'] = $order_invoice['order_sn'];
                $data['address_id'] = $order_invoice['tax_id'];
                $data['address_type'] = 2;
                $data['province'] = $info['consignee_province'];
                $data['city'] = $info['consignee_city'];
                $data['district'] = $info['consignee_district'];
                $data['address'] = $info['consignee_address'];
                $data['consignee'] = $info['consignee'];
                $data['mobile'] = $info['consignee_phone'];
                $OrderAddressModel->createOrderAddress($data);
            }

            // 操作记录
            D('OrderActionLog')->addLog($order_id, $user_id, 1, '确认收货');
        } catch (Exception $e) {
        }

        return $this->apiReturn(0, '确认收货成功');
    }

    // 获取最新的两笔待收货物流信息
    public function lastShipping()
    {
        $user_id = cookie('uid');

        if (!$user_id) return $this->apiReturn(21016, '用户未登录');

        $OrderModel = D('Order');

        $map['order_goods_type'] = 1;
        $map['user_id']          = $user_id;
        $map['status']           = ['in', [7, 8]];
        $map['is_type']          = 0;

        $order = $OrderModel->where($map)->limit(2)->getField('order_id, order_sn');

        $shipping_info = [];

        if ($order) {
            foreach ($order as $k => $v) {
                $data = [];
                $data['id']  = $k;
                $data['uid'] = $user_id;
                $data['k1']  = time();
                $data['k2']  = pwdhash($data['k1'], C('SUPER_AUTH_KEY'));

                $res = json_decode(post_curl(API_DOMAIN . '/order/shipping', $data), true);

                if ($res['err_code'] != 0 || is_string($res['data'][0]['info'])) continue;

                $temp = [];
                $temp['order_id']      = $k;
                $temp['order_sn']      = $v;
                $temp['shipping_name'] = $res['data'][0]['shipping_name'];
                $temp['shipping']      = $res['data'][0]['info'][0];

                $shipping_info[] = $temp;
            }
        }

        return $this->apiReturn(0, '获取成功', $shipping_info);
    }

    /**
     * 获取快递信息
     * @return [type] [description]
     */
    public function shipping()
    {
        $order_id      = I('request.id', 0, 'intval');
        $shipping_type = I('request.type', 1, 'intval');
        $shipping_no   = I('shipping_no', '', 'trim');
        $user_id       = $this->auth() ? I('request.uid', 0, 'intval') : cookie('uid');

        $OrderModel               = D('Order');
        $OrderExtendModel         = D('OrderExtend');
        $OrderAddressModel        = D('OrderAddress');
        $OrderShippingModel       = D('OrderShipping');
        $OrderShippingItemsModel  = D('OrderShippingItems');
        $OrderShippingInsideModel = D('OrderShippingInside');

        $info = $OrderModel->getInfo($order_id);
        if ($info['user_id'] != $user_id) {
            return $this->apiReturn(21015, '只能获取自己订单的快递信息');
        }

        if ($info['status'] < 4) return $this->apiReturn(21016, '当前状态无法获取物流信息');

        //内部轨迹   单据类型 货品  自营
        if ($shipping_type == 1 && $info['order_goods_type'] == 2) {
            $inside = $OrderShippingInsideModel->getInsideInfo($order_id);
        }

        // 获取猎芯快递ID
        $self_id = $this->getConf('shipping', 'SELF_SHIPPING_ID');
        $list = $OrderShippingModel->getList($order_id, $shipping_type, $shipping_no);

        // 获取关联的已发货自营ID
        if ($info['order_goods_type'] == 1) {
            $extend_info = $OrderExtendModel->getInfo($order_id);

            if ($extend_info['erp_sn']) {
                $map = [];
                $map['o.order_goods_type'] = 2;
                $map['o.status']           = ['gt', 4];
                $map['oe.erp_sn']          = $extend_info['erp_sn'];

                $self_order_id = $OrderModel->alias('o')->join('LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id')->where($map)->getField('o.order_id');

                if ($self_order_id) {
                    $self_list = $OrderShippingModel->getList($self_order_id);
                    $list = array_merge($list, $self_list);
                }
            }
        }

        if ($list) {
            foreach ($list as &$shipping) {
                $items = $OrderShippingItemsModel->getList($shipping['order_shipping_id']);
                $item_arr = array();
                foreach ($items as $v) {
                    $item_arr[] = $v['goods_name'] . '(' . $v['num'] . ')';
                }
                $shipping['goods_items'] = implode(',', $item_arr);
                // 已经签收 或 快递为猎芯快递
                if ($shipping['status'] == 2 || $shipping['shipping_id'] == $self_id) {
                    $shipping_text = json_decode($shipping['info'], true);

                    // 未签收
                } elseif (in_array($shipping['status'], array(0, 1))) {
                    if ($shipping['expire_time'] < $_SERVER['REQUEST_TIME']) {
                        //通过地址获取收件人手机号
                        $address = $OrderAddressModel->getInfo($order_id, 1);
                        // 获取最新物流
                        $get_info = $this->getShipping($shipping['shipping_no'], $shipping['shipping_id'], $address['mobile']);
                        if ($get_info['err_code'] == 0 && $get_info['data'] !== false) {
                            $shipping['info'] = $get_info['data']['info'];
                            $save['status'] = $get_info['data']['status'];
                        }
                        $save['info'] = $shipping['info'];
                        $save['order_shipping_id'] = $shipping['order_shipping_id'];
                        $save['update_time'] = time();
                        $save['expire_time'] = $save['update_time'] + 300;
                        try {
                            $OrderShippingModel->save($save);
                        } catch (Exception $e) {
                        }
                    }

                    $shipping_text = json_decode($shipping['info'], true);

                    // 未发货
                } else {
                    $shipping_text = '';
                }
                if (!empty($shipping_text) && is_array($shipping_text)) {
                    rsort($shipping_text);
                    if (!empty($inside)) {
                        $shipping['info'] = array_merge($shipping_text, $inside);
                    } else {
                        $shipping['info'] = $shipping_text;
                    }
                } elseif ((is_int($shipping_text) || is_string($shipping_text) || is_null($shipping_text)) && !empty($shipping['info'])) {
                    $shipping_text = $shipping['info'];
                } else {
                    if (!empty($inside)) {
                        $shipping_text = $inside;
                    } else {
                        $shipping_text = '未获取到快递信息';
                    }
                    $shipping['info'] = $shipping_text;
                }
            }
        }

        if (empty($list) && !empty($inside)) {
            $list[] = array(
                'order_shipping_id' => 0,
                'shipping_id' => 0,
                'shipping_no' => '',
                'info' => $inside,
                'shipping_name' => $info['order_shipping_type'] == 1 ? '普通快递' : '上门自提',
                'status' => -1
            );
        }
        if (!empty($shipping_no)) {
            $list = $list[0];
        }
        return $this->apiReturn(0, '获取成功', $list);
    }

    /**
     * 获取用户订单数量
     * @return [type] [description]
     */
    public function count()
    {
        $user_id = cookie('uid');
        $mulit = I('request.mul', 0, 'intval');
        $OrderModel = D('Order');
        if ($mulit == 1) {
            $status = explode(',', I('status', ''));
            $order_status = C('ORDER_STATUS');
            foreach ($status as $v) {
                if (!isset($order_status[$v])) {
                    continue;
                }
                $map['O.status'] = $v;
                $data[$v] = intval($OrderModel->getUserCount($user_id, $map));
            }
        } else {
            $map = map_filter(array('O.status'));
            $data = $OrderModel->getUserCount($user_id, $map);
        }
        return $this->apiReturn(0, '获取成功', $data);
    }

    /**
     * 获取订单发票列表
     * @return [type] [description]
     */
    public function invoice()
    {
        $p = I('p', 1, 'intval');
        $filter = array('I.order_sn', 'O.stime', 'O.etime');
        $user_id = cookie('uid');
        $OrderInvoiceModel = D('OrderInvoice');
        $OrderAddressModel = D('OrderAddress');
        $OrderShippingModel = D('OrderShipping');
        $map = map_filter($filter);
        $map['I.invoice_status'] = array('egt', 2);
        $count = $OrderInvoiceModel->getUserCount($user_id, $map);
        $data = $OrderInvoiceModel->getUserList($user_id, $map, $p);
        foreach ($data as &$v) {
            $v['consignee'] = '';
            $v['shipping_no'] = '';
            if ($v['invoice_status'] == 2) { // 已发货
                $address = $OrderAddressModel->getInfo($v['order_id'], 2); // 发票地址
                $shipping = $OrderShippingModel->getInfo($v['order_id'], 2); // 快递
                $v['consignee'] = $address['consignee'];
                $v['shipping_no'] = $shipping['shipping_no'];
                $v['shipping_name'] = $shipping['shipping_name'];
            }
            $v['inv_status_val'] = C('ORDER_INVOICE_STATUS.' . $v['invoice_status']);
            $v['inv_type_val'] = C('ORDER_INVOICE_TYPE.' . $v['inv_type']);
        }
        $datas = page_data($data, $count, $p);
        if (empty($datas)) {
            return $this->apiReturn(21009, '发票获取失败');
        }
        return $this->apiReturn(0, '获取成功', $datas);
    }

    /**
     * 获取订单发票
     * @return [type] [description]
     */
    public function invoiceInfo()
    {
        if (!$this->auth()) {
            return $this->apiReturn(21005, '非法操作');
        }
        $order_id = I('id', '');
        $OrderInvoiceModel = D('OrderInvoice');
        $datas = $OrderInvoiceModel->getInfo($order_id);
        return $this->apiReturn(0, '获取成功', $datas);
    }

    /**
     * 修改发票信息
     * @return [type] [description]
     */
    public function updateOrderInvoice()
    {
        $order_id            = I('order_id', ''); // 订单ID
        $tax_id              = I('tax_id', ''); // 发票ID
        $inv_type            = I('inv_type', ''); // 发票类型
        $operator_id         = I('operator_id'); // 操作人ID
        $OrderModel          = D('Order');
        $OrderInvoiceModel   = D('OrderInvoice');
        $OrderExtendModel    = D('OrderExtend');
        $RemovalModel        = D('Removal');
        $OrderActionLogModel = D('OrderActionLog');

        $order_info = $OrderModel->getInfo($order_id);

        // 获取修改前订单信息
        $invoice_before = $OrderInvoiceModel->getInfo($order_id);

        switch ($invoice_before['inv_type']) {
            case 1:
                $inv_type_val = '不开票';
                $event = '修改发票，修改前信息：发票类型：' . $inv_type_val;
                break;
            case 2:
                $inv_type_val = '普票';
                $event = '修改发票，修改前信息：发票类型：' . $inv_type_val . '，发票抬头：' . $invoice_before['tax_title'];
                break;
            case 3:
                $inv_type_val = '增值税专用发票';
                $event = '修改发票，修改前信息：发票类型：' . $inv_type_val . '，发票抬头：' . $invoice_before['tax_title'] . '，税务登记号：' . $invoice_before['tax_no'] . '，开户银行：' . $invoice_before['bank_name'] . '，银行卡号：' . $invoice_before['bank_account'] . '，公司电话：' . $invoice_before['company_phone'] . '，公司地址：' . $invoice_before['company_address'];
                break;
            case 4:
                $inv_type_val = '增值税普通发票';
                $event = '修改发票，修改前信息：发票类型：' . $inv_type_val . '，发票抬头：' . $invoice_before['tax_title'] . '，税务登记号：' . $invoice_before['tax_no'] . '，开户银行：' . $invoice_before['bank_name'] . '，银行卡号：' . $invoice_before['bank_account'] . '，公司电话：' . $invoice_before['company_phone'] . '，公司地址：' . $invoice_before['company_address'];
                break;
        }

        $OrderModel->startTrans();

        $TaxinfoModel = D('Invoice/Taxinfo');

        if ($tax_id) {
            $taxinfo = $TaxinfoModel->where(['tax_id' => $tax_id])->find();

            $save_data = [
                'tax_id'          => $taxinfo['tax_id'],
                'inv_type'        => $taxinfo['inv_type'],
                'tax_title'       => $taxinfo['tax_title'],
                'company_address' => $taxinfo['company_address'],
                'company_phone'   => $taxinfo['company_phone'],
                'tax_no'          => $taxinfo['tax_no'],
                'bank_name'       => $taxinfo['bank_name'],
                'bank_account'    => $taxinfo['bank_account'],
            ];
        } else {
            $save_data = [
                'tax_id'          => $tax_id,
                'inv_type'        => $inv_type,
                'tax_title'       => '',
                'company_address' => '',
                'company_phone'   => '',
                'tax_no'          => '',
                'bank_name'       => '',
                'bank_account'    => '',
            ];
        }

        $updateInvoice = $OrderInvoiceModel->where(['order_id' => $order_id])->save($save_data);

        if ($updateInvoice === false) {
            $OrderModel->rollback();
            return $this->apiReturn(21017, '更新发票失败');
        }

        // 若为增值税专票，则同步到ERP
        // if ($save_data['inv_type'] == 3) {
        //     $data['CUSTOMER']      = $save_data['tax_title']; //客户名称
        //     $data['DESCRIPTION']   = ''; //客户备注
        //     $data['FADDRESS']      = $save_data['company_address']; //地址
        //     $data['FTXREGISTERNO'] = $save_data['tax_no']; //税务登记号
        //     $data['FPHONE']        = $save_data['company_phone']; //电话
        //     $data['FBANK']         = $save_data['bank_name']; //开户行
        //     $data['FBANKACCOUNT']  = $save_data['bank_account']; //帐号

        //     if (strpos($_SERVER['HTTP_REFERER'], 'sz') === false) { // 非测试环境下执行
        //         $res = A('Server/Consume')->pushUser($data);

        //         if ($res === false) {
        //             $OrderModel->rollback();
        //             return $this->apiReturn(21018, '专票同步ERP失败');
        //         }
        //     }

        //     $event .= '，同步专票到ERP';
        // }

        // 已付款订单修改同步信息
        if ($order_info['status'] > 3) {
            // 不开票或普票或增普
            if (in_array($save_data['inv_type'], [1, 2, 4])) {
                $updateExtend = $OrderExtendModel->where(['order_id' => $order_id])->save(['fms_syn' => 1]);

                if ($updateExtend === false) {
                    $OrderModel->rollback();
                    return $this->apiReturn(21018, '更新发票同步字段失败');
                }

                $event .= '，更新extend发票同步字段';
            }

            // 增专 + 已发货
            if (in_array($save_data['inv_type'], [3]) && $order_info['status'] > 4) {
                $updateRemoval = $RemovalModel->where(['order_id' => $order_id])->save(['fms_syn' => 1]);

                if ($updateRemoval === false) {
                    $OrderModel->rollback();
                    return $this->apiReturn(21019, '更新发票同步字段失败');
                }

                $event .= '，更新removal发票同步字段';
            }
        }

        // 操作记录
        $log = $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event);

        if ($log === false) {
            $OrderModel->rollback();
            return $this->apiReturn(21020, '更新操作记录失败');
        }

        $OrderModel->commit();

        return $this->apiReturn(0, '更新发票信息成功');
    }

    /**
     * |||转换订单状态
     * @return [type] [description]
     */
    public function transStatus()
    {
        $order_status = I('order_status', null);
        $pay_status = I('pay_status', null);
        $data['id'] = status_to_id($order_status, $pay_status);
        $data['name'] = status_to_name($order_status, $pay_status);
        return $this->apiReturn(0, '转换成功', $data);
    }

    /**
     * |||获取所有状态
     * @return [type] [description]
     */
    public function status()
    {
        $order_status = C('ORDER_STATUS_MAPPING');
        $status = array();
        foreach ($order_status as $k => $v) {
            $status[$k] = $v['0'];
        }
        return $this->apiReturn(0, '', $status);
    }

    /**
     * 获取所有订单
     * @return [Json]
     */
    public function getAllOrder()
    {
        $p = I('p', '');
        $size = I('size', 10);
        $map = I('map', '');

        //  获取订单
        $data = D('Order')->getAllOrderDetails($p, $map, $size);

        return $this->apiReturn(0, '', $data);
    }

    // 获取订单其他信息
    public function getOrderOtherInfo()
    {
        $map = I('map', '');
        $data = D('Order')->getAllOrderOtherInfo($map);

        return $this->apiReturn(0, '', $data);
    }

    // 订单统计---获取订单金额
    public function getOrderAmount()
    {
        $map = I('map', '');
        $data = D('Order')->getOrderAmountInfo($map);

        return $this->apiReturn(0, '', $data);
    }

    /*
     * py脚本同步订单到erp
     */
    public function pyPushOrderAddressToErp()
    {
        //        $order_id      = I('order_id', 0);
        $order_sn      = I('order_sn', "");
        $orderInfo = D("Order")->where(["order_sn" => $order_sn])->field("order_shipping_type,order_sn,order_id")->find();
        if ($orderInfo["order_shipping_type"] != 1) {
            return $this->apiReturn(0, '该订单为自提订单无需快递地址');
        }

        $order_id = $orderInfo["order_id"];

        $orderAddress = D("OrderAddress")->where(["order_id" => $order_id])->where(["address_type" => 1])->find();
        if (empty($orderAddress)) {
            return $this->apiReturn(-1, sprintf("没有找到订单为:%s的订单快递配送地址", $order_id));
        }
        $erp_params = [];
        $erp_params['NUMBER']               = $orderInfo['order_sn'];
        $erp_params['address']['PROVINCE']  = get_province($orderAddress['province']);
        $erp_params['address']['CITY']      = get_city($orderAddress['city']);
        $erp_params['address']['DISTRICT']  = get_district($orderAddress['district']);
        $erp_params['address']['ADDRESS']   = $orderAddress['address'];
        $erp_params['address']['CONSIGNEE'] = $orderAddress["consignee"];
        $mobile   = $orderAddress["mobile"] ? sprintf("%s%s", $orderAddress["area_code"], $orderAddress["mobile"]) : "";
        $telphone = $orderAddress["telphone"] ? $orderAddress["telphone"] : "";
        $contact = $mobile ? $mobile : $telphone;
        $erp_params['address']['MOBILE'] = $contact;
        try {
            $res = A('Server/Consume')->pushOrderAddress($erp_params);
            if ($res !== true) return $this->apiReturn(1, '推送地址到ERP失败，原因：' . $res);
            return $this->apiReturn(0, "推送成功");
        } catch (\Exception $e) {
            return $this->apiReturn(1, sprintf("推送失败:%s", $e->getMessage()));
        }
    }

    /**
     * 修改订单地址
     * @return [type] [description]
     */
    public function updateOrderAddress()
    {
        $order_id      = I('order_id', 0);
        $shipping_type = I('shipping_type', 1);
        $consignee     = I('consignee', '');
        $mobile        = I('mobile', '');
        $nation_id     = I('nation_id', 36); // 默认中国
        $province      = I('province', 0);
        $city          = I('city', 0);
        $district      = I('district', '');
        $address       = I('address', '');
        $operator_id   = I('operator_id', 0);

        $order_address = [];
        $order_address['consignee'] = $consignee;
        $order_address['mobile']    = $mobile;
        $order_address['nation_id'] = $nation_id;
        $order_address['province']  = $shipping_type == 2 ? 0 : $province;
        $order_address['city']      = $shipping_type == 2 ? 0 : $city;
        $order_address['district']  = $shipping_type == 2 ? 0 : $district;
        $order_address['address']   = $address;

        $OrderModel          = D('Order');
        $OrderExtendModel    = D('OrderExtend');
        $OrderAddressModel   = D('OrderAddress');
        $OrderActionLogModel = D('OrderActionLog');

        $order_info   = $OrderModel->getInfo($order_id);
        $order_extend = $OrderExtendModel->getInfo($order_id);

        if ($order_info['status'] < 1 || $order_info['status'] > 4) return $this->apiReturn(44017, '当前状态无法修改收货地址');

        $event = '修改订单收货信息';

        // 若是联营订单且为快递配送，则推送地址到ERP - 注释原因：ERP通过发货通知单的地址出库，这里推送的意义不大20240423
        // if ($order_info['order_goods_type'] == 1 && $shipping_type == 1 && $order_extend['erp_sn']) {
        //     $erp_params = [];
        //     $erp_params['NUMBER'] = $order_info['order_sn'];

        //     $nation = get_nation($nation_id);

        //     $erp_params['address']['NATION']    = $nation['name_cn'];
        //     $erp_params['address']['PROVINCE']  = get_province($order_address['province']);
        //     $erp_params['address']['CITY']      = get_city($order_address['city']);
        //     $erp_params['address']['DISTRICT']  = get_district($order_address['district']);
        //     $erp_params['address']['ADDRESS']   = $order_address['address'];
        //     $erp_params['address']['CONSIGNEE'] = $consignee;
        //     $erp_params['address']['MOBILE']    = $mobile;

        //     $res = A('Server/Consume')->pushOrderAddress($erp_params);

        //     if ($res !== true) return $this->apiReturn(44017, '推送地址到ERP失败，原因：'.$res);

        //     $event .= '，推送地址到ERP成功';
        // }

        $old_address_info = $OrderAddressModel->getInfo($order_id);
        $old_shipping_type = $order_info['order_shipping_type'] == 1 ? '快递配送' : '自提';
        $old_nation = get_nation($old_address_info['nation_id']);
        $old_address = $old_nation['name_cn'];

        if ($old_address_info['nation_id'] == 36) {
            $old_address .= $old_address_info['province_val'] . $old_address_info['city_val'] . $old_address_info['district_val'];
        }

        $old_address .= $old_address_info['address'];

        $event .= '，修改前---配送方式：' . $old_shipping_type . '，联系人：' . $old_address_info['consignee'] . '，联系电话：' . $old_address_info['mobile'] . '，地址：' . $old_address;

        try {
            $OrderModel->startTrans();

            $OrderModel->where(['order_id' => $order_id])->save(['order_shipping_type' => $shipping_type]);
            $OrderAddressModel->where(['order_id' => $order_id, 'address_type' => 1])->save($order_address);

            $OrderModel->commit();

            $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event);
        } catch (\Exception $e) {
            $OrderModel->rollback();
            return $this->apiReturn(21018, '更新失败，原因：' . $e->getMessage());
        }

        return $this->apiReturn(0, '更新成功');
    }

    // 订单对账 3.0
    public function checkPay()
    {
        $order_id = I('order_id', '');
        $cid = I('cid', '');
        $serial_number = I('serial_number', '');
        $operator_id = I('operator_id', '');

        $OrderModel = D('Order');
        $OrderPriceModel = D('OrderPrice');
        $OrderInvoiceModel = D('OrderInvoice');
        $OrderExtendModel = D('OrderExtend');
        $PayLog = D('PayLog');
        $OrderActionLogModel = D('OrderActionLog');
        $OrderModel->startTrans();

        /** 获取订单所有信息 **/
        $info = $OrderModel->getInfo($order_id);

        $data['user_id'] = $info['user_id'];
        $data['order_id'] = $price['order_id'] = $order_id;
        $data['order_sn'] = $price['order_sn'] = $info['order_sn'];
        $data['serial_number'] = $serial_number;
        $price['currency'] = $info['currency'];
        $price['create_time'] = time();

        // 根据货币类型判断银行
        if ($info['currency'] == 1) { // RMB
            $data['pay_id'] = 0; // 支付方式ID
            $data['pay_name'] = '交通银行'; // 支付方式名
            $currency = '￥';
        } else {
            $data['pay_id'] = 0;
            $data['pay_name'] = '恒生银行';
            $currency = '$';
        }

        $data['is_paid'] = 1;

        $order['erp_syn'] = 1; // 标记erp同步

        // 更新订单状态
        if ($cid == 1) { // 全款
            if ($info['order_shipping_type'] == 1) {
                $order['status'] = 4;
            } else {
                $order['status'] = 8; // 自提时，对账后状态为待收货
            }

            $order['pay_time'] = time();

            $data['pay_type'] = 1;
            $data['pay_amount'] = $info['order_amount'];

            // 若支付日志存在，则更新，否则创建
            if ($info['order_pay_type'] == 3) { // 账期
                $data['pay_type'] = 4;
            } else if ($info['order_pay_type'] == 1) { // 全款
                $data['pay_type'] = 1;
            }

            $payLogInfo = $PayLog->where(['order_id' => $order_id, 'pay_type' => $data['pay_type']])->find();

            if ($payLogInfo) {
                $updatePayLog = $PayLog->where(['order_id' => $order_id, 'pay_type' => $data['pay_type']])->save($data);
            } else {
                $data['create_time'] = time();
                $data['pay_time'] = time();

                $updatePayLog = $PayLog->add($data);
            }

            // 插入到order_price
            $price['price_type'] = -1;
            $price['price'] = '-' . $info['order_amount'];

            $addOrderPrice = $OrderPriceModel->add($price);
            $keyword = 'order-full-paid';

            $event = '对账成功，订单金额：' . $currency . $data['pay_amount'] . '，流水号：' . $serial_number;
        } else if ($cid == 2) { // 首款
            $order['status'] = 3;
            $order['advance_pay_time'] = time();

            $data['pay_type'] = 2;
            $data['pay_amount'] = $info['advance_amount'];

            // 若支付日志存在，则更新，否则创建
            $payLogInfo = $PayLog->where(['order_id' => $order_id, 'pay_type' => 2])->find();

            if ($payLogInfo) {
                $updatePayLog = $PayLog->where(['order_id' => $order_id, 'pay_type' => 2])->save($data);
            } else {
                $data['create_time'] = time();
                $data['pay_time'] = time();

                $updatePayLog = $PayLog->add($data);
            }

            // 插入到order_price
            $price['price_type'] = -2;
            $price['price'] = '-' . $info['advance_amount'];

            $addOrderPrice = $OrderPriceModel->add($price);
            $keyword = 'order-advance-prepaid'; // 首款对账成功发送短信

            $event = '首款对账，订单金额：' . $currency . $data['pay_amount'] . '，流水号：' . $serial_number;;
        } else { // 尾款
            if ($info['order_shipping_type'] == 1) {
                $order['status'] = 4;
            } else {
                $order['status'] = 8; // 自提时，对账后状态为待收货
            }

            $order['pay_time'] = time();

            $data['pay_type'] = 3;
            $data['pay_amount'] = $info['order_amount'] - $info['advance_amount'];

            // 若支付日志存在，则更新，否则创建
            $payLogInfo = $PayLog->where(['order_id' => $order_id, 'pay_type' => 3])->find();

            if ($payLogInfo) {
                $updatePayLog = $PayLog->where(['order_id' => $order_id, 'pay_type' => 3])->save($data);
            } else {
                $data['create_time'] = time();
                $data['pay_time'] = time();

                $updatePayLog = $PayLog->add($data);

                $payLog['is_paid'] = 1; // 更新首款支付状态
                $updateFirstPay = $PayLog->where(['order_id' => $order_id, 'pay_type' => 2])->save($payLog);

                if ($updateFirstPay === false) {
                    $OrderModel->rollback();

                    return $this->apiReturn(-1, '尾款对账，更新首款支付状态失败');
                }
            }

            // 插入到order_price
            $price['price_type'] = -3;
            $price['price'] = '-' . ($info['order_amount'] - $info['advance_amount']);

            $addOrderPrice = $OrderPriceModel->add($price);
            $keyword = 'order-advance-endpaid'; // 尾款

            $event = '尾款对账，订单金额：' . $currency . $data['pay_amount'] . '，流水号：' . $serial_number;;
        }

        $updateOrder = $OrderModel->where(['order_id' => $order_id])->save($order);

        $actionLog = $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event); // 操作记录

        if ($updatePayLog && $addOrderPrice && $updateOrder && $actionLog) {
            $OrderModel->commit();

            //全款/首款对账成功 送抽奖资格
            if (in_array($cid, [1, 2])) {
                $this->increaseLotteryQualify($info['user_id'], $order_id);
            }

            $this->checkPaySendMsg($info, $keyword); // 发送信息

            return $this->apiReturn(0, '操作成功');
        } else {
            $OrderModel->rollback();

            return $this->apiReturn(-1, '更新记录失败');
        }
    }

    // 自营对账
    public function selfCheckPay()
    {
        $order_id         = I('order_id', '');
        // $serial_number = I('serial_number', '');
        $operator_id      = I('operator_id', '');
        $trans_amount     = I('trans_amount', 0);

        /** 获取订单所有信息 **/
        $OrderModel        = D('Order');
        $OrderPrice        = D('OrderPrice');
        $OrderInvoiceModel = D('OrderInvoice');
        $OrderExtendModel  = D('OrderExtend');
        $PayLog            = D('PayLog');

        $info = $OrderModel->getInfo($order_id);
        $order_extend = $OrderExtendModel->getInfo($order_id);

        if ($info['status'] == -1) return $this->apiReturn(44017, '订单已取消');

        $OrderModel->startTrans();

        try {
            $data['user_id']  = $info['user_id'];
            $data['order_id'] = $order_id;
            $data['order_sn'] = $info['order_sn'];
            // $data['serial_number'] = $serial_number;

            // 根据货币类型判断银行
            if ($info['currency'] == 1) { // RMB
                $data['pay_id']   = 0; // 支付方式ID
                $data['pay_name'] = '交通银行'; // 支付方式名
                $currency         = '￥';
            } else {
                $data['pay_id']   = 0;
                $data['pay_name'] = '恒生银行';
                $currency         = '$';
            }

            $data['is_paid'] = 1;
            $data['pay_amount'] = $trans_amount ? $trans_amount : $info['order_amount'];

            // 若支付日志存在，则更新，否则创建
            if ($info['order_pay_type'] == 3) { // 账期
                $data['pay_type'] = 4;
            } else if ($info['order_pay_type'] == 1) { // 全款
                $data['pay_type'] = 1;
            }

            $payLogInfo = $PayLog->where(['order_id' => $order_id, 'pay_type' => $data['pay_type']])->find();

            if ($payLogInfo) {
                $updatePayLog = $PayLog->where(['order_id' => $order_id, 'pay_type' => $data['pay_type']])->save($data);
            } else {
                $data['create_time'] = time();
                $data['pay_time']    = time();

                $updatePayLog  = $PayLog->add($data);

                if ($data['pay_amount'] > 0) {
                    $RbmqModel = D('Common/Rbmq');
                    $RbmqModel->connect('RBMQ_MSG_CONFIG')->queue(C('MEMBER_TASK_SYSTEM_LIST'))->push(array('user_id' => $info['user_id'], 'pay_amount' => $data['pay_amount'], 'task_type' => 3), C('MEMBER_TASK_SYSTEM_LIST'));
                }
            }

            $price['order_id']    = $order_id;
            $price['order_sn']    = $info['order_sn'];
            $price['currency']    = $info['currency'];
            $price['create_time'] = time();

            // 插入到order_price
            $price['price_type'] = -1;
            $price['price']      = '-' . $info['order_amount'];

            $updateOrderPrice = $OrderPrice->add($price);

            $order['status'] = 4; // 待发货

            // if ($info['sale_type'] == 1) { // 现卖标记wms
            //     $order['wms_syn'] = 1;
            // }

            $order['pay_time'] = time();

            $updateOrder = $OrderModel->where(['order_id' => $order_id])->save($order);

            $updateOrderExtend = 1;

            // 发票同步
            // 自动开票开关 并且 金额大于0
            if (C('AUTO_OPEN_INVOICE') && $info['order_amount'] > 0) {
                $inv_type = $OrderInvoiceModel->getFieldByOrderId($order_id, 'inv_type');
                if (in_array($inv_type, array(2, 4))) { //普通发票
                    $extend['fms_syn'] = 1;
                }
            }

            if ($info['sale_type'] == 2) {
                $extend['pur_syn'] = 1; // 预售标记
            }

            if (!empty($extend)) {
                $updateOrderExtend = $OrderExtendModel->where(['order_id' => $order_id])->save($extend);
            }

            if ($updatePayLog && $updateOrderPrice && $updateOrder && $updateOrderExtend) {
                $OrderModel->commit();

                // 现卖订单推入队列
                if ($info['sale_type'] == 1) {
                    $resWms = $this->makeOrder($order_id);

                    if ($resWms['err_code'] != 0) {
                        $send_data['data'] = array(
                            'msg' => '订单：' . $info['order_sn'] . '准备付款，wms预分配失败'
                        );
                        $send_data = json_encode($send_data);
                        $this->sendOrderMsg('wms_error_warning', $send_data, '', true);
                        return $this->apiReturn($resWms['err_code'], $resWms['err_msg']);
                    }
                }

                //对账成功 送抽奖机会
                $this->increaseLotteryQualify($info['user_id'], $order_id);

                // 操作记录
                // $event = '对账成功，订单金额：'.$currency.$data['pay_amount'].'，流水号：'.$serial_number;
                if ($trans_amount) {
                    $event = '财务对账成功，对账金额：' . $currency . $data['pay_amount'];
                } else {
                    $event = '对账成功，订单金额：' . $currency . $data['pay_amount'];
                }

                $actionLog = D('OrderActionLog')->addLog($order_id, $operator_id, 2, $event);

                // 发送信息
                $keyword = 'order-full-paid';
                $this->checkPaySendMsg($info, $keyword);

                return $this->apiReturn(0, '操作成功');
            } else {
                $OrderModel->rollback();

                return $this->apiReturn(-1, '更新记录失败');
            }
        } catch (Exception $e) {
            $OrderModel->rollback();
        }
    }

    // ERP推送对账信息---新增支付记录
    public function setPaidLog()
    {
        $salenumber  = I('SALENUMBER', ''); // 销售订单号
        $reclasttime = I('RECLASTTIME', ''); // 收款时间
        $reclog      = I('RECLOG', ''); // 收款信息

        if (!$salenumber) return $this->apiReturn(25101, '缺少销售订单号');

        $OrderModel          = D('Order');
        $OrderPriceModel     = D('OrderPrice');
        $OrderInvoiceModel   = D('OrderInvoice');
        $OrderExtendModel    = D('OrderExtend');
        $PayLogModel         = D('PayLog');
        $OrderActionLogModel = D('OrderActionLog');

        $this->apiRecord('ERP推送对账信息，销售订单号：' . $salenumber . '，收款时间：' . date('Y-m-d H:i:s', $reclasttime)); // 记录接口访问信息

        try {
            /** 获取订单所有信息 **/
            $info = $OrderModel->getInfo('', $salenumber);
            $order_id = $info['order_id'];
            $event = ''; // 记录信息

            if ($info['status'] == -1) {
                $event = 'ERP对账：平台订单已取消，不修改数据';

                $OrderActionLogModel->addLog($order_id, 0, 4, $event);
                return $this->apiReturn(0, $event);
            }

            if ($info['order_pay_type'] == 2) { // 预付款情况下
                $map = [];
                $map['order_id'] = $order_id;
                $map['pay_type'] = ['in', [2, 3]];
                $pay_log_id = $PayLogModel->where($map)->getField('pay_log_id');

                if ($pay_log_id) {
                    $event = 'ERP对账：平台预付款订单已支付，不修改数据';

                    $OrderActionLogModel->addLog($order_id, 0, 4, $event);
                    return $this->apiReturn(0, $event);
                }
            }

            $OrderModel->startTrans();

            $data['user_id']  = $info['user_id'];
            $data['order_id'] = $order_id;
            $data['order_sn'] = $info['order_sn'];

            // 根据货币类型判断银行
            if ($info['currency'] == 1) { // RMB
                $data['pay_id']   = 0; // 支付方式ID
                $data['pay_name'] = '交通银行'; // 支付方式名
                $currency         = '￥';
            } else {
                $data['pay_id']   = 0;
                $data['pay_name'] = '恒生银行';
                $currency         = '$';
            }

            $data['is_paid']    = 1;
            $data['pay_amount'] = $info['order_amount'];
            $data['pay_type']   = $info['order_pay_type'] == 3 ? 4 : 1; // 账期/全款类型 （ERP对账无预付款情况，收齐金额后调用此支付接口）

            // 若支付日志存在，则更新，否则创建
            $payLogInfo = $PayLogModel->where(['order_id' => $order_id, 'pay_type' => $data['pay_type']])->find();

            if ($payLogInfo) {
                $save['is_paid']  = 1;
                $save['pay_time'] = $reclasttime;

                $updatePayLog = $PayLogModel->where(['order_id' => $order_id, 'pay_type' => $data['pay_type']])->save($save);

                $event = $data['pay_type'] == 4 ? 'ERP账期还款成功' : 'ERP对账成功';
            } else {
                $data['create_time'] = time();
                $data['pay_time']    = $reclasttime;

                $updatePayLog  = $PayLogModel->add($data);

                $event = 'ERP对账成功';
            }

            if ($info['status'] > 4) {
                $OrderActionLogModel->addLog($order_id, 0, 4, $event . '，平台订单已发货，仅修改收款时间');
                $OrderModel->commit();
                return $this->apiReturn(0, '平台订单为已发货状态，仅修改收款时间，不修改订单数据');
            }

            // 插入到order_price
            $price['order_id']    = $order_id;
            $price['order_sn']    = $info['order_sn'];
            $price['currency']    = $info['currency'];
            $price['price_type']  = -1;
            $price['price']       = '-' . $info['order_amount'];

            $orderPrice = $OrderPriceModel->where(['order_id' => $order_id, 'price_type' => -1])->find();

            if ($orderPrice) {
                $updateOrderPrice = $OrderPriceModel->where(['order_id' => $order_id, 'price_type' => -1])->save($price);
            } else {
                $price['create_time'] = time();

                $updateOrderPrice = $OrderPriceModel->add($price);
            }

            // 更新订单表
            $order['status']   = 4; // 待发货
            $order['pay_time'] = $reclasttime;

            // if ($info['order_goods_type'] == 2 && $info['sale_type'] == 1) { // 自营现卖标记wms
            //     $order['wms_syn'] = 1;
            // }

            $updateOrder = $OrderModel->where(['order_id' => $order_id])->save($order);

            // 更新扩展表
            $extend['sensors_syn'] = 3; // 标记神策同步
            $updateOrderExtend = $OrderExtendModel->where(['order_id' => $order_id])->save($extend);

            if ($updatePayLog !== false && $updateOrderPrice !== false && $updateOrder !== false && $updateOrderExtend !== false) {
                $OrderActionLogModel->addLog($order_id, 0, 4, $event . '，订单金额：' . $currency . $data['pay_amount']); // 操作记录

                $OrderModel->commit();

                // 现卖订单付款后，推入队列
                if ($info['order_goods_type'] == 2 && $info['sale_type'] == 1) {
                    $resWms = $this->makeOrder($order_id);

                    if ($resWms['err_code'] != 0) return $this->apiReturn($resWms['err_code'], $resWms['err_msg']);
                }

                if ($info['order_pay_type'] != 3 && $info['order_amount'] > 0) { // 非账期和订单金额>0，添加到任务体系队列
                    $RbmqModel = D('Common/Rbmq');
                    $RbmqModel->connect('RBMQ_MSG_CONFIG')->queue(C('MEMBER_TASK_SYSTEM_LIST'))->push(array('user_id' => $info['user_id'], 'pay_amount' => $info['order_amount'], 'task_type' => 3), C('MEMBER_TASK_SYSTEM_LIST'));
                }

                $this->increaseLotteryQualify($info['user_id'], $order_id); //对账成功 送抽奖机会


                // regIssueCoupon($info['user_id'],platform(),$order_id);//系统自动送优惠券机会

                // 若不存在支付记录，则给客户发送支付信息
                // if (!$payLogInfo) {
                //     $keyword = 'order-full-paid';
                //     $this->checkPaySendMsg($info, $keyword);
                // }

                return $this->apiReturn(0, 'ERP对账成功');
            } else {
                $OrderModel->rollback();
                return $this->apiReturn(25103, 'ERP对账更新记录失败');
            }
        } catch (Exception $e) {
            $OrderModel->rollback();
            return $this->apiReturn(25104, $e->getMessage());
        }
    }

    // ERP删除支付记录
    public function delPaidLog()
    {
        $salenumber  = I('SALENUMBER', ''); // 销售订单号

        if (!$salenumber) return $this->apiReturn(25101, '缺少销售订单号');

        $OrderModel          = D('Order');
        $OrderPriceModel     = D('OrderPrice');
        $PayLogModel         = D('PayLog');
        $OrderActionLogModel = D('OrderActionLog');

        $this->apiRecord('ERP删除支付记录，销售订单号：' . $salenumber); // 记录接口访问信息

        $order_info = $OrderModel->getInfo('', $salenumber);

        if ($order_info['status'] == -1) {
            $OrderActionLogModel->addLog($order_info['order_id'], 0, 4, 'ERP取消支付记录失败，原因：平台订单已取消，不修改数据');
            return $this->apiReturn(0, '平台订单已取消，不修改数据');
        }

        if (in_array($order_info['status'], [1, 2])) {
            $OrderActionLogModel->addLog($order_info['order_id'], 0, 4, 'ERP取消支付记录失败，原因：平台订单未支付');
            return $this->apiReturn(0, 'ERP取消支付记录失败，原因：平台订单未支付');
        }

        if ($order_info['status'] > 4) {
            $OrderActionLogModel->addLog($order_info['order_id'], 0, 4, 'ERP取消支付记录失败，原因：平台订单已发货，不修改支付数据');
            return $this->apiReturn(0, '平台订单已发货，不修改支付记录');
        }

        $OrderModel->startTrans();

        // 删除支付记录
        $delPayLog = $PayLogModel->where(['order_sn' => $salenumber])->delete();

        if ($delPayLog === false) {
            $OrderModel->rollback();
            return $this->apiReturn(25105, '删除支付记录失败');
        }

        // 删除价格表记录
        $delOrderPrice = $OrderPriceModel->where(['order_sn' => $salenumber, 'price_type' => -1])->delete();

        if ($delOrderPrice === false) {
            $OrderModel->rollback();
            return $this->apiReturn(25106, '删除价格表记录失败');
        }

        // 更新订单状态为待付款
        $data['status'] = 2;
        $data['pay_time'] = 0;
        $updateOrder = $OrderModel->where(['order_sn' => $salenumber])->data($data)->save();

        if ($updateOrder === false) {
            $OrderModel->rollback();
            return $this->apiReturn(25107, '更新订单状态失败');
        }

        $OrderActionLogModel->addLog($order_info['order_id'], 0, 4, 'ERP取消支付记录');

        $OrderModel->commit();

        return $this->apiReturn(0, '删除支付记录成功');
    }

    // ERP新增收款记录
    public function setReceiptLog()
    {
        $receipt_bills = I('RECEIPTBILLS', '');

        if (!$receipt_bills) return $this->apiReturn(25105, '无收款记录');

        $this->apiRecord('ERP推送收款信息，' . json_encode($receipt_bills)); // 记录接口访问信息

        $OrderModel     = D('Order');
        $ErpPayLogModel = D('ErpPayLog');
        $OrderModel->startTrans();

        foreach ($receipt_bills as $k => $v) {
            $data = [];
            $map  = [];

            $map['order_sn']       = $v['PTNUMBER'];
            $map['receipt_sn']     = $v['RECNUMBER'];
            $map['receipt_amount'] = $v['RECAMT'];
            $map['receipt_time']   = $v['RECDATE'];

            $pay_log = $ErpPayLogModel->where($map)->find();

            if ($pay_log) continue;

            $order_info = $OrderModel->getInfo('', $v['PTNUMBER']); // 获取订单所有信息

            $data['order_id']       = $order_info['order_id'];
            $data['user_id']        = $order_info['user_id'];
            $data['currency']       = $order_info['currency'];
            $data['order_sn']       = $v['PTNUMBER'];
            $data['receipt_sn']     = $v['RECNUMBER'];
            $data['receipt_amount'] = $v['RECAMT'];
            $data['receipt_time']   = $v['RECDATE'];
            $data['create_time']    = time();

            $res = $ErpPayLogModel->add($data);

            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(25106, '新增收款记录失败，收款信息：' . json_encode($v));
            }
        }

        $OrderModel->commit();

        return $this->apiReturn(0, '新增收款记录成功');
    }

    // ERP取消收款记录
    public function delReceiptLog()
    {
        $receipt_bills = I('RECEIPTBILLS', '');

        if (!$receipt_bills) return $this->apiReturn(25105, '无收款记录');

        $this->apiRecord('ERP取消收款信息，' . json_encode($receipt_bills)); // 记录接口访问信息

        $ErpPayLogModel = D('ErpPayLog');
        $ErpPayLogModel->startTrans();

        foreach ($receipt_bills as $k => $v) {
            $save = [];
            $map  = [];

            $map['order_sn']       = $v['PTNUMBER'];
            $map['receipt_sn']     = $v['RECNUMBER'];
            // $map['receipt_amount'] = $v['RECAMT'];

            $save['cancel_time'] = $v['RECDATE']; // 取消支付时间
            $save['status']      = -1; // 取消支付
            $save['update_time'] = time();

            $res = $ErpPayLogModel->where($map)->save($save);

            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(25107, '取消收款记录失败，收款信息：' . json_encode($v));
            }
        }

        $ErpPayLogModel->commit();

        return $this->apiReturn(0, '取消收款记录成功');
    }

    // 线下转账操作
    public function offlinePaid()
    {
        $order_id = I('order_id');
        $order_sn = I('order_sn');
        $operator_id = I('operator_id');

        $OrderModel = D('Order');
        $OrderActionLogModel = D('OrderActionLog');

        // 删除ERP订单
        if (strpos($_SERVER['HTTP_REFERER'], 'sz') === false) { // 非测试环境下执行
            $order_info = $OrderModel->getInfo($order_id);

            $param = [];
            $param['TYPE']    = 1;
            $param['ORDERID'] = $order_info['erp_order_id'];
            $param['NUMBER']  = $order_sn;

            $res = A('Server/Consume')->pushDeleteOrder($param);

            if ($res === false) return $this->apiReturn(25110, '线下转账操作，删除ERP订单失败');
        }

        try {
            $OrderModel->startTrans();

            $data['erp_syn'] = 1; // 标记同步字段
            $data['pay_time'] = 0; // 取消付款时间 （定时取消任务不会执行该订单）
            $OrderModel->where(['order_id' => $order_id])->data($data)->save();
            $OrderActionLogModel->addLog($order_id, $operator_id, 2, '设置线下转账');

            $OrderModel->commit();

            return $this->apiReturn(0, '线下转账操作成功');
        } catch (Exception $e) {
            $OrderModel->rollback();
            return $this->apiReturn(25111, '线下转账操作失败，失败原因：' . $e->getMessage());
        }
    }

    //对账成功 添加抽奖机会
    private function increaseLotteryQualify($user_id, $order_id)
    {
        $result = $this->getCurrentActivities();
        if (0 == $result['errcode']) {
            foreach ($result['data'] as $value) {
                $rules_arr = explode(',', $value['qualify_get_rule']);
                if (in_array(4, $rules_arr)) {
                    $hasReceived = json_decode(get_curl(MARKET_DOMAIN . '/webapi/getReceivedQualifyDuringActivity', array('user_id' => $user_id, 'lottery_id' => $value['lottery_id'])), true);
                    if ($hasReceived['data']['from_order_count'] < $value['qualify_order']) {
                        //发放活动抽奖机会  下单只有一共送几次  每一单直送一次  所以次数为固定值
                        json_decode(get_curl(MARKET_DOMAIN . '/webapi/increaseQualifyToUser', array('user_id' => $user_id, 'lottery_id' => $value['lottery_id'], 'increase_draw_count' => 1, 'order_id' => $order_id, 'increase_type' => 4)), true);
                    }
                }
            }
        }
    }

    // 对账发送短信或微信
    public function checkPaySendMsg($info, $keyword)
    {
        $to_user = $info['user_id'];

        if ($info['currency'] == 1) {
            $currency = '￥';
        } else {
            $currency = '$';
        }
        $wechat_data = C("WECHAT_TEMPLATE.{$keyword}");
        $wechat_data['orderMoneySum']['value'] = $currency . $info['order_amount']; // 支付金额

        $goodsName = array(); //商品名称

        foreach ($info['order_items_info'] as $goods) {
            $goodsName[] = $goods['goods_name'];
        }

        $wechat_data['orderProductName']['value'] = implode(',', $goodsName);  // 商品信息

        $send_data['wechat_data'] = $wechat_data;
        $send_data['data'] = getOrderSpecialDetailsByOrderId($info['order_id']);
        $send_data['data']['order_sn'] = $info['order_sn']; // 需要包含order_sn
        $send_data['data']['create_time'] = $info['create_time']; // 需要包含order_sn
        $send_data['data']['order_amount'] = $info['order_amount']; // 需要包含order_sn
        //F('send_data_180112', $send_data);
        $send_data = json_encode($send_data);
        $this->sendOrderMsg($keyword, $send_data, $to_user);
    }

    /**
     * 标记明细的ERPid
     * @return [type] [description]
     */
    public function updateItemErpId()
    {
        if (!$this->auth()) {
            exit();
        }
        $mapping = I('mapping');
        $OrderItemsModel = D('OrderItems');

        foreach ($mapping as $k => $v) {
            $res = $OrderItemsModel->setErpId($k, $v);
            if ($res === false) {
                return $this->apiReturn(21019, '更新失败');
            }
        }
        return $this->apiReturn(0, '更新成功');
    }

    /**
     * 标记ERPid
     * @return [type] [description]
     */
    public function updateErpId()
    {
        if (!$this->auth()) {
            exit();
        }
        $order_id = I('order_id', 0, 'intval');
        $order_sn = I('order_sn', '');
        $erp_order_id = I('erp_order_id', '');
        $OrderModel = D('Order');

        $data = array(
            'erp_order_id' => $erp_order_id,
            'erp_syn_last_time' => $_SERVER['REQUEST_TIME'],
        );
        if (empty($order_id) && !empty($order_sn)) {
            $map = array('order_sn' => $order_sn);
        } else {
            $map = $order_id;
        }
        $res = $OrderModel->updateOrder($map, $data);
        if ($res === false) {
            return $this->apiReturn(21019, '更新失败');
        }
        return $this->apiReturn(0, '更新成功');
    }

    /**
     * 修改同步标记信号
     * @return [type] [description]
     */
    public function updateSynSign()
    {
        if (!$this->auth()) {
            exit();
        }
        $order_id = I('order_id', 0, 'intval');
        $sign = I('sign', 0, 'intval');
        $OrderModel = D('Order');
        if (!in_array($sign, array(-1, 1))) {
            $this->apiReturn(21020, '无效标记');
        }
        $data = array(
            'erp_syn' => $sign,
        );
        $res = $OrderModel->updateOrder($order_id, $data);
        if ($res === false) {
            return $this->apiReturn(21021, '更新失败');
        }
        return $this->apiReturn(0, '更新成功');
    }

    /**
     * 需要同步的订单ID列表
     * @return [type] [description]
     */
    public function synOrder()
    {
        if (!$this->auth()) {
            exit();
        }
        $OrderModel = D('Order');
        $map = array(
            'O.erp_syn' => 1,
            'O.is_type' => 0,
        );
        $list = $OrderModel->getNotTestUserList($map, 'order_id');
        return $this->apiReturn(0, '获取成功', $list);
    }

    /**
     * 发送订单至ERP同步队列
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function erpNeedInfo()
    {
        if (!$this->auth()) {
            exit();
        }
        $order_id = I('order_id', 0, 'intval');
        $OrderModel = D('Order');
        $OrderItemsModel = D('OrderItems');
        $OrderInvoiceModel = D('OrderInvoice');
        $OrderPriceModel = D('OrderPrice');
        $PayLogModel = D('PayLog');
        $CmsModel = D('Cms');

        $info = $OrderModel->getInfo($order_id);
        $goods_price = $OrderPriceModel->getGoodsPrice($order_id); //商品价格
        $preferential_price = $OrderPriceModel->getPreferentialPrice($order_id); //优惠价
        $ext_price = $OrderPriceModel->getExtPrice($order_id); //附加费
        $other_price = $preferential_price + $ext_price;

        $sale_name = $CmsModel->getUserName($info['sale_id']);
        $lists = $OrderItemsModel->getOrderList($info['order_id'], '', null);
        $invoice = $OrderInvoiceModel->getInfo($info['order_id']);
        $user = $this->getUser($info['user_id']);
        $pay = $PayLogModel->getInfo($info['order_id'], 'order_id,serial_number,pay_id,pay_name,pay_type,pay_amount,is_paid');

        if ($other_price != 0) {
            //平摊金额
            $other_arr = array();
            for ($i = 0; $i < count($lists) - 1; $i++) {
                $other_arr[$i] = price_format($lists[$i]['goods_amount'] / $goods_price * $other_price);
            }
            $other_arr[] = $other_price - array_sum($other_arr);
            foreach ($lists as $k => &$v) {
                $goods_amount = price_format($other_arr[$k] + $v['goods_amount'], 0, 4);
                $goods_price = price_format(($other_arr[$k] + $v['goods_amount']) / $v['goods_number'], 0, 4);
                $v['goods_amount'] = $goods_amount;
                $v['goods_price'] = $goods_price;
                $v['goods_amount_format'] = price_format($v['goods_amount'], $info['currency'], 4);
                $v['goods_price_format'] = price_format($v['goods_price'], $info['currency'], 4);
            }
        }

        $info['preferential_price'] = $preferential_price;
        $info['ext_price'] = $ext_price;
        $info['sale_name'] = $sale_name;
        $data = array(
            'info' => $info,
            'lists' => $lists,
            'invoice' => $invoice,
            'user' => $user['data'],
            'pay' => $pay,
        );
        return $this->apiReturn(0, '获取成功', $data);
    }

    public function erpShipping()
    {
        if (!$this->auth()) {
            exit();
        }
        //主信息
        $order_sn = I('order_sn', '');
        $erp_removal_id = I('erp_removal_id', '', 'trim');
        $order_shipping_type = I('order_shipping_type', 1, 'intval');
        $shipping_no = I('shipping_no', '');
        $shipping_name = I('shipping_name', '');
        $shipping_type = I('shipping_type', 1, 'intval'); //订单物流
        $flag = I('flag', 0, 'intval');
        //明细信息
        $list = I('list', array(), '');

        $OrderModel = D('Order');
        $OrderItemsModel = D('OrderItems');
        $OrderShippingModel = D('OrderShipping');
        $OrderShippingItemsModel = D('OrderShippingItems');
        $info = $OrderModel->getInfo('', $order_sn);
        if (empty($info)) {
            return $this->apiReturn(-0, '未找到相关订单', $order_sn);
        }

        //已付款才可以发货
        if ($info['status'] < 4) {
            return $this->apiReturn(-0, '当前状态未审核或未付款无法发货操作');
        }

        //获取快递公司ID
        if (!empty($shipping_name)) {
            $shipping = $this->getShippingId($shipping_name);
            if (empty($shipping['data'])) {
                return $this->apiReturn(-0, '无效快递公司', $shipping_name);
            }
            $shipping_id = $shipping['data'];
        } else {
            $shipping_id = 0;
        }

        $data = array(
            'order_id' => $info['order_id'],
            'erp_removal_id' => $erp_removal_id,
            'order_shipping_type' => $order_shipping_type,
            'shipping_no' => $shipping_no,
            'shipping_id' => $shipping_id,
            'shipping_type' => $shipping_type,
            'status' => 1,
        );
        //检查是否已存在物流单号
        $shipping = $OrderShippingModel->getOnly($erp_removal_id, $info['order_id']);

        $OrderShippingModel->startTrans();
        if (!empty($shipping)) { //已存在
            //清空旧发货明细
            $res = $OrderShippingItemsModel->deleteShipping($shipping['order_shipping_id']);
            if ($res === false) {
                return $this->apiReturn(-0, '清空旧发货明细失败');
            }
            $order_shipping_id = $shipping['order_shipping_id'];
            $data['order_shipping_id'] = $order_shipping_id;
            $res = $OrderShippingModel->save($data);
            if ($res === false) {
                $OrderShippingModel->rollback();
                return $this->apiReturn(-0, '保存发货主信息失败');
            }
        } else {
            $order_shipping_id = $OrderShippingModel->add($data);
            if ($order_shipping_id === false) {
                $OrderShippingModel->rollback();
                return $this->apiReturn(-0, '添加发货主信息失败');
            }
        }

        //发货明细
        foreach ($list as $v) {
            $item = $OrderItemsModel->getErpInfo($v['erp_rec_id']);
            $data = array(
                'order_shipping_id' => $order_shipping_id,
                'order_id' => $info['order_id'],
                'rec_id' => $item['rec_id'],
                'goods_id' => $item['goods_id'],
                'goods_name' => $item['sku_name'],
                'num' => $v['num'],
            );
            if (!$OrderShippingItemsModel->create($data)) {
                $OrderShippingModel->rollback();
                return $this->apiReturn(-0, $OrderShippingItemsModel->getError());
            }
            $res = $OrderShippingItemsModel->add();
            if ($res === false) {
                $OrderShippingModel->rollback();
                return $this->apiReturn(-0, '添加发货明细失败', $data);
            }
        }


        $stock = $OrderItemsModel->getSumNum($info['order_id']);
        $send_stock = $OrderShippingItemsModel->getSumNum($info['order_id']);
        if ($stock <= $send_stock) {
            //全部发货
            $status = 8;
        } else {
            //部分发货
            $status = 7;
        }
        $res = $OrderModel->setUserStatus($info['user_id'], $info['order_id'], $status);
        if ($res === false) {
            $OrderShippingModel->rollback();
            return $this->apiReturn(-0, '修改订单状态失败');
        }
        $OrderShippingModel->commit();

        if ($status == 8) {
            try {
                //消息通知客户
                $OrderAddressModel = D('OrderAddress');
                $order_address_info = $OrderAddressModel->getInfo($info['order_id']); //收货信息
                $mobile = $order_address_info['mobile']; // 收货联系方式
                $consignee = $order_address_info['consignee']; // 收货人姓名
                $province_val = $order_address_info['province_val'];
                $city_val = $order_address_info['city_val'];
                $district_val = $order_address_info['district_val'];
                $address = $province_val . ' ' . $city_val . ' ' . $district_val . ' ' . $order_address_info['address']; // 收货人地址

                $shipping = $OrderShippingModel->getInfo($info['order_id'], 1); // 订单物流信息
                switch (strval($info['order_goods_type'])) {
                    case '1':
                        $keyword = 'order-confirm-send';
                        break;
                    case '2':
                        $keyword = 'order-self-confirm-send';
                        break;
                    default:
                        break;
                }
                $to_user = $info['user_id']; // 客户
                $wechat_data = C("WECHAT_TEMPLATE.{$keyword}");
                $wechat_data['delivername']['value'] = $shipping['shipping_name']; // 快递公司
                $wechat_data['ordername']['value'] = $shipping_no; // 快递单号

                $order_res = getOrderSpecialDetailsByOrderId($info['order_id']);
                $order_res['order_sn'] = $info['order_sn'];
                $order_res['create_time'] = $info['create_time'];
                $order_res['order_amount'] = price_format($info['order_amount'], C('PLACE_CURRENCY_MAPPING.' . $info['currency']));
                $send_data['data'] = $order_res;
                $send_data['data']['address'] = $address;
                $send_data['data']['mobile'] = $mobile;
                $send_data['data']['consignee'] = $consignee;
                $send_data['data']['order_id'] = $info['order_id'];
                $send_data['data']['url'] = getShortUrl($info['order_id']);
                $send_data['url'] = '';
                $send_data['wechat_data'] = $wechat_data;
                $send_data = json_encode($send_data);
                $this->sendOrderMsg($keyword, $send_data, $to_user);
            } catch (Exception $e) {
            }
        }
        // 操作记录
        D('OrderActionLog')->addLog($info['order_id'], 0, 4, '订单已发货，快递公司：' . $shipping_name . '，快递单号：' . $shipping_no);

        return $this->apiReturn(0, '发货成功', $order_shipping_id);
    }

    //定时自动确认收货
    public function crontabconfirmsend()
    {
        $OrderModel = D('Order');
        $OrderShippingModel = D('OrderShipping');
        $lefttime = time() - C('ORDER_COMFIRM_TIME');

        $map = array(
            'shipping_time' => array('lt', $lefttime),
            'status' => 8,
        );
        $field = 'order_id,user_id,create_time,is_type';
        $info = $OrderModel->field($field)->where($map)->select();
        $RbmqModel = D('Common/Rbmq');
        foreach ($info as $key => $value) {
            $order_id = $value['order_id'];
            $where = array();
            $where['order_id'] = $order_id;
            $save = array();
            $save['finish_time'] = time();
            $save['status'] = 10; // 确认收货
            $OrderModel->where($where)->save($save);

            if (C('MKT_POINT_TIME') <= $value['create_time']) {
                if (0 == $value['is_type']) {
                    $push_data = array(
                        'user_id' => $value['user_id'], //用户id
                        'flow_type' => 1, //积分流向 订单完成固定为1
                        'flow_reason_type' => 3, //积分流向原因 订单固定为3
                        'flow_pf' => platform(), //平台'1PC 2H5 3小程序 4后台人工调整'
                        'flow_extra_id' => $order_id, //order_id
                    );
                    $RbmqModel->queue(C('QUEUE_MKT_POINT'))->push($push_data, C('QUEUE_MKT_POINT'));
                }
            }

            $order_where = array();
            $order_where['order_id'] = array('eq', $order_id);
            $order_where['shipping_type'] = array('eq', 1); // 货物物流
            $order_save = array();
            $order_save['status'] = 2;
            $OrderShippingModel->where($order_where)->save($order_save);

            // 操作记录
            D('OrderActionLog')->addLog($order_id, 0, 3, '自动确认收货');
        }

        // 物流轨迹自动确认收货
        $maps = array();
        $maps['status'] = array('eq', 8);
        $info = $OrderModel->field($field)->where($maps)->select();
        if ($info) {
            foreach ($info as $key => $value) {
                $order_id = $value['order_id'];
                $check['id'] = $order_id;
                $check = array_merge($check, authkey());
                $res = post_curl(API_DOMAIN . '/order/shipping', $check);

                // 查是否该order_id 下的物流信息status是否都是2,如果不是，七天后过期自动签收（包含猎芯快递）
                $where = array();
                $where['order_id'] = array('eq', $order_id);
                $where['shipping_type'] = array('eq', 1);
                $count = $OrderShippingModel->where($where)->count();
                if ($count) { //有物流信息
                    $where['status'] = array('neq', 2);
                    $count = $OrderShippingModel->where($where)->count();
                    if (strval($count) === '0') { // 说明都已签
                        //修改订单表
                        $where = array();
                        $where['order_id'] = $order_id;
                        $save = array();
                        $save['finish_time'] = time();
                        $save['status'] = 10; // 确认收货
                        $OrderModel->where($where)->save($save);

                        if (C('MKT_POINT_TIME') <= $value['create_time']) {
                            if (0 == $value['is_type']) {
                                $push_data = array(
                                    'user_id' => $value['user_id'], //用户id
                                    'flow_type' => 1, //积分流向 订单完成固定为1
                                    'flow_reason_type' => 3, //积分流向原因 订单固定为3
                                    'flow_pf' => platform(), //平台'1PC 2H5 3小程序 4后台人工调整'
                                    'flow_extra_id' => $order_id, //order_id
                                );
                                $RbmqModel->queue(C('QUEUE_MKT_POINT'))->push($push_data, C('QUEUE_MKT_POINT'));
                            }
                        }

                        // 操作记录
                        D('OrderActionLog')->addLog($order_id, 0, 3, '自动确认收货');
                    }
                }
            }
        }
        return $this->apiReturn(0, '成功');
    }

    /**
     * wms预分配
     * @return [type] [description]
     */
    public function wmsOrder()
    {
        $order_id = I('order_id', 0, 'intval');
        $order_sn = I('order_sn', '', 'trim');
        $res = $this->makeOrder($order_id, $order_sn);
        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }

    /**
     * 将create_time=0的真实用户 create_time数据更新
     * 1、create_time==0 && is_type=0
     * 2、有一个订单 && is_type==0
     * 3、如果没有订单 create_time=1483200000
     */
    public function updateUserCreateTime()
    {
        $UserMainModel = D('Home/UserMain');
        $OrderModel = D('Order');
        $count = $UserMainModel->where(array('create_time' => 0, 'is_type' => 0))->field('user_id')->count();
        $current_offset = 0;
        $page_num = 10;
        while ($current_offset < $count - 1) {
            $user_ids = $UserMainModel->where(array('create_time' => 0, 'is_type' => 0))->field('user_id')->limit($current_offset, $page_num)->select();
            if (count($user_ids) > 0) {
                foreach ($user_ids as $user_id) {
                    $find_order = $OrderModel->where(array('user_id' => $user_id, 'is_type' => 0))->find();
                    $create_time = !empty($find_order) ? $find_order['create_time'] : 1483200000;
                    $UserMainModel->data(array('create_time' => $create_time))->where(array('user_id' => $user_id))->save();
                }
            } else {
                break;
            }
            $current_offset += $page_num;
        }
    }

    // 获取ERP汇率
    public function getRate()
    {
        $rate = $this->getErpExchangeRate(2, $_REQUEST['REQUEST_TIME']);

        return $this->apiReturn(0, '', $rate);
    }

    // 处理ERP推送的支付记录时间
    public function handlePayLog()
    {
        $p = I('p', 1);
        $limit = 20;
        $first = $limit * ($p - 1);

        $OrderModel  = D('Order');
        $PayLogModel = D('PayLog');

        $map['p.is_paid']    = 1;
        $map['p.pay_type'] = ['in', [1, 4]]; // 全款和账期
        $map['o.status']   = ['gt', 2]; // 已付款订单
        $map['o.is_type']  = ['eq', 0]; // 过滤尽调订单

        $count = $PayLogModel->alias('p')->join('LEFT JOIN __ORDER__ o ON p.order_id = o.order_id')->where($map)->count();

        $total = ceil($count / $limit); // 总页数

        if ($p <= $total) {
            $p++;
        } else {
            echo '执行完毕';
            die;
        }

        $pay_log = $PayLogModel->alias('p')
            ->join('LEFT JOIN __ORDER__ o ON p.order_id = o.order_id')
            ->where($map)
            ->field('p.pay_log_id, p.order_id, p.order_sn, p.pay_time, o.create_time')
            ->limit($first . ',' . $limit)
            ->order('p.pay_log_id desc')
            ->select();

        if ($pay_log) {
            $params = [];

            foreach ($pay_log as $v) {
                if ($v['pay_time'] <= $v['create_time']) {
                    $params['ORDERS'][]['PTORDER'] = $v['order_sn'];
                }

                continue;
            }

            if (empty($params['ORDERS'])) {
                header('Refresh: 1;url=' . API_DOMAIN . '/order/handlepaylog?p=' . $p);
                die;
            }

            $res = A('Server/Consume')->getPayLogTime($params); // 调用ERP接口获取支付时间

            $this->apiRecord('获取ERP支付时间: ' . json_encode($res)); // 记录接口访问信息

            if (empty($res) || isset($res['4444'])) {
                header('Refresh: 1;url=' . API_DOMAIN . '/order/handlepaylog?p=' . $p);
                die;
            }

            // 更新支付时间
            foreach ($res['DATA'] as $val) {
                $save = [];
                $save['pay_time'] = $val['RECDATE'];

                $PayLogModel->where(['order_sn' => $val['PTORDER']])->save($save);
                $OrderModel->where(['order_sn' => $val['PTORDER']])->save($save);
            }

            if ($res === false) {
                header('Refresh: 1;url=' . API_DOMAIN . '/order/handlepaylog?p=' . $p);
                die;
            }
        }

        header('Refresh: 1;url=' . API_DOMAIN . '/order/handlepaylog?p=' . $p);
        die;
    }

    /**
     * 自营样品订单创建
     * @return [type] [description]
     */
    public function selfSampleCreate()
    {
        $user_id               = cookie('uid');
        $redis                 = redis_init();
        $goods_id              = I('request.goods_id', 0);       // 样品ID
        // $apply_num             = I('request.apply_num', 0, 'intval');      // 申请数量
        // $com_name           = I('request.com_name', '');              // 单位名称
        $sample_demand_desc    = I('request.sample_demand_desc', '');      // 项目需求描述
        $address_id            = I('request.address_id', 0, 'intval');     // 收货地址
        $user_basic            = I('request.user_basic', ''); // 用户基本信息
        $user_extend_info      = I('request.user_info', ''); // 用户扩展信息
        $company_info          = I('request.company_info', ''); // 公司信息
        $sample_type           = I('request.type', 1); // 样片类型，1-样片，2-工具尺，3-国产替代料
        $sample_project_name   = I('request.sample_project_name', ''); // 国产样片项目名称
        $sample_project_stage  = I('request.sample_project_stage', ''); // 国产样片项目阶段
        $sample_project_reason = I('request.sample_project_reason', ''); // 国产样片申请理由

        if (!$user_id) return $this->apiReturn(26001, '用户未登录');
        if (!$goods_id) return $this->apiReturn(26002, '样品ID缺失');
        // if (!$apply_num) return $this->apiReturn(26003, '请填写申请数量');
        // if (!$com_name) return $this->apiReturn(26004, '请填写单位名称');
        if (!$address_id) return $this->apiReturn(26005, '请填写收货地址');

        $OrderModel           = D('Order');
        $OrderExtendModel     = D('OrderExtend');
        $sampleInfoModel      = D('SampleInfo');
        $OrderItemsModel      = D('OrderItems');
        $OrderItemsExtModel   = D('OrderItemsExt');
        $OrderPriceModel      = D('OrderPrice');
        $OrderShippingModel   = D('OrderShipping');
        $OrderAddressModel    = D('OrderAddress');
        $OrderInvoiceModel    = D('OrderInvoice');
        $OrderActionLogModel  = D('OrderActionLog');
        $CmsModel             = D('Cms');
        $UserSampleApplyModel = D('UserSampleApply');
        $UserInfoModel        = D('UserInfo');
        $UserMainModel        = D('UserMain');
        $UserCompanyModel     = D('UserCompany');
        $quota = 0; // 消耗领取机会数量

        //根据类型判断
        switch ($sample_type) {
            case 1:
                if (!$sample_demand_desc) return $this->apiReturn(26017, '请填写项目需求描述');

                $apply_count = $UserInfoModel->where(['user_id' => $user_id])->getField('apply_count');
                if (!$apply_count) return $this->apiReturn(26018, '样片领取机会已用完');

                $goods_url = GOODS_DOMAIN . '/self/sample/list';
                $post_data['goods_id'] = $goods_id;

                $goods_info = json_decode(post_curl($goods_url, $post_data), true); // 自营样片信息

                if ($goods_info === false) return $this->apiReturn(26003, '请求商品服务失败');
                if ($goods_info['errcode'] != 0) return $this->apiReturn($goods_info['errcode'], $goods_info['errmsg']);

                $apply_num = $goods_info['data']['data'][$goods_id]['max_number']; // 最大申请数量
                $quota     = $goods_info['data']['data'][$goods_id]['quota']; // 消耗申请数量(领取机会)

                //默认使用最大申请数量
                // $apply_num = $max_number;
                // if ($apply_num > $max_number) return $this->apiReturn(26006, '超过了最大申请数量，最大申请数量为'.$max_number);

                if ($quota > $apply_count) return $this->apiReturn(26006, '该产品需要' . $quota . '个邀请机会才能领取');

                break;
            case 2:
                //还要去判断是否有工具尺订单
                //进来先放一个缓存,当作乐观锁
                if ($redis->HGET('lie_ruler_activity_applying', $user_id)) {
                    //其实是有订单在申请中
                    return $this->apiReturn(26019, '已经存在工具尺订单,不能重复申请');
                } else {
                    $sampleModel = D('Activity/Sample');
                    $hasRulerOrder = $sampleModel->checkHasRulerOrder($user_id);

                    if ($hasRulerOrder) return $this->apiReturn(26020, '已经存在工具尺订单,不能重复申请');

                    $redis->HSET('lie_ruler_activity_applying', $user_id, 1);
                }

                //去判断goods_id是否是特定的尺子商品id
                if ($goods_id != C('RULER_ACTIVITY_GOODS_ID')) return $this->apiReturn(26021, '该商品不是工具尺活动可领取的商品');

                $apply_num = 1;

                break;
            case 3:
                $apply_num = I('request.apply_num', 0, 'intval');      // 申请数量
                if (!$apply_num) return $this->apiReturn(26022, '请填写申请数量');
                if ($apply_num > 10) return $this->apiReturn(26023, '最大申请数量10PCS');
                if (!$sample_project_name) return $this->apiReturn(26024, '请填写样片项目名称');
                if (!$sample_project_stage) return $this->apiReturn(26025, '请选择样片项目阶段');
                if (!$sample_project_reason) return $this->apiReturn(26026, '请选择样片申请理由');

                $has_order = D('Activity/Sample')->checkCanApplyDomesticGoods($user_id, $goods_id);
                if ($has_order === false) return $this->apiReturn(26027, '已申请过该商品');

                break;
            default:
                return $this->apiReturn(26028, '样片类型不符合');
        }

        $currency = 1; // rmb

        $res = $this->getFinalGoods($goods_id, $apply_num, $currency); // 获取最终商品信息
        if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

        $goods = $res['data']['goods_info'];
        $initial_prices = ladder_final_price($res['data'], $currency, false); // 获取优惠前 原始价格
        $order_goods_type = in_array($goods['goods_type'], C('ORDER_GOODS_TYPE_MAP.1')) ? 1 : 2;

        $OrderModel->startTrans();

        $order_sn = $OrderModel->findSn(1, 'SZ'); // 生成订单编号
        $_REQUEST['is_liexin'] = 1; // 防止校验CSRF
        $order_source = A('Order/Cart')->order_source();
        $sale_id      = C('SELF_SAMPLE_SALES') ? C('SELF_SAMPLE_SALES') : 0; // 指定内部人员

        // 添加用户标签
        $count_where['user_id'] = $user_id;
        $count_where['status']  = ['neq', -1]; // 过滤已取消订单
        $count_where['is_type'] = 0; // 真实订单
        $order_count = $OrderModel->where($count_where)->count();
        $send_remark = '';

        if ($order_count) {
            $user_tags['is_new'] = 2; // 老用户
        } else {
            $user_tags['is_new'] = 1; // 新用户
            $send_remark = '新用户';
        }

        $user_basic && $user_tags = array_merge($user_tags, $user_basic); // 用户基本信息存在则合并

        $res = $UserMainModel->where(array('user_id' => $user_id))->save($user_tags);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(25027, '用户标签更新失败');
        }

        // 添加用户标签到缓存
        $s_user_info = S_user($user_id);
        $s_user_info['is_new'] = $user_tags['is_new'];
        $user_basic && $s_user_info['mobile']    = $user_basic['mobile'];
        $user_basic && $s_user_info['email']     = $user_basic['email'];
        $user_basic && $s_user_info['user_name'] = $user_basic['user_name'];
        S_user($user_id, $s_user_info);

        // 用户扩展信息
        if (!empty($user_extend_info)) {
            $res = $UserInfoModel->where(['user_id' => $user_id])->save($user_extend_info);
            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(25028, '用户扩展信息更新失败');
            }
        }

        /***  订单主表  ***/
        $data = array(
            'order_sn'         => $order_sn,
            'user_id'          => $user_id,
            'order_amount'     => 0, // 样品订单总额为0
            'order_goods_type' => 1,
            'order_source'     => $order_source,
            'sale_id'          => $sale_id,
        );

        $order_id = $OrderModel->createOrder($data);
        if ($order_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26007, '生成订单失败', $data);
        }

        /***  扩展信息  ***/
        $data = array(
            'order_id'              => $order_id,
            'client_ip'             => get_client_ip(0, true),
            'order_type'            => 0,
            'business_type'         => 1,
            'sensors_syn'           => 1, //神策推送
            'send_remark'           => $send_remark,
        );

        $res = $OrderExtendModel->createExtent($data);
        if ($res == false) {
            $OrderModel->rollback();
            return $this->apiReturn(26008, '生成订单扩展信息失败', $data);
        }

        $sampleInfo = [
            'order_id'              => $order_id,
            'sample_demand_desc'    => $sample_demand_desc,
            'sample_project_name'   => $sample_project_name,
            'sample_project_stage'  => $sample_project_stage,
            'sample_project_reason' => $sample_project_reason,
        ];

        $sampleResult = $sampleInfoModel->addSampleInfo($sampleInfo);
        if ($sampleResult == false) {
            $OrderModel->rollback();
            return $this->apiReturn(26008, '生成样片信息失败', $sampleInfo);
        }


        $delivery_time = is_array($goods['delivery_time']) ? $goods['delivery_time'][1] : $goods['delivery_time'];

        /***  订单明细  ***/
        $data = array(
            'order_id'      => $order_id,
            'user_id'       => $user_id,
            'goods_id'      => $goods_id,
            'brand_id'      => intval($goods['brand_id']),
            'supplier_id'   => $goods['supplier_id'],
            'supplier_name' => $goods['supplier_name'],
            'goods_name'    => $goods['goods_name'],
            'sku_name'      => $goods['sku_name'],
            'brand_name'    => $goods['brand_name'],
            'goods_number'  => $apply_num,
            'goods_price'   => 0, // 样片单价 0
            'initial_price' => $initial_prices['price'],
            'goods_type'    => $goods['goods_type'],
            'ac_type'       => $goods['ac_type'],
            'delivery_time' => $delivery_time,
            'order_goods_type' => $order_goods_type,
        );

        // 若为自营商品，则默认采购员为平台
        if (in_array($goods['goods_type'], [3, 4])) {
            $data['buyer_id'] = C('SELF_SAMPLE_PUR') ? C('SELF_SAMPLE_PUR') : 0;
        }

        // $push_sale_data = [];
        // $push_sale_data['goods_list'][0]['goods_name'] = $goods['goods_name'];
        // $push_sale_data['goods_list'][0]['brand_name'] = $goods['brand_name'];
        // $push_sale_data['goods_list'][0]['uni_key']    = $goods['brand_name'];

        // // 添加到销售商品库
        // $res = post_curl(SALE_DOMAIN . '/inner/goods/createMultiGoods', $push_sale_data);

        // $path = C('LOG_PATH').ACTION_NAME.'/'.date('y_m_d').'.log'; // 接口日志文件
        // \Think\Log::write('自营样片添加到销售商品库，参数：'.json_encode($push_sale_data).'返回数据：'.$res, INFO, '', $path);

        // $res = json_decode($res, true);

        // if ($res && $res['code'] == 0) {
        //     $goods_lib_data = [];

        //     $goods_lib_data[$goods['brand_name']]['sale_goods_id'] = $res['data']['bind_list'][0]['goods_id'];
        //     $goods_lib_data[$goods['brand_name']]['goods_sn'] = $res['data']['bind_list'][0]['goods_sn'];
        //     $goods_lib_data[$goods['brand_name']]['brand_id'] = $res['data']['bind_list'][0]['brand_id'];

        //     $data['sale_goods_id'] = isset($goods_lib_data[$data['brand_name']]) ? $goods_lib_data[$data['brand_name']]['sale_goods_id'] : 0;
        //     $data['brand_id']      = isset($goods_lib_data[$data['brand_name']]) ? $goods_lib_data[$data['brand_name']]['brand_id'] : 0;
        //     $data['goods_sn']      = isset($goods_lib_data[$data['brand_name']]) ? $goods_lib_data[$data['brand_name']]['goods_sn'] : '';
        // }

        $rec_id = $OrderItemsModel->createOrderItems($data);
        if ($rec_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26009, '生成订单明细失败', $data);
        }

        // 明细扩展表
        $goods_ext = array(
            'order_id'      => $order_id,
            'rec_id'        => $rec_id,
            'goods_moq'     => isset($goods['min_buy']) ? $goods['min_buy'] : 1,
            'goods_spq'     => isset($goods['min_mpq']) ? $goods['min_mpq'] : 1,
            'goods_packing' => isset($goods['packing_name']) ? $goods['packing_name'] : '',
            'goods_encap'   => isset($goods['encap']) ? $goods['encap'] : '',
            'goods_class'   => isset($goods['class2_name']) ? $goods['class2_name'] : '',
            'goods_unit'    => isset($goods['goods_unit_name']) ? $goods['goods_unit_name'] : '',
        );

        $res = $OrderItemsExtModel->add($goods_ext);
        if ($rec_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26009, '生成订单明细扩展表失败', $data);
        }

        /***  订单金额  ***/
        $price_id = $OrderPriceModel->createOrderPrice($order_id, 0, 1, $currency, $order_sn);
        if ($price_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26010, '生成订单金额失败');
        }

        /***  订单收货地址  ***/
        $data = array();
        $address_info = $this->getAddress($address_id);

        if ($address_info['err_code'] != 0) {
            $OrderModel->rollback();
            return $this->apiReturn($address_info['err_code'], $address_info['err_msg']);
        }

        $data = $address_info['data'];

        $data['area_code']           = $data['zipcode'];
        $data['address']             = $data['detail_address'];
        $data['order_shipping_type'] = 1;
        $data['order_id']            = $order_id;
        $data['order_sn']            = $order_sn;
        $data['address_id']          = $address_id;
        $data['address_type']        = 1;

        $order_address_id = $OrderAddressModel->createOrderAddress($data);
        if ($order_address_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26011, '生成订单收货地址失败');
        }

        /***  订单发票  ***/
        $data = array();
        $data['tax_id']    = 0;
        $data['nike_name'] = !empty($s_user_info['nike_name']) ? $s_user_info['nike_name'] : '';
        $data['order_id']  = $order_id;
        $data['order_sn']  = $order_sn;
        $data['inv_type']  = 1; // 不开票
        $order_invoice_id  = $OrderInvoiceModel->createOrderInvoice($data);
        if ($order_invoice_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26012, '生成订单发票信息失败');
        }

        /***  公司名称  ***/
        if (!empty($company_info)) {
            $com_id = $UserCompanyModel->where(['user_id' => $user_id])->getField('com_id');

            if ($com_id) {
                $res = $UserCompanyModel->where(['user_id' => $user_id])->save($company_info);

                if ($res === false) {
                    $OrderModel->rollback();
                    return $this->apiReturn(26013, '更新公司名称失败');
                }

                $com_info = S_company($user_id);
                $com_info['com_name']        = $company_info['com_name'];
                $com_info['type_id']         = $company_info['type_id'];
                $com_info['com_telphone']    = $company_info['com_telphone'];
                $com_info['com_province_id'] = $company_info['com_province_id'];
                $com_info['com_city_id']     = $company_info['com_city_id'];
                $com_info['com_area_id']     = $company_info['com_area_id'];
                $com_info['province_name']   = get_province($company_info['com_province_id']);
                $com_info['city_name']       = get_city($company_info['com_city_id']);
                $com_info['area_name']       = get_district($company_info['com_area_id']);
                $com_info['com_address']     = $company_info['com_address'];
                S_company($user_id, $com_info); // 保存到缓存
            } else {
                $company_info['user_id'] = $user_id;

                $add_com_id = $UserCompanyModel->add($company_info);

                if ($add_com_id === false) {
                    $OrderModel->rollback();
                    return $this->apiReturn(26013, '新增公司信息失败');
                }

                $UserMainModel->where(['user_id' => $user_id])->save(['company_id' => $add_com_id]); // 保存到用户主表

                $s_com_info = $UserCompanyModel->where(['com_id' => $add_com_id])->find();
                $s_com_info['province_name'] = get_province($company_info['com_province_id']);
                $s_com_info['city_name']     = get_city($company_info['com_city_id']);
                $s_com_info['area_name']     = get_district($company_info['com_area_id']);
                S_company($user_id, $s_com_info);

                $s_user_info['company_id'] = $add_com_id;
                S_user($user_id, $s_user_info);
            }
        }

        $UserMainModel = D('Home/UserMain');
        $user_info = $UserMainModel->getUserInfo($user_id);

        // 添加领取记录
        $data = array(
            'user_id'     => $user_id,
            'account'     => $user_info['mobile'] ? $user_info['mobile'] : $user_info['email'],
            'order_id'    => $order_id,
            'order_sn'    => $order_sn,
            'goods_id'    => $goods_id,
            'goods_name'  => $goods['goods_name'],
            'apply_num'   => $apply_num,
            'quota'       => $quota,
            'create_time' => time(),
            'type'        => $sample_type, //添加类型
        );

        $res = $UserSampleApplyModel->add($data);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(26015, '新增领取记录失败');
        }
        //样片需要扣减,如果是工具尺(type=2)就不需要扣减
        if ($sample_type == 1) {
            $res = $UserInfoModel->where(['user_id' => $user_id])->setDec('apply_count', $quota); // 扣减领取机会
            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(26016, '领取机会扣减失败');
            }
        }

        // 自营 锁库存
        // if ($order_goods_type == 2) {
        //     $lock_items = array();
        //     $lock_items[$goods_id] = $apply_num;

        //     $res = $this->lockSku($order_id, $lock_items);
        //     if (!$res || $res['errcode'] !== 0) {
        //         $OrderModel->rollback();
        //         $this->unlockSku($order_id, $lock_items);//解锁基石库存
        //         return $this->apiReturn($res['errcode'], $res['errmsg']);
        //     }
        // }

        $OrderModel->commit(); // 提交事务

        //通知内部人员
        $data = array(
            'data' => array(
                'order_sn' => $order_sn,
            )
        );
        $keyword = '';

        if ($sample_type == 1) {
            $event   = '提交自营样片订单';
            $keyword = 'self-sample-order'; // 自营样片消息模板
        } else if ($sample_type == 2) {
            $redis->HDEL('lie_ruler_activity_applying', $user_id);

            $event   = '提交工具尺订单';
        } else if ($sample_type == 3) {
            $data['data']['title'] = $order_goods_type == 1 ? '联营' : '自营';

            $event   = '提交国产样片订单';
            $keyword = 'domestic-sample-order'; // 国产样片消息模板
        }

        $data = json_encode($data);

        if ($sale_id) {
            $to_user   = $CmsModel->getUserWebUserId($sale_id); // 后台业务员对应前台uid
            $sale_name = $CmsModel->getUserName($sale_id);
            $event     .= '，订单已分配给客服：' . $sale_name;

            $keyword && $this->sendOrderMsg($keyword, $data, $to_user); // 推送短信
        }

        $to_inner_user = $user_info['is_test'] == 1 ? 'test' : ''; // 表明为测试单，不用通知业务人员
        $keyword && $this->sendOrderMsg($keyword, $data, $to_inner_user); // 推送短信到内部人员

        $OrderActionLogModel->addLog($order_id, $user_id, 1, $event); // 操作记录

        return $this->apiReturn(0, '生成成功', $order_sn);
    }

    // 样片申请校验
    public function applySampleCheck()
    {
        $user_id = cookie('uid');
        $goods_id = I('request.goods_id', 0);

        if (!$user_id) return $this->apiReturn(26101, '用户未登录');
        if (!$goods_id) return $this->apiReturn(26103, '样品ID缺失');

        $params = [
            'user_id' => $user_id,
            'goods_id' => $goods_id,
        ];

        $res = $this->checkSampleOrder($params);

        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        return $this->apiReturn(0, '校验成功');
    }

    // 样片订单创建 - 2022-12-7
    public function sampleCreate()
    {
        return $this->apiReturn(26201, '暂时不能申请样品');

        $params = [];
        $params['user_id']      = cookie('uid');
        $params['address_id']   = I('request.address_id', 0, 'intval'); // 收货地址
        $params['goods_id']     = I('request.goods_id', 0); // 样品ID
        $params['apply_num']    = I('request.apply_num', 0, 'intval'); // 申请数量
        $params['project_info'] = $_REQUEST['project_info']; // 项目信息

        if (!$params['user_id']) return $this->apiReturn(26101, '用户未登录');
        if (!$params['address_id']) return $this->apiReturn(26102, '请填写收货地址');
        if (!$params['goods_id']) return $this->apiReturn(26103, '样品ID缺失');
        if (!$params['apply_num']) return $this->apiReturn(26104, '请填写申请数量');

        if (I('request.pf', '') != 1) {
            $params['project_info'] = json_decode($params['project_info'], true);
        }

        if (empty($params['project_info'])) return $this->apiReturn(26105, '项目信息参数缺失');
        if (empty($params['project_info']['email'])) return $this->apiReturn(26105, '项目信息-email必填');
        if (empty($params['project_info']['position'])) return $this->apiReturn(26105, '项目信息-职位必填');
        if (empty($params['project_info']['apply_name'])) return $this->apiReturn(26105, '项目信息-申请人姓名必填');
        if (empty($params['project_info']['mobile'])) return $this->apiReturn(26105, '项目信息-手机号必填');
        if (empty($params['project_info']['com_name'])) return $this->apiReturn(26105, '项目信息-公司名称必填');
        if (empty($params['project_info']['com_nature'])) return $this->apiReturn(26105, '项目信息-公司性质必填');
        if (empty($params['project_info']['project_name'])) return $this->apiReturn(26105, '项目信息-项目名称必填');
        if (empty($params['project_info']['project_type'])) return $this->apiReturn(26105, '项目信息-项目类型必填');
        if (empty($params['project_info']['project_production_time'])) return $this->apiReturn(26105, '项目信息-项目投产时间必填');
        if (empty($params['project_info']['project_desc'])) return $this->apiReturn(26105, '项目信息-项目描述必填');
        if (empty($params['project_info']['annual_usage'])) return $this->apiReturn(26105, '项目信息-年使用量必填');


        // 校验样片单
        $res = $this->checkSampleOrder($params);
        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        // 生成样片单
        $res = $this->createSampleOrder($params);
        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        return $this->apiReturn(0, '生成成功', $res['data']);
    }


    /**
     * 判断用户是否支付过一笔联营订单
     */
    public function checkUser()
    {
        try {
            $uid = cookie("uid");
            $OrderModel = D('Order');
            $count = $OrderModel->where("order_goods_type = 1 and status > 2 and user_id= {$uid}")->count("order_id");
            $is_pay_order = $count ? 1 : 0;
            return $this->apiReturn(0, '', ['is_pay_order' => $is_pay_order]);
        } catch (\Exception $e) {
            return $this->apiReturn(0, '', ['is_pay_order' => 0]);
        }
    }

    /**
     * 查找用户公司的公司信息
     */
    public function checkUserCompany($uid)
    {
        $company = S_company($uid);
        if ($company && isset($company['com_name']) && in_array($company['com_name'], C("BCAK_HOUSE_COMPANYNAME"))) {
            return true;
        }
        return false;
    }


    // 同步明细的采购员、备注、批次
    public function sysItemInfo()
    {
        $order_id = I('order_id', 0);

        if (!$order_id) return $this->apiReturn(1, '参数缺失');

        $OrderModel       = D('Order');
        $OrderExtendModel = D('OrderExtend');
        $OrderItemsModel  = D('OrderItems');
        $CmsModel         = D('Cms');

        $params = [];
        $params['FNUMBER'] = $OrderExtendModel->where(['order_id' => $order_id])->getField('erp_sn');

        $order_items = $OrderItemsModel->getOrderList($order_id, '', null);

        foreach ($order_items as $k => $v) {
            $params['items'][$k]['erp_rec_id'] = $v['erp_rec_id'];
            $params['items'][$k]['batch']      = $v['batch'];

            $buyer = $CmsModel->getUserName($v['buyer_id']);

            $params['items'][$k]['buyer']      = $buyer;
            $params['items'][$k]['remark']     = $v['remarks'];
        }

        $this->apiRecord('发送邮件推送ERP通知：' . json_encode($params)); // 记录推送ERP参数信息

        $res = A('Server/Consume')->sysOrderItems($params); // 调用ERP接口

        if ($res !== true) return $this->apiReturn(2, '发送邮件失败，ERP返回信息：' . $res);

        return $this->apiReturn(0, '同步成功');
    }

    /*
        订单统计接口
        @return 付款次数 最后一次付款时间

    */
    public function order_count()
    {

        $user_id = cookie('uid') + 0;

        $activity_model = D('Order/PayLog');

        if ($user_id > 0) {
            $pay_money         = $activity_model->order_count($user_id);
            $data['count']     = count($pay_money);
            $data['last_time'] = !empty($pay_money[0]['pay_time']) ? $pay_money[0]['pay_time'] : 0;
            $data['now_time']  = time(); //服务器时间
            return $this->apiReturn(0, 'success', $data);
        }
    }

    /**
     * 判断团购是否已经成团
     */
    public function checkTuangouOrderIsOk()
    {
        $order_id = I("request.order_id", 0, "intval");
        if (!$order_id) {
            return $this->apiReturn(21025, '您的好友已经拼团成功，点击拼团购买，发起新的拼团单吧~');
        }
        $masterOrder = M("UserGroup")->where(["order_id" => $order_id, "status" => 1])->field("id")->find();
        if (!$masterOrder) {
            return $this->apiReturn(21026, '您的好友已经拼团成功，点击拼团购买，发起新的拼团单吧~');
        }

        $group_id = $masterOrder["id"];

        $childCount = M("UserGroupJoin")->where(["group_id" => $group_id, "status" => 1])->count("id");
        if (!$childCount) {
            return $this->apiReturn(0, '');
        }
        return $this->apiReturn(21026, '您的好友已经拼团成功，点击拼团购买，发起新的拼团单吧~');
    }

    /*
     * 判断团购  是否是自己发起的 如果是  不能继续购买
     */
    protected function checkTuangouBySelf($tuangouOrderId = 0)
    {
        if (!$tuangouOrderId || !cookie("uid")) return true;
        $tuangouOrder = M("UserGroup")->where(["order_id" => $tuangouOrderId])->field("user_id")->find();
        if ($tuangouOrder && $tuangouOrder["user_id"] == cookie("uid")) {
            return  false;
        }
        return true;
    }

    // 修改明细备注、批次、采购员
    public function updateOrderItems()
    {
        $order_id    = I('request.order_id', 0);
        $operator_id = I('request.operator_id', 0);
        $change_item = I('request.change_item', 0);

        // $OrderModel          = D('Order');
        // $OrderExtendModel    = D('OrderExtend');
        $OrderItemsModel     = D('OrderItems');
        $OrderItemsExtModel  = D('OrderItemsExt');
        $OrderActionLogModel = D('OrderActionLog');
        // $CmsModel            = D('Cms');

        // $order_info   = $OrderModel->getInfo($order_id);
        // $order_extend = $OrderExtendModel->getInfo($order_id);
        $event = '';

        $order_item_info = $OrderItemsModel->getOrderList($order_id, '', null);

        $event = '编辑明细，修改前：'; // 日志记录

        foreach ($order_item_info as $item) {
            $event .= '[' . $item['goods_name'] . '|批次：' . $item['batch'] . '；商品备注：' . $item['remarks'] . '；合同备注：' . $item['contract_remark'];

            // if ($order_info['order_goods_type'] == 1 && $item['buyer_id']) { // 联营
            //     $sale_name = $CmsModel->getUserName($item['buyer_id']);
            //     $event .= '；采购员：'.$sale_name;
            // }

            $event .= ']';
        }

        // 修改明细
        foreach ($change_item as $k => $v) {
            // $OrderItemsModel->where(['rec_id' => $k])->save(['buyer_id' => $v['buyer_id']]);

            // 明细扩展表
            $item_ext = [];
            $item_ext['batch'] = $v['batch'];
            $item_ext['remarks'] = $v['remarks'];
            $item_ext['contract_remark'] = $v['contract_remark'];

            $OrderItemsExtModel->where(['rec_id' => $k])->save($item_ext);
        }

        $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event);

        return $this->apiReturn(0, '编辑明细成功');
    }

    // 商品锁库、解库操作
    public function lockSkuAction()
    {
        $order_id = I('order_id', 0);
        $type     = I('type', 1); // 1-锁库，2-解锁

        if (!$order_id) return $this->apiReturn(-1, '参数缺失');

        $OrderItemsModel  = D('OrderItems');
        $OrderExtendModel = D('OrderExtend');

        $order_items = $OrderItemsModel->getOrderList($order_id, '', null);

        $lock_items = [];

        foreach ($order_items as $v) {
            $lock_items[$v['goods_id']] = $v['goods_number'];
        }

        if ($type == 1) {
            $res = $this->lockSku($order_id, $lock_items);
        } else {
            //判断是否需要请求wms取消订单
            $wms_order  = $OrderExtendModel->getFieldByOrderId($order_id, 'wms_order');
            $reduce_wms = $wms_order > 0 ? true : false;

            $res = $this->unlockSku($order_id, $lock_items, false, $reduce_wms); //解锁基石库存
        }

        return $this->apiReturn($res['errcode'], $res['errmsg']);
    }

    public function getOrderActivityInfo()
    {
        $order_id = I('order_id', 0);
        $rand_str = I('rand_str', "");
        $order_sn = I('order_sn', "");
        $is_debug = I('is_debug', "");
        $user_id  = cookie('uid');

        $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件

        if ($order_id) {
            $order_info = D("Order/Order")->where(["order_id" => $order_id, "user_id" => $user_id, "status" => ["gt", 1]])->find();
        } elseif ($order_sn) {
            $order_info = D("Order/Order")->where(["order_sn" => trim($order_sn), "user_id" => $user_id, "status" => ["gt", 1]])->find();
        }


        $order_id = $order_id ? $order_id : $order_info["order_id"];

        \Think\Log::write(sprintf("getOrderActivityInfo:获取抽奖资格:订单id:%s,单号:%s,uid:%s,rand_str:%s", $order_id, $order_sn, $user_id, $rand_str), 'WARN', '', $path);


        $maxAmount = 100;
        if (in_array($user_id, [438627, 132654, 449215, 129106])) {
            $maxAmount = 0.1;
        }
        if (empty($order_info)) {
            \Think\Log::write(sprintf("getOrderActivityInfo:获取抽奖资格:订单id:%s,单号:%s,uid:%s,错误原因:没有查询到订单信息", $order_id, $order_sn, $user_id), 'WARN', '', $path);
            return $this->apiReturn(-1, "");
        }


        if (!$rand_str) {
            \Think\Log::write(sprintf("getOrderActivityInfo:获取抽奖资格:订单id:%s,单号:%s,uid:%s,rand_str:%s,参数错误:rand_str is empty", $order_id, $order_sn, $user_id, $rand_str), 'WARN', '', $path);
            return $this->apiReturn(-1, "参数错误:rand_str is empty");
        }

        if (md5_32(strval($order_id)) != $rand_str) {
            \Think\Log::write(sprintf("getOrderActivityInfo:获取抽奖资格:订单id:%s,单号:%s,uid:%s,rand_str:%s,参数校验错误", $order_id, $order_sn, $user_id, $rand_str), 'WARN', '', $path);
            return $this->apiReturn(-1, "参数错误:rand_str 参数校验错误");
        }


        $time = time() - 3600 * 48;
        $paylog = D("Order/PayLog")->where(["order_id" => $order_id, "create_time" => ["lt", $time]])->find();
        if (!empty($paylog)) {
            \Think\Log::write(sprintf("getOrderActivityInfo:获取抽奖资格:订单id:%s,单号:%s,uid:%s,错误原因:该订单付款时间是48小时前的", $order_id, $order_sn, $user_id), 'WARN', '', $path);
            return $this->apiReturn(-1, "查询该付款订单数据时间超过1小时前的,不符合标准");
        }

        $order_extend = D("Order/OrderExtend")->where(["order_id" => $order_id])->find();
        $exchange_rate = !empty($order_extend) ? $order_extend : 0;

        if (in_array($order_info["currency"], [1]) && $order_info["order_amount"] < $maxAmount) {
            \Think\Log::write(sprintf("getOrderActivityInfo:获取抽奖资格:订单id:%s,单号:%s,uid:%s,错误原因:订单金额不满足", $order_id, $order_sn, $user_id), 'WARN', '', $path);
            return $this->apiReturn(-1, "");
        }

        if (in_array($order_info["currency"], [2, 3, 4]) && $order_info["order_amount"] * $exchange_rate < $maxAmount) {
            \Think\Log::write(sprintf("getOrderActivityInfo:获取抽奖资格:订单id:%s,单号:%s,uid:%s,错误原因:订单金额不满足", $order_id, $order_sn, $user_id), 'WARN', '', $path);
            return $this->apiReturn(-1, "");
        }

        $send_qualify = json_decode(get_curl(MARKET_DOMAIN . '/webapi/increaseQualifyToUser', array('user_id' => $user_id, 'lottery_id' => 282, 'increase_draw_count' => 1, 'order_id' => 33344455, 'increase_type' => 4)), true);
        \Think\Log::write(sprintf("getOrderActivityInfo:获取抽奖资格,订单id:%s,单号:%s,uid:%s:increaseQualifyToUser返回:%s", $order_id, $order_sn, $user_id, json_encode($send_qualify)), 'WARN', '', $path);
        if ($send_qualify && $send_qualify['errcode'] == 0) {
            return $this->apiReturn(0, "");
        } else {
            return $this->apiReturn(1, "");
        }
    }
}
