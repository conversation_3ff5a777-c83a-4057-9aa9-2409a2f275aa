<?php
namespace Order\Controller;

use Order\Controller\BaseController;
use Dompdf\Dompdf;
use Order\Model\OrderModel;

class ContractController extends BaseController{

    /**
     * 信息获取
     * @param  boolean $check 是否严重
     * @return [type]         [description]
     */
    private function order($check = true)
    {
        $id = I('id', '');
        $types = I('types', 0, 'intval');
        if (empty($id)) {
            return $this->apiReturn(21004, '未找到相关订单');
        }
        $user_id = cookie('uid');
        if ($types == 0) {//订单
            $data = A('Order/Order')->contractInfo($id);
        } elseif ($types == 2) {//PCB
            $data = $this->pcbinfo($id);
        }
        if ($data['err_code'] != 0) {
            return $this->apiReturn($data['err_code'], $data['err_msg']);
        }

        $tax_rate = OrderModel::$SALE_COM_ID_TAX[$data["data"]["sale_com_id"]];
        if ($tax_rate == 0.13){
            $data['data']['tax_rate'] = strtotime($data['data']['create_time']) >= 1553443200 ? '13' : '16';
        }else{
            $data['data']['tax_rate'] =  $tax_rate*100;
        }

        $data['data']['show_logo'] = true;
        // 销售公司ID，1：猎芯科技，2：深贸，3：华云，4：猎芯供应链，5：香港富开，6：华云香港，7：猎芯技术，8：猎芯技术香港，9：粤丰实，10：工品数字 11 华云泰国 12 华云越南
        // 华云，工品，粤丰实的合同不需要logo
        if (in_array($data['data']['sale_com_id'], [3, 9, 10])) {
            $data['data']['show_logo'] = false;
        }
        // $supplierInfo = [
        //     'supplier_name' => '深圳市猎芯科技有限公司',
        //     'supplier_address' => '深圳市龙岗区坂田街道宝能科技园12栋11楼',
        //     'supplier_open_bank' => '交通银行股份有限公司深圳梅林支行',
        //     'supplier_bank_account' => '4438 9999 1010 0035 1176 4',
        //     'stamp' => 'stamp1.png'
        // ];
        // if ($data['data']['sale_com_id'] == 3) {
        //     $supplierInfo = [
        //         'supplier_name' => '深圳华云数智工业科技有限公司',
        //         'supplier_address' => '深圳市龙岗区坂田街道岗头社区清湖工业区宝能科技园（南区）一期B区B3栋1001B',
        //         'supplier_open_bank' => '交通银行深圳梅林支行',
        //         'supplier_bank_account' => '4430 6638 8013 0084 1990 9',
        //         'stamp' => 'stamp_sale_com_id_3.png'
        //     ];
        // } else if ($data['data']['sale_com_id'] == 9) {
        //     $supplierInfo = [
        //         'supplier_name' => '深圳市粤丰实工业有限公司',
        //         'supplier_address' => '深圳市龙岗区坂田街道马安堂社区环城南路15号恒大都会广场1栋2111-C033',
        //         'supplier_open_bank' => '中国建设银行股份有限公司深圳梅林支行',
        //         'supplier_bank_account' => '4425 0100 0069 0000 3970',
        //         'stamp' => 'stamp_sale_com_id_9.png'
        //     ];
        // } else if ($data['data']['sale_com_id'] == 10) {
        //     $supplierInfo = [
        //         'supplier_name' => '深圳工品数字科技有限公司',
        //         'supplier_address' => '深圳市龙岗区坂田街道岗头社区清湖工业区宝能科技园（南区）一期B区B3栋1001D',
        //         'supplier_open_bank' => '中国建设银行股份有限公司深圳梅林支行',
        //         'supplier_bank_account' => '4425 0100 0069 0000 3971',
        //         'stamp' => 'stamp_sale_com_id_10.png'
        //     ];
        // }

        // 获取签署公司信息
        $supplierInfo = [];
        $SignComsModel = D('Crm/SignComs');
        $signComs = $SignComsModel->getInfoById($data['data']['sign_com_id']);

        // 深贸
        if ($data['data']['sale_com_id'] == 2) {
            $supplierInfo['supplier_name'] = $signComs['com_name_hk'];
            $supplierInfo['supplier_name_en'] = $signComs['com_name_en'];
            $supplierInfo['supplier_address'] = $signComs['com_addr_hk'];
            $supplierInfo['swift_code'] = $signComs['swift_code'];
            $supplierInfo['bank_addr'] = $signComs['bank_addr'];
            $supplierInfo['email'] = $signComs['email'];
        } else {
            $supplierInfo['supplier_name'] = $signComs['com_name'];
            $supplierInfo['supplier_address'] = $signComs['com_addr'];
        }

        $supplierInfo['supplier_open_bank'] = $signComs['bank_name'];
        $supplierInfo['supplier_bank_account'] = $signComs['bank_account'];
        $supplierInfo['stamp'] = $signComs['com_chop_src'] ? 'data:image/png;base64,' . base64_encode(file_get_contents($signComs['com_chop_src'])) : '';
        $supplierInfo['logo'] = $signComs['logo_src'] ? 'data:image/png;base64,' . base64_encode(file_get_contents($signComs['logo_src'])) : '';
        $supplierInfo['com_website'] = $signComs['com_website'];

        $data['data']['supplier_info'] = $supplierInfo;

        $data['data']['company_address_full'] = preg_replace('/([\s0-9a-zA-Z]+)/', ' $1', $data['data']['company_address_full']);
        if (mb_strlen($data['data']['company_address_full'], 'utf8') >= 25) {
            $arr = mb_str_split($data['data']['company_address_full']);
            $new = [];
            array_walk($arr, function($v, $k) use (&$new) {
                if ($k == 25) $new[] = ' ';
                $new[] = $v;
            });
            $data['data']['company_address_full'] = implode('', $new);
        }
        if ($check) {
            if ($data['data']['user_id'] != $user_id) {
                return $this->apiReturn(21011, '请打印自己订单的合同');
            }
            // if ($types != 2 && $data['data']['order_pay_type'] != 1) {
                // return $this->apiReturn(21012, '非全款订单请联系客服发送合同');
            // }
            if ($data['order_goods_type'] == 2) {
                if (in_array($data['data']['status'], array(-2, -1, 1))) {
                    return $this->apiReturn(21013, '当前状态无法下载合同');
                }
                if (empty($data['data']['company_name'])) {
                    return $this->apiReturn(21013, '请完善公司名称');
                }
                if (empty($data['data']['nick_name'])) {
                    return $this->apiReturn(21013, '请完善会员姓名');
                }
                if (empty($data['data']['company_phone'])) {
                    return $this->apiReturn(21013, '请完善公司座机');
                }
                if (empty($data['data']['company_address'])) {
                    return $this->apiReturn(21013, '请完善公司地址');
                }
            }
        }
        return $this->apiReturn(0, '成功', $data['data']);
    }

    /**
     * PCB订单合同信息
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    private function pcbinfo($id)
    {
        $datas = $this->getPcbInfo($id);
        if ($datas['err_code'] != 0) {
            return $this->apiReturn($datas['err_code'], $datas['err_msg']);
        }
        $data = $datas['data'];
        $user = S_user($data['user_id']);
        $company = S_company($data['user_id']);

        $data['nick_name'] = $user['nike_name'];
        $data['company_fax'] = $company['com_fax'];
        $data['create_time'] = date('Y-m-d', $data['create_time']);
        $data['pay_name'] = $data['pay_name'] == '交通银行' ? '公司转账' : $data['pay_name'];
        $data['order_amount_format'] = price_format($data['order_amount'], $data['currency']);
        if ($data['invoice_type'] == 3) {//增值税专票
            $data['company_name'] = $data['order_invoice']['tax_title'];
            $data['nick_name'] = !empty($data['order_invoice']['consignee']) ? $invoice['consignee'] : $user['nike_name'];
            $data['company_phone'] = $data['order_invoice']['company_phone'];
            $data['company_address'] = $data['order_invoice']['company_address'];
            $data['company_address_full'] = $data['order_invoice']['company_address'];
        } else {
            $province = !empty($company['com_province_id']) ? get_province($company['com_province_id']) : '';
            $city = !empty($company['com_city_id']) ? get_city($company['com_city_id']) : '';
            $area = !empty($company['com_area_id']) ? get_district($company['com_area_id']) : '';
            $data['nick_name'] = $user['nike_name'];
            $data['company_name'] = $company['com_name'];
            $data['company_phone'] = $company['com_telphone'];
            $data['company_address'] = $company['com_address'];
            $data['company_address_full'] = $province . $city . $area . $company['com_address'];
        }
        $datas['data'] = $data;
        return $datas;
    }

    //检查或者
    public function checkPdf()
    {
//        if (I("pf") == 2){
//            $pdf = $this->pdf();
//            return $this->apiReturn($pdf['err_code'], $pdf['err_msg'],$pdf["data"]);
//        }
        $order = $this->order();
        return $this->apiReturn($order['err_code'], $order['err_msg']);
    }

    public function pdf()
    {
        $pf = I("pf");
        $order = $this->order();//获取订单
        if ($order['err_code'] != 0) {
            return $this->apiReturn($order['err_code'], $order['err_msg']);
        }
        $check = I('check', 0, 'intval');
        $types = I('types', 0, 'intval');
        if (($check || IS_POST) && $pf !=2 ){
            exit();
        }
        $pf = platform();
        $out = array();
        if ($pf == 2) {
            $out = array('Attachment'=>0);
        }
        $this->assign('data', $order['data']);
        if ($types == 2) {//PCB
            $html = $this->fetch('pdf_pcb');
        } elseif ($order['data']['order_goods_type'] == 2) {//自营
            $html = $this->fetch('pdf_self');
        } else {
            $template = $order['data']['currency'] == 1 ? 'pdf' : 'pdf_hk';
            $html = $this->fetch($template);
        }
        Vendor('dompdf.autoload_inc');
        $dompdf = new \Dompdf\Dompdf();
        $options = new \Dompdf\Options();
        $options->setIsPhpEnabled(true);
        $options->setIsFontSubsettingEnabled(true);//启用字体子集，缩小pdf大小
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->setOptions($options);
        $dompdf->render();

//        $order_sn = $order['data']['order_sn'];
//        if (I("pf") == 2){ //如果是手机端，输入
//            $output = $dompdf->output();
//            $path = DIRECTORY_SEPARATOR."public".DIRECTORY_SEPARATOR."down".DIRECTORY_SEPARATOR."pdf".DIRECTORY_SEPARATOR.date("Ymd").DIRECTORY_SEPARATOR;
//            $file_name =$order_sn."_".time().".pdf";;
//            $real_path = $_SERVER['DOCUMENT_ROOT'].$path.$file_name;
//
//            if(!file_exists($_SERVER['DOCUMENT_ROOT'].$path)){
//                $res = mkdir(iconv("UTF-8", "GBK", $_SERVER['DOCUMENT_ROOT'].$path),0755,true);
//            }
//            file_put_contents($real_path, $output);
//
//
//            $dompdf->stream($order['data']['order_sn'].'.pdf', $out);
//
//            #return $this->apiReturn(0, '获取pdf成功', getSiteUrl()."/public/down/pdf/".date("Ymd")."/".$file_name);
//        }

        $dompdf->stream($order['data']['order_sn'].'.pdf', $out);
    }

    /**
     * 内部查看合同
     * @return [type] [description]
     */
    public function pdfInfo()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(60); // 设置最大执行时间为 60 秒

        if (!$this->auth()) {
            exit();
        }
        $order = $this->order(false);//获取订单
        if ($order['err_code'] != 0) {
            return $this->apiReturn($order['err_code'], $order['err_msg']);
        }

        $this->assign('data', $order['data']);
        // p($order);die;
        // return $this->display('pdf');
        // if ($order['data']['order_goods_type'] == 2) {//自营
        //     $html = $this->fetch('pdf_self');
        // } else {
        //     $template = $order['data']['currency'] == 1 ? 'pdf' : 'pdf_hk';
        //     $html = $this->fetch($template);
        // }

        // 销售公司为“香港深贸电子”并且签约公司为非“香港深贸电子”公司的香港订单合同使用此新合同模板
        if ($order['data']['sale_com_id'] == 2 && $order['data']['sale_com_id'] != $order['data']['sign_com_id']) {
            $template = 'pdf_hk_new';
        } else {
            $template = $order['data']['currency'] == 1 ? 'pdf' : 'pdf_hk';
        }

        $html = $this->fetch($template);

        Vendor('dompdf.autoload_inc');
        $dompdf = new \Dompdf\Dompdf();
        $options = new \Dompdf\Options();

        $options->setIsPhpEnabled(true);
        $options->setIsFontSubsettingEnabled(true);

        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->setOptions($options);
        $dompdf->render();
        $dompdf->stream($order['data']['order_sn'].'.pdf');
    }
}