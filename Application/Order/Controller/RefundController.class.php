<?php
namespace Order\Controller;

use Order\Controller\BaseController;
use Payment\Cpcn;

class RefundController extends BaseController
{
    const WX = 'wxpay';
    const ALI = 'alipay';
    const UNION = 'unionpay';
    const UNIONB2B = 'unionpay_b2b';
    const WALLET = 'wallet';

    public function _initialize()
    {
        parent::_initialize();
        Vendor('payment.cpcn.cpcn');
    }

    /**
     * 获取退款单信息
     * @return [type] [description]
     */
    public function info()
    {
        $refund_id = I('refund_id', 0, 'intval');
        $refund_sn = I('refund_sn', '', 'trim');
        $OrderRefundModel = D('OrderRefund');
        $data = $OrderRefundModel->getOrderInfo($refund_id, $refund_sn);
        return $this->apiReturn(0, '成功', $data);
    }

    /**
     * 退款单信息
     * @return [type] [description]
     */
    public function serialInfo()
    {
        $serial_number = I('serial_number', '', 'trim');
        $OrderRefundLogModel = D('OrderRefundLog');
        $map = array(
            'serial_number' => $serial_number,
        );
        $info = $OrderRefundLogModel->where($map)->find();
        return $this->apiReturn(0, '成功', $info);
    }

    /**
     * 设置退款单完成
     */
    public function setFinish()
    {
        if (!$this->auth()) {
            $this->_empty();
            exit();
        }
        $refund_id = I('refund_id', 0, 'intval');
        $serial_number = I('serial_number', '', 'trim');
        $refund_time = I('refund_time', 0, 'intval');
        $OrderRefundModel = D('OrderRefund');
        $OrderRefundLogModel = D('OrderRefundLog');
        $OrderRefundModel->startTrans();
        $res = $OrderRefundModel->setStatus($refund_id, 10, $refund_time);
        if ($res === false) {
            $OrderRefundModel->rollback();
            return $this->apiReturn(41059, '记录退款状态失败');
        }
        $res = $OrderRefundLogModel->setRefundStatus($refund_id, 10, $serial_number, $refund_time);
        if ($res === false) {
            $OrderRefundModel->rollback();
            return $this->apiReturn(41059, '记录流水号失败');
        }
        $OrderRefundModel->commit();
        return $this->apiReturn(0, '记录成功');
    }

    /**
     * 联营订单退款
     * @return [type] [description]
     */
    public function order($refund_id)
    {
        // $refund_id = I('refund_id', 0, 'intval'); //订单ID
        if(!isset($_SERVER["HTTP_API_KEY"]) || $_SERVER["HTTP_API_KEY"] != "ichunt2020"){
            return $this->apiReturn(-1, '非法请求');
        }
        $this->apiRecord(sprintf("退款单id：%s",$refund_id));
        $PayLogModel = D('PayLog');
        $PaymentModel = D('Payment');
        $OrderPriceModel = D('OrderPrice');
        $OrderRefundModel = D('OrderRefund');
        $OrderRefundLogModel = D('OrderRefundLog');
        $info = $OrderRefundModel->getOrderInfo($refund_id);
        if ($info['order_pay_type'] == 3) { //账期
            $this->apiRecord(sprintf("退款单id：%s,失败原因:%s",$refund_id,'账期订单不支持在线退款，请联系财务处理'));
            return $this->apiReturn(41046, '账期订单不支持在线退款，请联系财务处理');
        }
        $list = $OrderRefundLogModel->getListBySn($info['refund_sn'], 'RL.refund_log_id');
        if (!empty($list)) {
            $this->apiRecord(sprintf("退款单id：%s,失败原因:%s",$refund_id,'退款单已申请过退款，请重新生成退款单退款'));
            return $this->apiReturn(41072, '退款单已申请过退款，请重新生成退款单退款');
        }
        //查询支付方式分布
        $datas = $PayLogModel->getInfo($info['order_id']);
        if ($info['order_pay_type'] == 2) { //预付款
            if (count($datas) > 1) { //多次支付
                $this->apiRecord(sprintf("退款单id：%s,失败原因:%s",$refund_id,'不支持多笔付款订单自动退款操作'));
                return $this->apiReturn(41068, '不支持多笔付款订单自动退款操作');
            }
        }
        //获取总已付金额
        // 'ORDER_PAYED_STATUS' => array('-1','-2','-3','-7','4'),
        $payed_amount = abs($OrderPriceModel->getPayed($info['order_id']));
        //获取总已退金额(包括所有正在退款和当前退款单)
        $amount = $OrderRefundModel->getSumAmount($info['order_id']);

        //订单剩余可退金额
        $order_surplus_amount = bcsub($payed_amount, $amount, 2);
        if ($order_surplus_amount < 0) {
            $this->apiRecord(sprintf("退款单id：%s,失败原因:%s",$refund_id,'订单剩余可退款金额不足需退款金额'));
            return $this->apiReturn(41047, '订单剩余可退款金额不足需退款金额');
        }
        //退款单剩余退款金额
        $surplus_refund_amount = $info['pay_amount'] - $info['price_fall'];

        // echo sprintf('付款金额：%s，已付金额：%s', $info['order_sn'], $payed_amount),'<br/>';
        // echo sprintf('退款单：%s，退款金额：%s', $info['refund_sn'], $surplus_refund_amount),'<br/>';
        // echo sprintf('已退、退款中金额：%s', $amount);
        // dump($datas);

        $error = '';
        $error_arr = array();
        foreach ($datas as $data) {
            //判断是否还需要退钱
            if ($surplus_refund_amount == 0) {
                $error_arr[] = '退款单剩余退款金额为0';
                break;

            } elseif (empty($data['pay_id'])) {
                $error_arr[] = '请联系财务处理流水号';
                continue;

            } elseif ($data['pay_id'] == -1) {//0元付款，无需退款
                $error_arr[] = '0元付款，无需退款';
                continue;
            }
            //单条支付流水的已退金额（含正在退款）
            $serial_refunded = $OrderRefundLogModel->getRefundedAmount($data['pay_log_id']);

            //单条支付流水的可退金额
            $serial_refund_amount = bcsub($data['pay_amount'], $serial_refunded, 2);
            if ($serial_refund_amount <= 0) {
                // $error_arr[] = sprintf('%s流水号：%s已退款', $data['pay_name'], $data['serial_number']);
                continue;
            }
            //计算单条支付流水需要退的金额
            if (bccomp($surplus_refund_amount, $serial_refund_amount, 2) >= 0) {
                $refund_amount = $serial_refund_amount;
                $surplus_refund_amount = bcsub($surplus_refund_amount, $serial_refund_amount, 2);
            } else {
                $refund_amount = $surplus_refund_amount;
                $surplus_refund_amount = 0;
            }
            // echo $refund_amount,'<br/>';
            // echo $surplus_refund_amount,'<br/>';
            // continue;

            $pay_code = $PaymentModel->getFieldByPayId($data['pay_id'], 'pay_code');
            if ($pay_code == self::WALLET) {
                $is_cpcn = false;//是否中金
            } elseif ($data['pay_order_sn'] == $data['serial_number']) {
                $this->Pay = new Cpcn(Cpcn::BUSINESS_MODE);
                $is_cpcn = true;//是否中金
            } else {
                Vendor('payment.'.$pay_code.'.'.$pay_code);
                $this->Pay = new $pay_code();
                $is_cpcn = false;//是否中金
            }
            $is_aggregate_pay = in_array($pay_code, array(self::ALI, self::WX)) ? true : false;//聚合支付（微信支付宝）

            //查询第三方支付订单是否已退款
            if ($is_aggregate_pay || $is_cpcn) {
                if ($is_aggregate_pay && $is_cpcn) {
                    $pay = $this->Pay->query($data['serial_number'], array('is_aggregate_pay' => $is_aggregate_pay));
                } else {
                    $pay = $this->Pay->query($data['serial_number']);
                }
                if ($is_cpcn) {
                    $out_refund_no = $info['refund_sn'].'_'.date('md').hash_key(8, 2);
                } else {
                    $out_refund_no = $info['refund_sn'];
                }

            } elseif (in_array($pay_code, array(self::UNION, self::UNIONB2B))) {
                $pay = $this->Pay->query($data['pay_order_sn'], $data['create_time']);
                $out_refund_no = str_replace('-', 'X', $info['refund_sn']);

            } elseif ($pay_code == self::WALLET) {//钱包
                $error_arr[] = sprintf('暂不支持钱包支付方式的退款，请联系财务处理公司转账流水号：%s，金额：%s。', $data['serial_number'], $refund_amount);
                continue;
                $res = $this->walletRefundAmount($info['order_sn']);
                if ($res['err_code'] != 0) {
                    return $this->apiReturn('xx', '获取已退款款项失败');
                }
                $pay = array(
                    'status' => 0,
                );
                if ($data['pay_amount'] > $res['data']) {//支付金额 与 实际已退金额不一致
                    $pay['status'] = 1;//可退
                }
                $out_refund_no = '';

            } else {
                $error_arr[] = sprintf('请联系财务处理公司转账流水号：%s，金额：%s。', $data['serial_number'], $refund_amount);
                continue;
            }
            // dump(json_decode($pay['origin'],true));
            // die();
            if ($pay === false) {
                $error_arr[] = sprintf('%s查询不到流水号：%s，请联系技术人员及财务查询。', $data['pay_name'], $data['serial_number']);
                continue;
            }
            if ($pay['status'] != 1) {
                $error_arr[] = sprintf('%s流水号：%s，已退款，无法再申请退款。', $data['pay_name'], $data['serial_number']);
                continue;
            }

            $refund_status = $is_cpcn || $pay_code == self::WALLET ? 1 : 10;//中金支付需要后续确认退款成功

            $add = array(
                'refund_id' => $refund_id,
                'order_id' => $info['order_id'],
                'pay_log_id' => $data['pay_log_id'],
                'serial_number' => $out_refund_no,//退款交易流水号
                'pay_amount' => $refund_amount,
                'refund_status' => $refund_status,
            );
            $res = $OrderRefundLogModel->add($add);
            if ($res === false) {
                $error_arr[] = '记录支付流水单退款申请失败';
                continue;
            }
            
            $order = array(
                'is_aggregate_pay' => $is_aggregate_pay,//是否聚合支付
                'out_refund_no' => $out_refund_no,//退款单号
                'order_amount' => $data['pay_amount'],//支付金额
                'refund_amount' => $refund_amount,//退款金额
                'serial_number' => $data['serial_number'],//支付交易流水号
                'pay_order_sn' => $data['pay_order_sn'],//支付订单号
                'pay_create_time' => $data['create_time'],//第三方创建支付单据时间
            );

            if ($pay_code != self::WALLET) {//非钱包直接退款
                $refund = $this->Pay->refund($order);
                try{
                    $this->apiRecord(sprintf("退款单id：%s,非钱包直接退款:%s",$refund_id,json_encode($refund)));
                }catch(\Exception  $e){

                }

                // dump($refund);
                // dump(json_decode($refund['origin'],true));
                if ($refund === false) {
                    $error_arr[] = sprintf('订单请求接口退款失败，联系技术查明原因');
                    continue;
                } elseif ($refund === true) {
                    $error_arr[] = sprintf('%s流水号：%s，已退款，无法再申请退款。', $data['pay_name'], $data['serial_number']);
                    continue;
                }
            } else {
                $refund = array(
                    'refund_id' => '',
                );
                $error_arr[] = sprintf('流水号：%s，由于订单使用钱包支付，请联系财务申请钱包充值方式退款', $data['serial_number']);
                continue;
            }
        }
        //确认是否所有流水已退款成功，修改主单
        $status_arr = $OrderRefundLogModel->getListBySn($info['refund_sn'], 'RL.refund_status');
        $success = array_filter($status_arr, function($v){
            return $v['refund_status'] == 10;
        });
        if (!empty($success) && count($success) == count($status_arr)) {//全部流水已退款
            $save = array(
                'status' => 10,
                'is_refund' => 1,
                'is_sys' => 2,//2-同步财务
                'refund_time' => $_SERVER['REQUEST_TIME'],
                'update_time' => $_SERVER['REQUEST_TIME'],
            );
            $OrderRefundModel->where(array('refund_sn' => $info['refund_sn']))->save($save);
            D("OrderService")->where(["refund_sn"=>$info['refund_sn']])->save(["refund_status"=>3,"update_time"=>time()]);
            $this->pushOrderRefundDoneToErp(["order_id"=>$info["order_id"]]);
        }

        if (!empty($error_arr)) {
            $error = implode("</br>", $error_arr);
            $this->apiRecord(sprintf("退款单id：%s,失败原因:%s",$refund_id,$error));
            return $this->apiReturn(41042, $error);
        }
        return $this->apiReturn(0, '申请退款成功');
    }

    /**
     * 给用户手动退款充值用户中金账号
     * @return [type] [description]
     */
    public function recharge()
    {
        $refund_id = I('refund_id', 0, 'intval');
        // $order_id = I('order_id', 0, 'intval');
        $bank_id = I('bank_id', 0, 'intval');
        $pay_code = I('pay_code', '', 'trim');
        // $final_amount = I('refund_amount', 0); // 部分退款金额

        // $OrderModel = D('Order');
        // $PayLogModel = D('PayLog');
        $OrderRefundModel = D('OrderRefund');
        $OrderRefundLogModel = D('OrderRefundLog');

        $info = $OrderRefundModel->getOrderInfo($refund_id);
        if ($info['status'] != 10) {
            // return $this->apiReturn(41072, '退款单已申请过退款，请重新生成退款单退款');
        }
        // $order_info = $OrderModel->getInfo($order_id);

        // $total_refund_amount = 0;//总共退款金额

        // // 若部分退款金额存在
        // if ($final_amount) {
        //     $total_refund_amount = $final_amount;
        // } else {
        //     $map['order_id'] = $order_id;
        //     $datas = $PayLogModel->getInfo($map);

        //     foreach ($datas as $data) {
        //         if ($data['pay_id'] != 9) {//只退钱包支付
        //             continue;
        //         }
        //         //获取已退款款项
        //         $amount = $OrderRefundModel->getSumAmount($order_id, $data['pay_type']);
        //         $refund_amount = bcsub($data['pay_amount'], $amount, 2);
        //         if ($refund_amount <= 0) {
        //             continue;
        //         }
        //         $total_refund_amount += $refund_amount;
        //     }
        // }

        // if ($total_refund_amount <= 0) {
        //     return $this->apiReturn(41058, '没有可退金额');
        // }

        $data = array(
            'order_sn' => $info['refund_sn'],
            'wallet_id' => get_wallet($info['user_id']),
            'user_id' => $info['user_id'],
            'bank_id' => $bank_id,
            'account_type' => '12',//企业
            'card_type' => '',//企业账号不需要传
            'limit_pay' => '20',//聚合支付限制信用卡类型 10可用，20不可用
            'serial_number' => $info['refund_sn']. '_'. date('Ymd').hash_key(5,2),
            'remark' => $info['order_sn'].'|退款|',
            'usage' => '销售订单退款',
            'create_time' => time(),
            'order_amount' => $info['pay_amount'] - $info['price_fall'],
        );

        $this->Pay = new Cpcn(Cpcn::MARKET_MODE);
        $config = array(
            'pay_code' => $pay_code,
            'pay_mode' => 1,
        );
        $str = $this->Pay->get_code($data, $config);
        if (in_array($pay_code, array(self::ALI, self::UNION, self::UNIONB2B))) {
            echo $str;
        } else{
            redirect($str);
        }
    }


    /**
     * 自营通过支付记录ID退款
     * @param  [type] $pay_log_id [description]
     * @return [type]             [description]
     */
    public function payLogId($pay_log_id, $refund_amount, $refund_sn, $force_offline = false)
    {
        $PayLogModel = D('PayLog');
        if (empty($pay_log_id)) {
            return $this->apiReturn(-1, '日志ID为空');
        }
        $data = $PayLogModel->getInfo(array('pay_log_id' => $pay_log_id), 'pay_log_id, user_id, order_sn, pay_order_sn, serial_number, pay_id, pay_name, pay_type, pay_amount, refund_amount,  create_time', true);
        $res = $this->act($data, $refund_amount, $refund_sn, $force_offline);
        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }

    /**
     * 自营通过流水号退款
     * @param  [type] $serial_number [description]
     * @param  [type] $refund_amount [description]
     * @param  [type] $refund_sn     [description]
     * @return [type]                [description]
     */
    public function serialNumber($serial_number, $refund_amount, $refund_sn, $force_offline = false)
    {
        $PayLogModel = D('PayLog');
        if (empty($serial_number)) {
            return $this->apiReturn(-1, '流水号为空');
        }
        $data = $PayLogModel->getInfo(array('serial_number'=> $serial_number), 'pay_log_id, user_id, order_sn, pay_order_sn, serial_number, pay_id, pay_name, pay_type, pay_amount, refund_amount, create_time', true);
        $res = $this->act($data, $refund_amount, $refund_sn, $force_offline);
        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }

    /**
     * 自营退款接口
     * 账期：不退
     * 钱包：生成退款地址
     * 支付宝微信银联：退款
     * @param  [type] $refund_sn     [description]
     * @param  [type] $refund_amount [description]
     * @return [type]                [description]
     */
    private function act($data, $refund_amount, $refund_sn, $force_offline)
    {
        $PayLogModel = D('PayLog');
        $PaymentModel = D('Payment');
        if ($data['pay_type'] == 4) {
            return $this->apiReturn(41046, '账期订单无需生成退款单');
        } elseif ($data['pay_id'] == -1) {//0元付款，无需退款
            return $this->apiReturn(0, '0元无需退款');
        } elseif ($data['pay_amount'] < ($data['refund_amount'] + $refund_amount)) {
            return $this->apiReturn(41046, '该流水号仅剩余'.($data['pay_amount'] - $data['refund_amount']).'元可退金额');
        }

        if ($data['pay_id'] == 0 || $force_offline) {//公司转账 || 强制线下退款
            $res = $this->setPayLogRefundAmount($refund_amount, $data['pay_log_id']);//登记
            if ($res['err_code'] !== 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
            $res = array(
                'payment_no' => $data['serial_number'],
                'serial_number' => $data['serial_number'],
            );
            return $this->apiReturn(0, '登记退款成功', $res);
        }

        $pay_code = $PaymentModel->getFieldByPayId($data['pay_id'], 'pay_code');
        if ($pay_code == self::WALLET) {
            $Pay = new Cpcn(Cpcn::MARKET_MODE);
            $is_cpcn = false;//是否中金
        } elseif ($data['pay_order_sn'] == $data['serial_number']) {
            $Pay = new Cpcn(Cpcn::BUSINESS_MODE);
            $is_cpcn = true;//是否中金
        } else {
            Vendor('payment.'.$pay_code.'.'.$pay_code);
            $Pay = new $pay_code();
            $is_cpcn = false;//是否中金
        }
        $is_aggregate_pay = in_array($pay_code, array(self::ALI, self::WX)) ? true : false;//聚合支付（微信支付宝）

        //查询第三方支付订单是否已退款
        if ($is_aggregate_pay || $is_cpcn) {
            if ($is_aggregate_pay && $is_cpcn) {
                $pay = $Pay->query($data['serial_number'], array('is_aggregate_pay' => $is_aggregate_pay));
            } else {
                $pay = $Pay->query($data['serial_number']);
            }
            if ($is_cpcn) {
                $out_refund_no = $refund_sn.'_'.date('md').hash_key(8, 2);
            } else {
                $out_refund_no = $refund_sn;
            }

        } elseif (in_array($pay_code, array(self::UNION, self::UNIONB2B))) {
            $pay = $Pay->query($data['pay_order_sn'], $data['create_time']);
            $out_refund_no = str_replace('-', 'X', $refund_sn);

        } else {//钱包
            $res = $this->walletRefundAmount($data['order_sn']);
            if ($res['err_code'] != 0) {
                return $this->apiReturn(-1, '获取已退款款项失败');
            }
            $pay = array(
                'status' => 0,
            );
            if ($data['pay_amount'] > $res['data']) {//支付金额 与 实际已退金额不一致
                $pay['status'] = 1;//可退
            }
            $out_refund_no = $refund_sn.'_'.date('md').hash_key(8, 2);
        }

        if ($pay === false) {
            return $this->apiReturn(41042, sprintf('%s查询不到流水号：%s，请联系技术人员及财务查询。', $data['pay_name'], $data['serial_number']));
        }
        if ($pay['status'] != 1) {
            try {
                $this->setPayLogRefundAmount($data['pay_amount'], $data['pay_log_id'], null, false);
            } catch (\Exception $e) {
            }
            return $this->apiReturn(41042, sprintf('%s流水号：%s，已退款，无法再申请退款。', $data['pay_name'], $data['serial_number']));
        }

        $order = array(
            'refund_sn' => $refund_sn,//退款单号
            'is_aggregate_pay' => $is_aggregate_pay,//是否聚合支付
            'out_refund_no' => $out_refund_no,//退款流水号
            'order_amount' => $data['pay_amount'],//支付金额
            'refund_amount' => $refund_amount,//退款金额
            'serial_number' => $data['serial_number'],//支付交易流水号
            'pay_order_sn' => $data['pay_order_sn'],//支付订单号
            'pay_create_time' => $data['create_time'],//第三方创建支付单据时间
        );
        $PayLogModel->startTrans();
        if ($pay_code != self::WALLET) {//非钱包直接退款
            $res = $this->setPayLogRefundAmount($refund_amount, $data['pay_log_id']);//登记该记录已退款金额
            if ($res['err_code'] !== 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }

            $refund = $Pay->refund($order);
            if ($refund === false) {
                $PayLogModel->rollback();
                return $this->apiReturn(41042, '订单请求接口退款失败，联系技术查明原因');
            } elseif ($refund === true) {
                $PayLogModel->rollback();
                return $this->apiReturn(41042, sprintf('%s流水号：%s，已退款，无法再申请退款。', $data['pay_name'], $data['serial_number']));
            }
            //推送检测退款状态
            try {
                if ($is_cpcn) {
                    $RbmqModel = D('Common/Rbmq');
                    $RbmqModel->queue(C('REFUND_QUEUE_KEY'))->push($order, C('REFUND_QUEUE_KEY'));
                }
            } catch (\Exception $e) {
            }
            $res = array(
                'payment_no' => $refund['out_trade_no'],
                'serial_number' => $refund['out_refund_no'],
                'recharge_url' => '',
            );
            $msg = '退款申请已提交成功';

        } else {//钱包生成付款地址
            $data = array(
                'order_sn' => $refund_sn,
                'wallet_id' => get_wallet($data['user_id']),
                'user_id' => $data['user_id'],
                'account_type' => '12',//企业
                'card_type' => '',//企业账号不需要传
                'limit_pay' => '20',//聚合支付限制信用卡类型 10可用，20不可用
                'serial_number' => $out_refund_no,
                'remark' => $data['order_sn'].'|退款|'.$data['serial_number'],
                'usage' => '销售订单退款',
                'create_time' => time(),
                'order_amount' => $refund_amount,
            );
            $config = array(
                'pay_code' => $pay_code,
                'pay_mode' => 1,
            );
            $str = $Pay->get_code($data, $config);
            if (empty($str)) {
                return $this->apiReturn(41042, '生成网银充值地址失败');
            }
            $url = API_DOMAIN.'/public/redirecthtml?code='.urlencode(base64_encode($str));
            $res = array(
                'recharge_url' => $url,
                'payment_no' => '',
                'serial_number' => '',
            );
            $msg = '退款地址生成成功';
        }
        $PayLogModel->commit();
        return $this->apiReturn(0, $msg, $res);
    }

    /**
     * 设置支付流水的退款金额
     * @param [type]  $refund_amount 退款金额
     * @param [type]  $pay_log_id    支付订单流水ID
     * @param [type]  $payment_no    支付订单流水号
     * @param boolean $diff          true退款金额为单次 false退款金额为总额
     */
    public function setPayLogRefundAmount($refund_amount, $pay_log_id = null, $payment_no = null, $diff = true)
    {
        $PayLogModel = D('PayLog');
        if ($pay_log_id) {
            $user_id = $PayLogModel->getFieldByPayLogId($pay_log_id, 'user_id');
            $data = $pay_log_id;
        } elseif ($payment_no) {
            $user_id = $PayLogModel->getFieldBySerialNumber($payment_no, 'user_id');
            $data = array('serial_number' => $payment_no);
        } else {
            return $this->apiReturn(-1, '条件缺失');
        }
        $res = $PayLogModel->setRefundAmount($data, $refund_amount, $diff);//登记
        if ($res === false) {
            $this->apiReturn(-1, '登记退款失败');
        }
        return $this->apiReturn(0, '成功', array('user_id' => $user_id));
    }

    /**
     * 检测退款进度
     * @param  [type]  $out_refund_no    退款单流水号
     * @param  boolean $is_aggregate_pay 是否聚合支付
     * @return [type]                    [description]
     */
    public function query($out_refund_no, $is_aggregate_pay = false)
    {
        $Pay = new Cpcn(Cpcn::BUSINESS_MODE);
        $data = $Pay->queryRefund($out_refund_no, $is_aggregate_pay);
        if (empty($data)) {
            return $this->apiReturn(-1, '查询失败');
        }
        return $this->apiReturn(0, '查询采购', $data);
    }


    // 订单后台退货退款
    public function refundGoods()
    {
        $order_id          = I('order_id', 0);
        $refund_info       = I('refund_info'); // 退货信息
        $all_refund_amount = I('all_refund_amount'); // 退款金额
        $price_fall        = I('price_fall'); // 手动差价
        $refund_reason     = I('refund_reason'); // 退货原因
        $operator_id       = I('operator_id'); // 操作人

        if (!$order_id) return $this->apiReturn(44001, '参数缺失');

        $OrderModel            = D('Order');
        $OrderItemsModel       = D('OrderItems');
        $OrderRefundModel      = D('OrderRefund');
        $OrderRefundItemsModel = D('OrderRefundItems');
        $OrderPriceModel       = D('OrderPrice');
        $OrderExtendModel      = D('OrderExtend');
        $OrderActionLogModel   = D('OrderActionLog');
        $ErpPayLogModel        = D('ErpPayLog');
        $OrderModel->startTrans();

        $order_info = $OrderModel->getInfo($order_id);

        // 判断订单状态是否在待付尾款、待发货、部分发货
        if (!in_array($order_info['status'], [3, 4, 7])) return $this->apiReturn(44002, '当前订单状态无法退货退款');

        $param = [];
        $param['NUMBER'] = $order_info['order_sn'];
        $param['PAYAMOUNT'] = price_format($all_refund_amount - $price_fall, 0, 2); // 退款总额 = 退款金额 - 手动差价

        // 若为账期订单或预付款为0
        if ($order_info['order_pay_type'] == 3 || ($order_info['order_pay_type'] == 2 && $order_info['advance_amount'] == 0)) {
            $res = A('Server/Consume')->pushDeleteOrder($param);

            if ($res === false) {
                return $this->apiReturn(44003, 'ERP删除订单失败');
            }

            // ERP删除订单成功后，取消ERP收款记录
            $ErpPayLogModel->where(['order_id'=>$order_id])->save(['status'=>-1, 'cancel_time'=>time()]);

            $discount_amount = $OrderPriceModel->getPreferentialPrice($order_id); // 优惠金额
            $discount_price = 0; // 退款优惠金额
            $refund_goods_info = ''; // 退货商品信息

            // 修改明细数量
            foreach ($refund_info as $k=>$v) {
                if (!$v['refund_num']) continue;

                // 若存在优惠金额，（实际单价-均摊后单价）* 退货数量的总和，再加上优惠金额，为退款后最终优惠金额
                if ($discount_amount < 0) {
                    $discount_price += ($v['goods_price'] - $v['single_pre_price']) * $v['refund_num'];
                }

                $OrderItemsModel->where(['rec_id' => $k])->setDec('goods_number', $v['refund_num']); 

                $refund_goods_name = $OrderItemsModel->where(['rec_id' => $k])->getField('goods_name');

                $refund_goods_info .= $refund_goods_name.': '.$v['refund_num'].'; ';
            }

            // 若退款优惠金额存在，则更新订单优惠金额
            if ($discount_price > 0) {
                $final_discount_amount = $discount_amount + $discount_price; // 最终优惠金额
                $this->setOrderPrice($order_info, $final_discount_amount, -4);
            }

            // 商品总额
            $orderItems = $OrderItemsModel->getOrderList($order_id, '', null);

            $goods_total = 0;
            foreach ($orderItems as $item) {
                $goods_total += $item['goods_amount'];
            }

            $this->setOrderPrice($order_info, $goods_total, 1);

            // 重新均摊优惠单价
            $ext_price         = $OrderPriceModel->getExtPrice($order_id); // 附加款
            $order_amount      = $order_info['order_amount'] - $param['PAYAMOUNT']; // 退款后订单总额

            $activity_price    = $OrderPriceModel->getActivityPrice($order_id); // 活动优惠
            $coupon_price      = $OrderPriceModel->getPreferentialPrice($order_id); // 优惠券
            $change_extend_fee = $OrderExtendModel->where(array('order_id' => $order_id))->getField('extend_fee_items');
            $shipping_fee      = $OrderPriceModel->getShippingPrice($order_id); // 运费

            $avg_need_amount = [
                'order_amount'   => $order_amount,
                'coupon_price'   => abs($coupon_price),
                'activity_price' => abs($activity_price),
                'extend_fee'     => abs($ext_price),
                'shipping_price' => $shipping_fee,
            ];

            A('Order/Order')->countItemsPrice($order_id, $avg_need_amount, $orderItems, json_decode($change_extend_fee, true));

            // if ($ext_price > 0) {
            //     $del_extend_fee = $OrderItemsModel->where(array('order_id' => $order_id, 'status' => -1))->sum('extend_price');
            //     $this->dividePrice($order_id, $order_amount, $goods_total, $orderItems, $ext_price, $del_extend_fee);
            // }
            
            // 尾款减款
            $this->setOrderPrice($order_info, '-'.$param['PAYAMOUNT'], -5);

            // 调整订单总额，并重新同步订单
            $order['order_amount'] = $order_amount; 
            $order['erp_order_id'] = '';
            $order['erp_syn']      = 1;
            $updateOrder = $OrderModel->where(['order_id'=>$order_id])->data($order)->save();

            if ($updateOrder === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44004, '更新订单表失败');
            }   

            if ($order_info['order_pay_type'] == 3) {
                $event = '账期订单发起退款申请';
            } else {
                $event = '预付款为0的订单发起退款申请';
            }   

            // 操作日志
            $OrderActionLogModel->addLog($order_id, $operator_id, 2, $event.'，退货原因：'.$refund_reason.'，退货信息：'.$refund_goods_info);

            $OrderModel->commit();

            return $this->apiReturn(0, $event.'成功'); 
        }

        // 请求ERP退款申请接口
        $res = A('Server/Consume')->pushOrderRefundApply($param);

        if ($res === false) {
            return $this->apiReturn(44005, '订单退款申请请求失败');
        }

        // 退款表
        $refund_id = $OrderRefundModel->where(['order_id'=>$order_id, 'refund_type'=>2])->getField('refund_id');

        if ($refund_id) {
            $saveData['pay_amount']    = $all_refund_amount;
            $saveData['price_fall']    = $price_fall;
            $saveData['refund_reason'] = $refund_reason;
            $saveData['update_time']   = time();

            $res = $OrderRefundModel->where(['refund_id' => $refund_id])->data($saveData)->save();

            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44006, '更新订单退款表失败');
            }
        } else {
            $count = $OrderRefundModel->countOrderRefund($order_id);
            
            $addData['order_id']         = $order_id;
            $addData['user_id']          = $order_info['user_id'];
            $addData['order_sn']         = $order_info['order_sn'];
            $addData['refund_sn']        = 'T-'.$order_info['order_sn'].'-'.$count;
            $addData['order_goods_type'] = $order_info['order_goods_type'];
            $addData['currency']         = $order_info['currency'];
            $addData['pay_amount']       = $all_refund_amount;
            $addData['price_fall']       = $price_fall;
            $addData['refund_reason']    = $refund_reason;
            $addData['refund_type']      = 2; // 退货类型
            $addData['create_uid']       = $operator_id; // 操作人
            $addData['create_time']      = time();

            $refund_id = $OrderRefundModel->data($addData)->add();

            if ($refund_id === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44007, '新增订单退款表失败');
            }
        }

        // 退款明细表
        foreach ($refund_info as $k=>$v) {
            $orderItems = $OrderItemsModel->getInfo($k);

            $refund_rec_id = $OrderRefundItemsModel->where(['refund_id'=>$refund_id, 'order_id'=>$order_id, 'rec_id'=>$k])->getField('refund_rec_id');

            if ($refund_rec_id) {
                if (!$v['refund_num']) { // 退款数量为0或空，则删除
                    $OrderRefundItemsModel->where(['refund_rec_id'=>$refund_rec_id])->delete();
                }

                $saveItemsData['single_pre_price'] = $v['single_pre_price'];
                $saveItemsData['refund_num']       = $v['refund_num'];
                $saveItemsData['update_time']      = time();

                $res = $OrderRefundItemsModel->where(['refund_rec_id'=>$refund_rec_id])->data($saveItemsData)->save();

                if ($res === false) {
                    $OrderModel->rollback();
                    return $this->apiReturn(44008, '更新订单退款明细表失败，明细ID：'.$k);
                }
            } else {
                if (!$v['refund_num']) // 过滤退款数量为0或空
                    continue;

                $addItemsData['refund_id']        = $refund_id;
                $addItemsData['order_id']         = $order_id;
                $addItemsData['rec_id']           = $k;
                $addItemsData['goods_id']         = $orderItems['goods_id'];
                $addItemsData['goods_name']       = $orderItems['goods_name'];
                $addItemsData['brand_id']         = $orderItems['brand_id'];
                $addItemsData['brand_name']       = $orderItems['brand_name'];
                $addItemsData['supplier_id']      = $orderItems['supplier_id'];
                $addItemsData['supplier_name']    = $orderItems['supplier_name'];
                $addItemsData['sku_name']         = $orderItems['sku_name'];
                $addItemsData['goods_price']      = $orderItems['goods_price'];
                $addItemsData['single_pre_price'] = $orderItems['single_pre_price'];
                $addItemsData['refund_num']       = $v['refund_num'];
                $addItemsData['create_time']      = time();

                $res = $OrderRefundItemsModel->data($addItemsData)->add();

                if ($res === false) {
                    $OrderModel->rollback();
                    return $this->apiReturn(44009, '新增订单退款明细表失败，明细ID：'.$k);
                }
            }
        }

        // 操作日志
        $refund_amount = price_format($param['PAYAMOUNT'], $order_info['currency']);
        $OrderActionLogModel->addLog($order_id, $operator_id, 2, '后台发起订单退款申请，退款总额：'.$refund_amount.'，退款原因：'.$refund_reason);

        $OrderModel->commit();

        return $this->apiReturn(0, '退货成功');
    }

    // 分摊单价 (原附加费不变的情况下)
    public function dividePrice($order_id, $order_amount, $goods_amount, $items, $extend_fee, $del_extend_fee)
    {
        $sum_item_pay_amount = 0;//累加除最后一行实付金额
        $item_pay_amount = 0;

        $end_key = end(array_keys($items));

        // 订单总额 - 附加费 + 已删除的商品附加费总额
        $pre_amount = $order_amount - $extend_fee + $del_extend_fee;

        foreach ($items as $k => $item) {
            if ($k == $end_key) { //最后一条商品明细
                $single_pre_price = price_format(($item['extend_price'] / $item['goods_number']) + (($pre_amount - $sum_item_pay_amount) / $item['goods_number']), 0, 8); // 将对应供应商附加费分摊到单个商品单价 + 除附加费外单个商品实际单价
            } else {
                $item_pay_amount = ($item['goods_price'] * $item['goods_number'] / $goods_amount) * $pre_amount;
                $single_pre_price = price_format(($item['extend_price'] / $item['goods_number']) + ($item_pay_amount / $item['goods_number']), 0, 8);
            }

            $item['single_pre_price'] = $single_pre_price;

            $map = array(
                        'order_id' => $order_id,
                        'rec_id' => $item['rec_id'],
                    );

            D('OrderItems')->where($map)->save($item);

            $sum_item_pay_amount += $item_pay_amount;
        }
    }

    // 退货退款列表
    public function getRefundOrderList()
    {
        $p = I('p', '');
        $size = I('size', 10);
        $map = I('map', '');
        $OrderRefundModel = D('OrderRefund');

        //  获取订单
        $data = $OrderRefundModel->getRefundList($map, $p, $size);

        return $this->apiReturn(0, '', $data);
    }

    // ERP返回订单退款申请处理结果
    public function orderRefundResultFromErp()
    {
        $order_sn = I('ORDER_SN', 0);
        $result   = I('RESULT', -1);

        if (!$order_sn) return $this->apiReturn(44001, '参数缺失', ['order_sn'=>$order_sn, 'result'=>$result]);

        $OrderModel          = D('Order');
        $OrderPriceModel     = D('OrderPrice');
        $OrderRefundModel    = D('OrderRefund');
        $OrderActionLogModel = D('OrderActionLog');
        $OrderModel->startTrans();

        try {
            $order_info = $OrderModel->getInfo('', $order_sn);
            $order_id = $order_info['order_id'];

            // 退款表
            $refund['status']      = $result == 1 ? 10 : -1;
            $refund['refund_time'] = time();
            $OrderRefundModel->where(['order_id'=>$order_id, 'refund_type'=>2])->data($refund)->save();

            // 若处理成功则更新订单表，价格表新增退款记录
            if ($result == 1) {
                $order_refund = $OrderRefundModel->where(['order_id'=>$order_id, 'refund_type'=>2])->field('pay_amount, price_fall')->find(); 

                // 退款总额 = 退款金额 - 手动差价 
                $pay_amount = price_format($order_refund['pay_amount'] - $order_refund['price_fall'], 0, 2);

                // 更新订单表，重新同步
                $order['order_amount'] = $order_info['order_amount'] - $pay_amount; // 调整订单总额
                $OrderModel->where(['order_id'=>$order_id])->data($order)->save();

                // 退款记录
                $this->setOrderPrice($order_info, '-'.$pay_amount, 4);

                // 待付尾款，添加尾款减款
                if ($order_info['order_pay_type'] == 2 && $order_info['status'] == 3) {
                    $this->setOrderPrice($order_info, '-'.$pay_amount, -5);

                    // 若退款金额超过尾款金额 
                    // if ($pay_amount > $order_info['order_amount'] - $order_info['advance_amount']) {
                    //     // ....
                    // }    
                }

                $res_val = '已处理';
            } else {
                $res_val = '已拒绝';
            }

            // 操作日志
            $OrderActionLogModel->addLog($order_id, 0, 4, '订单退款申请处理结果：'.$res_val);
        } catch (Exception $e) {
            $OrderModel->rollback();
            return $this->apiReturn(44010, '更新失败');
        }

        $OrderModel->commit();

        return $this->apiReturn(0, '更新成功');
    }

    // 设置价格表
    public function setOrderPrice($order_info, $price, $price_type)
    {
        $OrderPriceModel = D('OrderPrice');

        $price_id = $OrderPriceModel->where(['order_id'=>$order_info['order_id'], 'price_type'=>$price_type])->getField('price_id');

        if ($price_id) {
            $savePrice['price']       = $price;
            $savePrice['create_time'] = time();
            $OrderPriceModel->where(['price_id'=>$price_id])->data($savePrice)->save();
        } else {
            $OrderPriceModel->createOrderPrice($order_info['order_id'], $price, $price_type, $order_info['currency'], $order_info['order_sn']);
        }
    }

    // 同步退款状态
    public function sysRefundStatus()
    {
        $refund_sn = I('refund_sn', '');
        $type      = I('type', 1); // 1-退款，2-退货退款

        $path = C('LOG_PATH').ACTION_NAME.'/'.date('y_m_d').'.log'; // 接口日志文件
        \Think\Log::write(json_encode(I('request.')), INFO, '', $path);

        if (!$refund_sn) return $this->apiReturn(44001, '参数缺失');

        $save = [];

        if ($type == 1) {
            $OrderRefundModel = D('OrderRefund');

            $save['is_refund'] = 1;
            $save['status'] = 10;
            $res = $OrderRefundModel->where(['refund_sn' => $refund_sn])->save($save);
        } else {
            $OrderReturnModel = D('OrderReturn');

            $save['is_refund'] = 1;
            $res = $OrderReturnModel->where(['return_sn' => $refund_sn])->save($save);
        }

        if ($res === false) return $this->apiReturn(44002, '同步退款状态失败');

        return $this->apiReturn(0, '同步退款状态成功');
    }

    // 根据售后单审核通过生成退款单或退货单 -- 2020-12-11
    public function apply()
    {
        $service_id  = I('id', 0, 'intval');
        $operator_id = I('operator_id', 0, 'intval');

        if (!$service_id) return $this->apiReturn(44001, '售后ID缺失');

        $OrderModel             = D('Order');
        $OrderItemsModel        = D('OrderItems');
        $OrderExtendModel       = D('OrderExtend');
        $PayLogModel            = D('PayLog');
        $OrderRefundModel       = D('OrderRefund');
        $OrderRefundItemsModel  = D('OrderRefundItems');
        $OrderRefundLogModel    = D('OrderRefundLog');
        $OrderServiceModel      = D('OrderService');
        $OrderServiceItemsModel = D('OrderServiceItems');

        $order_service       = $OrderServiceModel->getInfo($service_id);
        $order_service_items = $OrderServiceItemsModel->getServiceItems($service_id);  
        $order_extend        = $OrderExtendModel->getInfo($order_service['order_id']);      

        $refund_amount = 0; // 退款总额
        $erp_params    = []; // 推送ERP参数

        $erp_params['SERVICE_SN'] = $order_service['service_sn']; // 售后单号
        $erp_params['TYPE']       = $order_service['service_type']; // 1-退款，2-退货
        $erp_params['FNUMBER']    = $order_extend['erp_sn']; // ERP单号

        $is_self_exists = false; // 售后明细中是否存在自营商品
        foreach ($order_service_items as $v) {
            $goods_amount = $v['adjust_price'] * $v['adjust_number'];
            $refund_amount += $goods_amount;

            if ($v['order_goods_type'] == 2) $is_self_exists = true;

            if ($v['adjust_number']) {
                $tmp = [];
                $tmp['FENTRYID'] = $v['erp_rec_id'];
                $tmp['FQTY']     = $v['adjust_number'];

                $erp_params['ENTRY'][] = $tmp;
            }
        }  

        $self_order_id = 0;

        if ($order_extend['erp_sn']) {
            $map = [];
            $map['o.order_goods_type'] = 2;
            $map['o.status']           = ['neq', -1];
            $map['oe.erp_sn']          = $order_extend['erp_sn'];

            $self_order_id = $OrderModel->alias('o')
                            ->join('LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id')
                            ->where($map)
                            ->getField('o.order_id');
        }

        if ($order_service['service_type'] == 1) { // 未出库退款 
            if ($is_self_exists && $self_order_id) { // 若自营商品存在，且存在自营单，则需取消自营订单，另ERP需重新推送自营单
                $post = [];
                $post['order_id']      = $self_order_id;
                $post['user_id']       = $order_service['user_id'];
                $post['cancel_reason'] = '联营售后存在自营商品，取消自营单';
                $post['operator_id']   = $operator_id;
                $post['type']          = 5;

                $post = array_merge($post, authkey());

                $res = post_curl(API_DOMAIN.'/order/cancel', $post);
                if (!$res) return $this->apiReturn(44006, '售后取消自营单失败');

                $res = json_decode($res, true);
                if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);     

                // $res = A('Order/Order')->cancelSelfLock($self_order_id);
                // if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);
            }

            $order_info       = $OrderModel->getInfo($order_service['order_id']);
            $order_items_info = $OrderItemsModel->getOrderList($order_service['order_id'], '', null);       
           
            // $is_sys = $order_service['refund_type'] == 1 ? 1 : 0; // 是否需要原路返回
            $is_sys = 0;  // 临时注释上一行，后续会开启原路返回 20210308

            if ($order_info['order_pay_type'] == 2) { // 预付款
            	$all_pay_log = $PayLogModel->getInfo($order_service['order_id']);
            	
                $pay_log = count($all_pay_log) > 1 ? $all_pay_log[1] :  $all_pay_log[0];  
            } else {
                $pay_log = $PayLogModel->getInfo($order_service['order_id'], '*', true);
            } 
        
            $all_service_items = $OrderServiceItemsModel->getAllServiceItems($order_service['order_id'], 3);

            $service_number = []; // 订单商品售后总数量 
            if (!empty($all_service_items)) {
                foreach ($all_service_items as $v) {
                    $service_number[$v['rec_id']] += $v['adjust_number'];
                }
            }

            $is_cancel_order = true; // 若售后数量与原数量全部一致，则取消订单
            if ($order_info['status'] != -1) { // 订单未取消
                foreach ($order_items_info as $v) {
                    if (empty($service_number[$v['rec_id']]) || ($service_number[$v['rec_id']] != $v['goods_number'])) {
                        $is_cancel_order = false;
                    }
                } 
            } else {
                $is_cancel_order = false;
            }

            $OrderModel->startTrans();

            $refund_info = [];
        
            $count = $OrderRefundModel->countOrderRefund($order_service['order_id']); // 退款单数量
                
            $refund_info['refund_sn']        = 'T-'.$order_info['order_sn'].'-'.$count;
            $refund_info['order_id']         = $order_service['order_id'];
            $refund_info['order_sn']         = $order_info['order_sn'];
            $refund_info['user_id']          = $order_info['user_id'];
            $refund_info['order_goods_type'] = $order_info['order_goods_type'];
            $refund_info['currency']         = $order_info['currency'];
            $refund_info['pay_log_id']       = $pay_log['pay_log_id'];
            $refund_info['serial_number']    = $pay_log['serial_number'];
            $refund_info['pay_name']         = $pay_log['pay_name'];
            $refund_info['pay_amount']       = number_format($refund_amount, 2, '.', '');
            $refund_info['refund_reason']    = '售后退款';
            $refund_info['create_uid']       = $operator_id; // 操作人
            $refund_info['is_sys']           = $is_sys;
            $refund_info['create_time']      = time();

            $refund_id = $OrderRefundModel->data($refund_info)->add();
            if ($refund_id === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44007, '新增订单退款表失败');
            }

            // 生成退款明细
            $refund_items_info = [];
            foreach ($order_service_items as $v) {
                $addItemsData = [];

                $addItemsData['refund_id']        = $refund_id;
                $addItemsData['order_id']         = $order_service['order_id'];
                $addItemsData['rec_id']           = $v['rec_id'];
                $addItemsData['goods_id']         = $v['goods_id'];
                $addItemsData['goods_name']       = $v['goods_name'];
                $addItemsData['sku_name']         = $v['sku_name'];
                $addItemsData['brand_id']         = $v['brand_id'];
                $addItemsData['brand_name']       = $v['brand_name'];
                $addItemsData['supplier_id']      = $v['supplier_id'];
                $addItemsData['supplier_name']    = $v['supplier_name'];
                $addItemsData['goods_price']      = $v['goods_price'];
                $addItemsData['single_pre_price'] = $v['single_pre_price'];
                $addItemsData['refund_num']       = $v['adjust_number'];
                $addItemsData['create_time']      = time();

                $refund_items_info[] = $addItemsData;
            }

            $res = $OrderRefundItemsModel->addAll($refund_items_info);
            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44008, '新增订单退款明细表失败');
            }

            // 非原路返回 生成退款日志
            if ($is_sys == 0) {
                $refund_log_info = [];
                $refund_log_info['refund_id']     = $refund_id;
                $refund_log_info['order_id']      = $order_service['order_id'];
                $refund_log_info['pay_log_id']    = $pay_log['pay_log_id'];
                $refund_log_info['serial_number'] = $pay_log['serial_number'];
                $refund_log_info['pay_amount']    = number_format($refund_amount, 2);
                $refund_log_info['refund_status'] = 2; // 财务ERP退款

                $res = $OrderRefundLogModel->add($refund_log_info);
                if ($res === false) {
                    $OrderModel->rollback();
                    return $this->apiReturn(44009, '新增订单退款日志表失败');
                }
            }

            // 回写退款单号到售后单
            $service_update = [];
            $service_update['refund_id']     = $refund_id;
            $service_update['refund_sn']     = $refund_info['refund_sn'];
            $service_update['refund_status'] = 2;
            $res = $OrderServiceModel->where(['id' => $service_id])->save($service_update);
            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44010, '更新售后单失败');
            }

            if ($is_cancel_order) { // 取消订单
                $post = [];
                $post['order_id']      = $order_service['order_id'];
                $post['user_id']       = $order_service['user_id'];
                $post['cancel_reason'] = '整单售后取消';
                $post['operator_id']   = $operator_id;
                $post['type']          = 5;

                $post = array_merge($post, authkey());

                $res = post_curl(API_DOMAIN.'/order/cancel', $post);
                if (!empty($res)) {
                    $res = json_decode($res, true);

                    if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
            }

            $OrderModel->commit();
        } 

        // 已出库退货：若存在自营商品、自营单，且存在自营出库单，则需生成自营订单的退货申请
        if ($order_service['service_type'] == 2 && $is_self_exists && $self_order_id) { 
            $RemovalItemsModel = D('RemovalItems');
            $removal_info = $RemovalItemsModel->getInfo($self_order_id);

            if ($removal_info) {
                $UserMainModel         = D('Home/UserMain');
                $RemovalModel          = D('Removal');
                $OrderReturnModel      = D('OrderReturn');
                $OrderReturnItemsModel = D('OrderReturnItems');
                $OrderReturnLogModel   = D('OrderReturnLog');
                $OrderActionLogModel   = D('OrderActionLog');

                $order_info = $OrderModel->getInfo($self_order_id);
                $user_info  = $UserMainModel->getUserInfo($order_info['user_id']);
                $count      = $OrderReturnModel->countReturn($self_order_id);
                $return_sn  = 'K-'.$order_info['order_sn'].'-'.$count; // 生成退货单号
                $removal_sn = $RemovalModel->where(['order_id' => $self_order_id])->getField('removal_sn'); // 获取出库单号
                
                $OrderReturnModel->startTrans();

                // 退货单
                $return_info['return_sn']        = $return_sn;
                $return_info['removal_sn']       = $removal_sn;
                $return_info['order_id']         = $self_order_id;
                $return_info['order_sn']         = $order_info['order_sn'];
                $return_info['user_id']          = $order_info['user_id'];
                $return_info['order_goods_type'] = 2;
                $return_info['currency']         = $order_info['currency'];
                $return_info['mobile']           = $user_info['mobile'];
                $return_info['email']            = $user_info['email'];
                $return_info['company_name']     = '';
                $return_info['pay_amount']       = $order_info['order_amount'];
                $return_info['return_way']       = 2;
                $return_info['return_amount']    = number_format($refund_amount, 2);
                $return_info['return_reason']    = '售后审核通过，自动生成自营退货单';
                $return_info['syn_sign']         = 1;

                if ($order_service['refund_type'] == 3) {
                    $return_info['bank_account']  = $order_service['refund_account'];
                    $return_info['bank_name']     = $order_service['bank_name'];
                    $return_info['bank_sub_name'] = $order_service['bank_sub_name'];
                    $return_info['bank_user']     = $order_service['customer_name'];
                }
                
                $return_info['status']           = 7;   
                $return_info['create_uid']       = $operator_id;
                $return_info['create_time']      = time(); 
                 
                $return_id = $OrderReturnModel->add($return_info);

                // 退货单明细
                foreach ($order_service_items as $k=>$v) {
                    $add_return_items = [];

                    $add_return_items[$k]['return_id']     = $return_id;
                    $add_return_items[$k]['return_sn']     = $return_sn;
                    $add_return_items[$k]['order_id']      = $v['order_id'];
                    $add_return_items[$k]['rec_id']        = $v['rec_id'];
                    $add_return_items[$k]['goods_id']      = $v['goods_id'];
                    $add_return_items[$k]['goods_name']    = $v['goods_name'];
                    $add_return_items[$k]['sku_name']      = $v['sku_name'];
                    $add_return_items[$k]['supplier_id']   = $v['supplier_id'];
                    $add_return_items[$k]['supplier_name'] = $v['supplier_name'];
                    $add_return_items[$k]['brand_id']      = $v['brand_id'];
                    $add_return_items[$k]['brand_name']    = $v['brand_name'];
                    $add_return_items[$k]['cost_price']    = 1000; // 默认采购成本1000 --- 桥均定     
                    $add_return_items[$k]['single_price']  = $v['single_pre_price'];
                    $add_return_items[$k]['return_num']    = $v['adjust_number'];
                }

                $OrderReturnItemsModel->addAll($add_return_items);

                // 退货记录
                $OrderReturnLogModel->addLog($return_id, $operator_id, '售后审核通过，自动生成自营退货单');
                $OrderActionLogModel->addLog($self_order_id, $operator_id, 2, '售后审核通过，自动生成自营退货单');

                $OrderReturnModel->commit();
            }
        }

        /* 将售后明细推送到ERP */ 
        $res = A('Server/Consume')->pushOrderServiceItems($erp_params);

        $path = C('LOG_PATH').ACTION_NAME.'/'.date('y_m_d').'.log'; // 接口日志文件
        \Think\Log::write('调用ERP售后接口，参数：'.json_encode($erp_params).'，ERP返回结果：'.$res, INFO, '', $path);

        if ($res !== true) return $this->apiReturn(44018, '推送售后明细到ERP失败，原因：'.$res);
        /*  end   */ 

        $create_sn = $order_service['service_type'] == 1 ? $refund_info['refund_sn'] : '';

        return $this->apiReturn(0, '成功', $create_sn);
    }

    // 确认退款完成
    public function confirmRefund()
    {
        $id = I('id', 0, 'intval'); // 售后ID
        $operator_id = I('operator_id', 0, 'intval'); // 操作人ID

        $OrderModel             = D('Order');
        $OrderItemsModel        = D('OrderItems');
        $OrderServiceModel      = D('OrderService');
        $OrderServiceItemsModel = D('OrderServiceItems');
        $OrderServiceLogModel   = D('OrderServiceLog');
        $OrderRefundModel       = D('OrderRefund');
        $OrderRefundLogModel    = D('OrderRefundLog');
        $OrderActionLogModel    = D('OrderActionLog');

        $order_service = $OrderServiceModel->getInfo($id);
        if (!$order_service) return $this->apiReturn(44030, '售后单不存在');

        $all_service_items = $OrderServiceItemsModel->getAllServiceItems($order_service['order_id'], 3);

        $service_number = []; // 订单商品售后总数量 
        $push_tracking_data = []; // 推送明细生成跟踪信息
        if (!empty($all_service_items)) {
            $operator_name = D('Order/Cms')->getUserName($operator_id); // 操作人名称 

            foreach ($all_service_items as $v) {
                $service_number[$v['rec_id']] += $v['adjust_number'];

                $temp = [];
                $temp['RBMQTYPE']     = 'SCZZ';
                $temp['BIZDATE']      = date('Y-m-d H:i:s', time());
                $temp['MSG']          = '售后退款成功';
                $temp['CREATOR']      = $operator_name;
                $temp['ORDERENTRYID'] = $v['erp_rec_id'];
                $temp['ORDERID']      = $v['order_id'];
                $temp['STATUS']       = 134; // 生产跟踪状态

                $push_tracking_data[] = $temp;

            }
        }

        $order_info       = $OrderModel->getInfo($order_service['order_id']);
        $order_items_info = $OrderItemsModel->getOrderList($order_service['order_id'], '', null); 

        $is_cancel_order = true; // 若售后数量与原数量全部一致，则取消订单
        $cancel_items = []; // 若售后数量与原数量全部一致，则取消订单对应明细
        if ($order_info['status'] != -1) { // 订单未取消
            foreach ($order_items_info as $v) {
                if (empty($service_number[$v['rec_id']]) || ($service_number[$v['rec_id']] != $v['goods_number'])) {
                    $is_cancel_order = false;
                    continue;
                }

                $cancel_items[] = $v['rec_id'];
            } 
        } else {
            $is_cancel_order = false;
        }

        $OrderModel->startTrans();

        $update_service = [];
        $update_service['refund_status'] = 3;
        $update_service['update_time'] = time();
        
        $res = $OrderServiceModel->where(['id' => $id])->save($update_service);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44031, '更新售后退款状态失败');
        }

        $res = $OrderServiceLogModel->addLog($id, $operator_id, '确认退款');
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44032, '添加售后日志失败');
        }

        // 取消订单明细
        if (!empty($cancel_items)) {
            $items_map = [];
            $items_map['rec_id'] = ['in', $cancel_items];

            $res = $OrderItemsModel->where($items_map)->save(['status' => -1]);
            if ($res === false) {
                $OrderModel->rollback();
                return $this->apiReturn(44033, '更新订单明细失败');
            }
        }

        $update = [];
        $update['is_refund']   = 1;
        $update['status']      = 10;
        $update['is_sys']      = 2; // 同步财务
        $update['refund_time'] = time();

        $res = $OrderRefundModel->where(['refund_id' => $order_service['refund_id']])->save($update);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44034, '更新退款表失败');
        }

        $update_log = [];
        $update_log['refund_status'] = 10;
        $update_log['refund_time'] = time();

        $res = $OrderRefundLogModel->where(['refund_id' => $order_service['refund_id']])->save($update_log);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(-1, '更新退款日志失败');
        }

        $OrderActionLogModel->addLog($order_service['order_id'], $operator_id, 2, '售后确认退款');

        if ($is_cancel_order && $order_info['status'] < 7) { // 未出库的需要取消订单
            $post = [];
            $post['order_id']      = $order_service['order_id'];
            $post['user_id']       = $order_service['user_id'];
            $post['cancel_reason'] = '整单售后取消';
            $post['operator_id']   = $operator_id;
            $post['type']          = 5;

            $post = array_merge($post, authkey());

            $res = post_curl(API_DOMAIN.'/order/cancel', $post);
            if (!empty($res)) {
                $res = json_decode($res, true);

                if ($res['err_code'] != 0) {
                    $OrderModel->rollback();
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
            }
        }

        $OrderModel->commit();

        $push_tracking_data && $this->pushProductTracking($push_tracking_data); // 推送生产跟踪信息

        return $this->apiReturn(0, '成功');
    }


}
