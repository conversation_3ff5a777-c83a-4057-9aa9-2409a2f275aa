<?php
namespace Order\Controller;

use Order\Controller\BaseController;

class RemovalController extends BaseController
{
    // 获取未发货明细，用于订单系统退货退款
    public function getUnshippedItems()
    {
        $order_id = I('order_id', 0);

        if (!$order_id) return $this->apiReturn(44001, '参数缺失');

        $OrderItemsModel = D('OrderItems');
        $RemovalItemsModel = D('RemovalItems');

        // 获取订单明细
        $orderItems = $OrderItemsModel->getOrderList($order_id, '', null);
        
        // 出库订单明细
        $removalItems = $RemovalItemsModel->getInfo($order_id);

        if (!$removalItems) return $this->apiReturn(0, '', $orderItems);

        $items = []; // 出库明细 (rec_id => removal_number)

        $items = array_column($removalItems, 'removal_number', 'rec_id');

        // 未出库或部分出库明细
        foreach ($orderItems as $k => &$v) {
            // 出库表存在该明细
            if (isset($items[$v['rec_id']])) {  
                if ($items[$v['rec_id']] == $v['goods_number']) { // 出库数量与购买数量相等，则删除该条明细
                    unset($orderItems[$k]);
                } else if ($items[$v['rec_id']] < $v['goods_number']) { // 未发货数量 = 购买数量 - 出库数量
                    $orderItems[$k]['goods_number'] = $v['goods_number'] - $items[$v['rec_id']];
                }     
            }
        }

        return $this->apiReturn(0, '', $orderItems);
    }

    // 获取已出库明细
    public function getRemovalItems()
    {
        $order_id = I('order_id', 0);

        if (!$order_id) return $this->apiReturn(44001, '参数缺失');

        $OrderModel            = D('Order');
        $OrderItemsModel       = D('OrderItems');
        $OrderPriceModel       = D('OrderPrice');
        $OrderExtendModel      = D('OrderExtend');
        $RemovalItemsModel     = D('RemovalItems');
        $OrderReturnItemsModel = D('OrderReturnItems');

        // 出库订单明细
        $removalItems = $RemovalItemsModel->getInfo($order_id);

        if (!$removalItems) return $this->apiReturn(44002, '无出库数据');

        // $order_amount   = $OrderModel->where(['order_id' => $order_id])->getField('order_amount'); // 订单总额
        // $pay_amount     = $OrderPriceModel->getPayPreferential($order_id); // 支付优惠
        // $last_paid      = $order_amount + $pay_amount; // 最终支付金额
        // $orderItemsList = $OrderItemsModel->getOrderList($order_id, '', null);
        // $goods_total    = $OrderPriceModel->getGoodsPrice($order_id); // 商品总额
        // $ext_price      = $OrderPriceModel->getExtPrice($order_id); // 附加费
        // $del_extend_fee = $OrderItemsModel->where(array('order_id' => $order_id, 'status' => -1))->sum('extend_price'); // 删除商品的附加费

        // $items = $this->dividePrice($order_id, $last_paid, $goods_total, $orderItemsList, $ext_price, $del_extend_fee);

        // 重新均摊优惠单价
        $orderItemsList         = $OrderItemsModel->getOrderList($order_id, '', null);
        $order_amount           = $OrderModel->where(['order_id' => $order_id])->getField('order_amount'); // 订单总额
        $pay_preferential_price = $OrderPriceModel->getPayPreferential($order_id); // 支付优惠
        
        $ext_price         = $OrderPriceModel->getExtPrice($order_id); // 附加款
        $activity_price    = $OrderPriceModel->getActivityPrice($order_id); // 活动优惠
        $coupon_price      = $OrderPriceModel->getPreferentialPrice($order_id); // 优惠券
        $change_extend_fee = $OrderExtendModel->where(array('order_id' => $order_id))->getField('extend_fee_items');
        $shipping_fee      = $OrderPriceModel->getShippingPrice($order_id); // 运费

        $avg_need_amount = [
            'order_amount'           => $order_amount,
            'coupon_price'           => abs($coupon_price),
            'activity_price'         => abs($activity_price),
            'extend_fee'             => abs($ext_price),
            'pay_preferential_price' => abs($pay_preferential_price),
            'shipping_price'         => $shipping_fee,
        ];

        $items = $this->countItemsPrice($order_id, $avg_need_amount, $orderItemsList, json_decode($change_extend_fee, true));

        foreach ($removalItems as &$v) {
            $orderItems = $OrderItemsModel->where(['rec_id'=>$v['rec_id']])->field('sku_name, single_pre_price')->find();

            $v['sku_name']           = $orderItems['sku_name'];
            $v['single_pre_price']   = $items[$v['rec_id']];
            $v['already_return_num'] = $OrderReturnItemsModel->where(['rec_id'=>$v['rec_id']])->sum('return_num'); // 获取已申请退货的型号数量
        }

        return $this->apiReturn(0, '', $removalItems);
    }

    // 分摊单价
    // public function dividePrice($order_id, $last_paid, $goods_amount, $items, $extend_fee, $del_extend_fee)
    // {
    //     $sum_item_pay_amount = 0;//累加除最后一行实付金额
    //     $item_pay_amount = 0;

    //     $end_key = end(array_keys($items));

    //     // 支付金额 - 附加费 + 已删除的商品附加费总额
    //     $pre_amount = $last_paid - $extend_fee + $del_extend_fee;

    //     $datas = array();

    //     foreach ($items as $k => $item) {
    //         if ($k == $end_key) { //最后一条商品明细
    //             $single_pre_price = price_format(($item['extend_price'] / $item['goods_number']) + (($pre_amount - $sum_item_pay_amount) / $item['goods_number']), 0, 8); // 将对应供应商附加费分摊到单个商品单价 + 除附加费外单个商品实际单价
    //         } else {
    //             $item_pay_amount = ($item['goods_price'] * $item['goods_number'] / $goods_amount) * $pre_amount;
    //             $single_pre_price = price_format(($item['extend_price'] / $item['goods_number']) + ($item_pay_amount / $item['goods_number']), 0, 8);
    //         }

    //         $datas[$item['rec_id']] = $single_pre_price;

    //         $sum_item_pay_amount += $item_pay_amount;
    //     }

    //     return $datas;
    // }

    /**
     * 计算每条明细商品优惠单价
     * @param  [type] $order_id     [订单ID]
     * @param  array  $amount       [各项金额]
     *                order_amount  [订单总额]
     *                coupon_amount [优惠券]
     *                extend_fee    [附加费]
     * @param  [type] $items        [商品明细]
     * @param  array  $extend_items [附加费明细]
     * @return [type]               [description]
     */
    public function countItemsPrice($order_id, $amount,  $items, $extend_items = [])
    {
        // 重新均摊优惠单价
        $datas['list']                   = $items;
        $datas['extend_items']           = $extend_items;
        $datas['extend_fee']             = $amount['extend_fee'];
        $datas['coupon_price']           = abs($amount['coupon_price']);
        $datas['activity_price']         = abs($amount['activity_price']);
        $datas['order_amount']           = $amount['order_amount'];
        $datas['shipping_price']         = $amount['shipping_price'];
        $datas['pay_preferential_price'] = $amount['pay_preferential_price'];

        $res = A('Order/Order')->getAvgPriceBywallt($datas);

        $list = array();

        foreach ($res['list'] as $k => $item) {
            $list[$item['rec_id']] = $item['single_pre_price'];
        }

        return $list;
    }
    
}