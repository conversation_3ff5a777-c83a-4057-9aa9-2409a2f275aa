<?php
namespace Order\Controller;

use Order\Controller\BaseController;
use Order\Service\CartService;
use Order\Service\OpenService;
use Home\Services\InquiryService;

class CartController extends BaseController
{
    use \Order\Traits\CartTrait;
    public static $QING_KU_CUN_ACTYPE = 6;//清库存的商品
    public $currencyTOsuppfee = [1=>"cn",2=>"hk"];
    public function _initialize()
    {
        parent::_initialize();
        //购物车特殊，不管是否跳过，必须先校验。
        if (!in_array(strtolower(ACTION_NAME),["updatebatch", 'exportitems',"setCount_init"])){
            $this->login = $this->checkLogin();
            $res = $this->authRule(array('lists', 'add', 'change', 'count', 'allcount', 'changenum', 'delete', 'binduseridsign', 'selfaddress',
                'setcount','getconfirmgoods', 'addbyordersystem', 'listsbyordersystem', 'changenumbyordersystem', 'deletebyordersystem',
                'confirmbyordersystem', 'createbyordersystem', 'getexpressfeebyorder', 'addbatch', 'updatebatch', 'usercanac'));
            if (!$res && (empty($this->login) || $this->login['err_code'] != 0)) {
                return $this->apiReturn($this->login['err_code'], $this->login['err_msg']);
            }
        }

    }


    /**
     * 获取购物车的用户ID
     * @return [type] [description]
     */
    private function getCartUid($uid = null)
    {
        if (is_null($uid)) {
            if ($this->auth()) {
                $uid = I('request.uid', 0, 'intval');
            }

            if (empty($uid)) {
                $uid = cookie('uid');
            }
        }
        if (!$this->auth() && $this->login['err_code'] != 0) {//弱登录态下购物车数据不属于当前用户账号的
            $uid = 0;
        }
        return $uid;
    }

    /**
     * 获取订单来源
     * @return [type] [description]
     */
    public function order_source($arr = array())
    {
        $pf = platform();
        $url_arr = parse_url($_SERVER['HTTP_REFERER']);
        parse_str($url_arr['query'], $param_arr);
        $adtag = adtag();
        $ptag = ptag();
        $search = page_param('search', 'k');
        if (!empty($adtag)) {
            $param_arr['adtag'] = $adtag;
        }
        if (!empty($ptag)) {
            $param_arr['ptag'] = $ptag;
        }
        if (!empty($search)) {
            $param_arr = array_merge($param_arr, $search);
        }
        $param['pf'] = $pf;
        $param = array_merge($param, $param_arr, $arr);
        foreach ($param as $k => $v) {
            if (empty($v)) {
                unset($param[$k]);
            }
        }
        $param = str_replace('&', ',', urldecode(http_build_query($param)));
        return $param;
    }

    /**
     * 绑定用户ID与GID
     * @return [type] [description]
     */
    public function bindUserIdSign($gid = '', $uid = '')
    {

        $res = true;
        if (!empty($gid) && !empty($uid)) {

            $res = CartService::bindUserIdSign($gid,$uid);
            // $ShoppingCartModel = D('ShoppingCart');
            // $map = array(
            //     'user_sign' => $gid,
            // );
            // $save = array(
            //     'user_id' => $uid,
            // );
            // $res = $ShoppingCartModel->where($map)->save($save);
        }
        return $res;
    }

    /**
     * 合并购物车相同商品
     * @param  [type] $gid [description]
     * @param  [type] $uid [description]
     * @return [type]      [description]
     */
    public function mergeCart($gid = '', $uid = '')
    {
        empty($gid) && $gid = cookie('gid');
        empty($uid) && $uid = $this->getCartUid();
        if (!empty($gid) && !empty($uid)) {
            $ShoppingCartModel = D('ShoppingCart');
            //先绑定现有id、gid
            $res = $this->bindUserIdSign($gid, $uid);
            if ($res === false) {
                return $this->apiReturn(24012, '关联绑定购物车失败');
            }
            //获取uid和gid重复商品
            $list = $ShoppingCartModel->getRepeatGoods($uid, $gid);
            foreach ($list as &$v) {
                // $res = $ShoppingCartModel->delete($v['c1_cart_id']);
                $res = CartService::delCartByCartId($v['c1_cart_id']);
                if ($res === false) {
                    $this->pushReportMonitorLog(
                        [
                            "interface_type"=>"3",
                            "err_msg"=>"购物车商品删除失败",
                            "err_code"=>"24011",
                            "remark"=>sprintf("合并购物车相同商品 gid=%s",$gid),
                        ]
                    );
                    return $this->apiReturn(24011, '购物车商品删除失败');
                }
                if ($v['c1_update_time'] > $v['c2_update_time']) {
                    $data = json_decode($v['c1_goods_data'], true);
                } else {
                    $data = json_decode($v['c2_goods_data'], true);
                }
                $number = $v['c1_goods_number'] + $v['c2_goods_number'];
                $sale_type = 1;
                if ($data['goods_number'] < $number) {
                    $number = $data['goods_number'];
//                    if (!in_array($data['goods_type'], array(1,2))) {//自营
//                        $sale_type = 2;
//                    } else {//联营
//                        $number = $data['goods_number'];
//                    }
                }
                $save = array(
                    'cart_id' => $v['c2_cart_id'],
                    'user_sign' => '',//$gid
                    'sale_type' => $sale_type,
                    'goods_number' => $number,
                );
                // $res = $ShoppingCartModel->save($save);
                $res = CartService::updateCart($save);
                if ($res === false) {
                    $this->pushReportMonitorLog(
                        [
                            "interface_type"=>"3",
                            "err_msg"=>"购物车合并商品失败",
                            "err_code"=>"24010",
                            "remark"=>sprintf("gid是%s,新增购物车参数:%s",$gid,json_encode($save))
                        ]
                    );
                    return $this->apiReturn(24010, '购物车合并商品失败');
                }
                $this->update($v['c2_cart_id']);
            }
            //后续清空gid购物车
            try {
                $ShoppingCartModel->clearUserSign($gid);
            } catch (Exception $e) {
            }
        }
        $this->setCount();//统计uid的购物车
        $this->setCount(0);//统计gid的购物车
        return $this->apiReturn(0, '合并成功');
    }

    /**
     * 判断购物车商品是否失效
     * @param  integer  id      商品ID
     * @param  string   goods   商品接口返回信息
     * @return boolean [description]
     */
    public function isFaild($goods = '')
    {
        $goods_id = I('request.id', '');
        if (empty($goods) && empty($goods_id)) {
            return $this->apiReturn(24005, '未找到相关商品信息');
        } elseif (empty($goods) && !empty($goods_id)) {
            $res = $this->getGoods($goods_id);
            if ($res['err_code'] == 80003) {
                return $this->apiReturn(24014, '购物车当前商品已失效');
            }
            $goods = $res['data'];
        }
        //2017.11.30 产品提出库存1以下就不能购买 => 2018.3.22业务提出数量为1也能购买
        if (empty($goods['tiered']) || $goods['is_buy'] != 1) {
            return $this->apiReturn(24014, '购物车当前商品已失效');
        }
        return $this->apiReturn(0, '');
    }

    /**
     * 异步更新用户购物车数据  (将update拆分updateBatch和updateUserCart)
     * @return [type] [description]
     */
    public function updateUserCart()
    {
        // if (!$this->auth()) {
        //     exit();
        // }
        $user_id = cookie('uid');
        $ShoppingCartModel = D('ShoppingCart');
        $RbmqModel = D('Common/Rbmq');
        $map = array(
            'user_id' => $user_id,
        );
        // $list = $ShoppingCartModel->where($map)->getField('goods_id', true);
        $list = CartService::getUserCartGoodsIdArr($user_id);
        foreach ($list as $v) {
            $RbmqModel->queue(C('QUEUE_SHOPPING_CART'))->push($v, C('QUEUE_SHOPPING_CART'));
        }
        return $this->apiReturn(0, '成功');
    }

    /**
     * 批量更新购物车  (将update拆分updateBatch和updateUserCart)
     * @return [type] [description]
     */
    public function updateBatch()
    {
        // if (!$this->auth()) {
        //     exit();
        // }
        $data = I('request.sku_id');
        if (empty($data)) {
            return $this->apiReturn(24023, '无有效数据');
        }
        if (is_string($data)) {
            $data = explode(',', $data);
        }
        $ShoppingCartModel = D('ShoppingCart');
        //过滤无效sku_id
        $goods_ids = array_filter($data, function($v){return !empty($v);});
        if (empty($goods_ids)) {
            return $this->apiReturn(24005, '无有效sku');
        }
        //批量获取信息
        $goods = $this->getGoodsArr($goods_ids);
//        dump($goods);exit;
        if ($goods['err_code'] != 0) {
            return $this->apiReturn($goods['err_code'], $goods['err_msg']);
        }
        $goods = $goods['data'];
        $fail_ids = array();
        $success = 0;
        $now = time();
        $map = array(
            'goods_id_arr' => $goods_ids,
            'type' => 1,//只更新前台客户
            // 'update_time' => array('lt', $now-30),//更新时间超30秒的更新数据
            'update_time_lt' => $now-30,//更新时间超30秒的更新数据
        );
        // $list = $ShoppingCartModel->getList($map, null, 'update_time', 'user_id, user_sign, cart_id, goods_id, currency, goods_number, status');

        $list = CartService::getCartList($map);
        foreach ($list as $v) {
            $goods_id = $v['goods_id'];
            if (!isset($goods[$goods_id])) {
                $data = array(
                    'status' => -1,
                    'update_time' => $now,
                );

            } elseif ($goods[$goods_id] === false) {//基石已删除商品
                $data = array();
                // $ShoppingCartModel->delete($v['cart_id']);
                CartService::delCartByCartId($v['cart_id']);

            } else {
                $isFaild = $this->isFaild($goods[$goods_id]);
                if ($isFaild['err_code'] != 0) {
                    //过期下架、库存0下架、无价格阶梯下架
                    $data = array(
                        'status' => -1,
                        'goods_data' => json_encode($goods[$goods_id]),
                        'update_time' => $now,
                    );

                } else {
                    $data = array(
                        'goods_data' => json_encode($goods[$goods_id]),
                        'update_time' => $now,
                    );

                    $use_ac = $this->isCanAc($v['user_id']);
                    //查询指定数量价格信息
                    $res = $this->getFinalGoods($goods_id, $v['goods_number'], $v['currency'], $use_ac);
//                    dump($res);
                    if ($res['err_code'] !== 0) {
                        continue;
                    }
                    $sale_type = $res['data']['enough_stock'] ? 1 : 2;
                    $goods_prices = ladder_final_price($res['data'], $v['currency'], $res['data']['goods_info']['ac_type']);

                    if ($goods_prices['price'] <= 0) {//根据阶梯计算出单价依然为0的计入失效
                        $data['status'] = -1;
                    } else {
                        $data['goods_price'] = $goods_prices['price'];
                        $data['status'] = 1;
                    }
                    //是否预售判断
                    $data['sale_type'] = $sale_type;
                }
            }
            if (!empty($data)) {
                // $where = array(
                //     'cart_id' => $v['cart_id']
                // );
                // $ShoppingCartModel->where($where)->save($data);
                if(empty($data["cart_id"])){
                    $data["cart_id"] = $v['cart_id'];
                }
                CartService::updateCart($data);
            }
        }
        return $this->apiReturn(0);
    }

    /**
     * 更新购物车数据
     * @param  integer  cart_id     购物车ID（可选）
     * @param integer $isGetMemberPrice  是否获取真正的会员价
     * @return [type] [description]
     */
    public function update($cart_id = 0,$isGetMemberPrice=false)
    {
        $gid = cookie('gid');
        $uid = $this->getCartUid();
        empty($cart_id) && $cart_id = I('request.cart_id', 0, 'intval');
        $ShoppingCartModel = D('ShoppingCart');
        $map = array(
            // 'status' => 1,
//            'update_time' => array('lt', time()-30),//更新时间超30秒的更新数据
        );
        if (!empty($cart_id)) {
            $map['cart_id'] = $cart_id;
        } elseif (!empty($uid)) {
            $map['user_id'] = $uid;
        } else {
            $map['gid'] = $gid;
        }
        // $list = $ShoppingCartModel->getList($map, null, 'update_time', 'cart_id, goods_id, currency, goods_number, goods_type, is_gift, update_time');
        $list = CartService::getCartList($map, null, 'update_time');
        foreach ($list as &$v) {
            //联营数据 超2小时更新一次
            if (in_array($v['goods_type'], C('ORDER_GOODS_TYPE_MAP.1')) && $v['update_time'] <= $_SERVER['REQUEST_TIME'] - 7200) {
                continue;
            }
            $use_ac = $this->isCanAc($uid);
            $power = $use_ac;

            if($isGetMemberPrice){
                $userTax = $this->getUserTax($uid);
                $power["verify_blacklist"] = true;
                $power = array_merge($power,$userTax["data"]);
                $isGetMemberPrice = $userTax["err_code"] != 0 ? false : true;

            }

            if (!$v['goods_id']) {
                continue;
            }

            //获取商品
            $res = $this->getFinalGoods($v['goods_id'], $v['goods_number'], $v['currency'], $power);
            if ($res['err_code'] !== 0) {
                $this->pushReportMonitorLog([
                    "interface_type"=>"3",
                    "err_msg"=>$res['err_msg'],
                    "err_code"=>$res['err_code'],
                    "remark"=>sprintf("更新购物车，获取商品信息失败 参数 %s",json_encode($v)),

                ]);
                continue;
            }
            $goods = $res['data'];
//            p($goods);
//             die;
            $isFaild = $this->isFaild($goods['goods_info']);
//            p($isFaild);
            if ($isFaild['err_code'] != 0) {
                //过期下架、库存0下架、无价格阶梯下架
                $data = array(
                    'cart_id' => $v['cart_id'],
                    'status' => -1,
                    'update_time' => time(),
                );

            } else {
                //正常更新
                //价格计算
                $goods_ac_type = $res['data']['goods_info']['ac_type'];
                if(in_array($res['data']['goods_info']["goods_id"],C("TUANGOU_GOODSLIST"))){
                    $goods_ac_type = false;
                }
                $goods_prices = ladder_final_price($goods, $v['currency'], $goods_ac_type);
                $initial_prices = ladder_final_price($goods, $v['currency'], false);

                //重新设置商品会员价
                $isGetMemberPrice == false && $this->setActyPrice($goods_ac_type,$goods_prices,$initial_prices);

                if ($goods_prices['price'] <= 0) {//根据阶梯计算出单价依然为0的计入失效
                    $data = array(
                        'cart_id' => $v['cart_id'],
                        'status' => -1,
                        'update_time' => time(),
                    );
                } else {
                    $data = array(
                        'cart_id' => $v['cart_id'],
                        'initial_price' => $initial_prices['price'],
                        'goods_data' => json_encode($goods['goods_info']),
                        'status' => 1,
                        'goods_number' => $v["goods_number"],
                        'self_supplier_type' => isset($goods['goods_info']['self_supplier_type']) ? $goods['goods_info']['self_supplier_type'] : 0,
                        'update_time' => time(),
                    );
                    $data['goods_price'] = $v['is_gift'] != 1 ? $goods_prices['price'] : -1;
                }
            }

            // $ShoppingCartModel->save($data);
            CartService::updateCart($data);
        }
        return $this->apiReturn(0, '更新成功');
    }

    /*
     * 如果是ac_type = 8 的活动价 出订单确认页面 其它地方一律显示原价
     */
    protected function setActyPrice($goods_ac_type=0,&$goods_prices,&$initial_prices){
        if($goods_ac_type == 8){
            $goods_prices = $initial_prices;
        }
        return ;
    }

    /*
     * 处理限购的
     * 更新购物车的时候判断用户购车车数量是否超过限购
     * is_quota = 1时为限购，没有或其他为不限购，quota_num为限购数量
     */
    protected function chGoodsNumsByUpCart($goods_info,&$v){
        if(!isset($goods_info['is_quota']) || $goods_info['is_quota'] != '1' || !isset($goods_info['quota_num'])){
            return true;
        }

        $quota_num = intval($goods_info['quota_num']);
        $v["userHasBuyNums"] = 0;
        //超过限购数量
        if($v["goods_number"] > $quota_num){
            $v["goods_number"] = $quota_num ? $quota_num : $v["goods_number"];
        }
        if(cookie('uid')){
            $userLimitGoodsNumsList = S(C("ichuntUserLimitGoodsNumsList").cookie('uid'));
            if($userLimitGoodsNumsList && is_array($userLimitGoodsNumsList)){
                $exists = array_key_exists($v["goods_id"],$userLimitGoodsNumsList);
                if($exists && isset($userLimitGoodsNumsList[$v["goods_id"]]) && $userLimitGoodsNumsList[$v["goods_id"]] > 0){
                    $goods_number = $quota_num - $userLimitGoodsNumsList[$v["goods_id"]];
                    if($goods_number <= 0){
                        $v["goods_number"] = 0;
                        return false;
                    }else{
                        $v["userHasBuyNums"] = intval($userLimitGoodsNumsList[$v["goods_id"]]);
                        if($goods_number <= $v["goods_number"]){
                            $v["goods_number"] = intval($goods_number);
                        }
                    }
                }
            }
        }
        return true;

    }

    // 订单后台添加购物车   作废XXXXXXXXXXXXXXX
    public function addByOrderSystem()
    {
        cookie('gid', '');
        $data['org_id']         = I('org_id', 1);
        $data['uid']            = I('uid');
        $data['id']             = I('request.id', 0); //商品ID
        $data['num']            = I('request.num', 1, 'intval');//购物数量
        $data['buy']            = I('request.buy', -1, 'intval');//立即购买
        $data['delivery_place'] = I('request.delivery_place', 1, 'intval');//交货地
        $data['type']           = I('request.type', 1, 'intval'); // 订单后台
        $data['pf']             = I('request.pf', '');
        $data['is_gift']        = I('request.is_gift', -1); // 是否赠品
        $data['is_purchase']    = I('request.is_purchase', 1); // 是否采购
        $data['is_vacuo']    = I('request.is_vacuo', 1); // 是否真空包装

        $data['k1'] = time();
        $data['k2'] = pwdhash($data['k1'], C('SUPER_AUTH_KEY'));

        $res = post_curl(API_DOMAIN.'/cart/add', $data);

        print_r($res);
    }

    /**
     * 批量添加购物车(目前仅提供给BOM)
     */
    public function addBatch()
    {
        $data = I('data', '', '');
        $from_type = I('from_type', '1');//bom  来自询报价
        if (empty($data)) {
            return $this->apiReturn(24023, '无有效数据');
        }

        if (!is_array($data)) { // 非数组则解析
            $data = json_decode($data, true);
        }

        $ShoppingCartModel = D('ShoppingCart');
        //获取sku_id数组
        $goods_ids = array_map(function($v){return $v['id'];}, $data);
        //过滤无效sku_id
        $goods_ids = array_filter($goods_ids, function($v){return !empty($v);});
        if (empty($goods_ids)) {
            return $this->apiReturn(24005, '无有效sku');
        }

        $fail_ids = [];
        $cartids = [];
        foreach ($data as $k=>$v) {
            //单个添加
            $res = $this->add($v);
            if ($res['err_code'] != 0) {
                array_push($fail_ids,$k+1);
//                 return $this->apiReturn($res['err_code'], sprintf("第%s个商品：%s",$k+1,$res['err_msg']));
            }else{
                array_push($cartids,$res["data"]);
            }
        }
        $this->setCount();

        if(empty($cartids)){
            return $this->apiReturn(-1, '下单失败');
        }

        if(!empty($fail_ids) && !empty($cartids)){
            return $this->apiReturn(0, sprintf("第%s条记录下单失败",implode(",",$fail_ids)),$cartids);
        }

        return $this->apiReturn(0, '下单成功',$cartids);
    }

    // 联营批量添加物料 --- 订单后台 作废XXXXXXXXXX
    public function addBatchByOrderSystem()
    {
        $items = I('data', '');
        $type = I('type', 1);

        if (empty($items)) return $this->apiReturn(24023, '无有效数据');

        foreach ($items as $v) {
            $res = $this->addJoint($v);

            if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg'] . '，商品名称：' . $v['goods_name']);
        }

        $this->setCount();

        return $this->apiReturn(0, '添加成功');
    }

    // 联营物料添加购物车  作废XXXXXXXXXX
    public function addJoint($goods)
    {
        $gid            = cookie('gid');
        $uid            = $this->getCartUid();
        $type           = I('request.type', 1, 'intval'); // 1-前台，2-后台，3-后台批量添加，4-导入销售报价明细
        $delivery_place = I('request.delivery_place', 1, 'intval');//交货地
        $operator_id    = I('operator_id', 0);
        $operator_name  = I('operator_name', '');
        $bom_id         = I('request.bom_id', 0, 'intval');//bom单id
        $org_id         = I('org_id', 1); // 组织ID

        $ShoppingCartModel = D('ShoppingCart');
        $CmsModel          = D('Cms');

        $bom_sn = I('request.bom_sn','', 'trim');//bom单号
        $matching_id = isset($goods["matching_id"]) ? intval($goods["matching_id"]) : 0;//bom单 匹配列表的主键id

        $buyer_id = $goods['buyer_id'] ? $goods['buyer_id'] : 0; // 默认采购员ID为0

        // 赠品
        if ($goods['is_gift'] == 1) {
            $goods['goods_price'] = 0;
        }

        // 检查购物车是否已有同商品 (当【商品名称】、【制造商】、【供应商】、【采购单价】四者不同时，视为不同的SKU)
        if ($goods['id']) {
            $has = $ShoppingCartModel->getUserHasById($uid, $goods['id'], $delivery_place, $type, $org_id);
        } else {
            // 后台批量导入的，商品名称、制造商、供应】、采购单价、货期不同时，视为不同的SKU
            // $has = $ShoppingCartModel->getUserHasByGoods($uid, $goods, $delivery_place, $type, $org_id);
        }

        if ($has && $type == 4) return $this->apiReturn(0, '销售报价明细已添加');

        // $currency = C('PLACE_CURRENCY_MAPPING.'. $delivery_place);
        $currency = I('currency', 1);

        $extend_type = 0;//购物车商品扩展类型  5是来自bom单
        $extend_type_id = "";//关联的id或者其他

        if($bom_sn != "" && $matching_id > 0){
            $extend_type = 5;//5表示 来自于bom单
            $extend_type_id = $bom_sn."_".$matching_id;
        }

        $num = $goods['num'];
        if (!empty($has['cart_id'])) {
            $num = intval($has['goods_number']) + $num;

            $data = array(
                'cart_id'        => $has['cart_id'],
                'user_sign'      => $gid,
                'goods_number'   => $num,
                'goods_price'    => $goods['goods_price'],
                'initial_price'  => 0,
                // 'goods_data'     => json_encode($goods),
                'bom_id'         => $bom_id,
                'status'         => 1,
                'update_time'    => time(),
                'extend_type'    => $extend_type,
                'extend_type_id' => $extend_type_id,
                'buyer_id'       => $buyer_id,
                'remarks'        => !empty($goods['remarks']) ? $goods['remarks'] : '',
                'batch'          => !empty($goods['batch']) ? $goods['batch'] : '',
                'goods_id'       => !empty($goods['id']) ? $goods['id'] : '',
                'is_gift'        => $goods['is_gift'],
                'is_purchase'    => $goods['is_purchase'],
                'is_vacuo'    => $goods['is_vacuo'],
                'is_provide_dc' => $goods['is_provide_dc'],
                'is_provide_producer' => $goods['is_provide_producer'],
                'customer_material_number' => !empty($goods['customer_material_number']) ? $goods['customer_material_number'] : '',
                'contract_remark' => !empty($goods['contract_remark']) ? $goods['contract_remark'] : '',
            );
            !empty($uid) && $data['user_id'] = $uid;
            $res = $ShoppingCartModel->save($data);
            if ($res === false) {
                $this->behavior_push(4, 24007, '更新购物车失败，请稍后重试');
                return $this->apiReturn(24007, '更新购物车失败，请稍后重试');
            }
            $cart_id = $has['cart_id'];
        } else {
            $sale_type = 1;

            if ($goods['id']) { // 若商品ID存在，从商品服务获取数据
                $power = $this->isCanAc($uid);//是否满足优惠条件
                $res = $this->getFinalGoods($goods['id'], $goods['num'], $currency, $power); //获取商品

                if ($res['err_code'] == 0) {
                    $goods_info = $res['data']['goods_info'];

                    if (!$res['data']['enough_stock']) {
                        //如果是清库存商品 则最大下单数据量不能超过库存量
                        if($goods_info['ac_type'] != static::$QING_KU_CUN_ACTYPE){
                            $sale_type = 2;//预售
                        }else{
                            //清库存allow_presale :1不允许下预售  2允许下预售
                            $bool = !!(isset($goods_info['allow_presale']) && $goods_info['allow_presale']== 2);
                            if($goods_info['ac_type'] == static::$QING_KU_CUN_ACTYPE && $bool){
                                $sale_type = 2;//预售
                            }
                        }
                    }
                }
            }

            $data = array(
                'user_sign'           => !empty($gid) ? $gid : '',
                'user_id'             => !empty($uid) ? $uid : 0,
                'supplier_id'         => !empty($goods['supplier_id']) ? $goods['supplier_id'] : '',
                'supplier_name'       => !empty($goods['supplier_name']) ? $goods['supplier_name'] : '',
                'goods_name'          => strConv($goods['goods_name']),
                'sku_name'            => strConv($goods['goods_name']),
                'brand_name'          => strConv($goods['brand_name']),
                'goods_number'        => $num,
                'goods_price'         => $goods['goods_price'],
                'create_goods_price'  => $goods['goods_price'],
                'order_source'        => $this->order_source(),
                'goods_data'          => isset($goods_info) ? json_encode($goods_info) : json_encode($goods),
                'currency'            => $currency,
                'delivery_place'      => $delivery_place,
                'goods_type'          => isset($goods_info) ? $goods_info['goods_type'] : 2,
                'sale_type'           => $sale_type,
                'status'              => 1,
                'extend_type'         => $extend_type,
                'extend_type_id'      => $extend_type_id,
                'bom_id'              => $bom_id,
                'type'                => $type,
                'buyer_id'            => $buyer_id,
                'remarks'             => !empty($goods['remarks']) ? $goods['remarks'] : '',
                'batch'               => !empty($goods['batch']) ? $goods['batch'] : '',
                'goods_id'            => !empty($goods['id']) ? $goods['id'] : '',
                'raw_goods_sn'        => isset($goods['raw_goods_sn']) ? strConv($goods['raw_goods_sn']) : '',
                'raw_goods_packing'   => isset($goods['raw_goods_packing']) ? strConv($goods['raw_goods_packing']) : '',
                'raw_brand_name'      => isset($goods['raw_brand_name']) ? strConv($goods['raw_brand_name']) : '',
                'is_gift'             => isset($goods['is_gift']) ? $goods['is_gift'] : -1,
                'is_purchase'         => isset($goods['is_purchase']) ? $goods['is_purchase'] : 1,
                'is_vacuo'            => isset($goods['is_vacuo']) ? $goods['is_vacuo'] : -1,
                'delivery_time'       => isset($goods['delivery_time']) ? $goods['delivery_time'] : '',
                'is_provide_dc'       => isset($goods['is_provide_dc']) ? $goods['is_provide_dc'] : -1,
                'is_provide_producer' => isset($goods['is_provide_producer']) ? $goods['is_provide_producer'] : -1,
                'customer_material_number' => !empty($goods['customer_material_number']) ? $goods['customer_material_number'] : '',
                'contract_remark' => !empty($goods['contract_remark']) ? $goods['contract_remark'] : '',
            );

            if (strtolower($goods['supplier_name']) == 'digikey') {
                $data['supplier_id'] = 7;
            }

            if (isset($goods_info['supplier_name'])) {
                $data['supplier_id'] = $goods_info['supplier_id'];
                $data['supplier_name'] = $goods_info['supplier_name'];
            }

            /** 调整标准品牌 2022-3-31 */
            $data['standard_brand_id']   = !empty($goods['standard_brand_id']) ? $goods['standard_brand_id'] : 0;
            $data['standard_brand_name'] = !empty($goods['standard_brand_name']) ? strConv($goods['standard_brand_name']) : '';

            if (!$ShoppingCartModel->create($data)) {
                $this->behavior_push(4, 24003, $ShoppingCartModel->getError());
                return $this->apiReturn(24003, $ShoppingCartModel->getError());
            }

            $cart_id = $ShoppingCartModel->add();

            if ($cart_id === false) {
                $this->behavior_push(4, 24004, '添加购物车失败，请稍后重试');
                return $this->apiReturn(24004, '添加购物车失败，请稍后重试');
            }
        }

        $this->setCount();

        return $this->apiReturn(0, '添加购物车成功', $cart_id);
    }

    // 获取标准品牌 --- 2021-7-15
    public function getStandardBrand($brand_name)
    {
        $url = STONE_DOMAIN.'/AuthApi/get_standard_brand';
        $key = C('footstone_api_key');

        $map['brand_name'] = $brand_name;
        $map['time']       = time();
        $map['AuthSign']   = MD5(MD5(http_build_query($map)) . $key);

        $data['data'] = $map;

        $res = json_decode(post_curl($url, $data), true);

        if ($res['errcode'] != 0) {
            $path = C('LOG_PATH').ACTION_NAME.'/'.date('y_m_d').'.log'; // 接口日志文件
            \Think\Log::write(json_encode($res), INFO, '', $path);
        }

        return $res;
    }

    // 获取标准品牌
    public function getStandardBrandName($brand_name)
    {
        $url = STONE_DOMAIN.'/AuthApi/get_scm_brand_list';
        $key = C('footstone_api_key');

        $map['erp_brand_name'] = $brand_name;
        $map['time']     = time();
        $map['AuthSign'] = MD5(MD5(http_build_query($map)) . $key);

        $data['data'] = $map;

        $res = json_decode(post_curl($url, $data), true);

        if ($res['errcode'] != 0) {
            $path = C('LOG_PATH').ACTION_NAME.'/'.date('y_m_d').'.log'; // 接口日志文件
            \Think\Log::write(json_encode($res), INFO, '', $path);
            return false;
        }

        if (empty($res['data'])) return false;

        // 匹配品牌
        foreach ($res['data'] as $v) {
            if ($v['erp_brand_name'] == $brand_name) {
                return $v;
            }
        }

        return false;
    }

    // 绑定品牌
    public function bindStandardBrand($params)
    {
        $url = STONE_DOMAIN.'/AuthApi/binding_scm_brand';
        $key = C('footstone_api_key');

        $map['time']         = time();
        $map['scm_brand_id'] = $params['standard_brand_id'];
        $map['brand_id']     = [$params['brand_id']];
        $map['brand_name']   = [$params['brand_name']];
        $map['admin_id']     = $params['operator_id'];
        $map['admin_name']   = $params['operator_name'];
        $map['AuthSign']     = MD5(MD5(http_build_query($map)) . $key);

        $data['data'] = $map;

        $res = json_decode(post_curl($url, $data), true);

        if ($res['errcode'] != 0) {
            $path = C('LOG_PATH').ACTION_NAME.'/'.date('y_m_d').'.log'; // 接口日志文件
            \Think\Log::write(json_encode($res), INFO, '', $path);
        }

        return $res;
    }

    /**
     * 重新购买订单商品
     * @return [type] [description]
     */
    public function rebuy()
    {
        $order_id = I('order_id', 0, 'intval');
        $OrderModel = D('Order');
        $OrderItemsModel = D('OrderItems');
        $info = $OrderModel->getInfo($order_id);
        $lists = $OrderItemsModel->where(array('order_id' => $order_id, 'status' => 1))->getField('goods_id, goods_name, goods_number, brand_name, goods_type');
        if ($info['user_id'] != cookie('uid')) {
            return $this->apiReturn(24024, '重新购买订单无效');
        }
        $count = count($lists);
        $goods_ids = array_keys($lists);

        //批量获取信息
        $goods = $this->getGoodsArr($goods_ids);
        if ($goods['err_code'] != 0) {
            return $this->apiReturn($goods['err_code'], $goods['err_msg']);
        }
        $goods = $goods['data'];

        $goods_ids = array_keys($goods);//获取能从基石获取到的skuid列表

        global $_REQUEST;
        $_REQUEST['buy'] = 1;//立即购买方式

        $fail_goods = array();
        $success = 0;
        foreach ($lists as $k => &$v) {
            $v['goods_type'] = in_array($v['goods_type'], C('ORDER_GOODS_TYPE_MAP.1')) ? 1 : 2;

            if (!in_array($k, $goods_ids)) {
                $fail_goods[] = $v;
                continue;
            }
            $data = array(
                'id' => $k,
                'num' => $v['goods_number'],
                'delivery_place' => $info['delivery_place'],
            );
            $res = $this->add($data);

            if ($res['err_code'] != 0) {
                $fail_goods[] = $v;
                continue;
            }

            $success++;
        }

        $datas['count']   = $count;
        $datas['success'] = $success;
        $datas['fail']    = $fail_goods;

        return $this->apiReturn(0, '', $datas);

    }


    /**
     * 添加购物车 更新缓存
     * 缓存：主要缓存购物车中的供应商和品牌
     * 目前主要给showCoupon方法用  显示优惠券
     */
    protected function updateShowCouponShopCart($supplier_id,$brand_id,$uid=0){
        try{
            $user_id = $uid;
            if(!$user_id) {
                return true;
            }
            $key = C("SHOWCOUPON_SHOPPING_CART_CACHE");
            $cache_key = $key.":".$user_id;
            $cache = S($cache_key);
            $selected_supplier_ids = [];
            $selected_brand_ids = [];
            if(!$cache){
                if($supplier_id){
                    array_push($selected_supplier_ids,$supplier_id);
                }

                if($brand_id){
                    array_push($selected_brand_ids,$brand_id);
                }

            }else{
                $selected_supplier_ids = isset($cache['selected_supplier_ids']) ? $cache['selected_supplier_ids'] : [];
                $selected_brand_ids = isset($cache['selected_brand_ids']) ? $cache['selected_brand_ids'] : [];
                if($supplier_id  && !in_array($supplier_id,$selected_supplier_ids)){
                    array_push($selected_supplier_ids,$supplier_id);
                }

                if($brand_id && !in_array($brand_id,$selected_brand_ids)){
                    array_push($selected_brand_ids,$brand_id);
                }
            }
            $selected_supplier_ids = array_unique($selected_supplier_ids);
            $selected_brand_ids = array_unique($selected_brand_ids);
            S($cache_key,
                ["selected_supplier_ids"=>$selected_supplier_ids,"selected_brand_ids"=>$selected_brand_ids],
                ['expire'=>C("SHOWCOUPON_SHOPPING_CART_CACHE_TTL")]
            );
        }catch(\Exception $e){

        }
    }

    /**
     * 重置购物车缓存的供应商和品牌
     */
    protected function resetShowCouponShopCartCache($user_id=0){
        try{
            if(!$user_id) {
                return true;
            }
            $key = C("SHOWCOUPON_SHOPPING_CART_CACHE");
            $cache_key = $key.":".$user_id;
            S($cache_key,null);
        }catch(\Exception $e){

        }
    }


    /*
     * bom单后台下单
     * 获取商品信息
     */
    public function bomOrderGetFinalGoods(){
        $goods_id = I('request.id', 0);
        $uid = I('request.uid', 0);
        $num = I('request.num', 0, 'intval');
        $currency = I('request.currency', 1, 'intval');
        $power = I('request.power', array());
        //是否满足优惠条件
        $use_ac = $this->isCanAc($uid);
        //获取商品
        $res = $this->getFinalGoods($goods_id, $num, $currency, $use_ac);
        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        return $this->apiReturn($res['err_code'], $res['err_msg'],$res['data']);
    }


    /*
     * 修改购物车备注
     */
    public function editCartRemarks(){
        $cart_id = I("cart_id",0,"intval");
        $remarks = I("remark","","trim");
        $user_id = cookie("uid") ? cookie("uid") : 0;
        if($user_id <= 0){
            return $this->apiReturn(24013, "您还没有登录,无法修改商品备注!");
        }
        if($remarks == ""){
            return $this->apiReturn(24013, "备注内容不能为空!");
        }

        $ShoppingCartModel = D('ShoppingCart');

        // if($ShoppingCartModel->where(["user_id"=>$user_id,"cart_id"=>$cart_id])->count() <= 0){
        //     return $this->apiReturn(24013, "没有找到属于您的商品信息!");
        // }
        $cartIdArr = CartService::getCartIdsByWheres([
            "cart_id"=>$cart_id,
            "user_id"=>$user_id,
        ]);
        if(count($cartIdArr) <= 0){
            return $this->apiReturn(24013, "没有找到属于您的商品信息!");
        }
        // $bk = $ShoppingCartModel->where(["user_id"=>$user_id,"cart_id"=>$cart_id])->save([
        //     "remarks"=>$remarks
        // ]);
        $bk = CartService::updateCart([
            // "user_id"=>$user_id,
            "cart_id"=>$cart_id,
            "remarks"=>$remarks
        ]);

        if ($bk === false){
            return $this->apiReturn(24013, "修改商品备注失败!");
        }

        return $this->apiReturn(0, "修改商品备注成功!");

    }


    /**
     * 添加购物车
     * @param  string   goods_id    商品ID
     * @param  integer  num         数量
     * @param  array    items       批量添加数组
     * @param  array    goods       提前获取商品明细
     * @param  integer  delivery_place       交货地 1大陆，2香港
     */
    public function add($items = null, $goods = null)
    {
        $gid          = cookie('gid');
        $uid          = $this->getCartUid();
        $org_id       = I('org_id', 1); // 组织ID，1-猎芯，3-华云
        $type         = I('request.type', 1, 'intval'); // 前后台
        // $sale_type = I('request.sale_type', 1, 'intval');//销售类型，1现卖，2预售
        $bom_id       = I('request.bom_id', 0, 'intval');//BOMID记录
        $buy          = I('request.buy', -1, 'intval');//立即购买

        $bom_sn       = I('request.bom_sn','', 'trim');//bom单号
        $matching_id  = I('request.matching_id', 0, 'intval');//bom单 匹配列表的主键id
        $sale_type    = 1; //默认现卖
        $is_gift      = I('request.is_gift', -1); // 是否赠品，1-是，-1-否

        if (is_null($items)) {
            $goods_id       = I('request.id', 0);//商品ID
            $num            = I('request.num', 1, 'intval');//购物数量
            $delivery_place = I('request.delivery_place', 1, 'intval');//交货地
            $batch          = false;
        } else {
            $goods_id       = $items['id'];
            $num            = $items['num'];
            $delivery_place = $items['delivery_place'];
            $batch          = true;
            $matching_id    = isset($items["matching_id"]) ? intval($items["matching_id"]) : 0;//bom单 匹配列表的主键id
            $bom_sn         = isset($items["bom_sn"]) ? trim($items["bom_sn"]) : "";//bom单 匹配列表的bomsn
        }
        // $attr = I('attr', '', 'trim');//楼层参数(已集成在PTAG)
        empty($delivery_place) && $delivery_place = 1;
        $currency = C('PLACE_CURRENCY_MAPPING.'. intval($delivery_place));


        if (empty($gid) && empty($uid)) {
            $this->behavior_push(4, 24013, '未找到当前用户标识');
            return $this->apiReturn(24013, '未找到当前用户标识');
        }

        //合并现有和历史购物车
        $this->mergeCart($gid, $uid);
        // p(1);exit;
        $ShoppingCartModel = D('ShoppingCart');
        //检查购物车是否已有同商品
        if (!empty($uid)) {
            $has = $ShoppingCartModel->getUserHasById($uid, $goods_id, $delivery_place, $type, $org_id);
        } else {
            $has = $ShoppingCartModel->getUserHasBySign($gid, $goods_id, $delivery_place, $org_id);
        }
        //立即购买 =》 数量覆盖 ， 其他 =》 数量叠加
        if (!empty($has) && $buy != 1) {
            $num = intval($has['goods_number']) + $num;
        }
        //是否满足优惠条件
        $power = $use_ac = $this->isCanAc($uid);

        //获取商品
        $res = $this->getFinalGoods($goods_id, $num, $currency, $power);
        if ($res['err_code'] != 0) {
            $this->pushReportMonitorLog(
                [
                    "interface_type"=>"3",
                    "err_msg"=>$res['err_msg'],
                    "err_code"=>$res['err_code'],
                    "remark"=>sprintf("goods_id:%s,数量:%s,币别:%s,use_ac:%s 未找到相关商品信息，
                    请重新加入购物车,%s",$goods_id, $num, $currency, json_encode($use_ac),json_encode($res)),

                ]
            );
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        $goods = $res['data']['goods_info'];
        //价格计算
        $goods_prices = ladder_final_price($res['data'], $currency, $goods['ac_type']);
        //获取优惠前 原始价格
        $initial_prices = ladder_final_price($res['data'], $currency, false);

        if(in_array($goods["ac_type"], [8, 10])){
            $goods_prices = $initial_prices;
        }

       if ($goods['is_buy'] != 1) {
//            $this->behavior_push(4, 24009, '商品已下架');
           $this->pushReportMonitorLog(
               [
                   "interface_type"=>"3",
                   "err_msg"=>"商品已下架",
                   "err_code"=>"24009",
                   "remark"=>json_encode($res),

               ]
           );
           return $this->apiReturn(24009, '商品已下架');
       }

        if ($goods['min_buy'] > $num) {
            if ($batch) {//批量时达不到最小起订量 则 以最小起订量购买
                $num = $goods['min_buy'];
            } else {
                $this->pushReportMonitorLog(
                    [
                        "interface_type"=>"3",
                        "err_msg"=>"未达到当前商品最小起订量",
                        "err_code"=>"24020",
                        "remark"=>sprintf("该用户起订量为%s,商品信息 %s",$num,json_encode($res)),

                    ]
                );
                return $this->apiReturn(24020, '未达到当前商品最小起订量');
            }
        }
        //限制购买数量是否是倍数
        if (in_array($goods['goods_type'], C('ORDER_GOODS_TYPE_MAP.1'))) { //联营
            $mpl = !empty($goods['multiple']) ? $goods['multiple'] : 1;//旧数据
            if (($num - $goods['min_buy']) % $mpl > 0) {
                return $this->apiReturn(24006, '购买数量必须为'.$mpl.'的整倍数');
            }
        } else { //自营
            $mpl = !empty($goods['mpl']) ? $goods['mpl'] : 1;//旧数据
            if (($num - $goods['min_buy']) % $mpl > 0) {
                return $this->apiReturn(24006, '购买数量必须为'.$mpl.'的整倍数');
            }
        }
        //限购购买数量
//        $tmp_goods = $goods;
//        $tmp_goods["goods_number"] = $num;
        //限制购买数量是否是倍数end
        //预售无需限制库存
        //检查数量(2017.11.30产品提出数量为1的不能购买) => 2018.3.22业务提出数量为1也能购买
//        if (!in_array($goods['goods_type'], array(1,2))) {
//            if (!$res['data']['enough_stock']) {
//                //如果是清库存商品 则最大下单数据量不能超过库存量
//                if($goods['ac_type'] != static::$QING_KU_CUN_ACTYPE){
//                    $sale_type = 2;//预售
//                }else{
//                    //清库存allow_presale :1不允许下预售  2允许下预售
//                    $bool = !!(isset($goods['allow_presale']) && $goods['allow_presale']== 2);
//                    if($goods['ac_type'] == static::$QING_KU_CUN_ACTYPE && $bool){
//                        $sale_type = 2;//预售
//                    }else{
//                        $modNum = ($goods['goods_number']) % $mpl;
//                        $num = $goods['goods_number'] - intval($modNum);
//                    }
//                }
//                return $this->apiReturn(24006, '购物车商品数量已超过现有库存');
//
//            }
//        } else {
//            if (!$res['data']['enough_stock']) {
//                return $this->apiReturn(24006, '购物车商品数量已超过现有库存');
//            }
//        }

        if (!$res['data']['enough_stock']) {
            return $this->apiReturn(24006, '购物车商品数量已超过现有库存');
        }


        //价格异常
        if ($goods_prices['price'] <= 0) {
            if ($goods['goods_type'] == 1) {
                $this->pushReportMonitorLog(
                    [
                        "interface_type"=>"3",
                        "err_msg"=>"商品不支持当前交货地",
                        "err_code"=>"24008",
                        "remark"=>json_encode($res),

                    ]
                );
                return $this->apiReturn(24008, '商品不支持当前交货地');
            } else {
                $this->pushReportMonitorLog(
                    [
                        "interface_type"=>"3",
                        "err_msg"=>"商品已下架",
                        "err_code"=>"24009",
                        "remark"=>json_encode($res),

                    ]
                );
                return $this->apiReturn(24009, '商品已下架');
            }
        }

        $extend_type = 0;//购物车扩展 类型 5 bom单
        $extend_type_id = "";//购物车扩展 类型 5 bom单
        if($matching_id > 0 && $bom_sn != ""){
            //来自于bom 假如购物车
            $extend_type = 5;//5表示 来自于bom单
            $extend_type_id = $bom_sn."_".$matching_id;
        }

        $goods_price = $is_gift != 1 ? $goods_prices['price'] : 0; // 商品单价
        $is_purchase = I('is_purchase', 1);
        $is_vacuo = I('is_vacuo', -1);
        if (!empty($has['cart_id'])) {
            $data = array(
                'cart_id'        => $has['cart_id'],
                'user_sign'      => $gid,
                'goods_number'   => $num,
                'goods_price'    => $goods_price,
                'initial_price'  => $initial_prices['price'],
                'goods_data'     => json_encode($goods),
                'bom_id'         => !empty($bom_id) ? $bom_id : 0,
                'extend_type_id' => $extend_type_id,//扩展类型对应的关联id
                'extend_type'    => $extend_type,//扩展类型
                'status'         => 1,
                'update_time'    => time(),
                'sale_type'      => $sale_type,
                'is_gift'        => $is_gift, // 是否赠品，1-是，-1-否
                'is_purchase'    => $is_purchase, // 是否采购，1-是，-1-否
                'is_vacuo'    => $is_vacuo, // 是否真空包装，1-是，-1-否
            );
            !empty($uid) && $data['user_id'] = $uid;
            $res = CartService::updateCart($data);
            // $res = $ShoppingCartModel->save($data);
            if ($res === false) {
                return $this->apiReturn(24007, '更新购物车失败，请稍后重试');
            }
            $cart_id = $has['cart_id'];
        } else {
            $data = array(
                'user_sign'          => !empty($gid) ? $gid : '',
                // 'org_id'             => $org_id,
                'user_id'            => !empty($uid) ? $uid : 0,
                'goods_id'           => $goods['goods_id'],
                'brand_id'           => intval($goods['brand_id']),
                'supplier_id'        => $goods['supplier_id'],
                'supplier_name'      => $goods['supplier_name'],
                'goods_sn'           => $goods['goods_sn'],
                'goods_name'         => htmlspecialchars_decode($goods['goods_name']),
                'sku_name'           => htmlspecialchars_decode($goods['sku_name']),
                'brand_name'         => htmlspecialchars_decode($goods['brand_name']),
                'goods_number'       => $num,
                'goods_price'        => $goods_price,
                'create_goods_price' => $goods_prices['price'],
                'initial_price'      => $initial_prices['price'],
                'order_source'       => $this->order_source(),
                'goods_data'         => json_encode($goods),
                'currency'           => $currency,
                'delivery_place'     => $delivery_place,
                'change_place'       => $goods['change_place'],
                'goods_type'         => $goods['goods_type'],
                'sale_type'          => $sale_type,
                'bom_id'             => !empty($bom_id) ? $bom_id : 0,
                'status'             => 1,
                'extend_type_id'     => $extend_type_id,//扩展类型对应的关联id
                'extend_type'        => $extend_type,//扩展类型
                'self_supplier_type' => isset($goods['self_supplier_type']) ? $goods['self_supplier_type'] : 0,//1是自采， 2是立创
                'type'               => $type, // 区分前后台
                'is_gift'            => $is_gift, // 是否赠品，1-是，-1-否
                'is_purchase'        => $is_purchase, // 是否采购，1-是，-1-否
                'is_vacuo'    => $is_vacuo, // 是否真空包装，1-是，-1-否
                'create_time'    => time(),
            );

            // 后台加购获取采购员ID
            if ($type != 1) {
                $SupplierModel = D('Supplier');
                $IntracodeModel = D('Cms/Intracode');

                $supplier = $SupplierModel->getSupplierInfo('', $goods['supplier_name']);

                if ($supplier['purchase_uid']) {
                    $encode = $supplier['purchase_uid'];
                } else {
                    $encode = $goods["encoded"] ?: '';
                }

                if (!empty($encode)) {
                    $intracode = $IntracodeModel->getIntracodeInfo($encode);
                    $data['buyer_id'] = !empty($intracode) ? $intracode['admin_id'] : 0;
                }
            }

            /** 调整标准品牌 2022-3-31 */
            $data['standard_brand_id']   = isset($goods['standard_brand_id']) ? $goods['standard_brand_id'] : 0;
            $data['standard_brand_name'] = isset($goods['standard_brand_name']) ? $goods['standard_brand_name'] : '';
            /** end */
            // if (!$ShoppingCartModel->create($data)) {
            //     return $this->apiReturn(24003, "添加购物车失败，请稍后重试");
            // }

//            $cart_id = $ShoppingCartModel->add();
            $cart_id = CartService::addCart($data);
            if ($cart_id === false) {
                return $this->apiReturn(24004, '添加购物车失败，请稍后重试');
            }

            //更新用户优惠券关联的购物车缓存
//            $this->updateShowCouponShopCart($data['supplier_id'],$data['brand_id'],$uid);
        }
        $this->setCount();
        $this->add_extend($goods,$buy,$uid,$sale_type,$delivery_place,$goods_prices,$num);
        return $this->apiReturn(0, '添加购物车成功', $cart_id);
    }

    protected function add_extend($goods,$buy,$uid,$sale_type,$delivery_place,$goods_prices,$num){
        $behavior_param = array(
            'goods_id' => $goods['goods_id'],
            'goods_name' => $goods['goods_name'],
            'goods_number' => I('request.num'),
            'brand_name' => $goods['brand_name'],
            'supplier_name' => !empty($goods['canal']) ? $goods['supplier_name'].'-'.$goods['canal'] : $goods['supplier_name'],
        );
        if ($buy != 1) {//加入购物车
            $this->behavior_push(4, 0, $behavior_param);
        } else {//立即购买
            $clear = platform() == 2 ? false : true; //移动版立即购买不清除场景值
            $this->behavior_push(5, 0, $behavior_param, 1, $clear);
        }
        //提供首页滚动数据
        $this->setRollData(1, $uid, array('goods_count' => I('request.num')));

        //神策上报数据
//        if ($sale_type == 1) {
//            $delivery_time = is_array($goods['delivery_time']) ? $goods['delivery_time'][$delivery_place] : $goods['delivery_time'];
//            $delivery_time = !empty($delivery_time) ? $delivery_time : '';
//        } else {
//            $delivery_time = '';
//        }
//        $ptag = ptag();
//        $has_coupon = $this->getHasCoupon($goods['supplier_id'], $goods['brand_id'], 2);
//        $properties = array(
//            'event' => 'addToShoppingcart',
//            'user_id' => !empty($uid) ? $uid : $_COOKIE['distinct_id'],
//            'is_login' => !empty($uid),
//            'goods_id' => $goods['goods_id'],
//            'goods_name' => $goods['goods_name'],
//            'class_name1' => $goods['class1_name'],
//            'class_name2' => $goods['class2_name'],
//            'goods_price' => $goods_prices['price'],
//            'goods_number' => $num,
//            'cart_ptag_click' => $ptag,
//            'brand_name' => $goods['brand_name'],
//            'supplier_name' => $goods['supplier_name'],
//            'encap' => $goods['encap'],
//            'batch_sn' => '',
//            'delivery_time' => $delivery_time,
//            'moq' => $goods['min_buy'],
//            'mpq' => $goods['min_mpq'],
//            'delivery_place' => $delivery_place,
//            'module_name' => I('module_name', ''),
//            'click_adtag' => adtag(),
//            'click_ptag' => I('click_ptag', ''),
//            'ac_type' => $goods['ac_type'] > 0 ? true : false,
//            'has_activity' => strpos($ptag, 'activity') !== false ? true : false,
//            'has_bonus' => $has_coupon['err_code'] == 0 && !empty($has_coupon['data']),
//        );
//        $this->report_data($properties);
    }

    // 订单后台删除购物车商品 作废XXXXXXXXXX
    public function deleteByOrderSystem()
    {
        cookie('gid', '');
        $data['uid'] = I('uid');
        $data['cart_id'] = I('cart_id');

        $data['k1'] = time();
        $data['k2'] = pwdhash($data['k1'], C('SUPER_AUTH_KEY'));

        $res = post_curl(API_DOMAIN.'/cart/delete', $data);

        print_r($res);
    }

    /**
     * 删除
     * @param  string   cart_id      购物车商品ID 多个,隔开
     * @param  string   act          类型 normal普通商品删除 faild失效商品删除
     * @return [type] [description]
     */
    public function delete($cart_id = '')
    {
        $gid = cookie('gid');
        $uid = $this->getCartUid();
        empty($cart_id) && $cart_id = I('request.cart_id', '');
        $act = I('request.act', 'normal');
        if (empty($cart_id)) {
            $this->apiReturn(24015, '请选择购物车商品');
        }
        if (!in_array($act, array('normal', 'faild'))) {
            $this->apiReturn(24016, '请选择需要删除类型');
        }
        if (empty($uid) && empty($gid)) {
            return $this->apiReturn(24013, '未找到当前用户标识');
        }
        // $ShoppingCartModel = D('ShoppingCart');
        if (!empty($uid)) {
            $map['user_id'] = $uid;
        } else {
            $map['user_sign'] = $gid;
        }
        $cart_ids = explode(',', $cart_id);
        $map['cart_id'] = $cart_ids;
        //清空失效商品
        if ($act == 'faild') {
            $map['status'] = -1;
        //删除商品
        } else {
            // $map['status'] = 1;
        }
        $cartIdArr = CartService::getCartIdsByWheres($map);
        if(empty($cartIdArr)){
            return $this->apiReturn(0, '删除成功');
        }
        //批量删除
        array_map(function($cartId) {
            CartService::delCartByCartId($cartId);
        }, $cartIdArr);

        // $res = $ShoppingCartModel->where($map)->delete();
        // if ($res === false) {
        //     return $this->apiReturn(24018, '清空购物车失败，请稍后重试');
        // }

        //清除优惠券关联的购物车缓存
//        if($uid){
//            $lists = $ShoppingCartModel->where(['user_id'=>$uid,"status"=>1])->field("supplier_id,brand_id")->select();
//            //重置缓存
//            $this->resetShowCouponShopCartCache($uid);
//            //添加缓存数据
//            foreach($lists as $val){
//                $this->updateShowCouponShopCart($val['supplier_id'],$val['brand_id'],$uid);
//            }
//        }
//
//        D("OrderItemsGift")->deleteOrCreateByCart($cart_ids);
        $this->setCount();
        return $this->apiReturn(0, '删除成功');
    }

    /**
     * 获取确认订单数据
     * @return [type] [description]
     */
    public function confirm($extend_merge = true,$from_inquiry_item_ids="")
    {
        $inquiry_item_ids       = I('request.inquiry_item_ids', '');//询价单转订单
        $cart_ids       = I('request.cart_id', '');
        $user_coupon_id = I('request.user_coupon_id', 0, 'intval');
        $shipping_type  = I('shipping_type', 1, 'intval');//1快递，2自提
        $address_id     = I('address_id', 0, 'intval');
        $type           = I('type', 0, 'intval'); // 订单后台新增订单 (3-内部采购下单，不计算运费， 4-自营其他业务)
        $business_type  = I('business_type', 0, 'intval'); // 自营其他业务类型，1-样品销售，单价0，2-仓库损耗，单价0，不需要其他费用
        $uid            = $this->getCartUid();
        $uc_id          = I('uc_id') ? I('uc_id') : cookie('ucid');
        $is_free_ship   = I('is_free_ship', ''); // 是否免运费 （用于后台下单）
        
        if(!empty($inquiry_item_ids) || !empty($from_inquiry_item_ids)){
            $inquiry_item_ids = $inquiry_item_ids? $inquiry_item_ids : $from_inquiry_item_ids;
            $datas =  $this->confirmByInquiry($inquiry_item_ids);
            return $this->apiReturn(0, '获取成功', $datas);
        }

        //前台展示时合并专卖附加费，后台记录详细专卖附加费
        $extend_merge = $this->auth() ? false : $extend_merge;

        $ShoppingCartModel = D('ShoppingCart');
        $OrderAddressModel = D('OrderAddress');
        
        $map = array(
            'user_id' => $uid,
            'cart_id_arr' => explode(',', $cart_ids),
            'status' => 1
        );
        ///先更新数据
        // $update_list = $ShoppingCartModel->where($map)->order('create_time DESC, cart_id DESC')->field('cart_id, goods_type, type')->select();
        $update_list = CartService::getCartList($map,'create_time DESC, cart_id DESC');
        foreach ($update_list as &$v) {
            if ($v['type'] != 1) continue; // 后台系统添加的不更新

            $this->update($v['cart_id'],true);
        }

        $order = $type ? 'cart_id ASC' : 'create_time DESC'; // 后台下单按ID升序

        $map["goods_number_gt_0"] = true;
        // $data = $ShoppingCartModel->getList($map, null, $order, 'cart_id, goods_id, brand_id, standard_brand_id, supplier_id, goods_sn, goods_name, sku_name,
        // brand_name, standard_brand_name, supplier_name, goods_number, goods_price, create_goods_price, initial_price, goods_type, sale_type, currency,
        //  goods_data, bom_id, delivery_place, order_source, create_time, update_time, self_supplier_type, buyer_id,extend_type, extend_type_id, batch, remarks,raw_goods_sn,raw_goods_packing,raw_brand_name,
        //   is_gift, is_purchase,is_vacuo,is_provide_dc, is_provide_producer, customer_material_number, contract_remark');
        $data = CartService::getCartList($map,$order);
        if (empty($data)) {
            return $this->apiReturn(24001, '购物车内无有效商品', $map);
        }
        $goods_total            = 0;//货款金额
        $zy_goods_total         = 0;//货款金额  自营
        $zyxianmai_goods_total  = 0;//货款金额  自营现卖  判断运费用
        $ly_goods_total         = 0;//货款金额  联营
//        $hasZiYinXianMai        =0;//是否含有自营现卖
//        $order_goods_type    = 0;//商品类型   订单合并后 此字段失效 不能用该字段作为判断依据
//        $sale_type           = 0;//销售类型   订单合并后 此字段失效 不能用该字段作为判断依据
        $delivery_place      = 0;//运费
        $free_shipping_price = 0;//减免金额
        $preferential_price  = 0;//优惠金额
        $coupon_price        = 0;//优惠券金额（构成preferential_price其中之一）
        $activity_price      = 0;//活动优惠金额（构成preferential_price其中之一）
        $currency            = 0;//币种
        $words               = array();
        $allow_coupon = 1;
        $push_sale_data = []; // 推送到销售商品库
        $SupplierModel = D('Supplier');
        $IntracodeModel = D('Cms/Intracode');
        $is_exists_mro = 0; // 是否含有京东MRO商品
        $mro_goods_total = 0; // 京东MRO货款金额

    //    p($data);exit;
        foreach ($data as $k => &$v) {
            //第一条的数据，防止一个订单混合交货地
            empty($delivery_place) && $delivery_place = $v['delivery_place'];
            empty($currency) && $currency = $v['currency'];
            $_order_goods_type = C('GOODS_ORDER_TYPE_MAP.' . $v['goods_type']);
            //sale_type 1现卖2预售
            //goods_type 商品类型  联营:1专卖,2联营  自营: 3自营4寄售
            $_sale_type = $_order_goods_type == 1 ? 1 : $v['sale_type'];
            //添加商品销售类型和  商品类型
            $v["sale_type"] = $_sale_type; //销售类型1现卖 2预售
            $v["order_goods_type"] = $_order_goods_type;//商品类型1联营 2自营

            if ($delivery_place != $v['delivery_place']) {//跳过不同交货地商品
                unset($data[$k]);
                continue;
            }
            $info = json_decode($v['goods_data'], true);
//            $v["buyer_id"] =  ? $info["encoded"] : 0;
            // if(isset($info["encoded"]) && $info["encoded"]){
            //     $v["buyer_id"] = $info["encoded"];
            // }

            // 专营商品根据供应商获取采购员
            if ($v["order_goods_type"] == 1) {
                $supplier = $SupplierModel->getSupplierInfo('', $v['supplier_name']);

                if ($supplier['purchase_uid']) {
                    $encode = $supplier['purchase_uid'];
                } else {
                    $encode = $info["encoded"];
                }

                if (!empty($encode)) {
                    $intracode = $IntracodeModel->getIntracodeInfo($encode);
                    $v['buyer_id'] = !empty($intracode) ? $intracode['admin_id'] : 0;
                }
            }

            if ($_sale_type == 1 && $_order_goods_type ==2){
                //自营现卖
                $delivery_time = "现卖";
            }else{
//                if ($_sale_type == 2 && $_order_goods_type ==2){
                    //预售需要从商品信息中  获取采购员
//                    $v["buyer_id"] = isset($info["encoded"]) ? $info["encoded"] : 0;
//                }
                //联营和预售 显示商品的货期
                $delivery_time = is_array($info['delivery_time']) ? $info['delivery_time'][$delivery_place] : $info['delivery_time'];
                $delivery_time = !empty($delivery_time) ? $delivery_time : '';
            }


            //供应商的营销词
            $v['ad_word'] = '';
            if (!isset($words[$v['supplier_id']])) {
                $word = $this->getAdWord($v['supplier_id']);
                if ($word['err_code'] == 0) {
                    $words[$v['supplier_id']] = $word['data'];
                }
            }

            $v['ad_word'] = $words[$v['supplier_id']];
            if ($info['allow_coupon'] == 2) {
                $allow_coupon = 2;//不允许使用
            }

            // 自营其他业务调整单价为0 (仓库损耗、样品销售)
            if ($business_type) {
                $v['goods_price'] = 0;
            }


            $v['min_mpq']               = isset($info['min_mpq']) ? $info['min_mpq'] : 1;
            $v['min_buy']               = isset($info['min_buy']) ? $info['min_buy'] : 1;
            $v['encap']                 = isset($info['encap']) ? $info['encap'] : '';//封装
            $v['class2_name']           = isset($info['class2_name']) ? $info['class2_name'] : '';//二级分类
            $v['packing_name']          = isset($info['packing_name']) ? $info['packing_name'] : '';//包装
            $v['goods_unit']            = isset($info['goods_unit_name']) ? $info['goods_unit_name'] : '';//单位
            $v['canal']                 = isset($info['canal']) ? $info['canal'] : '';//专卖渠道标记
            $v['stock']                 = isset($info['goods_number']) ? $info['goods_number'] : 0;//库存
            $v['supplier_stock']        = isset($info['supplier_stock']) ? $info['supplier_stock'] : 0;//供应商库存
            $v['delivery_time']         = $delivery_time;
            $v['ac_type']               = isset($info['ac_type']) ? $info['ac_type'] : 0;
            $v['goods_price_format']    = price_format($v['goods_price'], $v['currency'], 4);
            $v['goods_amount']          = price_format($v['goods_price'] * $v['goods_number'], 0, 2);
            $v['goods_amount_format']   = price_format($v['goods_price'] * $v['goods_number'], $v['currency'], 2);
            $v['initial_price']         = price_format($v['initial_price'], 0, 4);
            $v['initial_amount']        = price_format($v['initial_price'] * $v['goods_number'], 0, 2);
            $v['initial_amount_format'] = price_format($v['initial_price'] * $v['goods_number'], $v['currency'], 2);
            $v['diff_price']            = price_format($v['create_goods_price'] - $v['goods_price'], 0, 4);
            $v['diff_price_format']     = price_format($v['diff_price'], $v['currency'], 4);
            $v['create_time']           = date('Y-m-d H:i:s', $v['create_time']);
            $v['update_time']           = date('Y-m-d H:i:s', $v['update_time']);
            $v["activity_info"]         = isset($info["activity_info"]) ? $info["activity_info"] : null;
            $v['goods_tag']             = !empty($info['goods_tag']) ? $info['goods_tag']['goods_label_name'] : '';
            $v["goods_images"] = $info['goods_images'] ? $info['goods_images'] : 'http://static.ichunt.com/dist/res/home/<USER>/goods_default.png';
            $v['source'] = $info['source'];

            // 若为寄售商品，则将商品类型设置为寄售
            if ($info['source'] == 12) {
                $v['goods_type'] = 4;
            }

            // if ($v["standard_brand_id"] > 0){ //todo 2023.2.1 标准品牌
            //     $v["brand_id"] = $v["standard_brand_id"];
            //     $v["brand_name"] = $v["standard_brand_name"];
            // }

            $goods_total += (float)price_format($v['goods_price'] * $v['goods_number'], 0, 2);//单独4位小数的小计之和

            //统计自营商品小计之和
            if (intval($_order_goods_type) == 2) {
                $zy_goods_total += (float)price_format($v['goods_price'] * $v['goods_number'], 0, 2);//单独4位小数的小计之和  统计自营商品小计之和
            }

            //统计自营现卖总金额
            if (intval($_order_goods_type) == 2 && $_sale_type == 1) {
                $zyxianmai_goods_total += (float)price_format($v['goods_price'] * $v['goods_number'], 0, 2);//单独4位小数的小计之和  统计自营商品小计之和
            }

            //统计联营商品小计之和
            // dump($_order_goods_type);
            if (intval($_order_goods_type) == 1) {
                $ly_goods_total += (float)price_format($v['goods_price'] * $v['goods_number'], 0, 2); //单独4位小数的小计之和  统计联营商品小计之和
            }

            if ($v['goods_type'] == 6) {
                $is_exists_mro = 1;
                $mro_goods_total += (float)price_format($v['goods_price'] * $v['goods_number'], 0, 2); //统计京东MRO商品小计之和
            }

            //限购的商品 添加限购字段
            try{
                $goods_data = json_decode($v['goods_data'],true);
                $v['is_quota'] = isset($goods_data["is_quota"]) ? $goods_data["is_quota"] : 0;
                $v['quota_num'] = isset($goods_data["quota_num"]) ? $goods_data["quota_num"] : 0;
            }catch(\Exception $e){}
            unset($v['goods_data']);
        }
        // p($data);exit;

        //商品总优惠金额
        $preferential_price = $coupon_price + $activity_price ;
        //物流价格
        $shipping_price = price_format(0);
        $address_free_shipping = true;//可包邮地区
        $isAllZYXianMai = $this->checkOrderListIsZYXM($data);//是否全是自营现卖商品
        $isAllZY = $this->checkOrderListIsZY($data);//是否全是自营商品
        $zy_delivery_type =  I("send_type",2,"intval");//1 自营优先发货  2拼单发货
       if ($isAllZYXianMai){
           //如果商品全部都是自营现卖 则默认只能是自营优先发货 需要收取运费
           $zy_delivery_type = 1;
       }
        // if ($isAllZY && $zy_goods_total >= 0 && $business_type != 2) { //自营需要判断运费 (仓库损耗不需要运费)
        //    $this->getShippingPrices($address_id,$type,$uid,$shipping_type,$zy_goods_total,$address_free_shipping,
        //        $shipping_price,$preferential_price,$free_shipping_price);
        // }
        // $free_shipping_price = price_format($free_shipping_price);

        // 若存在京东MRO商品且物流方式为快递，则获取MRO运费
        // $mro_shipping_fee = 0;
        // if ($is_exists_mro && $shipping_type == 1) {
        //     $mro_shipping_fee = $this->getMroShippingFee($address_id, $type, $uid, $mro_goods_total);
        //     $shipping_price += $mro_shipping_fee;
        // }
        //请求开放平台
        $address_info = $this->getUserAddressInfo( null,$address_id);
        $requestOpenApiData=[];
        $requestOpenApiDataItems=[];
        foreach ($data as $v2) {
            $requestOpenApiDataItemsTmpArr=[];
            $requestOpenApiDataItemsTmpArr["goods_id"] = $v2["goods_id"];
            $requestOpenApiDataItemsTmpArr["goods_name"] = $v2["goods_name"];
            $requestOpenApiDataItemsTmpArr["goods_number"] = (int)$v2["goods_number"];
            $requestOpenApiDataItemsTmpArr["goods_price"] = (float)$v2["goods_price"];
            $requestOpenApiDataItemsTmpArr["goods_type"] = $v2["goods_type"];
            $requestOpenApiDataItemsTmpArr["brand_id"] = $v2["brand_id"];
            $requestOpenApiDataItemsTmpArr["brand_name"] = $v2["brand_name"];
            $requestOpenApiDataItemsTmpArr["standard_brand_id"] = $v2["standard_brand_id"];
            $requestOpenApiDataItemsTmpArr["standard_brand_name"] = $v2["standard_brand_name"];
            $requestOpenApiDataItemsTmpArr["supplier_id"] = $v2["supplier_id"]?:0;
            $requestOpenApiDataItemsTmpArr["supplier_name"] = $v2["supplier_name"]?:"";
            $requestOpenApiDataItems[] = $requestOpenApiDataItemsTmpArr;
        }
        $requestOpenApiData["sale_com_id"] = $delivery_place == 1 ? 1 : 2; // 1 香港 2 大陆;
        $requestOpenApiData["uc_id"] = $uc_id; // 用户uc_id;
        $requestOpenApiData["user_id"] = $uid; // 用户ID;
        $requestOpenApiData["shipping_type"] = $shipping_type;// 快递方式，1-快递，2-自提
        $requestOpenApiData["province_id"] = $address_info ? $address_info["province"] : 0; 
        $requestOpenApiData["city_id"] = $address_info ? $address_info["city"] : 0; 
        $requestOpenApiData["delivery_place"] = $delivery_place; // 用户ID;
        $requestOpenApiData["is_get_extra_fee"] = 1; //  是否获取附加费，0-否，1-是 如果询报价则为0
        $requestOpenApiData["currency"] = $delivery_place == 1 ? 1 : 2; // 1 香港 2 大陆;
        $requestOpenApiData["user_coupon_id"] = $user_coupon_id > 0 ? $user_coupon_id : 0; // 用户ID;
        $requestOpenApiData["items"] = $requestOpenApiDataItems;
        // echo json_encode($requestOpenApiData);exit;
        $openReturn = OpenService::requestOpenApi(OPENPLATFORM_DOMAIN. '/order/getOrderFees',$requestOpenApiData,"POST");
        $shipping_price = $openReturn["shipping_fee"] +  $openReturn["shipping_fee_mro"];
        //商品总价
        $goods_total = price_format($openReturn["goods_amount"], 0, 2);
        //总商品优惠价格
        $preferential_price = price_format($preferential_price);

        // 订单金额 (后台内部采购下单、仓库损耗不需要运费)
        // if (($type == 3 || $business_type == 2 || $is_free_ship) && !$is_exists_mro) {
        //     $amount = orderAmount($goods_total, $preferential_price);
        // } else {
        //     $amount = orderAmount($goods_total, $preferential_price, $shipping_price, $free_shipping_price, 0);
        // }

        //***********二次调整-货款价格最低价限制***********//
        // $this->minOrderConfirm($data, $amount);
        //***********三次调整-根据供应商设置附加费*********//
        // $extend_items = $this->getItemsGroupFee($data, $amount, $extend_merge);//补充extend_price
        
        foreach($openReturn["items"] as $openReturnItem){
            foreach($data as $k3=>$v3){
                if($v3["goods_id"] == $openReturnItem["goods_id"]){
                    $data[$k3]["extend_price"] = $openReturnItem["extend_price"];
                    $data[$k3]["extend_price_items"] = $openReturnItem["extend_price_items"];
                }
            }
        }

        $datas = page_data($data);
        // $datas = array_merge($datas, $amount);

        $datas['original_order_amount'] = price_format($openReturn["order_amount"], $currency); 
        $datas['order_amount'] = $openReturn["order_amount"];
        $datas['goods_total'] = $openReturn["goods_amount"];;
        $datas['preferential_price'] = $preferential_price;
        $datas['shipping_price'] = $shipping_price;
        $datas['free_shipping_price'] = $free_shipping_price;
        $datas['extend_fee'] = $openReturn["extra_fee"];

        $datas['address_free_shipping'] = $address_free_shipping;
        $datas['mkt_point'] = conver_mkt_point($ly_goods_total, 1, $currency) + conver_mkt_point($zy_goods_total, 2, $currency);//计算积分
        $datas['allow_coupon'] = $allow_coupon;//是否允许优惠券
        $datas['extend_items'] = [];//附加费组成
        $datas['order_goods_type'] = 0;//生成订单类型 该字段作废 20200818
        $datas['sale_type'] = 0;//销售方式  不生效 该字段作废 20200818
        $datas['user_coupon_id'] = $user_coupon_id;//使用优惠券ID
        $datas['coupon_price'] = $openReturn["coupon_amount"]?:0;//优惠券优惠金额
        $datas['activity_price'] = $activity_price;//活动优惠金额
        $datas["zy_goods_total"] = $zy_goods_total;//自营总价格
        $datas["zy_goods_total_format"] = price_format($zy_goods_total, $currency);//自营总价格
        $datas["ly_goods_total"] = $ly_goods_total;//联营总价格
        $datas["ly_goods_total_format"] = price_format($ly_goods_total,$currency); //联营总价格
        $datas["mro_goods_total"] = $mro_goods_total; //MRO总价格
        $datas["mro_goods_total_format"] = price_format($mro_goods_total, $currency); //MRO总价格
        $datas["mro_shipping_fee"] = $openReturn["shipping_fee_mro"]; //MRO运费
        // $datas['coupon_price_format'] = price_format($coupon_price, $currency);//优惠券优惠金额
        $datas['coupon_price_format'] = price_format($openReturn["coupon_amount"], $currency);//优惠券优惠金额
        $datas['activity_price_format'] = price_format($activity_price, $currency);//活动优惠金额
        // $datas['goods_total_format'] = price_format($datas['goods_total'], $currency, 2);
        $datas['goods_total_format'] = price_format($openReturn['goods_amount'], $currency, 2);
        $datas['preferential_price_format'] = price_format($datas['preferential_price'], $currency);
        $datas['order_amount_format'] = price_format($datas['order_amount'], $currency);
        $datas['extend_fee_format'] = price_format($datas['extend_fee'], $currency);
        $datas['finally_shipping_price_format'] = price_format($openReturn['shipping_fee'] + $openReturn['shipping_fee_mro'], $currency);//最终优惠后运费
        $datas['isAllZYXianMai'] = $isAllZYXianMai;
        $datas['currency'] = $currency; // 币种
        $datas['is_exists_mro'] = $is_exists_mro;

        if ($currency == 2) { // 若是香港订单，则获取合同乙方公司名称
            $datas['contract_com_name'] = $this->getContractComName($uid);
        }

        return $this->apiReturn(0, '获取成功', $datas);
    }

    public function confirmByInquiry($inquiry_item_ids = ''){
        $inquiry_item_id_arr = explode(',', $inquiry_item_ids);
        if(empty($inquiry_item_id_arr)){
            return $this->apiReturn(-1, '无效的询价商品');
        }
        $inquiryService = new InquiryService();
        $inquiryList = $inquiryService->inquiry($inquiry_item_id_arr);

        $user_coupon_id = I('request.user_coupon_id', 0, 'intval');
        $shipping_type  = I('shipping_type', 1, 'intval');//1快递，2自提
        $address_id     = I('address_id', 0, 'intval');
        $uid            = $this->getCartUid();
        $uc_id          = I('uc_id') ? I('uc_id') : cookie('ucid');

        $delivery_place = 0; // 交货地 1 大陆 2 香港;
        foreach($inquiryList as $item){
            if($delivery_place && $delivery_place != $item['delivery_place']){
                return $this->apiReturn(-1, '交货地不一致，无法下单!');
            }
            if(!$delivery_place){
                $delivery_place = $item['delivery_place'];
            }
        }
        if(!$delivery_place || !in_array($delivery_place,[1,2])){
            return $this->apiReturn(-1, '下单明细中无交货地!');
        }

        $address_info = $this->getUserAddressInfo( null,$address_id);

        $requestOpenApiData=[];
        $requestOpenApiDataItems=[];
        foreach ($inquiryList as $inquiryListItem) {
            $requestOpenApiDataItemsTmpArr=[];
            $requestOpenApiDataItemsTmpArr["goods_id"] = isset($inquiryListItem["goods_id"]) ? $inquiryListItem["goods_id"] : 0;
            $requestOpenApiDataItemsTmpArr["goods_name"] = isset($inquiryListItem["goods_name"])? $inquiryListItem["goods_name"] : "";
            $requestOpenApiDataItemsTmpArr["goods_number"] = isset($inquiryListItem["goods_number"]) ?  (int)$inquiryListItem["goods_number"] : 0;
            $requestOpenApiDataItemsTmpArr["goods_price"] = isset($inquiryListItem["goods_price"])? (float)$inquiryListItem["goods_price"] : 0;
            $requestOpenApiDataItemsTmpArr["brand_id"] = isset($inquiryListItem["brand_id"])? $inquiryListItem["brand_id"] : 0  ;
            $requestOpenApiDataItemsTmpArr["brand_name"] =isset($inquiryListItem["brand_name"])? $inquiryListItem["brand_name"] : "";
            $requestOpenApiDataItemsTmpArr["standard_brand_id"] = isset($inquiryListItem["standard_brand_id"])? $inquiryListItem["standard_brand_id"] : 0;
            $requestOpenApiDataItemsTmpArr["standard_brand_name"] = isset($inquiryListItem["standard_brand_name"])? $inquiryListItem["standard_brand_name"] : "";
            $requestOpenApiDataItemsTmpArr["supplier_id"] = isset($inquiryListItem["supplier_id"])? $inquiryListItem["supplier_id"] : 0;
            $requestOpenApiDataItemsTmpArr["supplier_name"] = isset($inquiryListItem["supplier_name"])? $inquiryListItem["supplier_name"] : "";
            $requestOpenApiDataItems[] = $requestOpenApiDataItemsTmpArr;
        }
        $requestOpenApiData["sale_com_id"] = $delivery_place == 1 ? 1 : 2; // 1 大陆 2 香港;
        $requestOpenApiData["uc_id"] = $uc_id; // 用户uc_id;
        $requestOpenApiData["user_id"] = $uid; // 用户ID;
        $requestOpenApiData["shipping_type"] = $shipping_type;// 快递方式，1-快递，2-自提
        $requestOpenApiData["province_id"] = $address_info ? $address_info["province"] : 0; 
        $requestOpenApiData["city_id"] = $address_info ? $address_info["city"] : 0; 
        $requestOpenApiData["delivery_place"] = $delivery_place; // 用户ID;
        $requestOpenApiData["is_get_extra_fee"] = 0; //  是否获取附加费，0-否，1-是 如果询报价则为0
        $requestOpenApiData["currency"] = $delivery_place == 1 ? 1 : 2; // 1 香港 2 大陆;
        $requestOpenApiData["user_coupon_id"] = $user_coupon_id > 0 ? $user_coupon_id : 0; // 用户ID;
        $requestOpenApiData["items"] = $requestOpenApiDataItems;
        // echo json_encode($requestOpenApiData);exit;
        $openReturn = OpenService::requestOpenApi(OPENPLATFORM_DOMAIN. '/order/getOrderFees',$requestOpenApiData,"POST");
        // p($openReturn);exit;

        $shipping_price = $openReturn["shipping_fee"] +  $openReturn["shipping_fee_mro"];
        //商品总价
        $goods_total = price_format($openReturn["goods_amount"], 0, 2);

        $returnData["p"] = 1;
        $returnData["total_page"] = 1;
        $returnData["limit"] = 1;
        $returnData["count"] = count($inquiryList);
        $returnData["original_order_amount"] = price_format($openReturn["order_amount"], $requestOpenApiData["currency"]); 
        $returnData["order_amount"] = $openReturn["order_amount"];
        $returnData["goods_total"] = $openReturn["goods_amount"];
        $returnData["preferential_price"] = "0.00";
        $returnData["shipping_price"] = $shipping_price;
        $returnData["free_shipping_price"] = 0;
        $returnData["extend_fee"] = 0;
        $returnData["address_free_shipping"] = true;
        $returnData["mkt_point"] = 0;
        $returnData["allow_coupon"] = 2;
        $returnData["extend_items"] = [];
        $returnData["order_goods_type"] = 0;
        $returnData["sale_type"] = 0;
        $returnData["user_coupon_id"] = 0;
        $returnData["coupon_price"] = 0;
        $returnData["activity_price"] = 0;
        $returnData["zy_goods_total"] = 0;
        $returnData["zy_goods_total_format"] = price_format(0, $requestOpenApiData["currency"], 2);
        $returnData["ly_goods_total"] = 0;
        $returnData["ly_goods_total_format"] = price_format(0, $requestOpenApiData["currency"], 2);
        $returnData["mro_goods_total"] = 0;
        $returnData["mro_goods_total_format"] = price_format(0, $requestOpenApiData["currency"], 2);
        $returnData["mro_shipping_fee"] = 0;
        $returnData["coupon_price_format"] =  price_format(0, $requestOpenApiData["currency"], 2);
        $returnData["activity_price_format"] = price_format(0, $requestOpenApiData["currency"], 2);
        $returnData["goods_total_format"] = price_format($openReturn['goods_amount'], $requestOpenApiData["currency"], 2);
        $returnData["preferential_price_format"] =  price_format(0, $requestOpenApiData["currency"], 2);
        $returnData["order_amount_format"] =  price_format($openReturn['order_amount'], $requestOpenApiData["currency"]);
        $returnData["extend_fee_format"] = price_format(0, $requestOpenApiData["currency"], 2);
        $returnData["finally_shipping_price_format"] = price_format(0, $requestOpenApiData["currency"], 2);
        $returnData["isAllZYXianMai"] = false;
        $returnData["currency"] = $requestOpenApiData["currency"];
        $returnData["is_exists_mro"] = 0;
        $returnData["list"] = [];
        foreach ($inquiryList as $inquiryListItem) {
            $returnItemData["user_sign"] = "";
            $returnItemData["inquiry_item_id"] = isset($inquiryListItem["inquiry_item_id"]) ? $inquiryListItem["inquiry_item_id"] : 0;
            $returnItemData["org_id"] = 1;
            $returnItemData["user_id"] = $uid;
            $returnItemData["goods_id"] = isset($inquiryListItem["goods_id"]) ? $inquiryListItem["goods_id"] : 0;
            $returnItemData["brand_id"] = isset($inquiryListItem["brand_id"])? $inquiryListItem["brand_id"] : 0  ;
            $returnItemData["standard_brand_id"] =isset($inquiryListItem["standard_brand_id"])? $inquiryListItem["standard_brand_id"] : 0;
            $returnItemData["supplier_id"] = isset($inquiryListItem["supplier_id"])? $inquiryListItem["supplier_id"] : 0;
            $returnItemData["goods_sn"] = "";
            $returnItemData["goods_name"] = isset($inquiryListItem["goods_name"])? $inquiryListItem["goods_name"] : "";
            $returnItemData["sku_name"] = isset($inquiryListItem["goods_name"])? $inquiryListItem["goods_name"] : "";
            $returnItemData["brand_name"] = isset($inquiryListItem["brand_name"])? $inquiryListItem["brand_name"] : "";
            $returnItemData["standard_brand_name"] = isset($inquiryListItem["standard_brand_name"])? $inquiryListItem["standard_brand_name"] : "";
            $returnItemData["supplier_name"] = isset($inquiryListItem["supplier_name"])? $inquiryListItem["supplier_name"] : "";
            $returnItemData["warehouse"] = "";
            $returnItemData["goods_number"] = isset($inquiryListItem["goods_number"]) ?  (int)$inquiryListItem["goods_number"] : 0;
            $returnItemData["goods_price"] = isset($inquiryListItem["goods_price"])? (float)$inquiryListItem["goods_price"] : 0;
            $returnItemData["create_goods_price"] = isset($inquiryListItem["goods_price"])? (float)$inquiryListItem["goods_price"] : 0;
            $returnItemData["initial_price"] = isset($inquiryListItem["goods_price"])? (float)$inquiryListItem["goods_price"] : 0;
            $returnItemData["order_source"] = "pf=1,ptag=detail";
            $returnItemData["delivery_time"] = isset($inquiryListItem["delivery_time"])? $inquiryListItem["delivery_time"] : "";
            $returnItemData["currency"] = isset($inquiryListItem["currency"])? (int)$inquiryListItem["currency"] : "";
            $returnItemData["delivery_place"] = isset($inquiryListItem["delivery_place"])? $inquiryListItem["delivery_place"] : "";
            $returnItemData["change_place"] = 0;
            $returnItemData["sale_type"] = 1;
            $returnItemData["goods_type"] = 1;
            $returnItemData["status"] = 1;
            $returnItemData["bom_id"] = 0;
            $returnItemData["extend_type_id"] = "";
            $returnItemData["extend_type"] = 0;
            $returnItemData["type"] = 1;
            $returnItemData["self_supplier_type"] = 0;
            $returnItemData["is_remind"] = 0;
            $returnItemData["batch"] = isset($inquiryListItem["batch"])? $inquiryListItem["batch"] : "";
            $returnItemData["remarks"] = "";
            $returnItemData["raw_goods_sn"] = "";
            $returnItemData["raw_goods_packing"] = "";
            $returnItemData["raw_brand_name"] = "";
            $returnItemData["is_gift"] = -1;
            $returnItemData["is_purchase"] = 1;
            $returnItemData["is_provide_dc"] = -1;
            $returnItemData["is_provide_producer"] = -1;
            $returnItemData["is_vacuo"] = -1;
            $returnItemData["customer_material_number"] = "";
            $returnItemData["contract_remark"] = "";
            $returnItemData["cart_id"] = 0;
            $returnItemData["order_goods_type"] = 1;
            $returnItemData["ad_word"] = [];
            $returnItemData["min_mpq"] = 1;
            $returnItemData["min_buy"] = 1;
            $returnItemData["encap"] = "";
            $returnItemData["class2_name"] = "";
            $returnItemData["packing_name"] = "";
            $returnItemData["goods_unit"] = "";
            $returnItemData["canal"] = "";
            $returnItemData["stock"] = 0;
            $returnItemData["supplier_stock"] = 0;
            $returnItemData["ac_type"] = 0;
            $returnItemData["goods_price_format"] = price_format($inquiryListItem["goods_price"], $returnData["currency"], 6);
            $returnItemData["goods_amount"] = price_format($inquiryListItem["goods_price"]*$inquiryListItem["goods_number"], 0, 2);
            $returnItemData["goods_amount_format"] = price_format($inquiryListItem["goods_price"]*$inquiryListItem["goods_number"], $returnData["currency"], 2);
            $returnItemData["initial_amount"] = price_format($inquiryListItem["goods_price"]*$inquiryListItem["goods_number"], 0, 2);
            $returnItemData["initial_amount_format"] = price_format($inquiryListItem["goods_price"]*$inquiryListItem["goods_number"], $returnData["currency"], 2);
            $returnItemData["diff_price"] = "0";
            $returnItemData["diff_price_format"] = price_format(0, $requestOpenApiData["currency"], 2);
            $returnItemData["activity_info"] = [];
            $returnItemData["goods_tag"] = "";
            $returnItemData["goods_images"] = "http://static.ichunt.com/dist/res/home/<USER>/goods_default.png";
            $returnItemData["source"] = 1;
            $returnItemData["is_quota"] = 0;
            $returnItemData["quota_num"] = 0;
            $returnItemData["extend_price"] = 0.00;
            $returnItemData["extend_price_items"] = "";
            $returnItemData["inquiry_item_id"] = isset($inquiryListItem["inquiry_item_id"]) ? $inquiryListItem["inquiry_item_id"] : 0;//询价明细id
            $returnItemData["inquiry_id"] = isset($inquiryListItem["inquiry_id"]) ? $inquiryListItem["inquiry_id"] : 0;//询价单id
            $returnItemData["inquiry_sn"] = isset($inquiryListItem["inquiry_sn"]) ? $inquiryListItem["inquiry_sn"] : "";//询价单号
            $returnItemData["quote_id"] = isset($inquiryListItem["quote_id"]) ? $inquiryListItem["quote_id"] : 0;//报价单id
            $returnItemData["quote_sn"] = isset($inquiryListItem["quote_sn"]) ? $inquiryListItem["quote_sn"] : "";//报价单号
            $returnItemData["buyer_id"] = isset($inquiryListItem["buyer_id"]) ? $inquiryListItem["buyer_id"] : 0;//采购员
            $returnData["list"][] = $returnItemData;
        }

        return $returnData;
        
    }


    public function confirm_bakaaa($extend_merge = true){
    
        $cart_ids       = I('request.cart_id', '');
        $user_coupon_id = I('request.user_coupon_id', 0, 'intval');
        $shipping_type  = I('shipping_type', 1, 'intval');//1快递，2自提
        $address_id     = I('address_id', 0, 'intval');
        $type           = I('type', 0, 'intval'); // 订单后台新增订单 (3-内部采购下单，不计算运费， 4-自营其他业务)
        $business_type  = I('business_type', 0, 'intval'); // 自营其他业务类型，1-样品销售，单价0，2-仓库损耗，单价0，不需要其他费用
        $uid            = $this->getCartUid();
        $is_free_ship   = I('is_free_ship', ''); // 是否免运费 （用于后台下单）

        //前台展示时合并专卖附加费，后台记录详细专卖附加费
        $extend_merge = $this->auth() ? false : $extend_merge;

        $ShoppingCartModel = D('ShoppingCart');
        $OrderAddressModel = D('OrderAddress');
        
        $map = array(
            'user_id' => $uid,
            'cart_id_arr' => explode(',', $cart_ids),
            'status' => 1
        );
        ///先更新数据
        // $update_list = $ShoppingCartModel->where($map)->order('create_time DESC, cart_id DESC')->field('cart_id, goods_type, type')->select();
        $update_list = CartService::getCartList($map,'create_time DESC, cart_id DESC');
        foreach ($update_list as &$v) {
            if ($v['type'] != 1) continue; // 后台系统添加的不更新

            $this->update($v['cart_id'],true);
        }

        $order = $type ? 'cart_id ASC' : 'create_time DESC'; // 后台下单按ID升序

        $map["goods_number_gt_0"] = true;
        // $data = $ShoppingCartModel->getList($map, null, $order, 'cart_id, goods_id, brand_id, standard_brand_id, supplier_id, goods_sn, goods_name, sku_name,
        // brand_name, standard_brand_name, supplier_name, goods_number, goods_price, create_goods_price, initial_price, goods_type, sale_type, currency,
        //  goods_data, bom_id, delivery_place, order_source, create_time, update_time, self_supplier_type, buyer_id,extend_type, extend_type_id, batch, remarks,raw_goods_sn,raw_goods_packing,raw_brand_name,
        //   is_gift, is_purchase,is_vacuo,is_provide_dc, is_provide_producer, customer_material_number, contract_remark');
        $data = CartService::getCartList($map,$order);
        if (empty($data)) {
            return $this->apiReturn(24001, '购物车内无有效商品', $map);
        }
        $goods_total            = 0;//货款金额
        $zy_goods_total         = 0;//货款金额  自营
        $zyxianmai_goods_total  = 0;//货款金额  自营现卖  判断运费用
        $ly_goods_total         = 0;//货款金额  联营
//        $hasZiYinXianMai        =0;//是否含有自营现卖
//        $order_goods_type    = 0;//商品类型   订单合并后 此字段失效 不能用该字段作为判断依据
//        $sale_type           = 0;//销售类型   订单合并后 此字段失效 不能用该字段作为判断依据
        $delivery_place      = 0;//运费
        $free_shipping_price = 0;//减免金额
        $preferential_price  = 0;//优惠金额
        $coupon_price        = 0;//优惠券金额（构成preferential_price其中之一）
        $activity_price      = 0;//活动优惠金额（构成preferential_price其中之一）
        $currency            = 0;//币种
        $words               = array();
        $allow_coupon = 1;
        $push_sale_data = []; // 推送到销售商品库
        $SupplierModel = D('Supplier');
        $IntracodeModel = D('Cms/Intracode');
        $is_exists_mro = 0; // 是否含有京东MRO商品
        $mro_goods_total = 0; // 京东MRO货款金额

//        p($data);
        foreach ($data as $k => &$v) {
            //第一条的数据，防止一个订单混合交货地
            empty($delivery_place) && $delivery_place = $v['delivery_place'];
            empty($currency) && $currency = $v['currency'];
            $_order_goods_type = C('GOODS_ORDER_TYPE_MAP.' . $v['goods_type']);
            $_sale_type = $_order_goods_type == 1 ? 1 : $v['sale_type'];
            //添加商品销售类型和  商品类型
            $v["sale_type"] = $_sale_type; //销售类型1现卖 2预售
            $v["order_goods_type"] = $_order_goods_type;//商品类型1联营 2自营

            if ($delivery_place != $v['delivery_place']) {//跳过不同交货地商品
                unset($data[$k]);
                continue;
            }
            $info = json_decode($v['goods_data'], true);
//            $v["buyer_id"] =  ? $info["encoded"] : 0;
            // if(isset($info["encoded"]) && $info["encoded"]){
            //     $v["buyer_id"] = $info["encoded"];
            // }

            // 专营商品根据供应商获取采购员
            if ($v["order_goods_type"] == 1) {
                $supplier = $SupplierModel->getSupplierInfo('', $v['supplier_name']);

                if ($supplier['purchase_uid']) {
                    $encode = $supplier['purchase_uid'];
                } else {
                    $encode = $info["encoded"];
                }

                if (!empty($encode)) {
                    $intracode = $IntracodeModel->getIntracodeInfo($encode);
                    $v['buyer_id'] = !empty($intracode) ? $intracode['admin_id'] : 0;
                }
            }

            if ($_sale_type == 1 && $_order_goods_type ==2){
                //自营现卖
                $delivery_time = "现卖";
            }else{
//                if ($_sale_type == 2 && $_order_goods_type ==2){
                    //预售需要从商品信息中  获取采购员
//                    $v["buyer_id"] = isset($info["encoded"]) ? $info["encoded"] : 0;
//                }
                //联营和预售 显示商品的货期
                $delivery_time = is_array($info['delivery_time']) ? $info['delivery_time'][$delivery_place] : $info['delivery_time'];
                $delivery_time = !empty($delivery_time) ? $delivery_time : '';
            }


            //供应商的营销词
            $v['ad_word'] = '';
            if (!isset($words[$v['supplier_id']])) {
                $word = $this->getAdWord($v['supplier_id']);
                if ($word['err_code'] == 0) {
                    $words[$v['supplier_id']] = $word['data'];
                }
            }

            $v['ad_word'] = $words[$v['supplier_id']];
            if ($info['allow_coupon'] == 2) {
                $allow_coupon = 2;//不允许使用
            }

            // 自营其他业务调整单价为0 (仓库损耗、样品销售)
            if ($business_type) {
                $v['goods_price'] = 0;
            }
//            if ($info['ac_type'] == 5) {//新客价
//                $activity_price += price_format($v['initial_price'] * $v['goods_number'], 0, 4) - price_format($v['goods_price'] * $v['goods_number'], 0, 4);
//                $v['goods_price'] = $v['initial_price'];//使用原价作为商品价格，新客价作为订单优惠金额
//            }

            $v['min_mpq']               = isset($info['min_mpq']) ? $info['min_mpq'] : 1;
            $v['min_buy']               = isset($info['min_buy']) ? $info['min_buy'] : 1;
            $v['encap']                 = isset($info['encap']) ? $info['encap'] : '';//封装
            $v['class2_name']           = isset($info['class2_name']) ? $info['class2_name'] : '';//二级分类
            $v['packing_name']          = isset($info['packing_name']) ? $info['packing_name'] : '';//包装
            $v['goods_unit']            = isset($info['goods_unit_name']) ? $info['goods_unit_name'] : '';//单位
            $v['canal']                 = isset($info['canal']) ? $info['canal'] : '';//专卖渠道标记
            $v['stock']                 = isset($info['goods_number']) ? $info['goods_number'] : 0;//库存
            $v['supplier_stock']        = isset($info['supplier_stock']) ? $info['supplier_stock'] : 0;//供应商库存
            $v['delivery_time']         = $delivery_time;
            $v['ac_type']               = isset($info['ac_type']) ? $info['ac_type'] : 0;
            $v['goods_price_format']    = price_format($v['goods_price'], $v['currency'], 4);
            $v['goods_amount']          = price_format($v['goods_price'] * $v['goods_number'], 0, 2);
            $v['goods_amount_format']   = price_format($v['goods_price'] * $v['goods_number'], $v['currency'], 2);
            $v['initial_price']         = price_format($v['initial_price'], 0, 4);
            $v['initial_amount']        = price_format($v['initial_price'] * $v['goods_number'], 0, 2);
            $v['initial_amount_format'] = price_format($v['initial_price'] * $v['goods_number'], $v['currency'], 2);
            $v['diff_price']            = price_format($v['create_goods_price'] - $v['goods_price'], 0, 4);
            $v['diff_price_format']     = price_format($v['diff_price'], $v['currency'], 4);
            $v['create_time']           = date('Y-m-d H:i:s', $v['create_time']);
            $v['update_time']           = date('Y-m-d H:i:s', $v['update_time']);
            $v["activity_info"]         = isset($info["activity_info"]) ? $info["activity_info"] : null;
            $v['goods_tag']             = !empty($info['goods_tag']) ? $info['goods_tag']['goods_label_name'] : '';
            $v["goods_images"] = $info['goods_images'] ? $info['goods_images'] : 'http://static.ichunt.com/dist/res/home/<USER>/goods_default.png';
            $v['source'] = $info['source'];

            // 若为寄售商品，则将商品类型设置为寄售
            if ($info['source'] == 12) {
                $v['goods_type'] = 4;
            }

            // if ($v["standard_brand_id"] > 0){ //todo 2023.2.1 标准品牌
            //     $v["brand_id"] = $v["standard_brand_id"];
            //     $v["brand_name"] = $v["standard_brand_name"];
            // }

            $goods_total += (float)price_format($v['goods_price'] * $v['goods_number'], 0, 2);//单独4位小数的小计之和

            //统计自营商品小计之和
            if (intval($_order_goods_type) == 2) {
                $zy_goods_total += (float)price_format($v['goods_price'] * $v['goods_number'], 0, 2);//单独4位小数的小计之和  统计自营商品小计之和
            }

            //统计自营现卖总金额
            if (intval($_order_goods_type) == 2 && $_sale_type == 1) {
                $zyxianmai_goods_total += (float)price_format($v['goods_price'] * $v['goods_number'], 0, 2);//单独4位小数的小计之和  统计自营商品小计之和
            }

            //统计联营商品小计之和
            if (intval($_order_goods_type) == 1) {
                $ly_goods_total += (float)price_format($v['goods_price'] * $v['goods_number'], 0, 2); //单独4位小数的小计之和  统计联营商品小计之和
            }

            if ($v['goods_type'] == 6) {
                $is_exists_mro = 1;
                $mro_goods_total += (float)price_format($v['goods_price'] * $v['goods_number'], 0, 2); //统计京东MRO商品小计之和
            }

            //限购的商品 添加限购字段
            try{
                $goods_data = json_decode($v['goods_data'],true);
                $v['is_quota'] = isset($goods_data["is_quota"]) ? $goods_data["is_quota"] : 0;
                $v['quota_num'] = isset($goods_data["quota_num"]) ? $goods_data["quota_num"] : 0;
            }catch(\Exception $e){}
            unset($v['goods_data']);
        }

        //只有大陆交货订单可以使用优惠券
        if ($currency == 1 && $allow_coupon != 2) {
            try {
                //todo::优惠券
                if (empty($user_coupon_id)) {//没选过 则推荐一个最优的
                    $coupon = $this->getBestCoupon($cart_ids);//优惠券使用的商品,商品类型(1联营 2自营)
                } else {//选过了 可重选
                    if ($type) {
                        $coupon = $this->getCouponByOrder($uid, $user_coupon_id, json_encode([$zy_goods_total,$ly_goods_total]), $cart_ids);// 订单后台选择使用的券
                    } else {
                        $coupon = $this->getCoupon($user_coupon_id, json_encode([$zy_goods_total,$ly_goods_total]), $cart_ids);//选择使用的券
                    }
                }
//                var_dump($coupon);
                if ($coupon['err_code'] == 0) {//存在使用优惠券
                    $coupon = $coupon['data'];
                    $user_coupon_id = $coupon['user_coupon_id'];
                    if ($coupon['coupon_type'] == 1) {
                        $coupon_price = $coupon['sale_amount'];
                    } else {
                        $coupon_price = $coupon['preferential'];
                    }
                } else {//未使用优惠券
                    $user_coupon_id = '0';
                }
            } catch (\Exception $e) {
            }
        } else {
            $user_coupon_id = '0';
        }

        if ($activity_price > 0) {
            $res = $this->getOrderAcivityLimit();
            if ($res['errcode'] === 0 && $res['data'] < $activity_price) {
                $activity_price = $res['data'];
            }
        }

        //商品总优惠金额
        $preferential_price = $coupon_price + $activity_price ;
        //物流价格
        $shipping_price = price_format(0);
        $address_free_shipping = true;//可包邮地区
        $isAllZYXianMai = $this->checkOrderListIsZYXM($data);//是否全是自营现卖商品
        $isAllZY = $this->checkOrderListIsZY($data);//是否全是自营商品
//        $zy_delivery_type =  I("send_type",2,"intval");//1 自营优先发货  2拼单发货
//        if ($isAllZYXianMai){
//            //如果商品全部都是自营现卖 则默认只能是自营优先发货 需要收取运费
//            $zy_delivery_type = 1;
//        }
        if ($isAllZY && $zy_goods_total >= 0 && $business_type != 2) { //自营需要判断运费 (仓库损耗不需要运费)
           $this->getShippingPrices($address_id,$type,$uid,$shipping_type,$zy_goods_total,$address_free_shipping,
               $shipping_price,$preferential_price,$free_shipping_price);
        }
        $free_shipping_price = price_format($free_shipping_price);

        // 若存在京东MRO商品且物流方式为快递，则获取MRO运费
        $mro_shipping_fee = 0;
        if ($is_exists_mro && $shipping_type == 1) {
            $mro_shipping_fee = $this->getMroShippingFee($address_id, $type, $uid, $mro_goods_total);
            $shipping_price += $mro_shipping_fee;
        }

        //商品总价
        $goods_total = price_format($goods_total, 0, 2);
        //总商品优惠价格
        $preferential_price = price_format($preferential_price);

        // 订单金额 (后台内部采购下单、仓库损耗不需要运费)
        if (($type == 3 || $business_type == 2 || $is_free_ship) && !$is_exists_mro) {
            $amount = orderAmount($goods_total, $preferential_price);
        } else {
            $amount = orderAmount($goods_total, $preferential_price, $shipping_price, $free_shipping_price, 0);
        }

        //***********二次调整-货款价格最低价限制***********//
        $this->minOrderConfirm($data, $amount);
        //***********三次调整-根据供应商设置附加费*********//
        $extend_items = $this->getItemsGroupFee($data, $amount, $extend_merge);//补充extend_price

        $datas = page_data($data);
        $datas = array_merge($datas, $amount);

        $datas['address_free_shipping'] = $address_free_shipping;
        $datas['mkt_point'] = conver_mkt_point($ly_goods_total, 1, $currency) + conver_mkt_point($zy_goods_total, 2, $currency);//计算积分
        $datas['allow_coupon'] = $allow_coupon;//是否允许优惠券
        $datas['extend_items'] = $extend_items;//附加费组成
        $datas['order_goods_type'] = 0;//生成订单类型 该字段作废 20200818
        $datas['sale_type'] = 0;//销售方式  不生效 该字段作废 20200818
        $datas['user_coupon_id'] = $user_coupon_id;//使用优惠券ID
        $datas['coupon_price'] = $coupon_price;//优惠券优惠金额
        $datas['activity_price'] = $activity_price;//活动优惠金额
        $datas["zy_goods_total"] = $zy_goods_total;//自营总价格
        $datas["zy_goods_total_format"] = price_format($zy_goods_total, $currency);//自营总价格
        $datas["ly_goods_total"] = $ly_goods_total;//联营总价格
        $datas["ly_goods_total_format"] = price_format($ly_goods_total,$currency); //联营总价格
        $datas["mro_goods_total"] = $mro_goods_total; //MRO总价格
        $datas["mro_goods_total_format"] = price_format($mro_goods_total, $currency); //MRO总价格
        $datas["mro_shipping_fee"] = $mro_shipping_fee; //MRO运费
        $datas['coupon_price_format'] = price_format($coupon_price, $currency);//优惠券优惠金额
        $datas['activity_price_format'] = price_format($activity_price, $currency);//活动优惠金额
        $datas['goods_total_format'] = price_format($datas['goods_total'], $currency, 2);
        $datas['preferential_price_format'] = price_format($datas['preferential_price'], $currency);
        $datas['order_amount_format'] = price_format($datas['order_amount'], $currency);
        $datas['extend_fee_format'] = price_format($datas['extend_fee'], $currency);
        $datas['finally_shipping_price_format'] = price_format($datas['shipping_price'] - $datas['free_shipping_price'], $currency);//最终优惠后运费
        $datas['isAllZYXianMai'] = $isAllZYXianMai;
        $datas['currency'] = $currency; // 币种
        $datas['is_exists_mro'] = $is_exists_mro;

        if ($currency == 2) { // 若是香港订单，则获取合同乙方公司名称
            $datas['contract_com_name'] = $this->getContractComName($uid);
        }

        return $this->apiReturn(0, '获取成功', $datas);
    }

    /**
     * 获取合同乙方公司名称
     * 优先取本会员账号最新的香港订单公司名称，如无下单记录，则取本会员账号基本信息维护好的公司名称，都取不到值就留空
     * */
    public function getContractComName($user_id)
    {
        $contract_com_name = D('Order')->alias('o')
                            ->join('LEFT JOIN ' . C('DB_PREFIX') . 'order_contract oc ON o.order_id = oc.order_id')
                            ->where(['o.user_id' => $user_id, 'o.currency' => 2])
                            ->order('oc.sc_id DESC')
                            ->getField('oc.contract_com_name');

        if ($contract_com_name) return $contract_com_name;

        $com_info = S_company($user_id);

        if ($com_info) return $com_info['com_name'];

        return '';
    }

    /*
     * //检查商品中是否全都是自营现卖的商品
     * 自营现卖的商品需要跳转到支付
     */
    protected function checkOrderListIsZYXM($datas){
        $zy_xianmai = true;
        foreach ($datas as $v) {
            //判断是否全都是自营现卖
            $checkzixm = intval($v["order_goods_type"]) != 2 || (intval($v["order_goods_type"]) == 2 && intval($v["sale_type"]) != 1 );
            if($checkzixm){
                $zy_xianmai = false;
                break;
            }

        }
        return $zy_xianmai;
    }

    protected function checkOrderListIsZY($datas){
        $zy = true;
        foreach ($datas as $v) {
            //判断是否全都是自营现卖
            if(intval($v["order_goods_type"]) != 2 ){
                $zy = false;
                break;
            }

        }
        return $zy;
    }

    /*
     * 订单确认页面 获取自营的运费
     * return
     * $address_free_shipping  $shipping_price   $preferential_price  $free_shipping_price
     */
    protected function getShippingPrices($address_id,$type,$uid,$shipping_type,$goods_total,&$address_free_shipping,
                                          &$shipping_price,&$preferential_price,&$free_shipping_price)
    {

        if (!empty($address_id)) {
            if ($type) { // 订单后台新增
                $address_info = $this->getAddressByOrder($uid, $address_id);
            } else {
                $address_info = $this->getAddress($address_id);
            }

            $address = $address_info['data'];
        } else {
            $address_info = $this->getDefaultAddress($uid, 0);
            $address = $address_info['data'];
        }
        if ($shipping_type == 2) {//自提
//            $address = array(//以本地价收取费用
//                'province' => C('LOCATION_PROVINCE_ID'),
//                'city' => C('LOCATION_CITY_ID'),
//            );
            $address_free_shipping=false;
            $shipping_price = 0;
            $free_shipping_price=0;
        }else{
            $address_free_shipping = empty(C('WITHOUT_FREE_SHIPPING_PROVINCE.' . $address['province']));//是否可包邮地区
        }

        if ($address_free_shipping) {
            $shipping_price = price_format(get_shipping_price($address['province'], $address['city']));
            //包邮开关  并且 属于可包邮省份
            if (C('SELF_ORDER_FREE_SHIPPING')) {
                $floor = C('FREE_SHIPPING_FLOOR');//包邮金额下限
                if ($goods_total - $preferential_price >= $floor) {
                    $free_shipping_price += $shipping_price;
                }
            }
        }else{
            if($shipping_type == 1) {
                $address_free_shipping = true;
                $shipping_price = C("WITHOUT_FREE_SHIPPING_PROVINCE_PRICE");
                //包邮开关  并且 属于可包邮省份
                if (C('SELF_ORDER_FREE_SHIPPING')) {
                    $floor = C('FREE_SHIPPING_FLOOR');//包邮金额下限
                    if ($goods_total - $preferential_price >= $floor) {
                        $free_shipping_price += $shipping_price;
                    }
                }
            }

        }
    }

    // 获取京东MRO运费
    public function getMroShippingFee($address_id, $type, $uid, $mro_goods_total)
    {
        if (!empty($address_id)) {
            if ($type) { // 订单后台新增
                $address_info = $this->getAddressByOrder($uid, $address_id);
            } else {
                $address_info = $this->getAddress($address_id);
            }

            $address = $address_info['data'];
        } else {
            $address_info = $this->getDefaultAddress($uid, 0);
            $address = $address_info['data'];
        }

        $fee = 0;

        if (!$address['province']) {
            return $fee;
        }

        // 判断是否属于特殊地区 MRO_SPECIEL_SHIPPING_PROVINCE
        $is_belongs_speciel_province = !empty(C('MRO_SPECIEL_SHIPPING_PROVINCE.' . $address['province'])) ? 1 : 0;

        // 特殊地区 且 MRO商品金额小于500，运费50
        if ($is_belongs_speciel_province && $mro_goods_total < C('MRO_SHIPPING_AMOUNT_LIMIT')) {
            $fee = C('MRO_SPECIEL_SHIPPING_PROVINCE_PRICE');
        }

        // 非特殊地区 且 MRO商品金额小于500，运费15
        if (!$is_belongs_speciel_province && $mro_goods_total < C('MRO_SHIPPING_AMOUNT_LIMIT')) {
            $fee = C('MRO_SHIPPING_PROVINCE_PRICE');
        }

        return $fee;
    }

    // 订单后台切换销售类型
    public function switchSaleType()
    {
        $sale_type = I('sale_type', 1, 'intval');
        $cart_ids = I('cart_ids');
        $ShoppingCartModel = D('ShoppingCart');

        foreach ($cart_ids as $v) {
            $ShoppingCartModel->where(['cart_id' => $v])->data(['sale_type' => $sale_type])->save();
        }

        return $this->apiReturn(0, '切换成功', '');
    }

    /**
     * 判断货款金额过低设置最低售价
     * @param  [type] &$amount [description]
     * @param  [type] &$items  [description]
     * @return null
     */
    private function minOrderConfirm(&$items, &$amount = array())
    {
        $price = 0.1;
        // 货款 < 0.1 并且 货款 > 0.0001
        if (bccomp($amount['goods_total'], $price, 4) < 0 && bccomp($amount['goods_total'], 0, 4) > 0) {
            //明细第一条调整
            $count = count($items);
            if ($count > 1) {
                //除了第一条明细，其他的商品总额
                $goods_amount = $amount['goods_total'] - price_format($items[0]['goods_number'] * $items[0]['goods_price'], 0, 4);
                $surplus = $price - $goods_amount;
                $goods_price = price_format($surplus / $items[0]['goods_number'], 0, 6);
            } else {
                $goods_price = price_format($price / $items[0]['goods_number'], 0, 6);
            }
            $items[0]['goods_price'] = $goods_price;
            $items[0]['goods_price_format'] = price_format($goods_price, $items[0]['currency'], 4);
            $items[0]['goods_amount_format'] = price_format($price, $items[0]['currency'], 4);
            $items[0]['diff_price'] = price_format($items[0]['create_goods_price'] - $price, 0, 4);
            $items[0]['diff_price_format'] = price_format($items[0]['diff_price'], $items[0]['currency'], 4);

            //总价调整
            $amount = orderAmount($price, $amount['preferential_price'], $amount['shipping_price'], $amount['free_shipping_price']);
        }

        // 订单金额 < 0.1 并且 订单金额 > 0.0001 (原因使用了优惠券或其他优惠促销) 目前不处理
    }

    /**
     * 获取各供应商明细的附加费
     * @param  [type] &$items     [description]
     * @param  array  &$amount    [description]
     * @param  bool   $merge_supp 是否合并显示专卖供应商（不显示阶梯）
     * @return [type]          返回附加费组成部分
     */
    public function getItemsGroupFee(&$items, &$amount = array(), $merge_supp = false)
    {
        $supplier = array();
        $extend_fee = 0;
        $currency = isset($items[0]['currency']) ? $items[0]['currency'] : 1;
        //分类
        foreach ($items as &$v) {
            //跳过自营
            if($v["order_goods_type"] == 2) continue;
            //通过SKU判断供应商是否专卖
            $key = getSuppKey($v['supplier_id'], $v['canal'], $v['goods_type']);
            $supplier[$key]['supplier_name'] = $v['supplier_name'];
            $supplier[$key]['amount'] += $v['goods_price'] * $v['goods_number'];
            $supplier[$key]['extend_fee'] = 0;
            $supplier[$key]['extend_fee_format'] = price_format($supplier[$key]['extend_fee'], $currency);
            $supplier[$key]['count'] += 1;
        }
        //判断价格
        foreach ($supplier as $k => &$v) {
            //附加费阶梯
            $ladders = S_suppfee($k);
            if (empty($ladders) && $merge_supp) {
                continue;
            }
//            dump($ladders);
            //附加费转美元
            if (!isset($rate) && $currency == 2) {
                $rate = $this->getErpExchangeRate($currency, $_REQUEST['REQUEST_TIME']);
                empty($rate) && $rate = 1;
            } else {
                $rate = 1;
            }
            $suppfee = $ladders[$this->currencyTOsuppfee[$currency]];
            $max = $suppfee["max"];
            $price = $suppfee["price"];
            if ($max > $v['amount']) {
                $v['ladder_max'] = $max;
                $v['extend_fee'] += $price;
                $extend_fee += $price;
            }


            //满足免附加费条件
            if ($v['extend_fee'] <= 0 && $merge_supp) {
                continue;
            }
            if ($merge_supp && strpos($k, '.') !== false) {
                list($supp_id, $canal) = explode('.', $k, 2);
                $data[$supp_id]['supplier_name'] = $v['supplier_name'];
                $data[$supp_id]['amount'] += $v['amount'];
                $data[$supp_id]['extend_fee'] += $v['extend_fee'];
                $data[$supp_id]['extend_fee_format'] = price_format($data[$supp_id]['extend_fee'], $currency);
                $data[$supp_id]['count'] = $v['count'];
            } else {
                $v['extend_fee_format'] = price_format($v['extend_fee'], $currency);
                $data[$k] = $v;
            }
        }

        if ($extend_fee > 0) {
            //总价调整
            $amount = orderAmount($amount['goods_total'], $amount['preferential_price'], $amount['shipping_price'], $amount['free_shipping_price'], $extend_fee);
        }
        return $data;
    }

     /**
      * 获取订单中的商品(展示可用优惠券内部调用)  作废XXXXXXXXXXXXXXXX
      */
     public function getConfirmGoods()
     {
         $cart_ids = I('cart_id',0);
//         $uid = $this->auth() ? I('request.uid', 0, 'intval') : cookie('uid');//内部调用
         $uid = I('uid',0);
         $ShoppingCartModel = D('ShoppingCart');
         $map = array(
             'user_id' => $uid,
             'cart_id_arr' => explode(',', $cart_ids),
             'status' => 1
         );


         $data = CartService::getCartList($map,'create_time DESC');
         if (empty($data)) {
             return $this->apiReturn(24001, '购物车内无有效商品', $map);
         }
         $goods_total = 0;
         $delivery_place = 0;
         $currency = 0;
//         $words = array();
         foreach ($data as $k => &$v) {
             //第一条的数据，防止一个订单混合交货地
             empty($delivery_place) && $delivery_place = $v['delivery_place'];
             empty($currency) && $currency = $v['currency'];
             if ($delivery_place != $v['delivery_place']) {//跳过不同交货地商品
                 unset($data[$k]);
                 continue;
             }

             $v['goods_amount'] = price_format($v['goods_price'] * $v['goods_number'], 0, 4);
             $goods_total += price_format($v['goods_price'] * $v['goods_number'], 0, 4);//单独4位小数的小计之和
             unset($v['goods_data']);
         }
         //商品总价
         $goods_total = price_format($goods_total, 0, 4);

         $datas = page_data($data);
         $datas['goods_total'] = $goods_total;
//         $datas['goods_total_format'] = price_format($datas['goods_total'], $currency, 4);
         return $this->apiReturn(0, '获取成功', $datas);
     }

    /**
     * 选择优惠券
     * @return [type] [description]
     */
    public function selectCoupon()
    {
        // $user_coupon_id = I('request.user_coupon_id', 0, 'intval');
        // $cart_id = I('request.cart_id', '');
        // $uid = $this->auth() ? I('request.uid', 0, 'intval') : cookie('uid');//内部调用

        // $ShoppingCartModel = D('ShoppingCart');
        // $map = array(
        //     'user_id' => $uid,
        //     'cart_id' => array('in', $cart_id),
        //     'status' => 1
        // );
        // $data = $ShoppingCartModel->getList($map, null, 'create_time DESC', 'cart_id, goods_number, goods_price, currency, delivery_place');
        // $goods_total = 0;
        // $delivery_place = 0;
        // $currency = 0;
        // foreach ($data as $k => &$v) {
        //     //第一条的数据，防止一个订单混合交货地
        //     empty($delivery_place) && $delivery_place = $v['delivery_place'];
        //     empty($currency) && $currency = $v['currency'];
        //     if ($delivery_place != $v['delivery_place']) {//跳过不同交货地商品
        //         unset($data[$k]);
        //         continue;
        //     }
        //     $goods_total += price_format($v['goods_price'] * $v['goods_number'], 0, 4);//单独4位小数的小计之和
        // }

        // //选择优惠券
        // $coupon = $this->getCoupon($user_coupon_id, $goods_total);
        // if ($coupon['err_code'] != 0) {
        //     return $this->apiReturn($coupon['err_code'], $coupon['err_msg']);
        // }
        // $coupon = $coupon['data'];
        // //判断优惠券优惠方式
        // if ($coupon['coupon_type'] == 1) {
        //     $preferential_price = $coupon['sale_amount'];
        // } else {
        //     $preferential_price = $goods_total * $coupon['sale_amount'];
        // }
    }

    // 订单后台获取购物车列表  作废XXXXXXXXXXXXXXXX
    public function listsByOrderSystem()
    {
        cookie('gid', '');
        $data['org_id'] = I('org_id', 1);
        $data['uid'] = I('uid');
        $data['type'] = 2; // 订单后台
        // $data['order_goods_type'] = I('type'); // 区分联营、自营
        $data['delivery_place'] = I('delivery_place');
        $data['currency'] = I('currency', 1);
        $data['is_new'] = I('is_new', 0); // 用于新的后台下单页面

        $data['k1'] = time();
        $data['k2'] = pwdhash($data['k1'], C('SUPER_AUTH_KEY'));

        $res = post_curl(API_DOMAIN.'/cart/lists', $data);

        print_r($res);
    }

    /**
     * 获取购物车列表
     * @param  integer  delivery_place       交货地 1大陆，2香港
     * @return [type] [description]
     */
    public function lists()
    {
        $gid = cookie('gid');
        $uid = $this->getCartUid();
        
        $filter = array('delivery_place', 'sale_type');
        $map = map_filter($filter, array(), function($data, $arr){
            //自营，联营标签
//            if ((isset($data['order_goods_type']) && $data['order_goods_type'] == 1) || empty($data['order_goods_type'])) {
//                $arr['goods_type'] = array('in', C('ORDER_GOODS_TYPE_MAP.1'));//联营、专卖
//            } else {
//                $arr['goods_type'] = array('in', C('ORDER_GOODS_TYPE_MAP.2'));//自营、寄售
//                $arr['delivery_place'] = 1;
//            }
            //默认交货地大陆
            $delivery_place = isset($arr['delivery_place']) ? intval($arr['delivery_place']) : 1;
            switch ($delivery_place){
                case 1://大陆
                     $arr['delivery_place'] = 1;
                    break;
                case 2 ://香港
                     $arr['delivery_place'] = 2;
                     break;
                case 12: //泰国
                    $arr['delivery_place'] = 12;
                    break;
                case 13: //越南
                    $arr['delivery_place'] = 13;
                    break;
                default://大陆
                    $arr['delivery_place'] = 1;
                    break;
            }

            return $arr;
        });

        if (!empty($uid)) {
            $map['user_id'] = $uid;
        } else {
            $map['user_sign'] = $gid;
        }

        // $map['org_id'] = I('org_id', 1);

        // 获取后台添加订单
        $type = I('type');
        $map['type'] = $type ? [2, 3, 4] : [1]; // 2-后台单个添加 3-批量添加商品 4-导入销售报价

        if ($type) {
            $map['currency'] = I('currency', 1);
        }

        $count_self = $this->count(2);
        $count_cn = $this->count(1, 1);
        $count_hk = $this->count(1, 2);
        $ShoppingCartModel = D('ShoppingCart');
        
        //先更新数据
        // $update_list = $ShoppingCartModel->where($map)->order('create_time DESC, cart_id DESC')->field('cart_id, goods_type, type')->select();
        $update_list = CartService::getCartList($map,'create_time DESC, cart_id DESC');

        foreach ($update_list as &$v) {
            if ($v['type'] != 1) continue; // 后台系统添加的不更新

            $this->update($v['cart_id']);
        }

        // $data = $ShoppingCartModel->getList($map, null, 'status DESC,create_time DESC', 'cart_id, goods_id, goods_name, sku_name, brand_id, standard_brand_id, brand_name,
        //  standard_brand_name, supplier_id, supplier_name, goods_number, goods_price, create_goods_price, initial_price, goods_type, sale_type, status,
        //  currency, delivery_place, goods_data, change_place, create_time, type, update_time, buyer_id, batch,remarks, is_gift, is_purchase, is_provide_dc,is_vacuo,
        //   is_provide_producer, customer_material_number');
          $data = CartService::getCartList($map,'status DESC,create_time DESC');
        if (empty($data)) {
            $datas['cn_count'] = $count_cn['data'];
            $datas['hk_count'] = $count_hk['data'];
            return $this->apiReturn(-24001, '购物车内无商品', $datas);
        }
        $words = array();
        $has_new_client_activity = false;
        $CmsModel = D('Cms');
        foreach ($data as &$v) {
            $info = json_decode($v['goods_data'], true);
            $activity_info = isset($info["activity_info"]) ? $info["activity_info"] : [];
            //供应商的营销词
            if ($type == 1 && !isset($words[$v['supplier_id']])) {
                $word = $this->getAdWord($v['supplier_id']);

                if ($word['err_code'] == 0) {
                    $v['ad_word'] = $word['data'];
                    $words[$v['supplier_id']] = $v['ad_word'];
                }
            }

            $v['ad_word'] = isset($words[$v['supplier_id']]) ? $words[$v['supplier_id']] : '';

            if ($v['sale_type'] == 1 && in_array($v["goods_type"],[3,4])) {
                //自营现卖
                $delivery_time = "现卖";
            }else{
                //联营和预售 显示商品的货期
                $delivery_time = is_array($info['delivery_time']) ? $info['delivery_time'][$v['delivery_place']] : $info['delivery_time'];
            }

            //如果是ac_type = 8 的活动价 除了下单页面 其它地方都显示原始价格 不显示优惠价格
            $user_scope_bool = !empty($activity_info) && isset($activity_info["user_scope"]) && $activity_info["user_scope"] == 2;
            if( $user_scope_bool && !$this->checkLoginByCart()){
                //会员价  如果商品设置了需要登录才能查看   1则不需要登陆,2是要登陆
                $v["goods_price"] = $v["initial_price"];
                $v['goods_price_format'] = price_format($v['goods_price'], $v['currency'], 4);
            }

            // $ladder = ladder_transform($info['tiered'], $v['supplier_id'], $v['goods_type'], $info['ratio'], $info['ac_type']);
            $v['tiered'] = $info['tiered'];
            $v['min_mpq'] = $info['min_mpq'];
            $v['min_buy'] = $info['min_buy'];
            $v['encap'] = $info['encap'];
            $v['stock'] = $info['goods_number'];//库存
            $v['supplier_stock'] = $info['supplier_stock'];
            $v['mpl'] = $info['mpl'];//自营的mpl，联营没有mpl
            $v['multiple'] = $info['multiple'];//联营特用，类似mpl
            $v['ac_type'] = $info['ac_type'];
            $v['delivery_time'] = $delivery_time;
            $v['create_time'] = date('Y-m-d H:i:s', $v['create_time']);
            $v['update_time'] = date('Y-m-d H:i:s', $v['update_time']);
            $v['diff_price'] = price_format($v['create_goods_price'] - $v['goods_price'], 0, 4);
            $v['initial_price_format'] = price_format($v['initial_price'], $v['currency'], 4);
            $v['goods_price_format'] = price_format($v['goods_price'], $v['currency'], 4);
            $v['diff_price_format'] = price_format($v['diff_price'], $v['currency'], 4);
            $v['goods_amount_format'] = price_format($v['goods_price'] * $v['goods_number'], $v['currency'], 2);
            $v['initial_amount_format'] = price_format($v['initial_price'] * $v['goods_number'], $v['currency'], 2);
            $v["activity_info"] = isset($info["activity_info"]) ? $info["activity_info"] : null;
            $v['goods_tag'] = !empty($info['goods_tag']) ? $info['goods_tag']['goods_label_name'] : '';
            $v["goods_images"] = $info['goods_images'] ? $info['goods_images'] : 'http://static.ichunt.com/dist/res/home/<USER>/goods_default.png';
            $v['source'] = $info['source'];

            unset($v['goods_data']);
            if ($v['ac_type'] == 5) {
                $has_new_client_activity = true;
            }

            if ($v['buyer_id']) {
                $v['buyer_name'] = $CmsModel->getUserName($v['buyer_id']);
            }

            // if ($v["standard_brand_id"] > 0){ //todo 2023.2.1 标准品牌
            //     $v["brand_id"] = $v["standard_brand_id"];
            //     $v["brand_name"] = $v["standard_brand_name"];
            // }

            //混淆数据
            !$this->auth() && $v = dataConfuse($v);
        }

        // 根据cart_id排序商品
        array_multisort(array_column($data, 'cart_id'), SORT_DESC, $data);

        //活动优惠限制金额
        $activity_limit = 0;
        if ($has_new_client_activity) {
            $res = $this->getOrderAcivityLimit();
            if ($res['errcode'] === 0) {
                $activity_limit = $res['data'];
            }
        }
        $datas = page_data($data);
        $datas['activity_limit'] = $activity_limit;
        $datas['self_count'] = $count_self['data'];
        $datas['cn_count'] = $count_cn['data'];
        $datas['hk_count'] = $count_hk['data'];

        $is_new = I('is_new', 0);
        if (!$is_new && in_array($type, [2, 3])) $datas['cart_gift'] = $this->getCartMZInfo(array_column($data, 'cart_id')); // 订单后台添加购物车，获取赠品信息

        return $this->apiReturn(0, '获取成功', $datas);
    }

    // 后台获取订单赠品
    public function getOrderGift()
    {
        $cart_ids = I('cart_ids');

        if (empty($cart_ids)) return $this->apiReturn(25101, '获取赠品失败，无购物车ID');

        $res = $this->getCartMZInfo($cart_ids);

        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }


    protected function  checkLoginByCart(){
        return cookie("uid") && cookie("lskey") && cookie("skey");
    }

    // /**
    //  * 价格阶梯乘以系数转换
    //  * @param  [type] $tiered      阶梯数组
    //  * @param  [type] $supplier_id 供应商ID
    //  * @param  [type] $goods_type  商品类型
    //  * @return [type]              [description]
    //  */
    // private function ladder_transform($tiered, $supplier_id, $goods_type, $discount_ratio)
    // {
    //     $supplier = getSupplier($supplier_id);
    //     $coeff = current($supplier['price_json']);
    //     foreach ($tiered as &$val) {
    //         if (isset($val['price_cn']) || isset($val['price_us'])) {//新格式
    //             $hk = $val['price_us'];
    //             $cn = $val['price_us'];
    //             if (in_array($goods_type, array(1,2))) {//联营处理价格系数
    //                 if (!empty($coeff)) {
    //                     $hk = $hk * $coeff['hk'];
    //                     $cn = $cn * $coeff['cn'];
    //                 }
    //                 if (!empty($discount_ratio) && $discount_ratio != 100) {
    //                     $ratio = $discount_ratio / 100;
    //                     $hk_ac = $hk * $ratio;
    //                     $cn_ac = $cn * $ratio;
    //                     // $v['price_us_ac'] = price_format($hk_ac, 0, 4);//目前不支持美元活动
    //                     $val['price_ac'] = price_format($cn_ac, 0, 4);
    //                 }
    //             }
    //             $val['price_us'] = price_format($hk, 0, 4);
    //             $val['price_cn'] = price_format($cn, 0, 4);

    //         } else {//兼容旧格式上线1周后可废除
    //             if (in_array($goods_type, array(1,2))) {//联营处理价格系数
    //                 if (!empty($coeff)) {
    //                     $hk = price_format($val[1] * $coeff['hk'], 0, 4);
    //                     $cn = price_format($val[1] * $coeff['cn'], 0, 4);
    //                     $val[1] = $hk;
    //                     $val[2] = $cn;
    //                 }
    //             }
    //             $val['price_us'] = price_format($val[1], 0, 4);
    //             $val['price_cn'] = price_format($val[2], 0, 4);
    //             unset($val[1], $val[2]);
    //         }
    //     }
    //     return $tiered;
    // }

    /**
     * 自提地址信息
     * @return [type] [description]
     */
    public function selfAddress()
    {
        $order_goods_type = I('order_goods_type', 1, 'intval');
        $currency = I('currency', 1, 'intval');

        if ($order_goods_type == 1) {
            $datas = $currency == 1 ? C('SELF_PICK_ADDRESS') : C('HK_SELF_PICK_ADDRESS');
        } else {
            $datas = C('S_SELF_PICK_ADDRESS');
        }
        foreach ($datas as $k => &$v) {
            $data[$k] = array(
                'address' => $v[0],
                'phone' => $v[1],
                'worktime' => $v[2],
            );
        }
        return $this->apiReturn(0, '成功', $data);
    }

    /**
     * 更变交货地(合并已有商品)
     * @param  integer  cart_id      购物车商品ID
     * @return [type] [description]
     */
    public function change()
    {
        $gid = cookie('gid');
        $uid = $this->getCartUid();
        $cart_ids = I('request.cart_id', '');

        $cart_ids = explode(',', $cart_ids);

        if (empty($cart_ids)) {
            $this->apiReturn(24015, '请选择购物车商品');
        }

        if (empty($uid) && empty($gid)) {
            return $this->apiReturn(24013, '未找到当前用户标识');
        }

        $ShoppingCartModel = D('ShoppingCart');
        // $ShoppingCartModel->startTrans();

        $cartList = [];
        foreach ($cart_ids as $cart_id) {
            $info = $ShoppingCartModel->getInfo($cart_id);
            $cartList[$cart_id] = $info;
            if ($info['user_id'] != $uid && $info['user_sign'] != $gid) {
                return $this->apiReturn(24005, '未找到相关商品信息');
            }
            if ($info['change_place'] != 1) {
                return $this->apiReturn(24008, '商品不支持更换交货地');
            }
            
        }

        foreach ($cart_ids as $cart_id) {
            $info = !empty($cartList[$cart_id])? $cartList[$cart_id] : [];
            if(empty($info)) continue;
            //更换交货地
            $delivery_place = $info['delivery_place'] == 1 ? 2 : 1;
            $currency = C('PLACE_CURRENCY_MAPPING.' . $delivery_place);

            //目标交货地已存在相同商品
            $has = CartService::getUserGoods([
                'goods_id'=> $info['goods_id'],
                'user_id'=> $uid,
                'user_sign'=> $gid,
                'delivery_place'=> $delivery_place,
            ]);
            // $has = $ShoppingCartModel->getUserGoods($info['goods_id'], $gid, $uid, array('delivery_place' => $delivery_place));
            // //以更新时间近的为准
            // if (!empty($has) && $has['update_time'] > $info['update_time']) {
            //     $data = json_decode($has['goods_data'], true);
            // } else {
            //     $data = json_decode($info['goods_data'], true);
            // }

            $num = intval($has['goods_number']) + $info['goods_number'];

            $use_ac = $this->isCanAc($uid);
            $res = $this->getFinalGoods($info['goods_id'], $num, $currency, $use_ac);
            if ($res['err_code'] !== 0) {
                return $this->apiReturn(-1, '商品已下架');
            }
            $data = $res['data'];
            if (intval($data['goods_info']['goods_number']) < $num) {
                return $this->apiReturn(24006, '购物车商品数量已超过现有库存');
            }

            //价格计算
            $goods_prices = ladder_final_price($data, $currency, $data['goods_info']['ac_type']);
            $initial_prices = ladder_final_price($data, $currency, false);

            $save = array(
                'cart_id' => $cart_id,
                'goods_price' => $goods_prices['price'],
                'create_goods_price' => $goods_prices['price'], //更换交货地重新计算添加价格
                'initial_price' => $initial_prices['price'],
                'goods_number' => $num,
                'currency' => $currency,
                'delivery_place' => $delivery_place,
                'goods_data' => json_encode($data['goods_info']),
            );

            // $res = $ShoppingCartModel->save($save);
            $res = CartService::updateCart($save);
            if ($res === false) {
                return $this->apiReturn(24019, '更换交货地失败，请稍后重试');
            }

            //删除目标交货地已有商品
            if (!empty($has)) {
                $res = $this->delete($has['cart_id']);
                // $res = CartService::delCartByCartId($has['cart_id']);
                if ($res['err_code'] != 0) {
                    // $ShoppingCartModel->rollback();
                    return $this->apiReturn($res['err_code'], '合并删除已有失败，请稍后重试');
                }
            }
        }

        // $ShoppingCartModel->commit();

        $this->setCount();
        return $this->apiReturn(0, '更换成功');
    }

    /**
     * 获取用户购物车数量
     * @param  integer  delivery_place       交货地 1大陆，2香港 0合计
     * @return [type] [description]
     */
    public function count($order_goods_type = null, $delivery_place = null)
    {
        $gid = cookie('gid');
        $uid = $this->getCartUid();
//        is_null($order_goods_type) && $order_goods_type = I('requset.order_goods_type', 1, 'intval');
        is_null($delivery_place) && $delivery_place = I('request.delivery_place', 0, 'intval');
        // $this->setCount();
        // $count_arr = S_cart_num($uid, $gid);
        $count_arr = S_cart_num($uid, $gid);
//        p($count_arr);
        if (empty($count_arr)) {
            $this->setCount();
            $count_arr = S_cart_num($uid, $gid);
        }
        $count = 0;
        if (empty($delivery_place)){
            //所有  自营+联营(大陆+香港)   1联营   2自营
            foreach($count_arr as $v){
                $count += array_sum($v);
            }
        }else{
            //大陆或者香港
            switch ($delivery_place){
                case 1:
                    //所有自营(大陆) +  联营大陆
                    $count +=  array_sum($count_arr[2]) + $count_arr[1][1];
                    break;
                case 2:
                    //联营香港
                    $count +=   $count_arr[1][2];
                    break;
            }
        }
//        if (empty($order_goods_type)) {//全部数量
//            foreach ($count_arr as $v) {
//                $count += array_sum($v);
//            }
//
//        } else {
//            if (empty($delivery_place)) {//全部交货地
//                $count = array_sum($count_arr[$order_goods_type]);
//            } else {//大陆 或 香港
//                $count = intval($count_arr[$order_goods_type][$delivery_place]);
//            }
//        }
        return $this->apiReturn(0, '获取成功', $count);
    }

    /**
     * 获取用户所有购物车数量
     * @return [type] [description]
     */
    public function allcount()
    {
        $gid = cookie('gid');
        $uid = $this->getCartUid();
        $count_arr = S_cart_num($uid, $gid);
        if (empty($count_arr)) {
            $this->setCount();
            $count_arr = S_cart_num($uid, $gid);
        }
        return $this->apiReturn(0, '获取成功', $count_arr);
    }

    /**
     * 设置缓存购物车数量
     */
    public function setCount($uid = null, $gid = null, $type = 1)
    {
        is_null($gid) && $gid = cookie('gid');
        is_null($uid) && $uid = $this->getCartUid();
        $ShoppingCartModel = D('ShoppingCart');
        if (!empty($gid) || !empty($uid)) {
            //缓存数量
            try {
                foreach (C('ORDER_GOODS_TYPE_MAP') as $k => $v) {
                    //历史代码 很早之前的代码
                    //大概$i就是交货地 目前有大陆和香港交货地
                    for ($i = 1; $i <= 2; $i++) {
                        // if (!empty($uid)) {
                        //     $or = array(
                        //         'user_id' => $uid,
                        //         'user_sign' => $gid,
                        //         '_logic' => 'or',
                        //     );
                        // } else {
                        //     $or = array(
                        //         'user_sign' => $gid,
                        //     );
                        // }
                        $map = array(
                            'status' => 1,
                            'delivery_place' => $i,
                            'type' => $type,
                            'goods_type' => $v,//商品类型
                            'user_id' => $uid,
                            'user_sign' => $gid,
                        );
                        // $count = $ShoppingCartModel->getCount($map);
                        $count = CartService::getCartCount($map);
                        $count_arr[$k][$i] = $count;
                    }
                }
                S_cart_num($uid, $gid, json_encode($count_arr));
            } catch (\Exception $e) {
            }
        }
    }

    // 订单后台修改购物车商品数量
    public function changeNumByOrderSystem()
    {
        cookie('gid', '');
        $data['uid'] = I('uid');
        $data['cart_id'] = I('cart_id');
        $data['num'] = I('num');

        $data['k1'] = time();
        $data['k2'] = pwdhash($data['k1'], C('SUPER_AUTH_KEY'));

        $res = post_curl(API_DOMAIN.'/cart/changeNum', $data);

        print_r($res);
    }

    /**
     * 修改购物车商品数量
     * @param  integer  cart_id      购物车商品ID
     * @param  integer  num          数量
     * @return [type] [description]
     */
    public function changeNum()
    {
        $gid = cookie('gid');
        $uid = $this->getCartUid();
        $cart_id = I('request.cart_id', 0, 'intval');
        $num = I('request.num', 0, 'intval');
        $ShoppingCartModel = D('ShoppingCart');
        $info = $ShoppingCartModel->getInfo($cart_id);
        if ($info['user_id'] != $uid && $info['user_sign'] != $gid) {
            $this->pushReportMonitorLog(
                [
                    "interface_type"=>"3",
                    "err_msg"=>"未找到相关商品信息",
                    "err_code"=>24005,
                    "remark"=>"修改购物车商品数量",

                ]
            );
            return $this->apiReturn(24005, '未找到相关商品信息');
        }
        if ($num <= 0) {
            return $this->apiReturn(24021, '购物车数量必须大于0');
        }

        $use_ac = $this->isCanAc($uid);
        $res = $this->getFinalGoods($info['goods_id'], $num, $info['currency'], $use_ac);
        if ($res['err_code'] !== 0) {
            $this->pushReportMonitorLog(
                [
                    "interface_type"=>"3",
                    "err_msg"=>"商品已下架",
                    "err_code"=>24005,
                    "remark"=>sprintf("修改购物车商品数量 %s",json_encode($info)),

                ]
            );
            return $this->apiReturn(-1, '商品已下架');
        }
        $goods = $res['data']['goods_info'];
        //价格计算
        $goods_prices = ladder_final_price($res['data'], $info['currency'], $goods['ac_type']);
        $initial_prices = ladder_final_price($res['data'], $info['currency'], false);

        // if (in_array($goods['ac_type'], [8, 10])) {
        //     $goods_prices = $initial_prices;
        // }

        //限购商品  需要判断用户是否还能加入购物车
//        $tmp_goods=$goods;
//        $tmp_goods["goods_number"] = $num;
//        $this->chGoodsNumsByUpCart($goods,$tmp_goods);
//        if(isset($goods["is_quota"]) && $goods["is_quota"] == 1){
//            if($tmp_goods["goods_number"] == 0 || $tmp_goods["goods_number"] < $num){
//                $userHasBuy = "";
//                if(isset($tmp_goods["userHasBuyNums"]) && $tmp_goods["userHasBuyNums"] > 0){
//                    $userHasBuy .= ",您已购买{$tmp_goods["userHasBuyNums"]}";
//                }
//                return $this->apiReturn(24006, sprintf("商品%s,每人每天限购%s %s",$tmp_goods["goods_name"],$tmp_goods["quota_num"],$userHasBuy));
//            }
//        }
        $sale_type = 1;
        if ($goods['goods_number'] < $num) {
            if (in_array($goods['goods_type'], C('ORDER_GOODS_TYPE_MAP.1'))) { //联营
                $mpl = !empty($goods['multiple']) ? $goods['multiple'] : 1;//旧数据
                if (($num - $goods['min_buy']) % $mpl > 0) {
                    return $this->apiReturn(24006, '购买数量必须为'.$mpl.'的整倍数');
                } else {
                    return $this->apiReturn(24006, '购物车商品数量已超过现有库存'.$goods['goods_number']);
                }
            } else { //自营
                $mpl = !empty($goods['mpl']) ? $goods['mpl'] : 1;//旧数据
                if (($num - $goods['min_buy']) % $mpl > 0) {
                    return $this->apiReturn(24006, '购买数量必须为'.$mpl.'的整倍数，我们已经为您调整购买量。');
                }


//                if($goods['ac_type'] == static::$QING_KU_CUN_ACTYPE){
//                    $bool = !!(isset($goods['allow_presale']) && $goods['allow_presale']== 2);
//                    if($bool){
//                        //清理库存 预售
//                        $sale_type = 2;
//                    }else{
//                        //清理库存
//                        $modNum = ($goods['goods_number']) % $mpl;
//                        $num = $goods['goods_number'] - intval($modNum);
//                    }
//
//                }else{
//                    $sale_type = 2;
//                }

                $modNum = ($goods['goods_number']) % $mpl;
                $num = $goods['goods_number'] - intval($modNum);
            }
        }
        //预售不受库存限制
        if ($goods['min_buy'] > $num) {
            return $this->apiReturn(24020, '未达到当前商品最小起订量'.$goods['min_buy']);
        }

        $save = array(
            'cart_id' => $cart_id,
            'sale_type' => $sale_type,
            'goods_number' => $num,
            'goods_price' => $goods_prices['price'],
            'initial_price' => $initial_prices['price'],
        );
        // $res = $ShoppingCartModel->save($save);
        $res = CartService::updateCart($save);
        if ($res === false) {
            $this->pushReportMonitorLog(
                [
                    "interface_type"=>"3",
                    "err_msg"=>"修改数量失败",
                    "err_code"=>24022,
                    "remark"=>sprintf("%s",json_encode($save)),

                ]
            );
            return $this->apiReturn(24022, '修改数量失败');
        }

//        $this->addShoppingCartGift($cart_id,$goods,["price"=>$goods_prices['price'],"num"=>$num]);
        $sale_type = 1;
        if ($goods['goods_number'] < $num) {
            if (!in_array($goods['goods_type'], C('ORDER_GOODS_TYPE_MAP.1'))) {//自营
                $sale_type = 2;
            }
        }
        $data = array(
            'sale_type' => $sale_type,
            'num' => $num,
            'ac_type' => $goods['ac_type'],
            'tiered' => $goods['tiered'],
            'goods_price' => price_format($goods_prices['price'], 0, 4),
            'goods_price_format' => price_format($goods_prices['price'], $info['currency'], 4),
            'initial_price' => price_format($initial_prices['price'], 0, 4),
            'initial_price_format' => price_format($initial_prices['price'], $info['currency'], 4),
            'initial_amount' => price_format($initial_prices['price'] * $num, 0, 2),
            'initial_amount_format' => price_format($initial_prices['price'] * $num, $info['currency'], 2),
            'goods_amount' => price_format($goods_prices['price'] * $num, 0, 2),
            'goods_amount_format' => price_format($goods_prices['price'] * $num, $info['currency'], 2),
            'diff_price' => price_format($info['create_goods_price'] - $goods_prices['price'], 0, 4),
            'diff_price_format' => price_format($info['create_goods_price'] - $goods_prices['price'], $info['currency'], 4),
        );
        !$this->auth() && $data = dataConfuse($data, array('tiered', 'purchases'));
        return $this->apiReturn(0, '修改成功', $data);
    }

    // 修改联营批量添加的商品数量
    public function changeNumForJoint()
    {
        $cart_id = I('request.cart_id', 0, 'intval');
        $num = I('request.num', 0, 'intval');
        $ShoppingCartModel = D('ShoppingCart');

        $save = array(
            'cart_id' => $cart_id,
            'goods_number' => $num,
        );

        $res = $ShoppingCartModel->save($save);

        if ($res === false) {
            return $this->apiReturn(24022, '修改数量失败');
        }

        return $this->apiReturn(0, '修改成功');
    }

    // 修改商品单价 - 订单系统电商下单
    public function changePrice()
    {
        $cart_id = I('request.cart_id', 0, 'intval');
        $price = I('request.price', 0, 'float');
        $ShoppingCartModel = D('ShoppingCart');

        $save = array(
            'goods_price' => $price,
        );

        $res = $ShoppingCartModel->where(['cart_id' => $cart_id])->save($save);

        if ($res === false) {
            return $this->apiReturn(24022, '修改单价失败');
        }

        return $this->apiReturn(0, '修改成功');
    }

    //用户是否能使用优惠价
    public function userCanAc()
    {
        $uid = I('uid');
        $ac_type = I('ac_type', null);
        $data = $this->isCanAc($uid, $ac_type);
        return $this->apiReturn(0, '成功', $data);
    }
    /**
     * 检测是否符合新客属性
     * @return boolean [description]
     */
    private function isNewUser($user_id)
    {

    }
    /**
     * 购物车 用户添加购物车降价提醒
     */
    public function addShopingCartRemind(){
        $user_id = cookie('uid');
        if(!$user_id)
            return $this->apiReturn(240026, '请先登录');
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        if($redis->zScore("user_shopping_cart_remind",$user_id)){
            return $this->apiReturn(0, '成功');
        }
        $res = $redis->ZADD("user_shopping_cart_remind",time()+24*3600*30,$user_id);
        if(!$res)
            return $this->apiReturn(240026, '设置提醒失败');
        return $this->apiReturn(0, '成功');
    }

    /**
     * 购物车 用户添加购物车降价提醒
     */
    public function getSCartRStatus(){
        $user_id = cookie('uid');
        if(!$user_id)
            return $this->apiReturn(240026, '请先登录');
        $user_shopping_cart_remind = cookie("user_shopping_cart_remind".$user_id);
        if($user_shopping_cart_remind == "1"){
            return $this->apiReturn(0, '',["status"=>1]);
        }
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        if($redis->zScore("user_shopping_cart_remind",$user_id)){
            cookie("user_shopping_cart_remind".$user_id,1,['expire'=>3600*24]);
            return $this->apiReturn(0, '',["status"=>1]);
        }else{
            cookie("user_shopping_cart_remind".$user_id,null);
            return $this->apiReturn(0, '',["status"=>0]);
        }


    }

    /**
     * 订单确认页面获取djk应用类型
     */
    public function getDJKConfg(){
        $configs = C("DJKCONFIG");
        $arr = [];
        foreach($configs["class_one"] as $k=>$v){
            $arr[$v] = $configs["class_two"][$k];
        }
        return $this->apiReturn(0, '',["data"=>$arr]);
    }

    /*
     * 获取购物车内商品的满赠信息
     */
    public function getCartMZInfo($cart_ids=[]){
        return $this->apiReturn(240550, '无赠品信息',[]);

//        if(!$this->auth() && !$this->checkLoginByCart()){
//            return $this->apiReturn(240550, '无赠品信息',[]);
//        }
//        $cart_id = $cart_ids ? $cart_ids : I("cart_id");
//        if(is_string($cart_id)){
//            $cart_id = trim($cart_id);
//            $cart_id = trim($cart_id,",");
//            $cart_id = explode(",",$cart_id);
//        }elseif(is_array($cart_id)){
//            foreach($cart_id as &$value){
//                $value = trim($value);
//                $value = trim($value,",");
//            }
//        }
//        $cartGift = $this->getCartGift($cart_id);
//        if(empty($cartGift)){
//            return $this->apiReturn(240550, '无赠品信息',[]);
//        }else{
//            foreach($cartGift as $k=>$item){
//                $cartGift[$k]["content"] = sprintf("已满%s,赠%s",$item['amount'],$item["item_name"]);
//            }
//            return $this->apiReturn(0, 'ok',$cartGift);
//        }

    }

    // 添加备注
    public function addRemark()
    {
        $cart_id = I('cart_id', '');
        $remark = I('remark', '');

        if (!$cart_id) {
            return $this->apiReturn(-1, '参数缺失');
        }

        $uid = $this->getCartUid();

        $ShoppingCartModel = D('ShoppingCart');

        $map = [];
        $map['cart_id'] = $cart_id;
        $map['user_id'] = $uid;

        $cart_info = $ShoppingCartModel->where($map)->find();

        if (empty($cart_info)) {
            return $this->apiReturn(-1, '未找到数据');
        }

        $update = [
            'remarks' => $remark,
            'update_time' => time(),
        ];

        $res = $ShoppingCartModel->where(['cart_id' => $cart_id])->save($update);

        if ($res === false) {
            return $this->apiReturn(-1, '添加备注失败');
        }

        return $this->apiReturn(0, '成功');
    }

    // 导出明细
    public function exportItems()
    {
        $gid = cookie('gid');
        $uid = $this->getCartUid();
        $cart_ids = I('cart_ids', '');

        if (!$cart_ids) {
            return $this->apiReturn(-1, '参数缺失');
        }

        $cart_ids = explode(',', $cart_ids);

        $ShoppingCartModel = D('ShoppingCart');

        $map = [];
        if (!empty($uid)) {
            $map['user_id'] = $uid;
        } else {
            $map['user_sign'] = $gid;
        }

        $map['cart_id_arr'] = $cart_ids;
        // $cart_info = $ShoppingCartModel->where($map)->order('cart_id desc')->select();
        $cart_info = CartService::getCartList($map,"cart_id desc");

        if (empty($cart_info)) {
            return $this->apiReturn(-1, '未找到数据');
        }
        $this->exportDataHandle($cart_info);
    }

    public function exportDataHandle($data, $is_invalid=0)
    {
        $cell_data = [];

        foreach ($data as $k => $v) {
            $cell_data[$k]['goods_name'] = $v['goods_name'];
            $cell_data[$k]['brand_name'] = $v['standard_brand_name'] ?: $v['brand_name'];
            $cell_data[$k]['supplier_name'] = $v['supplier_name'];
            $cell_data[$k]['goods_number'] = $v['goods_number'];
            $cell_data[$k]['goods_price'] = $v['goods_price'];

            $goods_data = json_decode($v['goods_data'], true);
            $cell_data[$k]['delivery_time'] = isset($goods_data['delivery_time'][$v['currency']]) ? $goods_data['delivery_time'][$v['currency']] : '';

            $cell_data[$k]['currency'] = $v['currency'] == 1 ? '人民币' : '美元';
            $cell_data[$k]['goods_amount'] = price_format($v['goods_number'] * $v['goods_price']);
        }

        $header = ['商品型号', '品牌', '供应商', '数量', '单价', '交期', '币种', '小计'];

        if ($is_invalid) {
            $file_name = '导出无效明细_' . date('YmdHis') . '.csv';
        } else {
            $file_name = '导出明细_' . date('YmdHis') . '.csv';
        }
        $cell_data = array_merge($cell_data);
        export_csv($cell_data, $header, $file_name);
    }

    // 导出无效明细
    public function exportInvalidItems()
    {
        $uid = $this->getCartUid();
        $delivery_place = I('delivery_place', 1);

        $ShoppingCartModel = D('ShoppingCart');

        $map = [];
        $map['user_id'] = $uid;
        $map['delivery_place'] = $delivery_place;
        $map['status'] = -1;

        $cart_info = $ShoppingCartModel->where($map)->order('cart_id desc')->select();

        if (empty($cart_info)) {
            return $this->apiReturn(-1, '未找到数据');
        }

        $this->exportDataHandle($cart_info, 1);
    }

    public function setCount_init($user_id=0,$gid=''){
        if(!empty($user_id)){
            $this->setCount($user_id,null,1);
        }elseif(!empty($gid)){
            $this->setCount(null,$gid,1);
        }
        return $this->apiReturn(0, '');
    }

}
