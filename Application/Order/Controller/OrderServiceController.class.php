<?php
namespace Order\Controller;

use Order\Controller\BaseController;

// 订单售后处理
class OrderServiceController extends BaseController
{
   // 申请
	public function apply()
	{
		$data          = I('request.data', '');
		$operator_id   = I('request.operator_id', 0, 'intval');
		$operator_name = I('request.operator_name', '');

		if (!$data || empty($data['service_item']) || !$operator_id) return $this->apiReturn(48001, '参数缺失');
		if (strpos($_SERVER['HTTP_REFERER'], 'sz') !== false) return $this->apiReturn(48002, '测试环境不能生成售后单');

		$OrderModel             = D('Order');
		$OrderActionLogModel    = D('OrderActionLog');
		$OrderServiceModel      = D('OrderService'); 
		$OrderServiceItemsModel = D('OrderServiceItems'); 
		$OrderServiceLogModel   = D('OrderServiceLog');

		$order_info = $OrderModel->getInfo($data['order_id']);

		$order_service = [];

		$count = $OrderServiceModel->getOrderServiceCount($data['order_id']);

		$order_service['service_sn']  = 'F-'.$order_info['order_sn'].'-'.$count; // 生成售后单号
		$order_service['order_id']    = $data['order_id'];
		$order_service['order_sn']    = $order_info['order_sn'];
		$order_service['user_id']     = $order_info['user_id'];
		$order_service['apply_id']    = $operator_id;
		$order_service['apply_name']  = $operator_name;
		$order_service['create_time'] = time();
		$order_service['update_time'] = $order_service['create_time'];
		$order_service['refund_type'] = isset($data['refund_type']) ? $data['refund_type'] : 0;

		if ($data['refund_type'] == 2) { // 支付宝
			$order_service['customer_name']  = $data['ali_customer_name'];
			$order_service['refund_account'] = $data['ali_refund_account'];
		} else if ($data['refund_type'] == 3) { // 银行
			$order_service['customer_name']  = $data['bank_customer_name'];
			$order_service['refund_account'] = $data['bank_refund_account'];
			$order_service['bank_name']      = $data['bank_name'];
			$order_service['bank_sub_name']  = $data['bank_sub_name'];
		}

		$order_service['service_type'] = $data['service_type']; // 售后类型

		if ($data['service_type'] == 2) { // 出库退货
			$order_service['return_status'] = 2; // 等待入库
			$order_service['is_return']     = 1;
		}

		$OrderModel->startTrans();

		// 添加售后单
		$service_id = $OrderServiceModel->add($order_service);
		if ($service_id === false) {
			$OrderModel->rollback();
			return $this->apiReturn(48002, '添加售后单失败');
		}

		// 添加售后单明细
		$items = $push_tracking_data = [];
		foreach ($data['service_item'] as $k=>&$v) {
			if ($v['adjust_number'] == 0) continue; // 过滤申请数量0

			$v['service_id']  = $service_id;
			$v['create_time'] = time();
			$v['update_time'] = $v['create_time'];

			$items[] = $v;

			if ($v['adjust_number'] && $v['adjust_price']) { // 售后数量、单价都存在
				$temp = [];
	            $temp['RBMQTYPE']     = 'SCZZ';
	            $temp['BIZDATE']      = date('Y-m-d H:i:s', time());
	            $temp['MSG']          = '售后申请成功，等待处理';
	            $temp['CREATOR']      = $operator_name;
	            $temp['ORDERENTRYID'] = $v['erp_rec_id'];
	            $temp['ORDERID']      = $data['order_id'];
	            $temp['STATUS']       = 131; // 生产跟踪状态

	            $push_tracking_data[] = $temp;
			}
		}
		
		$res = $OrderServiceItemsModel->addAll($items);
		if ($res === false) {
			$OrderModel->rollback();
			return $this->apiReturn(48003, '添加售后单明细失败');
		}

		// 添加售后单日志
		$res = $OrderServiceLogModel->addLog($service_id, $operator_id, '添加售后单');
		if ($res === false) {
			$OrderModel->rollback();
			return $this->apiReturn(48004, '添加售后单日志失败');
		}

		// 添加订单日志
		$res = $OrderActionLogModel->addLog($data['order_id'], $operator_id, 2, '申请售后单，售后单号：'.$order_service['service_sn']);
		if ($res === false) {
			$OrderModel->rollback();
			return $this->apiReturn(48005, '添加订单日志失败');
		}

		$OrderModel->commit();

		$push_tracking_data && $this->pushProductTracking($push_tracking_data); // 推送生产跟踪信息

		return $this->apiReturn(0, '申请成功');
	}

	// 检查是否能原路返回
	public function checkTurnBack()
	{
		$data = I('request.data', '');

		$OrderModel  = D('Order');
		$PayLogModel = D('PayLog');

		$order_info = $OrderModel->getInfo($data['order_id']);

		$is_turn_back  = true; // 是否原路返回 
		$refund_amount = 0; // 售后退款总额

		foreach ($data['service_item'] as $k=>$v) {
			if ($v['adjust_number'] == 0) continue; // 过滤申请数量0

			$goods_amount = $v['adjust_price'] * $v['adjust_number'];
            $refund_amount += $goods_amount;
        }

		$refund_amount = number_format($refund_amount, 2, '.', ''); // 售后金额保留两位

		if ($order_info['order_pay_type'] == 3) $is_turn_back = false; // 账期不支持原路返回

		if ($order_info['order_pay_type'] == 2) { // 预付款
			$all_pay_log = $PayLogModel->getInfo($order_info['order_id']);

			if (count($all_pay_log) > 1) $is_turn_back = false; // 多次支付 不支持原路返回

			$pay_log = $all_pay_log[0];
		} else { // 全款
			$pay_log = $PayLogModel->getInfo($order_info['order_id'], '*', true);
		}

        if (!$pay_log['pay_order_sn'] && !$pay_log['serial_number']) $is_turn_back = false; // 支付单号和流水号为空 不支持原路返回
        if ($refund_amount != $pay_log['pay_amount']) $is_turn_back = false; // 退款金额不等于支付金额 不支持原路返回
        if ($pay_log['pay_id'] == 9) $is_turn_back = false;; // 钱包支付 不支持原路返回

        return $this->apiReturn(0, '', $is_turn_back);
	}

	/**
	 * 取消订单时生成售后单
	 * @param  [type] $order_id    [订单ID]
	 * @param  [type] $operator_id [操作人ID]
	 * @return [type]              [description]
	 */
	public function createOrderService($order_id, $operator_id)
	{
		$OrderModel             = D('Order');
		$OrderItemsModel        = D('OrderItems');
		$OrderServiceModel      = D('OrderService'); 
		$OrderServiceItemsModel = D('OrderServiceItems'); 
		$OrderServiceLogModel   = D('OrderServiceLog');

		$order_info = $OrderModel->getInfo($order_id);

		$order_service = [];

		$count = $OrderServiceModel->getOrderServiceCount($order_id);
		$CmsModel = D('Cms');

		$order_service['service_sn']   = 'F-'.$order_info['order_sn'].'-'.$count; // 生成退货单号
		$order_service['order_id']     = $order_id;
		$order_service['order_sn']     = $order_info['order_sn'];
		$order_service['user_id']      = $order_info['user_id'];
		$order_service['apply_reason'] = 4;
		$order_service['remark']       = '';
		$order_service['apply_id']     = $operator_id;
		$order_service['apply_name']   = $CmsModel->getUserName($operator_id);
		$order_service['create_time']  = time();
		$order_service['update_time']  = $order_service['create_time'];

		$OrderModel->startTrans();

		// 添加售后单
		$service_id = $OrderServiceModel->add($order_service);
		if ($service_id === false) {
			$OrderModel->rollback();
			return $this->apiReturn(48002, '添加售后单失败');
		}

		// 添加售后单明细
		$order_items = $OrderItemsModel->getOrderList($order_id, '', null);

		$items = [];
		foreach ($order_items as $v) {
			$tmp = [];
			$tmp['service_id']       = $service_id;
			$tmp['rec_id']           = $v['rec_id'];
			$tmp['goods_id']         = $v['goods_id'];
			$tmp['goods_name']       = $v['goods_name'];
			$tmp['sku_name']         = $v['sku_name'];
			$tmp['brand_id']         = $v['brand_id'];
			$tmp['brand_name']       = $v['brand_name'];
			$tmp['supplier_id']      = $v['supplier_id'];
			$tmp['supplier_name']    = $v['supplier_name'];
			$tmp['goods_price']      = $v['goods_price'];
			$tmp['single_pre_price'] = $v['single_pre_price'];
			$tmp['goods_number']     = $v['goods_number'];
			$tmp['adjust_price']     = $v['goods_price'];
			$tmp['adjust_number']    = $v['goods_number'];
			$tmp['create_time']      = time();
			$tmp['update_time']      = $tmp['create_time'];

			$items[] = $tmp;
		}
		
		$res = $OrderServiceItemsModel->addAll($items);
		if ($res === false) {
			$OrderModel->rollback();
			return $this->apiReturn(48003, '添加售后单明细失败');
		}

		// 添加售后单日志
		$res = $OrderServiceLogModel->addLog($service_id, $operator_id, '添加售后单');
		if ($res === false) {
			$OrderModel->rollback();
			return $this->apiReturn(48004, '添加售后单日志失败');
		}

		$OrderModel->commit();

		return $this->apiReturn(0, '成功', $order_service['service_sn']);
	}
	


}