<?php
namespace Order\Controller;

use Common\Model\ThirdnwmsModel;
use Order\Controller\BaseController;

class WmsController extends BaseController
{
    /**
     * 重写初始化
     * @return [type] [description]
     */
    public function _initialize()
    {
        $token = I('get.token', '', 'trim');
        if (!in_array(strtolower(ACTION_NAME), array('test1','ds')) && CONTROLLER_NAME == 'Wms') {
            $res = service_auth(I('request.'), $token);
            if ($res !== true) {
                return $this->apiReturn(-1, $res);
            }
        }
    }

    public function a(){
        #todo 2020.9.9 自营出库推入erp队列
        $pushData = json_encode(["order_sn"=>$_REQUEST['order_sn']]);
        $RbmqModel = D('Common/Rbmq');
        $s = $RbmqModel->connect('WMS_RBMQ_CONFIG2')->queue(C("QUEUE_ZY_REMOVAL_ORDER"))->push($pushData, C("QUEUE_ZY_REMOVAL_ORDER"));
        print_r($s);
        print_r("写入队列成功");
    }

    public function test1()
    {
        $a = I('request.');
        $a['timestamp'] = time();
        $data = json_encode(array('entity' => array($a)));
        // dump($a);
        $data = json_encode($a);
        echo $a['timestamp'],'<br/>';
        echo service_token(urlencode($data), $a['timestamp'], C('SERVICE_KEY')),'<br/>';
        echo urlencode($data);
    }

    // /**转移到订单微服务
    // 
    //  * 预分配订单 
    //  * @param  [type] $order_id [description]
    //  * @param  string $order_sn [description]
    //  * @return [type]           [description]
    //  */
    // public function makeOrder($order_id, $order_sn = '')
    // {
    //     $OrderModel = D('Order');
    //     $OrderItemsModel = D('OrderItems');
    //     $OrderPriceModel = D('OrderPrice');
    //     $OrderInvoiceModel = D('OrderInvoice');
    //     $OrderAddressModel = D('OrderAddress');
    //     $OrderShippingModel = D('OrderShipping');
    //     $OrderExtendModel = D('OrderExtend');

    //     if (!empty($order_sn) && empty($order_id)) {
    //         $map['O.order_sn'] = $order_sn;
    //     } else {
    //         $map['O.order_id'] = $order_id;
    //     }
    //     $info = $OrderModel->getOrderUser($map, 'O.order_goods_type,O.status,O.order_id,O.order_sn,O.order_amount,O.currency,O.sale_type,O.order_shipping_type,O.order_remark,O.create_time,U.is_test');
    //     if (empty($info)) {
    //         return $this->apiReturn(21004, '未找到相关订单');
    //     }
    //     if ($info['is_test'] == 1) {//测试人员不预分配
    //         return $this->apiReturn(0, '操作成功');
    //     }
    //     if ($info['order_goods_type'] != 2) {//自营订单可预分配
    //         return $this->apiReturn(0, '操作成功');
    //     }
    //     if (!C('WMS_ORDER_SYN')) {//开关
    //         return $this->apiReturn(0, '操作成功');
    //     }
    //     $extend = $OrderExtendModel->getInfo($order_id, 'wms_order,order_type');
    //     //待付款 或者 后台下单可同步
    //     if ((!in_array($info['status'], array(2,4)) && $extend['order_type'] == 3 && $info['sale_type'] == 2) ||
    //          (!in_array($info['status'], array(2,4)) && $extend['order_type'] != 3)) {
    //         return $this->apiReturn(21021, '操作失败');
    //     }


    //     //已标记无需请求
    //     if ($extend['wms_order'] > 0) {
    //         return $this->apiReturn(0, '操作成功');
    //     }

    //     $order_id = $info['order_id'];

    //     // 金额相关
    //     $extend_fee = $OrderPriceModel->getExtPrice($order_id);
    //     $goods_total = $OrderPriceModel->getGoodsPrice($order_id);
    //     $preferential_price = $OrderPriceModel->getPreferentialPrice($order_id);
    //     $shipping_price = $OrderPriceModel->getShippingPrice($order_id);
    //     $address = $OrderAddressModel->getInfo($order_id, 1);
    //     $list = $OrderItemsModel->getOrderList($order_id, '', null);
    //     $invoice = $OrderInvoiceModel->getInfo($order_id);
    //     if (in_array($invoice['inv_type'], array(2, 4))) {//普通发票
    //         $tax_title = $invoice['tax_title'];
    //         $tax_no = !empty($invoice['tax_no']) ? $invoice['tax_no'] : '';
    //     } else {
    //         $tax_title = '';
    //         $tax_no = '';
    //     }
    //     foreach ($list as &$v) {
    //         $detail[] = array(
    //             'rec_id' => $v['rec_id'],
    //             'order_id' => $order_id,
    //             'goods_id' => $v['goods_id'],
    //             'goods_number' => $v['goods_number'],
    //             'goods_price' => $v['goods_price'],
    //             'user_company' => !empty($v['user_company']) ? $v['user_company'] : '',
    //             'user_sku' => !empty($v['user_sku']) ? $v['user_sku'] : '',
    //             'user_skuname' => !empty($v['user_skuname']) ? $v['user_skuname'] : '',
    //         );
    //     }
    //     if (empty($address['province']) && empty($address['city']) && empty($address['district']) && !empty($address['address'])) {//只有地址没有省市区
    //         //省
    //         $province = mb_substr($address['address'], 0, 2, 'utf8');
    //         if (!in_array($province, C('JD_PROVINCE'))) {
    //             $province = mb_substr($address['address'], 0, 3, 'utf8');
    //             if (!in_array($province, C('JD_PROVINCE'))) {
    //                 $province = '';
    //             }
    //         }
    //         // 北京 朝阳区 安贞街道
    //         // 上海 浦东新区 北蔡镇
    //         // 上海 浦东新区 城区
    //         // 天津 蓟州区 全境
    //         // 辽宁 大连市 庄河市 太平岭乡
    //         // 内蒙古 呼和浩特市土默特左旗 毕克齐镇
    //         // 海南 文昌市 国营东路农场
    //         // 青海 海东地区 乐都县 洪水镇
    //         // 台湾 台湾 高雄 前金区
    //         // 港澳 香港特别行政区 九龙 黄大仙区
    //         // 港澳 澳门特别行政区 澳门半岛
    //         // 湖北 神农架林区 松柏镇
    //         //市
    //         $special = array('北京', '上海', '天津', '重庆','台湾');
    //         $address['address'] = preg_replace('('.$province.'(?:省)?)', '', $address['address']);
    //         if (in_array($province, $special)) {
    //             $city = $province;
    //             $citys = preg_replace('/([^区]+(?:市|区|县|镇))(.*)/', '$1|$2', $address['address'], 1);
    //             list($district, $address_val) = explode('|', $citys, 2);
    //         } else {
    //             $citys = preg_replace('/([^区]+(?:市|州|县|地区|盟))(.*(?:市|区|县|镇))(.*)/', '$1|$2|$3', $address['address'], 1);
    //             list($city, $district, $address_val) = explode('|', $citys, 3);
    //         }
    //     } else {
    //         $province = $address['province_val'];
    //         $city = $address['city_val'];
    //         $district = $address['district_val'];
    //         $address_val = $address['address'];
    //     }
    //     $data = array(
    //         'order_id' => intval($info['order_id']),
    //         'order_sn' => $info['order_sn'],
    //         'order_level' => 1,
    //         'order_amount' => $info['order_amount'],
    //         'discount_amount' => $preferential_price,
    //         'express_amount' => $shipping_price,
    //         'goods_amount' => $goods_total,
    //         'currency' => C('CURRENCY_CODE.'. $info['currency']),
    //         'order_shipping_type' => $info['order_shipping_type'] == 1 ? 2 : 1,//wms与数据库定义颠倒
    //         'order_remark' => $info['order_remark'],
    //         'create_time' => $info['create_time'],
    //         'province' => $province,
    //         'city' => $city,
    //         'district' => $district,
    //         'consignee' => $address['consignee'],
    //         'mobile' => $address['mobile'],
    //         'telephone' => $address['telphone'],
    //         'address' => $address_val,
    //         'shipping_id' => $info['order_shipping_type'] == 2 ? '' : 10,//默认10速尔快递 , 1顺丰
    //         'total_rows' => count($list),
    //         'warehouse_code' => '01',
    //         'tax_title' => $tax_title,
    //         'tax_no' => $tax_no,
    //         'inv_type' => $invoice['inv_type'],
    //         'detail' => $detail,
    //     );
    //     $res = $this->submitOrder($data);
    //     if (!empty($res) && $res['entity'][0]['Status'] == 1) {
    //         // 是否预分配成功标记
    //         $res = $OrderExtendModel->updateOrInsert($order_id, array('wms_order' => 1));
    //         if ($res === false) {
    //             return $this->apiReturn(21023, '操作失败，请联系客服处理');
    //         }
    //         return $this->apiReturn(0, '成功');
    //     } else {
    //         return $this->apiReturn(21022, $res['entity'][0]['Message']);
    //     }
    // }

    /**
     * 内部物流轨迹
     * @return [type] [description]
     */
    public function shippingInsideAct()
    {
        $data = I('post.data', '', 'trim');
        $data = json_decode(urldecode($data), true);
        $res = isset_field($data, array('track_id', 'order_id', 'track_type', 'track_content', 'track_time', 'track_userid'));
        if (0 !== $res) {
            return $this->apiReturn(10001, '数据缺失字段' . $res);
        }

        $OrderModel = D('Order');
        $OrderShippingInsideModel = D('OrderShippingInside');
        $info = $OrderModel->getInfo($data['order_id']);
        if (empty($info)) {
            return $this->apiReturn(10010, '找不到相关订单');
        }
        $res = $OrderShippingInsideModel->getInfoType($data['order_id'], $data['track_type']);
        if (empty($res)) {
            $add = array(
                'order_id' => $data['order_id'],
                'order_sn' => $info['order_sn'],
                'inside_type' => $data['track_type'],
                'info' => $data['track_content'],
                'create_uid' => $data['track_userid'],
                'create_time' => $data['track_time'],
            );
            $res = $OrderShippingInsideModel->add($add);
            if ($res === false) {
                return $this->apiReturn(10002, '轨迹记录失败');
            }

        } else {
            $save = array(
                'inside_id' => $res['inside_id'],
                'info' => $data['track_content'],
                'create_uid' => $data['track_userid'],
                'create_time' => $data['track_time'],
            );
            $res = $OrderShippingInsideModel->save($save);
            if ($res === false) {
                return $this->apiReturn(10003, '轨迹记录失败');
            }
        }
        return $this->apiReturn(0, '记录成功');
    }

    /*
     *测试自动收货推送新wms
     */
    public function removalTest(){
        $data = json_decode('{"stock_out_type":'.I("stock_out_type").',"erp_sn":"","order_sn":"'.I("order_sn").'","shipping_name":"速腾快递","shipping_no":"************"}',true);
        $res = (new ThirdnwmsModel())->egoldsFinish($data);
        print_r($res);
    }

    /**
     * 自营出库
     * @return [type] [description]
     */
    public function removalAct()
    {
        \Think\Log::write(" 出库数据：".json_encode($_REQUEST), 1, '', "");  //日志

        $dataTemp = I('post.data', '', 'trim');
        if (@$_REQUEST["debug"]){
            $dataTemp= rawurldecode(urlencode(urldecode($_REQUEST["debug"])));
        }

        $data = json_decode(urldecode($dataTemp), true);
        $res = isset_field($data, array('order_id', 'shipping_id', 'box_weight', 'express_amount', 'express_sn', 'send_time', 'send_userid', 'is_print', 'outstock_sn', 'warehouse_code', 'detail'));

        if (0 !== $res) {
            return $this->apiReturn(10001, '数据缺失字段' . $res);
        }
        $express_sn = explode(',', $data['express_sn'], 2);//存在主单、子单

        if ($res["shipping_id"] == 222){
            $res["shipping_id"] = 15;
        }
        $OrderModel = D('Order');
        $RemovalItemsModel = D('RemovalItems');
        $OrderExtendModel = D('OrderExtend');

        $info = $OrderModel->getInfo($data['order_id']);
        if (empty($info)) {
            return $this->apiReturn(10010, '找不到相关订单');
        }
        $OrderModel->startTrans();
        if ($info['status'] < 8) {//已发货
            //出库记录
            $unlock_items = array();
            foreach ($data['detail'] as $v) {
                $unlock_items[$v['goods_id']] = $v['goods_number'];
            }

            #基石解锁扣减库存
            $res = $this->unlockSku($data['order_id'], $unlock_items, true);
            if ($res['errcode'] !== 0 && $res['errcode'] !== '0') {
                $OrderModel->rollback();
                return $this->apiReturn($res['errcode'], $res['errmsg']);
            }
        }
        $OrderModel->commit();

        #todo 2020.9.9 自营出库推入erp队列
        $erp_sn = $OrderExtendModel->where(["order_id" => $info["order_id"]])->getField("erp_sn");
        $shipping_name = D("Shipping")->where(["shipping_id" => $data["shipping_id"]])->getField("shipping_name");

        $stock_out_type = $OrderExtendModel->where(["order_id" => $info["order_id"]])->getField("stock_out_type");
        $pushData = [
            "erp_sn"=>$erp_sn,
            "order_sn"=>$info["order_sn"] ? $info["order_sn"]:$info["sale_order_sn"] ,
            "shipping_name" => $shipping_name ? $shipping_name:"顺丰快递",
            "shipping_no"=>$express_sn[0],
            "stock_out_type"=>$stock_out_type,
        ];
       # $data = json_decode('{"stock_out_type":'.I("stock_out_type").',"erp_sn":"","order_sn":"'.I("order_sn").'","shipping_name":"速腾快递","shipping_no":"************"}',true);
        $res = (new ThirdnwmsModel())->egoldsFinish($pushData);

        dingZy(json_encode($pushData)); //发送钉钉消息

        $path = C('LOG_PATH').'zy_removal/zy_removal'.date('y_m_d').'.log';
        \Think\Log::write("推入队列数据： ".json_encode($pushData)." 出库数据：".json_encode($data)." 推入队列结果：".$res, 1, '', $path);  //日志

        return $this->apiReturn(0, '创建成功');
    }

    /**
     * 新自营出库
     */
    public function removalActNew()
    {
        \Think\Log::write(" 出库数据newremovalActNew：".json_encode($_REQUEST), 1, '', "");  //日志

        $dataTemp = I('post.data', '', 'trim');
        if (@$_REQUEST["debug"]){
            $dataTemp= rawurldecode(urlencode(urldecode($_REQUEST["debug"])));
        }

        $data = json_decode(urldecode($dataTemp), true);
        $res = isset_field($data, array('order_id', 'shipping_id', 'express_amount', 'express_sn', 'send_time', 'send_userid', 'outstock_sn',  'detail'));

        if (0 !== $res) {
            return $this->apiReturn(10001, '数据缺失字段' . $res);
        }
        $express_sn = explode(',', $data['express_sn'], 2);//存在主单、子单

        if ($res["shipping_id"] == 222){
            $res["shipping_id"] = 15;
        }
        $OrderModel = D('Order');
        $RemovalItemsModel = D('RemovalItems');
        $OrderExtendModel = D('OrderExtend');

        $info = $OrderModel->getInfo($data['order_id']);
        if (empty($info)) {
            return $this->apiReturn(10010, '找不到相关订单');
        }
        $OrderModel->startTrans();
        if ($info['status'] <= 8) {//已发货
            //出库记录
            $unlock_items = array();
            foreach ($data['detail'] as $v) {
                $unlock_items[$v['goods_id']] = isset($unlock_items[$v['goods_id']]) ? $unlock_items[$v['goods_id']]+$v['goods_number']:$v['goods_number'];
            }

            #基石解锁扣减库存
            $res = $this->unlockSku($data['order_id'], $unlock_items, true);
            if ($res['errcode'] !== 0 && $res['errcode'] !== '0') {
                $OrderModel->rollback();
                return $this->apiReturn($res['errcode'], $res['errmsg']);
            }
        }
        $OrderModel->commit();

        #todo 2020.9.9 自营出库推入erp队列
        $erp_sn = $OrderExtendModel->where(["order_id" => $info["order_id"]])->getField("erp_sn");
        $shipping_name = D("Shipping")->where(["shipping_id" => $data["shipping_id"]])->getField("shipping_name");

        $stock_out_type = $OrderExtendModel->where(["order_id" => $info["order_id"]])->getField("stock_out_type");
        $pushData = [
            "erp_sn"=>$erp_sn,
            "order_sn"=>$info["order_sn"] ? $info["order_sn"]:$info["sale_order_sn"] ,
            "shipping_name" => $shipping_name ? $shipping_name:"顺丰快递",
            "shipping_no"=>$express_sn[0],
            "stock_out_type"=>$stock_out_type,
        ];

        dingZy(json_encode($pushData)); //发送钉钉消息

        $path = C('LOG_PATH').'zy_removal/new_'.date('y_m_d').'.log';
        \Think\Log::write("推入队列数据： ".json_encode($pushData)." 出库数据：".json_encode($data)." 推入队列结果：".$res, 1, '', $path);  //日志

        return $this->apiReturn(0, '新wms自营出库成功');
    }

    /**
     * 修改出库物流单号
     * @return [type] [description]
     */
    public function changeShipping()
    {
        $data = I('post.data', '', 'trim');
        $data = json_decode(urldecode($data), true);
        $res = isset_field($data, array('shipping_id', 'express_amount', 'express_sn', 'send_time', 'send_userid', 'outstock_sn'));
        if (0 !== $res) {
            return $this->apiReturn(10001, '数据缺失字段' . $res);
        }

        $OrderShippingModel = D('OrderShipping');
        $RemovalModel = D('Removal');
        $removal = $RemovalModel->getInfo(array('removal_sn' => $data['outstock_sn']));
        if (!empty($removal) && ($removal['shipping_sn'] != $data['express_sn'] || $removal['shipping_id'] != $data['shipping_id'])) {//存在可修改
            $RemovalModel->startTrans();
            $removal_id = $removal['removal_id'];
            $map = array(
                'removal_id' => $removal_id,
            );
            $save = array(
                'shipping_id' => $data['shipping_id'],
                'shipping_sn' => $data['express_sn'],
                'shipping_amount' => $data['express_amount'],
                'create_uid' => $data['send_userid'],
                'create_time' => $data['send_time'],
            );
            $res = $RemovalModel->where($map)->save($save);
            if ($res === false) {
                $RemovalModel->rollback();
                return $this->apiReturn(10011, '修改出库单物流失败');
            }

            $express_sn = explode(',', $data['express_sn'], 2);//存在主单、子单
            //物流数据
            $save = array(
                'removal_id' => $removal_id,
                'shipping_id' => $data['shipping_id'],
                'shipping_no' => $express_sn[0],
                'status' => 1,
                'info' => '',
                'expire_time' => 0,
                'update_time' => 0,
            );
            $res = $OrderShippingModel->where($map)->save($save);
            if ($res === false) {
                $RemovalModel->rollback();
                return $this->apiReturn(10012, '修改出库单物流失败');
            }
            $RemovalModel->commit();
        }
        return $this->apiReturn(0, '操作成功');
    }

    /**
     * 获取已付款待推送至wms订单
     * @return [type] [description]
     */
    public function payOrderWmsMark()
    {
        $map = array(
            'O.is_type' => 0,
            'O.wms_syn' => 1,
            'O.order_goods_type' => 2,
            'O.is_test' => 0,
//            '_string' => 'O.order_type = 3',
        );
        $OrderModel = D('Order');
        $list = $OrderModel->getNotTestUserList($map, 'O.order_id,O.status');
        return $this->apiReturn(0, '', $list);
    }

    /**
     * 获取已付款待推送至wms订单
     * @return [type] [description]
     */
    public function setMarkPush()
    {
        $data = I('post.data', '', 'trim');
        $data = json_decode(urldecode($data), true);
        $res = isset_field($data, array('order_id'));
        if (0 !== $res) {
            return $this->apiReturn(10001, '数据缺失字段' . $res);
        }

        $OrderModel = D('Order');
        $save = array(
            'order_id' => $data['order_id'],
            'wms_syn' => -1,
            'wms_syn_last_time' => time(),
        );
        $res = $OrderModel->save($save);
        if ($res === false) {
            $this->apiReturn(10008, '标记WMS已同步推送失败');
        }
        
        return $this->apiReturn(0, '');
    }

    /**
     * 获取寄售出库至采购
     * @return [type] [description]
     */
    public function removalPurMark()
    {
        $orders = array();
        $res = array();
        $map = array(
            'pur_syn' => 1,
        );
        $RemovalModel = D('Removal');
        $RemovalItemsModel = D('RemovalItems');
        $datas = $RemovalItemsModel->group('removal_id,supplier_id')->where($map)->field('removal_id,supplier_id')->select();
        foreach ($datas as $k => $data) {
            if (!isset($orders[$data['removal_id']])) {
                $orders[$data['removal_id']] = $RemovalModel->getInfoWithOrder($data['removal_id'], 'O.order_sn,O.currency,R.create_time');
                $orders[$data['removal_id']]['suffix'] = 1;
            }
            $data = array_merge($data, $orders[$data['removal_id']]);

            $items = array();
            $map = array(
                'removal_id' => $data['removal_id'],
                'supplier_id' => $data['supplier_id'],
            );
            $lists = $RemovalItemsModel->getList($map);
            foreach ($lists as $v) {
                $goods = $this->getGoods($v['goods_id']);
                if ($goods['err_code'] != 0) {
                    return $this->apiReturn($goods['err_code'], $goods['err_msg']);
                }
                $items[] = array(
                    'removal_items_id' => $v['removal_items_id'],
                    'sku_id' => $v['goods_id'],
                    'goods_name' => $goods['data']['goods_name'],
                    'brand_id' => $v['brand_id'],
                    'brand_name' => $v['brand_name'],
                    'encap' => $v['encap'],
                    'mpq' => $v['mpq'],
                    'picking_number' => $v['removal_number'],
                    'packing_id' => $v['goods_packing'],
                );
            }
            $res[] = array(
                'picking_sn' => sprintf('%s-%d', $data['order_sn'], $orders[$data['removal_id']]['suffix']++),
                'sale_sn' => $data['order_sn'],
                'send_time' => $data['create_time'],
                'currency' => $data['currency'],
                'supplier_id' => $data['supplier_id'],
                'sku_items' => $items,
            );
        }
        return $this->apiReturn(0, '', $res);
    }

    /**
     * 修改采购标记
     */
    public function setMarkPur()
    {
        $data = I('post.data', '', 'trim');
        $data = json_decode(urldecode($data), true);
        $res = isset_field($data, array('removal_items_id'));
        if (0 !== $res) {
            return $this->apiReturn(10001, '数据缺失字段' . $res);
        }
        $RemovalItemsModel = D('RemovalItems');
        $map = array(
            'removal_items_id' => array('in', $data['removal_items_id']),
        );
        $save = array(
            'pur_syn' => -1,
            'pur_syn_last_time' => time(),
        );
        $res = $RemovalItemsModel->where($map)->save($save);
        if ($res === false) {
            $this->apiReturn(10009, '标记WMS已同步推送失败');
        }
        return $this->apiReturn(0, '');
    }

    /*
     * 直接外部推送
     */
    public function ds()
    {
        $order_sn = I('order_sn', '');
        $work = I('work', false, 'boolval');
        if (!empty($order_sn)) {
            echo post_curl(ORDERAPI_DOMAIN.'/order/wmsorder', ['order_sn' => $order_sn, 'work' => $work]);
        }
    }

}
