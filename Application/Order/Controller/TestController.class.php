<?php
namespace Order\Controller;
use Home\Controller\LoginController;
use Home\Services\UcenterService;
use Order\Service\CartService;
use Pcb\Service\Guzzle;
use Think\Controller;
use Order\Controller\BaseController;

use function PHPSTORM_META\type;

class TestController extends BaseController
{
    protected $abcdefg;

    public function _initialize(){
        parent::_initialize();
//        $this->abcdefg  = new \Common\Service\JaegerInject("opentarcing-php");
    }

    public function test()
    {
       
        dump(S_user(132654));
        p(S_user(447272));exit;

        $send_qualify = json_decode(get_curl(MARKET_DOMAIN . '/webapi/increaseQualifyToUser', array('user_id' => 400654, 'lottery_id' => 282, 'increase_draw_count' => 1, 'order_id' =>33344455,'increase_type' => 4)),true);
        p($send_qualify);
        exit;

        // p(decryptParameter("bMmc/5V9gmfZMwVNZG4EBy5zUEJENFJsQ3ZEd056eDk0NUR1anlrQjBNdUY5VFN4SmlueWxHa1pUbm9rPQ=="));
        $str = "S2025070927391SZ";
        $a = simpleEncrypt($str,"ichunt@1iliLIiiilll110O0O0");
        $b = simpleDecrypt($a,"ichunt@1iliLIiiilll110O0O0");
        p($str);
        p($a);
        p($b);

        exit;
        $order_id = I("order_id","123456");
        $send_qualify = json_decode(get_curl(MARKET_DOMAIN . '/webapi/increaseQualifyToUser', array('user_id' => 431475, 'lottery_id' => 282, 'increase_draw_count' => 1, 'order_id' => $order_id,'increase_type' => 4)),true);


        p($send_qualify);
        exit;


    }

    public function refund()
    {


        $order = array(
            'is_aggregate_pay' => true,
            'out_refund_no' => 'T-12019111152757-0',//退款单号
            'order_amount' => 1.79,//支付金额
            'refund_amount' => 1.79,//退款金额
            'serial_number' => '4200000462201911113700065831',//支付交易流水号
            'pay_order_sn' => '12019111152757_1_1713292545412',//支付订单号
            'pay_create_time' => 1573463621,//第三方创建支付单据时间
        );
        // Vendor('payment.cpcn.cpcn');//中金支付sdk引入
        // dump($order);


        Vendor("payment.wxpay.wxpay");
        $this->Pay = new \wxpay();

        // exit;
        // die;
        // $this->Pay = new \Payment\Cpcn(\Payment\Cpcn::BUSINESS_MODE);
        // var_dump($this->Pay);
        // dump($this->Pay->query("4200000462201911113700065831"));
        $refund = $this->Pay->refund($order);
        dump($refund);
    }

    public function aa()
    {
        $info = D('Order')->getInfo(731137);
        dump($info);
        // $save = array(
        //     'pay_type' => 4,
        //     'price' => $info['order_amount'],
        //     'serial_number' => '-',
        //     'pay_order_sn' => '',
        //     'pay_id' => 0,
        //     'pay_name' => '账期支付',
        // );
        $save = array(
            'pay_type' => 3,
            'price' => $info['order_amount'] - $info['advance_amount'],
            'serial_number' => '',
            'pay_order_sn' => '',
            'pay_id' => 0,
            'pay_name' => '交通银行',
        );
        dump($save);
        die;
        global $_REQUEST;

        $_REQUEST = array_merge(authkey());
        $res = A('Order/Pay')->setPayOrder($info, $save);
        dump($res);
    }

    public function removal()
    {
        // echo D('RemovalItems')->where(['removal_items_id' => array('in', '1425,1426,1427')])->save(array('pur_syn'=>1));
    }

    public function cny()
    {
        echo cny_number('14100.00');
    }

    public function wms()
    {
        $order_sn = I('order_sn', '');
        $work = I('work', false, 'boolval');
        if (!empty($order_sn)) {
            echo post_curl(ORDERAPI_DOMAIN.'/order/wmsorder', ['order_sn' => $order_sn, 'work' => $work]);
        }
        // echo D('OrderExtend')->where(['order_id' => 446400])->setField('wms_order', -1);
    }

    public function erp()
    {
        $order_id = I('order_id');
        echo D('Order')->where(['order_id' => $order_id])->setField('wms_syn', 1);
    }

    public function cancel()
    {
        $datas = array(
            'order_id' => 782498,
            'Items' => array('45987' => 5000),
            'reduce' => false,
            'reduce_wms' => false,
        );
        $data['type'] = 'sku.unlock';
        $data['data'] = urlencode(json_encode($datas));
        $data['timestamp'] = time();
        $res = post_curl(SERVICE_DOMAIN.'/transpond/wmsapi/WebApiUnLockStock?token='.service_token($data['data'], $data['timestamp']), $data, array('timeout' => 10));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        dump($res);
        return $res;
    }

    public function wallet()
    {
    }

    public function response()
    {
//         Vendor('payment.cpcn.cpcn');//中金支付sdk引入
//         $jsons = array(
// '{"message":"PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+PFJlcXVlc3Q+PEhlYWQ+PFR4Q29kZT4xMzQ4PC9UeENvZGU+PC9IZWFkPjxCb2R5PjxJbnN0aXR1dGlvbklEPjAwMzIwNDwvSW5zdGl0dXRpb25JRD48U2VyaWFsTnVtYmVyPjMyMDE5MDMyODE2ODk4XzMyMjwvU2VyaWFsTnVtYmVyPjxBbW91bnQ+NTg0PC9BbW91bnQ+PFN0YXR1cz40MDwvU3RhdHVzPjxPcmRlck5vPjAwMDAwMDAwMDwvT3JkZXJObz48U3VjY2Vzc1RpbWU+MjAxOTA0MDIxMDQ1NTY8L1N1Y2Nlc3NUaW1lPjxUcmFuc2ZlclRpbWU+MjAxOTA0MDIxMDQ1NTY8L1RyYW5zZmVyVGltZT48RXJyb3JNZXNzYWdlLz48L0JvZHk+PC9SZXF1ZXN0Pg==","signature":"10CC2B3748506A1D33B7907473932DFABDBA90BBA8E3F8FA3CD51257F6901DC9A51F7B864F72B22CF6196D31902019A9DB0556FEE88314534B3E3ED9597DCB454CC3484084AEAF127FF5646AE12747B7F1AC75515D978079E17880F954955B33317883085E3344549AC43C9DE52FA214193142F820F11248EFF2478F4C65C5D2","pay_code":"003204"}'
//         );
//         $payment = S('payment');
//         foreach ($jsons as $json) {
//             $arr = json_decode($json, true);
//             if ($arr['pay_code'] == '003204') {
//                 $mode = \Payment\Cpcn::MARKET_MODE;
//             } else {
//                 $mode = \Payment\Cpcn::BUSINESS_MODE;
//             }
//             $Pay = new \Payment\Cpcn($mode);
//             global $_GET;
//             global $_POST;
//             $_GET['pay_code'] = $arr['pay_code'];
//             $_POST['message'] = $arr['message'];
//             $_POST['signature'] = $arr['signature'];
//             $arr = $Pay->response();
//             $data['wallet_sn'] = $arr['order_sn'];
//             $data['serial_number'] = $arr['serial_number'];
//             $data['create_time'] = $arr['transfer_time'];
//             $data['amount'] = $arr['settle_amount'];
//             $data = array_merge($data, authkey());
//             $res = post_curl(API_DOMAIN.'/wallet/setwithdrawfinish', $data);
//             dump($res);die();
//             extract($a);

//             $info = explode('-', $order_sn);//获取退款订单 例如T-1201812131234
//             $order_sn = $info[1];
//             $OrderModel = D('Order');
//             $OrderRefundModel = D('OrderRefund');
//             $info = $OrderModel->getInfo('', $order_sn);
//             $res = $OrderRefundModel->setNotSerialNumber($info['order_id'], $serial_number);
//             if ($res === false) {
//                 return $this->apiReturn(41059, '记录流水号失败');
//             }
//             $data = array(
//                 'user_id' => $info['user_id'],
//                 'order_id' => $info['order_id'],
//                 'order_sn' => $order_sn,
//                 'price' => $order_amount,
//                 'pay_order_sn' => $pay_order_sn,
//                 'serial_number' => $serial_number,
//                 'pay_id' => $payment[$pay_code]['pay_id'],
//                 'pay_name' => $payment[$pay_code]['pay_name'],
//                 'pay_time' => $create_time,
//             );
//             dump($data);

//             $data = array_merge($data, authkey());
//             $res = post_curl(API_DOMAIN.'/wallet/setrefund', $data);
//             if (!empty($res)) {
//                 $res = json_decode($res, true);
//             }
//             dump($res);
//             // if ($res['err_code'] != 0) {
//             //     \LogReport::write(\LogReport::anlyError('支付失败:'.json_encode(I('request.')), __FILE__, __LINE__, $res['err_code'], __METHOD__, get_client_ip(0, true)));
//             //     return $this->apiReturn($res['err_code'], $res['err_msg']);
//             // }
//             dump($a);
//         }
    }

    const WX = 'wxpay';
    const ALI = 'alipay';
    const UNION = 'unionpay';
    const UNIONB2B = 'unionpay_b2b';
    const CPCN = 'cpcn';

    static $topay_name = array(
        '1' => '全款',
        '2' => '预付款',
        '3' => '尾款',
    );

    /**
     * 获取微信信息
     * @param  string $url  [description]
     * @param  string $code [description]
     * @return [type]       [description]
     */
    public function getWxInfo($url = '', $code = '')
    {
        $data['backUrl'] = $url;
        $data['code'] = $code;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN.'/login/getwechatinfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }
    //前往支付页
    public function toDo($act = '')
    {
        //来源
        // $_SERVER['HTTP_REFERER'] = 'http://m.liexin.com';
        if (preg_match('/^http(?:s)?:\/\/([^\/?]+)/', $_SERVER['HTTP_REFERER'], $args)) {
            $this->fullreferer = $args[0];// 例子http://www.liexin.com
            $this->referer = $args[1];// 例子www.liexin.com
        }
        $this->pay_code = I('request.pay_code', '');
        $payment = S('payment');
        if (empty($payment[$pay_code])) {
            $PaymentModel = D('Payment');
            $payment[$pay_code] = $PaymentModel->getInfoByCode($pay_code);
            if (!empty($payment[$pay_code])) {
                S('payment', $payment);
            }
        }

        is_null($rescue) && $rescue = I('request.rescue');//备用方案
        if ($rescue) {
            //旧版有银联支付B2B\B2C、微信、支付宝
            if (empty($payment[$pay_code])) {
                return $this->apiReturn(41028, '服务器点对点，无效付款方式');
            }
            Vendor('payment.'.$pay_code.'.'.$pay_code);
            $this->Pay = new $pay_code();

        } else {
            //新版中金银联支付B2B\B2C、微信、支付宝
            $cpcn = self::CPCN;
            Vendor('payment.'.$cpcn.'.'.$cpcn);
            $this->Pay = new $cpcn($cpcn::BUSINESS_MODE);
            //非直通车 不支持 前置选择B2B还是B2C，故无法得知是B2B还是B2C
            if ($pay_code == self::UNIONB2B) {
                $this->pay_code = self::UNION;
            }
        }
        $pay_code = $this->pay_code;
        $order_id = I('request.order_id', 0, 'intval');
        $pay_mode = I('request.pay_mode', 0, 'intval');//支付宝 0前置二维码 2跳转 || 微信 0扫码支付 1H5支付 2公众号支付
        $bank_id = I('request.bank_id', 0, 'intval');
        $account_type = I('request.account_type', 1, 'intval');//1:B2C 或 2:B2B
        $site_type = I('request.site_type', 1, 'intval');//1PC 2H5
        $open_id = I('open_id', '', 'trim');//小程序传入
        $miniProgram = cookie('miniProgram');

        switch ($site_type) {
            case '1' :
                $domain = WWW_DOMAIN;
                break;
            case '2' :
                $domain = M_DOMAIN;
                break;
            default :
                exit();
        }
        $err_url = $domain.'/v3/user/orderdetail?order_id='.$order_id;

        //订单信息
        $OrderModel = D('Order');
        $info = $OrderModel->getInfo($order_id);
        if (empty($info)) {
            return $this->apiReturn(41036, '未找到相关订单');
        }
        if ($info['delivery_place'] == 2) {
            !empty($this->fullreferer) && redirect($err_url);
            return $this->apiReturn(41042, '暂不支持该付款方式', $info);
        }
        if ($info['status'] < 2) {
            !empty($this->fullreferer) && redirect($err_url);
            return $this->apiReturn(41040, '订单尚未审核，无法付款', $info);
        } elseif ($info['status'] > 3) {
            !empty($this->fullreferer) && redirect($err_url);
            return $this->apiReturn(41041, '订单已付款，无法再付款', $info);
        } elseif ($info['order_goods_type'] == 2 && $info['sale_type'] != 2) {//自营现卖

        }

        $behavior_param = array('order_id' => $info['order_id'], 'order_sn' => $info['order_sn'], 'pay_code' => $pay_code);

        //当前支付行为
        $topay_type = 1;
        if ($info['order_pay_type'] == 2 && $info['status'] == 2) {
            $topay_type = 2;
        } elseif ($info['order_pay_type'] == 2) {
            $topay_type = 3;
        }
        $info['topay_type'] = $topay_type;

        //由于坑爹微信，所以单独写个处理微信的多支付方式
        if ($pay_code == self::WX) {
            //判断是否是微信浏览器
            $agent = getAgentInfo();
            if ($agent['bro'] == 9 && $pf == 6 || !in_array($miniProgram, array('false', null))) {
                $pay_mode = 3;//小程序支付
            } elseif ($agent['bro'] == 9) { //微信内打开 请求授权 获取open_id
                $pay_mode = 2;//公众号支付
            } elseif ($pay_code == self::WX && $site_type == 2) {
                $pay_mode = 1;//H5支付
            } elseif ($pay_code == self::WX) {
                $pay_mode = 0;//扫码支付
            }

            if ($pay_mode == 2) {
                $open_id = session('open_id');
                $param = array(
                    'pay_code' => $pay_code,
                    'order_id' => $order_id,
                    'site_type' => $site_type,
                    'pf' => platform(),
                );
                $url = strtolower(rtrim(API_DOMAIN, '/').'/'.CONTROLLER_NAME.'/'.ACTION_NAME.'?'.http_build_query($param));
                if (empty($open_id)) {
                    $code = I('code', '');
                    $res = $this->getWxInfo($url, $code);
                    if ($res['data']['status'] == 2) { //用户确认授权页面
                        return $this->apiReturn(0, 'oauth', $res['data']['data']);
                    }
                }
            }

            //用于微信刷新二维码使用
            if ($act == 'reload') {
                $str = $this->wxMulitPay($info, $pay_mode, $open_id, false);
                try {
                    $this->behavior_push(7, 0, $behavior_param);
                } catch (\Exception $e) {
                }
                return $str;
            }
            $str = $this->wxMulitPay($info, $pay_mode, $open_id, true);
            try {
                $this->behavior_push(7, 0, $behavior_param);
            } catch (\Exception $e) {
            }
            if ($pay_mode == 3) {
                return $this->apiReturn(0, 'program', $str);
            } elseif ($pay_mode != 2) { //微信内打开 请求授权 获取unionId
                return $this->apiReturn(0, 'QR', $str);
            } elseif (I('callback')) {
                return $this->apiReturn(0, 'js', $url);
            }

        } elseif ($pay_code == self::ALI){
            //用于刷新二维码使用
            if ($act == 'reload') {
                $str = $this->aliMulitPay($info, $pay_mode);
                try {
                    $this->behavior_push(7, 0, $behavior_param);
                } catch (\Exception $e) {
                }
                return $str;
            }

            $str = $this->aliMulitPay($info, $pay_mode);
            $now_pay_type = strtolower(get_class($this->Pay));//当前支付实例名
            if ($now_pay_type == self::CPCN && $pay_mode == 0) {//中金支付宝二维码
                try {
                    $this->behavior_push(7, 0, $behavior_param);
                } catch (\Exception $e) {
                }
                return $this->apiReturn(0, 'QR', $str);
            }
        } else {
            //网银
            $str = $this->bankMulitPay($info, $pay_mode);
        }
        echo $str;
        try {
            $this->behavior_push(7, 0, $behavior_param);
        } catch (\Exception $e) {
        }
        // dump($str, 1,'',0);
    }


    /**
     * 统一格式返回
     * @param  integer $code   [description]
     * @param  string  $msg    [description]
     * @param  array   $extend [description]
     * @return [type]          [description]
     */
    public function apiReturn($code = 0, $msg = '', $extend = array())
    {
        $callback = I('callback', '');
        $data = array(
            'err_code' => $code,
            'err_msg' => $msg,
            'data' => $extend,
        );
        if ($code != 0) {
            $this->apiLog($code, $msg, $extend);
            if ($code > 0) {
                unset($data['data']);
            }
            $data['err_code'] = abs($data['err_code']);
        }
        if (!empty($callback)) {
            $res = $callback.'('.json_encode($data).')';
        } else {
            $res = json_encode($data);
        }
        //判断是否内部调用，内部调用不输出只返回
        $near_trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        $backtrace = array_pop($near_trace);
        $backtrace_arr = explode('\\', $backtrace['class']);
        $backtrace_name = array_pop($backtrace_arr);
        $index = strpos($backtrace_name, C('DEFAULT_C_LAYER'));

        $file = !empty($near_trace[0]['file']) ? $near_trace[0]['file'] : '';
        $line = !empty($near_trace[0]['line']) ? $near_trace[0]['line'] : 0;
        $method = !empty($backtrace['function']) ? $backtrace['function'] : '';

        if (strtolower(CONTROLLER_NAME) == strtolower(substr($backtrace_name, 0, $index)) && strtolower(ACTION_NAME) == strtolower($backtrace['function'])) {
            if ($data['err_code'] != 0) {
                \LogReport::write(\LogReport::anlyError($msg, $file, $line, $data['err_code'], $method));
            }
            header('Content-type:application/json');
            echo $res;
            \Think\Log::save();

        } elseif (strtolower(substr($backtrace_name, 0, $index)) == 'base' || strtolower($backtrace['function']) == '_initialize') {
            if ($data['err_code'] != 0) {
                \LogReport::write(\LogReport::anlyError($msg, $file, $line, $data['err_code'], $method));
            }
            header('Content-type:application/json');
            echo $res;
            \Think\Log::save();
            exit();
        }
        return $data;
    }

    /**
     * 支付宝 应对中金 多处理方式
     * @param  [type]  $info     [description]
     * @param  [type]  $pay_mode [description]
     * @param  boolean $cache    [description]
     * @return [type]            [description]
     */
    private function aliMulitPay($info, $pay_mode)
    {
        //当前支付行为
        $topay_type = $info['topay_type'];
        //订单号——订单支付类型——时分秒7位随机码  包分隔符共30位
        //银联40位 、 支付宝64位 、 微信32位
        $serial_arr = array($info['order_sn'], $topay_type, date('His').hash_key(7,2));
        $serial_number = implode('_', $serial_arr);

        if ($pay_mode == 2) {//H5支付使用原生
            $this->_pay($this->pay_code, true);
        }

        $info['topay_name'] = self::$topay_name[$topay_type];
        $info['serial_number'] = $serial_number;
        $order = $this->orderPayInfo($info);

        //获取支付信息
        $config = array(
            'pay_code' => $this->pay_code,
            'type' => $pay_mode,//旧使用
            'pay_mode' => $pay_mode,//新使用
            'qr_pay_mode' => 2,//支付宝好看模式
        );
        $str = $this->Pay->get_code($order, $config);
        return $str;
    }


    /**
     * 网银支付
     * @param  [type]  $info     [description]
     * @param  [type]  $pay_mode [description]
     * @param  boolean $cache    [description]
     * @return [type]            [description]
     */
    private function bankMulitPay($info, $pay_mode)
    {
        //当前支付行为
        $topay_type = $info['topay_type'];
        //订单号——订单支付类型——时分秒7位随机码  包分隔符共30位
        //银联40位 、 支付宝64位 、 微信32位
        $serial_arr = array($info['order_sn'], $topay_type, date('His').hash_key(7,2));
        $serial_remark = $info['order_sn'];

        if ($pay_mode == 2) {//H5支付使用原生
            $this->_pay($this->pay_code, true);
        }
        $now_pay_type = strtolower(get_class($this->Pay));//当前支付实例名
        //交易流水号（唯一）
        if ($now_pay_type == self::CPCN) { // 支付宝长度64位 字母数字下划线
            $serial_number = implode('_', $serial_arr);
        } else { // 银联长度32位 字母数字
            $serial_number = implode('X', $serial_arr);
        }

        $info['bank_id'] = $bank_id;
        $info['account_type'] = $account_type;
        $info['user_id'] = $info['user_id'];
        $info['serial_remark'] = $serial_remark;
        $info['topay_name'] = self::$topay_name[$topay_type];
        $info['serial_number'] = $serial_number;
        $order = $this->orderPayInfo($info);

        //获取支付信息
        $config = array(
            'pay_code' => $this->pay_code,
            'pay_mode' => $pay_mode,//新使用
        );
        $str = $this->Pay->get_code($order, $config);
        return $str;
    }

    /**
     * 针对微信多支付方式处理（PC扫码、H5支付、公众号支付）
     * 微信一个订单号绑定一种支付方式、多种支付方式需要多个订单号
     * 微信订单只有2个小时有效期，过期需重新换个后缀生成
     * @return [type] [description]
     */
    private function wxMulitPay($info, $pay_mode, $open_id = '')
    {
        //当前支付行为
        $topay_type = $info['topay_type'];
        //订单号——订单支付类型——时分秒7位随机码  包分隔符共30位
        //银联40位 、 支付宝64位 、 微信32位
        $serial_arr = array($info['order_sn'], $topay_type, date('His').hash_key(7,2));
        $serial_number = implode('_', $serial_arr);

        $info['topay_name'] = self::$topay_name[$topay_type];
        $info['serial_number'] = $serial_number;
        $order = $this->orderPayInfo($info);

        //获取支付信息
        $config = array(
            'pay_code' => $this->pay_code,
            'pay_mode' => $pay_mode,
            'open_id' => $open_id,//$open_id,
        );
        //H5支付使用原始微信支付
        if ($pay_mode == 1) {
            $this->_pay($this->pay_code, true);
        }
        $str = $this->Pay->get_code($order, $config);
        return $str;
    }

    /**
     * 支付所需相关订单信息
     * @param  [type] $info       [description]
     * @param  string $topay_name [description]
     * @return [type]             [description]
     */
    private function orderPayInfo($info)
    {
        $OrderItemsModel = D('OrderItems');
        $OrderPriceModel = D('OrderPrice');
        // $names = $OrderItemsModel->getAllName($info['order_id']);
        $price = $OrderPriceModel->getToPay($info['order_id'], $info['order_pay_type'], $info['status'], $info['advance_amount']);
        $order = array(
            'order_id' => $info['order_id'],
            'order_sn' => $info['order_sn'],
            'user_id' => $info['user_id'],
            'bank_id' => $info['bank_id'],
            'account_type' => $info['account_type'] == 1 ? 11 : 22,
            'serial_number' => $info['serial_number'],
            'remark' => $info['serial_remark'],
            'create_time' => $info['create_time'],
            'order_amount' => $price,
            'topay_name' => $info['topay_name'],
            'goods_items' => '电子元器件',//implode(',', $names),
            'usage' => '销售订单款项',
        );
        return $order;
    }


    public function invoice()
    {
        $list = D('Taxinfo')->where(['inv_type' => 3])->order('is_default desc')->group('user_id')->field('user_id, tax_title, company_phone')->select();
        foreach ($list as $v) {
            $redis = S_company($v['user_id']);
            $company = D('UserCompany')->where(['user_id' => $v['user_id']])->find();
            dump($company);
            dump($redis);
            if (empty($company) && empty($redis)) {
                echo 1;
                // $com_id = D('UserCompany')->add([
                //     'user_id' => $v['user_id'],
                //     'com_name' => $v['tax_title'],
                //     'com_telphone' => $v['company_phone'],
                // ]);
                // if ($com_id !== false) {
                //     S_company($v['user_id'], [
                //         'user_id' => $v['user_id'],
                //         'com_name' => $v['tax_title'],
                //         'com_telphone' => $v['company_phone'],
                //         'brand_list' => false,
                //     ]);
                // }
            } elseif (empty($company) && !empty($redis)) {
                echo 2;
                // if (!empty($redis['com_name']) && $v['tax_title'] != $redis['com_name']) {//缓存！=发票
                //     $com_id = D('UserCompany')->add([
                //         'user_id' => $v['user_id'],
                //         'com_name' => $redis['com_name'],
                //         'com_telphone' => $redis['com_telphone'],
                //     ]);
                // } elseif (!empty($redis['com_name'])) {//缓存 == 发票
                //     $com_id = D('UserCompany')->add([
                //         'user_id' => $v['user_id'],
                //         'com_name' => $v['tax_title'],
                //         'com_telphone' => $v['company_phone'],
                //     ]);
                // } else {
                //     $com_id = D('UserCompany')->add([
                //         'user_id' => $v['user_id'],
                //         'com_name' => $v['tax_title'],
                //         'com_telphone' => $v['company_phone'],
                //     ]);
                //     if ($com_id !== false) {
                //         $redis['com_id'] = $com_id;
                //         $redis['com_name'] = $v['tax_title'];
                //         $redis['com_telphone'] = $v['company_phone'];
                //         S_company($v['user_id'], $redis);
                //     }
                // }
            } elseif (empty($company['com_name']) && empty($redis['com_name'])) {
                echo 3;
                // $redis['com_name'] = $v['tax_title'];
                // $redis['com_telphone'] = $v['company_phone'];
                // S_company($v['user_id'], $redis);
                // D('UserCompany')->save([
                //     'com_id' => $company['com_id'],
                //     'com_name' => $v['tax_title'],
                //     'com_telphone' => $v['company_phone'],
                // ]);
            } elseif (empty($company['com_name']) && !empty($redis['com_name'])) {
                echo 4;
                // D('UserCompany')->save([
                //     'com_id' => $company['com_id'],
                //     'com_name' => $redis['com_name'],
                //     'com_telphone' => $redis['com_telphone'],
                // ]);
            } elseif (!empty($company['com_name']) && empty($redis['com_name'])) {
                echo 5;
                // $redis['com_name'] = $company['com_name'];
                // $redis['com_telphone'] = $company['com_telphone'];
                // S_company($v['user_id'], $redis);
            }

        }

    }

    protected function getFinalGoods($id, $num, $currency, $power = array())
    {
        $default = array(
            'newCustomer' => false,
            'member' => false,
//            "fast"=>1,
        );
        $data['id'] = $id;
        $data['num'] = $num;
        $data['currency'] = $currency;
        $data['power'] = array_intersect_key($power, $default);
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN.'/goods/finalinfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    //处理订单
    public function dealwithOrder(){
        try{

            $res = $this->lockSku(972677, ['28351'=>40]);

            dump($res);

            $a = D("Order")->where(["order_id"=>972677])->save([
                "sale_type"=>1
            ]);

            dump($a);
            exit;
            set_time_limit(0);
            $list = D("order")->where("from_unixtime(create_time,'%Y-%m-%d') >= '2019-12-31'")->field("order_id,currency,from_unixtime(create_time,'%Y-%m-%d')")->select();

            foreach($list as $k =>$item){
//                dump($k);
//                if($k > 500) break;
                dump($item['order_id']);
                $orderItemsList = D("orderItems")->where("order_id =".intval($item['order_id']))->field("rec_id,goods_number,goods_id,goods_class,goods_unit")->select();
                if($orderItemsList){
                    foreach($orderItemsList as $orderItems){
                        if(!$orderItems['goods_id']) continue;

                        if($orderItems['goods_class'] !="" || orderItems['goods_unit'] != ""){
                            continue;
                        }
                        $power = ['newCustomer' => false,
                            'member' => false,];
                        dump("999999999999999");
                        $res = $this->getFinalGoods($orderItems['goods_id'], $orderItems['goods_number'], $orderItems['currency'], $power);
                        dump($res['err_code']);
                        if($res['err_code'] == 0){
                            $info = $res['goods_info'];
                            $class2_name           = isset($info['class2_name']) ? $info['class2_name'] : '';//二级分类
                            $goods_unit_name           = isset($info['goods_unit_name']) ? $info['goods_unit_name'] : '';//单位
                            $bk = D("orderItems")->where(["rec_id"=>$orderItems['rec_id']])->save([
                                'goods_class'=>$class2_name,
                                'goods_unit'=>$goods_unit_name,
                            ]);
                            dump($class2_name);
                            dump($goods_unit_name);
                            dump($bk);
                        }

                    }
                }
            }
        }catch(\Exception $e){
            dump($e->getMessage());
        }

    }


    //处理erp删订单的情况
    public function dealWithErpDeleteOrderItem($id="123",$a=[]){

        dump($id);
        dump($a);

        dump(I('request.id',""));
        dump(I('request.a',[]));

exit;
        $a = A('Order/Order')->contractInfo(4304);
        dump($a);
        exit;
//        (new \Order\Model\OrderActionLogModel)->addLog(
//            $order_id=989518,
//            $operator_id=1000,
//            $operator_type = 2,
//            $event=sprintf("erp删除了订单中的商品，型号为 %s 单价为 %s 数量为 %s ，订单总价格调整为 %s",
//                "TS5A3159QDBVR","6.200000","8","￥8678.37"));
//
//        exit;
        $order_id = 989518;
        $orderInfo = D("Order")->where(["order_id"=>$order_id])->field("order_id,order_sn,order_amount,advance_amount,status")->find();
        dump($orderInfo);
        $orderItems = D("OrderItems")->where(["order_id"=>$order_id])->field("rec_id,order_id,goods_id,goods_sn,goods_name,sku_name,goods_number,goods_price,status")->select();
        dump($orderItems);
        $orderPrice = D("OrderPrice")->where(["order_id"=>$order_id])->select();
        dump($orderPrice);
        $payLog = D("PayLog")->where(["order_id"=>$order_id])->select();
        dump($payLog);


//        $order_amount = 8678.37;
//        $bk = D("Order")->where(["order_id"=>$order_id])->save(["order_amount"=>$order_amount]);
//        dump(sprintf("修改订单金额 %s 是否成功 %s ",$order_amount,$bk));
//
//
//        $rec_id = [1088979];
//        foreach($rec_id as $k=>$v){
//            $bk = D("OrderItems")->where(["order_id"=>$order_id,"rec_id"=>$v])->save(["status"=>-1]);
//            dump(sprintf("删除商品 %s  是否成功 %s ",$v,$bk));
//        }
//
//
//        $price_id = 718531;
//        $price = -8678.37;
//        D("OrderPrice")->where(["order_id"=>$order_id,"price_id"=>$price_id,"price_type"=>-1])->save(["price"=>$price]);
//
//
//        $price_id = 715078;
//        $price = 8678.37;
//        D("OrderPrice")->where(["order_id"=>$order_id,"price_id"=>$price_id,"price_type"=>1])->save(["price"=>$price]);
//
//
//
//        $pay_log_id = 939890;
//        $pay_amount = 8678.37;
//        D("PayLog")->where(["order_id"=>$order_id,"pay_log_id"=>$pay_log_id])->save(["pay_amount"=>$pay_amount]);




    }

    public function opentarcing_one(){
        set_time_limit(0);
//        $jaeger = new \Common\Service\JaegerInject("opentarcing-php");
        $jaeger = \Common\Service\JaegerInject::getInstance();
        $jaeger->init("order");


        $jaeger->setTag("opentarcing_one","事件","创建订单");
        $jaeger->log("opentarcing_one","请求参数",json_encode(cookie()));
        $jaeger->log("opentarcing_one","hello world");
        $jaeger->finish("opentarcing_one");
        $jaeger->finish("opentarcing_one");
        $jaeger->finish("opentarcing_one");

        $spanName = "order-admin";
        $jaeger->start($spanName);
        $injectHeaders = $jaeger->inject($spanName);

        $jaeger->setTag("order-admin","事件","创建订单");


        $client =  new \GuzzleHttp\Client;
        $method = 'GET';
        $url = 'http://crm.ichunt.net/';
        $res = $client->request($method, $url, ['headers' => $injectHeaders]);

        $jaeger->finish($spanName);
        $jaeger->finish($spanName);
        $jaeger->finish($spanName);

//        $this->opentarcing_two();

        $spanName = "ichunt-api";
        $jaeger->start($spanName);
        $injectHeaders = $jaeger->inject($spanName);
        $client =  new \GuzzleHttp\Client;
        $method = 'GET';
        $url = 'http://order.ichunt.net/';
        $res = $client->request($method, $url, ['headers' => $injectHeaders]);
        $jaeger->finish($spanName);
        $jaeger->finish($spanName);
        $jaeger->finish($spanName);


        $jaeger = \Common\Service\JaegerInject::getInstance();
        $jaeger->init("user");


        //--------------crm.net-----下包含子span---crm.child.net------------------------------
        $spanName = "crm.net";
        $jaeger->start($spanName);
        $injectHeaders = $jaeger->inject($spanName);
        $client =  new \GuzzleHttp\Client;
        $method = 'GET';
        $url = 'http://label.ichunt.net/';
        $res = $client->request($method, $url, ['headers' => $injectHeaders]);
        $jaeger->finish($spanName);
        $jaeger->finish($spanName);

        $spanName = "crm.child.net";
        $parentSpanName = $jaeger->getSpanName("crm.net");
        $jaeger->start($spanName,$parentSpanName);
        $injectHeaders = $jaeger->inject($spanName);
        $client =  new \GuzzleHttp\Client;
        $method = 'GET';
        $url = 'http://crm.ichunt.net/';
        $res = $client->request($method, $url, ['headers' => $injectHeaders]);
        $jaeger->finish($spanName);

        //----------------crm.net-----下包含子span---crm.child.net-------------------------------

        $jaeger = \Common\Service\JaegerInject::getInstance();
        $jaeger->init("task");
//        $this->opentarcing_two();

//        dump($_SERVER["UBER-TRACE-ID"]);
        $spanName = "crm.net.6677";
        $jaeger->start($spanName);
        $injectHeaders = $jaeger->inject($spanName);
        $client =  new \GuzzleHttp\Client;
        $method = 'GET';
        $url = 'http://label.ichunt.net/';
        $res = $client->request($method, $url, ['headers' => $injectHeaders]);
        $jaeger->finish($spanName);
        $jaeger->finish($spanName);


//exit;

        $spanName = "跨进程访问http2.php";
        $jaeger->start($spanName);

        $injectHeaders = $jaeger->inject($spanName);
        $client =  new \GuzzleHttp\Client;
        $method = 'GET';
        $url = 'http://api.liexin.com/order/test/opentarcing_two';
        $jar = new \GuzzleHttp\Cookie\CookieJar();
        $cookieJar = $jar->fromArray(cookie(), "liexin.com");
        $res = $client->request($method, $url, ['headers' => $injectHeaders,"cookies"=>$cookieJar]);
        $jaeger->finish($spanName);


        $spanName = "crm.net";
        $jaeger->start($spanName);
        $injectHeaders = $jaeger->inject($spanName);
        $client =  new \GuzzleHttp\Client;
        $method = 'GET';
        $url = 'http://label.ichunt.net/';
        $res = $client->request($method, $url, ['headers' => $injectHeaders]);
        $jaeger->finish($spanName);
        $jaeger->finish($spanName);


    }

    public function opentarcing_two()
    {



        set_time_limit(0);
//        $jaeger = new \Common\Service\JaegerInject("opentarcing-php");
        $jaeger = \Common\Service\JaegerInject::getInstance();
        $spanName = "crm.net.1";

        $injectHeaders = $jaeger->simpleInit("pcb",$spanName);
        $client =  new \GuzzleHttp\Client;
        $method = 'GET';
        $url = 'http://label.ichunt.net/';
        $res = $client->request($method, $url, ['headers' => $injectHeaders]);
        $jaeger->finish($spanName);


        $spanName = "crm.net.2";
        $jaeger->simpleInject($spanName);

        sleep(2);
        $jaeger->setTag("$spanName","xx","oo");
        $spanName = "crm.child.net.1";
        $injectHeaders = $jaeger->simpleInject($spanName,"crm.net.1");

        $client =  new \GuzzleHttp\Client;
        $method = 'GET';
        $url = 'http://crm.ichunt.net/';
        $res = $client->request($method, $url, ['headers' => $injectHeaders]);
        sleep(3);

        $jaeger->finish($spanName);


        $spanName = "crm.net.2";
        $jaeger->simpleInject($spanName);
        $jaeger->setTag($spanName,"xx","oo");
        $jaeger->finish($spanName);

    }
}