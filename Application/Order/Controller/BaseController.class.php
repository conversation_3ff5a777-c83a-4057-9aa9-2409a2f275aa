<?php

namespace Order\Controller;

class BaseController extends \Common\Controller\BaseController
{

    public $opentracingJager; //链路追踪

    public function _initialize()
    {
        parent::_initialize();
        $this->opentracingJager = \Common\Service\JaegerInject::getInstance();
    }

    /**
     * 发送订单发票接口
     * @param  [type] $sn [description]
     * @return [type]     [description]
     */
    protected function getOrderInvoice($sn)
    {
        $data['sn'] = $sn;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/invoice/orderinvinfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取指定商品最终价格及信息
     * @param  [type] $id       [description]
     * @param  [type] $num      [description]
     * @param  [type] $currency [description]
     * @return [type]           [description]
     */
    protected function getFinalGoods($id, $num, $currency, $power = array())
    {
        try {
            if (!isset($power["user_id"])) {
                $power["user_id"] = 0;
            }
            if (!isset($power["email"])) {
                $power["email"] = '';
            }
            if (!isset($power["mobile"])) {
                $power["mobile"] = '';
            }
            $user_id = $this->getUid();
            if ($user_id > 0) {
                $power["user_id"] = $user_id;
                $userInfo = S_user($user_id);
                $power["mobile"] = isset($userInfo['mobile']) ? $userInfo['mobile'] : '';
                $power["email"] = isset($userInfo['email']) ? $userInfo['email'] : '';
            }
        } catch (\Exception $e) {
        }

        $default = array(
            'newCustomer' => false,
            'member' => false,
            "assemble" => 1,
            "mobile" => '',
            "email" => '',
            "user_id" => 0,
            "invoice" => "", //增值税普通发票公司名字,活动价时必须，否则可能导致用户无法享受活动价
            "special_invoice" => "", //增值税专用发票公司名字,活动价时需要，否则可能导致用户无法享受活动价
            "verify_blacklist" => false, //是否验证黑名单，用于折扣活动提交订单页面与后台下单
        );

        //        $data['id'] = $id;
        //        $data['num'] = $num;
        //        $data['currency'] = $currency;
        //        $data['power'] = array_intersect_key($power, $default);
        //        $data = array_merge($data, authkey());
        //        dump($data);
        //        $res = post_curl(API_DOMAIN.'/goods/finalinfo', $data);
        $power = array_intersect_key($power, $default);
        $res = A("Goods/Goods")->finalinfo($id, $num, $currency, $power);
        if (!empty($res) && is_string($res)) {
            $res = json_decode($res, true);
        }

        return $res;
    }

    /**
     * 发送商品接口(原始数据)
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    protected function getGoods($id)
    {
        $data['id'] = $id;
        $data = array_merge($data, authkey());
        //        $res = post_curl(API_DOMAIN.'/goods/info', $data);
        $res = A("Goods/Goods")->infos($id);
        if (!empty($res)  && is_string($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取多个商品接口(原始数据)
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    protected function getGoodsArr($id)
    {
        $data['id'] = $id;
        $data = array_merge($data, authkey());
        //        $res = post_curl(API_DOMAIN.'/goods/infos', $data);
        $res = A("Goods/Goods")->infos($id);
        if (!empty($res) && is_string($res)) {
            $res = json_decode($res, true);
        }
        //        dump($_SERVER);
        //        dump(getallheaders());
        return $res;
    }

    // 从商品服务获取数据
    protected function getGoodsData($id)
    {
        if (is_array($id)) {
            $id = implode(',', $id);
        }

        $res = get_curl(GOODS_DOMAIN . '/synchronization?goods_id=' . $id);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 发送商品接口(含价格系数优惠)
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    protected function getGoodsDetail($id)
    {
        $data['id'] = $id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/goods/detail', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取多个商品接口(含价格系数优惠)
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    protected function getGoodsDetailArr($id)
    {
        $data['id'] = $id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/goods/details', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取供应商广告词
     * @param  [type] $supplier_id [description]
     * @return [type]              [description]
     */
    protected function getAdWord($supplier_id)
    {
        $data['sid'] = $supplier_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/goods/adword', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    // 后台新增订单获取用户地址
    public function getAddressByOrder($user_id, $addres_id)
    {
        $data['uid'] = $user_id;
        $data['address_id'] = $addres_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/address/infobyorder', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取用户的地址
     * @param  [type] $addres_id [description]
     * @return [type]            [description]
     */
    public function getAddress($addres_id)
    {
        $data['address_id'] = $addres_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/address/info', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取用户的地址
     * @param  [type] $addres_id [description]
     * @return [type]            [description]
     */
    public function getDefaultAddress($user_id, $address_type = 0)
    {
        $data['user_id'] = $user_id;
        $data['address_type'] = $address_type;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/address/getdefaultinfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取快递
     * @param  [type] $no [description]
     * @return [type]            [description]
     */
    public function getShipping($no, $id, $phone = '')
    {
        $data['shipping_no'] = $no;
        $data['shipping_id'] = $id;
        $data['phone'] = $phone;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/shipping/info', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    // 后台新增订单获取用户发票地址
    public function getUserInvoiceByOrder($user_id, $tax_id)
    {
        $data['uid'] = $user_id;
        $data['tax_id'] = $tax_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/invoice/infobyorder', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取用户发票的地址
     * @param  [type] $tax_id    [description]
     * @return [type]            [description]
     */
    public function getUserInvoice($tax_id)
    {
        $data['tax_id'] = $tax_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/invoice/info', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }


    /**
     * 获取微信信息
     * @param  string $url  [description]
     * @param  string $code [description]
     * @return [type]       [description]
     */
    public function getWxInfo($url = '', $code = '')
    {
        $data['backUrl'] = $url;
        $data['code'] = $code;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/login/getwechatinfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取用户
     * @param  [type] $user_id [description]
     * @return [type]          [description]
     */
    public function getUser($user_id)
    {
        $res = post_curl(API_DOMAIN . '/user/getusertype', authkey(), array($this->loginCookie(array(C('COOKIE_PREFIX') . 'uid' => $user_id))));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 读取用户信息带缓存
     * @param  [type] $user_id [description]
     * @return [type]          [description]
     */
    public function getUserCache($user_id)
    {
        $data = array_merge(array('user_id' => $user_id), authkey());
        //        $res = post_curl(API_DOMAIN.'/user/info', $data);
        $res = A("Home/User")->info($data);
        //        if (!empty($res)) {
        //            $res = json_decode($res, true);
        //        }
        //        dump($res);
        return $res;
    }

    /**
     * 获取快递公司ID
     * @param  [type] $name [description]
     * @return [type]       [description]
     */
    public function getShippingId($name)
    {
        $data['shipping_name'] = $name;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/shipping/getshippingid', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取最优优惠券
     * @param  [type] $cart_ids [description]
     * @return [type]         [description]
     */
    public function getBestCoupon($cart_ids, $order_goods_type = 0)
    {
        $check['cart_ids'] = $cart_ids;
        if ($order_goods_type) {
            $check['order_goods_type'] = $order_goods_type; //1联营 2自营
        }
        $check = array_merge($check, authkey());
        $res = post_curl(API_DOMAIN . '/ucoupon/bestuse', $check, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 后台新增订单获取选择优惠券
     * @param  [type] $amount [description]
     * @return [type]         [description]
     */
    public function getCouponByOrder($user_id, $user_coupon_id, $goods_total = "", $cart_ids, $order_goods_type = 0)
    {
        $check['uid'] = $user_id;
        $check['user_coupon_id'] = $user_coupon_id;
        $check['goods_total'] = $goods_total;
        $check['cart_ids'] = $cart_ids;
        //        $check['order_goods_type'] = $order_goods_type;//1联营 2自营
        $check = array_merge($check, authkey());
        $res = post_curl(API_DOMAIN . '/ucoupon/infobyorder', $check, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取选择优惠券
     * @param  [type] $amount [description]
     * @return [type]         [description]
     */
    public function getCoupon($user_coupon_id, $goods_total = "", $cart_ids, $order_goods_type = 0)
    {
        $check['user_coupon_id'] = $user_coupon_id;
        $check['goods_total'] = $goods_total;
        $check['cart_ids'] = $cart_ids;
        $check['order_goods_type'] = $order_goods_type; //1联营 2自营
        $check = array_merge($check, authkey());
        $res = post_curl(API_DOMAIN . '/ucoupon/info', $check, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 使用优惠券
     * @param  [type] $user_coupon_id [description]
     * @param  [type] $order_id       [description]
     * @param  [type] $order_sn       [description]
     * @return [type]                 [description]
     */
    protected function useCoupon($user_coupon_id, $order_id, $order_sn)
    {
        $check['user_coupon_id'] = $user_coupon_id;
        $check['order_id'] = $order_id;
        $check['order_sn'] = $order_sn;
        $check = array_merge($check, authkey());
        $res = post_curl(API_DOMAIN . '/ucoupon/usecoupon', $check, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    // 订单后台使用优惠券
    protected function useCouponByOrder($user_id, $user_coupon_id, $order_id, $order_sn)
    {
        $check['uid'] = $user_id;
        $check['user_coupon_id'] = $user_coupon_id;
        $check['order_id'] = $order_id;
        $check['order_sn'] = $order_sn;

        $check = array_merge($check, authkey());
        $res = post_curl(API_DOMAIN . '/ucoupon/usecouponbyorder', $check, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 返还优惠券
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    protected function returnCoupon($order_id, $user_id = 0)
    {
        $check['order_id'] = $order_id;
        $check['user_id'] = $user_id;
        $check = array_merge($check, authkey());
        $res = post_curl(API_DOMAIN . '/ucoupon/returncoupon', $check, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 发送推送消息通知内部人员
     */
    public function sendOrderMsg($keyword, $data, $to_user = '', $cn_uncode = false)
    {
        // 推送给内部人员
        $data = json_decode($data, true);
        if ($cn_uncode) {
            if (isset($data['data'])) {
                $send_data = json_encode($data['data'], JSON_UNESCAPED_UNICODE); //防止中文乱码
            } else {
                $send_data = json_encode($data, JSON_UNESCAPED_UNICODE); //防止中文乱码
            }

            $wechat_data = json_encode($data['wechat_data'], JSON_UNESCAPED_UNICODE); //防止中文乱码
        } else {
            $send_data = json_encode($data['data']);
            $wechat_data = json_encode($data['wechat_data']);
        }
        if (strval($to_user) === 'test') {
            $to_user = C('INNER_PERSON_TEST');
        }
        if (!$to_user) { // 给正式的内部人员推送
            $to_user = 'INNER_PERSON';
        }

        $touser_json = json_encode($to_user);
        $check['touser'] = $touser_json;
        $check['data'] = $send_data;
        $check['wechat_data'] = $wechat_data;
        $check['pf'] = platform();
        $check['keyword'] = $keyword;
        $check['is_ignore'] = false;

        $check = array_merge($check, authkey());
        //F('check_180112', $check);
        // $res = post_curl(API_DOMAIN.'/msg/sendMessageByAuto', $check);
        $res = A("Message/Message")->sendMessageByAutoV2($check);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 统计用户券
     * @return [type] [description]
     */
    public function couponCount()
    {
        $check['cache'] = false;
        $check = array_merge($check, authkey());
        $res = post_curl(API_DOMAIN . '/ucoupon/count', $check, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取当前活动 同时只可能一个
     * @return [type] [description]
     */
    public function getCurrentActivities()
    {
        $res = post_curl(MARKET_DOMAIN . '/webapi/getCurrentActivities', array());
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }


    /**
     * 获取ERP汇率
     * @return [type] [description]
     */
    public function getErpExchangeRate($currency, $time)
    {
        if ($currency == 1) {
            return 1;
        }
        $data = array(
            'currency' => $currency,
            'time' => $time,
        );
        $res = post_curl(API_DOMAIN . '/server/consume/exchangerate', $data, array(), array('timeout' => 2));
        return $res;
    }

    /**
     * 对接服务WMS预分配订单
     * @return [type] [description]
     */
    protected function submitOrder($info)
    {
        $data['type'] = 'submitorder';
        $data['from'] = 'oms';
        $data['to'] = 'wms';
        $data['Timestamp'] = time();
        $data['jsonParas'] = json_encode(array('entity' => array($info)));
        $res = post_curl(SERVICE_DOMAIN, $data, array(), array('timeout' => 10));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 对接服务取消WMS订单
     * @return [type] [description]
     */
    protected function cancelOrder($order_id)
    {
        $data['type'] = 'cancelorder';
        $data['from'] = 'oms';
        $data['to'] = 'wms';
        $data['Timestamp'] = time();
        $data['jsonParas'] = json_encode(array('entity' => array(array('order_id' => $order_id, 'order_type' => 1))));
        $res = post_curl(SERVICE_DOMAIN, $data, array(), array('timeout' => 10));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * WMS预分配 调用 微服务
     * @param  integer $order_id [description]
     * @param  string  $order_sn [description]
     * @param  boolean $work     是否作业，注意使用时事务是否已提交来判断是否使用
     * @return [type]            [description]
     */
    public function makeOrder($order_id = 0, $order_sn = '', $work = false)
    {
        // $res = post_curl(ORDERAPI_DOMAIN.'/order/wmsorder', ['order_id' => $order_id, 'order_sn' => $order_sn, 'work' => $work]);
        // if (!empty($res)) {
        //     $res = json_decode($res, true);
        //     return ['err_code' => $res['errcode'], 'err_msg' => $res['errmsg'], 'data' => $res['data']];
        // }

        // 推入到队列 （消费端去处理预分配、作业）
        $queue_name = C('SELF_ORDER_PUSH_WMS');

        $RbmqModel = D('Common/Rbmq');

        $data['order_id'] = $order_id;

        $res = $RbmqModel->connect('ORDER_PUSH_RBMQ_CONFIG')->queue($queue_name)->push($data, $queue_name);

        $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件
        \Think\Log::write('订单ID：' . $order_id . '，推送队列返回：' . $res, INFO, '', $path);

        if ($res) return ['err_code' => 0, 'err_msg' => '订单推送队列成功'];

        return ['err_code' => -1, 'err_msg' => '订单推送队列失败'];
    }

    /**
     * 锁定基石库存
     * @return [type] [description]
     */
    protected function lockSku($order_id, $skus = array(), $is_pre_sale = false)
    {
        $this->apiRecord('锁库订单ID：' . $order_id . '，商品数据：' . json_encode($skus));

        $datas = array(
            'order_id' => $order_id,
            'Items' => $skus,
            'is_pre_sale' => $is_pre_sale,
        );
        $data['type'] = 'sku.lock';
        $data['data'] = urlencode(json_encode($datas));
        $data['timestamp'] = time();
        $res = post_curl(SERVICE_DOMAIN . '/transpond/wmsapi/WebApiLockStock?token=' . service_token($data['data'], $data['timestamp']), $data, array(), array('timeout' => 15));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 解锁基石库存
     * @param  [type]  $order_id   订单ID
     * @param  array   $skus       sku明细 sku-》数量
     * @param  boolean $reduce     是否扣减库存
     * @param  boolean $reduce_wms 是否请求WMS取消订单
     * @return [type]              [description]
     */
    protected function unlockSku($order_id, $skus = array(), $reduce = false, $reduce_wms = false)
    {
        $this->apiRecord('解锁订单ID：' . $order_id . '，商品数据：' . json_encode($skus));

        $datas = array(
            'order_id' => $order_id,
            'Items' => $skus,
            'reduce' => $reduce,
            'reduce_wms' => $reduce_wms,
        );
        $data['type'] = 'sku.unlock';
        $data['data'] = urlencode(json_encode($datas));
        $data['timestamp'] = time();
        $res = post_curl(SERVICE_DOMAIN . '/transpond/wmsapi/WebApiUnlockStock?token=' . service_token($data['data'], $data['timestamp']), $data, array(), array('timeout' => 15));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 专营商品解锁库
     * @param  [type]  $order_id   订单ID
     * @param  array $skus sku明细
     * @param  boolean $status 状态 1:锁定库存 2:取消锁定库存 3:减少实际库存 4:加回实际库存
     */
    public function jointSkuAction($order_id, $skus, $status = 1)
    {
        $footstone_url = STONE_V2_DOMAIN . '/open/lockLyStock'; // 基石锁库接口

        $post_data = [];
        $post_data['order_id'] = $order_id;
        $post_data['status'] = $status;
        $post_data['skus'] = json_encode($skus);

        $res = post_curl($footstone_url, $post_data);
        $this->apiRecord('专营解锁库，请求url：' . $footstone_url . '，请求参数：' . json_encode($post_data) . '，返回数据：' . $res); // 记录传递过来的数据

        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取基石锁定库存量
     * @param  [type] $order_id [description]
     * @param  array  $skus     [description]
     * @return [type]           [description]
     */
    protected function getLockSku($order_id, $skus = array())
    {
        $datas = array(
            'order_id' => $order_id,
            'Items' => $skus,
        );
        $data['from'] = 1;
        $data['type'] = 'sku.getlock';
        $data['data'] = urlencode(json_encode($datas));
        $data['timestamp'] = time();
        $res = post_curl(SERVICE_DOMAIN . '/transpond/wmsapi/WebApiHdOrderSkuLock?token=' . service_token($data['data'], $data['timestamp']), $data, array(), array('timeout' => 15));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取PCB订单信息
     * @return [type] [description]
     */
    protected function getPcbInfo($order_id)
    {
        $data['order_id'] = $order_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/pcb/index/order_details', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 设置滚动数据
     * @param [type] $type    [description]
     * @param [type] $user_id [description]
     * @param array  $extend  [description]
     */
    protected function setRollData($type, $user_id, $extend = array())
    {
        try {
            if (!empty($user_id)) {
                $data['type'] = $type;
                $data['user_id'] = $user_id;
                $data = array_merge($data, authkey(), $extend);
                $res = post_curl(API_DOMAIN . '/user/recorduserbehaviortoshow', $data);
                if (!empty($res)) {
                    $res = json_decode($res, true);
                }
            }
        } catch (\Exception $e) {
        }
        return $res;
    }

    /**
     * 获取用户指定优惠券信息
     * @param  [type] $user_coupon_id [description]
     * @return [type]                 [description]
     */
    protected function getCouponInfo($user_coupon_id, $user_id)
    {
        $data['user_coupon_id'] = $user_coupon_id;
        $data['user_id'] = $user_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/ucoupon/getusercouponinfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取订单优惠券
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    protected function getOrderCoupon($order_id)
    {
        $data['order_id'] = $order_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/ucoupon/getordercoupon', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }


    /**
     * 获取钱包单据
     * @param  [type] $wallet_id [description]
     * @return [type]            [description]
     */
    protected function getWallet($wallet_id)
    {
        $data['wallet_id'] = $wallet_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/wallet/getwalletorder', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 充值成功回调
     * @param [type] $data [description]
     */
    protected function setRecharge($data)
    {
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/wallet/setrecharge', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 运营充值成功回调
     * @param [type] $data [description]
     */
    protected function setOperationRecharge($data)
    {
        $res = post_curl(FINANCE_DOMAIN . '/webapi/paycallback', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 退款充值成功回调
     * @param [type] $data [description]
     */
    protected function setRefund($data)
    {
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/wallet/setrefund', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 设置同步标记已处理
     * @param [type] $wallet_id [description]
     * @param string $error     [description]
     */
    protected function setWalletSyn($wallet_id, $error = '')
    {
        $data['wallet_id'] = $wallet_id;
        $data['error'] = $error;
        $data['type'] = 1;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/wallet/setwallet', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 完成钱包单据状态
     * @param [type] $wallet_id [description]
     * @param [type] $wallet_sn [description]
     */
    protected function setWithdrawFinish($wallet_id, $wallet_sn, $serial_number, $create_time, $amount)
    {
        $data['wallet_id'] = $wallet_id;
        $data['wallet_sn'] = $wallet_sn;
        $data['serial_number'] = $serial_number;
        $data['create_time'] = $create_time;
        $data['amount'] = $amount;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/wallet/setwithdrawfinish', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取钱包单据信息
     * @param  string $wallet_id [description]
     * @param  string $wallet_sn [description]
     * @return [type]            [description]
     */
    protected function getWalletOrderInfo($wallet_id = '', $wallet_sn = '')
    {
        $data['wallet_id'] = $wallet_id;
        $data['wallet_sn'] = $wallet_sn;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/wallet/orderinfo', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 使用钱包
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    protected function payWallet($data)
    {
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/wallet/orderpay', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取钱包退款金额
     * @return [type] [description]
     */
    protected function walletRefundAmount($order_sn)
    {
        $data['sn'] = $order_sn;
        $data['log_type'] = 11;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/wallet/checklogamount', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取是否有符合的优惠券
     * @param  [type]  $supplier_id      [description]
     * @param  [type]  $brand_id         [description]
     * @param  [type]  $coupon_mall_type [description]
     * @param  string  $tags             [description]
     * @param  integer $type             [description]
     * @return [type]                    [description]
     */
    protected function getHasCoupon($supplier_id, $brand_id, $coupon_mall_type, $tags = 'coupon_setup', $type = 2)
    {
        $data['supplier_id'] = $supplier_id;
        $data['brand_id'] = $brand_id;
        $data['coupon_mall_type'] = $coupon_mall_type;
        $data['tags'] = $tags;
        $data['type'] = $type;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/coupon/showcoupon', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 钱包活动
     * @param  integer $mall_type         类型 1全部 2自营 3联营
     * @param  integer $preferential_type [description]
     * @return [type]                     [description]
     */
    protected function walletActive($mall_type = 1, $preferential_type = 1)
    {
        $data['mall_type'] = $mall_type;
        $data['preferential_type'] = $preferential_type;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/lottery/getwalletpreferentialrule', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 财务退款申请
     * @return [type] [description]
     */
    protected function refundSyn($datas)
    {
        $data['data'] = urlencode(json_encode($datas));
        $data['timestamp'] = time();
        $res = post_curl(FINANCE_DOMAIN . '/webapi/addOrderRefund?token=' . service_token($data['data'], $data['timestamp']), $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 财务退货同步
     * @return [type] [description]
     */
    protected function returnsyn($datas)
    {
        // $data['data'] = urlencode(json_encode($datas));
        // $data['timestamp'] = time();
        $res = post_curl(FINANCE_DOMAIN . '/webapi/pushDeliveredOrder', $datas);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取用户账期信息
     * @param  [type] $user_id [description]
     * @return [type]          [description]
     */
    protected function getUserCredit($user_id)
    {
        $data = array(
            'user_id' => $user_id,
            'diff_amount' => $diff_amount,
        );
        $res = post_curl(CREDIT_DOMAIN . '/credit/info', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 设置用户的账期占用额度
     * @param [type] $user_id     [description]
     * @param [type] $diff_amount [description]
     */
    protected function setUserOccupy($user_id, $diff_amount, $occupy_amount = null)
    {
        $data = array(
            'user_id' => $user_id,
            'diff_amount' => $diff_amount,
            'occupy_amount' => $occupy_amount,
        );
        $res = post_curl(CREDIT_DOMAIN . '/credit/setoccupy', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 订单活动订单优惠金额上限
     * @return [type] [description]
     */
    protected function getOrderAcivityLimit()
    {
        $res = post_curl(GOODS_DOMAIN . '/get_new_customer_limit', array());
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    //是否能使用优惠价
    protected function isCanAc($uid, $ac_type = null)
    {
        $condition = function ($ac_type) use ($uid) {
            static $user_info;
            switch ($ac_type) {
                case 1:
                case 2:
                case 6:
                case 4:
                case 10:
                    return true;
                case 3:
                    //是否可用会员价
                    return !empty($uid);
                case 5:
                    $uid = !empty($uid) ? $uid : 0;
                    //是否可用新客价,未登录也显示新客价
                    //is_new=0  可以使用新客价
                    if (!isset($user_info[$uid])) {
                        if (!empty($uid)) {
                            $user_info[$uid] = $this->getUserCache($uid);
                        } else {
                            $user_info[$uid] = ['data' => ['is_new' => 0]];
                        }
                    }
                    return empty($user_info[$uid]) || $user_info[$uid]['data']['is_new'] == 0;
                default:
                    return false;
            }
        };
        //查询会员价、新客价优惠条件
        if (is_null($ac_type)) {
            $res = array(
                'member' => $condition(3),
                // 'newCustomer' => true,
                'newCustomer' => $condition(5),
            );
            return $res;
        } else {
            return $condition($ac_type);
        }
    }

    /**
     * 推送用户和业务员到CRM队列
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    protected function UpdateUserSales($data)
    {
        // 推入到队列
        $queue_name = C('CRM_UPDATE_USER_SALES');

        $RbmqModel = D('Common/Rbmq');

        $res = $RbmqModel->queue($queue_name)->push($data, $queue_name);

        if ($res) {
            dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.crm_mq'), '修改用户绑定客服，推送到队列任务成功，用户ID：' . $data['user_id'] . '，客服ID：' . $data['sale_id']);
            return ['err_code' => 0, 'err_msg' => '推送队列成功'];
        }

        dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.crm_mq'), '修改用户绑定客服，推送到队列任务失败，用户ID：' . $data['user_id'] . '，客服ID：' . $data['sale_id']);
        return ['err_code' => -1, 'err_msg' => '推送队列失败'];
    }

    public function dingDingGJ($label = "", $content = "")
    {
        try {
            $data = array(
                'msgtype' => "text",
                'text' => array(
                    'content' => "{$label}:{$content}",
                ),
            );
            $data = json_encode($data);
            $url = C("YAOHAOYOU_C_ORDER_DING");
            $bk = post_curl($url, $data, array('Content-Type:application/json;charset=UTF-8'));
        } catch (\Exception $e) {
        }
    }

    /**
     * 获取用户ID
     * @return [type] [description]
     */
    public function getUid($uid = null)
    {
        if (is_null($uid)) {
            if ($this->auth()) {
                $uid = I('request.uid', 0, 'intval');
            }

            if (empty($uid)) {
                $uid = cookie('uid');
            }
        }
        if (!$this->auth() && $this->login['err_code'] != 0) { //弱登录态下购物车数据不属于当前用户账号的
            $uid = 0;
        }
        return $uid;
    }

    /*
     * 推送队列
     * 退款完成后 推送订单id到队列中
     * py队列消费：推送订单相关信息到erp通过退款完成
     */
    public function pushOrderRefundDoneToErp($arr = [])
    {
        $RbmqModel = D('Common/Rbmq');
        $RbmqModel->connect('WMS_RBMQ_CONFIG')->queue(C('COMPLETED_ORDER_REFUND_ERP'))->push($arr, C('COMPLETED_ORDER_REFUND_ERP'));
    }

    // 推送生产跟踪到队列
    public function pushProductTracking($data)
    {
        $queue_name = C('ORDER_PRODUCT_TRACK');

        $RbmqModel = D('Common/Rbmq');

        $res = $RbmqModel->connect('RBMQ_CONFIG')->queue($queue_name)->push($data, $queue_name);

        $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件
        \Think\Log::write('推送生产跟踪数据：' . json_encode($data) . '，推送队列返回：' . $res, INFO, '', $path);

        if ($res) return ['err_code' => 0, 'err_msg' => '推送队列成功'];

        return ['err_code' => -1, 'err_msg' => '推送队列失败'];
    }

    /**
     * 销售订单来源
     * @param  [type] $pf   [平台]
     * @return [type]       [description]
     */
    public function getSaleOrderSource($order_source)
    {
        if (!$order_source) return 0;

        $explode = explode(',', $order_source);

        if (strpos($explode[0], 'pf=') === false) return 0;

        $res = explode('=', $explode[0]);

        return $res[1];
    }

    // 接口日志记录
    public function apiRecord($info)
    {
        $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件
        \Think\Log::write($info, INFO, '', $path);
    }

    /**
     * 推送异步队列
     *
     * @param  string $route  队列对应系统地址的路由 /open/sendSkuPurMail
     * @param  string $callback_path  相对回调路径：http://order.ichunt.net/open/testa
     * @param  string $search_key  日志搜索字段，自定义可以存订单号 ，售后单号 等
     * @param  int $main_time_out  主推送超时时间
     * @param  int $callback_timeout 回调推送时间
     *
     * */
    public function pushAsynQueue($route, $param, $queue = "lie_queue_order", $callback_path = "", $search_key = "", $main_time_out = 50, $callback_timeout = 10)
    {
        $uk =  md5(json_encode($param) . time());

        $data = [
            "queue_name" => $queue,
            "mq_data" => [
                "__from" => $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'],
                "__insert_time" => time(),
                "__timeout" => $main_time_out,
                "__route_key" => $route,
                "__type" => "http",
                "__uk" => $uk,
                "data" => (object)$param,
            ]
        ];

        if ($search_key) {
            $data["mq_data"]["__search_key"] = $search_key;
        }

        if ($callback_path) { //存在回调
            $data["mq_data"]["__callback"] =   [ // 选填字段,有回调时必填
                "__callback_url" => $callback_path,    // 回调的url路由地址，暂不支持回调soap接口列
                "__callback_timeout" => $callback_timeout,            // 回调该接口的超时时间，默认为10秒，选填字段
                "__callback_type" => "http",              // 回调该接口的请求方式，同"__type"，选填字段
                //"__callback_verify"=>"verify"         // 若回调的接口需要校验，如请求龙哥的接口需要该字段，选填字段
                "__callback_search_key" => $search_key,
            ];
        }

        $res = post_curl(ASYN_QUEUE_DOMAIN, json_encode($data));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }

        return $res;
    }
}
