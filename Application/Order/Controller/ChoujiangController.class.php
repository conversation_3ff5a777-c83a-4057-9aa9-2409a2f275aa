<?php
namespace Order\Controller;

use Order\Controller\BaseController;


class ChoujiangController extends BaseController
{
    public $com_people = [];

    //所有员工的信息
    public $prizeKey = "ichunt_lie_people";

    //放到有序集合
    public $onePrizeKey = "ichunt_lie_people_one_prize";
    public $twoPrizeKey = "ichunt_lie_people_two_prize";
    public $threePrizeKey = "ichunt_lie_people_three_prize";
    public $fourPrizeKey = "ichunt_lie_people_four_prize";
    public $fivePrizeKey = "ichunt_lie_people_five_prize";
    public $sixPrizeKey = "ichunt_lie_people_six_prize";


    //实物奖项
    public $prizeKey_v1 = "ichunt_lie_people_prize_v1";
    public $prizeKey_v2 = "ichunt_lie_people_prize_v2";
    public $prizeKey_v3 = "ichunt_lie_people_prize_v3";
    public $prizeKey_v4 = "ichunt_lie_people_prize_v4";
    public $prizeKey_v5 = "ichunt_lie_people_prize_v5";
    public $prizeKey_v6 = "ichunt_lie_people_prize_v6";
    public $prizeKey_v7 = "ichunt_lie_people_prize_v7";
    public $prizeKey_v8 = "ichunt_lie_people_prize_v8";
    public $prizeKey_v9 = "ichunt_lie_people_prize_v9";
    public $prizeKey_v10 = "ichunt_lie_people_prize_v10";
    public $prizeKey_v11 = "ichunt_lie_people_prize_v11";
    public $prizeKey_v12 = "ichunt_lie_people_prize_v12";
    public $prizeKey_v13 = "ichunt_lie_people_prize_v13";

    //实物奖项对应的redis缓存key
    public $physicalAwardPrizeKey = [
        //实物奖项
        1=>"ichunt_lie_people_prize_v1",
        2=>"ichunt_lie_people_prize_v2",
        3=>"ichunt_lie_people_prize_v3",
        4=>"ichunt_lie_people_prize_v4",
        5=>"ichunt_lie_people_prize_v5",
        6=>"ichunt_lie_people_prize_v6",
        7=>"ichunt_lie_people_prize_v7",
        8=>"ichunt_lie_people_prize_v8",
        9=>"ichunt_lie_people_prize_v9",
        10=>"ichunt_lie_people_prize_v10",
        11=>"ichunt_lie_people_prize_v11",
        12=>"ichunt_lie_people_prize_v12",
        13=>"ichunt_lie_people_prize_v13",
    ];


    //实物奖项对应的奖品名称
    public $shiWuJiangCn = [
        //实物奖项
        1=>"MINISO加湿器",
        2=>"充电宝",
        3=>"暖风机",
        4=>"自动磁力搅拌杯",
        5=>"乐扣乐扣迷你果汁机",
        6=>"乘风破浪便携式茶具",
        7=>"小米筋膜枪",
        8=>"蕉下防晒礼盒",
        9=>"九阳养生壶",
        10=>"SKG颈部按摩仪",
        11=>"破壁机",
        12=>"小米电动牙刷",
        13=>"富安娜纯棉四件套礼盒",
    ];

    //实物奖项对应的奖品个数
    public $shiWuJiangNums = [
        //实物奖项
        1=>2,
        2=>20,
        3=>30,
        4=>10,
        5=>3,
        6=>5,
        7=>3,
        8=>2,
        9=>8,
        10=>4,
        11=>5,
        12=>1,
        13=>1,
    ];

    //实物奖项排除名单
    public $excludePeoples=[

    ];



    public function _initialize()
    {
//        $peopleArray = file_get_contents("yunagong.txt");
        $this->com_people = include "yuangong.php";
//        dump($this->com_people);
//        $this->com_people = json_decode($peopleArray,true);
        $this->setHeaders();
        $this->path = C('LOG_PATH').'choujiang/'.date('y_m_d').'.log'; // 接口日志文件
    }

    /**
     * 跨域头部设置
     */
    private function setHeaders()
    {
        $origin = isset($_SERVER['HTTP_ORIGIN'])? $_SERVER['HTTP_ORIGIN'] : '';
        $origin_arr = explode('//', $origin);
        $allow_origin = C('ALLOW_ORIGIN');
        if(isset($origin_arr['1']) && in_array($origin_arr['1'], $allow_origin)){
            header('Access-Control-Allow-Origin:'.$origin);
            header('Access-Control-Allow-Credentials:true');
            header('Access-Control-Allow-Methods:POST');
            header('Access-Control-Allow-Headers:x-requested-with,content-type');
        }
    }

    public function index(){
        echo "</br>";
        echo "</br>";
        echo "<a target='_blank' href='/Order/Choujiang/init'>(请勿操作)初始化公司员工信息(谨慎操作)</a>";
        echo "</br>";
        echo "</br>";
        echo "</br>";
        echo "</br>";


        echo "<a target='_blank' href='/Order/Choujiang/showPeople'>查看剩餘可以抽奖的员工信息</a>";
        echo "</br>";
        echo "</br>";
        echo "</br>";
        echo "</br>";

        echo "<a target='_blank' href='/Order/Choujiang/explodeMemble'>导出中奖员工名单</a>";
        echo "</br>";
        echo "</br>";
        echo "</br>";
        echo "</br>";

//        echo "<a target='_blank' href='/Order/Choujiang/adduser?username=&department='>添加一个员工信息</a>";
//        echo "</br>";
//        echo "</br>";
//
//        echo "<a target='_blank' href='/Order/Choujiang/resetPrize?level=3'>重置三等奖(谨慎操作)</a>";
//        echo "</br>";
//        echo "</br>";
//
//        echo "<a target='_blank' href='/Order/Choujiang/resetPrize?level=2'>重置二等奖(谨慎操作)</a>";
//        echo "</br>";
//        echo "</br>";
//
//        echo "<a target='_blank' href='/Order/Choujiang/resetPrize?level=1'>重置一等奖(谨慎操作)</a>";
//        echo "</br>";
//        echo "</br>";


        exit;
    }


    public function getAllPeople()
    {
        $peoples = $this->com_people;
         shuffle($peoples);
        return $this->ajaxReponse(0,"ok",$peoples);

    }

    protected function retain_key_shuffle(array &$arr){
        if (!empty($arr)) {
            $key = array_keys($arr);
            shuffle($key);
            shuffle($key);
            shuffle($key);
            for($i=0;$i<=1000;$i++){
                shuffle($key);
            }
            foreach ($key as $value) {
                $arr2[$value] = $arr[$value];
            }
            $arr = $arr2;
        }
    }

    /**
     * 初始化公司员工
     */
    public function init(){
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        $redis->del($this->prizeKey);
        $redis->del($this->onePrizeKey);
        $redis->del($this->twoPrizeKey);
        $redis->del($this->threePrizeKey);
        $redis->del($this->fourPrizeKey);
        $redis->del($this->fivePrizeKey);
        $redis->del($this->sixPrizeKey);

        foreach($this->physicalAwardPrizeKey as $shiwuRedisKey){
            $redis->del($shiwuRedisKey);
        }
//        $this->retain_key_shuffle($this->com_people);
//        $this->com_people = array_merge($this->com_people);

//        for($i=0;$i<=200;$i++){
//            shuffle($this->com_people);
//        }

        foreach($this->com_people as $name){
            $redis->sAdd($this->prizeKey,$name);
        }


        $count = $redis->sCard($this->prizeKey);
        echo "总人数:".$count;
        echo "<br/>";
        $members = $redis->sMembers($this->prizeKey);
        foreach($members as $member){
            echo $member;
            echo "<br/>";
        }
        exit;
    }

    /**
     * 查看所有员工的信息
     */
    public function showPeople(){
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        $count = $redis->sCard($this->prizeKey);
        echo "总人数:".$count;
        echo "<br/>";
        $members = $redis->sMembers($this->prizeKey);
        foreach($members as $member){
            echo $member;
            echo "<br/>";
        }
        exit;
    }

    public function explodeMemble(){
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        $one = $peoples=$redis->zRevRangeByScore($this->onePrizeKey,"+inf", "-inf");
        $two = $peoples=$redis->zRevRangeByScore($this->twoPrizeKey,"+inf", "-inf");
        $three = $peoples=$redis->zRevRangeByScore($this->threePrizeKey,"+inf", "-inf");
        $four = $peoples=$redis->zRevRangeByScore($this->fourPrizeKey,"+inf", "-inf");
        $five = $peoples=$redis->zRevRangeByScore($this->fivePrizeKey,"+inf", "-inf");
        $six = $peoples=$redis->zRevRangeByScore($this->sixPrizeKey,"+inf", "-inf");
//        $ar1r = ["特等奖" => $six,"一等奖" => $one, "二等奖" => $two, "三等奖" => $three, "四等奖" => $four, "幸运奖" => $five ];
        $ar1r = ["幸运奖" => $five ];
        $arr2=[];
        foreach($this->physicalAwardPrizeKey as $k=>$physicalAward){
            $arr2[$this->shiWuJiangCn[$k]] =  $redis->zRevRangeByScore($physicalAward,"+inf", "-inf");
        }

        $arr3 = array_merge($ar1r,$arr2);

        $allPrize = $arr3;
//        $allPrize = \GuzzleHttp\json_decode($allPrize,true);
        $html = "";
        foreach($allPrize as $k=>$v){
            $html .= sprintf("%s <br/ >",$k);
            $html .= sprintf("%s <br/ ><br/ >",implode(",",$v));
        }


        \Think\Log::write("-------所有中奖员工------------",INFO,"",$this->path);
        \Think\Log::write(\GuzzleHttp\json_encode($arr3),INFO,"",$this->path);

        echo   $html;
    }

    /**
     * 添加一个员工信息
     */
    function adduser(){
        $username = I("username","");
        $department = I("department","");

        if(!$username || !$department){
            echo "请输入username和department参数";
            exit;
        }
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        $redis->sAdd($this->prizeKey,$department."-".$username);
        echo "添加成功";
        exit;
    }

    /**
     * 添加一个员工信息
     */
    function deluser(){
        $username = I("username","");
        $department = I("department","");

        if(!$username || !$department){
            echo "请输入username和department参数";
            exit;
        }
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        $redis->sRem($this->prizeKey,$department."-".$username);
        echo "移除成功";
        exit;
    }

    function ajaxReponse($code,$msg,$data=[])
    {
        header('content-type:application/json;charset=utf-8');
        echo json_encode([
            "err_code"=>$code,
            "msg"=>$msg,
            "data"=>$data
        ]);
        exit;
    }

    /**
     * 获取中奖名单
     */
    function getWinningList(){
        $levelList = [1,2,3,4,5,6];
        $action = [
            1=>"get_prize_one",
            2=>"get_prize_two",
            3=>"get_prize_three",
            4=>"get_prize_four",
            5=>"get_prize_five",
            6=>"get_prize_six",
        ];

        $level = I("level",0);
        $level = intval($level);
        if(!$level){
            return $this->ajaxReponse(0,"参数错误:level");
        }

        if(!in_array($level,$levelList) && $level < 10){
            return $this->ajaxReponse(-1,"参数错误:level must  in 1 2 3 4 5 6");
        }

        if($level > 10){
            return $this->get_prize_v10($level);
        }


        $tmp = $action[$level];
        return $this->$tmp();

    }



    function get_prize_one(){
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));

        $count = $redis->zCard($this->onePrizeKey);
        if($count >= 5){
            return $this->ajaxReponse(-1,"一等奖已满额!");
        }

//        $people = $tmp = $redis->spop($this->prizeKey);
//        $redis->zAdd($this->onePrizeKey,time(),$people);

        $num = 5;

        $arr = [];
        for($i = 1;$i<=$num;$i++){
            $arr[] = $tmp = $redis->spop($this->prizeKey);
        }

        $pipe = $redis->multi(\Redis::PIPELINE);
        foreach($arr as $v){
            if(!$v) continue;
            sleep(0.1);
            $time = microtime(true);
            $pipe->zAdd($this->onePrizeKey,$time,$v);
        }
        $pipe->exec();



        $arr = $redis->zRevRangeByScore($this->onePrizeKey,"+inf", "-inf");
        \Think\Log::write("-------一等奖开奖成功------------",INFO,"",$this->path);
        \Think\Log::write(json_encode($arr,JSON_UNESCAPED_UNICODE),INFO,"",$this->path);
        return $this->ajaxReponse(0,"一等奖开奖成功!",$arr);
    }


    function get_prize_two(){
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));

        $num = 20;
        $count = $redis->zCard($this->twoPrizeKey);
        if($count >= 20){
            return $this->ajaxReponse(-1,"二等奖已满额!");
        }


        $arr = [];
        for($i = 1;$i<=$num;$i++){
            $arr[] = $tmp = $redis->spop($this->prizeKey);
        }

        $pipe = $redis->multi(\Redis::PIPELINE);
        foreach($arr as $v){
            if(!$v) continue;
            sleep(0.1);
            $time = microtime(true);
            $pipe->zAdd($this->twoPrizeKey,$time,$v);
        }
        $pipe->exec();


        \Think\Log::write("-------二等奖开奖成功---本次开奖------------",INFO,"",$this->path);
        \Think\Log::write(json_encode($arr,JSON_UNESCAPED_UNICODE),INFO,"",$this->path);


//        $arr_all = $redis->zRevRangeByScore($this->twoPrizeKey,"+inf", "-inf");
//        \Think\Log::write("-------二等奖开奖成功------------",INFO,"",$this->path);
//        \Think\Log::write(json_encode($arr_all,JSON_UNESCAPED_UNICODE),INFO,"",$this->path);
        return $this->ajaxReponse(0,"二等奖开奖成功!",$arr);
    }

    function get_prize_three (){
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));

        $nums = 15;

        $count = $redis->zCard($this->threePrizeKey);
        if($count >= 30){
            return $this->ajaxReponse(-1,"三等奖已满额!");
        }

        $arr = [];
        for($i = 1;$i<=$nums;$i++){
            $arr[]  = $redis->spop($this->prizeKey);
        }

        $pipe = $redis->multi(\Redis::PIPELINE);
        foreach($arr as $v){
            if(!$v) continue;
            sleep(0.1);
            $time = microtime(true);
            $pipe->zAdd($this->threePrizeKey,$time,$v);
        }
        $pipe->exec();


        \Think\Log::write("-------三等奖开奖成功---本次开奖------------",INFO,"",$this->path);
        \Think\Log::write(json_encode($arr,JSON_UNESCAPED_UNICODE),INFO,"",$this->path);


//        $arr_all = $redis->zRevRangeByScore($this->threePrizeKey,"+inf", "-inf");
//        \Think\Log::write("-------三等奖开奖成功------------",INFO,"",$this->path);
//        \Think\Log::write(json_encode($arr_all,JSON_UNESCAPED_UNICODE),INFO,"",$this->path);
        return $this->ajaxReponse(0,"三等奖开奖成功!",$arr);
    }

    function get_prize_four(){
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        //查看四等奖是否已经开奖了
        $count = $redis->zCard($this->fourPrizeKey);

        $nums = 20;
        if( $count >= 100){
            return $this->ajaxReponse(-1,"四等奖已满额!");
        }

        $arr = [];
        for($i = 1;$i<=$nums;$i++){
            $arr[]  = $redis->spop($this->prizeKey);
        }

        $pipe = $redis->multi(\Redis::PIPELINE);
        foreach($arr as $v){
            if(!$v) continue;
            sleep(0.1);
            $time = microtime(true);
            $pipe->zAdd($this->fourPrizeKey,$time,$v);
        }
        $pipe->exec();


        \Think\Log::write("-------四等奖开奖成功--本次开奖人数------------",INFO,"",$this->path);
        \Think\Log::write(json_encode($arr,JSON_UNESCAPED_UNICODE),INFO,"",$this->path);

//        $arr_all  =$redis->zRevRangeByScore($this->fourPrizeKey,"+inf", "-inf");
//        \Think\Log::write("-------四等奖开奖成功--所有人次------------",INFO,"",$this->path);
//        \Think\Log::write(json_encode($arr_all,JSON_UNESCAPED_UNICODE),INFO,"",$this->path);
        return $this->ajaxReponse(0,"四等奖开奖成功!",$arr);
    }


    function get_prize_five (){
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));

        $count = $redis->zCard($this->fivePrizeKey);
        if($count >= 40){
            return $this->ajaxReponse(-1,"幸运奖已满额!");
        }

        $people = $tmp = $redis->spop($this->prizeKey);
        $redis->zAdd($this->fivePrizeKey,time(),$people);

        $arr = $redis->zRevRangeByScore($this->fivePrizeKey,"+inf", "-inf");
        \Think\Log::write("-------幸运奖开奖成功------------",INFO,"",$this->path);
        \Think\Log::write(json_encode($arr,JSON_UNESCAPED_UNICODE),INFO,"",$this->path);
        return $this->ajaxReponse(0,"幸运奖开奖成功!",[$people]);
    }

    function get_prize_six (){
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));


        $count = $redis->zCard($this->sixPrizeKey);
        if($count >= 1){
            return $this->ajaxReponse(-1,"特等奖已满额!");
        }

        $people = $tmp = $redis->spop($this->prizeKey);
        $redis->zAdd($this->sixPrizeKey,time(),$people);

        $arr = $redis->zRevRangeByScore($this->sixPrizeKey,"+inf", "-inf");
        \Think\Log::write("-------特等奖开奖成功------------",INFO,"",$this->path);
        \Think\Log::write(json_encode($arr,JSON_UNESCAPED_UNICODE),INFO,"",$this->path);
        return $this->ajaxReponse(0,"特等奖开奖成功!",[$people]);
    }



    function getAllPrize(){
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        $one = $peoples=$redis->zRevRangeByScore($this->onePrizeKey,"+inf", "-inf");
        $two = $peoples=$redis->zRevRangeByScore($this->twoPrizeKey,"+inf", "-inf");
        $three = $peoples=$redis->zRevRangeByScore($this->threePrizeKey,"+inf", "-inf");
        $four = $peoples=$redis->zRevRangeByScore($this->fourPrizeKey,"+inf", "-inf");
        $five = $peoples=$redis->zRevRangeByScore($this->fivePrizeKey,"+inf", "-inf");
        $six = $peoples=$redis->zRevRangeByScore($this->sixPrizeKey,"+inf", "-inf");
//        $ar1r = ["特等奖" => $six,"一等奖" => $one, "二等奖" => $two, "三等奖" => $three, "四等奖" => $four, "幸运奖" => $five ];
        $ar1r = ["幸运奖" => $five ];
        $arr2=[];
        foreach($this->physicalAwardPrizeKey as $k=>$physicalAward){
            $arr2[$this->shiWuJiangCn[$k]] =  $redis->zRevRangeByScore($physicalAward,"+inf", "-inf");
        }

        $arr3 = array_merge($ar1r,$arr2);

        return $this->ajaxReponse(0,"所有获奖的名单!",$arr3);
    }

    /**
     * 重置开奖
     */
    public function resetPrize(){
        $level = I("level",0);
        if($level <= 0){
            return $this->ajaxReponse(-1,"level is must 1 2 3");
        }
        if(!in_array($level,[1,2,3])){
            return $this->ajaxReponse(-1,"level is must 1 2 3");
        }

        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        $arr = [];

        $action = [
            1=>$this->onePrizeKey,
            2=>$this->twoPrizeKey,
            3=>$this->threePrizeKey,
        ];

        $level = intval($level);
        $arr = $redis->zRevRangeByScore($action[$level],"+inf", "-inf");
        foreach($arr as $item){
            $redis->sAdd($this->prizeKey,$item);
        }

    }


    /**
     * Notes:实物奖项
     * User: sl
     * Date: 2023-01-11 16:47
     * @param $level
     */
    function get_prize_v10($level){
        $redis = new \Redis();
        $redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));

        $physicalAward = intval($level - 10);

        //奖品个数
        $num = !empty($this->shiWuJiangNums[$physicalAward]) ?  $this->shiWuJiangNums[$physicalAward] : 0;
        //奖品名称
        $prizeName = !empty($this->shiWuJiangCn[$physicalAward]) ?  $this->shiWuJiangCn[$physicalAward] : "";

        //奖品缓存redis key
        $redisKey = !empty($this->physicalAwardPrizeKey[$physicalAward]) ?  $this->physicalAwardPrizeKey[$physicalAward] : "";



        if($num <= 0 || $prizeName == "" || $redisKey == ""){
            return $this->ajaxReponse(-1,"实物奖项 参数错误");
        }





        $count = $redis->zCard($redisKey);
        if($count >= $num){
            return $this->ajaxReponse(-1,sprintf("实物奖:%s 已满额!",$prizeName));
        }


        $arr = [];
        for($i = 1;$i<=$num;$i++){
            for(;;){
                $tmpName = $redis->spop($this->prizeKey);
                if(strpos($tmpName, '华东北') !== false){
                    //包含 重新抽取
//                    echo sprintf("出现了排除出名单 名单为：%s",$tmpName);
                    continue;
                }
                if(strpos($tmpName, '香港') !== false){
                    //包含 重新抽取
//                    echo sprintf("出现了排除出名单 名单为：%s",$tmpName);
                    continue;
                }
                $arr[]  = $tmpName;
                break;
            }
        }

        $pipe = $redis->multi(\Redis::PIPELINE);
        foreach($arr as $v){
            if(!$v) continue;
            sleep(0.1);
            $time = microtime(true);
            $pipe->zAdd($redisKey,$time,$v);
        }
        $pipe->exec();



        $arr = $redis->zRevRangeByScore($redisKey,"+inf", "-inf");
        \Think\Log::write("-------实物奖-{$prizeName}-开奖成功------------",INFO,"",$this->path);
        \Think\Log::write(json_encode($arr,JSON_UNESCAPED_UNICODE),INFO,"",$this->path);

        return $this->ajaxReponse(0,"实物奖-{$prizeName}开奖成功!",$arr);
    }


}