<?php
return array(
    //新旧状态映射
    'ORDER_STATUS_MAPPING' => array(
        '-1' => array('已取消',     '2,3,5',    null),
        '1' => array('待审核',     '0',        null),
        '2' => array('待付款',     '1',        '0'),
        '3' => array('待收货',    '1',        '2'),//待付尾款
        '4' => array('待收货',      '1',     '1'),//待发货
        '5' => array('待收货',      '1',     '1,2'),
        // '6' => array('待评价')
        '10' => array('交易成功',    '4',   '0,1,2'),
    ),

    //订单类型
    'OLD_ORDER_TYPE'    => array(
        '0' => '全款订单',
        '1' => '预付订单',
        '2' => '账期订单',
    ),

    //旧表币种映射
    'CURRENCY_MAPPING' => array(
        '0' => '1',
        '1' => '2',
    ),

    //v3订单状态
    'ORDER_STATUS' => array(
        '-1' => '已取消',
        '-2' => '审核不通过',
        '1' => '待审核',
        '2' => '待付款',
        '3' => '待付尾款',
        '4' => '待发货',
        '7' => '部分发货',
        '8' => '待收货',
        '10' => '交易成功',
    ),
    //v3订单客户已付金额
    'ORDER_CLIENT_PAYED_STATUS' => array('-1','-2','-3','4'),
    //v3订单已付金额
    'ORDER_PAYED_STATUS' => array('-1','-2','-3','-7','4'),
    //v3订单总未付金额
    'ORDER_TOPAY_STATUS' => array('1','2','3','-8','-7','-6','-5','-4','-3','-2','-1'),
    //v3订单附加费
    'ORDER_EXT_STATUS' => array('2'),
    //v3订单总金额
    'ORDER_PRICE_TOTAL_STATUS' => array('1','2','3','-8','-4','-6'),
    //订单付款方式
    'ORDER_PAY_TYPE' => array(
        '1' => '全款支付',
        '2' => '预付款支付',
        '3' => '账期支付',
        '4' => '货到猎芯付款',
    ),
    //订单付款方式别名
    'ORDER_PAY_TYPE_NAME' => array(
        '1' => '全款订单',
        '2' => '预付订单',
        '3' => '账期订单',
        '4' => '预付订单',
    ),

    //支付日志类型
    'PAY_LOG_PAY_TYPE' => array(
        '1' => '全额付款',
        '2' => '预付款',
        '3' => '尾款',
        '4' => '账期',
    ),

    //销售类型别名
    'SALE_TYPE_NAME' => array(
        '1' => '现货订单',
        '2' => '期货订单',
    ),

    //交货地映射币种
    'PLACE_CURRENCY_MAPPING' => array(
        '1' => '1',//大陆
        '2' => '2',//香港
    ),

    //可预付限制最低金额
    'ADVANCE_MIN_LIMIT' => array(
        '1' => 10000,
        '2' => 1500,
    ),

    //可预付限制最高金额
    'ADVANCE_MAX_LIMIT' => array(
        '1' => 49999,
        '2' => 7800,
    ),

    //大陆自提地址
    'SELF_PICK_ADDRESS' => array(
        '1' => array(
            '深圳市龙岗区坂田清丽路1号宝能科技园南区12栋11楼',
            '0755-82560956-8001',
            '工作日（周一~周五）9:00~18:00',
        ),/*
        '2' => array(
            '深圳市福田区华强广场2楼Q2A242',
            '0755-23941714',
            '工作日（周一~周五）10:00~18:00'
        ),*/
    ),

    //香港自提地址
    'HK_SELF_PICK_ADDRESS' => array(
        '1' => array(
            '香港九龍觀塘成業街27號日昇中心9樓903C1室',
            '+852 -35908493',
            '工作日（周一~周五）9:00~18:00'
        ),
    ),

    //自营自提地址
    'S_SELF_PICK_ADDRESS' => array(
        '1' => array(
            '深圳市光明新区新湖街道楼村社区荔都路32号A栋三楼',
            '19925376867',
            '工作日（周一~周五）9:00~18:00'
        ),
    ),

    //【新增供应商需要修改】供应商标示
    //价格系数 hk:香港，cn:大陆，rate:暂时不理
    //价格系数 hk:香港，cn:大陆，rate:暂时不理
    'PRICE_MULTI' => array(
        'chip1stop'        => array('hk' => 1,    'cn' => 6.7*1.16*1,  'rate' =>0),
        'element14'        => array('hk' => 0.82*0.156, 'cn' => 0.82*1.16*1,'rate' =>0),
        'future'           => array(
                                  'hk' => 1,   'cn' => 6.7*1.16*1,
                                  'hk_E' => 1,   'cn_E' => 6.7*1.16,
                                  'rate' =>0),
        'digikey'          => array('hk' => 1,      'cn' => 6.7*1.16,   'rate' =>0),
        'verical'          => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'alliedelec'       => array('hk' => 1,    'cn' => 6.7*1.16*1,  'rate' =>0),
        'rs'               => array('hk' => 0,    'cn' => 1.16,     'rate' =>0),
        'avnet'            => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'arrow'            => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'online'           => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'rutronik24'       => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'mouser'           => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'rochester'        => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'tti'              => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'company'          => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'tme'              => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'powerandsignal'   => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'peigenesis'       => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
        'master'           => array('hk' => 1,    'cn' => 6.7*1.16,  'rate' =>0),
    ),

    //发票类型
    'INVOICE_TYPE' => array(
        '1' => '无需发票',
        '2' => '普通发票',
        '3' => '增值税专用发票',
    ),

    //发票类型
    'ORDER_INVOICE_TYPE' => array(
        '1' => '不开发票',
        '2' => '普通发票',
        '3' => '增值税专用发票',
        '4' => '增值税普通发票'
    ),

    //发票状态
    'ORDER_INVOICE_STATUS' => array(
        '-1' => '待确认',
        '1' => '已开票',
        '2' => '已发货',
    ),

    // 默认确认收货时间
    'ORDER_COMFIRM_TIME' => 604800, //60 * 60 * 24 * 7 7天

    // //订单类型 对 商品类型 映射
    // 'ORDER_GOODS_TYPE_MAP' => array(
    //     '1' => array(1, 2, 6),//联营 =》 联营、专卖、MRO
    //     '2' => array(3, 4),//自营 =》 自营、寄售
    // ),
    // //商品类型 对 订单类型 映射
    // 'GOODS_ORDER_TYPE_MAP' => array(
    //     '1' => 1,
    //     '2' => 1,
    //     '3' => 2,
    //     '4' => 2,
    //     '6' => 1,
    // ),

    //订单类型 对 商品类型 映射
    'ORDER_GOODS_TYPE_MAP' => array(
        '1' => array(1, 2, 4, 6),//联营 =》 联营、专卖、寄售、MRO
        '2' => array(3),//自营 =》 自营
    ),
    //商品类型 对 订单类型 映射
    'GOODS_ORDER_TYPE_MAP' => array(
        '1' => 1,
        '2' => 1,
        '3' => 2,
        '4' => 1,
        '6' => 1,
    ),

    //商品类型
    'ORDER_GOODS_TYPE' => array(
        '1' => '联营',
        '2' => '自营',
    ),

    //本地ID （深圳）
    'LOCATION_CITY_ID' => 77,
    //本省ID （广东省）
    'LOCATION_PROVINCE_ID' => 6,

    //物流价格(已废弃，redis读取express_fee)
    'SHIPPING_PRICE' => array(
        '0' => 10,//本地区
        '1' => 10,//本省份
        '-1' => 15,//外省份
    ),

    //币种代号
    'CURRENCY_CODE' => array(
        '1' => 'CNY',//人民币
        '2' => 'USD',//美元
        '3' => 'HKD',//港币
        '4' => 'EUR',//欧元
        '5' => 'GBP',//英镑
        '6' => 'CHF',//瑞士法郎
    ),

    // 自营订单倒计时默认天数
    'self_rest_time' => 2,

    // 京东的省份
    'JD_PROVINCE' => array('北京','上海','天津','重庆','河北','山西','河南','辽宁','吉林','黑龙江','内蒙古','江苏','山东','安徽','浙江','福建','湖北','湖南','广东','广西','江西','四川','海南','贵州','云南','西藏','陕西','甘肃','青海','宁夏','新疆','台湾','港澳','钓鱼岛'),

    //支付时订单ID类型区分
    'PAY_ORDER_ID_TYPE' => array(
        '0' => 'SALE_ORDER',//普通销售订单
        '1' => 'RECHARGE_ORDER',//充值单
        '2' => 'PCB_SALE_ORDER',//PCB销售订单
    ),

    // 自营样片订单指定内部人员
    'SELF_SAMPLE_SALES' => 1450, // 吴承义
    'SELF_SAMPLE_PUR'   => 1605, // 自营采购 - 平台

    //发票小黑屋
    "BCAK_HOUSE_INVOICE"=>[
        '91440300305954796X',
    ],
    //公司小黑屋
    "BCAK_HOUSE_COMPANYNAME"=>[
        '深圳市华控技术科技有限公司',
        "华控技术",
        "华控技术科技",
    ],

    "ichuntUserLimitGoodsNumsList" => "ichuntUserLimitGoodsNumsList_",

    "DJKCUSTOMERTYPE"=>[
        1=>"终端",
        2=>"贸易商",
        3=>"大学",
        4=>"个人",
    ],
    "DJKCONFIG"=>[
        "class_one"=>[
            // 0=>"航空航天|Aerospace",
            1=>"汽车|Automotive",
            2=>"计算机与外围设备|Computer & Peripherals",
            3=>"消费电子产品|Consumer Electronics",
            4=>"工业|Industrial",
            5=>"医疗|Medical",
            6=>"军事|Military",
        ],
        "class_two"=>[
            0=>[
                "飞机、商业运输|Aircraft, commercial transport",
                "飞机、无人机|Aircraft, UAV",
                "通信设备|Communications equipment",
                "计算机系统|Computer systems",
                "娱乐系统|Entertainment systems",
                "传感器，控制器，测试设备|Sensors, controls, test equipment",
                "安全系统|Safety systems",
                "卫星,商业|Satellites, commercial",
                "其他|Other",
            ],
            1=>[
                "传动系|Drive train",
                "仪表|Instrumentation",
                "安全系统|Safety systems",
                "其他|Other",
            ],
            2=>[
                "配件|Accessories",
                "台式电脑|Desktop",
                "笔记本电脑|Laptop",
                "服务器/存储|Server/storage",
                "其他|Other",
            ],
            3=>[
                "音频/视频|Audio/video",
                "游戏系统|Gaming systems",
                "家电类|Home appliances",
                "照明系统|Lighting systems",
                "有线通讯|Wired communications",
                "无线通讯|Wireless communications",
                "其他|Other",
            ],
            4=>[
                "家电/白色家电|Appliances/white goods",
                "自动化与控制|Automation and control",
                "可再生/化石燃料能源|Renewable/fossil fuels energy",
                "核能|Nuclear energy",
                "照明系统|Lighting systems",
                "传感器，控件，测试设备|Sensors, controls, test equipment",
                "工具|Tools",
                "其他|Other",
            ],
            5=>[
                "诊断设备|Diagnostic equipment",
                "仪器，设备|Instruments, devices",
                "成像/扫描|Imaging / scanning",
                "其他|Other",
            ],
            6=>[
                "军用飞机|Aircraft, military",
                "飞机，无人机|Aircraft, UAV",
                "通讯设备|Communications equipment",
                "电脑系统|Computer systems",
                "导弹系统|Missile systems",
                "卫星，军事|Satellites, military",
                "传感器，控件，测试设备|Sensors, controls, test equipment",
                "车辆，人员运输|Vehicles, personnel transport",
                "车辆，武器运输|Vehicles, weapon transport",
                "武器系统|Weapons systems",
                "其他|Other",
            ],
        ]
    ],
    "DJKCONFIGERP"=>[
        "class_one"=>[
            0=>"001",
            1=>"002",
            2=>"003",
            3=>"004",
            4=>"005",
            5=>"006",
            6=>"007",
        ],
        "class_two"=>[
            0=>[
                "001001",
                "001002",
                "001003",
                "001004",
                "001005",
                "001006",
                "001007",
                "001008",
                "001009",
            ],
            1=>[
                "002001",
                "002002",
                "002003",
                "002004",
            ],
            2=>[
                "003001",
                "003002",
                "003003",
                "003004",
                "003005",
            ],
            3=>[
                "004001",
                "004002",
                "004003",
                "004004",
                "004005",
                "004006",
                "004007",
            ],
            4=>[
                "005001",
                "005002",
                "005003",
                "005004",
                "005005",
                "005006",
                "005007",
                "005008",
            ],
            5=>[
                "006001",
                "006002",
                "006003",
                "006004",
            ],
            6=>[
                "007001",
                "007002",
                "007003",
                "007004",
                "007005",
                "007006",
                "007007",
                "007008",
                "007009",
                "007010",
                "007011",
            ],
        ]
    ],
    "CREATE_ORDER_BUYER_ID"=>1605,//本地1605   外网：1668

    // 售后申请原因
    'service_apply_reason' => [
        1 => '批次不符',
        2 => '质量问题',
        3 => '包装问题',
        4 => '客户取消',
        5 => '库存不足',
        6 => '其他',
    ],

    // 固定税率
	'fixed_tax_rate' => 0.13,

    // 添加标准品牌运营人员手机
    'add_standard_brand_operator_mobile' => '13093778996', // 徐腾渊 13093778996

    'com_nature' => [ // 公司性质
        1 => 'KA终端',
        2 => 'SMB终端',
        3 => '贸易商',
        4 => '科研院校'
    ],

    'fixed_order_sale_id' => 1445,

    // 10楼库存编码
    'joint_supplier_code' => 'L0012413',


    // 调拨单状态：-4删除，1新创建，2任务生成，3待执行，4已完成，5待调拨出，6待调拨入，7已取消
    'ALLOCATE_STATUS_ALL' => [
        -4  => '删除',
        1 => '新创建',
        2 => '任务生成',
        3 => '待执行',
        4 => '已完成',
        5 => '待调拨出',
        6 => '待调拨入',
        7 => '已取消',
    ],

);