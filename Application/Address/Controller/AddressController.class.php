<?php
namespace Address\Controller;

use Address\Controller\BaseController;

class AddressController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), array('status', 'pcd', 'allpcd', 'getdefaultinfo', 'infobyorder', 'getallnation'))) {
            // 检查登录
            if (in_array(strtolower(ACTION_NAME), array('recvlist', 'sendlist', 'info'))) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    /**
     * 收货地址列表
     * @return [type] [description]
     */
    public function recvList()
    {
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $UserAddressModel = D('UserAddress');
        $datas = $UserAddressModel->getUserList(0, 0, ["uc_id"=>$uc_id], 20, 'is_default DESC, address_id DESC');
        if (empty($datas)) {
            return $this->apiReturn(23022, '未找到相关地址');
        }
        return $this->apiReturn(0, '获取成功', $datas);
    }

    /**
     * 发货地址列表
     * @return [type] [description]
     */
    public function sendList()
    {
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $UserAddressModel = D('UserAddress');
        $datas = $UserAddressModel->getUserList(0, 1, ["uc_id"=>$uc_id], 20, 'is_default DESC, address_id DESC');
        if (empty($datas)) {
            return $this->apiReturn(23022, '未找到相关地址');
        }
        return $this->apiReturn(0, '获取成功', $datas);
    }

    /**
     * 新增收货地址
     * @return [type] [description]
     */
    public function create()
    {
        $user_id        = I('user_id') ? I('user_id'): cookie('uid');
        $uc_id          = I('uc_id') ? I('uc_id'): cookie('ucid');
        $org_id         = I('org_id', 1, 'intval');//组织id 1猎芯 3华云
        $operator_id    = I('operator_id', 0);
        $address_type   = I('address_type', 0, 'intval');//地址类型（1-发货地址，0-收货地址）
        $mobile         = I('mobile');
        $intl_code      = I('intl_code');
        $type           = I('type', ''); // CRM传递过来的地址
        $mobile         = get_inte_mobile($mobile, $intl_code);
        $nation_id      = I('nation_id', 36); // 默认中国
        $province       = I('province', 0);
        $city           = I('city', 0);
        $district       = I('district', 0);
        $detail_address = I('detail_address', '');

        if ($nation_id == 36 && (in_array($province, [-1, 0]) || in_array($city, [-1, 0]) || in_array($district, [-1, 0]))) {
            return $this->apiReturn(23028, '国家为中国时，省市区必填', '');
        }

        if (!$detail_address) {
            return $this->apiReturn(23028, '详细地址必填', '');
        }

        if ($type != 'crm' && $nation_id == 36 && !is_mobile($mobile)) {
            return $this->apiReturn(11006, '手机号码格式错误，请重新输入', '');
        }

        if (!in_array($address_type, array(0, 1))) {
            return $this->apiReturn(23028, '地址类型错误');
        }
        $UserAddressModel = D('UserAddress');
        $count = $UserAddressModel->getUserCount(0, $address_type,["uc_id"=>$uc_id]);
        if ($count > 20) {
            return $this->apiReturn(23027, '地址已达20个上限');
        }

        // 为海外时，设置为空
        if ($nation_id != 36) {
//            $intl_code = '';
        }

        $data = [
            'user_id'        => $user_id,
            'org_id'         => $org_id,
            'uc_id'          => $uc_id,
            'consignee'      => I('consignee'),
            'nation_id'      => $nation_id,
            'province'       => $province,
            'city'           => $city,
            'district'       => $district,
            'detail_address' => $detail_address,
            'address_type'   => $address_type,
            'is_default'     => I('is_default', 0),
            'mobile'         => I('mobile'),
            'intl_code'      => $intl_code,
        ];
        if (!$UserAddressModel->create($data)) {
            return $this->apiReturn(23025, $UserAddressModel->getError());
        }
        $UserAddressModel->user_id = $user_id;
        $UserAddressModel->address_type = $address_type;
        $is_default = $UserAddressModel->is_default;
        $address_id = $UserAddressModel->add();
        if ($address_id === false) {
            return $this->apiReturn(23026, '新增地址失败');
        }
        //设置默认地址
        if ($is_default == 1) {
            $res = $UserAddressModel->setUserDefault(0, $address_id, $address_type,$uc_id);
            if ($res === false) {
                return $this->apiReturn(23023, '设置默认地址失败');
            }
        }

        $operator = $operator_id ?: $user_id; // 操作人
        $operator_type = $operator_id ? 2 : 1; // 操作类型

        D('Home/UserActionLog')->addLog($user_id, $operator, $operator_type, '新增地址，ID：'.$address_id);

        return $this->apiReturn(0, '新增成功', $address_id);
    }


    /**
     * 编辑地址
     * @return [type] [description]
     */
    public function update()
    {
        $user_id        = I('user_id') ? I('user_id') : cookie('uid');
        $uc_id          = I('uc_id') ? I('uc_id'): cookie('ucid');
//        $org_id         = I('org_id', 1, 'intval');//组织id 1猎芯 3华云
        $operator_id    = I('operator_id', 0);
        $address_id     = I('address_id', 0, 'intval');
        $mobile         = I('mobile');
        $intl_code      = I('intl_code');
        $type           = I('type', ''); // CRM传递过来的地址
        $mobile         = get_inte_mobile($mobile, $intl_code);
        $nation_id      = I('nation_id', 36); // 默认中国
        $province       = I('province', 0);
        $city           = I('city', 0);
        $district       = I('district', 0);
        $detail_address = I('detail_address', '');

        if ($nation_id == 36 && (in_array($province, [-1, 0]) || in_array($city, [-1, 0]) || in_array($district, [-1, 0]))) {
            return $this->apiReturn(23028, '国家为中国时，省市区必填', '');
        }

        if (!$detail_address) {
            return $this->apiReturn(23028, '详细地址必填', '');
        }

        if ($type != 'crm' && $nation_id == 36 && !is_mobile($mobile)) {
            return $this->apiReturn(11006, '手机号码格式错误，请重新输入', '');
        }
        $UserAddressModel = D('UserAddress');
        $info = $UserAddressModel->getUserInfo(0, $address_id,["uc_id"=>$uc_id]);
        if (empty($info)) {
            return $this->apiReturn(23022, '未找到相关地址');
        }

        // 为海外时，设置为空
        if ($nation_id != 36) {
//            $intl_code = '';
        }

        $data = [
            'address_id'     => $address_id,
            'consignee'      => I('consignee'),
            'nation_id'      => $nation_id,
            'province'       => $province,
            'city'           => $city,
            'district'       => $district,
            'detail_address' => $detail_address,
            'address_type'   => I('address_type', 0),
            'is_default'     => I('is_default', 0),
            'mobile'         => I('mobile'),
            'intl_code'      => $intl_code,
        ];
        if (!$UserAddressModel->create($data)) {
            return $this->apiReturn(23025, $UserAddressModel->getError());
        }
        $is_default = $UserAddressModel->is_default;
        $res = $UserAddressModel->save();
        if ($res === false) {
            return $this->apiReturn(23029, '编辑地址失败');
        }
        //设置默认地址
        if ($is_default == 1) {
            $res = $UserAddressModel->setUserDefault(0, $address_id, $info['address_type'],$uc_id);
            if ($res === false) {
                return $this->apiReturn(23023, '设置默认地址失败');
            }
        }

        // 编辑前地址信息
        $log_province       = $info['province_val'];
        $log_city           = $info['city_val'];
        $log_district       = $info['district_val'];
        $log_detail_address = $info['detail_address'];
        $log_mobile         = $info['intl_code'] && $info['intl_code'] != '0086' ? $info['intl_code'].'+'.$info['mobile'] : $info['mobile'];
        $log_consignee      = $info['consignee'];       
        $log_zipcode        = $info['zipcode'];
        $log_is_default     = $info['is_default'] ? '是' : '否';

        $log = '修改前：地址：'.$log_province.$log_city.$log_district.$log_detail_address.'，联系电话：'.$log_mobile.'，收货人：'.$log_consignee.'，邮编：'.$log_zipcode.'，是否默认：'.$log_is_default;

        $operator = $operator_id ? $operator_id : $user_id; // 操作人
        $operator_type = $operator_id ? 2 : 1; // 操作类型

        D('Home/UserActionLog')->addLog($user_id, $operator, $operator_type, '编辑地址，ID：'.$address_id.'，'.$log);

        return $this->apiReturn(0, '编辑成功');
    }

    /**
     * 设置默认地址
     */
    public function setDefault()
    {
        $user_id     = I('user_id') ? I('user_id') : cookie('uid');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $operator_id = I('operator_id', 0);
        $address_id  = I('address_id', 0, 'intval');
        $UserAddressModel = D('UserAddress');
        $info = $UserAddressModel->getUserInfo(0, $address_id,["uc_id"=>$uc_id]);
        if (empty($info)) {
            return $this->apiReturn(23022, '未找到相关地址');
        }
        $res = $UserAddressModel->setUserDefault(0, $address_id, $info['address_type'],$uc_id);
        if (!$res) {
            return $this->apiReturn(23023, '设置默认地址失败');
        }

        $operator = $operator_id ?: $user_id; // 操作人
        $operator_type = $operator_id ? 2 : 1; // 操作类型

        D('Home/UserActionLog')->addLog($user_id, $operator, $operator_type, '设置默认地址，ID：'.$address_id);

        return $this->apiReturn(0, '设置成功');
    }

    // 后台新增订单获取地址
    public function infoByOrder()
    {
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $address_id = I('address_id', 0, 'intval');

        $map = [];
        if ($uc_id) {
            $map['uc_id'] = $uc_id;
        }
        
        $UserAddressModel = D('UserAddress');
        $info = $UserAddressModel->getUserInfo(0, $address_id, $map);
        if (empty($info)) {
            return $this->apiReturn(23022, '未找到相关地址');
        }
        return $this->apiReturn(0, '获取成功', $info);
    }

    /**
     * 获取地址信息
     * @return [type] [description]
     */
    public function info()
    {
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $address_id = I('address_id', 0, 'intval');
        
        $map = [];
        if ($uc_id) {
            $map['uc_id'] = $uc_id;
        }

        $UserAddressModel = D('UserAddress');
        $info = $UserAddressModel->getUserInfo(0, $address_id, $map);
        if (empty($info)) {
            return $this->apiReturn(23022, '未找到相关地址');
        }
        return $this->apiReturn(0, '获取成功', $info);
    }

    /**
     * 获取用户默认地址
     * @return [type] [description]
     */
    public function getDefaultInfo()
    {
        $address_type = I('address_type', 0, 'intval');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $UserAddressModel = D('UserAddress');
        $info = $UserAddressModel->getDefaultInfo(0, $address_type,$uc_id);
        if (empty($info)) {
            return $this->apiReturn(23022, '未找到相关地址');
        }
        return $this->apiReturn(0, '获取成功', $info);
    }

    /**
     * 删除地址
     * @return [type] [description]
     */
    public function delete()
    {
        $user_id     = $this->getUidByAdmin();
        $operator_id = I('operator_id', 0);
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $address_id  = I('address_id', 0, 'intval');
        $UserAddressModel = D('UserAddress');
        $res = $UserAddressModel->deleteUser(0, $address_id,$uc_id);
        if (!$res) {
            return $this->apiReturn(23024, '删除地址失败');
        }

        $operator = $operator_id ?: $user_id; // 操作人
        $operator_type = $operator_id ? 2 : 1; // 操作类型

        D('Home/UserActionLog')->addLog($user_id, $operator, $operator_type, '删除地址，ID：'.$address_id);

        return $this->apiReturn(0, '设置成功');
    }

    /**
     * 获取地址数量
     * @return [type] [description]
     */
    public function count()
    {
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $address_type = I('address_type', 0, 'intval');
        if (!in_array($address_type, array(0, 1))) {
            return $this->apiReturn(23028, '地址类型错误');
        }
        $UserAddressModel = D('UserAddress');
        $count = $UserAddressModel->getUserCount(0, $address_type,["uc_id"=>$uc_id]);
        return $this->apiReturn(0, '获取成功', $count);
    }

    /**
     * 省市区
     * @return [type] [description]
     */
    public function pcd()
    {
        $parent_id = I('id', 0, 'intval');
        $RegionModel = D('Region');
        $datas = $RegionModel->getList($parent_id);
        return $this->apiReturn(0, '获取成功', $datas);
    }

    /**
     * 获取所有省市区
     * @return [type] [description]
     */
    public function allPCD()
    {
        $prefix = array('prefix' => '');
        $province = S('provinces', '', $prefix);
        $city = S('citys', '', $prefix);
        $district = S('towns', '', $prefix);
        $RegionModel = D('Region');
        //写缓存
        if (empty($province) || empty($city) || empty($district)) {
            //省
            $p = $RegionModel->getListValue(1);
            $province[1] = array(
                'parent_id' => 1,
                'children' => $p,
            );
            foreach ($p as $v) {
                //市
                $c = $RegionModel->getListValue($v['region_id']);
                $city[$v['region_id']] = array(
                    'parent_id' => $k,
                    'children' => $c,
                );
                foreach ($c as $v) {
                    //区
                    $d = $RegionModel->getListValue($v['region_id']);
                    $district[$v['region_id']] = array(
                        'parent_id' => $k,
                        'children' => $d,
                    );
                }
            }
            S('provinces', $province, $prefix);
            S('citys', $city, $prefix);
            S('towns', $district, $prefix);
        }
        //读缓存
        $datas = array();
        foreach ($province['1']['children'] as &$p) {
            $data = array(
                'value' => $p['region_id'],
                'text' => $p['region_name'],
                'children' => array(),
            );
            foreach ($city[$p['region_id']]['children'] as &$c) {
                $city_arr = array(
                    'value' => $c['region_id'],
                    'text' => $c['region_name'],
                    'children' => array(),
                );
                foreach ($district[$c['region_id']]['children'] as &$d) {
                    $city_arr['children'][] = array(
                        'value' => $d['region_id'],
                        'text' => $d['region_name'],
                    );
                }
                if (empty($city_arr['children']))
                    unset($city_arr['children']);
                $data['children'][] = $city_arr;
            }
            $datas[] = $data;
        }
        return $this->apiReturn(0, '获取成功', $datas);
    }

    // 获取所有国家
    public function getAllNation()
    {
        $key = 'all_nations';
        $nations = S($key);

        if (!$nations) {
            $NationModel = D('Nation');
            $nations = $NationModel->getAllNation();

            $options = [
                'expire' => 3600, // 缓存1小时
            ];
            S($key, $nations, $options);
        }

        return $this->apiReturn(0, '获取成功', $nations);
    }

}
