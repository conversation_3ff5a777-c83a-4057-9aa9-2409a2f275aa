<?php
namespace Address\Model;

use Think\Model;

class RegionModel extends Model
{
    public function getList($id = 0)
    {
        $map = array(
            'parent_id' => $id,
        );
        $datas = $this->where($map)->getField('region_id,region_name');
        return $datas;
    }

    public function getListValue($id = 0)
    {
        $map = array(
            'parent_id' => $id,
        );
        $datas = $this->where($map)->field('region_id,parent_id,region_name,region_type')->select();
        return $datas;
    }
}