<?php
namespace Address\Model;

use Think\Model;

class UserAddressModel extends Model
{
    protected $_validate = array(
        array('consignee', 'require', '收货人不能为空', 1),
//        array('consignee', 'require', '收货人请填写完整',1),
        array('mobile', 'require', '手机不能为空', 1),
        // array('province', 'number', '省份信息错误', 1),
        // array('city', 'number', '城市信息错误', 1),
        // array('district', 'require', '区信息错误', 1),
        array('detail_address', 'require', '详细地址不能为空', 1),
        array('address_type', array(1,0), '地址类型值错误', 2, 'in'),
    );

    // protected $_auto = array(
    //     array('user_id', 'cookie', 1, 'function', 'uid'),
    // );

    protected function nameLength($val)
    {
        return mb_strlen($val, 'utf8') >= 2 ? true :false;
    }

    /**
     * 获取客户地址数
     * @param  [type]  $user_id      [description]
     * @param  integer $address_type [description]
     * @param  string  $where        [description]
     * @return [type]                [description]
     */
    public function getUserCount($user_id, $address_type = 1, $where = '')
    {

        if(!empty($user_id)){
            $map = array(
                'user_id' => $user_id,
                'address_type' => $address_type,
            );
        }else{
            $map = array(
                'address_type' => $address_type,
            );
        }
        !empty($where) && $map = array_merge($map, $where);
        $datas = intval($this->where($map)->count());
        return $datas;
    }

    /**
     * 获取客户收发货地址列表
     * @param  [type]  $user_id      [description]
     * @param  integer $address_type [description]
     * @param  string  $where        [description]
     * @param  string  $limit        [description]
     * @param  string  $order        [description]
     * @return [type]                [description]
     */
    public function getUserList($user_id, $address_type = 1, $where = '', $limit = '', $order = 'address_id')
    {

        if(!empty($user_id)){
            $map = array(
                'user_id' => $user_id,
                'address_type' => $address_type,
            );
        }else{
            $map = array(
                'address_type' => $address_type,
            );
        }
        !empty($where) && $map = array_merge($map, $where);
        
        $datas = $this->where($map)->limit($limit)->order($order)->select();

        foreach ($datas as &$v) {
            $nation = get_nation($v['nation_id']);
            $v['nation_cn'] = $nation['name_cn'];
            $v['nation_en'] = $nation['name_en'];

            $v['province_val'] = get_province($v['province']);
            $v['city_val'] = get_city($v['city']);
            $v['district_val'] = !empty($v['district']) ? get_district($v['district']) : '';
        }
        return $datas;
    }

    /**
     * 获取客户地址信息
     * @param  [type] $user_id    [description]
     * @param  [type] $address_id [description]
     * @return [type]             [description]
     */
    public function getUserInfo($user_id, $address_id,$where = '')
    {
        if(!empty($user_id)){
            $map = array(
                'user_id' => $user_id,
                'address_id' => $address_id,
            );
        }else{
            $map = array(
                'address_id' => $address_id,
            );
        }

        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->where($map)->find();

        if (!$datas) return false;

        $datas['province_val'] = get_province($datas['province']);
        $datas['city_val'] = get_city($datas['city']);
        $datas['district_val'] = get_district($datas['district']);
        return $datas;
    }

    /**
     * 获取默认地址
     * @param  [type] $user_id [description]
     * @return [type]          [description]
     */
    public function getDefaultInfo($user_id, $address_type = 0,$uc_id=-999)
    {

        if(!empty($user_id)){
            $map = array(
                'user_id' => $user_id,
                'is_default' => 1,
                'address_type' => $address_type,
            );
        }else{
            $map = array(
                'is_default' => 1,
                'uc_id' => $uc_id,
                'address_type' => $address_type,
            );
        }
        $datas = $this->where($map)->field('address_id, consignee, zipcode, mobile, province, city, district, detail_address, is_default, address_type, intl_code')->find();
        return $datas;
    }

    /**
     * 设置客户默认地址
     * @param [type] $user_id      [description]
     * @param [type] $address_id   [description]
     * @param [type] $address_type [description]
     */
    public function setUserDefault($user_id, $address_id, $address_type,$uc_id=1)
    {
        $this->startTrans();


        if(!empty($user_id)){
            $map = array(
                'user_id' => $user_id,
                'address_type' => $address_type,
                'uc_id' => $uc_id,
            );
            $res = $this->where($map)->setField('is_default', 0);
            if ($res === false) {
                return false;
            }
            $map = array(
                'user_id' => $user_id,
                'uc_id' => $uc_id,
                'address_id' => $address_id,
            );
        }else{
            $map = array(
                'address_type' => $address_type,
                'uc_id' => $uc_id,
            );
            $res = $this->where($map)->setField('is_default', 0);
            if ($res === false) {
                return false;
            }
            $map = array(
                'uc_id' => $uc_id,
                'address_id' => $address_id,
            );
        }


        $res = $this->where($map)->setField('is_default', 1);
        if ($res === false) {
            $this->rollback();
            return false;
        }
        $this->commit();
        return $res;
    }

    /**
     * 删除地址
     * @param  [type] $user_id    [description]
     * @param  [type] $address_id [description]
     * @return [type]             [description]
     */
    public function deleteUser($user_id, $address_id, $uc_id=1)
    {
        if(!empty($user_id)){
            $map = array(
                'user_id' => $user_id,
                'uc_id' =>  $uc_id,
                'address_id' => $address_id,
            );
        }else{
            $map = array(
                'uc_id' =>  $uc_id,
                'address_id' => $address_id,
            );
        }
        $res = $this->where($map)->delete();
        return $res;
    }
}