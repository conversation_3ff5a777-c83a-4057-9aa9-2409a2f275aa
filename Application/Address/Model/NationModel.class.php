<?php
namespace Address\Model;

use Think\Model;

class NationModel extends Model
{
    public function getNationById($nation_id)
    {
        $map = [
            'nation_id' => $nation_id,
        ];
        return $this->where($map)->find();
    }

    // 获取所有国家
    public function getAllNation()
    {
        return $this->where(['status' => 1])->field('nation_id,name_cn,name_en')->select();
    }

}