<?php
namespace Oss\Controller;

class BaseController extends \Common\Controller\BaseController
{
    /**
     * 统一格式返回
     * @param  integer $code   [description]
     * @param  string  $msg    [description]
     * @param  array   $extend [description]
     * @return [type]          [description]
     */
    protected function apiReturn($code = 0, $msg = '', $extend = array())
    {
        $data = array(
            'code'    => $code,
            'message' => $msg,
            'data'    => $extend,
        );

        //判断是否内部调用，内部调用不输出只返回
        $near_trace     = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        $backtrace      = array_pop($near_trace);
        $backtrace_arr  = explode('\\', $backtrace['class']);
        $backtrace_name = array_pop($backtrace_arr);
        $index          = strpos($backtrace_name, C('DEFAULT_C_LAYER'));

        $comp1 = strtolower(substr($backtrace_name, 0, $index).':'.$backtrace['function']);
        $comp2 = strtolower(CONTROLLER_NAME.':'.ACTION_NAME);
        $comp  = explode(':', $comp1);
      
        if ($comp1 == $comp2 || $comp[0] == 'oss' || $comp[1] == '_initialize') {
            // 直接请求的控制器 或 构造函数终止
            if ($data['code'] != 0 && $data['code'] != 200) {
                $file        = !empty($near_trace[0]['file']) ? $near_trace[0]['file'] : '';
                $line        = !empty($near_trace[0]['line']) ? $near_trace[0]['line'] : 0;
                $class       = isset($backtrace['class']) ? $backtrace['class'] : '';
                $func        = isset($backtrace['function']) ? $backtrace['function'] : '';
                $method      = trim($class . '::' . $func, '::');
                $request_url = getUrl();
                $request     = !empty($_REQUEST) ? json_encode($_REQUEST) : '';
                $request     .= !empty($_FILES) ? json_encode($_FILES) : '';

                $log = \LogReport::anlyError($msg, $file, $line, $data['code'], $method, null, null, $request_url, $request);
                \LogReport::write($log);

                if (is_array($data['data']) || empty($data['data'])) {
                    $data['data']['unique'] = $log['unique'];
                }
            }

            $callback = I('get.callback', null);
            $callback_type = is_null($callback) ? 'json' : 'jsonp';

            return $this->ajaxReturn($data, $callback_type);
        }

        //内部逻辑调用返回
        return $data;
    }


    // 校验上传用户（source：1-内部用户，2-外部用户）
    public function validUser($request)
    {
        $uid = 0;

        // 检查传递过来的参数
        if (!isset($request['source'])) return ['err_code'=>26001, 'err_msg'=>'参数缺失，请检查传递的参数'];

        // 内部或外部---图片限制和登录校验
        if ($request['source'] == 1) {
            $uploadNum = C('InternalNum');

            if (!$this->auth()) return ['err_code'=>26002, 'err_msg'=>'内部免验证不通过']; 
        } else {
            $uploadNum = C('ExternalNum');

            parent::_initialize();

            //检查登录
            $res = $this->checkLogin();

            if ($res['err_code'] != 0) return ['err_code'=>$res['err_code'], 'err_msg'=>$res['err_msg']];

            $uid = cookie('uid');
        }

        $data = ['uid' => $uid, 'uploadNum' => $uploadNum]; 

        return ['err_code'=>0, 'err_msg'=>'成功', 'data'=>$data];
    }


}