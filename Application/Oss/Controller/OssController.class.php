<?php

namespace Oss\Controller;

use Oss\Controller\BaseController;
use OSS\OssClient;

// OSS客户端类
use OSS\Core\OssException;

// OSS异常类
use Think\Cache\Driver\Redis;

require_once './ThinkPHP/Library/Vendor/OSS/autoload.php';

class OssController extends BaseController
{
    // 允许的请求类型列表
    protected static $allowMethod = array('POST', 'GET', 'PUT', 'DELETE');
    protected static $ossClient = null;

    public function _initialize()
    {
        parent::_initialize();

        $requireMethod = strtoupper($_SERVER['REQUEST_METHOD']); // 请求类型

        if (!in_array($requireMethod, self::$allowMethod)) return $this->apiReturn(26000, '请求方法不被允许');
    }

    /**
     * 接收数据
     * @return [Json] [返回信息给前端]
     */
    public function upload()
    {
        $params = I('request.');

        $res = $this->validUser($params); // 校验用户

        if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

        $uid = $res['data']['uid']; // 用户ID
        $uploadNum = $res['data']['uploadNum']; // 最大上传数量

        $res = $this->checkFile($_FILES, $uploadNum); // 校验文件

        if ($res !== true) return $res;

        // 上传到OSS，并返回URL
        if (is_array($_FILES['upload']['name'])) { //  多文件上传
            $fileNum = count($_FILES['upload']['name']); // 上传文件数量

            for ($i = 0; $i < $fileNum; $i++) {
                $data['upload']['name'] = $_FILES['upload']['name'][$i];
                $data['upload']['type'] = $_FILES['upload']['type'][$i];
                $data['upload']['tmp_name'] = $_FILES['upload']['tmp_name'][$i];

                $response = $this->setObject($params, $data); // 设置object和水印

                $res = $this->uploadToOss($params, $response, $data, $uid);
                if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

                $object[$i] = $response['object'];

                // 拼接URL
                $return_url[$i][] = $res['data'];
                $return_url[$i][] = $_FILES['upload']['name'][$i];
            }

            return $this->apiReturn(200, '上传成功', $return_url);
        }

        // 单文件上传
        $response = $this->setObject($params, $_FILES); // 设置object和水印

        $res = $this->uploadToOss($params, $response, $_FILES, $uid);
        if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);

        $return_url[] = $res['data'];
        $return_url[] = $_FILES['upload']['name'];

        return $this->apiReturn(200, '上传成功', $return_url);
    }

    /**
     * 文件校验
     * @param  [Array] $file [文件]
     * @param  [Integer] $num  [文件数量限制]
     * @return [Boolen / Json]
     */
    public function checkFile($file, $num)
    {
        // 多文件上传
        if (is_array($file['upload']['name'])) {
            $fileNum = count($file['upload']['name']); // 上传文件数量

            if ($fileNum > $num) return $this->apiReturn(26003, '上传文件数量超出限制，最大限制为' . $num);

            $data = array();

            for ($i = 0; $i < $fileNum; $i++) {
                $type = D('File')->fileType($file['upload']['type'][$i]);

                if ($type === false) $this->apiReturn(26004, '暂时不支持此文件类型上传');

                $data['upload']['name'] = $file['upload']['name'][$i];
                $data['upload']['type'] = $file['upload']['type'][$i];
                $data['upload']['tmp_name'] = $file['upload']['tmp_name'][$i];
                $data['upload']['error'] = $file['upload']['error'][$i];
                $data['upload']['size'] = $file['upload']['size'][$i];

                $res = $this->verifyFileType($type, $data);

                if ($res !== true) return $res;

                unset($data);
            }

            return true;
        } else {
            $type = D('File')->fileType($file['upload']['type']);

            if ($type === false) $this->apiReturn(26004, '暂时不支持此文件类型上传');

            return $this->verifyFileType($type, $file);
        }
    }

    /**
     * 根据文件类型验证文件
     * @param  [String] $type [类型]
     * @param  [Array] $file [文件]
     * @return [Boolen / Json]
     */
    public function verifyFileType($type, $file)
    {
        $image = D('Image');
        $fileModel = D('File');
        $size = $file['upload']['size'];
        $allow_max_size = I('allow_max_size', '', 'int');

        switch ($type) {
            case 'images':
                $explode = explode('.', $file['upload']['name']);
                $ext = $explode[count($explode) - 1];

                if (!$image->checkImageExt($ext, $allow_max_size)) return $this->apiReturn(26005, $file['upload']['name'] . '类型错误，请重新上传');

                if (!$image->checkImageSize($size, $allow_max_size)) return $this->apiReturn(26006, $file['upload']['name'] . '过大，请重新上传');

                return true;
                break;
            case 'doc/word':
            case 'application/wps-office.pdf':
            case 'doc/excel':
            case 'doc/ppt':
            case 'doc/pdf':
            case 'doc/txt':
            case 'doc/zip':
                if (!$fileModel->checkDocSize($size, $allow_max_size)) return $this->apiReturn(26007, $file['upload']['name'] . '过大，请重新上传');

                return true;
                break;
            case 'video':
                if (!$fileModel->checkVideoSize($size, $allow_max_size)) return $this->apiReturn(26008, $file['upload']['name'] . '过大，请重新上传');

                return true;
                break;
            case 'audio/mp3':
            case 'audio/wmv':
                if (!$fileModel->checkAudioSize($size, $allow_max_size)) return $this->apiReturn(26009, $file['upload']['name'] . '过大，请重新上传');

                return true;
                break;
            default:
                return $this->apiReturn(26010, '没匹配到对应的文件类型!');
                break;
        }
    }

    /**
     * ossClient实例化
     * @return [Object]
     */
    public function ossClient()
    {
        $accessKeyId = C('AccessKeyId');
        $accessKeySecret = C('AccessKeySecret');
        $endPoint = C('EndPoint');

        if (self::$ossClient == null) {
            self::$ossClient = new OssClient($accessKeyId, $accessKeySecret, $endPoint);
        }

        return self::$ossClient;
    }

    // 获取来源页面
    public function getRefererUrl()
    {
        $origin_url = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';

        if (!$origin_url) {
            $origin_url = $_SERVER['HTTP_REFERER']; // 来源页面
        }

        return $origin_url ? $origin_url : '';
    }

    /**
     * / 设置object
     * @param [type] $params  [description]
     * @param [type] $file    [description]
     */
    public function setObject($params, $file)
    {
        $dir_name = ''; // 目录
        $test_dir_name = ''; // 测试目录
        $goods_name = isset($params['goods_name']) ? $params['goods_name'] : ''; // 基石商品名称变量
        $is_rename = isset($params['is_rename']) ? $params['is_rename'] : 1; // 是否改变原文件名，默认1，0-否，1-是
        $set_dir = isset($params['set_dir']) ? $params['set_dir'] : ''; // 自定义目录，默认空
        $file_name = $file['upload']['name']; // 文件名
        $file_type = $file['upload']['type']; // 文件类型
        $file_tmp_name = $file['upload']['tmp_name']; // 临时路径
        $origin_url = $this->getRefererUrl(); // 来源页面

        // 检查是否为测试数据，设置测试目录
        if (!$origin_url || preg_match('/^http(s?)\:\/\/(sz|szm|t|tm)/', $origin_url) || preg_match('/liexin/', $origin_url)) {
            $test_dir_name = 'test/';
            $dir_name .= $test_dir_name;
        }

        $explode = explode('.', $file_name);
        $ext = $explode[count($explode) - 1]; // 获取文件后缀

        $date = explode('-', date('Y-m-d', time())); // 分割日期

        // 是否改变文件名
        if ($is_rename) {
            $rename = md5(time() . rand('10000', '99999')) . '.' . strtolower($ext); // 新文件名
        } else {
            $rename = $file_name;
        }

        $file_type_dir = D('file')->fileType($file_type); // 获取对应文件类型
        $dir_name .= $file_type_dir . '/';

        // 自定义目录
        if ($set_dir) {
            if ($params['bucket_type'] == 2) {
                $set_dir .= $dir_name;
            }

            $response['object'] = $set_dir . $date[0] . $date[1] . '/' . $date[2] . '/' . $rename;
            $response['imageWater'] = '';

            return $response;
        }

        // 根据网站类型设置目录
        if (preg_match('/^http(s?)\:\/\/[sz|szm|t|tm|www|m]/', $origin_url)) {
            $dir_name .= 'ichunt/';
        } else if (preg_match('/^http\:\/\/[footstone]/', $origin_url) && $goods_name != 'undefined' && $goods_name != '') {
            $dir_name .= 'goods/';
        } else {
            $dir_name .= 'cms/';
        }

        if ($goods_name != 'undefined' && $goods_name != '') { // 基石系统上传商品图片目录设置
            $rename = md5($goods_name) . '.' . strtolower($ext);
            $footstoneDir1 = substr($rename, 0, 2);
            $footstoneDir2 = substr($rename, 2, 2);

            $response['object'] = $dir_name . $footstoneDir1 . '/' . $footstoneDir2 . '/' . $rename;

            $goods_water = C('goods_water');
            $response['imageWater'] = D('Image')->imageWater($file_tmp_name, $file_name, $goods_water['width'], $goods_water['height'], $goods_water['img_url']);
        } else if ($goods_name == 'undefined') { // 品牌图片上传 (固定目录)
            $response['object'] = $test_dir_name . 'images/ichunt/brand/' . $date[0] . $date[1] . '/' . $date[2] . '/' . $rename;

            $brand_water = C('brand_water');
            $response['imageWater'] = D('Image')->imageWater($file_tmp_name, $file_name, $brand_water['width'], $brand_water['height'], $brand_water['img_url']);
        } else { // 普通图片上传
            $response['object'] = $dir_name . $date[0] . $date[1] . '/' . $date[2] . '/' . $rename;
            $response['imageWater'] = '';
        }

        return $response;
    }

    /**
     * 上传到OSS
     * @param  [type] $params [上传参数]
     * @param  [type] $upload  [object和水印]
     * @param  [type] $file    [上传文件]
     * @param  [type] $uid     [用户ID]
     * @return [type]          [description]
     */
    public function uploadToOss($params, $upload, $file, $uid)
    {
        $ossClient = $this->ossClient();

        $bucket_type = isset($params['bucket_type']) ? $params['bucket_type'] : 1;

        $bucket = $bucket_type == 1 ? C('Bucket') : C('Business_Bucket');
        $object = $upload['object'];
        $endPoint = C('EndPoint');
        $endPoint = substr($endPoint, stripos($endPoint, '//') + 2);

        if ($upload['imageWater']) {
            $localPath = $upload['imageWater'];
        } else {
            $localPath = $file['upload']['tmp_name']; // 本地临时路径
        }

        try {
            $res = $ossClient->uploadFile($bucket, $object, $localPath);

            unlink($localPath);

            // 上传后处理
            if (isset($res['info']['http_code']) && $res['info']['http_code'] == 200) {
                $oss_url = $bucket . '.' . $endPoint . '/' . $object;

                if ($bucket_type == 1) {
                    $img_url = C('OSS_URL') . $object;
                } else {
                    $img_url = C('BUSINESS_OSS_URL') . $object;
                }

                $this->uploadLog($params, $uid, $file['upload']['name'], 1, $oss_url, $img_url); // 记录到日志表

                return ['err_code' => 0, 'err_msg' => '上传成功', 'data' => $img_url];
            }

            return ['err_code' => 26010, 'err_msg' => '上传失败，原因：' . json_encode($res)];
        } catch (Exception $e) {
            return ['err_code' => 26010, 'err_msg' => '上传接口异常，原因：' . $e->getMessage()];
        }
    }

    /**
     * 记录到日志表
     * @param  [Integer] $params      [上传参数]
     * @param  [Integer] $uid         [用户ID]
     * @param  [Varchar] $file_name   [文件名]
     * @param  [Integer] $status      [状态]
     * @param  [String]  $oss_url     [oss url]
     * @param  [String]  $img_url     [img url]
     * @return null / Exception
     */
    public function uploadLog($params, $uid, $file_name, $status, $oss_url = '', $img_url = '')
    {
        $uploadLog = M('uploadLog');

        $data['uid'] = $uid;
        $data['file_name'] = $file_name;
        $data['source'] = $params['source'];
        $data['status'] = $status;
        $data['oss_url'] = $oss_url;
        $data['img_url'] = $img_url;
        $data['referer_url'] = $this->getRefererUrl();
        $data['ip'] = get_client_ip(0, true);
        $data['create_time'] = time();

        try {
            $uploadLog->add($data);
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }

    // 删除object
    public function delete()
    {
        if (!$this->auth()) return $this->apiReturn(26002, '内部免验证不通过');

        $object = I('object', '');

        if (!$object) return $this->apiReturn(26030, '参数缺失，请检查传递的参数');

        $bucket_type = I('bucket_type', 1);

        $ossClient = $this->ossClient();
        $bucket = $bucket_type == 1 ? C('Bucket') : C('Business_Bucket');
        $oss_url = $bucket_type == 1 ? C('OSS_URL') : C('BUSINESS_OSS_URL');

        $object = str_replace($oss_url, '', $object);

        // 检查文件是否存在
        if (is_array($object)) { // 批量删除
            $count = count($object);

            if ($count > 100) return $this->apiReturn(26031, '批量删除超出了最大限制（100）');

            foreach ($object as $v) {
                $res = $ossClient->doesObjectExist($bucket, $v);

                if ($res === false) return $this->apiReturn(26032, 'object不存在，文件地址：' . $oss_url . $v);
            }
        } else {
            $res = $ossClient->doesObjectExist($bucket, $object);

            if ($res === false) return $this->apiReturn(26032, 'object不存在，文件地址：' . $oss_url . $object);
        }

        try {
            if (is_array($object)) {
                $ossClient->deleteObjects($bucket, $object);
            } else {
                $ossClient->deleteObject($bucket, $object);
            }
        } catch (OssException $e) {
            return $this->apiReturn(0, '删除失败，原因：' . $e->getMessage());
        }

        return $this->apiReturn(0, '删除成功');
    }

}
