<?php 
    namespace Oss\Model;

    // use Think\Model;

    class FileModel
    {
        protected $allowDocMaxSize   = 50*1024*1024; // 50M
        protected $allowAudioMaxSize = 30*1024*1024; // 30M
        protected $allowVideoMaxSize = 50*1024*1024; // 50M

        /**
         * 文件类型
         * @param  [String] $fileType [MIME]
         * @return [String]        
         */
        public function fileType($fileType)
        {
            switch ($fileType) {
                case 'image/jpg':
                case 'image/jpeg':
                case 'image/png':
                case 'image/gif':
                case 'image/bmp':
                    return 'images';
                    break;
                case 'application/msword':
                case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                    return 'doc/word'; 
                    break;
                case 'application/vnd.ms-excel':
                case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                case 'text/comma-separated-values': // csv
                case 'text/csv':
                case '.csv':
                    return 'doc/excel';
                    break;
                case 'application/vnd.ms-powerpoint':
                case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
                    return 'doc/ppt';
                    break;
                case 'application/pdf':
                    return 'doc/pdf';
                    break;
                case 'text/plain':
                    return 'doc/txt';
                    break;
                case 'application/zip':
                case 'application/x-zip-compressed':
                    return 'doc/zip';
                    break;
                case 'video/avi':
                case 'video/x-ms-wmv';
                case 'application/vnd.rn-realmedia-vbr':
                case 'application/octet-stream':
                    return 'video';
                    break;
                case 'audio/mpeg':
                case 'audio/mpeg3':
                case 'audio/x-mpeg-3':
                case 'audio/x-mpeg':
                case 'audio/mp3':
                    return 'audio/mp3';
                    break;
                case 'audio/x-wav':
                    return 'audio/wmv';
                    break;
                default: 
                    return false;
                    break;
            }
        }

    	/**
    	 * 检查文件大小
    	 * @param  [Integer] $size [文件大小]
    	 * @return [Boolen] 
    	 */
    	public function checkDocSize($size, $allow_max_size='')
    	{
            $max_size = $allow_max_size ? $allow_max_size * 1024 * 1024 : $this->allowDocMaxSize;

    		if ($size > $max_size) return false;

    		return true;
    	}

        // 检查视频大小
        public function checkVideoSize($size, $allow_max_size='')
        {
            $max_size = $allow_max_size ? $allow_max_size * 1024 * 1024 : $this->allowVideoMaxSize;

            if ($size > $max_size) return false;

            return true;
        }

        // 检查音频大小
        public function checkAudioSize($size, $allow_max_size='')
        {
            $max_size = $allow_max_size ? $allow_max_size * 1024 * 1024 : $this->allowAudioMaxSize;

            if ($size > $max_size) return false;

            return true;
        }
    }