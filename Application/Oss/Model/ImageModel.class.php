<?php 
    namespace Oss\Model;

    // use Think\Model;

    class ImageModel
    {
    	protected $allowExt = array('jpg', 'jpeg', 'png', 'gif', 'bmp');
    	protected $allowMaxSize = 10*1024*1024; // 10M

    	/**
    	 * 检查图片后缀名
    	 * @param  [String] $ext [图片后缀]
    	 * @return [Boolen]      
    	 */
    	public function checkImageExt($ext)
    	{
    		if (in_array(strtolower($ext), $this->allowExt)) {
    			return true;
    		} else {
    			return false;
    		}
    	}

    	/**
    	 * 检查图片大小
    	 * @param  [Integer] $size [图片大小]
    	 * @return [Boolen] 
    	 */
    	public function checkImageSize($size, $allow_max_size='')
    	{
            $max_size = $allow_max_size ? $allow_max_size * 1024 * 1024 : $this->allowMaxSize;

    		if ($size > $max_size) return false;

    		return true;
    	}

        // 图片水印处理
        public function imageWater($tmp_name, $file_name, $width, $height, $water_img) 
        {
            $image = new \Think\Image(); 

            $imageInfo = $image->open($tmp_name);

            $imageWidth  = $imageInfo->width();           
            $imageHeight = $imageInfo->height();    

            // 保存路径
            $savePath = './public/Uploads/'; 

            // 获取文件后缀
            $explode = explode('.', $file_name);
            $ext = $explode[count($explode)-1];   

            // 文件名
            $rename = uniqid('img_');
            $saveName = $savePath.$rename.'.'.$ext;   
       
            if ($imageWidth > $width && $imageHeight > $height) {
                $image->thumb($width, $height, \Think\Image::IMAGE_THUMB_SCALE)->water($water_img, \Think\Image::IMAGE_WATER_CENTER, 100)->save($saveName);

               return $saveName;
            } else {
                $image->thumb($width, $height, \Think\Image::IMAGE_THUMB_FILLED)->water($water_img, \Think\Image::IMAGE_WATER_CENTER, 100)->save($saveName);

                return $saveName;
            } 
        }
    }