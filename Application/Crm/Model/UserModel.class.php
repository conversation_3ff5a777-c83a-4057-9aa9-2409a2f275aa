<?php
namespace Crm\Model;
use Think\Model;

class UserModel extends Model
{
	protected $connection = 'CRM_DB_CONFIG';
    protected $tableName = 'user'; 

    // 获取用户绑定的业务员ID
    public function getSaleId($user_id, $org_id = 1)
    {
        return $this->where(['org_id' => $org_id, 'user_id' => $user_id])->getField('sale_id');
    }

    // 修改会员账号 - 手机
    public function updateAccount($user_id, $data)
    {
        $data['update_time'] = time();
        return self::where(['user_id' => $user_id])->save($data);
    }

    public function getInfoByUserId($user_id, $org_id = 1)
    {
        $map = [];
        $map['user_id'] = $user_id;
        $map['org_id'] = $org_id;

        return $this->where($map)->find();
    }

}
