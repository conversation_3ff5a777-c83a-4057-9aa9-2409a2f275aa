<?php
namespace Crm\Model;
use Think\Model;

class SignComsModel extends Model
{
	protected $connection = 'CRM_DB_CONFIG';
    protected $tableName = 'sign_coms'; 

    // 获取签约公司
    public function getInfoById($sign_com_id)
    {
        $map = [];
        $map['sign_com_id'] = $sign_com_id;
        $map['use_scope'] = ['in', [1, 3]]; // 1-销售，2-采购，3-全部

        return $this->where($map)->find();
    }

}
