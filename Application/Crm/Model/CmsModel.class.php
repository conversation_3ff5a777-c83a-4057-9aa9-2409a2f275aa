<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace Crm\Model;
use Think\Model;
/**
 * Description of CmsModel
 *
 * <AUTHOR>
 */
class CmsModel extends Model
{
    protected $autoCheckFields = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.CMS');
    }

    // 查找交易员ID
    public function getSaleId($name)
    {
        $data = array();
        $sale = explode(',', $name);

        foreach ($sale as $k=>$v) {
            $user = $this->table('user_info')->where(['name' => $v, 'status' => 0])->field('userId')->find();

            if (!empty($user['userid'])) {
                $data[] = $user['userid'];
            } 
        }

        return $data;
    }

    // 获取用户信息
    public function getUserInfo($userId)
    {
        return $this->table('user_info')->where(['userId' => $userId])->find();
    }

}
