<?php

namespace Crm\Model;

use Think\Model;

/**
 * Description of CmsModel
 *
 * <AUTHOR>
 */
class CrmModel extends Model
{
    public static $COM_INDUSTRY = [
        "消费电子",
        "医疗",
        "工业控制",
        "汽车电子",
        "新能源",
        "人工智能",
        "电力",
        "仪器仪表",
        "轨道交通",
        "OEM代工",
        "航空航天航海",
        "勘探",
        "通信",
    ];

    //公司规模
    public static $COMPANY_SCALES = [
        '100人以下',
        '100~300人',
        '300~500人',
        '500~1000人',
        '1000~3000人',
        '3000人以上'
    ];

    public static $COM_NATURE = [
        0=>'',
        1=>'KA终端',
        2=>'SMB终端',
        3=>'贸易商',
        4=>'科研院校',
        5=>'其他',
    ];


    CONST ERP_COM_NO = [
        1 => 'MY002', // 科技
        2 => 'MY001', // 深贸
        3 => 'MY004', // 华云
        9 => "MY006",//深圳市粤丰实工业有限公司
        10 => "MY005", //深圳工品数字科技有限公司
        11 => 'MY008', //泰国
        12 => 'MY007', //越南
    ];
    protected $autoCheckFields = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('CRM_DB_CONFIG');
    }

    // 新增erp用户
    public function insertErpUser($data)
    {
        return $this->table('lie_user')->add($data);
    }

    // 更新erp用户创建时间
    public function updateErpUser($user_id, $data)
    {
        return $this->table('lie_user')->where(['user_id' => $user_id])->save($data);
    }

    // 新增交易员
    public function insertSales($data)
    {
        $sales = $this->table('lie_salesman')->add($data);

        if ($sales === false) {
            return false;
        }

        return $sales;
    }

    // 新增公司信息
    public function insertCompany($data)
    {
        $company = $this->table('lie_user_company')->add($data);

        if ($company === false) {
            return false;
        }

        return $company;
    }

    // 新增发票信息
    public function insertInvoice($data)
    {
        $invoice = $this->table('lie_invoice')->add($data);

        if ($invoice === false) {
            return false;
        }

        return $invoice;
    }

    // 新增联系人信息
    public function insertLinkman($data)
    {
        $linkman = $this->table('lie_linkman')->add($data);

        if ($linkman === false) {
            return false;
        }

        return $linkman;
    }

    // 查找联系人
    public function getUserIdByLinkman($user_id, $linkman)
    {
        $linkmanInfo = $this->table('lie_linkman')->where([
            'user_id' => $user_id,
            'name' => $linkman['name']
        ])->field('link_id')->order('create_time desc')->find();

        if (empty($linkmanInfo)) {
            return false;
        }

        return $linkmanInfo['link_id'];
    }

    // 查找公司名称
    public function getUserIdByComname($com_name)
    {
        $company = $this->table('lie_user_company')->where(['com_name' => $com_name])->field('user_id')->order('create_time desc')->find();

        if (empty($company)) {
            return false;
        }

        return $company['user_id'];
    }

    // 修改公司名称
    public function updateComName($uid, $com_name)
    {
        $user_id = $this->table('lie_user')->where(['outter_uid' => $uid])->getField('user_id'); // 获取crm用户ID

        if (!$user_id) return false;

        $save['com_name'] = $com_name;
        $update = $this->table('lie_user_company')->where(['user_id' => $user_id])->save($save);

        return $update !== false ? true : false;
    }


    // 根据用户id获取crm跟进的客户信息
    public function getSaleIdCustomerId($userId)
    {
        $realUserId = $this->table('lie_user')->where(['outter_uid' => $userId])->getField('user_id');
        $sale = $this->table('lie_salesman')->where(['user_id' => $realUserId])->getField('sale_id');
        //找到客服id以后,还要去cms数据库找出对应的outter_id
        $cmsModel = D('Cms');
        $saleOutterId = $cmsModel->table('lie_intracode')->where(['admin_id' => $sale])->getField('user_id');
        return $saleOutterId ?: 0;
    }

    public function getCompanyAuth($where=[]){
        return $this->table('lie_company_auth')->where($where)->order('id desc')->find();
    }

    public function createCompanyAuth($data=[]){
        return $this->table('lie_company_auth')->add($data);
    }

    public function updateCompanyAuth($where,$data=[]){
        return $this->table('lie_company_auth')->where($where)->save($data);
    }

    public function getCompanyInfo($where=[]){
        return $this->table('lie_company')->where($where)->order('id desc')->find();
    }

    public function getCreditList($tax_id_arr){
        if (is_array($tax_id_arr) && count($tax_id_arr) > 0) {
            $tax_id_conditions = implode("','", $tax_id_arr);
            $tax_id_conditions = "tax_id IN ('". $tax_id_conditions ."')";
            return $this->table('lie_credit')->where($tax_id_conditions)->order('tax_id desc')->select();
        } else {
            return [];
        }
    }
    
    public function getCompanyErpComSn($com_name){
        return $this->table('lie_company')->where(["com_name"=>$com_name])->getfield('erp_com_sn');
    }


    public function  isExistsUserProtocol($tax_id){
        return $this->table('lie_protocol')->where(["tax_id"=>$tax_id,"audit_status"=>3,"fk_audit_status"=>3])->where("protocol_type",7)->count();
    }


}
