<?php
namespace Crm\Controller;

use Crm\Controller\BaseController;

class CrmController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();

        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), array('getcompanycredit'))) {
            // 检查登录
            $res = $this->checkLogin();
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }
    
    // 导入ERP用户
    public function erpUser()
    {
    	$cms = D('Cms');
    	$crm = D('Crm');

    	// 用户信息
    	$userInfo['source'] = 4;
        $userInfo['create_time'] = time();
    	$userInfo['copy_ctime'] = strtotime(I('fcreateTime', '')); // 用户在ERP创建时间

    	// 公司信息
    	$companyInfo['com_name'] = I('customer', '');
    	$companyInfo['address'] = I('addess', '');

    	// 发票信息
    	$invoiceInfo['tax_no'] = I('txRegisterNo', '');
    	$invoiceInfo['telephone'] = I('tel', '');
    	$invoiceInfo['bank_name'] = I('bank', '');
    	$invoiceInfo['bank_account'] = I('bankAccount', '');

    	// 联系人
    	$linkman['name'] = I('desMan', '');
    	$linkman['mobile'] = I('desPhone', '');
    	$linkman['fixed_tel'] = I('desTel', '');
    	$linkman['email'] = I('desEmail', '');

    	// 多联系人情况：根据公司名称查找user_id
    	$user_id = $crm->getUserIdByComname($companyInfo['com_name']); 

		if ($user_id !== false) {
			$res = $this->addRelationInfo($user_id, $userInfo, array(), array(), array(), $linkman);

			if ($res['err_code'] != 0) {
				return $this->apiReturn($res['err_code'], $res['err_msg']);
			}
		} else {
			$salesMan = I('salesMan', ''); // 交易员
	    	$sale_id = $cms->getSaleId($salesMan);

			$res = $this->addRelationInfo('', $userInfo, $sale_id, $companyInfo, $invoiceInfo, $linkman);

			if ($res['err_code'] != 0) {
				return $this->apiReturn($res['err_code'], $res['err_msg']);
			}	
		}  	

    	return $this->apiReturn(0, '导入成功', '0000');
    }

    // 根据user_id添加相关信息
    public function addRelationInfo($user_id, $userInfo, $sale_id=array(), $company=array(), $invoice=array(), $linkman=array())
    {
    	$crm = D('Crm');
    	$crm->startTrans();

        if ($user_id) {
            $data['copy_ctime'] = $userInfo['copy_ctime'];
            $update = $crm->updateErpUser($user_id, $data); // 保存用户创建时间

            if ($update === false) {
                $crm->rollback();
                return $this->apiReturn(-1, '保存用户创建时间失败', '4444');
            }
        } else {
            $user_id = $crm->insertErpUser($userInfo);
        }

    	if (!empty($sale_id)) { // 分配多人
    		$sales['user_id'] = $user_id;

    		foreach ($sale_id as $k=>$v) {
                $sales['sale_id'] = $v;
    			$sales['assign_time'] = time();
    			$saleRes = $crm->insertSales($sales);

		    	if ($saleRes === false) {
		    		$crm->rollback();
		    		return $this->apiReturn(-1, '导入交易员失败', '4444');
		    	}
    		}
    	} 

    	if (!empty($company)) {
    		$company['user_id'] = $user_id;
	    	$company['create_time'] = time();
	    	$comRes = $crm->insertCompany($company);

	    	if ($comRes === false) {
	    		$crm->rollback();
	    		return $this->apiReturn(-1, '导入公司信息失败', '4444');
	    	}
    	}
    	
    	if (!empty($invoice)) {
	    	$invoice['user_id'] = $user_id;
	    	$invoice['create_time'] = time();
	    	$invRes = $crm->insertInvoice($invoice);

	    	if ($invRes === false) {
	    		$crm->rollback();
	    		return $this->apiReturn(-1, '导入发票信息失败', '4444');
	    	}
	    }

        $linkmanInfo = $crm->getUserIdByLinkman($user_id, $linkman);

        // 如果联系人不存在则添加
        if (!$linkmanInfo) {
            if (!empty($linkman)) {
                $linkman['user_id'] = $user_id;
                $linkman['create_time'] = time();
                $linkRes = $crm->insertLinkman($linkman);

                if ($linkRes === false) {
                    $crm->rollback();
                    return $this->apiReturn(-1, '导入联系人失败', '4444');
                }
            }
        }    

    	$crm->commit();

    	return $this->apiReturn(0, '成功');
    }

    // 修改公司名称
    public function updateCrmComName()
    {
        $uid = I('request.uid', 0, 'intval');
        $com_name = I('request.com_name', '');

        if (!$uid || !$com_name) return $this->apiReturn(20101, '参数缺失');

        $crm = D('Crm');
        $res = $crm->updateComName($uid, $com_name);

        if ($res == false) return $this->apiReturn(20102, '更新crm公司名称失败');

        return $this->apiReturn(0, '成功');
    }

    //提醒发货(钉钉消息)
    public function remindDelivery()
    {
        $orderSn = I('order_sn');
        $userId = I('user_id');
//        //判断发送间隔
//        $checkKey = "remind_delivery_" . $userId . '_' . $orderSn;
//        if (S($checkKey)) {
//            return  $this->apiReturn(0, '提醒成功!我们将尽快为您安排发货');
//        }else{
//            S($checkKey, '1', 60);
//        }
        if (empty($userId)) {
            return $this->apiReturn(20101, "客户用户id不能为空");
        }
        
        // $crm = D('Crm');
        // //根据user_id去crm获取对应客服的user_id
        // $saleId = $crm->getSaleIdCustomerId($userId);

        $UserModel = D('User');
        $saleId = $UserModel->getSaleId($userId);

        if (!empty($saleId)) {
            //获取userId对应的账号和订单号
            $userModel = D('Home/UserMain');
            $userInfo = $userModel->getUserInfo($userId);
            if (!empty($userInfo)) {
                $account = $userInfo['mobile'];
                $data['data']['account'] = $account;
                $data['data']['order_sn'] = $orderSn;
                $res = sendMsg('order_remind_delivery',$data,[$saleId]);
                if ($res['err_code'] != 0) {
                    return $this->apiReturn(20104, "发送提醒失败,请联系客服", $res);
                }
                return  $this->apiReturn(0, '提醒成功!我们将尽快为您安排发货~');
            }else{
                return $this->apiReturn(20103, "该用户信息不存在");
            }
        }else{
            return  $this->apiReturn(0, '提醒成功!我们将尽快为您安排发货~');
        }
    }

    /**
     * 用户ID
     * @return [type] [description]
     */
    public function getCartUid($uid = null)
    {
        if (is_null($uid)) {
            if ($this->auth()) {
                $uid = I('request.uid', 0, 'intval');
            }

            if (empty($uid)) {
                $uid = cookie('uid');
            }
        }
        if (!$this->auth() && $this->login['err_code'] != 0) {//弱登录态下购物车数据不属于当前用户账号的
            $uid = 0;
        }
        return $uid;
    }

    // 获取用户绑定的客服QQ
    public function getSalesQQ()
    {
        $user_id = $this->getCartUid();

        if (!$user_id) return $this->apiReturn(20101, '用户ID不存在');

        $UserModel = D('User');
        $sale_id = $UserModel->getSaleId($user_id);

        if (!$sale_id) return $this->apiReturn(20105, '用户无绑定客服');

        $CmsModel = D('Cms');
        $user_info = $CmsModel->getUserInfo($sale_id);

        return $this->apiReturn(0, '成功', $user_info['qq']);
    }
    
    // 获取ERP公司账期信息
    public function getCompanyCredit()
    {
        $com_name = I('com_name', '');

        if (!$com_name) return $this->apiReturn(20101, '参数缺失');

        $res = A('Server/Consume')->getErpCompanyCredit(['CUSTOMER' => $com_name]);

        if ($res === false) return $this->apiReturn(20130, '未获取到公司账期信息');

        return $this->apiReturn(0, '', $res);
    }

}
