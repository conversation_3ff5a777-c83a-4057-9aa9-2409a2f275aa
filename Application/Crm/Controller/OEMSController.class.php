<?php
namespace Crm\Controller;

use Crm\Controller\BaseController;
use Crm\Model\UserModel;
use Help\Model\FeedbackModel;
use Illuminate\Container\Container;
use Illuminate\Database\Capsule\Manager as DB;
use Crm\Model\OemsUserInfoModel;

class OEMSController extends BaseController
{
	public  static  $DB;
    public function _initialize()
    {
        parent::_initialize();
		$container = new Container();
		//普通绑定
//        $container->bind('\Pcb\Model\LARAVELDB',null);
		//单例绑定
		$container->singleton('\Crm\Model\LaravelDb',function($container){
			return new \Crm\Model\LaravelDb($container);
		});
		//注册laravel db数据库驱动
		static::$DB = LaravelApp('\Crm\Model\LaravelDb');
    }


	/**
	 * oems用户
	 * 信息录入crm
	 */
	public function addOEMSUserinfo(){
		try{
			$data['contact'] = I("contact",'');
			$data['contact_name'] = I("contact_name",'');
			$data['company_name'] = I("company_name",'');
			$data['company_type'] = I("company_type",0);
			$data['com_type_info'] = I("com_type_info",'');
			$data['note'] = I("note",'');
			$data['create_time'] = time();
			DB::connection('CRM')->transaction(function()use($data){


				//如果用户存在 并且 名称为空 则更新
				$crmUserInfo = DB::connection('CRM')->table("user")->where("outter_uid",intval(cookie("uid")))->select("user_id","name","channel_source")->first();
				if($crmUserInfo){
					$crmUserInfoUpdateData = [];
					if(!$crmUserInfo->name)
						$crmUserInfoUpdateData['name'] = $data['contact_name'];
					if($crmUserInfo->channel_source <= 0)
						$crmUserInfoUpdateData['channel_source'] = 8;
					if(!empty($crmUserInfoUpdateData))
						DB::connection('CRM')->table("user")->where("outter_uid",intval(cookie("uid")))->update($crmUserInfoUpdateData);

				}

				$this->addOemsToFeedback($data,$crmUserInfo);

				//如果公司存在 并且公司名为空
				$companInfo = DB::connection('CRM')->table("user_company")->where("user_id",$crmUserInfo->user_id)->select("com_name","com_type")->first();
				if($companInfo && $data['company_name'] && !$companInfo->com_name){
					DB::connection('CRM')->table("user_company")->where("user_id",$crmUserInfo->user_id)->update(['com_name'=>$data['company_name']]);
				}
				//如果公司类型为空则修改 否则 略过
				if($companInfo && $companInfo->com_type <= 0 ){
					$userComData['com_type'] = intval($data['company_type']);
					if($data['company_type'] == 5)
						$userComData['com_type_other'] = $data['com_type_info'] ? $data['com_type_info'] : '';
					DB::connection('CRM')->table("user_company")->where("user_id",$crmUserInfo->user_id)->update($userComData);
				}

				if($crmUserInfo && !$companInfo){
					DB::connection('CRM')->table("user_company")->insert([
						'user_id'=>$crmUserInfo->user_id,
						'com_name'=> isset($data['company_name']) ? $data['company_name'] : '',
						'com_type'=> isset($data['company_type']) ? $data['company_type'] : 5,
						'com_type_other'=> isset($data['com_type_info']) ? $data['com_type_info'] : '',
					]);
				}


			});

			return $this->apiReturn("0","录入信息成功");

		}catch(\Crm\Exception\CrmException $e){
			return $this->apiReturn("21002",$e->getMessage());
		}catch(\Exception $e){
//			dump($e->getMessage());
			return $this->apiReturn("21001","录入信息失败");
		}
	}


    //添加oems信息到feedback中去
    public function addOemsToFeedback($data,$crmUserInfo)
    {
        if (empty($crmUserInfo)){
            $userId = 0;
        }else{
            $userId = $crmUserInfo->user_id;
        }

        $bk = OemsUserInfoModel::updateOrCreate([
            "outter_uid"=>cookie("uid"),
        ],$data);
        $insertValue['contact_name'] = $data['contact_name'];
        $insertValue['company_name'] = $data['company_name'];
        $insertValue['company_type'] = $data['company_type'];
        $insertValue['content'] = $data['com_type_info'];
        $insertValue['note'] = $data['note'];
        $insertValue['user_id'] = cookie("uid");
        $insertValue['outter_uid'] = $userId;
        $insertValue['type'] = 9;
        $insertValue['create_time'] = time();
        $insertId = DB::connection('CRM')->table("feedback")->insertGetId($insertValue);
        if (!empty($userId) && $insertId){
            DB::connection('CRM')->table('user')->where('user_id',$userId)->update(['fkid'=>$insertId]);
        }

        if($bk === false) throw new \Crm\Exception\CrmException("录入信息失败");
    }


    


}
