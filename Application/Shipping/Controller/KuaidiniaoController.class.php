<?php
namespace Shipping\Controller;

use Shipping\Controller\BaseController;

class <PERSON><PERSON>iniaoController extends BaseController
{
    private $shippingCode = array(
        '1' => 'SF',
        '3' => 'sblx',
        '4' => 'kdi',
        '5' => 'KYWL',
        '6' => 'KYWL',
        '7' => 'FEDEX_GJ',
    );

    /**
     * 获取快递信息表中的信息
     * @param $shippingNo
     * @param $shippingId
     * @return array|bool|mixed
     */
    public function getInfo($shippingNo, $shippingId)
    {
        $shippingCode = $this->shippingCode[$shippingId];
        //无快递码
        if(!$shippingCode){
            return false;
        }
        //获取信息
        $response = $this->getShipperInfo($shippingNo, $shippingCode);
        if($response['Traces']){
            $data['info'] = json_encode($response['Traces']);
            switch($response['State']){
                case 2: //配送中
                    $data['status'] = 1;
                    break;
                case 3: //已签收
                    $data['status'] = 2;
                    break;
                case 4: //问题件
                    $data['status'] = 1;
                    break;
                default: //异常
                    $data['status'] = 1;
                    break;
            }
            return $data;
        } else {
            return false;
        }
    }

    /**
     * 实时获取快递信息
     * @param $LogisticCode
     * @param $ShipperCode
     * @param string $sort
     * @return bool|mixed
     */
    private function getShipperInfo($LogisticCode, $ShipperCode, $sort = 'desc'){
        $info = C('KD_CONFIG');
        if(!$LogisticCode  || !$ShipperCode){
            return false;
        }

        $info['LogisticCode'] = $LogisticCode;
        $info['ShipperCode'] = $ShipperCode;
        $result = json_decode($this->getOrderTracesByJson($info), true);

        if($result['Success'] === true) {
            if($sort == 'desc')  {
                krsort($result['Traces']);
            }
            return $result;
        }else {
            return false;
        }
    }

    /**
     * Json方式 查询订单物流轨迹
     * @param $info
     * @return \url响应返回的html
     */
    private function getOrderTracesByJson($info){
        $json_data = array(
            'OrderCode' => '',
            'ShipperCode' => $info['ShipperCode'],
            'LogisticCode' => $info['LogisticCode'], 
        );
        $requestData = json_encode($json_data);

        $datas = array(
            'EBusinessID' => $info['EBusinessID'],
            'RequestType' => '1002',
            'RequestData' => urlencode($requestData) ,
            'DataType' => '2',
        );
        $datas['DataSign'] = $this->encrypt($requestData, $info['AppKey']);
        $result = $this->sendPost($info['ReqURL'], $datas);

        //根据公司业务处理返回的信息......

        return $result;
    }

    /**
     * post提交数据
     * @param $url
     * @param $datas
     * @return string
     */
    private function sendPost($url, $datas) {
        $temps = array();
        foreach ($datas as $key => $value) {
            $temps[] = sprintf('%s=%s', $key, $value);
        }
        $post_data = implode('&', $temps);
        $url_info = parse_url($url);
        if($url_info['port']=='')
        {
            $url_info['port']=80;
        }
        $httpheader = "POST " . $url_info['path'] . " HTTP/1.0\r\n";
        $httpheader.= "Host:" . $url_info['host'] . "\r\n";
        $httpheader.= "Content-Type:application/x-www-form-urlencoded\r\n";
        $httpheader.= "Content-Length:" . strlen($post_data) . "\r\n";
        $httpheader.= "Connection:close\r\n\r\n";
        $httpheader.= $post_data;
        $fd = fsockopen($url_info['host'], $url_info['port']);
        fwrite($fd, $httpheader);
        $gets = "";
        $headerFlag = true;
        while (!feof($fd)) {
            if (($header = @fgets($fd)) && ($header == "\r\n" || $header == "\n")) {
                break;
            }
        }
        while (!feof($fd)) {
            $gets.= fread($fd, 128);
        }
        fclose($fd);

        return $gets;
    }

    /**
     * 电商Sign签名生成
     * @param $data
     * @param $appkey
     * @return string
     */
    private function encrypt($data, $appkey) {
        return urlencode(base64_encode(md5($data.$appkey)));
    }
}
