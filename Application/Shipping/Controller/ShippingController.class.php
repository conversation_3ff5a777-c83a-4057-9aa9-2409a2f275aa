<?php
namespace Shipping\Controller;

use Shipping\Controller\BaseController;

class ShippingController extends BaseController
{
    const KD100 = 0;
    const KDN = 1;

    public function info()
    {
        $shipping_no = I('request.shipping_no', '');
        $shipping_id = I('request.shipping_id', 0, 'intval');
        $phone = I('request.phone', '');//顺丰需要收寄件人手机
        $type = I('request.type', 0, 'intval');//默认快递100
        switch ($type) {
            case self::KD100:
                $info = A('Shipping/Kuaidi100')->getInfo($shipping_no, $shipping_id, $phone);//767291525519顺丰测试单
                break;
            case self::KDN:
                $info = A('Shipping/Kuaidiniao')->getInfo($shipping_no, $shipping_id);
            default:
                break;
        }
        return $this->apiReturn(0, '获取成功', $info);
    }

    /**
     * 获取所有快递公司列表
     * @return [type] [description]
     */
    public function allShipping()
    {
        $ShippingModel = D('Shipping');
        $data = $ShippingModel->getListMap();
        return $this->apiReturn(0, '获取成功', $data);
    }

    /**
     * 获取快递公司ID
     */
    public function getShippingId()
    {
        $shipping_name = I('shipping_name', '');
        $ShippingModel = D('Shipping');
        $data = $ShippingModel->getNameId($shipping_name);
        return $this->apiReturn(0, '获取成功', $data);
    }

    /**
     * 创建新物流公司
     * @return [type] [description]
     */
    public function create()
    {
        $shipping_name = I('shipping_name', '', 'trim');
        $ShippingModel = D('Shipping');
        $shipping_id = $ShippingModel->getNameId($shipping_name);
        if (empty($shipping_id)) {
            $data = array(
                'shipping_name' => $shipping_name,
            );
            $shipping_id = $ShippingModel->add($data);
            if ($shipping_id === false) {
                return $this->apiReturn(1, '创建新物流公司失败');
            }

            $Rbmq = D('Common/Rbmq');
            $data = array(
                'type' => 'shipping.syn',
                'from' => 'oms',
                'to' => 'wms',
                'Timestamp' => time(),
                'data' => array(
                    'shipping_id' => $shipping_id,
                    'shipping_code' => '',
                    'shipping_name' => $shipping_name,
                ),
            );
            $queue = C('SERVICE_QUEUE_NAME');
            $res = $Rbmq->connect('WMS_RBMQ_CONFIG')->queue($queue)->push($data, $queue);
        }
        return $this->apiReturn(0, '操作成功');
    }

    /**
     * 推送所有物流公司
     * @return [type] [description]
     */
    public function pushAll()
    {
        $ShippingModel = D('Shipping');
        $entity = $ShippingModel->field('shipping_id, shipping_name')->select();
        $Rbmq = D('Common/Rbmq');
        $queue = C('SERVICE_QUEUE_NAME');
        foreach ($entity as $v) {
            $data = array(
                'type' => 'shipping.syn',
                'from' => 'oms',
                'to' => 'wms',
                'Timestamp' => time(),
                'data' => $v
            );
            $res = $Rbmq->connect('WMS_RBMQ_CONFIG')->queue($queue)->push($data, $queue);
        }
        return $this->apiReturn(0, '操作成功');
    }
}
