<?php
namespace Shipping\Controller;

use Shipping\Controller\BaseController;

class Kuaidi100Controller extends BaseController
{
    private $shippingCode = array(
        '1' => 'shunfeng',
        '3' => 'ups',
        '5' => 'yunda',
        '6' => 'kuayue',
        '7' => 'fedexcn',
        '9' => 'jd',
        '12' => 'shunfeng',
        '100' => 'ems'
    );

    /**
     * 获取快递信息表中的信息
     * @param $shippingNo
     * @param $shippingId
     * @return array|bool|mixed
     */
    public function getInfo($shippingNo, $shippingId = '', $phone = '')
    {
        $shippingCode = $this->shippingCode[$shippingId];
        //无快递码
        if(!$shippingCode){
            $shippingCode = $this->autoNumber($shippingNo);
            if (is_array($shippingCode)) {
                $shippingCode = current($shippingCode);
            }
        }
        $extend = array(
            'phone' => $phone,
        );
        //获取信息
        $response = $this->getShipperInfo($shippingNo, $shippingCode, 'desc', $extend);
        if($response['data']){
            $data['info'] = json_encode($response['data']);
            switch($response['state']){
                case 0: //在途中
                case 1: //已揽收
                case 2: //问题件
                case 5: //派件中
                case 6: //退回中
                    $data['status'] = 1;
                    break;
                case 3: //已签收
                case 4: //已退签
                case 14: //已拒签
                    $data['status'] = 2;
                    break;
                default: //异常
                    $data['status'] = 1;
                    break;
            }
            return $data;
        } else {
            return false;
        }
    }

    /**
     * 实时获取快递信息
     * @param $LogisticCode
     * @param $ShipperCode
     * @param string $sort
     * @return bool|mixed
     */
    private function getShipperInfo($LogisticCode, $ShipperCode = '', $sort = 'desc', $extend = array()){
        if(!$LogisticCode){
            return false;
        }
        $info = C('KD100_CONFIG');
        $info['num'] = $LogisticCode;
        $info['com'] = $ShipperCode;
        if ($ShipperCode == 'shunfeng') {//顺丰需要传收寄件人手机号
            $info['phone'] = $extend['phone'];
        }
        $result = json_decode($this->getOrderTracesByJson($info), true);
        if($result['message'] === 'ok') {
            foreach ($result['data'] as &$v) {
                //统一变量名
                $item = array(
                    'AcceptTime' => $v['time'],
                    'AcceptStation' => $v['context'],
                );
                $v = $item;
            }
            if($sort != 'desc')  {
                krsort($result['data']);
            }
            return $result;
        }else {
            return false;
        }
    }

    /**
     * Json方式 查询订单物流轨迹
     * @param $info
     * @return \url响应返回的html
     */
    private function getOrderTracesByJson($info){
        $json_data = array(
            'com' => $info['com'],
            'num' => $info['num'],
            'phone' => $info['phone'],
        );
        $requestData = json_encode($json_data);
        $datas = array(
            'customer' => $info['customer'],
            'param' => $requestData,
            'sign' => strtoupper($this->encrypt($requestData, $info['appKey'], $info['customer'])),
        );

        $result = post_curl($info['reqUrl'], $datas);
        return $result;
    }

    /**
     * 智能获取物流公司
     * @param  [type] $shippingNo [description]
     * @return [type]             [description]
     */
    public function autoNumber($shippingNo)
    {
        $config = C('KD100_CONFIG');
        $datas = array(
            'num' => $shippingNo,
            'key' => $config['appKey'],
        );
        $info = get_curl($config['autoUrl'], $datas);
        $list = json_decode($info, true);
        if (empty($list)) {
            return false;
        }
        $result = array_column($list, 'comCode');
        if (count($result) == 1) {
            $result = $result[0];
        }
        return $result;
    }

    /**
     * 电商Sign签名生成
     * @param $data
     * @param $appkey
     * @return string
     */
    private function encrypt($data, $appkey, $customer) {
        return md5($data.$appkey.$customer);
    }
}
