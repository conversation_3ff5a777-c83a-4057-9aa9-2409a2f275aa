<?php
namespace Shipping\Model;

use Think\Model;

class ShippingModel extends Model
{
    /**
     * 获取键值映射
     * @return [type] [description]
     */
    public function getListMap()
    {
        static $data;
        if (empty($data)) {
            $map = array(
                'is_enabled' => 1,
            );
            $data = $this->where($map)->getField('shipping_id, shipping_name');
        }
        return $data;
    }

    public function getInfo($id)
    {
        $data = $this->find($id);
        return $data;
    }

    public function getNameId($name)
    {
        $map = array(
            'shipping_name' => $name,
        );
        $data = $this->where($map)->getField('shipping_id');
        return $data;
    }
}