<?php

namespace Home\Controller;

use Home\Controller\BaseController;
use Home\Model\BehaviorLogModel;

class BehaviorController extends BaseController
{

    public function _initialize()
    {
        parent::_initialize();
        if (!$this->auth()) {
            //检查登录
            $res = $this->checkLogin(false);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    const VIEW_GOODS_DETAIL_BEHAVIOR_TYPE = 12;

    public function get_view_goods_history()
    {
        $userId = cookie('uid');
        $pageSize = I('page_size', 10);
        $page = I('page', 1);
        $model = new BehaviorLogModel();
        $type = self::VIEW_GOODS_DETAIL_BEHAVIOR_TYPE;
        $logList = $model->getBehaviorLogList($userId, $type, $pageSize, $page);
        $logList = $model->transformViewGoodsBehavior($logList);
        return $this->apiReturn(0, 'ok', [['data'=>$logList]]);
    }
}
