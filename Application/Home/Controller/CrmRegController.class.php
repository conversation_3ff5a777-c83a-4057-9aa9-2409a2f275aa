<?php
namespace Home\Controller;

use Home\Controller\BaseController;

class CrmRegController extends BaseController
{
    public function test()
    {
        // p(111);
    }

    // CRM用户注册
    public function regUser()
    {
        $mobile        = I('request.mobile', '');
        $email         = I('request.email', '');
        $intl_code     = I('request.intl_code', '');
        $password      = I('request.password', '');
        $user_name     = I('request.user_name', '');
        $account_type  = I('request.account_type', 1);
        $is_sendmsg    = I('request.is_sendmsg', 1);
        $qq            = I('request.qq', '');
        $user_position = I('request.user_position', 0);
        $user_nature   = I('request.user_nature', 0);
        $sale_id       = I('request.sale_id', 0);
        $channel_type  = I('request.channel_type', 1);
        $org_id        = I('request.org_id', 1); // 组织ID，1-猎芯，2-华云

        if (!$mobile && !$email) return $this->apiReturn(12040, '账号不能为空');
        if (!$sale_id) return $this->apiReturn(12041, '后台操作人不能为空');

        if ($org_id == 1) { // 猎芯注册
            $UserMainModel    = D('UserMain');
            $UserInfoModel    = D('UserInfo');
            $UserCompanyModel = D('UserCompany');

            // 判断账号是否存在
            $reg_type = !empty($mobile) ? 1 : 2;

            switch ($reg_type) {
                case 1:
                    $mibile = trim($mobile);
                    $exists_uid = $UserMainModel->where(["mobile" => $mibile])->getField("user_id");
                    if (!empty($exists_uid)) {
                        return $this->apiReturn(0, sprintf("手机号%s已经存在", $mibile), $exists_uid);
                    }
                    break;
                case 2:
                    $email = trim($email);
                    $exists_uid = $UserMainModel->where(["email" => $email])->getField("user_id");
                    if (!empty($exists_uid)) {
                        return $this->apiReturn(0, sprintf("邮箱%s已经存在", $email), $exists_uid);
                    }
                    break;
            }

            // 会员信息
            $userInfo = [
                'account'      => $reg_type == 1 ? $mobile : $email,
                'password'     => !empty($password) ? $password : '123456',
                'intl_code'    => $intl_code,
                'user_name'    => $user_name,
                'is_test'      => $account_type == 2 ? 1 : 0,
                'is_sendmsg'   => $is_sendmsg,
                'sale_id'      => $sale_id,
                'channel_type' => $channel_type, // 1线上用户，2线下用户，3大客户部，4苏州部
                'org_id'       => $org_id,
            ];

            // 新增用户
            $UserMainModel->startTrans();

            $user_id = $UserMainModel->regSuccess($reg_type, $userInfo, true);

            if (!$user_id) {
                $UserMainModel->rollback();
                return $this->apiReturn(12044, '新增用户失败');
            }

            // 用户扩展表
            // crm.user_position: 1-工程师，2-采购，3-销售，4-企业管理层，5-贸易商，6-终端商，7-学生，8-其他
            // work_function: 1=工程师，2=采购，3=企业管理，4=学生，5=其他，6=销售，7=贸易商，8-终端商
            $work_function_mapping = C('work_function_mapping');

            $addUserInfo['user_id']       = $user_id;
            $addUserInfo['work_function'] = isset($work_function_mapping[$user_position]) ? $work_function_mapping[$user_position] : 0;
            $addUserInfo['qq']            = $qq;
            $addUserInfo['sale_id']       = $sale_id;

            $res = $UserInfoModel->where(['user_id' => $user_id])->save($addUserInfo);

            if ($res === false) {
                $UserMainModel->rollback();
                return $this->apiReturn(12045, '更新用户扩展信息失败');
            }

            // 公司信息
            $companyInfo = [
                'user_id' => $user_id,
                'type_id' => isset($user_nature) ? $user_nature : '',
            ];

            $company_id = $UserCompanyModel->data($companyInfo)->add();

            if ($company_id === false) {
                $UserMainModel->rollback();
                return $this->apiReturn(12046, '新增用户公司信息失败');
            }

            // 回写company_id 到 用户表
            $UserMainModel->where(['user_id' => $user_id])->data(['company_id' => $company_id])->save();

            $UserMainModel->commit();

            // 添加缓存信息
            S_company($user_id, $companyInfo);

            $user_info = S_user($user_id);
            $user_info['company_id'] = $company_id;

            S_user($user_id, $user_info);
        } else {
            // 华云提供注册接口
            $user_id = 0;

        }
        
        return $this->apiReturn(0, '新增成功', $user_id);
    }

    // 编辑用户
    public function editUser()
    {
        $user_id       = I('request.user_id', 0);
        $mobile        = I('request.mobile', '');
        $email         = I('request.email', '');
        $intl_code     = I('request.intl_code', '');
        $user_name     = I('request.user_name', '');
        $account_type  = I('request.account_type', 1);
        $is_sendmsg    = I('request.is_sendmsg', 1);
        $qq            = I('request.qq', '');
        $user_position = I('request.user_position', 0);
        $user_nature   = I('request.user_nature', 0);
        $sale_id       = I('request.sale_id', 0);
        $org_id        = I('request.org_id', 1); // 组织ID，1-猎芯，2-华云

        if (!$user_id) return $this->apiReturn(12050, '用户ID不存在');
        if (!$sale_id) return $this->apiReturn(12041, '后台操作人不能为空');

        if ($org_id == 1) {
            $UserMainModel      = D('UserMain');
            $UserInfoModel      = D('UserInfo');
            $UserCompanyModel   = D('UserCompany');
            $UserActionLogModel = D('UserActionLog');

            $exists_user_id = $UserMainModel->where(['user_id' => $user_id])->getField('user_id');
            if (!$exists_user_id) return $this->apiReturn(12051, '用户不存在');

            $UserMainModel->startTrans();

            // 用户表
            $userInfo = [
                'mobile'     => $mobile,
                'email'      => $email,
                'intl_code'  => $intl_code,
                'user_name'  => $user_name,
                'is_test'    => $account_type == 2 ? 1 : 0,
                'is_sendmsg' => $is_sendmsg,
                // 'sale_id' => $sale_id,
            ];

            $res = $UserMainModel->where(['user_id' => $user_id])->save($userInfo);

            if ($res === false) {
                $UserMainModel->rollback();
                return $this->apiReturn(12052, '更新用户信息失败');
            }

            // 用户扩展表
            $work_function_mapping = C('work_function_mapping');

            $addUserInfo['user_id']       = $user_id;
            $addUserInfo['work_function'] = isset($work_function_mapping[$user_position]) ? $work_function_mapping[$user_position] : 0;
            $addUserInfo['qq']            = $qq;
            // $addUserInfo['sale_id']       = $sale_id;

            $res = $UserInfoModel->where(['user_id' => $user_id])->save($addUserInfo);

            if ($res === false) {
                $UserMainModel->rollback();
                return $this->apiReturn(12045, '更新用户扩展信息失败');
            }

            // 公司信息
            $companyInfo = [
                'type_id' => isset($user_nature) ? $user_nature : '',
            ];

            $res = $UserCompanyModel->where(['user_id' => $user_id])->save($companyInfo);

            if ($res === false) {
                $UserMainModel->rollback();
                return $this->apiReturn(12053, '更新用户公司信息失败');
            }

            $UserActionLogModel->addLog($user_id, $sale_id, 2, '从CRM更新用户信息');

            $UserMainModel->commit();

            // 更新缓存信息
            $user_info = S_user($user_id);
            S_user($user_id, $user_info);
        } else {
            // 华云提供更新接口

        }
        
        return $this->apiReturn(0, '更新成功');
    }


}