<?php

namespace Home\Controller;

use Home\Services\FrqService;
use Home\Model\BehaviorLogModel;
use Home\Services\InquiryService;
use Illuminate\Support\Facades\DB;
use Home\Controller\BaseController;

class InquiryController extends BaseController
{

    public function _initialize()
    {
        parent::_initialize();
        if (!$this->auth() && !in_array(ACTION_NAME, [
                'test',
            ])) {
            //检查登录
            $res = $this->checkLogin(false);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    public function test()
    {

        (new InquiryService())->syncInquiryToFrq(112);
        //(new InquiryService())->syncInquiryToFrq(107);
    }

    //新增到询价池
    public function addInquiryPool()
    {
        $userId = cookie('uid') ?: 0;
        $goodsId = I('goods_id', 0);
        $goodsNumber = I('goods_number', 0);
        $deliveryTime = I('delivery_time', 0);
        $deliveryPlace = I('delivery_place', '');
        $targetPrice = I('target_price', 0);
        $batch = I('batch', '');
        $specialRequest = I('special_request', '');
        $inquiryPoolModel = D('Home/InquiryPool');
        if ($goodsId) {
            $redis = \fs_redis_init();
            $sku = $redis->hget('sku', $goodsId);
            if (!empty($sku) && !is_array($sku)) {
                $sku = json_decode($sku, true);
            }
        }
        $goodsNumber = $goodsId ? $sku['moq'] : $goodsNumber;
        $goodsNumber = $goodsNumber ? $goodsNumber : 0;
        $inquiryPoolModel->add([
            'user_id' => $userId,
            'goods_id' => $goodsId,
            'goods_number' => $goodsNumber,
            'delivery_time' => $deliveryTime,
            'delivery_place' => $deliveryPlace,
            'target_price' => $targetPrice,
            'batch' => $batch,
            'special_request' => $specialRequest,
            'create_time' => time(),
        ]);

        return $this->apiReturn(0, '新增询价池成功');
    }

    //获取询价池列表
    public function getInquiryPoolList()
    {
        $user_id = cookie('uid') ?: 0;
        $delivery_place = I('delivery_place', 1);
        $inquiryPoolModel = D('Home/InquiryPool');
        $where = ['user_id' => $user_id];
        if ($delivery_place) {
            $where['delivery_place'] = $delivery_place;
        }
        $inquiryPoolList = $inquiryPoolModel->where($where)->select();
        $inquiryPoolCount = $inquiryPoolModel->where(['user_id' => $user_id])->count();
        $cnCount = $inquiryPoolModel->where(['user_id' => $user_id, 'delivery_place' => 1])->count();
        $hkCount = $inquiryPoolModel->where(['user_id' => $user_id, 'delivery_place' => 2])->count();
        $limit = I('limit', 10);
        $page = I('p', 1);
        $total_page = ceil($inquiryPoolCount / $limit);
        $goodsIds = array_column($inquiryPoolList, 'goods_id');
        $goodsIds = implode(',', array_unique($goodsIds));
        $result = post_curl(NEW_GOODS_DOMAIN . '/synchronization', ['goods_id' => $goodsIds]);
        $result = json_decode($result, true);
        if (isset($result['errcode']) && $result['errcode'] == 0) {
            $skuList = $result['data'];
        }
        $redis = \fs_redis_init();
        foreach ($inquiryPoolList as $key => &$inquiry) {
            $inquiry['inquiry_pool_id'] = $inquiry['id'];
            $goodsInfo = array_get($skuList, $inquiry['goods_id']);
            $inquiry['goods_name'] = $goodsInfo['goods_name'];
            $inquiry['brand_name'] = $goodsInfo['brand_name'];
            $inquiry['standard_brand_name'] = isset($goodsInfo['standard_brand']) ? array_get($goodsInfo['standard_brand'], 'brand_name') : '';
            $inquiry['standard_brand_id'] = isset($goodsInfo['standard_brand']) ? array_get($goodsInfo['standard_brand'], 'standard_brand_id') : '';
            $inquiry['brand_id'] = isset($goodsInfo['brand_id']) ? $goodsInfo['brand_id'] : '';
            $inquiry['brand_name'] = array_get($goodsInfo, 'brand_name');
            $inquiry['supplier_id'] = array_get($goodsInfo, 'supplier_id');
            $inquiry['supplier_name'] = $redis->hget('supplier', $inquiry['supplier_id']) ?: '';
        }
        unset($inquiry);

        $inquiryPoolList = [
            'p' => $page,
            'total_page' => $total_page,
            'limit' => $limit,
            'count' => $inquiryPoolCount,
            'list' => $inquiryPoolList,
            'cn_count' => $cnCount,
            'hk_count' => $hkCount
        ];
        return $this->apiReturn(0, 'ok', $inquiryPoolList);
    }

    //删除询价池
    public function deleteInquiryPool()
    {
        $inquiryPoolIds = I('inquiry_pool_ids', 0);
        $inquiryPoolModel = D('Home/InquiryPool');
        $inquiryPoolModel->delete(trim($inquiryPoolIds, ','));
        return $this->apiReturn(0, '删除询价池成功');
    }

    //获取询价详情
    public function getInquiryDetail()
    {
        $inquiryPoolIds = I('inquiry_pool_ids', 0);
        $inquiryId = I('inquiry_id', 0);
        if (empty($inquiryPoolIds) && empty($inquiryId)) {
            return $this->apiReturn(1, '询价池ID和询价ID不能同时为空');
        }

        $data = [];

        if (!empty($inquiryPoolIds)) {
            $inquiryPoolModel = D('Home/InquiryPool');
            $map['id'] = ['in', $inquiryPoolIds];
            $data = $inquiryPoolModel->where($map)->select();
        }
        if (!empty($inquiryId)) {
            $inquiryItemModel = D('Home/InquiryItem');
            $data = $inquiryItemModel->where('inquiry_id', $inquiryId)->select();
        }
        $inquiryList = [];
        $goodsIds = array_column($data, 'goods_id');
        $goodsIds = implode(',', array_unique($goodsIds));
        $result = post_curl(NEW_GOODS_DOMAIN . '/synchronization', ['goods_id' => $goodsIds]);
        $result = json_decode($result, true);
        if (isset($result['errcode']) && $result['errcode'] == 0) {
            $skuList = $result['data'];
        }
        foreach ($data as $key => $inquiry) {
            $goodsInfo = array_get($skuList, $inquiry['goods_id']);
            $inquiryList[] = [
                'inquiry_pool_id' => $inquiry['id'],
                'goods_number' => $inquiry['goods_number'],
                'target_price' => $inquiry['target_price'],
                'batch' => $inquiry['batch'],
                'special_request' => $inquiry['special_request'],
                'delivery_place' => $inquiry['delivery_place'],
                'goods_id' => $inquiry['goods_id'],
                'goods_name' => $goodsInfo['goods_name'],
                'brand_name' => !empty($goodsInfo['standard_brand']) ? $goodsInfo['standard_brand']['brand_name'] : $goodsInfo['brand_name'],
            ];
        }

        return $this->apiReturn(0, 'ok', $inquiryList);
    }

    //新增询价
    public function addInquiry()
    {
        $inquiryModel = D('Home/Inquiry');
        $inquiryModel->startTrans();
        $inquiryData = I('inquiry_list', []);
        if (empty($inquiryData)) {
            return $this->apiReturn(1, '询报价不能为空');
        }
        $inquiryModel = D('Home/Inquiry');
        $inquiryItemModel = D('Home/InquiryItem');
        if (!\is_array($inquiryData)) {
            $inquiryData = html_entity_decode($inquiryData);
            $inquiryData = json_decode($inquiryData, true);
        }

        //型号不能为空
        foreach ($inquiryData as $key => $data) {
            if (empty($data['goods_name'])) {
                return $this->apiReturn(1, '询报价型号不能为空');
            }
        }

        $inquiry = [
            'user_id' => cookie('uid'),
            'create_time' => time(),
            'status' => 1,
        ];
        $inquiryId = $inquiryModel->add($inquiry);
        $inquiryModel->where(['id' => $inquiryId])->save(['sn' => 'XJD' . str_pad($inquiryId, 9, '0', STR_PAD_LEFT)]);

        foreach ($inquiryData as $key => &$data) {
            $data['delivery_time'] = intval($data['delivery_time']) . '工作日';
            $data['create_time'] = time();
            $data['user_id'] = cookie('uid');
            $data['inquiry_id'] = $inquiryId;
        }
        unset($data);
        $inquiryItemModel->addAll($inquiryData);

        //删除询价池
        $inquiryPoolModel = D('Home/InquiryPool');
        $inquiryPoolIds = array_column($inquiryData, 'inquiry_pool_id');
        if (!empty($inquiryPoolIds)) {
            $inquiryPoolModel->delete(trim(implode(',', $inquiryPoolIds), ','));
        }



        $inquiryModel->commit();

        $result = (new InquiryService())->syncInquiryToFrq($inquiryId);
        if ($result !== true) {
            $inquiryModel->rollback();
            return $this->apiReturn(1, $result);
        }

        return $this->apiReturn(0, '新增询价成功');
    }

    public function getInquiryList()
    {
        $userId = cookie('uid');
        $p = I('p', 1, 'intval');
        $limit = I('limit', 10, 'intval');
        $inquiryModel = D('Home/Inquiry');
        $inquiryItemModel = D('Home/InquiryItem');
        $where = ['user_id' => $userId];
        $inquiryId = I('inquiry_id', 0, 'intval');
        if ($inquiryId) {
            $where['id'] = $inquiryId;
        }

        $totalCount = $inquiryModel->where($where)->count();
        $totalPage = ceil($totalCount / $limit);

        $frqStatus = I('frq_status', 0, 'intval');
        if ($frqStatus) {
            $where['frq_status'] = $frqStatus;
        }

        $inquiryList = $inquiryModel->where($where)->page($p, $limit)->order('id DESC')->select();
        if ($inquiryList === false) {
            $inquiryList = [];
        }

        $processedInquiryList = $this->transformInquiryItem($inquiryList);

        $amountAll = $totalCount;
        $amountTodo = $inquiryModel->where(['user_id' => $userId, 'frq_status' => 1])->count();
        $amountResponded = $inquiryModel->where(['user_id' => $userId, 'frq_status' => 3])->count();
        $amountPartial = $totalCount - $amountTodo - $amountResponded;

        $data = [
            'list' => $processedInquiryList,
            'amount' => [
                'all' => (int)$amountAll,
                'todo' => (int)$amountTodo,
                'partial' => (int)$amountPartial,
                'responded' => (int)$amountResponded,
            ],
            'count' => (int)$totalCount,
            'p' => (int)$p,
            'total_page' => (int)$totalPage,
            'limit' => (int)$limit,
        ];

        return $this->apiReturn(0, 'ok', $data);
    }

    public function transformInquiryItem($inquiryList = [])
    {
        $inquiryItemModel = D('Home/InquiryItem');
        $processedInquiryList = array_map(function ($inquiry) use ($inquiryItemModel) {
            $processedInquiry = [
                'id' => $inquiry['id'],
                'sn' => !empty($inquiry['sn']) ? $inquiry['sn'] : '',
                'create_time' => !empty($inquiry['create_time']) ? date('Y-m-d H:i:s', $inquiry['create_time']) : '',
                'update_time' => !empty($inquiry['update_time']) ? date('Y-m-d H:i:s', $inquiry['update_time']) : '',
                'frq_status' => !empty($inquiry['frq_status']) ? $inquiry['frq_status'] : 0,
                'frq_status_name' => '',
            ];
            switch ($processedInquiry['frq_status']) {
                case 1:
                    $processedInquiry['frq_status_name'] = '未答复';
                    break;
                case 3:
                    $processedInquiry['frq_status_name'] = '已答复';
                    break;
                case 2:
                    $processedInquiry['frq_status_name'] = '部分答复';
                    break;
                default:
                    $processedInquiry['frq_status_name'] = '未知';
            }
            $inquiryItems = $inquiryItemModel->where(['inquiry_id' => $inquiry['id']])->order('has_order ASC')->select();
            $processedInquiry['delivery_place'] = $inquiryItems[0]['delivery_place'];
            $processedInquiry['delivery_place_name'] = $inquiryItems[0]['delivery_place'] == 1 ? '大陆' : '香港';
            if ($inquiryItems === false) {
                $inquiryItems = [];
            }
            $webInquiryConfirmedQuoteList = (new FrqService())->getQuoteByInquiryItemIds(array_column($inquiryItems, 'id'));
            $webInquiryConfirmedQuoteMap = \collect($webInquiryConfirmedQuoteList)->keyBy('source_item_id')->toArray();
            $processedInquiry['inquiry_items'] = array_map(function ($item) use ($webInquiryConfirmedQuoteMap) {
                $frqItem = !empty($webInquiryConfirmedQuoteMap[$item['id']]) ? $webInquiryConfirmedQuoteMap[$item['id']] : [];
                $quote = !empty($frqItem['quote']) ? $frqItem['quote'] : [];
                $frqDeliveryPlace = !empty($item['delivery_place']) ? $item['delivery_place'] : 1;
                $frqDeliveryPlaceName = !empty($quote) ? ($quote['currency'] == 1 ? '大陆' : '香港') : '';
                $processedItem = [
                    'id' => $item['id'],
                    'create_time' => !empty($item['create_time']) ? date('Y-m-d H:i:s', $item['create_time']) : '',
                    'update_time' => !empty($item['update_time']) ? date('Y-m-d H:i:s', $item['update_time']) : '',
                    'can_inquiry' => !empty($item['frq_goods_number']) && $item['frq_goods_number'] > 0 ? 1 : 0,
                    'goods_name' => !empty($item['goods_name']) ? $item['goods_name'] : '',
                    'brand_name' => !empty($item['brand_name']) ? $item['brand_name'] : '',
                    'goods_number' => !empty($item['goods_number']) ? $item['goods_number'] : '',
                    'batch' => !empty($item['batch']) ? $item['batch'] : '',
                    'has_order' => !empty($item['has_order']) ? $item['has_order'] : 0,
                    //报价信息
                    'frq_valid_time' => !empty($frqItem['quote']['expire_time']) ? date('Y-m-d H:i:s', $frqItem['quote']['expire_time']) : '',
                    'frq_goods_id' => !empty($frqItem['goods_id']) ? $frqItem['goods_id'] : null,
                    'frq_goods_name' => !empty($frqItem['goods_name']) ? $frqItem['goods_name'] : '',
                    'frq_brand_name' => !empty($frqItem['brand_name']) ? $frqItem['brand_name'] : '',
                    'frq_goods_number' => !empty($frqItem['quote_number']) ? $frqItem['quote_number'] : '',
                    'frq_batch' => !empty($frqItem['batch']) ? $frqItem['batch'] : '',
                    'frq_price' => !empty($frqItem['quote_price']) && $frqItem['quote_price'] !== "0.00" ? $frqItem['quote_price'] : '',
                    'frq_delivery_time' => !empty($frqItem['delivery_time']) ? $frqItem['delivery_time'] : '',
                    'frq_delivery_place' => $frqDeliveryPlace,
                    'frq_delivery_place_name' => $frqDeliveryPlaceName,
                    'frq_create_time' => !empty($quote['create_time']) ? date('Y-m-d H:i:s', $quote['create_time']) : '',
                    'frq_reply' => !empty($quote['remark']) ? $quote['remark'] : '',
                    'can_buy' => (empty($item['has_order']) && !empty($quote)) ? 1 : 0,
                ];

                $inquiryDetail = [
                    'goods_name' => !empty($item['goods_name']) ? $item['goods_name'] : '',
                    'goods_name' => !empty($item['goods_name']) ? $item['goods_name'] : '',
                    'brand_name' => !empty($item['brand_name']) ? $item['brand_name'] : '',
                    'goods_number' => !empty($item['goods_number']) ? $item['goods_number'] : '',
                    'price' => !empty($item['target_price']) ? $item['target_price'] : '',
                    'batch' => !empty($item['batch']) ? $item['batch'] : '',
                    'delivery_time' => !empty($item['delivery_time']) ? $item['delivery_time'].'工作日' : '',
                    'delivery_place' => !empty($item['delivery_place']) ? $item['delivery_place'] : 0,
                    'delivery_place_name' => !empty($item['delivery_place']) ? ($item['delivery_place'] == 1 ? '大陆' : '香港') : '',
                    'special_request' => !empty($item['special_request']) ? $item['special_request'] : '',
                    'create_time' => !empty($item['create_time']) ? date('Y-m-d', $item['create_time']) : '',
                ];

                $rfqDetail = [
                    'frq_goods_id' => $processedItem['frq_goods_id'],
                    'frq_goods_name' => $processedItem['frq_goods_name'],
                    'frq_brand_name' => $processedItem['frq_brand_name'],
                    'frq_goods_number' => $processedItem['frq_goods_number'] ?: '',
                    'frq_price' => $processedItem['frq_price'] ?: '',
                    'frq_batch' => $processedItem['frq_batch'],
                    'frq_delivery_time' => $processedItem['frq_delivery_time'],
                    'frq_delivery_place' => $frqDeliveryPlace,
                    'frq_delivery_place_name' => $frqDeliveryPlaceName,
                    'frq_reply' => $processedItem['frq_reply'],
                    'frq_create_time' => !empty($quote['create_time']) ? date('Y-m-d', $quote['create_time']) : '',
                    'frq_quote_id' => !empty($quote['id']) ? $quote['id'] : '',
                ];

                $diffText = [
                    'goods_name' => ($inquiryDetail['goods_name'] == $rfqDetail['frq_goods_name']) ? '一致' : '不一致',
                    'brand_name' => ($inquiryDetail['brand_name'] == $rfqDetail['frq_brand_name']) ? '一致' : '不一致',
                    'goods_number' => ($inquiryDetail['goods_number'] == $rfqDetail['frq_goods_number']) ? '满足' : (($rfqDetail['frq_goods_number'] > 0) ? '部分满足' : '未满足'),
                    'price' => '需评估',
                    'batch' => ($inquiryDetail['batch'] == $rfqDetail['frq_batch']) ? '符合预期' : '需评估',
                    'delivery_time' => $rfqDetail['frq_delivery_time'] == $inquiryDetail['delivery_time'] ? '符合预期' : '需评估',
                    'delivery_place' => ($inquiryDetail['delivery_place'] == $rfqDetail['frq_delivery_place']) ? '符合预期' : '需评估',
                    'special_request' => '/',
                    'create_time' => '/',
                ];

                if (empty($rfqDetail['frq_goods_name'])) {
                    $diffText = [
                        'goods_name' => '',
                        'brand_name' => '',
                        'goods_number' => '',
                        'price' => '',
                        'batch' => '',
                        'delivery_time' => '',
                        'delivery_place' => '',
                        'special_request' => '',
                        'create_time' => '',
                    ];
                }

                $processedItem['frq_diff'] = [
                    'inquiry_detail' => $inquiryDetail,
                    'rfq_detail' => $rfqDetail,
                    'diff_text' => $diffText,
                ];

                return $processedItem;
            }, $inquiryItems);

            return $processedInquiry;
        }, $inquiryList);

        return $processedInquiryList;
    }

    public function receiveJson()
    {
        $jsonData = file_get_contents('php://input');
        $data = json_decode($jsonData, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $data;
        } else {
            return false;
        }
    }
}
