<?php

namespace Home\Controller;

use Home\Controller\BaseController;
use Home\Services\UcenterService;

class LoginController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        // if (in_array(strtolower(ACTION_NAME), array('wechat', 'handlewechatpc', 'getwxinfo'))) {
        Vendor('Wechat/WechatOpen');
        $this->WechatOpen = new \WechatOpen();
        // }
    }

    // 获取微信服务号 登录授权
    public function getWechatInfo()
    {
        \Think\Log::write(sprintf("getWechatInfo:1111111111111:%s",json_encode($_REQUEST)));
        $backUrl = I('backUrl', '', 'trim');
        $is_new_h5 = I('is_new_h5', 0, 'intval'); //是否是新版H5
        $miniProgram = cookie('miniProgram');
        if (empty($miniProgram)) {
            $miniProgram = I('miniProgram', 'false', 'trim');//false时真正小程序的请求，true小程序环境下H5的请求
        }
        $pf = platform();
        $code = I('code');
        $options = array(
            'backUrl' => rtrim(API_DOMAIN, '/') . '/login/handlewechath5?pf=' . $pf . '&miniProgram=' . $miniProgram . '&is_new_h5=' . $is_new_h5 . '&backUrl=' . urlencode($backUrl),
            'miniProgram' => $miniProgram,
            'pf' => $pf,
            'code' => $code,
        );

        if ($pf == 6 && $miniProgram === 'false') {//小程序直接请求
            // \Think\Log::write(sprintf("getWechatInfo:qqqqqqqqqqq"));
            $options = array_merge($options, C('MINI_PROGRAM_CONFIG'));
            $result = wxoauth($options, 'snsapi_base');
            if (is_array($result['data'])) {
                unset($result['data']['session_key']);
            }
            $result = $result['data'];
        } else {
            // \Think\Log::write(sprintf("getWechatInfo:wwwwwwwwwwwwwwww"));
            $result = wxoauth($options, 'snsapi_base');
        }
        // \Think\Log::write(sprintf("getWechatInfo:2222222:%s",$result ? json_encode($result) : ""));
        if (isset($result['wx_data']['unionid']) && is_array($result['data'])) {
            $userIdResult = $this->checkWechat($result['wx_data']['unionid']);
            if (!empty($userIdResult['data'])) {
                $userId = $userIdResult['data'];
                $result['data']['user_id'] = $userId;
            }
        }
        
//        var_dump($result);
        \Think\Log::write(sprintf("getWechatInfo:apiReturn:%s",json_encode($result)));
        return $this->apiReturn(0, '成功', $result);
    }



    // 获取微信授权登录二维码页
    public function wechat()
    {
        session('wechat_info', null);
        $backUrl = I('backUrl', '', 'trim');
        $act = I('act', '');
        $url = $this->WechatOpen->qrConnect($backUrl, $act);
        redirect($url);
    }

    //QQ授权地址
    public function qq()
    {
        session('qq_info', 'wait');
        Vendor('QQ.qqConnectAPI');
        //http://m.liexin.com/v3/login?referer=%2Fv3%2Fuser&

        $backUrl = $_SERVER['HTTP_REFERER'];
        preg_match('/^((?:http|https):\/\/[^\/]+)(\/v3)?[^\?]+(?:\?referer=([^&]+))?/', $backUrl, $domain_match);

        /*1:主域名http://m.ichunt.com  2:判断是否有/v3  3:参数referer的值/v3/user*/
        if (strpos($domain_match['3'], 'http') === 0) {
            $match_url = urldecode($domain_match['3']);
        } else {
            $match_url = $domain_match['1'] . urldecode($domain_match['3']);
        }
        $backUrl = $match_url;
        $backUrl = urldecode(str_replace('&amp;', '&', $backUrl));
        session('qq_referer', $backUrl);

        $type = I('type');
        if ($type === '1') { //表明为会员中心的绑定或更改qq号
            C('QQ_OAUTH.callback', urlencode(API_DOMAIN . '/login/handleqq') . '?act=change_bind-1');
        }
        $qc = new \QC();
        $qc->qq_login();
    }

    /**
     * PC获取用户微信信息
     * @return [type] [description]
     */
    private function getWxInfo()
    {
        $backUrl = I('request.backUrl', '', 'trim');
        $code = I('request.code', '', 'trim');
        $state = I('request.state', '', 'trim');
        if (empty($code)) {
            return $this->apiReturn(11027, '用户拒绝授权');
        }
        if (!$this->WechatOpen->validState($state)) {
            return $this->apiReturn(11026, '返回参数错误');
        }
        //获取access_token
        $res = $this->WechatOpen->getAccessToken($code);
        if ($res['errcode'] != 0) {
            return $this->apiReturn($res['errcode'], $res['errmsg']);
        }
        $access_token = $res['data']['access_token'];
        $open_id = $res['data']['openid'];

        //获取用户微信信息
        $res = $this->WechatOpen->getUserInfo($access_token, $open_id);
        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['errcode'], $res['errmsg']);
        }
        $data = $res['data'];
        S_wechatinfo($data['unionid'], $data);
        //授权成功，记录unionid及openid
        $expire = C('WX_EXPIRE_TIME');
        session('open_id', $data['openid']);
        session('unionid', $data['unionid']);
        $union_token = $data['unionid'] . '&' . $_SERVER['REQUEST_TIME'];
        $open_token = $data['openid'] . '&' . $_SERVER['REQUEST_TIME'];
        cookie('unionid', $data['unionid'], array('expire' => $expire));
        cookie('open_id', $data['openid'], array('expire' => $expire));
        cookie('unionkey', base64_encode(pwdhash($union_token, C('API_USE_KEY')) . '&' . $_SERVER['REQUEST_TIME']), array('expire' => $expire));
        cookie('openkey', base64_encode(pwdhash($open_token, C('API_USE_KEY')) . '&' . $_SERVER['REQUEST_TIME']), array('expire' => $expire));
        return $this->apiReturn(0, '获取成功', $data);
    }

    // 调用登录校验接口
    public function apiCheckLogin()
    {
        $hard = I('hard', true);
        $res = $this->checkLogin($hard);

        return $this->apiReturn($res['err_code'], $res['err_msg']);
    }

    /**
     * 检查是否登录
     * @return [type] [description]
     */
    public function check()
    {
        $user_id = I('request.uid', 0, 'intval');
        $skey = I('request.skey', '', 'trim');
        $lskey = I('request.lskey', '', 'trim');
        $platform = platform();
        $cookie_id = cookie('uid');
        $cookie_skey = cookie('skey');
        $cookie_lskey = cookie('lskey');
        !empty($user_id) && $cookie_id = $user_id;
        !empty($skey) && $cookie_skey = $skey;
        !empty($lskey) && $cookie_lskey = $lskey;

        if (empty($cookie_id)) {
            cookie('skey', null);
            cookie('lskey', null);
            return $this->apiReturn(11010, '未登录');
        }


        if (empty($cookie_skey) && empty($cookie_lskey)) {
            cookie('uid', null);
            cookie('ucid', null);
            session('uid', null);//API内部清空
            session('ucid', null);//API内部清空
            return $this->apiReturn(11012, '登录已超时，请重新登录');
        }


        //校验强登录态
        $info_skey = S_skey($cookie_id, $platform);
        if (empty($info_skey) || $info_skey != $cookie_skey) {
            //检验弱登录态
            $info_lskey = S_lskey($cookie_id, $platform);
            if (empty($info_lskey) || $info_lskey != $cookie_lskey) {
                cookie('uid', null);
                cookie('ucid', null);
                cookie('skey', null);
                session('uid', null);//API内部清空
                session('ucid', null);//API内部清空
                return $this->apiReturn(11012, '登录已超时，请重新登录');
            }
            $data = $this->getLoginUserInfo($cookie_id);

            //弱登录态校验通过
            cookie('skey', null);
            return $this->apiReturn(-11030, '登录已超时，请重新登录', $data);
        }
        //强登录态有效时间续期
        cookie('skey', $info_skey, array('expire' => C('SKEY_EXPIRE_TIME')));
        S_skey($cookie_id, $platform, $info_skey);

        $data = $this->getLoginUserInfo($cookie_id);
        return $this->apiReturn(0, '已登录', $data);
    }

    // 调用用户信息
    public function getLoginUserInfo($userid)
    {
        // $info = S_user($userid);
        $UserMainModel = D('Home/UserMain');
        $info = $UserMainModel->getUserInfo($userid);
        $data = array(
            'mobile' => isset($info['mobile']) ? $info['mobile'] : '',
            'email' => isset($info['email']) ? $info['email'] : '',
            'user_name' => isset($info['user_name']) ? $info['user_name'] : '',
            'nike_name' => isset($info['nike_name']) ? $info['nike_name'] : '',
            'user_head' => isset($info['user_head']) ? $info['user_head'] : '',
        );
        return $data;
    }

    /**
     * 退出登录
     * @return [type] [description]
     */
    public function logout()
    {
        $platform = platform();
        $data = [];
        $res = $this->check();
        if (in_array($res['err_code'], array(0, 11030))) {
            S_skey(cookie('uid'), $platform, null);
            cookie('uid', null);
            cookie('ucid', null);
            cookie('skey', null);
            session('uid', null);//API内部清空
            session('ucid', null);//API内部清空
            $agent_info = getAgentInfo();
            if ($agent_info['bro'] == 9) { // 微信环境下 退出按钮不需要微信自动登录（微信其余环境皆为自动登录），所以要调到登录注册页面
                $refer = I('referUrl') ? I('referUrl') : M_DOMAIN . IS_V . '/user';
                $data['referUrl'] = M_DOMAIN . IS_V . '/login?referer=' . $refer;
            }
        }
        try {
            $this->setCartCount();
        } catch (Exception $e) {
        }
        return $this->apiReturn(0, '已注销', $data);
    }

    /**
     * 检查是密码错误是否超限制，需要验证码
     * @return [type] [description]
     */
    public function checkError()
    {
        $account = I('request.account', '');
        if (empty($account)) {
            return $this->apiReturn(11001, '请输入手机号/邮箱/企业登录名');
        }
        $intlCode = I('request.intl_code', '', 'trim'); // 国际手机代号
        $account = get_inte_mobile($account, $intlCode);
        // $user_id = S_account($account);
        // $info = S_user($user_id);
        $UserMainModel = D('UserMain');
        $user_id = $UserMainModel->getAccountUserId($account);
        $info = $UserMainModel->getUserInfo($user_id);
        $error_count = intval($info['login_error']);
        //初始化密码错误次数
        if ($info['login_error_time'] < time()) {
            $info['login_error_time'] = mktime(0, 0, -1, date('m'), date('d') + 1, date('Y'));//一天内
            $info['login_error'] = 0;
            S_user($user_id, $info);
        } elseif ($info['login_error'] > C('LOGIN_ERROR_COUNT') + 100) {//错误3次以上要求填写验证码
            return $this->apiReturn(10001, '请输入验证码');
        }
        return $this->apiReturn(0, '成功');
    }

    //注册登录请求用户中心
    protected function requestUCenter($data = [])
    {

    }

    /**
     * 统一登录入口
     * @return [type] [description]
     */
    public function action($account_type = '')
    {
        if (!empty($_REQUEST['pwd'])) {
            //带账号密码验证码登录
            $res = $this->apvAction($account_type);
        } else {
            //验证登录信息(手机验证码登录)
            $res = $this->avAction($account_type);
        }
        if (strrpos($_SERVER["HTTP_REFERER"], "login/bind") !== false) {
            $user_id = S_account(I("account", "", "trim"));
        } else {
            $user_id = cookie('uid');
        }

        $unionid = cookie('unionid');
        $unionkey = cookie('unionkey');
        if (empty($unionid) || empty($unionkey)) {
            $unionid = I('unionid', '', 'trim');
            $unionkey = I('unionkey', '', 'trim');
        }

        $pf = platform();
        $agent = getAgentInfo();
        //自动微信绑定功能
        if (in_array($pf, array(2, 6)) && $agent['bro'] == 9) {//微信环境H5和小程序
            if (validhash($unionid, $unionkey, C('API_USE_KEY'), C('WX_EXPIRE_TIME'))) {
                $this->bindAct($user_id, $unionid, 3, $pf);
            }
        }

        $UserMainModel = D('UserMain');
        $user = $UserMainModel->getUserInfo($user_id);
        if ($user['is_type'] == 1) {//登录行为 , 将竞调用户改成普通用户
            $UserMainModel->setUserInfo($user_id, array('is_type' => 0));
            $user_info = $UserMainModel->getUserInfo($user_id);
            $user_info['is_type'] = 0;
            S_user($user_id, $user_info); // 修改缓存值

            $account = $user['mobile'] ? $user['mobile'] : $user['email'];
            $msg_text = '竞调用户 “' . $account . '”发生登录行为，转换为正常用户了';

            if (strpos($_SERVER['HTTP_REFERER'], 'sz') === false) { // 非测试环境下执行
                $queue_name = C('CRM_PUSH_USER');
                $RbmqModel = D('Common/Rbmq');

                $data['user_id'] = intval($user_id);
                $resp = $RbmqModel->queue($queue_name)->push($data, $queue_name);

                $msg_text .= $resp ? '，推送CRM新增队列成功' : '，推送CRM新增队列失败';

                dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.jingdiao'), $msg_text);
            }
        }

        $temp = [];
        if ($res['err_code'] == 0 && !empty($user_id)) {
            $info = $UserMainModel->getUserInfo($user_id);
            if (!empty($res['data']['user_type']) && I('from_activity') == 1) {
                $userType = $res['data']['user_type'];
            } else {
                $user_type = $res['data'];
                $userType = (!empty($user_type) && 1 == $user_type) ? $user_type : 0;
            }
            $temp = array(
                'mobile' => isset($info['mobile']) ? $info['mobile'] : '',
                'email' => isset($info['email']) ? $info['email'] : '',
                'user_name' => isset($info['user_name']) ? $info['user_name'] : '',
                'nike_name' => isset($info['nike_name']) ? $info['nike_name'] : '',
                'user_type' => $userType,
            );


            if (I("pf") == 2) {
                $temp["cookie"] = [
                    'Yo4teW_gid' => $this->addGid(),
                    'Yo4teW_csrf' => $this->addCsrf(),
                    'Yo4teW_ucid' => $info["uc_id"],
                    'Yo4teW_uid' => $info["user_id"],
                    'Yo4teW_skey' => $res['data']["skey"],
                    'Yo4teW_lskey' => $res['data']["lskey"],
                ];
            }
        }
        //还要去个人中心登录
        $token = UcenterService::getUcenterLoginToken($_REQUEST['account'], $_REQUEST['pwd']);
        $temp['token'] = $token;
        return $this->apiReturn($res['err_code'], $res['err_msg'], $temp);
    }

    /**
     * 验证登录信息(带账号密码验证码登录)
     * @return [type] [description]
     */
    public function apvAction($account_type = '')
    {
        $account = I('request.account', '');
        $intlCode = I('request.intl_code', '', 'trim'); // 国际手机代号
        $pwd = I('request.pwd', '');
        $verify = I('request.verify', '');
        $platform = I('request.pf', -1, 'intval');
        empty($account_type) && $account_type = I('request.at', -1, 'intval');
        if (empty($account)) {
            $this->behavior_push(3, 11001, '请输入手机号/邮箱/企业登录名');
            return $this->apiReturn(11001, '请输入手机号/邮箱/企业登录名');
        }
        if (empty($pwd)) {
            $this->behavior_push(3, 11002, '请输入登录密码');
            return $this->apiReturn(11002, '请输入登录密码');
        }
        //缺失平台
        if (!C('PLAT_FORM.' . $platform)) {
            $this->behavior_push(3, 11009, '网络错误，请您稍后重试');
            return $this->apiReturn(11009, '网络错误，请您稍后重试');
        }
        $account = get_inte_mobile($account, $intlCode);
        //获取是否有账号
        // $user_id = S_account($account);
        $UserMainModel = D('UserMain');
        $user_id = $UserMainModel->getAccountUserId($account);
        if (empty($user_id)) {
            //进一步读取数据库判断
            $user_id = D('UserMain')->isHasAccount($account);
            if (empty($user_id)) {
                //这里还要去判断个人中心是否存在,存在的话,直接新增一条用户记录到猎芯,然后登录成功
                $user_id = UcenterService::ucenterLogin($account, $pwd);
                if (empty($user_id)) {
                    return $this->apiReturn(11013, '账号不存在，请重新输入');
                }
            }
        }

        //获取信息
        // $info = S_user($user_id);
        $info = $UserMainModel->getUserInfo($user_id);
        if (empty($info)) {
            //进一步读取数据库判断
            $info = D('UserMain')->getInfo($user_id);
            if (empty($info)) {
                return $this->apiReturn(43001, '系统异常，请稍后再试');
            }
            S_user($user_id, $info);
        }

        //账号禁用
        if ($info['status'] == 2) {
            return $this->apiReturn(11014, '账号已禁用，请联系客服');
        }

        //邮箱账号未激活不允许登录
        if ($info['status'] == 5) {
            $this->behavior_push(3, 11023, '邮箱尚未激活，请激活后再尝试登录');
            return $this->apiReturn(11023, '邮箱尚未激活，请激活后再尝试登录');
        }
        //密码错误次数
        $error = $this->checkError();
        if ($error['err_code'] != 0) {
            if (empty($verify)) {
                $this->behavior_push(3, 10001, '请输入验证码');
                return $this->apiReturn(10001, '请输入验证码');
            }
            //图形验证码校验
            $Verify = new \Think\VerifyApi();
            if (!$Verify->check($verify, 1)) {
                $this->behavior_push(3, 10002, '验证码错误，请重新获取');
                return $this->apiReturn(10002, '验证码错误，请重新获取');
            }
        }

        //登录验证
        $pwdhash = pwdhash($pwd, $info['salt']);
        if ($info['password'] != $pwdhash && $info['password'] != md5($pwd)) { //兼容1.0
            //密码错误记录
            $info['login_error_time'] = mktime(0, 0, -1, date('m'), date('d') + 1, date('Y'));//一天内
            $info['login_error'] = intval($info['login_error']) + 1;
            S_user($user_id, $info);
            $this->behavior_push(3, 11004, '密码错误，请重新输入');
            return $this->apiReturn(11004, '密码错误，请重新输入', $info['login_error'] > C('LOGIN_ERROR_COUNT'));
        }

        $info['login_error'] = 0;
        S_user($user_id, $info);
        // $account_type 账号登录类型 1手机 2邮箱 3微信 4QQ 5公司
        if (empty($account_type) || $account_type == -1) {
            if (is_mobile($account)) {
                $account_type = 1;
            } elseif (is_email($account)) {
                $account_type = 2;
            } else {
                $account_type = 5;//公司暂时无用
            }
        }

        //登录成功
        if (in_array($account_type, array(1, 2, 5))) {
            $cok = $this->loginAct($user_id, $platform, $account_type, '', 1);
            return $this->apiReturn(0, '登录成功', $cok);
        } else {
            //交由调用本程序执行登录
            return $this->apiReturn(0, '登录成功', $user_id);
        }
    }

    /**
     * 验证登录信息(手机验证码登录)
     * @return [type] [description]
     */
    public function avAction($account_type = '')
    {
        //非常规请求拦截
        if (!visit_score($this->auth(), C('NOT_NORMAL_VISIT_SCORE')) && I("pf") != 2) {
            return $this->apiReturn(0, '亲，请使用浏览器或手机访问网站哦');//虚假返回值
        }

        $account = I('request.account', '');
        $sms_verify = I('request.sms_verify', '');//短信验证码
        $verify = I('request.verify', '');
        $intlCode = I('request.intl_code', '', 'trim'); // 国际手机代号
        $platform = I('request.pf', -1, 'intval');//平台标识
        $anonymous_id = I('request.anonymous_id', '', 'trim');
        $is_from_chain = I('request.is_from_chain', 0, 'intval');
        //$account_type 账号登录类型 1手机 2邮箱 3微信 4QQ 5公司
        empty($account_type) && $account_type = I('request.account_type', 1, 'intval');
        $self_sample = I('request.self_sample', 0, 'intval');//受邀人

        // 魔方系统注册时添加联系人
        $input_1 = I('input_1', '');
        $input_1_value = I('input_1_value', '');
        $user_name = isset($input_1) ? $input_1_value : '';

        if (empty($account)) {
            return $this->apiReturn(11001, '请输入手机号/邮箱/企业登录名');
        }
        if (empty($sms_verify)) {
            return $this->apiReturn(11007, '请输入短信验证码');
        }
        //缺失平台
        if (!C('PLAT_FORM.' . $platform)) {
            return $this->apiReturn(11009, '网络错误，请您稍后重试');
        }


        $UserMainModel = D('UserMain');
        $account = get_inte_mobile($account, $intlCode);
        //获取是否有账号
        $user_id = $UserMainModel->getAccountUserId($account);

//        if ($self_sample) {
//            $path = C('LOG_PATH') . 'sampleaction/' . date('y_m_d') . '.log'; // 接口日志文件
//            \Think\Log::write('校验受邀人是否存在：' . json_encode($user_id), INFO, '', $path);
//        }

        $user_type = 2;

        //校验短信验证码
//        $code = session_sms($account.'.1');
        $code1 = session_sms($account . '.1');
        $code2 = session_sms($account . '.2');
        $code = $code1 ?: $code2;

        if ($code !== pwdhash($sms_verify, C('SMS_SALT')) && I("hcy_test") != 1122) {  //debug 先屏蔽短信登录
            return $this->apiReturn(11015, '短信验证码错误，请重新获取');
        }
        //验证成功，清除短信验证码
        session_sms($account . '.1', null);

        if (empty($user_id)) {
            //增加注册验证码限制
            $count = intval(S_reg_visit());
            S_reg_visit(++$count);

            $user_type = 1;
            //进入自动注册环节
            $invite_uid = I('invite_uid');
            $reg_user_info = array(
                'account' => $account,
                'password' => '',
                'pf' => $platform,
                'invite_uid' => $invite_uid,
                'anonymous_id' => $anonymous_id,
                'user_name' => $user_name,
            );

            if ($self_sample) {
                $path = C('LOG_PATH') . 'sampleaction/' . date('y_m_d') . '.log'; // 接口日志文件
                \Think\Log::write('受邀人注册参数：' . json_encode($reg_user_info), INFO, '', $path);
            }

            $user_id = $UserMainModel->regSuccess(1, $reg_user_info, false, $is_from_chain);
            if ($user_id === false) {
                $this->behavior_push(3, 12012, '网络错误，请您重试');
                return $this->apiReturn(12012, '网络错误，请您重试');
            }

            // $info = S_user($user_id);
            $info = $UserMainModel->getUserInfo($user_id);
            if (empty($info)) {
                $this->behavior_push(3, 43001, '系统异常，请稍后再试');
                return $this->apiReturn(43001, '系统异常，请稍后再试');
            }

            $this->behavior_push(2, 0, array('account' => $account));

            //邀约成功后短信以及站内信通知
            if ($invite_uid && !$self_sample) { // 自营样片不推送短信
                //短信
                //邀请人
                // $invite_info = S_user($invite_uid);
                $invite_info = $UserMainModel->getUserInfo($invite_uid);
                $invite_mobile = $invite_info['mobile'];
                if ($invite_mobile) {
                    $this->sendSms($invite_mobile, 5, array('telphone' => $account, 'type' => 1), 2);
                }
                //被邀请人
                $this->sendSms($account, 6, array('telphone' => $account, 'type' => 1), 2);
                //站内信
                $check['pf'] = platform();
                $check['k1'] = time();
                $check['k2'] = pwdhash($check['k1'], C('SUPER_AUTH_KEY'));
                $check['inviter_uid'] = $invite_uid;
                $check['invitee_uid'] = $user_id;
                $check['type'] = 1;
                //邀请人
                $res = post_curl(API_DOMAIN . '/message/message/send', $check);
                if (!empty($res)) {
                    $res = json_decode($res, true);
                }
                //被邀请人
                $check['type'] = 2;
                $res = post_curl(API_DOMAIN . '/message/message/send', $check);
                if (!empty($res)) {
                    $res = json_decode($res, true);
                }
            }
        } else {
            //获取信息
            // $info = S_user($user_id);
            $info = $UserMainModel->getUserInfo($user_id);
            if (empty($info)) {
                //进一步读取数据库判断
                $info = D('UserMain')->getInfo($user_id);
                if (empty($info)) {
                    $this->behavior_push(3, 43001, '系统异常，请稍后再试');
                    return $this->apiReturn(43001, '系统异常，请稍后再试');
                }
            }

            //账号禁用
            if ($info['status'] == 2) {
                return $this->apiReturn(11014, '账号已禁用，请联系客服');
            }
        }

        //修改公司名称和职业(手机端登录download登录入口)
        if (!empty($_REQUEST['work_function'])) {
            $UserInfo = D('UserInfo');
            $UserInfo->table('lie_user_info')->where(array('user_id' => $user_id))->save(array('work_function' => $_REQUEST['work_function']));
        }

        if (!empty($_REQUEST['com_name'])) {
            $userCompany = D('UserCompany');

            //新用户查询是否有数据，没有就insert
            $user_info_exists = $userCompany->table('lie_user_company')->where(['user_id' => $user_id])->find();

            if (!empty($user_info_exists)) {
                $userCompany->table('lie_user_company')->where(['user_id' => $user_id])->save(array('com_name' => $_REQUEST['com_name']));
            } else {
                $userCompany->table('lie_user_company')->data([
                    'user_id' => $user_id,
                    'com_name' => $_REQUEST['com_name']
                ])->add();
            }
        }

        if (!empty($_REQUEST['email'])) {
            // userMain
            $email = D('userMain')->field('email')->where(array('user_id' => $user_id))->find();

            //如果没有邮箱地址
            if (strlen($email) <= 0) {
                D('userMain')->where(array('user_id' => $user_id))->save(array('email' => $_REQUEST['email']));
            }
        }

        //傻逼东西,一个登录返回几百种逻辑,还互相覆盖,去死吧
        if (I("pf") == 2 && I('from_activity') != 1) { //手机端登录
            # $this->behavior_push(3, 0, array('account' => $account));
            $cok = $this->loginAct($user_id, $platform, $account_type, '', 1);
            return $this->apiReturn(0, '登录成功', $cok);
        }

        if (I("pf") == 2 && I('from_activity') == 1) { //手机端登录
            #  $this->behavior_push(3, 0, array('account' => $account));
            $cok = $this->loginAct($user_id, $platform, $account_type, '', 1);
            $cok['user_type'] = $user_type;
            return $this->apiReturn(0, '登录成功', $cok);
        }

        //登录成功
        if (in_array($account_type, array(1))) {
            //$login_way 账号登录方式 1密码登录 2免密码登录
            $this->loginAct($user_id, $platform, $account_type, '', 2, $is_from_chain);
            return $this->apiReturn(0, '登录成功', $user_type);
        } else {
            //交由调用本程序执行登录
            return $this->apiReturn(0, '登录成功', $user_id);
        }
    }

    /*
        信用评测活动注册
    */
    public function create_apply($user_id)
    {
        $data['user_id'] = $user_id;
        $data['com_name'] = I('request.com_name', '');
        $data['com_telphone'] = I('request.account', '');
        $data['legal_person'] = I('request.user_name', '');

        $data['create_time'] = time();
        $data['pf'] = 21;//IC信用贷评测

        $userCreditApply = D('userCreditApply');

        $apply_exists = $userCreditApply->where(array('user_id' => $user_id))->where('status =1 or status =2')->find();

        if ($apply_exists == null) {
            $userCreditApply->add($data);
        }
        // else{

        //     unset($data['create_time']);

        //     $userCreditApply->where(array('user_id'=>$user_id))->save($data);
        // }
    }

    // 自营样片邀约注册登录
    public function sampleAction()
    {
        $invitee_account = I('request.account', ''); // 受邀人账号
        $invite_uid = I('request.invite_uid', 0, 'intval');  // 邀约人ID
        $sample_type = I('request.type', 1, 'intval');  //样片类型,1是普通样片,2是工具尺
        $self_sample = I('request.self_sample', 0, 'intval'); // 自营样片标记

        $path = C('LOG_PATH') . 'sampleaction/' . date('y_m_d') . '.log'; // 接口日志文件
        \Think\Log::write('接口请求参数：' . json_encode(I()), INFO, '', $path);

        if (!$invite_uid || !$self_sample) {
            return $this->apiReturn(11031, '自营样片参数缺失');
        }

        $res = $this->avAction(); // 调用免密登录

        \Think\Log::write('登录接口返回：' . json_encode($res), INFO, '', $path);

        $user_id = cookie('uid'); // 当前受邀人用户ID
        $UserMainModel = D('UserMain');

        if ($res['err_code'] == 0 && !empty($user_id)) {
            // $info = S_user($user_id);
            $info = $UserMainModel->getUserInfo($user_id);
            $user_type = $res['data'];
            $res['data'] = array(
                'mobile' => isset($info['mobile']) ? $info['mobile'] : '',
                'email' => isset($info['email']) ? $info['email'] : '',
                'user_name' => isset($info['user_name']) ? $info['user_name'] : '',
                'nike_name' => isset($info['nike_name']) ? $info['nike_name'] : '',
                'user_type' => (!empty($user_type) && 1 == $user_type) ? $user_type : 0, // 是否为新注册用户
            );

            \Think\Log::write('定义返回参数：' . json_encode($res['data']), INFO, '', $path);

            $UserInfoModel = D('UserInfo');
            $UserSampleInviteModel = D('UserSampleInvite');

            $invite_user_info = $UserMainModel->getUserInfo($invite_uid); // 邀约人信息

            \Think\Log::write('邀约人信息：' . json_encode($invite_user_info), INFO, '', $path);

            // 邀约人与受邀人账号不一致 且 新注册用户
            if ($invite_user_info['mobile'] != $invitee_account && $user_type == 1) {
                $UserMainModel->startTrans();

                // 添加用户邀约记录
                $data = array();
                $data['user_id'] = $invite_uid;
                $data['account'] = $invite_user_info['mobile'] ? $invite_user_info['mobile'] : $invite_user_info['email'];
                $data['invitee_uid'] = $user_id;
                $data['invitee_mobile'] = $invitee_account;
                $data['type'] = $sample_type;
                $data['create_time'] = time();
                $invite_res = $UserSampleInviteModel->add($data);

                if ($invite_res === false) {
                    $UserMainModel->rollback();
                    return $this->apiReturn(11032, '自营样片新增用户邀约记录失败');
                }

                // 增加邀约人领取机会
                // 其中$sample_type=1的时候代表普通样片,需要增加可领取次数,2的时候为工具尺样片,不用增加apply_count
                // 工具尺活动只需要判断邀请人数即可
                if ($sample_type == 1) {
                    $info_res = $UserInfoModel->where(['user_id' => $invite_uid])->setInc('apply_count');
                    if ($info_res === false) {
                        $UserMainModel->rollback();
                        return $this->apiReturn(11033, '自营样片增加领取机会失败');
                    }
                }

                $UserMainModel->commit();
            }
        }

        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }

    /**
     * 移动端微信登录
     * @return [type] [description]
     */
    public function wxAction()
    {
        $open_id = cookie('open_id');
        $unionid = cookie('unionid');
        $openkey = cookie('openkey');
        $unionkey = cookie('unionkey');
        $is_new_h5 = I('is_new_h5', 0, 'intval'); //是否是新版H5
        if (empty($open_id)) {
            $open_id = I('openid', '', 'trim');
        }
        if (empty($unionid)) {
            $unionid = I('unionid', '', 'trim');
        }
        if (empty($openkey)) {
            $openkey = I('openkey', '', 'trim');
        }
        if (empty($unionkey)) {
            $unionkey = I('unionkey', '', 'trim');
        }

//         $backUrl = I('backUrl', '', 'trim');
//        if (!validhash($unionid, $unionkey, C('API_USE_KEY'), C('WX_EXPIRE_TIME')) ||
//            !validhash($open_id, $openkey, C('API_USE_KEY'), C('WX_EXPIRE_TIME'))) {
//            //获取准确用户信息
//            $res = $this->getWechatInfo();
//            $result = $res['data'];
//            if ($result['status'] != 1) { //获取微信用户失败
//                return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
//            }
//            $unionid = $result['data']['unionid'];
//            $open_id = $result['data']['openid'];
//        }
        $data = array('unionid' => $unionid, 'openid' => $open_id);
        if (I('get.callback') || IS_AJAX) {//点击H5微信快捷登录按钮
            $data['tobind'] = true;
            $ajax = true;
        }
        list($url, $skey, $lskey, $user_id) = $this->wechatBackUrl($data, platform());
//        var_dump($url, $skey, $lskey, $user_id,$is_new_h5);
        $userInfo = S_user($user_id);
        if ($ajax || $is_new_h5) {
            if ($is_new_h5) {
                return $this->apiReturn(0, '成功', [
                    'url' => $url,
                    "cookie" => [
                        'Yo4teW_gid' => $this->addGid(),
                        'Yo4teW_csrf' => $this->addCsrf(),
                        'Yo4teW_uid' => empty($user_id) ? 0 : $user_id,
                        'Yo4teW_ucid' => empty($userInfo["uc_id"]) ? 0 : $userInfo["uc_id"],
                        'Yo4teW_skey' => $skey ?: "",
                        'Yo4teW_lskey' => $lskey ?: "",
                    ],
                ]);
            } else {
                return $this->apiReturn(0, '成功', $url);
            }
        }
        redirect($url);
    }

    /**
     * 记录用户是否被挤掉
     * pc登录=》手机登录
     */
    public function checkUserLoginSkey($uid = 0)
    {
        try {
            $platform = platform();
            $cookie_id = $uid;
            $cookie_skey = cookie('skey');
            $cookie_lskey = cookie('lskey');
            //强登陆
            $info_skey = S_skey($cookie_id, $platform);
            $info_lskey = S_lskey($cookie_id, $platform);

            $isWriteLog = false;
            if (($info_skey && !$cookie_skey) || ($info_skey && $cookie_skey && $info_skey != $cookie_skey)) {
                $isWriteLog = true;
            } else {
            }

            if ($isWriteLog) {
                //判断登录前是否已经强登陆 有其它浏览器登陆了 记录日志
                $this->pushReportMonitorLog([
                    "interface_type" => "1",
                    "err_msg" => "该用户同平台其它登陆软件上被挤掉了",
                    "err_code" => "11034",
                    "uid" => $cookie_id,
                    "user_name" => I('request.account', ''),
                    "remark" => "",
                ]);
            }
        } catch (\Exception $e) {
        }
    }

    /**
     * 登录操作
     * @param  [type]  $user_id      登录人ID
     * @param  [type]  $platform     登录平台
     * @param integer $account_type 账号登录类型 1手机 2邮箱 3微信 4QQ 5公司
     * @param string $open_id 第三方ID
     * @param integer $login_way 账号登录方式 1密码登录 2免密码登录
     * @return [type]                [description]
     */
    public function loginAct($user_id, $platform, $account_type = -1, $open_id = '', $login_way = 1, $is_from_chain = 0)
    {
        //防止直接访问造成漏洞
        if (strtolower(ACTION_NAME) == strtolower(array_pop(explode('::', __METHOD__)))) {
            $this->_empty();
            return;
        }
        if (empty($user_id)) {
            return;
        }
        //检查登录前的登录状态
        $res = $this->check();
        if ($res['err_code'] == 0) {
            $login_status = '强';
        } elseif ($res['err_code'] == 11030) {
            $login_status = '弱';
        } else {
            $login_status = '无';
        }

        $info = D('Home/UserMain')->getInfo($user_id);
        if (empty($info)) {
            return;
        }
        //判断是否用户在其他浏览器登录，并且挤掉其他登陆者
//        $this->checkUserLoginSkey($user_id);
        //登录成功
        $skey = hash_key();
        $lskey = hash_key(32);
        //判断是否是https
        if ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443)&&!empty(I('token'))) {
            //$this->setSameSiteCookie('ucid', $info["uc_id"], C('LSKEY_EXPIRE_TIME'));
            //$this->setSameSiteCookie('uid', $user_id, C('LSKEY_EXPIRE_TIME'));
            //$this->setSameSiteCookie('skey', $skey, C('SKEY_EXPIRE_TIME'));
            //$this->setSameSiteCookie('lskey', $lskey, C('LSKEY_EXPIRE_TIME'));
            cookie('ucid', $info["uc_id"], array('expire' => C('LSKEY_EXPIRE_TIME')));
            cookie('uid', $user_id, array('expire' => C('LSKEY_EXPIRE_TIME')));
            cookie('skey', $skey, array('expire' => C('SKEY_EXPIRE_TIME')));
            cookie('lskey', $lskey, array('expire' => C('LSKEY_EXPIRE_TIME')));
        } else {
            cookie('ucid', $info["uc_id"], array('expire' => C('LSKEY_EXPIRE_TIME')));
            cookie('uid', $user_id, array('expire' => C('LSKEY_EXPIRE_TIME')));
            cookie('skey', $skey, array('expire' => C('SKEY_EXPIRE_TIME')));
            cookie('lskey', $lskey, array('expire' => C('LSKEY_EXPIRE_TIME')));
        }

        S_skey($user_id, $platform, $skey);
        S_lskey($user_id, $platform, $lskey);
        //数据库填写数据 成功与否不影响流程
        try {
            $UserSkeyModel = D('UserSkey');
            $UserSkeyModel->setSkey($user_id, $skey, $lskey);


            $UserLoginLogModel = D('UserLoginLog');

            $IsFirstLogin = !boolval($UserLoginLogModel->getFieldByUserId($user_id, 'id'));
            //供应链注册什么都不送
            if (!$is_from_chain) {
                //送抽奖资格
                $this->sendLotteryQualify($UserLoginLogModel, $user_id);
                //送答题资格
                $this->sendQuizQualify($UserLoginLogModel, $user_id);
            }
            //数据收集
            $extend = page_param('search', 'k');
            $UserLoginLogModel->addLog($user_id, $platform, $account_type, $open_id, $extend);

            //尝试购物车绑定uid、gid，不影响流程
            $this->setBindUser($platform);
            $this->updateCart($platform);
        } catch (\Exception $e) {
        }
        /*
            如果用户是信用申请活动进来的
        */
        if ($_REQUEST['ptag'] == 'edcp') {
            $this->create_apply($user_id);
        }


        //记录登录行为 务必放在addLog的后面 get_scene会清除cookie标记
        if ($account_type == 3) {
            $this->behavior_push(3, 0, array('login_way' => 'wx', 'pf' => $platform));
        } elseif ($account_type == 4) {
            $this->behavior_push(3, 0, array('login_way' => 'qq', 'pf' => $platform));
        } else {
            $this->behavior_push(3, 0, array('pf' => $platform));
        }

        return ['skey' => $skey, 'lskey' => $lskey];
    }

    //为了兼容华云那边的跨域同步登录设置cookie
    private function setSameSiteCookie($name, $value, $expireTime)
    {
        $expireAt = time() + $expireTime;
        $name = 'Yo4teW_' . $name;
        header("Set-Cookie: {$name}={$value}; expires={$expireAt}; HttpOnly; SameSite=None; Secure", false);
        //setcookie('Yo4teW_'.$name, $value, [
        //    'expires' => time() + $expireTime,
        //    'path' => '/',
        //    'domain' => '.' . MAIN_DOMAIN, // 注意这里使用了点前缀来允许子域共享Cookie
        //    'secure' => true, // 推荐在HTTPS协议下使用
        //    'httponly' => true, // 如果不需要通过JavaScript访问Cookie，设置为true增加安全性
        //    'samesite' => 'None' // 如果需要在跨站请求中发送Cookie，设置为'None'并确保'Secure'是true
        //]);
    }

    /** 送抽奖资格 (内部方法)
     * @param $UserLoginLogModel
     * @param $user_id
     */
    private function sendLotteryQualify($UserLoginLogModel, $user_id)
    {
        $start_time = strtotime(date("Y-m-d"));
        $end_time = $start_time + 60 * 60 * 24;
        $is_logged_in_today = $UserLoginLogModel->getUserLastLogin($user_id, $start_time, $end_time);
        //获取抽奖活动及活动时间
        $result = json_decode(get_curl(MARKET_DOMAIN . '/webapi/getCurrentActivities'), true);
        //活动期间抽奖活动 赠送规则
        if (!$result['errcode']) {
            foreach ($result['data'] as $value) {//多个时活动循环送
                //获取赠送抽奖次数的渠道
                $rules_arr = explode(',', $value['qualify_get_rule']);
                //登陆送
                if (in_array(2, $rules_arr)) {
                    //如果没有限制每天，则一次性送出所有次数
                    if (empty($value['qualify_login_day'])) {
                        $hasReceived = json_decode(get_curl(MARKET_DOMAIN . '/webapi/getReceivedQualifyDuringActivity', array(
                            'user_id' => $user_id,
                            'lottery_id' => $value['lottery_id']
                        )), true);
                        //一次性送出 全部 只有没送过的 才会送
                        if ($hasReceived['data']['from_login_count'] == 0) {
                            //发放活动抽奖机会
                            get_curl(MARKET_DOMAIN . '/webapi/increaseQualifyToUser', array(
                                'user_id' => $user_id,
                                'lottery_id' => $value['lottery_id'],
                                'increase_draw_count' => $value['qualify_login'],
                                'increase_type' => 2
                            ));
                        }
                    } elseif (empty($value['qualify_login'])) {//如果没限制总共次数，则每天都送指定次数
                        //获取是否第一次登陆 是则送指定次数
                        if (!$is_logged_in_today) {
                            //发放活动抽奖机会
                            get_curl(MARKET_DOMAIN . '/webapi/increaseQualifyToUser', array(
                                'user_id' => $user_id,
                                'lottery_id' => $value['lottery_id'],
                                'increase_draw_count' => $value['qualify_login_day'],
                                'increase_type' => 2
                            ));
                        }
                    } else {//既限定了每天送  又限定了总共次数
                        //每天第一次登陆的时候判断是否超过总次数
                        if (!$is_logged_in_today) {
                            //获取登陆已赠送次数
                            $hasReceived = json_decode(get_curl(MARKET_DOMAIN . '/webapi/getReceivedQualifyDuringActivity', array(
                                'user_id' => $user_id,
                                'lottery_id' => $value['lottery_id']
                            )), true);
                            //已赠送次数 小于 活动规则总共可赠送次数
                            if ($hasReceived['data']['from_login_count'] < $value['qualify_login']) {
                                //剩余可赠送总次数
                                $left_count = $value['qualify_login'] - $hasReceived['data']['from_login_count'];
                                //本次发放次数
                                $cur_increase_count = 0;
                                if ($value['qualify_login_day'] >= $left_count) {
                                    $cur_increase_count = $left_count;
                                } else {
                                    $cur_increase_count = $value['qualify_login_day'];
                                }
                                //发放活动抽奖机会
                                get_curl(MARKET_DOMAIN . '/webapi/increaseQualifyToUser', array(
                                    'user_id' => $user_id,
                                    'lottery_id' => $value['lottery_id'],
                                    'increase_draw_count' => $cur_increase_count,
                                    'increase_type' => 2
                                ));
                            }
                        }
                    }
                } elseif (in_array(1, $rules_arr)) {//注册送
                    //是否注册
                    $is_logged_before = $UserLoginLogModel->getUserLastLogin($user_id, 0, time());
                    if (!$is_logged_before) {
                        //increase_type = 6 注册送抽奖
                        get_curl(MARKET_DOMAIN . '/webapi/increaseQualifyToUser', array(
                            'user_id' => $user_id,
                            'lottery_id' => $value['lottery_id'],
                            'increase_draw_count' => $value['qualify_register'],
                            'increase_type' => 6
                        ));
                    }
                }
            }
        }
    }

    /** 送答题资格 (内部方法)
     * @param $UserLoginLogModel
     * @param $user_id
     */
    public function sendQuizQualify($UserLoginLogModel, $user_id)
    {
        $start_time = strtotime(date("Y-m-d"));
        $end_time = $start_time + 60 * 60 * 24;

        $is_logged_in_today = $UserLoginLogModel->getUserLastLogin($user_id, $start_time, $end_time);
        //获取当前答题活动
        $result = json_decode(get_curl(MARKET_DOMAIN . '/webapi/getCurrentQuiz'), true);
        //活动期间抽奖活动 赠送规则
        if (!$result['errcode']) {
            foreach ($result['data'] as $value) {//多个时活动循环送
                //登陆送
                if ($value['rule'] == 2) {
                    //一次性送出所有次数
                    if (!empty($value['qualify_count_once'])) {
                        $hasReceived = json_decode(get_curl(MARKET_DOMAIN . '/webapi/getQuestionQualify', array(
                            'user_id' => $user_id,
                            'activity_id' => $value['activity_id']
                        )), true);
                        //一次性送出 全部 只有没送过的 才会送
                        if ($hasReceived['data']['total_answer_count'] == 0) {
                            //发放活动答题机会
                            get_curl(MARKET_DOMAIN . '/webapi/increaseQuestionQualify', array(
                                'user_id' => $user_id,
                                'activity_id' => $value['activity_id'],
                                'increase_count' => $value['qualify_count_once']
                            ));
                        }
                    } elseif (!empty($value['qualify_count_day'])) {
                        //获取是否第一次登陆 是则送指定次数
                        if (!$is_logged_in_today) {
                            //发放活动抽奖机会
                            get_curl(MARKET_DOMAIN . '/webapi/increaseQuestionQualify', array(
                                'user_id' => $user_id,
                                'activity_id' => $value['activity_id'],
                                'increase_count' => $value['qualify_count_day']
                            ));
                        }
                    }
                } elseif ($value['rule'] == 1) {//注册送
                    $long_long_ago = strtotime(date("Y-m-d"), "1 January 1970");
                    $tomorrow = strtotime(date("Y-m-d"), "+1 day");
                    //是否注册
                    $is_logged_before = $UserLoginLogModel->getUserLastLogin($user_id, $long_long_ago, $tomorrow);
                    if (!$is_logged_before) {
                        get_curl(MARKET_DOMAIN . '/webapi/increaseQuestionQualify', array(
                            'user_id' => $user_id,
                            'activity_id' => $value['activity_id'],
                            'increase_count' => $value['qualify_count_once']
                        ));
                    }
                }
            }
        }
    }

    /**
     * 等待QQ授权
     * @return [type] [description]
     */
    public function waitQQAuth()
    {
        $info = session('qq_info');
        if (!empty($info) && !empty($info['open_id'])) {
            return $this->apiReturn(0, '授权成功');
        } elseif (!empty($info) && $info == 'wait') {
            return $this->apiReturn(11019, '等待授权');
        } else {
            return $this->apiReturn(11016, 'QQ登录授权失败');
        }
    }

    /**
     * 等待微信授权
     * @return [type] [description]
     */
    public function waitWXAuth()
    {
        $info = session('wechat_info');
        if (!empty($info) && !empty($info['openid'])) {
            return $this->apiReturn(0, '授权成功');
        } else {
            return $this->apiReturn(11029, '微信登录授权失败');
        }
    }

    /**
     * 授权成功后获取的QQ信息
     * @return [type] [description]
     */
    public function authQQInfo()
    {
        $qq_info = session('qq_info');
        if (empty($qq_info) || empty($qq_info['open_id'])) {
            return $this->apiReturn(11017, 'QQ登录获取信息失败');
        }
        $data = array(
            'nickname' => !empty($qq_info['nickname']) ? $qq_info['nickname'] : '',
            'oauth_head' => !empty($qq_info['figureurl_qq_2']) ? $qq_info['figureurl_qq_2'] : $qq_info['figureurl_qq_1'],
        );
        return $this->apiReturn(0, '获取成功', $data);
    }

    /**
     * 授权成功后获取wx信息
     * @return [type] [description]
     */
    public function authWechatInfo()
    {
        $unionid = session('unionid');
        empty($unionid) && $unionid = cookie('unionid');
        if (empty($unionid)) {
            $unionid = I('request.unionid');
        }
        $wx_user_json = S_wechatinfoApi($unionid);
        if (empty($wx_user_json) || empty($wx_user_json['unionid'])) {
            return $this->apiReturn(11017, '微信登录获取信息失败');
        }
        $data = array(
            'nickname' => !empty($wx_user_json['nickname']) ? $wx_user_json['nickname'] : '',
            'headimgurl' => !empty($wx_user_json['headimgurl']) ? $wx_user_json['headimgurl'] : '/v3/dist/res/m/images/headimg/boy1.png',
        );
        return $this->apiReturn(0, '获取成功', $data);
    }

    /**
     * 检测QQ号是否已被绑定
     * @return [type] [description]
     */
    public function checkQQ($info = '')
    {
        empty($info) && $info = session('qq_info');
        if (empty($info)) {
            return $this->apiReturn(11017, 'QQ登录获取信息失败');
        }
        $user_id = S_qq($info['open_id']);
        if (is_array($user_id)) {
            $res = $user_id;
            $user_id = S_account($res['data']['mobile']);
            if (!$user_id) {
                $user_id = S_account($res['data']['email']);
            }
            if ($user_id) {
                S_qq($info['open_id'], null);
                S_qq($info['open_id'], $user_id);
            }
        }
        if ($user_id) { // 清除部分旧数据中，S_qq有数据，但S_user没数据的问题
            // $qq_info = S_user($user_id);
            $UserMainModel = D('UserMain');
            $qq_info = $UserMainModel->getUserInfo($user_id);
            $qq_open_id = $qq_info['qq_oauth']['open_id'];
            if (!$qq_open_id) {
                S_qq($info['open_id'], null);
                $user_id = '';
            }
        }

        if (empty($user_id)) {
            return $this->apiReturn(11018, 'QQ未绑定账号');
        } else {
            return $this->apiReturn(0, '已绑定', $user_id);
        }
    }

    /**
     * 检查微信是否已被绑定
     * @param string $unionid [description]
     * @return [type]          [description]
     */
    public function checkWechat($unionid = '')
    {
        empty($unionid) && $unionid = I('unionid', '');
        $user_id = S_wechat($unionid);
        if (empty($user_id)) {
            return $this->apiReturn(11018, '微信未绑定账号');
        } else {
            return $this->apiReturn(0, '已绑定', $user_id);
        }
    }

    /**
     * 登录并绑定
     * @return [type] [description]
     */
    public function bindQQ()
    {
        $qq_info = session('qq_info');//获取回调时写入的授权信息session
        $platform = I('request.pf', -1, 'intval');
        $type = I('request.type', 0, 'intval');
        if (empty($qq_info) || empty($qq_info['open_id'])) {
            return $this->apiReturn(11017, 'QQ登录获取信息失败');
        }
        //注册账号或登录校验
        if ($type) {
            $res = A('Home/Reg')->mobileReg(4);
            if (!in_array($res['err_code'], [0, 12002, 12024])) {
                return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
            }
        }
        $res = $this->action(4);
        if (is_array($res['data'])) {
            $user_id = S_account($res['data']['mobile']);
            if (!$user_id) {
                $user_id = S_account($res['data']['email']);
            }
            $res['data'] = $user_id;
            if (!$user_id) {
                $res['err_code'] = 43002;
                $res['err_msg'] = '缺失user_id';
                cookie('uid', null);
            }
        }
        if ($res['err_code'] == 0) {
            $user_id = $res['data'];
            if (empty($user_id)) {
                return $this->apiReturn(11020, '未获取用户相关ID');
            }
            //检查QQ是否已被绑定
            $res = $this->checkQQ($qq_info);
            if ($res['err_code'] == 0) {
                session('qq_info', null);
                if ($res['data'] == $user_id) {
                    return $this->apiReturn(11021, '账号已绑定，无需再绑定');
                } else {
                    return $this->apiReturn(11022, '该QQ已被其他账号绑定');
                }
            }
            //先解绑之前的
            // $info = S_user($user_id);
            $UserMainModel = D('UserMain');
            $info = $UserMainModel->getUserInfo($user_id);
            $pre_open_id = S_qq($info['qq_oauth']['open_id']);
            if ($pre_open_id) {
                S_qq($info['qq_oauth']['open_id'], null);
            }
            //绑定
            S_qq($qq_info['open_id'], $user_id);
            $data = array(
                'user_id' => $user_id,
                'open_id' => $qq_info['open_id'],
                'oauth_type' => 2,
                'bind_time' => $_SERVER['REQUEST_TIME'],
                'oauth_head' => !empty($qq_info['figureurl_qq_2']) ? $qq_info['figureurl_qq_2'] : $qq_info['figureurl_qq_1'],
                'oauth_nickname' => $qq_info['nickname'],
                'oauth_status' => 1,
                'access_token' => $qq_info['access_token'],
            );
            //记录用户绑定信息
            $third['qq_oauth'] = array(
                'open_id' => $data['open_id'],
                'bind_time' => $data['bind_time'],
                'oauth_head' => $data['oauth_head'],
                'oauth_nickname' => $data['oauth_nickname'],
                'oauth_status' => $data['oauth_status'],
                'access_token' => $data['access_token'],
            );
            // $info = S_user($user_id);
            $info = $UserMainModel->getUserInfo($user_id);
            $info = array_merge($info, $third);
            S_user($user_id, $info);

            $UserOauthModel = D('UserOauth');
            $UserOauthModel->add($data);

            //执行登录
            $this->loginAct($user_id, $platform, 4, $qq_info['open_id']);
            //session('qq_info', null); // 因为绑定成功页面要用到，所以暂时不能清理，等1小时后redis会自动清除 180809
            return $this->apiReturn(0, '绑定成功', cookie('user_type'));
        } else {
            return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
        }
    }

    // QQ回调
    public function handleQQ()
    {
        $act = I('get.act', '');
        Vendor('QQ.qqConnectAPI');
        $act_arr = explode('-', $act);
        $platform = isset($act_arr['1']) ? intval($act_arr) : -1;
        $qc = new \QC();
        $access_token = $qc->qq_callback();
        $open_id = $qc->get_openid();
        $qc = new \QC($access_token, $open_id);
        $info = $qc->get_user_info();
        if (!empty($open_id) && !empty($access_token) && !empty($info)) {
            $session_info = array(
                'access_token' => $access_token,
                'open_id' => $open_id,
            );
            $session_info = array_merge($session_info, $info);
            if (!$act && isMobile()) {
                $this->handleQQMobile($session_info);
                die;
            }
            switch ($act_arr['0']) {
                case 'access_code':
                    //检测是否绑定
                    $res = $this->checkQQ($session_info);
                    if ($res['err_code'] == 0 && !empty($res['data'])) {
                        //登录
                        $this->loginAct($res['data'], $platform, 4, $open_id);
                    }
                    session('qq_info', $session_info);
                    break;
                case 'change_bind':
                    session('qq_info', $session_info);
                    //会员中心 更改|新增 绑定
                    break;
            }
        }

        exit('<script>self.close();</script>');
    }

    // QQh5移动端回调
    public function handleQQMobile($session_info)
    {
        $platform = 2;
        $qq_referer = session('qq_referer');
        $backUrl = rtrim(M_DOMAIN, '/') . '/v3/login/bind?type=qq&referer=' . $qq_referer;

        //检测是否绑定
        $res = $this->checkQQ($session_info);
        if ($res['err_code'] == 0 && !empty($res['data'])) {
            //登录
            $this->loginAct($res['data'], $platform, 4, $session_info['open_id']);
            $backUrl = $qq_referer; // 回调地址
        }
        session('qq_info', $session_info);
        redirect($backUrl);
    }

    protected function checkLandingLogin()
    {
        $skey = cookie("lskey");
        $uid = cookie("uid");
        return $skey && $uid;
    }

    /*
     * 微信环境下 判断H5用户是否已经登录并且是否绑定了微信
     *
     */
    public function WechatCheckLogin()
    {
        $res = $this->checkLogin();
//        \Think\Log::write(print_r($res,true));
        if ($res["err_code"] == 0) {
            //判断是否绑定微信
            $user_id = 0;//默认未绑定任何账号
            $res["data"]["is_bind_wachat"] = 0;
            if (is_array($res['data'])) {
                $user_id = S_account($res['data']['mobile']);
                if (!$user_id) {
                    $user_id = S_account($res['data']['email']);
                }
            }
            if (!$user_id) {
                return $this->apiReturn(120200, '未登录');
            }
            $unionid = cookie('unionid');
            $unionkey = cookie('unionkey');
            if (!validhash($unionid, $unionkey, C('API_USE_KEY'), C('WX_EXPIRE_TIME'))) {
                return $this->apiReturn(11029, '获取授权信息失败，请刷新重新授权');
            }
            // \Think\Log::write(print_r($user_id,true));
            // $loginUserInfo = S_user($user_id); //当前登录用户信息
            // $wechatUserInfo = S_user(S_wechat($unionid));//当前微信绑定的用户信息

            $UserMainModel = D('UserMain');
            $loginUserInfo = $UserMainModel->getUserInfo($user_id);//当前登录用户信息
            $wechatUserInfo = $UserMainModel->getUserInfo(S_wechat($unionid));//当前微信绑定的用户信息
            $username = "";
            if ($wechatUserInfo) {
                $username = $wechatUserInfo["mobile"] ? $wechatUserInfo["mobile"] : $wechatUserInfo["email"];
            }
            if (isset($loginUserInfo["wechat_oauth"]) && !empty($loginUserInfo["wechat_oauth"])) {
                $wechat_oauth = $loginUserInfo["wechat_oauth"];

                //账号未绑定微信  该微信未绑定其它账号
                $__noBind = isset($wechat_oauth["oauth_status"]) && $wechat_oauth["oauth_status"] != 1 && !S_wechat($unionid);
                $noBind__ = isset($wechat_oauth["union_id"]) && !$wechat_oauth["union_id"] && !S_wechat($unionid);

                if ($noBind__ || $__noBind) {
                    return $this->apiReturn(0, 'ok', $res["data"]);
                }

                $loginUnion_id = isset($wechat_oauth["union_id"]) ? $wechat_oauth["union_id"] : null;
                if (!$loginUnion_id && S_wechat($unionid) && S_wechat($unionid) != $user_id) {
                    //已登录未绑定 微信绑定其它账号
                    $res["data"]["msg"] = sprintf("当前微信已绑定%s账号,，请先登录%s账号解除绑定", $username, $username);
                    $res["data"]["is_bind_wachat"] = 4;
                } elseif ($loginUnion_id && $loginUnion_id == $unionid && S_wechat($unionid) && S_wechat($unionid) == $user_id) {
                    //已绑定该微信 并且 已登录
                    $res["data"]["msg"] = "当前微信和账号已绑定";
                    $res["data"]["is_bind_wachat"] = 1;
                } elseif ($loginUnion_id && $loginUnion_id != $unionid && !S_wechat($unionid)) {
                    //当前账号已绑定其它微信  并且该微信未绑定其它账号
                    $res["data"]["msg"] = sprintf("当前账号已绑定%s微信，请点击解绑按钮", $loginUserInfo["oauth_nickname"]);
                    $res["data"]["is_bind_wachat"] = 2;
                } elseif ($loginUnion_id && $loginUnion_id != $unionid && S_wechat($unionid) && S_wechat($unionid) != $user_id) {
                    $username = "";
                    $oauth_nickname = "";
                    if ($loginUserInfo) {
                        $username = $loginUserInfo["mobile"] ? $loginUserInfo["mobile"] : $loginUserInfo["email"];
                        $oauth_nickname = isset($wechat_oauth["oauth_nickname"]) ? $wechat_oauth["oauth_nickname"] : "";
                    }
                    $res["data"]["msg"] = sprintf("当前账号%s已绑定微信%s,请先解绑", $username, $oauth_nickname);
                    $res["data"]["is_bind_wachat"] = 3;
                }
            } elseif (!isset($loginUserInfo["wechat_oauth"])) {
                //该账号没有绑定其它微信
                if (S_wechat($unionid) && S_wechat($unionid) != $user_id) {
                    $res["data"]["msg"] = sprintf("当前微信已绑定%s账号,，请先登录%s账号解除绑定", $username, $username);
                    $res["data"]["is_bind_wachat"] = 4;
                }
            }
            return $this->apiReturn(0, 'ok', $res["data"]);
        } else {
            return $this->apiReturn(120200, '未登录');
        }
    }

    /*
     * 微信环境已登录  微信未绑定状态  自动绑定接口
     */
    public function autoBindWachat()
    {
        $res = $this->WechatCheckLogin();
        if ($res["err_code"] != 0) {
            return $this->apiReturn(120200, '未登录');
        }
        $data = $res["data"];
        if ($data["is_bind_wachat"] == 0) {
            $platform = platform();
            $unionId = cookie('unionid');
            $user_id = cookie('uid');
            $bindActRes = $this->bindAct($user_id, $unionId, 3, $platform);
            return $this->apiReturn($bindActRes["err_code"], $bindActRes["err_code"]["msg"]);
        } elseif ($data["is_bind_wachat"] == 1) {
            return $this->apiReturn(0, "当前微信和账号已绑定");
        }
        return $this->apiReturn(120200, $data["msg"]);
    }

    // 绑定微信账号
    public function bindWechat()
    {
//        \Think\Log::write(print_r($this->WechatCheckLogin(),true));
//        exit;
        $platform = platform();
        $type = I('request.type', 0, 'intval');
        $currentUser_id = 0;//当前需要绑定微信的用户id
        if ($type) {
            $res = A('Home/Reg')->mobileReg(3);
            if (!in_array($res['err_code'], [0, 12002, 12024])) {
                return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
            }
        }

        if (!S_account(I("account", "", "trim"))) {
            //新注册号码

        } else {
            $currentUser_id = S_account(I("account", "", "trim"));
        }

        $res = $this->action(3);
        if (is_array($res['data'])) {
            $user_id = S_account($res['data']['mobile']);
            $user_id = $user_id?:$currentUser_id;
            if (!$user_id) {
                $user_id = S_account($res['data']['email']);
            }
            $res['data'] = $user_id;
            if (!$user_id) {
                $res['err_code'] = 43002;
                $res['err_msg'] = '缺失user_id';
            }
        }
        if ($res['err_code'] == 0) {
            $user_id = $res['data'];
            $unionid = cookie('unionid');
            $unionkey = cookie('unionkey');
            if (!validhash($unionid, $unionkey, C('API_USE_KEY'), C('WX_EXPIRE_TIME'))) {
                return $this->apiReturn(11029, '获取授权信息失败，请刷新重新授权');
            }

            //执行绑定
            $bindact_res = $this->bindAct($user_id, $unionid, 3, $platform);
//            return $this->apiReturn(99999, json_encode($bindact_res));
            if (in_array($bindact_res["err_code"], [110120, 110129])) {
                return $this->apiReturn($bindact_res["err_code"], $bindact_res["err_msg"]);
            }
            //执行登录
            $this->loginAct($user_id, $platform, 3, $unionid);
            return $this->apiReturn(0, '绑定成功666', cookie('user_type'));
        } else {
            return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
        }
    }

    /**
     * 绑定操作
     * @param  [type]  $user_id      [description]
     * @param  [type]  $unionId      [description]
     * @param integer $account_type [description]
     * @return [type]                [description]
     */
    public function bindAct($user_id, $unionId, $account_type = 3, $platform = -1)
    {
        if (empty($user_id)) {
            return $this->apiReturn(11020, '未获取用户相关ID');
        }
        $UserOauthModel = D('UserOauth');
        // $info = S_user($user_id);
        $UserMainModel = D('UserMain');
        $info = $UserMainModel->getUserInfo($user_id);
        switch ($account_type) {
            case 3:
                $wx_user_json = S_wechatinfoApi($unionId);
                /**
                 * [subscribe] => 1
                 * [openid] => o3We-wHBb_ipwLr2SG2qa4glRqXc
                 * [nickname] => 子小龍
                 * [unionid] => oH2A6waQHQNDqVs3zqSwwBk2TqIM
                 */
                if (!$wx_user_json) {
                    return $this->apiReturn(110120, '请重新刷新页面');
                }

                $pre_user_id = S_wechat($unionId);//新微信

                $oauth_nickname = isset($info["wechat_oauth"]["oauth_nickname"]) && $info["wechat_oauth"]["oauth_nickname"];
                $wechat_oauth = isset($info["wechat_oauth"]) ? $info["wechat_oauth"] : null;
                if ($wechat_oauth && $oauth_nickname && $pre_user_id != $user_id) {
                    $username = $info["mobile"] ? $info["mobile"] : $info["email"];
                    return $this->apiReturn(110120, sprintf("微信：%s 已绑定%s账号，请先登录%s账号解绑", $wechat_oauth["oauth_nickname"], $username, $username));
                }

                if (S_wechat($unionId) && $pre_user_id != $user_id) {
                    if ($oldInfo = S_user(S_wechat($unionId))) {
                        $username = $oldInfo["mobile"] ? $oldInfo["mobile"] : $oldInfo["email"];
                        return $this->apiReturn(110120, sprintf("微信:%s 已绑定 %s 的账号，请先登录%s账号解绑", $wx_user_json['nickname'], $username, $username));
                    }
                }

                //双重确定，确保已经绑定成功，无需再绑一次
                if ($wechat_oauth && $wechat_oauth['union_id'] == $wx_user_json['unionid'] && $pre_user_id == $user_id) {
                    return $this->apiReturn(110129, '该账号已经绑定过了');
                }


                //解绑旧微信的关系
                if ($wechat_oauth) {
                    S_wechat($info['wechat_oauth']['union_id'], null);
                }
                //新微信以前绑定过账号，先解绑
                if ($pre_user_id) {
                    //解绑新微信的原先的账号
                    // $other_user = S_user($pre_user_id);
                    $other_user = $UserMainModel->getUserInfo($pre_user_id);
                    unset($other_user['wechat_oauth']);
                    S_user($pre_user_id, $other_user);
                    S_wechat($unionId, null); //新微信解绑

                    $change_where['user_id'] = array('eq', $pre_user_id);
                    $change_where['union_id'] = $unionId;
                    $change_where['oauth_status'] = array('eq', 1);
                    $change_data['oauth_status'] = 2; // 解绑
                    $UserOauthModel->where($change_where)->save($change_data);
                }

                $oauth_type = $platform == 1 ? 1 : 3;
                //绑定现在的账号
                $data = array(
                    'user_id' => $user_id,
                    'open_id' => $wx_user_json['openid'],
                    'union_id' => $unionId,
                    'oauth_type' => $oauth_type, //1:wechat 2:qq 3:wechat公众号
                    'bind_time' => $_SERVER['REQUEST_TIME'],
                    'oauth_head' => !empty($wx_user_json['headimgurl']) ? $wx_user_json['headimgurl'] : '',
                    'oauth_nickname' => !empty($wx_user_json['nickname']) ? $wx_user_json['nickname'] : '',
                    'oauth_status' => 1,
                    'access_token' => '',
                );
                $UserOauthModel->add($data);

                //绑定到用户中心
                UcenterService::ucenterBindWechat($user_id, $wx_user_json['openid'], $unionId);

                //记录用户绑定信息
                $third['wechat_oauth'] = array(
                    'union_id' => $data['union_id'],
                    'bind_time' => $data['bind_time'],
                    'oauth_head' => $data['oauth_head'],
                    'oauth_nickname' => $data['oauth_nickname'],
                    'oauth_status' => $data['oauth_status'],
                );
                if ($platform == 1) {//PC使用的是微信开发平台
                    $third['wechat_oauth']['open_id_kf'] = $data['open_id'];
                    $third['wechat_oauth']['access_token_kf'] = $data['access_token'];
                } else {//移动使用的是微信公众平台
                    $third['wechat_oauth']['open_id'] = $data['open_id'];
                    $third['wechat_oauth']['access_token'] = $data['access_token'];
                }
                $info = array_merge($info, $third);
                S_wechat($data['union_id'], $user_id);
                break;
            case 4:
                //2018-8-15未整合QQ绑定
                $qq_info = session('qq_info');//获取回调时写入的授权信息session
                //检查QQ是否已被绑定
                $res = $this->checkQQ($qq_info);
                if ($res['err_code'] == 0) {
                    session('qq_info', null);
                    if ($res['data'] == $user_id) {
                        return $this->apiReturn(11021, '账号已绑定，无需再绑定');
                    } else {
                        return $this->apiReturn(11022, '该QQ已被其他账号绑定');
                    }
                }
                //先解绑之前的
                $pre_user_id = S_qq($info['qq_oauth']['open_id']);
                if ($pre_user_id) {
                    //解绑新QQ的原先的账号
                    // $other_user = S_user($pre_user_id);
                    $other_user = $UserMainModel->getUserInfo($pre_user_id);
                    unset($other_user['qq_oauth']);
                    S_user($pre_user_id, $other_user);
                    S_qq($info['qq_oauth']['open_id'], null);

                    $change_where['user_id'] = $user_id;
                    $change_where['oauth_type'] = 2;
                    $change_where['oauth_status'] = 1;
                    $change_data['oauth_status'] = 2; // 解绑
                    $UserOauthModel->where($change_where)->save($change_data);
                }
                $data = array(
                    'user_id' => $user_id,
                    'open_id' => $qq_info['open_id'],
                    'oauth_type' => 2,
                    'bind_time' => $_SERVER['REQUEST_TIME'],
                    'oauth_head' => !empty($qq_info['figureurl_qq_2']) ? $qq_info['figureurl_qq_2'] : $qq_info['figureurl_qq_1'],
                    'oauth_nickname' => $qq_info['nickname'],
                    'oauth_status' => 1,
                    'access_token' => $qq_info['access_token'],
                );
                $UserOauthModel->add($data);

                //记录用户绑定信息
                $third['qq_oauth'] = array(
                    'open_id' => $data['open_id'],
                    'bind_time' => $data['bind_time'],
                    'oauth_head' => $data['oauth_head'],
                    'oauth_nickname' => $data['oauth_nickname'],
                    'oauth_status' => $data['oauth_status'],
                    'access_token' => $data['access_token'],
                );
                $info = array_merge($info, $third);

                S_qq($qq_info['open_id'], $user_id);
                break;
        }
        S_user($user_id, $info);

        return $this->apiReturn(0, '绑定成功');
    }

    /**
     * PC微信回调
     * @return [type] [description]
     */
    public function handleWechatPc()
    {
        $platform = 1;
        $act = I('request.act', 'bind');
        $res = $this->getWxInfo();
        if ($res['err_code'] == 0) {
            $result = $res['data'];
            switch ($act) {
                case 'bind':
                    list($backUrl, $skey, $lskey) = $this->wechatBackUrl($result, $platform);
                    redirect($backUrl);
                    break;
                case 'change':
                    session('wechat_info', $result);//临时认证标记
                    exit('<script>window.close();</script>');
                    break;
            }
        }
        // echo $backUrl;
        // dump($result);
        // die();
    }

    /**
     * 移动微信回调
     * @return [type] [description]
     */
    public function handleWechatH5()
    {
        \Think\Log::write(sprintf("handleWechatH5:555555555555555555:%s",json_encode($_REQUEST)));
        // $platform = I('pf', '2', 'trim');
        $backUrl = I('backUrl', '', 'trim');
        $is_new_h5 = I('is_new_h5', 0, 'trim');
        if ( $is_new_h5) {
            $backUrl .= '&code=' . I('code');
            redirect($backUrl);
            return;
        }

        $res = $this->getWechatInfo();
        $result = $res['data'];
        if ($result['status'] == 1) { //获取微信用户成功
            // $backUrl = $this->wechatBackUrl($result['data'], $platform);
        } elseif ($result['status'] == 2) {
            $backUrl = $result['data'];
        }
        $backUrl .= (parse_url($backUrl, PHP_URL_QUERY) ? '&' : '?') . 'code=' . I('code');
        redirect($backUrl);
    }

    /**
     * 处理微信直接登录或未绑定的跳转地址
     * @param  [type] $result   [description]
     * @param  [type] $platform [description]
     * @return [type]           [description]
     */
    private function wechatBackUrl($result, $platform)
    {
        $backUrl = I('backUrl', '', 'trim');
        $unionId = !empty($result['unionid']) ? $result['unionid'] : '';
        $open_id = !empty($result['openid']) ? $result['openid'] : '';
        $skey = "";
        $lskey = "";
        // session('open_id', $open_id);
        // session('unionid', $unionId);
        //检测是否绑定
        $res = $this->checkWechat($unionId);
        if ($res['err_code'] == 0 && !empty($res['data'])) {//已绑定
            // $info = S_user($res['data']);
            $UserMainModel = D('UserMain');
            $info = $UserMainModel->getUserInfo($res['data']);
            if ($platform == 1) {
                $open_id_field = 'open_id_kf';
            } else {
                $open_id_field = 'open_id';
            }
            if ($info['wechat_oauth'][$open_id_field] != $open_id) {
                $info['wechat_oauth'][$open_id_field] = $open_id;
                S_user($res['data'], $info);
            }

            //登录['skey' => $skey, 'lskey' => $lskey]
            $weixinKey = $this->loginAct($res['data'], $platform, 3, $unionId);//微信登录
            $skey = $weixinKey['skey'];
            $lskey = $weixinKey['lskey'];

            preg_match('/^((?:http|https):\/\/[^\/]+)(\/v3)?[^\?]+(?:\?referer=([^&]+))?/', $backUrl, $domain_match);
            /*1:主域名http://m.ichunt.com  2:判断是否有/v3  3:参数referer的值/v3/user*/
            if (strpos($domain_match['3'], 'http') === 0) {
                $match_url = urldecode($domain_match['3']);
            } else {
                $match_url = $domain_match['1'] . urldecode($domain_match['3']);
            }
            if (!$domain_match['3']) {
                if (strpos($backUrl, '/login') !== false) {
                    $match_url = $domain_match['1'];
                } else {
                    $match_url = urldecode($backUrl);
                }
            }
            $backUrl = $match_url;
            $backUrl = urldecode(str_replace('&amp;', '&', $backUrl));
        } else {
            preg_match('/^((?:http|https):\/\/[^\/]+)(\/v3)?[^\?]+(?:\?referer=([^&]+))?/', $backUrl, $domain_match);
            if (!$domain_match[3]) {
                $referer = str_replace($domain_match[1], '', $domain_match[0]);
                if ($platform == 6 || isset($result['tobind'])) { // 微信小程序没有绑定账号时，直接跳转到绑定页面
                    // $backUrl = $domain_match['1'] . $domain_match['2'] . '/login/bind?referer=' . urldecode($referer);
                    $backUrl = $domain_match['1'] . $domain_match['2'] . '/login/bind?referer=/';
                } else {
                    $backUrl = $domain_match['1'] . $domain_match['2'] . '/login?referer=' . urldecode($referer);
                }
            }
        }
        return [
            $backUrl,
            $skey,
            $lskey,
            $res['data'],
        ];
    }

    //广播自动登录接口
    public function autoLogin()
    {
        $token = I('token', '');
        if (empty($token)) {
            $this->apiReturn(-1, 'token不能为空');
        }
        $userId = UcenterService::autoLogin($token);
        if (!is_int((int)$userId)) {
            return $this->apiReturn(-1, $userId);
        }
        $this->loginAct($userId, 1);
        return $this->apiReturn(0, '自动登录成功');
    }
}
