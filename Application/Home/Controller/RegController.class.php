<?php
namespace Home\Controller;

use Home\Controller\BaseController;
use Home\Services\UcenterService;

class RegController extends BaseController
{
    /**
     * 注册网关
     * @param  string  $verify 图形验证码值 默认false为计数累加， 传值为校验
     * @param  integer $type   图形验证分组值
     * @return [type]          [description]
     */
    public function regGateway($verify = false, $type = 1)
    {
        //非常规请求拦截
        if (!visit_score($this->auth(), C('NOT_NORMAL_VISIT_SCORE'))  && I("pf") != 2) {
            return $this->apiReturn(0, '亲，请使用浏览器或手机访问网站哦');//虚假返回值
        }

        //全局 区间 访问次数限制
        $count = intval(S_reg_visit());
        $res = $count >= C('REG_VISIT_LIMIT');

        if($verify === true){
            return true;
        }

        if ($verify !== false || $res) {//校验
            if (empty($verify)) {
                return $this->apiReturn(10001, '请输入图形验证码');
                // return $this->apiReturn(23019, '请输入图形验证码');
            }
            $Verify = new \Think\VerifyApi();
            if (!$Verify->check($verify, $type)) {
                return $this->apiReturn(10002, '验证码错误，请重新获取');
            }
        } else {//计数
            //计数
            S_reg_visit(++$count);
        }
        return true;
    }


    public function getUserInfo()
    {
        $k = I('k');
        if (is_mobile($k) || is_email($k)) {
            $user_id = S_account($k);
            $user_info = S_user($user_id);
            p($user_id);
            p($user_info);
        } else {
            $user_info = S_user($k);
            p($user_info);
        }
    }

    // 微信绑定信息获取
    public function getWechatBindInfo()
    {
       /*查询8.16号后面的绑定用户
        lie_user_oauth
        bind_time **********
        oauth_type = 3
        oauth_status = 1
        user_id 查redis
            wechat_oauth['bind_time']
            ['oauth_status'] = 1; ->获取
        根据user_id 和status 判断一下是否有绑定，没有则符合
        用户注册时间  redis没有则mysql
        会员账号
            手机或邮件
        绑定时间*/

        $UserOauthModel = D('UserOauth');
        $map['bind_time'] = array('gt', '**********');
        $map['oauth_type'] = array('eq', '3');
        $map['oauth_status'] = array('eq', '1');

        $field = 'user_id';
        $res = $UserOauthModel->field($field)->where($map)->group('user_id')->select();
        $UserMainModel = D('UserMain');
        $findInfo = [];
        if (!$res) {
            return false;
        }
        foreach ($res as $key => $value) {
            //根据user_id 和status 判断一下是否有绑定，没有则符合
            $maps = [];
            $maps['user_id'] = $value['user_id'];
            $maps['bind_time'] = array('lt', '**********');
            $maps['oauth_type'] = array('eq', '3');
            $count = $UserOauthModel->field($field)->where($maps)->count();
            if ($count) { // 只要指定日期后才有记录的才符合要求
                continue;
            }
            $user_id = $value['user_id'];
            // $userInfo = S_user($user_id);
            $userInfo = $UserMainModel->getUserInfo($user_id);
            // 用户注册时间   会员账号
            if ($userInfo['wechat_oauth'] && $userInfo['wechat_oauth']['oauth_status'] == '1') {
                $wheremaps = [];
                $wheremaps['user_id'] = array('eq', $user_id);
                $create_time = $userInfo['create_time'];
                if (!$create_time) {
                    $cou = $UserMainModel->field('create_time')->where($wheremaps)->select();
                    $create_time = $cou['create_time'];
                }
                $findInfo[$key]['account'] = $userInfo['mobile'] ? sctonum($userInfo['mobile']) : strval($userInfo['email']);
                $findInfo[$key]['create_time'] = $create_time ? date('Y-m-d h:i:s', $create_time) : '';
                $findInfo[$key]['bind_time'] = $userInfo['wechat_oauth']['bind_time'] ? date('Y-m-d h:i:s', $userInfo['wechat_oauth']['bind_time']) : ''; // 绑定时间
            }
        }
        // excel 导出
        $column = array('账号', '创建时间', '绑定时间'); // 表头
        $fileName = '微信绑定信息列表'.date('YmdHis', time()).'.csv';
        export_csv($findInfo, $column, $fileName);
    }
    public function index()
    {
        $testArr = C('TEST_PEOPLE_MOBILE');
        $UserMainModel = D('UserMain');
        foreach ($testArr as $key => $value) {
            // $user_id = S_account($value);
            $user_id = $UserMainModel->getAccountUserId($value);
            if ($user_id) {
                // $user_info = S_user($user_id);
                $user_info = $UserMainModel->getUserInfo($user_id);
                if ($user_info) {
                    $user_info['is_test'] = 1;
                    S_user($user_id, $user_info);
                    $where = array();
                    $where['user_id'] = array('eq', $user_id);
                    $save['is_test'] = 1;
                    $UserMainModel->where($where)->save($save);
                }
            }
        }
    }
    // 统计注册和关注
    public function getCount()
    {
        //关注领券
        $WxQrConfigModel = D('WxQrConfig');
        $WxScanSubscribeModel = D('WxScanSubscribe');
        $WxScanHistoryModel = D('WxScanHistory');
        $CouponModel = D('Coupon');
        $UserMainModel = D('UserMain');
        $UserCouponModel = D('UserCoupon');
        $qr_arr = F('QrArr_SUB');//符合条件的二维码渠道id
        $QRCODE_CREATE_TIME = C('QRCODE_CREATE_TIME');
        if (!$qr_arr) {
            $where['status'] = array('eq', 1);
            $where['create_time'] = array('between', "{$QRCODE_CREATE_TIME['START']}, {$QRCODE_CREATE_TIME['END']}");
            $res = $WxQrConfigModel->field('scene_id, scene_desc, url')->where($where)->select();
            $qr_arr = '';
            if ($res) {
                F('QrArr_SUB', $res);
                $qr_arr = F('QrArr_SUB');
            }
        }
        $count_arr = [];
        foreach ($qr_arr as $key => $value) {
            $map = [];
            $map['event_key'] = array('eq', $value['scene_id']);
            // 扫描二维码各个渠道场景的统计次数
            $count_arr['scan_history_count'][$value['scene_desc']] = $WxScanHistoryModel->where($map)->count();
            //扫码后关注公众号的人数
            $sub_map = [];
            $sub_map['status'] = array('eq', 1);
            $sub_map['event_key'] = array('eq', $value['scene_id']);
            $count_arr['scan_subscribe_count'][$value['scene_desc']] = $WxScanSubscribeModel->where($sub_map)->count();

            //通过此关注活动注册人数
            $keywordStr = $value['url'];
            $keyword_arr = explode("adtag=", $keywordStr);
            $adtag = $keyword_arr['1'];
            $reg_map['reg_remark'] = array('like', "%adtag={$adtag}%");
            $count_arr['scan_reg_count'][$adtag] = $UserMainModel->where($reg_map)->count();
        }
        //关注活动 领券人数
        $SUBSCRIBE_COUPON_ID = C('COUPON_EVENT_OPTION.SUBSCRIBE');
        $cou_res = $this->getCouResByCouID($SUBSCRIBE_COUPON_ID);
        $count_arr['scan_coupon_count'] = $cou_res['had_send_nums'];
        //优惠券的发放数量，使用数量，对应订单总金额
        $count_arr['scan_coupon_res'][$SUBSCRIBE_COUPON_ID] = $cou_res;
        $count_true_arr['scan'] = $count_arr;

        //注册优惠券统计
        //通过此活动领券的人数 几个优惠券id group by user_id
        $COUPON_REG_COUPON_ID_ARR = C('COUPON_EVENT_OPTION.REG');
        foreach ($COUPON_REG_COUPON_ID_ARR as $key => $value) {
            $reg_id_str .= $value . ',';
            //每张活动优惠券的发放数量，使用数量，对应订单总金额 优惠券id
            $reg[$value] = $this->getCouResByCouID($value);
        }
        $count_true_arr['reg']['coupon_details_info'] = $reg;
        $user_map = [];
        $user_map['coupon_id'] = array('in', $reg_id_str);
        $re = $UserCouponModel->field('user_id')->where($user_map)->group('user_id')->select();
        $count_true_arr['reg']['coupon_count'] = count($re);
        //通过此活动完成注册的人数 adtag 种类
        $REG_COUPON_ADTAG = C('REG_COUPON_ADTAG');
        foreach ($REG_COUPON_ADTAG as $key => $value) {
            $reg_cou_map['reg_remark'] = array('like', "%adtag={$value}%");
            $count_true_arr['reg']['reg_count'][$value] = $UserMainModel->where($reg_cou_map)->count();
        }

        foreach ($count_true_arr['scan']['scan_history_count'] as $key => $value) {
            $str_de .= $key . '：共' . $value . '<br>';
        }
        foreach ($count_true_arr['scan']['scan_subscribe_count'] as $key => $value) {
            $str_su .= $key . '：共' . $value . '<br>';
        }
        foreach ($count_true_arr['scan']['scan_reg_count'] as $key => $value) {
            $str_rc .= 'adtag为 ' . $key . ' 有 ' . $value . ' 人<br>';
        }
        foreach ($count_true_arr['scan']['scan_coupon_res'] as $key => $value) {
            $str_scr .= '优惠券id为 ' . $key . ' 的发放数量为 ' . $value['had_send_nums'] . '，使用数量为 ' . $value['cou_had_use_count'] . '，对应的订单总金额为 ' . $value['order_amount_count'] . '<br>';
        }

        foreach ($count_true_arr['reg']['reg_count'] as $key => $value) {
            $str_sr .= 'adtag为 ' . $key . '有 ' . $value . ' 人<br>';
        }

        foreach ($count_true_arr['reg']['coupon_details_info'] as $key => $value) {
            $str_cdi .= '优惠券id为 ' . $key . ' 的发放数量为 ' . $value['had_send_nums'] . '，使用数量为 ' . $value['cou_had_use_count'] . '，对应的订单总金额为 ' . $value['order_amount_count'] . '<br>';
        }
        $str = "关注领取优惠券：<br>";
        $str .= "1、扫描二维码的次数<br>";
        $str .= $str_de;
        $str .= "2、扫码后关注公众号的人数<br>";
        $str .= $str_su;
        $str .= "3、通过此活动注册人数<br>";
        $str .= $str_rc;
        $str .= "4、通过此活动领券人数<br>";
        $str .= "共 " . $count_true_arr['scan']['scan_coupon_count'] . ' 人<br>';
        $str .= "5、优惠券的发放数量，使用数量，对应订单总金额<br>";
        $str .= $str_scr . "<br><br>";

        $str .= "注册领优惠券：<br>";
        $str .= "1、通过此活动完成注册的人数<br>";
        $str .= $str_sr;
        $str .= "2、通过此活动领券的人数<br>";
        $str .= "共 " . $count_true_arr['reg']['coupon_count'] . ' 人<br>';
        $str .= "3、每张活动优惠券的发放数量，使用数量，对应订单总金额<br>";
        $str .= $str_cdi;
        p($str);
    }
    //获取某个优惠券的相关信息
    public function getCouResByCouID($coupon_id)
    {
        if (!$coupon_id) {
            return false;
        }
        $CouponModel = D('Coupon');
        $UserCouponModel = D('UserCoupon');
        $OrderModel = D('Order');
        $cou_map = [];
        $cou_map['coupon_id'] = array('eq', $coupon_id);
        $cou_res = $CouponModel->field('coupon_num, surplus_num')->where($cou_map)->find();
        $cou_res['had_send_nums'] = intval($cou_res['coupon_num']) - intval($cou_res['surplus_num']);
        //该券使用状态(统计已使用)
        $cou_user_map = [];
        $cou_user_map['status'] = array('eq', 1);
        $cou_user_map['coupon_id'] = array('eq', $coupon_id);
        $cou_user_res = $UserCouponModel->field('user_id, order_id')->where($cou_user_map)->select();
        $cou_had_use_count = count($cou_user_res);
        $cou_res['cou_had_use_count'] = $cou_had_use_count;
        $cou_res['order_amount_count'] = 0;
        if ($cou_had_use_count) {
            foreach ($cou_user_res as $key => $value) {
                //查询，金额统计
                $order_id_str .= $value['order_id'] . ',';
            }
            $order_id_str = rtrim($order_id_str, ',');
            $order_map['order_id'] = array('in', $order_id_str);
            $cou_res['order_amount_count'] = $OrderModel->where($order_map)->sum('order_amount');
        }
        return $cou_res ? $cou_res : '';
    }
    // 获取公司钉钉人员对应关系 (对应的userid)
    public function getdingtalkinfo()
    {
        $token = getDDAccessToken();
        $url = 'https://oapi.dingtalk.com/department/list?access_token=' . $token;
        $result=dtcurl($url);
        $department_arr = $result['department'];
        foreach ($department_arr as $key => $value) {
            $department_id = $value['id'];
            $url_user = "https://oapi.dingtalk.com/user/list?access_token=". $token ."&department_id=" . $department_id;
            $result=dtcurl($url_user);
            $user_list = $result['userlist'];
            if ($user_list && is_array($user_list)) {
                $UserMainModel = D('UserMain');

                foreach ($user_list as $key => $value) {
                    $mobile = $value['mobile'];
                    // $user_id = S_account($mobile);
                    // $user_info = S_user($user_id);
                    $user_id = $UserMainModel->getAccountUserId($mobile);
                    $user_info = $UserMainModel->getUserInfo($user_id);

                    if ($user_id && $user_info) {
                        $user_info['dtalk_oauth']['user_id'] = $value['userid'];
                        S_user($user_id, $user_info);
                        echo $value['name'] . '<br>';
                    }
                }
            }
        }
    }
    /**
     * 忘记密码
     */
    public function forgetPassword()
    {
        $account = I('request.account', '', 'trim');
        $intlCode = I('request.intl_code', '', 'trim');

        $platform = I('request.pf', -1, 'intval');
        $verify = I('request.verify', '');
        if (!$account) {
            return $this->apiReturn(11001, '请输入手机号/邮箱');
        }

        $account = get_inte_mobile($account, $intlCode);
        $user_id = S_account($account);//获取是否有账号
        if (empty($user_id)) {
            return $this->apiReturn(11013, '账号不存在，请重新输入');
        }
        if (is_mobile($account)) {
            $sms_verify = I('request.sms_verify', '');
            if (!$sms_verify) {
                return $this->apiReturn(12004, '请输入短信验证码');
            }
            $code = session_sms($account);//校验短信验证码
            if ($code !== pwdhash($sms_verify, C('SMS_SALT'))) {
                return $this->apiReturn(11015, '短信验证码错误，请重新获取');
            }
            session_sms($account, null); //验证成功，清除短信验证码
            S_pwd($account, $user_id);
            return $this->apiReturn(0, '成功', '');
        } elseif (is_email($account)) {
            //图形验证码校验
            if (empty($verify)) {
                return $this->apiReturn(10001, '请输入验证码');
            }
            $Verify = new \Think\VerifyApi();
            if (!$Verify->check($verify, 2)) {
                return $this->apiReturn(10002, '验证码错误，请重新获取');
            }

            // $userInfo = S_user($user_id);
            $UserMainModel = D('UserMain');
            $userInfo = $UserMainModel->getUserInfo($user_id);
            if ($userInfo['status'] == 5) {
                //return $this->apiReturn(12018, '邮箱尚未激活，请先到邮箱激活邮件');
            }
            S_pwd($account, $user_id);
            $type = 3;//忘记密码 发送验证邮箱
            $res = $this->activateOperationEmail($type, $user_id);
            if ($res['err_code'] !== '0') {
                return $this->apiReturn($res['err_code'], $res['err_msg']);die;
            } else {

                return $this->apiReturn(0, '成功');
            }

        } else {
            return $this->apiReturn(13001, '您输入的手机号码或邮箱格式有误');
        }
    }

    // 重置密码 初始123456 -- 会员系统
    public function resetPassword()
    {
        $uid = I('uid', '');
        $pwd = I('pwd', '');

        $userInfo = D('UserMain')->getInfo($uid);

        $salt = $userInfo['salt'];
        $newPwd = pwdhash($pwd, $salt);

        if ($userInfo['password'] != $newPwd) {
            if (S_account($userInfo['mobile'])) {
                $user_id = S_account($userInfo['mobile']);
                $account = $userInfo['mobile'];
            } else {
                $user_id = S_account($userInfo['email']);
                $account = $userInfo['email'];
            }

            if (!$user_id) {
                S_user($uid, $userInfo);
                S_account($account, $uid);
            }

            $userInfo['password'] = $newPwd;
            S_user($uid, $userInfo);

            $data['password'] = $newPwd;

            $user = D('UserMain')->where(['user_id' => $uid])->save($data);

            if ($user !== false) {
                return $this->apiReturn(0, '操作成功');
            } else {
                return $this->apiReturn(12007, '操作失败');
            }
        }

        return $this->apiReturn(0, '操作成功');
    }

    // 禁用、启用用户---会员系统3.0
    public function userActionByAdmin()
    {
        $uid = I('uid', '');
        $type = I('type', ''); // 1.启用 2.禁用(销户)

        $UserMainModel = D('UserMain');

        if (strpos($uid, ',') !== false) {
            $user_ids = explode(',', $uid);

            foreach ($user_ids as $v) {
                $res = $this->updateUserCache($v, $type);

                if ($res === false) {
                    return $this->apiReturn(12007, '操作失败，用户不存在，用户ID：'.$v);
                }
            }

            $map = [
                'user_id' => ['in', $user_ids],
            ];

            $save_res = $UserMainModel->where($map)->save(['status' => $type]);
        } else {
            $res = $this->updateUserCache($uid, $type);

            if ($res === false) {
                return $this->apiReturn(12007, '操作失败，用户不存在，用户ID：' . $uid);
            }

            $save_res = $UserMainModel->where(['user_id' => $uid])->save(['status' => $type]);
        }

        if ($save_res) {
            return $this->apiReturn(0, '操作成功');
        }

        return $this->apiReturn(12007, '操作失败');
    }

    // 更新用户缓存
    public function updateUserCache($user_id, $type)
    {
        $UserMainModel = D('UserMain');

        $userInfo = $UserMainModel->getInfo($user_id);

        if (empty($userInfo)) return false;

        $userInfo['status'] = $type;

        if ($type == 1) {
            S_user($user_id, $userInfo);
        } else {
            S_user($user_id, null);
        }

        return true;
    }

    /**
     * 用户新增密码 （忘记密码）
     */
    public function changePasswordFromMsg()
    {
        $password = I('request.password', '', 'trim');
        // $rePassword = I('request.repassword', '', 'trim');
        $account = I('request.account');//账号

        $intlCode = I('request.intl_code', '', 'trim');
        $token = I('request.token');//token 邮箱
        //密码是否为空
        if (!$password) {
            return $this->apiReturn(23003, '密码不能为空');
        }
        //密码位数是否正确 大于6
        if (intval(strlen($password)) < 6 ) {
            return $this->apiReturn(12007, '密码格式6-20字符，请重新设置');
        }
        //两次密码是否一致
        // if ($password !== $rePassword) {
        //     return $this->apiReturn(23004, '两次输入的密码不一致');
        // }

        $UserMainModel = D('UserMain');
        if (!$account) { //用于邮箱
            $token_info = S_token($token);
            $user_id = $token_info['user_id'];
            // $user_info = S_user($user_id);
            $user_info = $UserMainModel->getUserInfo($user_id);
            $account = $user_info['email'];
            if (!$account) {
                return $this->apiReturn(23036, '忘记密码的账号不能为空');
            }
        } else {
            $account = get_inte_mobile($account, $intlCode);
        }
        $user_id = S_pwd($account);
        // $userInfo = S_user($user_id);
        $userInfo = $UserMainModel->getUserInfo($user_id);
        if ($userInfo['intl_code']) {
            $mobile =  $userInfo['mobile'];
            if($userInfo['intl_code'] && $userInfo['intl_code'] != "0086"){
                $mobile = $userInfo['intl_code'] . '+' . $userInfo['mobile'];
            }
        } else {
            $mobile = $userInfo['mobile'];
        }
        if (!$user_id || ($userInfo['email'] !== $account && $mobile !== $account)) {
            return $this->apiReturn(23035, '该用户账号信息有误，请重新获取激活邮箱验证码');
        }
        $salt = $userInfo['salt'];
        $modPassword = pwdhash($password, $salt);
        $userInfo['password'] = $modPassword;
        $userInfo['status'] = 1;
        S_user($user_id, $userInfo);
        try {
            $save = array('password' => $modPassword, 'status'=>1);
            $ucenterResult = UcenterService::changePassword($userInfo["uc_id"],$password);
            if($ucenterResult["code"] != 0){
                return $this->apiReturn(0, '修改密码失败');
            }
            D('UserMain')->where(array('user_id'=>$user_id))->save($save);
        } catch (Exception $e) {
        }
        return $this->apiReturn(0, '修改密码成功');
    }

    /**
     * 公司账户注册
     * @return [type] [description]
     */
    public function userRegAdmin()
    {
        $type = 3; //账号注册
        $account = I('request.account', '', 'trim');
        $password = I('request.password', '', 'trim');
        $user_code = I('request.user_code', '', 'trim');
        $platform = I('request.pf', -1, 'intval');
        $invite_uid = I('invite_uid');
        $anonymous_id = I('anonymous_id','','trim');
        $res = $this->checkAccount($type, 1);//不需校验短信验证码
        if ($res['err_code'] == 0) {
            //注册成功,插入数据库和redis
            $UserMainModel = D('UserMain');
            $reg_user_info = array(
                'account'   => $account,
                'password'  => $password,
                'pf'        => $platform,
                'user_code' => $user_code,
                'invite_uid'=> $invite_uid,
                'anonymous_id'=>$anonymous_id
            );
            $user_id = $UserMainModel->regSuccess($type, $reg_user_info);
            return $this->apiReturn(0, '成功', $user_id);
        } else {
            return $this->apiReturn($res['err_code'], $res['err_msg'], '');
        }
    }

    /** 注册成功后 赠送积分(手机注册+邮箱验证后)
     * @param $user_id
     */
    private function addUserPoint($user_id){
        //rbmq入队
        $queue = C('QUEUE_MKT_POINT');
        $RbmqModel = D('Common/Rbmq');
        $push_data = array(
            'user_id' => $user_id,
            'flow_type' => 1,
            'flow_reason_type' => 1,
            'flow_pf' => platform()
        );
        $RbmqModel->queue($queue)->push($push_data, $queue);
    }

    // /**
    //  * 后台用户邮箱注册
    //  * @return [type] [description]
    //  */
    // public function emailRegAdmin()
    // {
    //     $type = 2; //账号注册
    //     $email = I('request.email', '', 'trim');
    //     $password = I('request.password', '', 'trim');
    //     $platform = I('request.pf', -1, 'intval');
    //     $parent_id = I('request.parent_id', 0, 'intval');
    //     $sale_uid = I('request.sale_uid', 0, 'intval');
    //     $invite_uid = I('invite_uid');
    //     $res = $this->checkAccount($type, 1);//不需校验短信验证码
    //     if ($res['err_code'] == 0) {
    //         //注册成功,插入数据库和redis
    //         $UserMainModel = D('UserMain');
    //         $reg_user_info = array(
    //             'account'   => $email,
    //             'password'  => $password,
    //             'pf'        => $platform,
    //             'parent_id' => $parent_id,
    //             'invite_uid'=> $invite_uid,
    //             'erp_sale_uid'=> $sale_uid,
    //         );
    //         $user_id = $UserMainModel->regSuccess($type, $reg_user_info);
    //         return $this->apiReturn(0, '成功', $user_id);
    //     } else {
    //         return $this->apiReturn($res['err_code'], $res['err_msg'], '');
    //     }
    // }

    // 后台批量导入会员
    public function batchImportByAdmin()
    {
        $post = I('post.');

        $fail = 0;
        $result = [];
        foreach ($post['import_user'] as $k=>$v) {
            $v['k1'] = time();
            $v['k2'] = pwdhash($v['k1'], C('SUPER_AUTH_KEY'));
            $v['pf'] = $post['pf'];

            $res = post_curl(API_DOMAIN.'/reg/regUserByAdmin', $v);

            if ($res['err_code'] != 0) {
                $result[$k] = '导入失败，'.$res['err_msg'];
                $fail++;
                continue;
            }

            $result[$k] = '导入成功';
        }

        $resp['fail'] = $fail;
        $resp['data'] = $result;

        return $this->apiReturn(0, '成功', $resp);
    }



    // 后台注册会员 --- 3.0会员系统
    public function regUserByAdmin()
    {
        $data = I('post.');

        // 会员信息
        $userInfo = [
            'account'      => !empty($data['mobile']) ? $data['mobile'] : $data['email'],
            'user_name'    => $data['link_name'],
            'password'     => $data['password'],
            'is_test'      => $data['is_test'],
            'is_sendmsg'   => isset($data['is_sendmsg']) ? $data['is_sendmsg'] : 1,
            'sale_id'      => isset($data['sale_id']) ? $data['sale_id'] : 0,
            'channel_type' => isset($data['channel_type']) ? intval($data['channel_type']) : 1,//1线上用户，2线下用户，3大客户部，4苏州部
        ];

        if (isset($data['user_nature'])) $userInfo['user_nature'] = $data['user_nature']; // 用户性质

        $UserMainModel    = D('UserMain');
        $UserInfoModel    = D('UserInfo');
        $UserCompanyModel = D('UserCompany');

        $reg_type = !empty($data['mobile']) ? 1 : 2;//后台注册只有手机和邮箱

        switch  ($reg_type) {
            case 1:
                $mibile = trim($data['mobile']);
                if (strstr($mibile, "+")) { // 国际手机
                    $account_arr          = explode("+", $userInfo["account"]);
                    $mibile    = $account_arr[1];
                }
                $isExists = D("UserMain")->where(["mobile"=>$mibile])->field("user_id")->find();
                if(!empty($isExists)){
                    return $this->apiReturn(0, sprintf("手机号%s已经存在",$mibile),$isExists["user_id"]);
                }
                break;
            case 2:
                $isExists = D("UserMain")->where(["email"=>$userInfo["account"]])->field("user_id")->find();
                if(!empty($isExists)){
                    return $this->apiReturn(0, sprintf("邮箱%s已经存在",$userInfo["account"]),$isExists["user_id"]);
                }
                break;
        }

        $UserMainModel->startTrans();

        $user_id = $UserMainModel->regSuccess($reg_type, $userInfo, true);

        if ($user_id) {
            if (isset($data['work_function']) || isset($data['user_type']) || $data['sale_id']) { // 用户扩展表
                $addUserInfo['user_id']       = $user_id;
                $addUserInfo['work_function'] = isset($data['work_function']) ? $data['work_function'] : 0;
                $addUserInfo['user_type']     = isset($data['user_type']) ? $data['user_type'] : 0;
                $addUserInfo['sale_id']       = $data['sale_id'];
                $res = $UserInfoModel->where(['user_id' => $user_id])->save($addUserInfo);

                if ($res === false) {
                    $UserMainModel->rollback();
                    return $this->apiReturn(12014, '新增用户扩展信息失败');
                }
            }

            // 公司信息
            $companyInfo = [
                'user_id'             => $user_id,
                'com_name'            => isset($data['com_name']) ? $data['com_name'] : '',
                'type_id'             => isset($data['type_id']) ? $data['type_id'] : '',
                'type_name'           => isset($data['type_name']) ? $data['type_name'] : '',
                'com_logo'            => isset($data['com_logo']) ? $data['com_logo'] : '',
                'brand_list'          => isset($data['brand_list']) ? $data['brand_list'] : '',
                'com_province_id'     => isset($data['com_province_id']) ? $data['com_province_id'] : '',
                'com_city_id'         => isset($data['com_city_id']) ? $data['com_city_id'] : '',
                'com_area_id'         => isset($data['com_area_id']) ? $data['com_area_id'] : '',
                'com_address'         => isset($data['com_address']) ? $data['com_address'] : '',
                'com_telphone'        => isset($data['com_telphone']) ? $data['com_telphone'] : '',
                'com_fax'             => isset($data['com_fax']) ? $data['com_fax'] : '',
                'product_service'     => isset($data['product_service']) ? $data['product_service'] : '',
                'business_area'       => isset($data['business_area']) ? $data['business_area'] : '',
                'com_desc'            => isset($data['com_desc']) ? $data['com_desc'] : '',
                'stock_area'          => isset($data['stock_area']) ? $data['stock_area'] : 0,
                'company_description' => isset($data['company_description']) ? htmlspecialchars_decode($data['company_description']) : NULL,
            ];

            $company_id = $UserCompanyModel->data($companyInfo)->add();

            if ($company_id === false) {
                $UserMainModel->rollback();
                return $this->apiReturn(12014, '新增用户公司信息失败');
            }

            // 回写company_id 到 用户表
            $UserMainModel->where(['user_id' => $user_id])->data(['company_id' => $company_id])->save();

            $UserMainModel->commit();

            if ($data['com_province_id'] !== '-1' && $data['com_city_id'] !== '-1' && $data['com_area_id'] === '-1') {
                $companyInfo['area_name']   = '';
                $companyInfo['com_area_id'] = '';
            } else {
                $companyInfo['area_name']   = get_district($data['com_area_id']) ? get_district($data['com_area_id']) : '';
                $companyInfo['com_area_id'] = $data['com_area_id'] ? $data['com_area_id'] : $companyInfo['com_area_id'];
            }

            $companyInfo['province_name'] = get_province($data['com_province_id']) ? get_province($data['com_province_id']) : '';
            $companyInfo['city_name']     = get_city($data['com_city_id']) ? get_city($data['com_city_id']) : '';

            S_company($user_id, $companyInfo);
            // $user_info =  S_user($user_id);
            $user_info =  $UserMainModel->getUserInfo($user_id);
            $user_info['company_id'] = $company_id;
            S_user($user_id,$user_info);

            return $this->apiReturn(0, '新增成功', $user_id);
        } else {
            return $this->apiReturn(12014, '新增失败');
        }
    }

    // 后台编辑会员 --- 3.0会员系统
    public function editUserByAdmin()
    {
        $data = I('post.');

        $uid = $data['uid'];

        $UserMainModel    = D('UserMain');
        $UserInfoModel    = D('UserInfo');
        $UserCompanyModel = D('UserCompany');

        $user = $UserMainModel->getInfo($uid);

        $account = $user['mobile'] ? $user['mobile'] : $user['email'];
        $msg_text = '会员系统编辑用户：'.$account;

        // 修改前的手机号码和邮箱
        if ($user['mobile']) { // 号码存在时
            $pre_mobile = $user['intl_code'] && $user['intl_code'] != '0086' ? $user['intl_code'].'+'.$user['mobile'] : $user['mobile'];
        } else {
            $pre_mobile = '';
        }

        $pre_email = $user['email'];

        $log = '修改前：手机号码：'.$pre_mobile.'，邮箱：'.$pre_email; // 日志

        // 会员信息
        $userInfo = [
            'mobile'      => $data['mobile'],
            'email'       => $data['email'],
            'user_name'   => $data['link_name'],
            'is_test'     => $data['is_test'],
            'is_type'     => $data['is_type'],
            'user_nature' => $data['user_nature'],
            'is_sendmsg'  => $data['is_sendmsg'],
            'intl_code'   => $data['intl_code'],
        ];

        $log_is_test    = $user['is_test'] ? '是' : '否';
        $log_is_sendmsg = $user['is_sendmsg'] ? '是' : '否';
        $log_is_type    = $user['is_type'] ? '是' : '否';

        $log .= '，用户名称：'.$user['user_name'].'，用户性质：'.$user['user_nature'].'，是否测试账号：'.$log_is_test.'，是否发送营销短信：'.$log_is_sendmsg.'，是否尽调：'.$log_is_type;

        $user['mobile']      = $data['mobile'];
        $user['email']       = $data['email'];
        $user['user_name']   = $data['user_name'];
        $user['is_test']     = $data['is_test'];
        $user['is_type']     = $data['is_type'];
        $user['user_nature'] = $data['user_nature'];
        $user['is_sendmsg']  = $data['is_sendmsg'];
        $user['intl_code']   = $data['intl_code'];

        // 公司信息
        $companyInfo = [
            'user_id'             => $uid,
            'com_name'            => isset($data['com_name']) ? $data['com_name'] : '',
            'type_id'             => isset($data['type_id']) ? $data['type_id'] : '',
            'type_name'           => isset($data['type_name']) ? $data['type_name'] : '',
            'com_logo'            => isset($data['com_logo']) ? $data['com_logo'] : '',
            'brand_list'          => isset($data['brand_list']) ? $data['brand_list'] : '',
            'com_province_id'     => isset($data['com_province_id']) ? $data['com_province_id'] : '',
            'com_city_id'         => isset($data['com_city_id']) ? $data['com_city_id'] : '',
            'com_area_id'         => isset($data['com_area_id']) ? $data['com_area_id'] : '',
            'com_address'         => isset($data['com_address']) ? $data['com_address'] : '',
            'com_telphone'        => isset($data['com_telphone']) ? $data['com_telphone'] : '',
            'com_fax'             => isset($data['com_fax']) ? $data['com_fax'] : '',
            'product_service'     => isset($data['product_service']) ? $data['product_service'] : '',
            'business_area'       => isset($data['business_area']) ? $data['business_area'] : '',
            'com_desc'            => isset($data['com_desc']) ? $data['com_desc'] : '',
            'stock_area'          => isset($data['stock_area']) ? $data['stock_area'] : 0,
            'company_description' => isset($data['company_description']) ? htmlspecialchars_decode($data['company_description']) : NULL,
        ];

        // 获取修改前公司信息
        $old_company_name = $UserCompanyModel->where(['user_id' => $uid])->getField('com_name');

        $UserMainModel->startTrans(); // 开启事务

        if (!empty($data['password'])) { // 如果密码改变，则重新加密
            $user['password'] = pwdhash($data['password'], $user['salt']);
            $userInfo['password'] = $user['password'];
            $log .= '，已更换密码';

            $ucenterResult = UcenterService::changePassword($user['uc_id'], $data['password']);
            if ($ucenterResult["code"] != 0) {
                $UserMainModel->rollback();
                return $this->apiReturn(0, '修改密码失败');
            }
        }

        if(!empty($user['company_id'])){
            $company = $UserCompanyModel->where(['user_id' => $uid])->save($companyInfo);
        }else{
            $company = $UserCompanyModel->data($companyInfo)->add();
            $userInfo['company_id'] = $company;
            $user['company_id'] = $company;
        }

        if ($company === false) {
            $UserMainModel->rollback();
            return $this->apiReturn(12015, '公司信息更新失败');
        }

        $res = $UserMainModel->where(['user_id' => $uid])->save($userInfo);

        if ($res === false) {
            $UserMainModel->rollback();
            return $this->apiReturn(12015, '用户信息更新失败');
        }

        if ($data['com_province_id'] !== '-1' && $data['com_city_id'] !== '-1' && $data['com_area_id'] === '-1') {
            $companyInfo['area_name']   = '';
            $companyInfo['com_area_id'] = '';
        } else {
            $companyInfo['area_name']   = get_district($data['com_area_id']) ? get_district($data['com_area_id']) : '';
            $companyInfo['com_area_id'] = $data['com_area_id'] ? $data['com_area_id'] : $companyInfo['com_area_id'];
        }

        $companyInfo['province_name'] = get_province($data['com_province_id']) ? get_province($data['com_province_id']) : '';
        $companyInfo['city_name']     = get_city($data['com_city_id']) ? get_city($data['com_city_id']) : '';

        // 客户职务
        if (!empty($data['work_function']) || !empty($data['landline'])) {
            $ui_id = $UserInfoModel->where(['user_id' => $uid])->getField('ui_id');

            if ($ui_id) {
                $res = $UserInfoModel->where(['user_id' => $uid])->save([
                    'work_function' => $data['work_function'],
                    'landline' => $data['landline'],
                ]);
            } else {
                $add = [];
                $add['user_id']       = $uid;
                $add['work_function'] = $data['work_function'];
                $add['landline'] = $data['landline'];
                $res = $UserInfoModel->data($add)->add();
            }

            if ($res === false) {
                $UserMainModel->rollback();
                return $this->apiReturn(12015, '用户扩展信息更新失败');
            }
        }

        D('UserActionLog')->addLog($uid, $data['operator_id'], 2, '编辑用户信息，'.$log);

        $UserMainModel->commit();

        if ($data['mobile']) { // 号码存在时
            $new_mobile = $data['intl_code'] && $data['intl_code'] != '0086' ? $data['intl_code'].'+'.$data['mobile'] : $data['mobile'];
        } else {
            $new_mobile = '';
        }

        if ($pre_mobile != $new_mobile) { // 绑定新的手机号码
            $pre_mobile && S_account($pre_mobile, null);
            $new_mobile && S_account($new_mobile, $uid);
        }

        if ($pre_email != $data['email']) { // 绑定新的邮箱
            $pre_email && S_account($pre_email, null);
            $data['email'] && S_account($data['email'], $uid);
        }

        S_user($uid, $user);
        S_company($uid, $companyInfo);

        return $this->apiReturn(0, '更新成功');
    }

    /**
     * 后台用户手机注册
     */
    public function mobileRegAdmin($account_type = 1)
    {
        $type = 1; //手机注册
        $account = I('request.account', '', 'trim');
        $password = I('request.password', '', 'trim');
        $platform = I('request.pf', -1, 'intval');
        $parent_id = I('request.parent_id', 0, 'intval');
        $sale_uid = I('request.sale_uid', 0, 'intval');
        $invite_uid = I('invite_uid');
        $res = $this->checkAccount($type, 1);//不需校验短信验证码
        if ($res['err_code'] == 0) {
            //短信验证码
            /*if (empty($sms_verify)) {
                return $this->apiReturn(11007, '请输入短信验证码');
            }*/
            //校验短信验证码
            /*$code = session_sms($account.'.1');
            if ($code !== pwdhash($sms_verify, C('SMS_SALT'))) {
                return $this->apiReturn(11015, '短信验证码错误，请重新获取');
            }*/
            //验证成功，清除短信验证码
            //session_sms($account.'.1', null);
            //注册成功,插入数据库和redis
            $UserMainModel = D('UserMain');
            $reg_user_info = array(
                'account'   => $account,
                'password'  => $password,
                'pf'        => $platform,
                'parent_id' => $parent_id,
                'invite_uid'=> $invite_uid,
                'erp_sale_uid'=> $sale_uid,
            );
            $user_id = $UserMainModel->regSuccess(1, $reg_user_info);
            return $this->apiReturn(0, '成功', $user_id);
            /*if ($account_type == 1) {
                A('Home/Login')->loginAct($user_id, $platform, $account_type);
                return $this->apiReturn(0, '成功');
            } else {
                //交由调用本程序执行登录
                return $this->apiReturn(0, '成功', $user_id);
            }*/
        } else {
            return $this->apiReturn($res['err_code'], $res['err_msg'], '');
        }
    }
    /**
     * 手机注册
     */
    public function mobileReg($account_type = 1)
    {
        $type = 1; //手机注册
        $intlCode = I('request.intl_code', '', 'trim'); // 国际号码
        $account = I('request.account', '', 'trim'); // 手机注册号码

        $account = get_inte_mobile($account, $intlCode);
        $password = I('request.password', '', 'trim');
        // $repassword = I('request.repassword', '', 'trim');
        $platform = platform();
        $sms_verify = I('request.sms_verify', '');
        $invite_uid = I('invite_uid');
        $anonymous_id = I('anonymous_id','','trim');
        $invite_code = I('yqcode', ''); // 用户邀请码

        if ($invite_code) {
            $sale_id = A('Home/Public')->decodeInviteCode(strtolower($invite_code));

            if (!$sale_id) {
                return $this->apiReturn(-3, '邀请码错误');
            }
        }

        $res = $this->checkAccount($type);
        if (in_array($res['err_code'], array(0, 12003, 12026))) {
            //短信验证码
            if (empty($sms_verify)) {
                $this->behavior_push(2, 11007, '请输入短信验证码');
                return $this->apiReturn(11007, '请输入短信验证码');
            }
            //校验短信验证码
//            $code = session_sms($account.'.2');
            $code1 = session_sms($account.'.1');
            $code2 = session_sms($account.'.2');
            $code = $code2 ? $code2 : $code1;
            if ($code !== pwdhash($sms_verify, C('SMS_SALT'))) {
                return $this->apiReturn(11015, '短信验证码错误，请重新获取');
            }
            //验证成功，清除短信验证码
            session_sms($account.'.2', null);

            $UserMainModel = D('UserMain');
            if ($res['err_code'] == 12003) {//竞调用户转普通用户
                $user_id = $res['user_id'];
                $UserMainModel->setUserInfo($user_id, array('is_type' => 0));
                dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.jingdiao'), '竞调用户 “'.$res['mobile'].'”发生注册（登录/下单）行为，转换为正常用户了，去看看吧~');
            } elseif ($res['err_code'] == 12026) { // 线下已存在的账号注册
                $user_id = $res['user_id'];

                // 更新密码
                $reg_user_info = array(
                    'user_id'  => $user_id,
                    'account'  => $account,
                    'password' => $password,
                );
                $res = $UserMainModel->updateUserPwd($reg_user_info);
                if ($res === false) return $this->apiReturn(11016, '注册更新密码失败');
            } else {
                //注册成功,插入数据库和redis
                $reg_user_info = array(
                    'account'   => $account,
                    'password'  => $password,
                    'pf'        => $platform,
                    'invite_uid'=> $invite_uid,
                    'anonymous_id'=>$anonymous_id
                );
                $user_id = $UserMainModel->regSuccess(1, $reg_user_info);
            }

            if (!empty($user_id)) {
                //单位时间内注册累加
                $this->regGateway();
            }
            $this->behavior_push(2, 0, array('account' => $account));
            //邀约成功后短信以及站内信通知
            if ($invite_uid) {
                //短信
                //邀请人
                // $invite_info = S_user($invite_uid);
                $invite_info = $UserMainModel->getUserInfo($invite_uid);
                $invite_mobile = $invite_info['mobile'];
                if ($invite_mobile) {
                    $this->sendSms($invite_mobile, 5, array('telphone' => $account, 'type' => 1), 2);
                }
                //被邀请人
                $this->sendSms($account, 6, array('telphone' => $account, 'type' => 1), 2);
            //站内信
                $check['pf'] = platform();
                $check['k1'] = time();
                $check['k2'] = pwdhash($check['k1'], C('SUPER_AUTH_KEY'));
                $check['inviter_uid'] = $invite_uid;
                $check['invitee_uid'] = $user_id;
                $check['type'] = 1;
                //邀请人
                $res = post_curl(API_DOMAIN.'/message/message/send', $check);
                if (!empty($res)) {
                    $res = json_decode($res, true);
                }
                //被邀请人
                $check['type'] = 2;
                $res = post_curl(API_DOMAIN.'/message/message/send', $check);
                if (!empty($res)) {
                    $res = json_decode($res, true);
                }
            }
            if ($account_type == 1) {
                A('Home/Login')->loginAct($user_id, $platform, $account_type);
                return $this->apiReturn(0, '成功', ['token' => cookie('ucenter_token')]);
            } else {
                //交由调用本程序执行登录
                //是否注册 写入cookie   bindQQ 和 bindWechat 时取出返回
                cookie('user_type', 1);
                return $this->apiReturn(0, '成功', $user_id);
            }
        } else {
//            $this->behavior_push(2, $res['err_code'], $res['err_msg']);
            $this->pushReportMonitorLog(
                [
                    "interface_type"=>"2",
                    "err_msg"=>$res['err_msg'],
                    "err_code"=>$res['err_code'],
                    "user_name"=>$account,

                ]
            );
            return $this->apiReturn($res['err_code'], $res['err_msg'], '');
        }
    }

    /**
     * [进口报关手机免密码自动注册]
     * @return [type] [description]
     */
    public function customMobileReg($mobile=0)
    {
        $mobile = empty($mobile)?I('post.mobile', '', 'trim'):$mobile;
        $platform = I('post.platform', -1, 'intval');
        $anonymous_id = I('anonymous_id','','trim');

        if (empty($mobile) && $platform == -1 ) {
            $this->behavior_push(2, 11009, '参数缺失，请您稍后重试');
            return $this->apiReturn(11009, '参数缺失，请您稍后重试');
        }

        // 清除之前的短信验证码
        session_sms($mobile, null);

        $UserMainModel = D('Home/UserMain');
        $reg_user_info = array(
            'account'   => $mobile,
            'password'  => '',
            'pf'        => $platform,
            'anonymous_id' => $anonymous_id
        );
        $user_id = $UserMainModel->regSuccess(1, $reg_user_info);

        if (isset($user_id)) {
            $this->behavior_push(2, 0, array('account' => $mobile));
            return $this->apiReturn(0, '自动注册成功', $user_id);
        } else {
            $this->behavior_push(2, 1, '自动注册失败');
            return $this->apiReturn(1, '自动注册失败');
        }
    }

    /**
     * @param string $type [类型 1：手机，2：邮箱]
     * 校验注册手机或邮箱数据合格性
     */
    public function checkAccount($type, $isAdmin = 0)
    {
        $platform = I('request.pf', -1, 'intval');
        $password = I('request.password', '', 'trim');
        // $repassword = I('request.repassword', '', 'trim');
        $sms_verify = I('request.sms_verify', '');
        $intlCode = I('request.intl_code', '', 'trim');
        if (!$type) {
            return $this->apiReturn(12012, '网络错误，请您重试！', '');
        }
        //缺失平台
        if (!C('PLAT_FORM.'. $platform)) {
            return $this->apiReturn(11009, '网络错误，请您稍后重试');
        }
        if ($type == '1') {
            $mobile = I('request.account');

            //是否为空
            if (!$mobile) {
                return $this->apiReturn(12001, '请输入您常用的手机号码', '');
            }

            $mobile = get_inte_mobile($mobile, $intlCode);

            //是否格式正确
            if (!is_mobile($mobile)) {
                return $this->apiReturn(11006, '手机号码格式错误，请重新输入', '');
            }

            if (!$isAdmin) {
                if (empty($sms_verify)) {
                    return $this->apiReturn(11007, '请输入短信验证码');
                }
            }
            if (empty($sms_verify)) {//手机免密码注册，验证码或登录密码至少有一个
                if (!$password) {
                    $resp = array('err_code' => 12006, 'err_msg' => '请设置您的登录密码');
                    return $resp;
                }
                //密码位数是否正确 大于6
                if (intval(strlen($password)) < 6 ) {
                    $resp = array('err_code' => 12007, 'err_msg' => '密码格式6-20字符，请重新设置');
                    return $resp;
                }
            }
            //手机是否已经被注册
            $UserMainModel = D('UserMain');
            $user_id = $UserMainModel->getAccountUserId($mobile);
            $info = $UserMainModel->getUserInfo($user_id);
            if ($info['is_type'] == 1) {//非竞调账户 , 竞调用户注册阶段转普通用户
                $resp = array('err_code' => 12003, 'err_msg' => '该手机号码已注册，请直接登录！', 'user_id' => $user_id, 'mobile' => $mobile);
                return $resp;

            // } elseif (!empty($user_id) && $info['channel_type'] == 1) {
            } elseif (!empty($user_id)) {
                $resp = array('err_code' => 12002, 'err_msg' => '该手机号码已注册，请直接登录！');
                return $resp;
            }
            // elseif (!empty($user_id) && in_array($info['channel_type'], [2, 3, 4])) { // 线下账号
            //     $resp = array('err_code' => 12026, 'err_msg' => '该手机号码为线下账号，已注册', 'user_id' => $user_id);
            //     return $resp;
            // }
        } elseif ($type == '2') {
            $email = I('request.email');
            //邮箱名称是否为空
            if (!$email) {
                $resp = array('err_code' => 12009, 'err_msg' => '请输入邮箱');
                return $resp;
            }

            //邮箱名称是否正确
            if (!is_email($email)) {
                $resp = array('err_code' => 12010, 'err_msg' => '邮箱格式不正确');
                return $resp;
            }

            //邮箱是否已经被注册
            $UserMainModel = D('UserMain');
            $user_id = $UserMainModel->getAccountUserId($email);
            $info = $UserMainModel->getUserInfo($user_id);
            // if (!empty($user_id) && $info['channel_type'] == 1) {
            if (!empty($user_id)) {
                $resp = array('err_code' => 12011, 'err_msg' => '该邮箱已经注册，请直接登录！');
                return $resp;
            }
            //  elseif (!empty($user_id) && in_array($info['channel_type'], [2, 3, 4])) { // 线下账号
            //     $resp = array('err_code' => 12026, 'err_msg' => '该邮箱为线下账号，已注册', 'user_id' => $user_id);
            //     return $resp;
            // }

            if (!$password) {
                $resp = array('err_code' => 12006, 'err_msg' => '请设置您的登录密码');
                return $resp;
            }
            //密码位数是否正确 大于6
            if (intval(strlen($password)) < 6 ) {
                $resp = array('err_code' => 12007, 'err_msg' => '密码格式6-20字符，请重新设置');
                return $resp;
            }
        } elseif ($type == '3') {
            $user_name = I('request.account');
            if (!$user_name) {
                $resp = array('err_code' => 12021, 'err_msg' => '请输入账号名');
                return $resp;
            }
            if (is_email($user_name)) {
                $resp = array('err_code' => 12022, 'err_msg' => '账号不能为邮箱');
                return $resp;
            }
            if (is_mobile($user_name)) {
                return $this->apiReturn(12023, '账号不能为手机');
            }
            if (S_account($user_name)) {
                return $this->apiReturn(12024, '该账号已注册，请更换账号');
            }
            if (!$password) {
                $resp = array('err_code' => 12006, 'err_msg' => '请设置您的登录密码');
                return $resp;
            }
            //密码位数是否正确 大于6
            if (intval(strlen($password)) < 6 ) {
                $resp = array('err_code' => 12007, 'err_msg' => '密码格式6-20字符，请重新设置');
                return $resp;
            }
        }

        // if (!$isAdmin) {
        //     //两次输入密码是否一致
        //     if (!empty($password) && !empty($repassword) && $password !== $repassword) {
        //         $resp = array('err_code' => 12008, 'err_msg' => '两次输入密码不一致！');
        //         return $resp;
        //     }
        // }
        $resp = array('err_code' => 0, 'err_msg' => '成功');
        return $resp;
    }

    public function check_company(){
        //检查手机是否注册
        $UserMainModel = D('UserMain');
        $user_id = $UserMainModel->getAccountUserId($_REQUEST['mobile']);

        if(!empty($user_id)){


            //检查是否已经填写公司名称
            $UserCompanyModel = D('UserCompany');
            $info = $UserCompanyModel->where(array('user_id'=>$user_id))->find();

            $userMain = D('UserMain');
            $info_main = $userMain->where(array('user_id'=>$user_id))->find();
        }

        $data['mobile'] = strlen($info['com_name'])>0?False:True;
        $data['email'] = strlen($info_main['email'])>0?False:True;


        echo json_encode(array('err_code'=>0,'data'=>$data));

    }

    /**
     * 发送激活邮件
     */
    public function activateEmail()
    {
        $email = I('request.email', '', 'trim');
        $type = I('request.type', '', 'trim');
        $type = strval($type);
        // $user_id = S_account($email);
        // $info = S_user($user_id);

        $UserMainModel = D('UserMain');
        $user_id = $UserMainModel->getAccountUserId($email);
        $info = $UserMainModel->getUserInfo($user_id);

        if (!$type) {
            $type = '1';
        }
        if ($type === '1') {
            if (!S_account($email)) {
                return $this->apiReturn(12018, '该邮箱尚未注册', '');
            }

            if ($info['status'] == '1') {
                return $this->apiReturn(12020, '该邮箱已经激活', '');
            }
        } elseif ($type === '2') { //邮箱绑定

        } elseif ($type === '3') { //忘记密码
            if (!S_account($email)) {
                return $this->apiReturn(12018, '该邮箱尚未注册', '');
            }
        }
        $res = $this->activateOperationEmail($type, $user_id);
        if ($res['err_code'] !== '0') {
            return $this->apiReturn($res['err_code'], $res['err_msg']);die;
        }
        return $this->apiReturn(0, '邮件发送成功~', '');
    }
    /**
     * 邮箱激活操作
     * @param type 1:注册激活邮箱，2:绑定邮箱，3:忘记密码
     */
    public function activateOperationEmail($type, $user_id, $email = '')
    {
        if (!$type || !$user_id) {
            return false;
        }

        //初始化邮箱发送次数
        // $info = S_user($user_id);
        $UserMainModel = D('UserMain');
        $info = $UserMainModel->getUserInfo($user_id);
        $emailErrorType = 'email_error_'.$type;
        $emailErrorTimeType = 'email_error_time_'.$type;

        if ($info[$emailErrorTimeType] < time()) {
            $info[$emailErrorTimeType] = mktime(0, 0, -1, date('m'), date('d') + 1, date('Y'));//一天内
            $info[$emailErrorType] = 0;
            S_user($user_id, $info);

        } elseif (intval($info[$emailErrorType]) > 3) {//今日发送3次以上不发邮件
            return $this->apiReturn(12019, '您发送邮件过于频繁，已超过今日发送最大次数', '');
        }
        D('UserToken')->activateEmail($user_id, $type, $email);//发送激活邮件
        return $this->apiReturn(0, '成功', '');
    }
    /**
     * 邮箱注册
     */
    public function emailReg()
    {
        $type = 2; //邮箱注册
        $email = I('request.email');
        $password = I('request.password');
        // $repassword = I('request.repassword');
        $tag = I('request.pf', -1, 'intval');
        $invite_uid = I('invite_uid');
        $anonymous_id = I('anonymous_id','','trim');
        $verify = I('verify', false);
        $res = $this->regGateway($verify, $type);
        if ($res !== true) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        F('email_invite_uid', $invite_uid);
        if (!$tag) {
            $tag = 0;//默认pc
        }

        $invite_code = I('yqcode', ''); // 用户邀请码

        if ($invite_code) {
            $sale_id = A('Home/Public')->decodeInviteCode(strtolower($invite_code));

            if (!$sale_id) {
                return $this->apiReturn(-3, '邀请码错误');
            }
        }

        $res = $this->checkAccount($type);
        if (in_array($res['err_code'], [0, 12026])) {
            $UserMainModel = D('UserMain');
            if ($res['err_code'] == 12026) { // 线下已存在的账号注册
                $user_id = $res['user_id'];

                // 更新密码
                $reg_user_info = array(
                    'user_id'  => $user_id,
                    'account'  => $email,
                    'password' => $password,
                );
                $res = $UserMainModel->updateUserPwd($reg_user_info);
                if ($res === false) return $this->apiReturn(11016, '注册更新密码失败');
            } else {
                $reg_user_info = array(
                    'account'   => $email,
                    'password'  => $password,
                    'pf'        => $tag,
                    'invite_uid'=> $invite_uid,
                    'anonymous_id'=>$anonymous_id
                );
                $user_id = $UserMainModel->regSuccess($type, $reg_user_info);
            }

            //邀约成功后短信以及站内信通知
            if ($invite_uid) {
                //短信
                //邀请人
                // $invite_info = S_user($invite_uid);
                $invite_info = $UserMainModel->getUserInfo($invite_uid);
                $invite_mobile = $invite_info['mobile'];
                if ($invite_mobile) {
                    $this->sendSms($invite_mobile, 5, array('telphone' => $email, 'type' => 1), 2);
                }
            //站内信
                $check['pf'] = platform();
                $check['k1'] = time();
                $check['k2'] = pwdhash($check['k1'], C('SUPER_AUTH_KEY'));
                $check['inviter_uid'] = $invite_uid;
                $check['invitee_uid'] = $user_id;
                $check['type'] = 1;
                //邀请人
                $res = post_curl(API_DOMAIN.'/message/message/send', $check);
                if (!empty($res)) {
                    $res = json_decode($res, true);
                }
                //被邀请人
                $check['type'] = 2;
                $res = post_curl(API_DOMAIN.'/message/message/send', $check);
                if (!empty($res)) {
                    $res = json_decode($res, true);
                }
            }
            if ($user_id) { //新增成功
                //单位时间内注册累加
                $this->regGateway();
                D('UserToken')->activateEmail($user_id);//发送激活邮件
                $this->behavior_push(2, 0, array('account' => $email));
                return $this->apiReturn(0, '注册成功', '');
            } else {
                $this->behavior_push(2, 12014, '插入数据库新增会员失败');
                return $this->apiReturn(12014, '插入数据库新增会员失败', '');
            }
        } else {
//            $this->behavior_push(2, $res['err_code'], $res['err_msg']);
            $this->pushReportMonitorLog(
                [
                    "interface_type"=>"2",
                    "err_msg"=>$res['err_msg'],
                    "err_code"=>$res['err_code'],
                    "user_name"=>$email,

                ]
            );
            return $this->apiReturn($res['err_code'],$res['err_msg'],'');
        }
    }

    /**
     * 注册邮件校验激活
     */
    public function verifyEmail()
    {
        $res = A('Public')->checkVerifyEmail();
        if ($res['err_code'] !== 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        $platform = 1;//默认pc
        $userTokenInfo = $res['data'];
        if ($userTokenInfo) {
            $save = array('status' => 1);

            $UserMainModel = D('UserMain');
            $re = $UserMainModel->where(array('user_id' => $userTokenInfo['user_id']))->save($save);
            if ($re) {
                $user_id = $userTokenInfo['user_id'];
                // $userData = S_user($user_id);
                $userData = $UserMainModel->getUserInfo($user_id);
                $userData['status'] = '1';
                S_user($user_id, $userData); //更换status
                //记录登录日志
                A('Login')->loginAct($user_id, $platform, 2);
                $this->addUserPoint($user_id);
                regIssueCoupon($user_id,$platform);
                $this->construct_report_data($user_id,$userData);
                return $this->apiReturn(0, '激活成功', ['point'=>C('ISSUE_POINT.REG')]);
            } else {
                return $this->apiReturn(42004, '写入数据库失败', '');
            }
        } else {
            return $this->apiReturn(42004, '写入token数据库失败', '');
        }
    }

    /** 构建及上报数据到sensors
     * @param $user_id
     * @param $userData
     */
    private function construct_report_data($user_id,$userData){
        $properties = array(
            'event'         => 'signUp',
            'user_id'       => $user_id,
            'anonymous_id'  => !empty($userData['anonymous_id'])?$userData['anonymous_id']:'',
            'Phone'         => '',
            'email'         => !empty($userData['email'])?$userData['email']:'',
            'register_type' => 'email',
            'signUp_ptag_click' => ptag(),
            'is_free_password' => false,
            'mob_type'      => false,
            'login_status'  => '',
            'first_send_email'  => false
        );
        $this->report_data($properties);
    }

    /**
     * 忘记密码邮件校验
     */
    public function activateForgetEmail()
    {
        $res = A('Public')->checkVerifyEmail();
        if ($res['err_code'] !== 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);die;
        }
        $userTokenInfo = $res['data'];
        $platform = 1;//默认pc
        if ($userTokenInfo) {
            return $this->apiReturn(0, '邮箱校验成功', '');
        } else {
            return $this->apiReturn(42004, '写入token数据库失败', '');
        }
    }
    /**
     * 获取国际短信区域
     */
    public function getOverseaMsgArea()
    {
        $OVERSEA_MSG_CODE = C('OVERSEA_MSG_CODE');
        return $this->apiReturn(0, '获取成功', $OVERSEA_MSG_CODE);
    }

    // 竞调用户转正常，并同步到CRM
    public function switchUser()
    {
        $account      = I('account', '');
        $account_type = I('account_type', 1); // 1-手机，2-邮箱
        $sale_id      = I('sale_id', 0);

        $field = $account_type == 1 ? 'mobile' : 'email';
        $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件

        try {
            $UserMainModel    = D('UserMain');
            $UserInfoModel    = D('UserInfo');
            $UserCompanyModel = D('UserCompany');

            $user_id = $UserMainModel->where([$field => $account])->getField('user_id');

            $UserMainModel->setUserInfo($user_id, array('is_type' => 0));
            $user_info = $UserMainModel->getUserInfo($user_id);
            $user_info['is_type'] = 0;
            S_user($user_id, $user_info); // 修改缓存值

            // 检查用户扩展表
            $ui_id = $UserInfoModel->where(['user_id' => $user_id])->getField('ui_id');

            if ($ui_id) {
                $UserInfoModel->where(['user_id' => $user_id])->save(['sale_id' => $sale_id]);
            } else {
                $UserInfoModel->add(['user_id' => $user_id, 'sale_id' => $sale_id]);
            }

            // 检查用户公司表
            $com_id = $UserCompanyModel->where(['user_id' => $user_id])->getField('com_id');

            if (!$com_id) {
                $UserCompanyModel->add(['user_id' => $user_id]);
            }

            // 推入到队列
            $queue_name = C('CRM_PUSH_USER');
            $RbmqModel  = D('Common/Rbmq');

            $data['user_id'] = intval($user_id);
            $res = $RbmqModel->queue($queue_name)->push($data, $queue_name);

            if ($res) {
                $msg_text = '会员系统注册时将竞调用户转为正常，并推送到队列任务成功，用户ID：'.$user_id;
            } else {
                $msg_text = '会员系统注册时将竞调用户转为正常，并推送到队列任务失败，用户ID：'.$user_id;
            }

            // dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.crm_mq'), $msg_text);

            \Think\Log::write($msg_text, INFO, '', $path);
        } catch (\Exception $e) {
            // dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.crm_mq'), '会员系统注册时将竞调用户（'.$account.'）转为正常，推送到队列任务失败，错误信息：'.$e->getMessage());

            $msg_text = '会员系统注册时将竞调用户（' . $account . '）转为正常，推送到队列任务失败，错误信息：' . $e->getMessage();
            \Think\Log::write($msg_text, INFO, '', $path);

            return $this->apiReturn(-1, '竞调用户（'.$account.'）转为正常，推送到队列任务失败，错误信息：'.$e->getMessage());
        }

        return $this->apiReturn(0, '推送CRM成功');
    }

}
