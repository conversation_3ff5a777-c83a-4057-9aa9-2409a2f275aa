<?php
/**
 * 充值
 */
namespace Home\Controller;

use Home\Controller\BaseController;
use Payment\Cpcn;

class WalletController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        $action = strtolower(ACTION_NAME);
        if (!$this->auth() && !in_array(strtolower(CONTROLLER_NAME), array('intraface')) && !in_array($action, array('operationrecharge'))) {
            //没有auth权限无法运行的接口
            if (in_array($action, array('issue', 'getliexinbalance', 'orderpay', 'setwallet', 'setwithdrawfinish', 'getwalletorder', 'setrecharge', 'setrefund'))) {
                $this->_empty();
            }

            if (in_array($action, array('safesms','checksafesms','info','check','lists','orderinfo','log', 'issue'))) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            // 检查登录
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }

            if (!in_array($action, array('info', 'check', 'activate', 'log'))) {
                // 检查钱包激活
                $res = $this->check();
                if ($res['err_code'] != 0) {
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
            }

            if (in_array($action, array('changesafemobile', 'changepwd', 'bindbankcard'))) {
                //需要验证安全手机
                $user_id = cookie('uid');
                $token = I('token', '', 'trim');
                if ($action == 'changepwd') {
                    $mode = I('mode', '', 'trim');
                }
                $mode = !empty($mode) ? $mode .= '_' : '';
                $verify_token = S_verifysafe($user_id);
                if (empty($verify_token) || $verify_token != $mode.$token) {
                    return $this->apiReturn(180053, '安全手机验证码校验失败');
                }
            }
        }
    }

    /**
     * 发放非充值性金额
     * @return [type] [description]
     */
    public function issue()
    {
        $user_id = I('user_id');
        $amount = I('amount');
        $relevance_sn = I('relevance_sn', '');
        $remark = I('remark', '');
        $UserInfoModel = D('UserInfo');
        $UserWalletModel = D('UserWallet');
        $UserWalletLogModel = D('UserWalletLog');
        if (empty($user_id)) {
            return $this->apiReturn(180061, '用户不存在，请确认用户信息');
        }
        if ($amount <= 0) {
            return $this->apiReturn(-1, '发放金额不能小于等于0');
        }
        $map = array(
            'serial_number' => $relevance_sn,
        );
        $log = $UserWalletLogModel->getInfoWhere($map, 'wallet_log_id');
        if (!empty($log)) {
            return $this->apiReturn(0, '已发放过');
        }

        //所有用户的非充值性账户余额
        $all_not_recharge_balance = $UserInfoModel->sumNotRechargeUsage();
        //猎芯营销中金账号
        $liexin_balance = $this->getBalance(C('LIEXIN_WALLET_ACCOUNT'));
        $surplus = bcsub(bcadd($all_not_recharge_balance,$amount, 2), $liexin_balance, 2);
        if ($surplus > 0) {
            return $this->apiReturn(180060, '营销账户余额不足，仍需充值金额：'.$surplus);
        }
        $user = S_user($user_id);
        if (empty($user)) {
            return $this->apiReturn(180061, '用户不存在，请确认用户信息');
        }
        $wallet = S_wallet($user_id);
        if (empty($wallet)) {//未激活过的初始化
            $save = array(
                'wallet_status' => -1,
                'wallet_not_recharge' => 0,
                'wallet_earning' => 0,
            );
            S_wallet($user_id, $save);
        }
        $extend = array(
            'pay_name' => '平台转账',
            'pay_time' => time(),
            'sn' => $relevance_sn,
            'serial_number' => $relevance_sn,
            'remark' => $remark,
        );
        $UserWalletModel->startTrans();
        $res = $this->balanceChange($user_id, $amount, 12, $extend);
        if ($res['err_code'] != 0) {
            S_wallet($user_id, $wallet);
            $UserWalletModel->rollback();
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        $UserWalletModel->commit();
        return $this->apiReturn(0, '发放成功');
    }

    /**
     * 获取猎芯账户可用余额
     * @return [type] [description]
     */
    public function getLiexinBalance()
    {
        $UserInfoModel = D('UserInfo');
        //所有用户的非充值性账户余额
        $all_not_recharge_balance = $UserInfoModel->sumNotRechargeUsage();
        //猎芯营销中金账号
        $liexin_balance = $this->getBalance(C('LIEXIN_WALLET_ACCOUNT'));
        $surplus = bcsub($liexin_balance, $all_not_recharge_balance, 2);
        return $this->apiReturn(0, '成功获取可用余额', $surplus);
    }

    /**
     * 运营充值猎芯钱包
     * @return [type] [description]
     */
    public function operationRecharge()
    {
        $sn = I('sn');
        $amount = I('amount');
        $data = array(
            'order_sn' => $sn,
            'wallet_id' => get_wallet(C('LIEXIN_WALLET_ACCOUNT')),
            'user_id' => C('LIEXIN_WALLET_ACCOUNT'),
            'account_type' => '12',//企业
            'card_type' => '',//企业账号不需要传
            'limit_pay' => '20',//聚合支付限制信用卡类型 10可用，20不可用
            'serial_number' => $sn. '_'. date('Ymd').hash_key(5,2),
            'remark' => $sn.'|运营',
            'usage' => '猎芯运营账户',
            'create_time' => time(),
            'order_amount' => $amount,
        );
        Vendor('payment.cpcn.cpcn');
        $Pay = new Cpcn(Cpcn::MARKET_MODE);
        $config = array(
            'pay_code' => Cpcn::UNIONB2B,
            'pay_mode' => 1,
        );
        $str = $Pay->get_code($data, $config);
        echo $str;
    }

    /**
     * 发送安全手机验证码
     * @return [type] [description]
     */
    public function safeSms()
    {
        $verify = I('verify', '', 'trim');
        $user_id = cookie('uid');
        $wallet = S_wallet($user_id);
        if (empty($wallet) || empty($wallet['safe_mobile'])) {
            return $this->apiReturn(180055, '安全手机号无效');
        }
        $res = $this->toSafeSms($wallet['safe_mobile'], $verify, 3, 0);
        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }

    /**
     * 检测安全手机短信发放次数
     * @return [type] [description]
     */
    public function checkSafeSms()
    {
        $user_id = cookie('uid');
        $wallet = S_wallet($user_id);
        if (empty($wallet) || empty($wallet['safe_mobile'])) {
            return $this->apiReturn(180055, '安全手机号无效');
        }
        $res = $this->toCheckSafeSms($wallet['safe_mobile'], $wallet['intl_code'], 3);
        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }

    /**
     * 订单使用零钱支付
     * @return [type] [description]
     */
    public function orderPay()
    {
        $user_id = I('user_id', 0, 'intval');
        $order_amount = I('order_amount');
        $pay_password = I('pay_password', '', 'trim');
        $order_id = I('order_id', 0, 'intval');
        $order_sn = I('order_sn', '', 'trim');
        $serial_number = I('serial_number', '', 'trim');//充值性账户流水号
        $serial_number2 = I('serial_number2', '', 'trim');//非充值性账户流水号

        $wallet = S_wallet($user_id);
        if (pwdhash($pay_password, $wallet['pay_salt']) != $wallet['pay_password']) {
            return $this->apiReturn(180031, '支付密码错误');
        }
        $usable = $wallet['wallet_not_recharge'] + $wallet['wallet_balance'] - $wallet['wallet_freeze'] - $wallet['wallet_freeze_not_recharge'];//可用余额
        //本地确认金额
        if ($usable < $order_amount) {
            return $this->apiReturn(180032, '钱包可用余额不足');
        }
        //中金确定金额
        $bank_balance = $this->getBalance($user_id);
        if (floatval($wallet['wallet_not_recharge']) + $bank_balance < $order_amount) {
            return $this->apiReturn(180032, '钱包可用余额不足');
        }

        $UserWalletLogModel = D('UserWalletLog');
        if (!empty($serial_number2)) {
            $map['serial_number'] = array('in', array($serial_number, $serial_number2));
        } else {
            $map['serial_number'] = $serial_number;
        }
        $res = $UserWalletLogModel->getInfoWhere($map, 'wallet_log_id');
        if (!empty($res)) {
            return $this->apiReturn(180059, '订单已支付过');
        }

        $UserWalletLogModel->startTrans();

        $extend = array(
            'id' => $order_id,
            'sn' => $order_sn,
            'serial_number' => $serial_number,
            'serial_number2' => $serial_number2,
            'pay_id' => 9,
            'pay_name' => '钱包支付',
        );
        $res = $this->balanceChange($user_id, -1 * $order_amount, 21, $extend);
        if ($res['err_code'] != 0) {
            $UserWalletLogModel->rollback();
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        //结算中金
        if ($res['data']['wallet_balance_diff'] < 0) {
            $change[] = array(
                'serial_number' => $serial_number,
                'order_sn' => get_wallet($user_id),
                'refund_amount' => abs($res['data']['wallet_balance_diff']),
            );
        }
        if ($res['data']['wallet_not_recharge_diff'] < 0) {
            $change[] = array(
                'serial_number' => $serial_number2,
                'order_sn' => C('LIEXIN_WALLET_ACCOUNT'),
                'refund_amount' => abs($res['data']['wallet_not_recharge_diff']),
            );
        }
        foreach ($change as $v) {
            $sellet = array(
                // 'serial_number' => $serial_number,
                // 'order_sn' => get_wallet($user_id),
                // 'refund_amount' => $order_amount,
                'remark' => $order_sn,
                'user_id' => $user_id
            );
            $sellet['refund_amount'] = price_format($sellet['refund_amount']);//格式化保留2位
            $sellet = array_merge($sellet, $v);
            try {
                $RbmqModel = D('Common/Rbmq');
                $res = $RbmqModel->queue(C('QUEUE_WALLET_SETTLE'))->push($sellet, C('QUEUE_WALLET_SETTLE'));
                if (!$res) {
                    S_wallet($user_id, $wallet);
                    $UserWalletLogModel->rollback();
                    return $this->apiReturn(180035, '结算失败');
                }
            } catch (\Exception $e) {
                S_wallet($user_id, $wallet);
                $UserWalletLogModel->rollback();
                return $this->apiReturn(180035, '结算失败');
            }
            
        }
        $UserWalletLogModel->commit();
        return $this->apiReturn(0, '支付成功');
    }

    /**
     * 钱包信息查询
     * @return [type] [description]
     */
    public function info()
    {
        $user_id = cookie('uid');
        $wallet = S_wallet($user_id);
        if (!empty($wallet['wallet_bank_id'])) {
            $UserWalletBankModel = D('UserWalletBank');
            $info = $UserWalletBankModel->getValidInfo($wallet['wallet_bank_id']);
            $bank_name = $info['bank_name'];
            $bank_account = substr($info['bank_account'], -4, 4);
            $bank_user = preg_replace('/^.*([\x{4e00}-\x{9fa5}]{1})$/u', '**$1', $info['bank_user']);
            $bank_mobile = preg_replace('/^(\d{3}|[^\-]+-)(\d{4})(\d+)/', '$1****$3', $info['bank_mobile']);
        }
        //可用余额 = 非充值余额 + 充值余额 - 冻结余额
        $usable = price_format($wallet['wallet_not_recharge'] + $wallet['wallet_balance'] - $wallet['wallet_freeze'] - $wallet['wallet_freeze_not_recharge']);
        //总余额
        $wallet_balance = $wallet['wallet_not_recharge'] + $wallet['wallet_balance'];
        //总冻结
        $wallet_freeze = $wallet['wallet_freeze'] + $wallet['wallet_freeze_not_recharge'];
        $data = array(
            'intl_code' => $wallet['intl_code'],
            'safe_mobile' => preg_replace('/^(\d{3}|[^\-]+-)(\d{4})(\d+)/', '$1****$3', $wallet['safe_mobile']),
            'wallet_status' => intval($wallet['wallet_status']) != 0 ? intval($wallet['wallet_status']) : -1,
            'wallet_usable' => $usable,
            'wallet_usable_format' => price_format($usable, 1),
            'wallet_balance' => price_format($wallet_balance),
            'wallet_balance_format' => price_format($wallet_balance, 1),
            'wallet_freeze' => price_format($wallet_freeze),
            'wallet_freeze_format' => price_format($wallet_freeze, 1),
            'wallet_earning' => price_format($wallet['wallet_earning']),
            'wallet_earning_format' => price_format($wallet['wallet_earning'], 1),
            'wallet_expend' => price_format($wallet['wallet_expend']),
            'wallet_expend_format' => price_format($wallet['wallet_expend'], 1),
            'wallet_bank_id' => intval($wallet['wallet_bank_id']),
            'bank_name' => $bank_name,
            'bank_account' => $bank_account,
            'bank_user' => $bank_user,
            'bank_mobile' => $bank_mobile,
        );
        return $this->apiReturn(0, '成功', $data);
    }

    /**
     * 钱包激活
     * @return [type] [description]
     */
    public function activate()
    {
        $user_id = cookie('uid');
        $intl_code = I('intl_code', '0086', 'trim');
        $safe_mobile = I('safe_mobile', '', 'trim');
        $verify_code = I('verify_code', '', 'trim');
        $pay_password = I('pay_password', '', 'trim');
        $reconfirm = I('reconfirm', '', 'trim');

        if (empty($safe_mobile) || !is_mobile($safe_mobile)) {
            return $this->apiReturn(180001, '安全手机号不正确');
        }
        if (empty($pay_password) || strlen($pay_password) < 6) {
            return $this->apiReturn(180001, '密码长度必须大于6位');
        }
        if ($pay_password != $reconfirm) {
            return $this->apiReturn(180052, '请确认两次填写密码一致');
        }
        //安全手机验证码
        $code = session_sms($safe_mobile.'.3');
        if ($code !== pwdhash($verify_code, C('SMS_SALT'))) {
            return $this->apiReturn(180053, '安全手机验证码校验失败');
        }

        $wallet = S_wallet($user_id);
        if (!empty($wallet) && $wallet['wallet_status'] == 1) {
            return $this->apiReturn(180003, '钱包已激活，无需再执行该操作');
        } elseif (!empty($wallet) && $wallet['wallet_status'] != -1) {
            return $this->apiReturn(180003, '钱包状态异常，无法执行该操作，请联系客服处理');
        } elseif (empty($wallet)) {
            $wallet = array();
        }
        $pay_salt = hash_key(32);
        $UserInfoModel = D('UserInfo');
        $has = $UserInfoModel->getOnlySafeMobile($safe_mobile);
        if (!empty($has)) {
            return $this->apiReturn(180057, '安全手机号已被使用，请使用其他手机号');
        }
        $UserInfoModel->startTrans();
        $info = $UserInfoModel->getUserInfo($user_id);
        $data = array(
            'wallet_status' => 1,
            'intl_code' => $intl_code,
            'safe_mobile' => $safe_mobile,
            'pay_password' => pwdhash($pay_password, $pay_salt),
            'pay_salt' => $pay_salt,
            'wallet_activate_time' => $_SERVER['REQUEST_TIME'],
        );
        if (empty($info)) {
            $data['user_id'] = $user_id;
            $res = $UserInfoModel->add($data);
        } else {
            $res = $UserInfoModel->saveUserInfo($user_id, $data);
        }
        if ($res === false) {
            $UserInfoModel->rollback();
            return $this->apiReturn(180002, '激活用户钱包失败');
        }
        $data = array_merge(array(
            'wallet_not_recharge' => 0,
            'wallet_balance' => 0,
            'wallet_freeze_not_recharge' => 0,
            'wallet_freeze' => 0,
            'wallet_bank_id' => 0,
            'wallet_pay_amount' => 0,
            'wallet_refund_amount' => 0,
            'wallet_earning' => 0,
            'wallet_expend' => 0,
        ), $wallet, $data);
        try {
            S_wallet($user_id, $data);
        } catch (\Exception $e) {
            $UserInfoModel->rollback();
            return $this->apiReturn(180004, '钱包激活异常，请联系客服处理');
        }
        $UserInfoModel->commit();
        session_sms($safe_mobile.'.3', null);
        try {
            //发送激活通知
            sendMsg('wallet_activate_notice', array(), $safe_mobile);
        } catch (\Exception $e) {
        }
        return $this->apiReturn(0, '激活成功');
    }

    /**
     * 手机安全验证校验
     * @return [type] [description]
     */
    public function verifySafe()
    {
        $verify_code = I('verify_code', '', 'trim');
        $pay_password = I('pay_password', '', 'trim');
        $user_id = cookie('uid');
        $wallet = S_wallet($user_id);
        if (empty($pay_password) || strlen($pay_password) < 6) {
            return $this->apiReturn(180001, '支付密码长度必须大于6位');
        }
        if (pwdhash($pay_password, $wallet['pay_salt']) != $wallet['pay_password']) {
            return $this->apiReturn(180037, '支付密码验证失败');
        }
        $code = session_sms($wallet['safe_mobile'].'.3');
        if ($code !== pwdhash($verify_code, C('SMS_SALT'))) {
            return $this->apiReturn(180053, '安全手机验证码校验失败');
        }
        session_sms($wallet['safe_mobile'].'.3', null);
        $token = pwdhash($_SERVER['REQUEST_TIME'], $wallet['pay_salt']);
        S_verifysafe($user_id, $token);
        return $this->apiReturn(0, '验证通过', $token);
    }

    /**
     * 重置支付密码验证环节
     * @return [type] [description]
     */
    public function verifyResetSms()
    {
        $user_id = cookie('uid');
        $verify_code = I('verify_code', '', 'trim');
        $wallet = S_wallet($user_id);
        if (empty($wallet) || empty($wallet['safe_mobile'])) {
            return $this->apiReturn(180055, '安全手机号无效');
        }
        $code = session_sms($wallet['safe_mobile'].'.3');
        if ($code !== pwdhash($verify_code, C('SMS_SALT'))) {
            return $this->apiReturn(180053, '安全手机验证码校验失败');
        }
        session_sms($wallet['safe_mobile'].'.3', null);
        $token = pwdhash($_SERVER['REQUEST_TIME'], $wallet['pay_salt']);
        S_verifysafe($user_id, 'forget_'.$token);
        return $this->apiReturn(0, '验证通过', $token);
    }

    /**
     * 修改安全手机号
     * @return [type] [description]
     */
    public function changeSafeMobile()
    {
        $user_id = cookie('uid');
        $intl_code = I('intl_code', '0086', 'trim');
        $safe_mobile = I('safe_mobile', '', 'trim');
        $verify_code = I('verify_code', '', 'trim');
        if (empty($safe_mobile) || !is_mobile($safe_mobile)) {
            return $this->apiReturn(180055, '安全手机号不正确');
        }
        $code = session_sms($safe_mobile.'.3');
        if ($code !== pwdhash($verify_code, C('SMS_SALT'))) {
            return $this->apiReturn(180053, '安全手机验证码校验失败');
        }
        $wallet = S_wallet($user_id);
        if (empty($wallet) || $wallet['wallet_status'] != 1) {
            return $this->apiReturn(180036, '钱包激活状态才可更换手机号');
        }
        $UserInfoModel = D('UserInfo');
        $has = $UserInfoModel->getOnlySafeMobile($safe_mobile);
        if (!empty($has)) {
            $this->pushReportMonitorLog([
                "interface_type"=>"9",
                "err_msg"=>"修改安全手机号失败",
                "err_code"=>180057,
                "remark"=>sprintf("安全手机已被使用，请使用其他手机号"),
            ]);
            return $this->apiReturn(180057, '安全手机已被使用，请使用其他手机号');
        }
        $UserInfoModel->startTrans();
        $data = array(
            'intl_code' => $intl_code,
            'safe_mobile' => $safe_mobile,
        );
        $res = $UserInfoModel->saveUserInfo($user_id, $data);
        if ($res === false) {
            $this->pushReportMonitorLog([
                "interface_type"=>"9",
                "err_msg"=>"修改安全手机号失败",
                "err_code"=>180057,
                "remark"=>sprintf("修改数据库失败 修改内容 %s",json_encode($data)),
            ]);
            return $this->apiReturn(180056, '安全手机修改失败，请联系客服处理');
        }
        $wallet['safe_mobile'] = $safe_mobile;
        try {
            S_wallet($user_id, $wallet);
        } catch (\Exception $e) {
            $UserInfoModel->rollback();
            $this->pushReportMonitorLog([
                "interface_type"=>"9",
                "err_msg"=>"修改安全手机号失败",
                "err_code"=>180057,
                "remark"=>sprintf("写入缓存失败 %s",json_encode($wallet)),
            ]);
            return $this->apiReturn(180054, '安全手机修改失败，请联系客服处理');
        }
        $UserInfoModel->commit();
        session_sms($safe_mobile.'.3', null);
        S_verifysafe($user_id, null);
        return $this->apiReturn(0, '修改成功');
    }

    /**
     * 修改支付密码
     * @return [type] [description]
     */
    public function changePwd()
    {
        $user_id = cookie('uid');
        $pay_password = I('pay_password', '', 'trim');
        $reconfirm = I('reconfirm', '', 'trim');
        if (empty($pay_password) || strlen($pay_password) < 6) {
            return $this->apiReturn(180001, '密码长度必须大于6位');
        }
        if ($pay_password != $reconfirm) {
            return $this->apiReturn(180052, '请确认两次填写密码一致');
        }
        $wallet = S_wallet($user_id);
        if (empty($wallet) || $wallet['wallet_status'] != 1) {
            return $this->apiReturn(180036, '钱包激活状态才可更换密码');
        }
        $pay_salt = hash_key(32);
        $UserInfoModel = D('UserInfo');
        $UserInfoModel->startTrans();
        $data = array(
            'pay_password' => pwdhash($pay_password, $pay_salt),
            'pay_salt' => $pay_salt
        );
        $res = $UserInfoModel->saveUserInfo($user_id, $data);
        if ($res === false) {
            $UserInfoModel->rollback();
            $this->pushReportMonitorLog([
                "interface_type"=>"9",
                "err_msg"=>"修改钱包密码失败",
                "err_code"=>180038,
                "remark"=>sprintf("操作数据库失败"),
            ]);
            return $this->apiReturn(180038, '修改钱包密码失败');
        }
        $wallet = array_merge($wallet, $data);
        try {
            S_wallet($user_id, $wallet);
        } catch (\Exception $e) {
            $UserInfoModel->rollback();
            $this->pushReportMonitorLog([
                "interface_type"=>"9",
                "err_msg"=>"修改钱包密码失败",
                "err_code"=>180039,
                "remark"=>sprintf("写入缓存失败 %s",json_encode($wallet)),
            ]);
            return $this->apiReturn(180039, '钱包修改密码失败，请联系客服处理');
        }
        $UserInfoModel->commit();
        S_verifysafe($user_id, null);
        return $this->apiReturn(0, '修改成功');
    }

    /**
     * 获取校验银行卡短信验证码
     * @return [type] [description]
     */
    public function getBankMsg()
    {
        $user_id = cookie('uid');

        $valid['mobile'] = I('mobile', '', 'trim');
        $valid['bank_user'] = I('bank_user', '', 'trim');
        $valid['bank_id'] = I('bank_id', 0, 'intval');
        $valid['bank_account'] = I('bank_account', '', 'trim');
        $valid['id_type'] = I('id_type', '0', 'trim');
        $valid['id_number'] = I('id_number', '', 'trim');
        $valid['account_type'] = I('account_type', 1, 'intval');
        $valid['bank_province'] = I('bank_province', '', 'trim');
        $valid['bank_city'] = I('bank_city', '', 'trim');
        $valid['bank_address'] = I('bank_address', '', 'trim');
        $valid['serial_number'] = date('YmdHis').hash_key(6, 2);
        $valid['card_type'] = 1;
        $res = $this->verifyBankCard($valid);
        if ($res !== true) {
            return $this->apiReturn(180048, $res);
        }
        unset($valid['card_type']);
        $key = pwdhash(json_encode($valid), 'bank');
        $data = array(
            'access_key' => $key,
            'access_data' => base64_encode(json_encode($valid)),
        );
        return $this->apiReturn(0, '发送成功', $data);
    }

    /**
     * 绑定银行卡
     * @return [type] [description]
     */
    public function bindBankCard()
    {
        $user_id = cookie('uid');
        $wallet_bank_id = I('wallet_bank_id', 0, 'intval');
        $sms_code = I('sms_code', '', 'trim');
        $key = I('access_key', '', 'trim');
        $data = I('access_data', '', 'trim');

        $data = json_decode(base64_decode($data), true);
        $valid_key = pwdhash(json_encode($data), 'bank');
        if ($key != $valid_key) {
            return $this->apiReturn(180049, '数据验证失败，请刷新重试');
        }
        $res = $this->verifyBindBankCard($data['serial_number'], $sms_code);
        if ($res === false || $res['verify_status'] != 40) {
            $parseUrl = parse_url(API_DOMAIN);
            list($protocl) = explode('.', $parseUrl['host'], 2);
            if ($protocl != 'szapi') {
                $this->pushReportMonitorLog([
                    "interface_type"=>"9",
                    "err_msg"=>"绑定银行卡失败",
                    "err_code"=>180050,
                    "remark"=>sprintf("短信验证码错误"),
                ]);
                return $this->apiReturn(180050, '短信验证码错误');
            }
        } elseif ($res['card_type'] != 10) {
            $this->pushReportMonitorLog([
                "interface_type"=>"9",
                "err_msg"=>"绑定银行卡失败",
                "err_code"=>180050,
                "remark"=>sprintf("请使用借记卡"),
            ]);
            return $this->apiReturn(180051, '请使用借记卡');
        }
        $account_type = $data['account_type'];
        $bank_user = $data['bank_user'];
        $bank_id = $data['bank_id'];
        $bank_account = $data['bank_account'];
        $bank_province = $data['bank_province'];
        $bank_city = $data['bank_city'];
        $bank_address = $data['bank_address'];
        $bank_mobile = $data['mobile'];

        $UserWalletBankModel = D('UserWalletBank');
        $UserInfoModel = D('UserInfo');
        $info = $UserWalletBankModel->getInfo($wallet_bank_id);
        $data = array(
            'account_type' => $account_type,
            'bank_user' => $bank_user,
            'bank_name' => C('CPCN_BANK_B2C.'.$bank_id),
            'bank_account' => $bank_account,
            'bank_province' => $bank_province,
            'bank_city' => $bank_city,
            'bank_address' => $bank_address,
            'bank_mobile' => $bank_mobile,
            'create_time' => time(),
        );
        $UserWalletBankModel->startTrans();
        if (!empty($wallet_bank_id)) {
            if ($info['user_id'] != $user_id) {
                $UserWalletBankModel->rollback();
                $this->pushReportMonitorLog([
                    "interface_type"=>"9",
                    "err_msg"=>"绑定银行卡失败",
                    "err_code"=>180026,
                    "remark"=>sprintf("非法操作，无法修改"),
                ]);
                return $this->apiReturn(180026, '非法操作，无法修改');
            }
            $data['wallet_bank_id'] = $wallet_bank_id;
            $res = $UserWalletBankModel->save($data);
            if ($res === false) {
                $UserWalletBankModel->rollback();
                $this->pushReportMonitorLog([
                    "interface_type"=>"9",
                    "err_msg"=>"绑定银行卡失败",
                    "err_code"=>180027,
                    "remark"=>sprintf("修改失败"),
                ]);
                return $this->apiReturn(180027, '修改失败');
            }

        } else {
            $data['user_id'] = $user_id;
            $wallet_bank_id = $UserWalletBankModel->add($data);
            if ($wallet_bank_id === false) {
                $UserWalletBankModel->rollback();
                $this->pushReportMonitorLog([
                    "interface_type"=>"9",
                    "err_msg"=>"绑定银行卡失败",
                    "err_code"=>180028,
                    "remark"=>sprintf("添加失败"),
                ]);
                return $this->apiReturn(180028, '添加失败');
            }
        }
        $res = $UserInfoModel->saveUserInfo($user_id, array('wallet_bank_id' => $wallet_bank_id));
        if ($res === false) {
            $UserWalletBankModel->rollback();
            $this->pushReportMonitorLog([
                "interface_type"=>"9",
                "err_msg"=>"绑定银行卡失败",
                "err_code"=>180029,
                "remark"=>sprintf("绑定失败"),
            ]);
            return $this->apiReturn(180029, '绑定失败');
        }
        try {
            $wallet = S_wallet($user_id);
            $wallet['wallet_bank_id'] = $wallet_bank_id;
            S_wallet($user_id, $wallet);
        } catch (\Exception $e) {
            $UserWalletBankModel->rollback();
            $this->pushReportMonitorLog([
                "interface_type"=>"9",
                "err_msg"=>"绑定银行卡失败",
                "err_code"=>180030,
                "remark"=>sprintf("绑定失败"),
            ]);
            return $this->apiReturn(180030, '绑定失败');
        }
        $UserWalletBankModel->commit();
        S_verifysafe($user_id, null);
        return $this->apiReturn(0, '绑定修改成功');
    }

    /**
     * 解绑银行卡
     * @return [type] [description]
     */
    public function unbindBankCard()
    {
        $user_id = cookie('uid');
        $wallet_bank_id = I('wallet_bank_id', 0, 'intval');
        $UserWalletBankModel = D('UserWalletBank');
        $UserInfoModel = D('UserInfo');
        $info = $UserWalletBankModel->getInfo($wallet_bank_id);
        $UserWalletBankModel->startTrans();
        if (!empty($wallet_bank_id)) {
            if ($info['user_id'] != $user_id) {
                $UserWalletBankModel->rollback();
                $this->pushReportMonitorLog([
                    "interface_type"=>"9",
                    "err_msg"=>"解绑银行卡失败",
                    "err_code"=>180026,
                    "remark"=>sprintf("非法操作，无法修改"),
                ]);
                return $this->apiReturn(180026, '非法操作，无法修改');
            }
            $data['wallet_bank_id'] = $wallet_bank_id;
            $data['status'] = -1;
            $res = $UserWalletBankModel->save($data);
            if ($res === false) {
                $UserWalletBankModel->rollback();
                $this->pushReportMonitorLog([
                    "interface_type"=>"9",
                    "err_msg"=>"解绑银行卡失败",
                    "err_code"=>180027,
                    "remark"=>sprintf("修改失败"),
                ]);
                return $this->apiReturn(180027, '修改失败');
            }
        }
        $res = $UserInfoModel->saveUserInfo($user_id, array('wallet_bank_id' => 0));
        if ($res === false) {
            $UserWalletBankModel->rollback();
            $this->pushReportMonitorLog([
                "interface_type"=>"9",
                "err_msg"=>"解绑银行卡失败",
                "err_code"=>180029,
                "remark"=>sprintf("绑定失败"),
            ]);
            return $this->apiReturn(180029, '绑定失败');
        }
        try {
            $wallet = S_wallet($user_id);
            $wallet['wallet_bank_id'] = 0;
            S_wallet($user_id, $wallet);
        } catch (\Exception $e) {
            $UserWalletBankModel->rollback();
            $this->pushReportMonitorLog([
                "interface_type"=>"9",
                "err_msg"=>"解绑银行卡失败",
                "err_code"=>180030,
                "remark"=>sprintf("绑定失败"),
            ]);
            return $this->apiReturn(180030, '绑定失败');
        }
        $UserWalletBankModel->commit();
        return $this->apiReturn(0, '解除绑定成功');
    }

    /**
     * 检查是否激活钱包
     * @return [type] [description]
     */
    public function check()
    {
        $user_id = cookie('uid');
        $info = S_wallet($user_id);
        if ($info['wallet_status'] == -1) {
            return $this->apiReturn(180005, '您尚未激活钱包，赶快激活使用吧！');
        } elseif ($info['wallet_status'] == -2) {
            return $this->apiReturn(180006, '钱包已锁定，暂时无法使用，请联系客服解锁!');
        }
        return $this->apiReturn(0, '已激活');
    }

    /**
     * 充值申请
     * @return [type] [description]
     */
    public function recharge()
    {
        $user_id = cookie('uid');
        $amount = I('amount', 0, 'floatval');
        $order_id = I('order_id', 0, 'intval');
        if ($amount <= 0) {
            return $this->apiReturn(180007, '充值金额必须大于0');
        }
        $amount = price_format($amount);
        $UserWalletModel = D('UserWallet');
        //一天内同金额未完成充值单不创建多条
//        $map = array(
//            'wallet_type' => 1,
//            'user_id' => $user_id,
//            'amount' => $amount,
//            'status' => 1,
//            'create_time' => array('lt', mktime(0,0,0, date('m'), date('d') + 1, date('Y'))),
//        );
//        $wallet = $UserWalletModel->getInfoWhere($map, 'wallet_id');
//        if (empty($wallet)) {
//            $data = array(
//                'wallet_type' => 1,
//                'user_id' => $user_id,
//                'amount' => $amount,
//                'source' => get_source(),
//                'create_time' => time(),
//            );
//            $wallet_id = $UserWalletModel->createOrder($data, 2);
//            if ($wallet_id === false) {
//                return $this->apiReturn(180008, '创建充值单失败');
//            }
//        } else {
//            $wallet_id = $wallet['wallet_id'];
//        }

        if($order_id > 0){
            $count = $UserWalletModel->where(["user_id"=>$user_id,"wallet_id"=>$order_id])->count("wallet_id");
            if(!$count){
                $this->pushReportMonitorLog([
                    "interface_type"=>"7",
                    "err_msg"=>"创建钱包充值申请单失败",
                    "err_code"=>180008,
                    "remark"=>sprintf('没找到属于你的充值单号:%s',$order_id),

                ]);
                return $this->apiReturn(180008, sprintf('没找到属于你的充值单号:%s',$order_id));
            }else{
                $count = $UserWalletModel->where(["user_id"=>$user_id,"wallet_id"=>$order_id,"status"=>1])->count("wallet_id");
                if($count > 0){
                    return $this->apiReturn(0, '充值申请成功', $order_id);
                }
            }
        }

        //判断用户一天内充值单过多 提醒
        $where = sprintf(" user_id = %s and status = 1 and FROM_UNIXTIME(create_time, '%%Y%%m%%d') = '%s'  ",$user_id,date("Ymd"));
        $currentCount = $UserWalletModel->where($where)->count("wallet_id");
        if($currentCount > C("NOT_PAY_USER_WALLET_NUMS")){
            $this->pushReportMonitorLog([
                "interface_type"=>"7",
                "err_msg"=>"创建钱包充值申请单失败",
                "err_code"=>180008,
                "remark"=>sprintf("您今天未支付的充值单过多,如想继续充值请联系客服！谢谢支持！给用户今天未支付单据数量为 %s",$currentCount),

            ]);
            return $this->apiReturn(180008,"您今天未支付的充值单过多,如想继续充值请联系客服！谢谢支持！");
        }

        $data = array(
            'wallet_type' => 1,
            'user_id' => $user_id,
            'amount' => $amount,
            'source' => get_source(),
            'create_time' => time(),
        );
        $wallet_id = $UserWalletModel->createOrder($data, 2);
        if ($wallet_id === false) {
            return $this->apiReturn(180008, '创建充值单失败');
        }
        return $this->apiReturn(0, '充值申请成功', $wallet_id);
    }

    /**
     * 提现申请
     * @return [type] [description]
     */
    public function withdraw()
    {
        $user_id = cookie('uid');
        $amount = I('amount', 0, 'floatval');
        $pay_password = I('pay_password', '', 'trim');
        if ($amount <= 0) {
            return $this->apiReturn(180009, '提现金额必须大于0');
        }
        $wallet = S_wallet($user_id);
        if (empty($pay_password) || strlen($pay_password) < 6) {
            return $this->apiReturn(180001, '支付密码长度必须大于6位');
        }
        if (pwdhash($pay_password, $wallet['pay_salt']) != $wallet['pay_password']) {
            return $this->apiReturn(180037, '支付密码验证失败');
        }
        $UserWalletModel = D('UserWallet');
        $UserWalletBankModel = D('UserWalletBank');
        $UserInfoModel = D('UserInfo');
        $bank = $UserWalletBankModel->getValidInfo($wallet['wallet_bank_id']);
        if (empty($bank)) {
            return $this->apiReturn(180010, '请先填写收款银行信息');
        }
        //可用余额
        $wallet_usable = $wallet['wallet_not_recharge'] + $wallet['wallet_balance'] - $wallet['wallet_freeze'] - floatval($wallet['wallet_freeze_not_recharge']);
        if ($wallet_usable < $amount) {
            return $this->apiReturn(180032, '钱包可用余额不足');
        }
        //中金校验
        $bank_balance = $this->getBalance($user_id);
        $bank_usable_balance = bcadd($bank_balance, bcsub($wallet['wallet_not_recharge'], $wallet['wallet_freeze_not_recharge'], 2), 2);
        if ($bank_usable_balance < $amount) {
            return $this->apiReturn(180032, '钱包可用余额不足');
        }

        
        $data = array(
            'wallet_type' => 2,
            'user_id' => $user_id,
            'amount' => -1 * $amount,
            'bank_user' => $bank['bank_user'],
            'bank_name' => $bank['bank_name'],
            'bank_account' => $bank['bank_account'],
            'bank_province' => $bank['bank_province'],
            'bank_city' => $bank['bank_city'],
            'bank_address' => $bank['bank_address'],
            'source' => get_source(),
            'create_time' => time(),
            'cpcn_syn' => 1,//发起中金结算申请
        );
        $UserWalletModel->startTrans();
        $wallet_id = $UserWalletModel->createOrder($data, 3);
        if ($wallet_id === false) {
            $UserWalletModel->rollback();
            return $this->apiReturn(180011, '创建提现单失败');
        }

        $wallet_sn = $UserWalletModel->getFieldByWalletId($wallet_id, 'wallet_sn');
        $extend = array(
            'id' => $wallet_id,
            'sn' => $wallet_sn,
            'bank_name' => $bank['bank_name'],
            'bank_account' => $bank['bank_account'],
            'freeze_amount' => $amount,
        );
        //冻结金额
        $res = $this->balanceChange($user_id, 0, 200, $extend);
        if ($res['err_code'] != 0) {
            S_wallet($user_id, $wallet);
            $UserWalletModel->rollback();
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        $UserWalletModel->commit();
        S_verifysafe($user_id, null);
        return $this->apiReturn(0, '提现申请成功', $wallet_id);
    }

    /**
     * 列表
     * @return [type] [description]
     */
    public function lists()
    {
        $p = I('request.p', 1, 'intval');
        $limit = I('limit', 0, 'intval');
        if (!empty($limit)) {
            $p .= ','.$limit;
        }
        $UserWalletModel = D('UserWallet');
        $filter = array('wallet_type', 'stime', 'etime');
        $map = map_filter($filter);
        $user_id = cookie('uid');
        $count = intval($UserWalletModel->getUserCount($user_id, $map));
        $data = $UserWalletModel->getUserList($user_id, $map, $p);
        foreach ($data as &$v) {
            $v['bank_account'] = substr($v['bank_account'], -4, 4);//卡号后4位
            $v['status_val'] = $v['wallet_type'] == 1 ? C('WALLET_RECHARGE_STATUS.'.$v['status']) : C('WALLET_WITHDRAW_STATUS.'.$v['status']);
            $v['amount_format'] = price_format($v['amount'], 1);
            $v['create_time'] = date('Y-m-d H:i:s', $v['create_time']);
        }
        $datas = page_data($data, $count, $p);
        return $this->apiReturn(0, '获取成功', $datas);
    }

    /**
     * 单据内容
     * @return [type] [description]
     */
    public function orderInfo()
    {
        $UserWalletModel = D('UserWallet');
        $user_id = cookie('uid');
        $wallet_id = I('wallet_id', 0, 'intval');
        $wallet_sn = I('wallet_sn', '', 'trim');
        $info = $UserWalletModel->getInfo($wallet_id, $wallet_sn, 'wallet_id, wallet_sn, wallet_type, amount, user_id, status');
        if ($info['user_id'] != $user_id) {
            return $this->apiReturn(180026, '非法操作，无法修改');
        }
        return $this->apiReturn(0, '获取成功', $info);
    }

    /**
     * 日志列表
     * @return [type] [description]
     */
    public function log()
    {
        $p = I('request.p', 1, 'intval');
        $limit = I('limit', 0, 'intval');
        if (!empty($limit)) {
            $p .= ','.$limit;
        }
        $UserWalletLogModel = D('UserWalletLog');
        $filter = array('log_type', 'stime', 'etime');
        $map = map_filter($filter);
        $map['_string'] = 'log_type < 100';
        $user_id = cookie('uid');
        $count = intval($UserWalletLogModel->getUserCount($user_id, $map));
        $data = $UserWalletLogModel->getUserList($user_id, $map, $p);
        foreach ($data as &$v) {
            if ($v['log_type'] == 21) {
                $first_sn = substr($v['relevance_sn'], 0, 1);
                $v['order_sn_type'] = C('ORDER_SN_TYPE.' . $first_sn);//区分支付订单以便跳转详细页
            }
            $v['bank_account'] = substr($v['bank_account'], -4, 4);//卡号后4位
            $v['log_type_val'] = C('WALLET_LOG_TYPE.'.$v['log_type']);
            $v['amount_format'] = price_format($v['amount'], 1);
            $v['user_balance_format'] = price_format($v['user_balance'], 1);
            $v['freeze_amount_format'] = price_format($v['freeze_amount_format'], 1);
            $v['create_time'] = date('Y-m-d H:i:s', $v['create_time']);
        }
        $datas = page_data($data, $count, $p);
        return $this->apiReturn(0, '获取成功', $datas);
    }


    /**
     * 直接修改钱包单据（目前用于标记）
     */
    public function setWallet()
    {
        $wallet_id = I('wallet_id', 0, 'intval');
        $wallet_sn = I('wallet_sn', '', 'trim');
        $type = I('type', 0, 'intval');
        $error = I('error', '', 'trim');
        $UserInfoModel = D('UserInfo');
        $UserWalletModel = D('UserWallet');
        $UserWalletLogModel = D('UserWalletLog');
        $info = $UserWalletModel->getInfo($wallet_id, $wallet_sn, 'wallet_id, wallet_sn, user_id, status, amount, bank_name, bank_account');
        switch ($type) {
            case 1:
                $data = array(
                    'cpcn_syn' => -1,
                    'cpcn_syn_last_time' => time(),
                    'cpcn_error' => $error,
                );
                $error = '标记已同步失败';
                break;

            case 2://暂时不用
                $data = array(
                    'status' => 10,
                    'finish_time' => time(),
                    'cpcn_syn' => -1,
                );
                $error = '修改状态失败';
                break;
            default:
                return $this->apiReturn(180012, '无效类型');
                break;
        }
        if (!empty($wallet_id)) {
            $map['wallet_id'] = $wallet_id;
        } elseif (!empty($wallet_sn)) {
            $map['wallet_sn'] = $wallet_sn;
        }
        $res = $UserWalletModel->where($map)->save($data);
        if ($res === false) {
            return $this->apiReturn(180013, $error);
        }
        return $this->apiReturn(0, '成功');
    }

    /**
     * 提现成功修改余额
     */
    public function setWithdrawFinish()
    {
        $wallet_id = I('wallet_id', 0, 'intval');
        $wallet_sn = I('wallet_sn', '', 'trim');
        $serial_number = I('serial_number', '', 'trim');
        $create_time = I('create_time', 0, 'intval');
        $amount = I('amount', '', 'trim');
        $UserInfoModel = D('UserInfo');
        $UserWalletModel = D('UserWallet');
        $UserWalletLogModel = D('UserWalletLog');
        $info = $UserWalletModel->getInfo($wallet_id, $wallet_sn, 'wallet_id, wallet_sn, user_id, status, amount, bank_name, bank_account');
        if ($info['status'] == 10) {
            return $this->apiReturn(0, '成功');
        }
        $user_id = $info['user_id'];
        $wallet = S_wallet($user_id);

        //校对金额
        $serial_arr = explode('_', $serial_number);
        $log_type = $serial_arr[1];
        $map = array(
            'relevance_sn' => $info['wallet_sn'],
            'log_type' => $log_type,
        );
        $log = $UserWalletLogModel->getInfoWhere($map, 'freeze_amount');
        if ($log['freeze_amount'] != $amount) {
            return $this->apiReturn(180062, '冻结金额与结算金额不一致');
        }

        if ($log_type == 322) {//非充值性 提现冻结
            $withdraw_log_type = 320;//非充值性余额提现
        } else {
            $withdraw_log_type = 220;//充值性余额提现
        }
        //记录日志
        $extend = array(
            'id' => $info['wallet_id'],
            'sn' => $info['wallet_sn'],
            'serial_number' => $serial_number,
            'bank_name' => $info['bank_name'],
            'bank_account' => $info['bank_account'],
            'freeze_amount' => -1 * $amount,//解冻
        );
        $UserWalletModel->startTrans();
        $res = $this->balanceChange($info['user_id'], -1 * $amount, $withdraw_log_type, $extend);
        if ($res['err_code'] != 0) {
            S_wallet($user_id, $wallet);
            $UserWalletModel->rollback();
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        //计算是否整单已退
        $sum_amount = $UserWalletLogModel->sumRelevanceAmount($info['wallet_id'], $info['wallet_sn'], array(220, 320));
        if ($sum_amount == $info['amount']) {
            $extend['serial_number'] = $info['wallet_sn'];
            $extend['freeze_amount'] = $info['amount'];
            $res = $this->balanceChange($info['user_id'], $info['amount'], 20, $extend);
            if ($res['err_code'] != 0) {
                S_wallet($user_id, $wallet);
                $UserWalletModel->rollback();
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }

            //修改单据
            $data = array(
                'status' => 10,
                'finish_time' => time(),
                'cpcn_syn' => -1,
            );
            $map = array(
                'wallet_id' => $info['wallet_id']
            );
            $res = $UserWalletModel->where($map)->save($data);
            if ($res === false) {
                S_wallet($user_id, $wallet);
                $UserWalletModel->rollback();
                return $this->apiReturn(180013, '修改状态失败');
            }
        }
        $UserWalletModel->commit();

        if ($sum_amount == $info['amount']) {
            try {
                //提现成功通知
                $notice['data'] = array(
                    'money' => price_format(abs($info['amount']), 1)
                );
                sendMsg('wallet_withdraw_notice', $notice, $wallet['safe_mobile']);
            } catch (\Exception $e) {
            }
        }
        return $this->apiReturn(0, '成功');
    }

    /**
     * 获取单据信息
     * @return [type] [description]
     */
    public function getWalletOrder()
    {
        $wallet_id = I('wallet_id', 0, 'intval');
        $wallet_sn = I('wallet_sn', '', 'trim');
        $UserWalletModel = D('UserWallet');
        $UserWalletLogModel = D('UserWalletLog');
        $info = $UserWalletModel->getInfo($wallet_id, $wallet_sn);
        //冻结日志
        $map = array(
            'relevance_id' => $wallet_id,
            'relevance_sn' => $info['wallet_sn'],
            'log_type' => array('in', array(222,322)),
        );
        $list = $UserWalletLogModel->getList($map, 'log_type,freeze_amount');
        $info['freeze_list'] = $list;
        return $this->apiReturn(0, '', $info);
    }

    /**
     * 支付成功充值
     */
    public function setRecharge()
    {
        $wallet_id = I('wallet_id', 0, 'intval');
        $wallet_sn = I('wallet_sn', '', 'trim');
        $price = I('price', '', 'trim');
        $pay_order_sn = I('pay_order_sn', '', 'trim');
        $serial_number = I('serial_number', '', 'trim');
        $pay_id = I('pay_id', 0, 'intval');
        $pay_name = I('pay_name', '', 'trim');
        $pay_time = I('pay_time', '', 'trim');

        $UserInfoModel = D('UserInfo');
        $UserWalletModel = D('UserWallet');
        $UserWalletLogModel = D('UserWalletLog');

        $info = $UserWalletModel->getInfo($wallet_id, $wallet_sn);
        if (empty($info)) {
            return $this->apiReturn(180014, '未找到相关订单信息', $wallet_sn);
        } elseif ($info['status'] != 1) {
            return $this->apiReturn(180015, '当前状态无法进行该操作');
        } elseif ($info['wallet_type'] != 1) {
            return $this->apiReturn(180016, '非充值单，操作失败');
        } elseif (bccomp($info['amount'], $price, 2) != 0) {
            return $this->apiReturn(180017, '当前应付金额与实际金额不相等');
        }

        $UserWalletModel->startTrans();
        //单据状态修改
        $map = array(
            'wallet_id' => $info['wallet_id'],
        );
        $save = array(
            'status' => 10,
            'finish_time' => !empty($pay_time) ? $pay_time : $_SERVER['REQUEST_TIME'],
        );
        $res = $UserWalletModel->where($map)->save($save);
        if ($res === false) {
            $UserWalletModel->rollback();
            return $this->apiReturn(180018, '充值异常');
        }
        $user_id = $info['user_id'];
        $wallet = S_wallet($user_id);
        
        $extend = array(
            'id' => $info['wallet_id'],
            'sn' => $wallet_sn,
            'pay_id' => $pay_id,
            'pay_name' => $pay_name,
            'pay_time' => $pay_time,
            'serial_number' => $serial_number,
        );
        $res = $this->balanceChange($info['user_id'], $info['amount'], 10, $extend);
        if ($res['err_code'] != 0) {
            S_wallet($user_id, $wallet);
            $UserWalletModel->rollback();
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        $UserWalletModel->commit();
        return $this->apiReturn(0, '修改成功');
    }

    /**
     * 检查某单据日志记录的合计金额
     * @return [type] [description]
     */
    public function checkLogAmount()
    {
        $sn = I('sn', '', 'trim');
        $log_type = I('log_type', 0, 'intval');
        $UserWalletLogModel = D('UserWalletLog');
        $map = array(
            'relevance_sn' => $sn,
            'log_type' => $log_type,
        );
        $datas = $UserWalletLogModel->getList($map, 'amount');
        $amount = 0;
        foreach ($datas as &$v) {
            $amount += $v['amount'];
        }
        return $this->apiReturn(0, '成功', $amount);
    }

    /**
     * 钱包退款充值——记录退款成功
     * @return [type] [description]
     */
    public function setRefund()
    {
        // $order_id = I('order_id', 0, 'intval');
        $refund_sn = I('refund_sn', '', 'trim');
        $price = I('price', '', 'trim');
        $pay_order_sn = I('pay_order_sn', '', 'trim');
        $serial_number = I('serial_number', '', 'trim');
        $pay_id = I('pay_id', 0, 'intval');
        $pay_name = I('pay_name', '', 'trim');
        $pay_time = I('pay_time', '', 'trim');

        //退款判断
        $words = str_split(substr($refund_sn, 0, 2));
        if ($words[1] == 'Z') {//自营
            //通知财务退款单
            $data = array(
                'status' => 1,
                'refund_sn' => $refund_sn,
                'pay_time' => $pay_time,
                'payment_no' => $pay_order_sn,
                'serial_number' => $serial_number,
                'message' => '',
            );
            $res = $this->setRefundCallback($data);
            if ($res['err_code'] !== 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
            //登记支付流水的退款金额
            $data = array(
                'payment_no' => $pay_order_sn,
                'refund_amount' => $price,
            );
            $res = $this->setPayLogRefundAmount($data);
            if ($res['err_code'] !== 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
            $user_id = $res['data']['user_id'];


        } else {//联营
            //获取退款信息
            $info = $this->getRefund(0, $refund_sn);
            if ($info['err_code'] != 0) {
                return $this->apiReturn($info['err_code'], $info['err_msg']);
            }
            $info = $info['data'];
            $user_id = $info['user_id'];
            //获取流水号信息
            $serial_info = $this->getRefundSerialInfo($serial_number);
            if ($serial_info['err_code'] != 0) {
                return $this->apiReturn($serial_info['err_code'], $serial_info['err_msg']);
            }
            if ($serial_info['data']['refund_status'] == 10) {//已退款过
                return $this->apiReturn(180058, '该流水号已退款');
            }
            //记录流水号
            $res = $this->setRefundFinish($info['refund_id'], $serial_number, $pay_time);
            if ($res['err_code'] !== 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }

        //钱包处理
        $extend = array(
            'id' => $order_id,
            'sn' => $refund_sn,
            'pay_id' => $pay_id,
            'pay_name' => $pay_name,
            'pay_time' => $pay_time,
            'serial_number' => $serial_number,
        );
        $UserWalletLogModel = D('UserWalletLog');
        $UserWalletLogModel->startTrans();
        $wallet = S_wallet($user_id);
        $res = $this->balanceChange($user_id, $price, 11, $extend);
        if ($res['err_code'] != 0) {
            S_wallet($user_id, $wallet);
            $UserWalletLogModel->rollback();
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        $UserWalletLogModel->commit();
        try {
            //退款成功通知
            $notice['data'] = array(
                'money' => $price,
                'order_sn' => $order_sn,
            );
            sendMsg('wallet_refund_notice', $notice, $wallet['safe_mobile']);
        } catch (\Exception $e) {
        }
        return $this->apiReturn(0);
    }

    /**
     * 余额变化调整方法
     * @param  [type] $user_id  用户ID
     * @param  [type] $amount   变化金额 正负区分
     * @param  [type] $log_type 类型
     * @param  array  $extend   id               关联ID
     *                          sn               关联单号
     *                          pay_id           支付ID
     *                          pay_name         支付名
     *                          pay_time         支付时间
     *                          serial_number    流水号
     *                          freeze_amount    冻结金额 正冻 负解
     * @return [type]           [description]
     */
    private function balanceChange($user_id, $amount, $log_type, $extend = array())
    {
        $UserWalletLogModel = D('UserWalletLog');
        $UserInfoModel = D('UserInfo');

        //获取变更前redis的余额
        $wallet = S_wallet($user_id);
// dump($wallet);

        //获取变更前日志的余额 （充值属性）
        $recharge_balance = $wallet['wallet_balance'];//充值余额
        //获取变更前日志的余额 （非充值属性）
        $not_recharge_balance = $wallet['wallet_not_recharge'];//非充值余额
        //获取变更前日志的冻结余额  充值冻结+非充值冻结
        $freeze_balance = bcadd($wallet['wallet_freeze'], $wallet['wallet_freeze_not_recharge'], 2);
        //可用余额(总)  充值余额+非充值-充值冻结-非充值冻结
        $useable_balance = $recharge_balance + $not_recharge_balance - $freeze_balance;
        //redis与数据库日志统计不一致
        // if ($wallet['wallet_balance'] != floatval($recharge_balance)) {
        //     $is_verify = true;//标记后续重新校对
        // }

        //redis先追加充值金额(以日志统计的为标准)
        $old_wallet_not_recharge = $wallet['wallet_not_recharge'];//非充值余额
        $old_wallet_balance = $wallet['wallet_balance'];//充值余额
        $old_wallet_freeze_not_recharge = $wallet['wallet_freeze_not_recharge'];//非充值冻结金额
        $old_wallet_freeze = $wallet['wallet_freeze'];//充值冻结金额
        $old_wallet_pay_amount = $wallet['wallet_pay_amount'];//支付金额
        $old_wallet_refund_amount = $wallet['wallet_refund_amount'];//退款金额
        $old_wallet_earning = $wallet['wallet_earning'];//收入金额（所有正数金额类型）
        $old_wallet_expend = $wallet['wallet_expend'];//支出金额（所有负数金额类型）

        $new_wallet_not_recharge = floatval($not_recharge_balance);//非充值余额
        $new_wallet_balance = floatval($recharge_balance);//充值余额
        $new_useable_balance = floatval($useable_balance) + $amount;//可用余额(总)  充值余额+非充值-充值冻结-非充值冻结
        //决定扣减的账户
        //优先级 支付时 非充值余额 > 充值余额
        //       提现时 充值余额 > 非充值余额 原因：尽量减少客户分批到账
        $surplus = abs($amount);
        if ($log_type == 21) {//支付
            if ($new_wallet_not_recharge >= $surplus) {
                $new_wallet_not_recharge -= $surplus;
            } else {
                $surplus -= $new_wallet_not_recharge;
                $new_wallet_not_recharge = 0;
                $new_wallet_balance -= $surplus;
            }
        } elseif ($log_type == 20) {//提现
            //日志记录，可用余额不变
            $new_useable_balance = floatval($useable_balance);
        } elseif (in_array($log_type, array(10, 11))) {//充值、退款、
            $new_wallet_balance += $amount;
        } elseif (in_array($log_type, array(12))) {//活动返现
            $new_wallet_not_recharge += $amount;
        } elseif (in_array($log_type, array(220, 320))) {//充值性提现、非充值性提现
            $log_type == 220 && $new_wallet_balance += $amount;
            $log_type == 320 && $new_wallet_not_recharge += $amount;
            //提现解冻，因为变更前可用余额已含冻结，所以无需再叠加
            $new_useable_balance = floatval($useable_balance);
        }
        $wallet['wallet_not_recharge'] = $new_wallet_not_recharge;
        $wallet['wallet_balance'] = $new_wallet_balance;

        //改变前后余额
        $wallet_balance_diff = $new_wallet_balance - $old_wallet_balance;
        $wallet_not_recharge_diff = $new_wallet_not_recharge - $old_wallet_not_recharge;


        //统计某类型的支付合计
        $type_balance = floatval($UserWalletLogModel->getUserBalance($user_id, array('log_type' => $log_type)));
        //统计
        if ($log_type == 11) {//退款
            $new_wallet_refund_amount = $type_balance + $amount;//正数
            $wallet['wallet_refund_amount'] = $new_wallet_refund_amount;
            $save['wallet_refund_amount'] = $new_wallet_refund_amount;

        } elseif ($log_type == 21) {//支付
            $new_wallet_pay_amount = abs($type_balance + $amount);//原本负数
            $wallet['wallet_pay_amount'] = $new_wallet_pay_amount;
            $save['wallet_pay_amount'] = $new_wallet_pay_amount;

        } elseif ($log_type == 200) {//提现冻结
            //计算冻结明细
            $surplus = abs($extend['freeze_amount']);
            $useable = bcsub($new_wallet_balance, $old_wallet_freeze, 2);
            if ($useable >= $surplus) {
                $wallet_freeze_diff = $surplus;
                $wallet_freeze_not_recharge_diff = 0;
            } else {
                $surplus = bcsub($surplus, $useable, 2);
                $wallet_freeze_diff = $useable;
                $wallet_freeze_not_recharge_diff = $surplus;
            }
            $new_useable_balance -= $extend['freeze_amount'];
            //用户总冻结金额
            $new_wallet_freeze = $wallet['wallet_freeze'] + $wallet_freeze_diff;
            $new_wallet_freeze_not_recharge = $wallet['wallet_freeze_not_recharge'] + $wallet_freeze_not_recharge_diff;
            $wallet['wallet_freeze_not_recharge'] = $new_wallet_freeze_not_recharge;
            $wallet['wallet_freeze'] = $new_wallet_freeze;
            $save['wallet_freeze_not_recharge'] = $new_wallet_freeze_not_recharge;
            $save['wallet_freeze'] = $new_wallet_freeze;
            // $new_wallet_freeze = $wallet['wallet_freeze'] + $extend['freeze_amount'];//原本正数  冻结变化
            // $wallet['wallet_freeze'] = $new_wallet_freeze;
            // $save['wallet_freeze'] = $new_wallet_freeze;

        } elseif ($log_type == 220) {//充值性提现、非充值性提现
            $wallet['wallet_freeze'] += $amount;
            $save['wallet_freeze'] = $wallet['wallet_freeze'];
            $wallet_freeze_diff = $amount;
        } elseif ($log_type == 320) {
            $wallet['wallet_freeze_not_recharge'] += $amount;
            $save['wallet_freeze_not_recharge'] = $wallet['wallet_freeze_not_recharge'];
            $wallet_freeze_not_recharge_diff = $amount;
        }
// echo 'log_type:',$log_type,'<br/>';
// echo 'useable_balance:',$useable_balance,'<br/>';
// echo 'new_useable_balance:',$new_useable_balance,'<br/>';
// echo 'new_wallet_balance:',$new_wallet_balance,'<br/>';
// echo 'new_wallet_not_recharge:',$new_wallet_not_recharge,'<br/>';
// echo 'wallet_freeze_diff:',$wallet_freeze_diff, '<br/>';
// echo 'wallet_freeze_not_recharge_diff:',$wallet_freeze_not_recharge_diff,'<br/>';
// dump($wallet);
// dump($save);
// die();

        //变化时再统计
        if ($amount != 0) {//排除冻结操作
            if ($amount > 0) {
                //统计收入金额
                $wallet_earning = $UserWalletLogModel->getUserBalance($user_id, 'WALLET_LOG_EARNING');
                $wallet_earning += $amount;
                $wallet['wallet_earning'] = $wallet_earning;
            } else {
                //统计支出金额
                $wallet_expend = $UserWalletLogModel->getUserBalance($user_id, 'WALLET_LOG_EXPEND');
                $wallet_expend += $amount;
                $wallet['wallet_expend'] = $wallet_expend;
            }
        }
        S_wallet($user_id, $wallet);

        //以便后续回滚
        $wallet['wallet_not_recharge'] = $old_wallet_not_recharge;
        $wallet['wallet_balance'] = $old_wallet_balance;
        $wallet['wallet_pay_amount'] = $old_wallet_pay_amount;
        $wallet['wallet_refund_amount'] = $old_wallet_refund_amount;
        $wallet['wallet_freeze'] = $old_wallet_freeze;
        $wallet['wallet_freeze_not_recharge'] = $old_wallet_freeze_not_recharge;
        $wallet['wallet_earning'] = $old_wallet_earning;
        $wallet['wallet_expend'] = $old_wallet_expend;

        //用户余额数据库修改
        $map = array(
            'user_id' => $user_id,
        );
        $info = $UserInfoModel->where($map)->find();
        if (!empty($info)) {
            $save['wallet_not_recharge'] = $new_wallet_not_recharge;
            $save['wallet_balance'] = $new_wallet_balance;
            $res = $UserInfoModel->saveUserInfo($user_id, $save);
            if ($res === false) {
                S_wallet($user_id, $wallet);
                return $this->apiReturn(180019, '修改余额异常');
            }
        } else {
            $save['user_id'] = $user_id;
            $save['wallet_not_recharge'] = $new_wallet_not_recharge;
            $res = $UserInfoModel->add($save);
            if ($res === false) {
                S_wallet($user_id, $wallet);
                return $this->apiReturn(180019, '修改余额异常');
            }
        }


        $data = array(
            'user_id' => $user_id,
            'relevance_id' => isset($extend['id']) ? $extend['id'] : 0,
            'relevance_sn' => isset($extend['sn']) ? $extend['sn'] : '',
            'pay_id' => isset($extend['pay_id']) ? $extend['pay_id'] : 0,
            'pay_name' => isset($extend['pay_name']) ? $extend['pay_name'] : '',
            'amount' => $amount,
            'user_balance' => $new_useable_balance,//当前可用余额
            'freeze_amount' => isset($extend['freeze_amount']) ? $extend['freeze_amount'] : 0,
            'bank_name' => isset($extend['bank_name']) ? $extend['bank_name'] : '',
            'bank_account' => isset($extend['bank_account']) ? $extend['bank_account'] : '',
            'create_time' => isset($extend['pay_time']) ? $extend['pay_time'] : time(),
            'remark' => isset($extend['remark']) ? $extend['remark'] : '',
        );
        //基础单据日志写入
        $change[] = array_merge($data, array(
            'log_type' => $log_type,
            'serial_number' => isset($extend['serial_number']) ? $extend['serial_number'] : '',
        ));
        //账户明细日志
        if ($log_type == 21) {
            if ($wallet_balance_diff != 0) {//充值性金额记录
                $change[] = array_merge($data, array(
                    'log_type' => 221,
                    'serial_number' => $extend['serial_number'],
                    'amount' => $wallet_balance_diff,
                ));
            }
            if ($wallet_not_recharge_diff != 0) {//非充值性金额记录
                $change[] = array_merge($data, array(
                    'log_type' => 321,
                    'serial_number' => $extend['serial_number2'],
                    'amount' => $wallet_not_recharge_diff,
                ));
            }
        } elseif (in_array($log_type, array(200))) {
            if ($wallet_freeze_diff != 0) {
                $change[] = array_merge($data, array(
                    'log_type' => $log_type == 200 ? 222 : $log_type,
                    'serial_number' => isset($extend['serial_number']) ? $extend['serial_number'] : '',
                    'freeze_amount' => $wallet_freeze_diff,
                ));
            }
            if ($wallet_freeze_not_recharge_diff != 0) {
                $change[] = array_merge($data, array(
                    'log_type' => $log_type == 200 ? 322 : $log_type,
                    'serial_number' => isset($extend['serial_number2']) ? $extend['serial_number2'] : '',
                    'freeze_amount' => $wallet_freeze_not_recharge_diff,
                ));
            }
        }
// dump($change);
// die();
        $res = $UserWalletLogModel->addAll($change);
        if ($res === false) {
            S_wallet($user_id, $wallet);
            return $this->apiReturn(180020, '日志记录失败');
        }

        $data = array(
            'old_wallet_balance' => $old_wallet_balance,
            'new_wallet_balance' => $new_wallet_balance,
            'old_wallet_not_recharge' => $old_wallet_not_recharge,
            'new_wallet_not_recharge' => $new_wallet_not_recharge,
            'wallet_balance_diff' => $wallet_balance_diff,
            'wallet_not_recharge_diff' => $wallet_not_recharge_diff,
        );
        // dump($data);
        // if ($is_verify) {
        //     //推送队列 后台校验 中金余额
        //     try {
        //         $RbmqModel = D('Common/Rbmq');
        //         $RbmqModel->queue(C('QUEUE_USER_BALANCE'))->push($info['user_id'], C('QUEUE_USER_BALANCE'));
        //     } catch (\Exception $e) {
        //     }
        // }
        return $this->apiReturn(0, '修改成功', $data);
    }
}