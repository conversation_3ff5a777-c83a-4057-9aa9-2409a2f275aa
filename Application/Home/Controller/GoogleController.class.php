<?php
namespace Home\Controller;

use Home\Controller\BaseController;

class GoogleController extends BaseController
{

	 public function reCAPTCHA(){
		$post_data = array(        
		        'secret' => '6Lc6la4ZAAAAANDuYHYiO5TWPlTZXYbMfUM9jAmO',        
		        'response' => $_POST["id"]    //前端传过来的响应码
		);

		$recaptcha_json_result = $this->send_google('https://www.recaptcha.net/recaptcha/api/siteverify', $post_data); 
		$recaptcha_result = json_decode($recaptcha_json_result);   
		//在这里处理返回的值 

		$this->add_not_spider($recaptcha_result);

		return $this->apiReturn(0, 'success', $recaptcha_result->score);

    }

    /*
		发送到google
    */
    private function send_google($url, $post_data)
	{
	    $postdata = http_build_query($post_data);
	    $options = array(
	        'http' => array(
	            'method' => 'POST',
	            'header' => 'Content-type:application/x-www-form-urlencoded',
	            'content' => $postdata,
	            'timeout' => 15 * 60 // 超时时间（单位:s）
	        )
	    );
	    $context = stream_context_create($options);
	    $result = file_get_contents($url, false, $context);
	    return $result;
	}

	/*
		加入非蜘蛛的redis库
	*/
	private function add_not_spider($data){

		// 加入非蜘蛛的redis库
		$this->redis = waf_redis_init();
		$ip = getip();

		if(!empty($data->score) && $data->score > 0.1){

			$ip_exists = $this->redis->get("spider_ip_not:".$ip);

			if(empty($ip_exists)){

				$info_data = array('score'	=>	$data->score,
								   'time'	=>	date('Y-m-d H:i:s',time()),
								   'action'	=>  $_REQUEST['action'],
								);

				$ip_exists = $this->redis->set("spider_ip_not:".$ip,json_encode($info_data));

				//设置有效期3小时
				$this->redis->expire("spider_ip_not:".$ip,3600);
			}else{
				//如果已经存在数据,那么在通过判定的状态下 则给有效期+5分钟
				$second = $this->redis->ttl("spider_ip_not:".$ip)/1000 + 5*60;
				$this->redis->expire("spider_ip_not:".$ip,$second);

			}
		}else if(!empty($data->score) && $data->score <= 0.1){

			
			$ip_exists = $this->redis->get("spider_ip_not:".$ip);

			if(!empty($ip_exists)){
				/*
					如果某次请求小于等于0.1 则减少5分钟
					之所以这么做是因为google有时候有些误判
				*/

				$second = $this->redis->ttl("spider_ip_not:".$ip)/1000 - 5*60;

				$this->redis->expire("spider_ip_not:".$ip,$second);
			}

		}
	}



}