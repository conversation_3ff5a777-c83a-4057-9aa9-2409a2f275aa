<?php

namespace Home\Controller;

use Home\Controller\BaseController;
use Home\Model\BehaviorLogModel;

class DifyDataController
{

    public function _initialize()
    {
    }

    public function apiReturn($code, $msg, $extend = '')
    {

        $data = [
            'code' => $code,
            'msg' => $msg,
            'data' => $extend,
        ];
        if ($code != 0) {
            // $this->apiLog($code, $msg, $extend);
            if ($code > 0) {
                unset($data['data']);
            }
            $data['code'] = abs($data['code']);
        }
        echo \GuzzleHttp\json_encode($data);
    }

    public function getActivityCenterData()
    {
        $activityList = D('Activity/ActivityNew')->getActivityCenterActivityList();
        $couponList = D('Coupon/Coupon')->getActivityCenterCouponList();
        $data = [
            'activity_list' => $activityList,
            'activity_count' => count($activityList),
            'coupon_list' => $couponList,
            'coupon_count' => count($couponList),
        ];
        $this->apiReturn(0, 'ok', $data);
    }

}
