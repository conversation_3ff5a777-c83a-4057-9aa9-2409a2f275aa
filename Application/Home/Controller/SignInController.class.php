<?php
/**
 * Created by PhpStorm.
 * User: leo
 * Date: 2018/10/17
 * Time: 19:07
 */

namespace Home\Controller;
use Home\Controller\BaseController;
//use function PHPSTORM_META\elementType;

class SignInController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), array())) {
            //检查登录
            $res = A('Login')->check();
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    /**
     * 用户今天是否签到
     * @return array
     */
    public function isSignInToday(){
        $sign_in_info = S_sign_in(cookie('uid'),'');
        if($sign_in_info){
            if($this->isInToday($sign_in_info['create_time'])){
                $code = 0;
                $msg = '已签到';
                $data = true;
            }else{
                $code = 0;
                $msg = '未签到';
                $data = false;
            }
        }else{
            $code = 80001;
            $msg = '未获取到user_id';
            $data = '';
        }

        return $this->apiReturn($code, $msg, $data);
    }

    /**
     * 用户签到接口
     * @return array
     */
    public function signIn(){
        $user_id = cookie('uid');
        $pf = platform();
        $UserSignInModel = D('UserSignIn');
        $is_sign_in_today = $UserSignInModel->isSignIn($user_id);
        if(!$is_sign_in_today){
            $res = $UserSignInModel->signIn($user_id,$pf);
            if(!$res){
                return $this->apiReturn(90001, '签到失败');
            }
            //将签到数据写入redis
            S_sign_in($res['user_id'],$res);
        }else{
            return $this->apiReturn(90002, '您今天已经签到过了，明天再来吧~');
        }

        
        //rbmq入队
        $queue = C('QUEUE_MKT_POINT');
        $RbmqModel = D('Common/Rbmq');
        $push_data = array(
            'user_id' => $user_id,
            'flow_type' => 1,
            'flow_reason_type' => 2,
            'flow_pf' => platform()
        );
        $RbmqModel->queue($queue)->push($push_data, $queue);
        return $this->apiReturn(0, '签到成功~');
    }

    /** 判断时间戳是否在今天内
     * @param int $timestamp
     * @return bool
     */
    private function isInToday($timestamp=0){
        $start_time = strtotime(date("Y-m-d"),time());
        $end_time = $start_time+60*60*24;
        if($timestamp>=$start_time && $timestamp<=$end_time){
            return true;
        }else{
            return false;
        }
    }
}