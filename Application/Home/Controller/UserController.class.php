<?php
namespace Home\Controller;

use Activity\Model\SampleModel;
use Crm\Model\CrmModel;
use Home\Controller\BaseController;
use Home\Services\UcenterService;
use Message\Model\MessageModel;
use Order\Model\OrderItemsModel;
use Order\Model\OrderModel;
use Order\Model\OrderPriceModel;

class UserController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), array('info', 'setcachefield', 'adduserinfo', 'sysusertocrm'))) {
            // 检查登录
            if (in_array(strtolower(ACTION_NAME), array('getusertype', 'getbindinfo', 'getusersampleinfo'))) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }
    /**
     * 解绑第三方绑定信息
     */
    public function unbind()
    {
        $user_id = cookie('uid');
        $info = S_user($user_id);
        $UserOauthModel = D('UserOauth');
        $unionId = $info['wechat_oauth']['union_id'];
        $qq_open_id = $info['qq_oauth']['open_id'];
        $agent_info = getAgentInfo();
        if (strval($agent_info['bro']) === '9' && $unionId) { // 微信环境下解绑微信，非微信环境下解绑qq
            //解绑旧微信的关系
            S_wechat($unionId, null);
            //解绑微信的原先的账号信息
            unset($info['wechat_oauth']);
            S_user($user_id, $info);
            try {
                $change_where['user_id'] = array('eq', $user_id);
                $change_where['union_id'] = $unionId;
                $change_where['oauth_status'] = array('eq', 1);
                $change_data['oauth_status'] = 2; // 解绑
                $UserOauthModel->where($change_where)->data($change_data)->save();
            } catch (\Exception $e) {
            }
        } else {
            if ($qq_open_id) {
                S_qq($qq_open_id, null);
                unset($info['qq_oauth']);
                S_user($user_id, $info);
                try {
                    $change_where['user_id'] = array('eq', $user_id);
                    $change_where['open_id'] = $qq_open_id;
                    $change_where['oauth_status'] = array('eq', 1);
                    $change_data['oauth_status'] = 2; // 解绑
                    $UserOauthModel->where($change_where)->data($change_data)->save();
                } catch (\Exception $e) {
                }
            } else {
                return $this->apiReturn(11020, '解绑失败，redis缺失qq_id');
            }
        }
        return $this->apiReturn(0, '解绑成功');
    }
    /**
     * 获取该user_id的第三方绑定信息
     * @return [type] [description]
     */
    public function getBindInfo()
    {
        $user_id = cookie('uid');

        $user_info = S_user($user_id);
        $agent_info = getAgentInfo();
        $qq_open_id = $user_info['qq_oauth']['open_id'];
        $wx_open_id = $user_info['wechat_oauth']['union_id'];

        $data['status'] = -1;
        $data['type_'] = 2;
        if (strval($agent_info['bro']) === '9') { // 微信环境
            if ($wx_open_id) {
                $data['status'] = 1;
            }
            $data['type_'] = 1;
        } else { // qq或H5
            if ($qq_open_id) {
                $data['status'] = 2;
            }
        }
        return $this->apiReturn(0, '获取成功', $data);
    }
    /**
     * [getSubscribeCoupon 领取关注服务号优惠券]
     * @return [type] [description]
     */
    public function getSubscribeCoupon()
    {
        $user_id = cookie('uid');
        $coupon_id = I('id');
        $pf = platform();
        $agentInfo = getAgentInfo();
        $err_info = [];
        if ($pf == 1 || strval($agentInfo['bro']) !== '9') { // pc端 或者 不在微信环境
            return $this->apiReturn(25023, '该优惠券仅限微信服务号里领取');
        }
        $user_info = S_user($user_id);
        $union_id = $user_info['wechat_oauth']['union_id'];
        $open_id = $user_info['wechat_oauth']['open_id'];
        if (!$union_id || !$open_id) {
            $err_info['union_id'] = $union_id;
            $err_info['open_id'] = $open_id;
            return $this->apiReturn(25024, '该会员尚未绑定微信', $err_info);
        }
        $we_obj = wechat();
        $info = $we_obj->getUserInfo($open_id);
        $subscribe = $info['subscribe']; //是否已经关注服务号
        if (strval($subscribe) !== '1') {
            $err_info['subscribe'] = $subscribe;
            return $this->apiReturn(25025, '该会员尚未关注微信服务号', $err_info);
        }

        $check['pf'] = $pf;
        $check['id'] = $coupon_id;
        $check['uid'] = $user_id;
        $check = array_merge($check, authkey());
        $res = post_curl(API_DOMAIN.'/coupon/issue', $check);

        if ($res['errcode'] != 0) {
            return $this->apiReturn($res['errcode'], $res['errmsg']);
        }
        return $this->apiReturn(0, '领取成功');
    }
    /**
     * [getCoupon 注册领取优惠券（在特定的时间内）]
     * @return [type] [description]
     */
    public function getCoupon()
    {
        $pf = I('pf') ? I('pf') : platform();
        $coupon_id = I('id'); // 领取优惠券的id
        $user_id = cookie('uid');

        $userInfo = S_user($user_id);
        $create_time = $userInfo['create_time'];
        $err_info = array();
        if (!$create_time) { //redis中获取不到
            $UserMainModel = D('UserMain');
            $create_time = $UserMainModel->where("user_id = {$user_id}")->getField('create_time');
        }
        //是否在要求的时间内注册的
        if ($create_time >= C('COUPON_REG_TIME.START') && $create_time <= C('COUPON_REG_TIME.END')) {
            //请求优惠券
            $check['pf'] = $pf;
            $check['id'] = $coupon_id;
            $check['uid'] = $user_id;
            $check = array_merge($check, authkey());
            $res = post_curl(API_DOMAIN.'/coupon/issue', $check);

            if ($res['errcode'] != 0) {
                return $this->apiReturn($res['errcode'], $res['errmsg']);
            }
            return $this->apiReturn(0, '领取成功');
        } else {
            $err_info['create_time'] = $create_time;
            return $this->apiReturn(25022, '会员注册时间不在领券范围内', $err_info);
        }
    }
    public function uploadCompanyImg()
    {
        $this->display();
    }


    protected function auth1($data)
    {
        $k1 =  !empty($data["k1"]) ? $data["k1"] : "" ;
        $k2 = !empty($data["k2"]) ? $data["k2"] : "";
//        $cancel_time_verify = I('request.cancel_time_verify', 0); // 取消验证时间，1-是
        $cancel_time_verify = !empty($data["cancel_time_verify"]) ? $data["cancel_time_verify"] : 0;
        $key = C('SUPER_AUTH_KEY');
        if (!$cancel_time_verify && !empty($k1) && $k1 < time() - 600) {
            return false;
        }
        $pwd = pwdhash($k1, $key);
        if ($k2 == $pwd) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 查询用户信息
     * @return [type] [description]
     */
    public function info($data=[])
    {

        $user_id = I('request.user_id', 0, 'intval');
        $dataUserId = !empty($data["user_id"]) ? $data["user_id"] : 0;
        $user_id = !empty($user_id) ? $user_id : $dataUserId;

        $params = [
            "user_id"=>0,
            "k1"=>"",
            "k2"=>"",
            "cancel_time_verify"=>"",
        ];
        $params = array_merge($params,$data);

        if (!$this->auth1($params)) {
            exit();
        }

        $UserMainModel = D('Home/UserMain');
        $info = $UserMainModel->getUserInfo($user_id);
        return $this->apiReturn(0, '成功', $info);
    }

    /**
     * 授权成功后获取的QQ信息-会员中心显示
     * @return [type] [description]
     */
    public function authQQInfo()
    {
        $user_id = cookie('uid');
        $user_info = S_user($user_id);
        $qq_info = $user_info['qq_oauth'];
        if (empty($qq_info) || empty($qq_info['open_id'])) {
            return $this->apiReturn(11017, 'QQ登录获取信息失败');
        }
        $data = array(
            'nickname' => $qq_info['oauth_nickname'],
            'oauth_head' => $qq_info['oauth_head'],
        );
        return $this->apiReturn(0, '获取成功', $data);
    }

    /**
     * 更改第三方账号
     * [changeOAuthAccount description]
     * @return [type] [description]
     */
    public function changeOAuthAccount()
    {
        $platform = platform();
        $type = I('request.type');
        switch (strval($type)) {
            case '1':
                $wechat_info = session('wechat_info');
                if (empty($wechat_info)) {
                    return $this->apiReturn(11029, '微信登录获取信息失败');
                }
                $UserOauthModel = D('UserOauth');
                $user_id = cookie('uid');
                $info = S_user($user_id);
                $pre_user_id = S_wechat($wechat_info['unionid']);//新微信
                //双重确定，确保已经绑定成功，无需再绑一次
                if ($info['wechat_oauth']['union_id'] == $wechat_info['unionid'] && $pre_user_id == $user_id) {
                    if (!empty($info['wechat_oauth']['oauth_head']) && !empty($info['wechat_oauth']['oauth_nickname'])) {
                        return $this->apiReturn(0, '已绑定无需再绑定');
                    }
                }
                S_wechat($info['wechat_oauth']['union_id'], null);//解绑旧微信的关系
                //新微信以前绑定过账号，先解绑
                if ($pre_user_id) {
                    $other_user = S_user($pre_user_id);
                    unset($other_user['wechat_oauth']);
                    S_user($pre_user_id, $other_user);
                    S_wechat($wechat_info['unionid'], null);//解绑新微信的原先关系
                    try {
                        $change_where['user_id'] = array('eq', $pre_user_id);
                        $change_where['union_id'] = $wechat_info['unionid'];
                        $change_where['oauth_status'] = array('eq', 1);
                        $change_data['oauth_status'] = 2; // 解绑
                        $UserOauthModel->where($change_where)->data($change_data)->save();
                    } catch (\Exception $e) {
                    }
                }

                $oauth_type = $platform == 1 ? 1 : 3;
                //绑定现在的账号
                $data = array(
                    'user_id' => $user_id,
                    'open_id' => $wechat_info['openid'],
                    'union_id' => $wechat_info['unionid'],
                    'oauth_type' => $oauth_type, //1:wechat 2:qq 3:wechat公众号
                    'bind_time' => $_SERVER['REQUEST_TIME'],
                    'oauth_head' => $wechat_info['headimgurl'],
                    'oauth_nickname' => $wechat_info['nickname'],
                    'oauth_status' => 1,
                    'access_token' => '',
                );

                //记录用户绑定信息
                $third['wechat_oauth'] = array(
                    'union_id' => $data['union_id'],
                    'bind_time' => $data['bind_time'],
                    'oauth_head' => $data['oauth_head'],
                    'oauth_nickname' => $data['oauth_nickname'],
                    'oauth_status' => $data['oauth_status'],
                );
                if ($platform == 1) {//PC使用的是微信开发平台
                    $third['wechat_oauth']['open_id_kf'] = $data['open_id'];
                    $third['wechat_oauth']['access_token_kf'] = $data['access_token'];
                } else {//移动使用的是微信公众平台
                    $third['wechat_oauth']['open_id'] = $data['open_id'];
                    $third['wechat_oauth']['access_token'] = $data['access_token'];
                }

                $info = array_merge($info, $third);

                $pre_user_info = S_user($user_id);
                S_user($user_id, $info);
                S_wechat($data['union_id'], $user_id);

                $point_info['wechat'] = !empty($pre_user_info['wechat_oauth'])?1:'';
                $point = $this->mktScore($user_id,$point_info);
                //数据库填写数据 成功与否不影响流程
                try {
                    $UserOauthModel->add($data);
                } catch (\Exception $e) {
                }
                session('wechat_info', null);
                $this->apiReturn(0, '绑定成功',['point'=>$point]);
                exit();
                break;

            case '2': //qq
                empty($qq_info) && $qq_info = session('qq_info');
                if (empty($qq_info)) {
                    return $this->apiReturn(11017, 'QQ登录获取信息失败');
                }
                $user_id = S_qq($qq_info['open_id']);
                $now_user_id = cookie('uid');
                if (empty($user_id) || $user_id == $now_user_id) { //该qq号没有绑定网站会员 或 //修改自己绑定的qq号
                    //解绑原来的
                    empty($user_id) && $user_id = $now_user_id;
                    S_qq($qq_info['open_id'], $user_id);
                    $user_info = S_user($user_id);
                    if (S_qq($user_info['qq_oauth']['open_id'])) {
                        S_qq($user_info['qq_oauth']['open_id'], null);
                    }
                    $data = array(
                        'user_id' => $user_id,
                        'open_id' => $qq_info['open_id'],
                        'oauth_type' => 2,
                        'bind_time' => $_SERVER['REQUEST_TIME'],
                        'oauth_head' => !empty($qq_info['figureurl_qq_2']) ? $qq_info['figureurl_qq_2'] : $qq_info['figureurl_qq_1'],
                        'oauth_nickname' => $qq_info['nickname'],
                        'oauth_status' => 1,
                        'access_token' => $qq_info['access_token'],
                    );
                    //记录用户绑定信息
                    $third['qq_oauth'] = array(
                        'open_id' => $data['open_id'],
                        'bind_time' => $data['bind_time'],
                        'oauth_head' => $data['oauth_head'],
                        'oauth_nickname' => $data['oauth_nickname'],
                        'oauth_status' => $data['oauth_status'],
                        'access_token' => $data['access_token'],
                    );
                    $info = S_user($user_id);
                    $qq_info = $info['qq_oauth'];
                    $info = array_merge($info, $third);
                    S_user($user_id, $info);

                    $point_info['qq'] = !empty($qq_info)?1:'';
                    $point = $this->mktScore($user_id,$point_info);

                    $UserOauthModel = D('UserOauth');
                    $UserOauthModel->add($data);

                    //执行登录
                    A('Login')->loginAct($user_id, $platform, 4, $qq_info['open_id']);
                    session('qq_info', null);
                    $this->apiReturn(0, '绑定成功',$point);
                    exit();
                } else { //说明该qq号已经被别人绑定了
                    session('qq_info', null);
                    return $this->apiReturn(11022, '该QQ已被其他账号绑定');
                }
                break;
            default:
                # code...
                break;
        }
    }
    /**
     * 会员头像保存
     * @param  [type]  $user_head      [description]
     */
    public function saveHeadImg()
    {
        $user_head = I('user_head', '', 'trim');
        if (!$user_head) {
            $this->apiReturn(23021, '会员头像不能为空');
        }
        //存redis userInfo
        $user_id = cookie('uid');
        $userInfo = S_user($user_id);
        $preUserInfo = $userInfo;
        $user_head = WWW_DOMAIN . '/v3/dist/res/home/<USER>/headimg/'.$user_head;
        $userInfo['user_head'] = $user_head;
        S_user($user_id, $userInfo);

        $point_info['user_head'] = !empty($user_head)?1:'';
        $point = $this->mktScore($user_id,$point_info);

        //mysql
        try {
            $UserInfoModel = D('UserInfo');
            $where = array('user_id' => $user_id);
            $save = array('user_head' => $user_head);
            $UserInfoModel->where($where)->save($save);
        } catch (\Exception $e) {

        }
        $this->apiReturn(0, '会员头像修改成功',['point'=>$point]);
    }

    /**
     * 会员品牌上传保存
     * @param 2343,34343,3343
     */
    public function saveBrandList()
    {
        $brandIdStr = I('request.brand_id');
        $user_id = cookie('uid');
        $user_info = S_user($user_id);
        //将品牌相关信息放redis S_company
        //插入数据库
        $addData['brand_list'] = $brandIdStr;
        $UserCompanyModel = D('UserCompany');
        if ($user_info['company_id']) {
            try {
                //修改
                $comWhere = array('user_id' => $user_id);
                $UserCompanyModel->where($comWhere)->save($addData);
            } catch (\Exception $e) {

            }
        } else {
            //新增
            $addComData = $addData;
            $addComData['user_id'] = $user_id;
            $company_id = $UserCompanyModel->data($addComData)->add();
            $user_info['company_id'] = $company_id;
            S_user($user_id, $user_info);
        }
        $company_info = S_company($user_id);
        $brandRes = getBrandArrByStr($brandIdStr);
        $company_info['brand_list'] = $brandRes;
        S_company($user_id, $company_info);
        $point_info['brand_list'] = !empty($company_info['brand_list'])?1:'';
        $point = $this->mktScore($user_id,$point_info);
        $this->apiReturn(0, '成功',['point'=>$point]);
    }

    /**
     * 内部调整公司信息使用
     * @return [type] [description]
     */
    public function updateCompanyInfo()
    {
        if (!$this->auth()) {
            exit();
        }
        $user_id = I('user_id', 0, 'intval');
        $com_name = I('com_name', '', 'trim');//公司名称
        $com_telphone = I('com_telphone', '', 'trim');//公司座机
        $com_fax = I('com_fax', '', 'trim');//传真
        $company_info = S_company($user_id);
        $addData = array();
        if (empty($company_info['com_name']) && !empty($com_name)) {
            $company_info['com_name'] = $com_name;
            $addData['com_name'] = $com_name;
        }
        if (empty($company_info['com_telphone']) && !empty($com_telphone)) {
            $company_info['com_telphone'] = $com_telphone;
            $addData['com_telphone'] = $com_telphone;
        }
        if (empty($company_info['com_fax']) && !empty($com_fax)) {
            $company_info['com_fax'] = $com_fax;
            $addData['com_fax'] = $com_fax;
        }
        $user_info = S_user($user_id);
        S_company($user_id, $company_info);
        if (!empty($addData)) {
            $UserCompanyModel = D('UserCompany');
            $comWhere = array('user_id' => $user_id);
            $UserCompanyModel->where($comWhere)->save($addData);
        }
        return $this->apiReturn(0, '设置成功');
    }

    /**
     * 会员基本信息新增或修改
     */
    public function changeUserInfo()
    {
        $user_id = cookie('uid');
        $company_info = S_company($user_id);

        $nike_name = I('nike_name', '', 'trim');//会员姓名
        $erp_sale_uid = I('erp_sale_uid', 0, 'intval');//用户销售
        //companyInfo
        $com_logo = I('com_logo', '', 'trim');
        $com_name = I('com_name', '', 'trim');//公司名称
        $type_id = I('type_id', '', 'trim'); //公司类型
        $com_desc = I('com_desc', '', 'trim');
        $com_province_id = I('com_province_id', '', 'trim');
        $com_city_id = I('com_city_id', '', 'trim');
        $com_area_id = I('com_area_id', '', 'trim');
        $com_address = I('com_address', '', 'trim');
        $com_telphone = I('com_telphone', '', 'trim');//公司座机
        $com_fax = I('com_fax', '', 'trim');
        $com_mobile = I('com_mobile');//手机号码
        $intl_code = I('intl_code'); // 国际号码
        $com_mobile = get_inte_mobile($com_mobile, $intl_code);

        if ($nike_name) {
            if (intval(mb_strlen($nike_name,"utf-8")) < 2) {
                return $this->apiReturn(23030, '会员姓名不能小于2个字数');
            }
        }
        if ($com_province_id !== '-1') {
            if ($com_city_id === '-1') {
                return $this->apiReturn(23031, '请填写完整的公司地址');
            }
        }
        if ($com_province_id === '-1' || $com_city_id === '-1') {
            $com_province_id = $company_info['com_province_id'];
            $com_city_id = $company_info['com_city_id'];
            $com_area_id = $company_info['com_area_id'];
        }
        // if (!strstr($com_logo, 'Uploads/company')) {
        //     //logo base64转换为图片
        //     $com_logo = base64_upload($com_logo);
        //     if ($com_logo == 'oversize') {
        //         return $this->apiReturn(23036, '图片大小超过1M，请重新更换图片');
        //     } else {
        //         if ($com_logo) {
        //             $com_logo = API_DOMAIN.'/Uploads/company/'.$com_logo;
        //         } else {
        //             $com_logo = $company_info['com_logo'];
        //         }
        //     }
        // }

        $addData = array(
            'com_name'              => $com_name,
            'type_id'               => $type_id,
            'com_desc'              => $com_desc,
            'com_address'           => $com_address,
            'com_telphone'          => $com_telphone,
            'com_fax'               => $com_fax,
            'com_mobile'            => $com_mobile,
        );

        if ($com_logo) {
            $addData['com_logo'] = $com_logo;
        }

        if ($com_province_id && $com_city_id) {
            $addData['com_province_id'] = $com_province_id;
            $addData['com_city_id'] = $com_city_id;
            $addData['com_area_id'] = $com_area_id;
        }
        if ($addData['com_name']) {
            if (intval(mb_strlen($addData['com_name'],"utf-8")) < 2 || intval(mb_strlen($addData['com_name'],"utf-8")) > 50) {
                return $this->apiReturn(23030, '公司名称字数过短或过长(2-50)');
            }
        }

        if ($addData['com_desc']) {
            if (intval(mb_strlen($addData['com_desc'],"utf-8")) > 200) {
                return $this->apiReturn(23031, '公司简介字数超过限制');
            }
        }
        if ($addData['com_address']) {
            if (intval(mb_strlen($addData['com_address'],"utf-8")) > 100) {
                return $this->apiReturn(23032, '街道详细地址超过100字数');
            }
        }
        if ($addData['com_telphone']) {
            if (!is_mobile_phone($addData['com_telphone'])) {
                return $this->apiReturn(23033, '公司座机格式有误');
            }
        }
        if ($addData['com_fax']) {
            if (!is_numeric($addData['com_fax'])) {
                return $this->apiReturn(23034, '公司传真仅能为数字');
            }
        }
        if ($addData['com_mobile'] && I('com_mobile')) {
            if (!is_mobile($com_mobile)) {
                return $this->apiReturn(11006, '公司移动电话格式错误，请重新输入');
            }
        }

        if ($com_province_id !== '-1' && $com_city_id !== '-1' && $com_area_id === '-1') {
            $company_info['area_name'] = '';
            $company_info['com_area_id'] = '';
        } else {
            $company_info['area_name'] = get_district($com_area_id) ? get_district($com_area_id) : $company_info['area_name'];
            $company_info['com_area_id'] = $com_area_id ? $com_area_id : $company_info['com_area_id'];
        }
        $company_info['province_name'] = get_province($com_province_id) ? get_province($com_province_id) : $company_info['province_name'];
        $company_info['city_name'] = get_city($com_city_id) ? get_city($com_city_id) : $company_info['city_name'];
        $company_info['type_name'] = C("SUPPLIER_TYPE.{$type_id}") ? C("SUPPLIER_TYPE.{$type_id}") : $company_info['type_name'];
        $company_info['com_logo'] = $com_logo ? $com_logo : $company_info['com_logo'];
        $company_info['com_name'] = $com_name ? $com_name : '';
        $company_info['type_id'] = $type_id != '' ? $type_id : $company_info['type_id'];
        $company_info['com_desc'] = $com_desc ? $com_desc : '';
        $company_info['com_province_id'] = $com_province_id ? $com_province_id : $company_info['com_province_id'];
        $company_info['com_city_id'] = $com_city_id ? $com_city_id : $company_info['com_city_id'];

        $company_info['com_address'] = $com_address ? $com_address : '';
        $company_info['com_telphone'] = $com_telphone ? $com_telphone : '';
        $company_info['com_fax'] = $com_fax ? $com_fax : '';
        $company_info['com_mobile'] = $com_mobile ? $com_mobile : '';
        if (strstr($com_mobile, "+")) { // 国际手机
            $res = explode("+", $com_mobile);
            $company_info['intl_code'] = $res[0];
            $company_info['com_mobile'] = $res[1];
        } else {
            $company_info['intl_code'] = $intl_code;
            $company_info['com_mobile'] = $com_mobile;
        }
        $user_info = S_user($user_id);
        $user_info['nike_name'] = $nike_name ? $nike_name : '';
        $user_info['erp_sale_uid'] = $erp_sale_uid ? $erp_sale_uid : $user_info['erp_sale_uid'];
        $sex = I('sex');
        $user_info['sex'] = $sex ? $sex : 0;

        S_user($user_id, $user_info);
        S_company($user_id, $company_info);

        try {
            $UserMainModel = D('UserMain');
            $UserInfoModel = D('UserInfo');
            $userWhere = array('user_id' => $user_id);
            //用户扩展信息
            $userInfoSave = array('nike_name' => $user_info['nike_name'], 'sex' => $user_info['sex']);
            $UserInfoModel->where($userWhere)->save($userInfoSave);
            //用户主信息
            $userSave = array('erp_sale_uid' => $user_info['erp_sale_uid']);

            $UserCompanyModel = D('UserCompany');
            if ($user_info['company_id']) {
                //修改
                $comWhere = array('user_id' => $user_id);
                $userSave['company_id'] = $user_info['company_id'];
                $UserCompanyModel->where($comWhere)->save($addData);
            } else {
                //新增
                $addComData = $addData;
                $addComData['user_id'] = $user_id;
                $company_id = $UserCompanyModel->data($addComData)->add();
                $user_info['company_id'] = $company_id;
                $userSave['company_id'] = $company_id;
                S_user($user_id, $user_info);
            }
            $UserMainModel->where($userWhere)->save($userSave);
            // 积分推送
            $point_info = $addData;
            //去除省市区 防止计算送积分出错
            if ($com_province_id && $com_city_id) {
                unset($point_info['com_province_id']);
                unset($point_info['com_city_id']);
                unset($point_info['com_area_id']);
            }
            $point_info['com_logo'] = $com_logo;
            $point_info['nike_name'] = $nike_name;
            $point_info['sex'] = $sex;
            $point = $this->mktScore($user_id,$point_info);

        } catch (\Exception $e) {

        }
        return $this->apiReturn(0, '成功',['point'=>$point]);
    }

    // 会员信息新增，积分推送
    public function mktScore($user_id,$point_info)
    {
        //1绑定手机号，2绑定邮箱，3qq账号绑定，4微信绑定，5会员头像，6会员姓名，7性别，8公司logo，9公司名称，10公司类型，11公司简介，12主营品牌，13公司地址，14公司座机，15传真号码，16移动电话
        $user_point_info = S_user_point_info($user_id);
        $point_qty = 0;
        foreach ($point_info as $k=>$v){
            if($v && empty($user_point_info[$k])){
                $point_qty += 1;
                $user_point_info[$k] = $v;
                $this->pushMktScore($user_id, C('USER_INFO_NO.'.$k));
            }
        }
        S_user_point_info($user_id,$user_point_info);
        return C('ISSUE_POINT.OTHER')*$point_qty;
    }

    //积分推送入队
    //$user_id, $flow_extra_id,(事件类型）
    public function pushMktScore($user_id, $flow_extra_id)
    {
        $RbmqModel = D('Common/Rbmq');
        $push_data = array(
            'user_id' => $user_id,//用户id
            'flow_type' => 1,//积分流向 固定为1
            'flow_reason_type' => 4,//积分流向原因 完善资料为4
            'flow_pf' => platform(), //平台'1PC 2H5 3小程序 4后台人工调整'
            'flow_extra_id' => $flow_extra_id,//flow_extra_id 新增积分事件类型
        );
        $RbmqModel->queue(C('QUEUE_MKT_POINT'))->push($push_data, C('QUEUE_MKT_POINT'));
    }

    public function checkMktScore(){
        $user_id = cookie('uid');
        $pf = platform();
        $user_point_info = S_user_point_info($user_id);
        $can_get_point = 1;
        if(($pf==1) && (!$user_point_info || count($user_point_info)<16)){
            $can_get_point = -1;
        }elseif($pf==2 || $pf==6){
            if(empty($user_point_info['nike_name']) || empty($user_point_info['sex']) || empty($user_point_info['com_name']) || empty($user_point_info['com_type']) || empty($user_point_info['com_telphone']) || empty($user_point_info['com_desc']) || empty($user_point_info['com_address'])){
                $can_get_point = -1;
            }
        }
        return $this->apiReturn(0, 'success',$can_get_point);
    }

    /**
     * 新增|绑定手机
     */
    public function bindMobile()
    {
        $user_id = cookie('uid');
        $userInfo = S_user($user_id);
        // $prePassword = I('prepassword', '', 'trim');
        $account = I('account', '', 'trim');
        $intlCode = I('request.intl_code', '');
        $account = get_inte_mobile($account, $intlCode);
        $sms_verify = I('sms_verify', '', 'trim');
        $platform = I('request.pf', -1, 'intval');
        //缺失平台
        if (!C('PLAT_FORM.'. $platform)) {
            return $this->apiReturn(11009, '网络错误，请您稍后重试');
        }
        if (!$userInfo) {
            return $this->apiReturn(23016, '无相关用户信息');
        }
        // if ($userInfo['password']) { //有密码用户
        //     if (!$prePassword) {
        //         return $this->apiReturn(23003, '原密码不能为空');
        //     }
        //     if ($userInfo['password'] !== pwdhash($prePassword, $userInfo['salt'])) {
        //         return $this->apiReturn(23003, '原密码有误');
        //     }
        // }
        if (empty($account)) {
            return $this->apiReturn(12001, '手机号码不可为空');
        }
        if (!is_mobile($account)) {
            return $this->apiReturn(11006, '手机号码格式错误，请重新输入', '');
        }
        if (S_account($account)) {
            return $this->apiReturn(12002, '该手机号码已注册，请重新输入！', '');
        }

        //校验短信验证码
        if (empty($sms_verify)) {
            return $this->apiReturn(11007, '请输入短信验证码');
        }
        $code = session_sms($account);
        if ($code !== pwdhash($sms_verify, C('SMS_SALT'))) {
            return $this->apiReturn(11015, '短信验证码错误，请重新获取');
        }

        //同步手机号到用户中心
        $ucId = !empty($userInfo["uc_id"]) ? $userInfo["uc_id"] : 0;
        if(!empty($ucId)){
            $ucenterResult = UcenterService::ucenterBindMobile($ucId,$account);
            if($ucenterResult["code"] != 0){
                return $this->apiReturn(11015, '该手机号已存在，无法绑定');
            }
        }


        //验证成功，清除短信验证码
        session_sms($account, null);

        S_account($account, $user_id); // 设置手机账号缓存

        //redis 手机更换
        if ($userInfo['intl_code']) {
            $pre_mobile = $userInfo['intl_code'] . '+' .$userInfo['mobile'];
        } else {
            $pre_mobile = $userInfo['mobile'];
        }
        S_account($userInfo['mobile'], null);
        S_account($pre_mobile, null);

        $pre_account = I('account', '', 'trim');
        $userInfo['mobile'] = $pre_account;
        if ($intlCode !== '0086') { // 国际短信
            $userInfo['intl_code'] = $intlCode;
        }
        S_user($user_id, $userInfo);


        // 积分首次绑定手机送积分
        $point_info['mobile'] = !empty($pre_mobile)?1:'';
        $point = $this->mktScore($user_id,$point_info);

        //数据库填写数据 成功与否不影响流程
        try {
            $save = array('mobile' => $account, 'intl_code' => $intlCode);
            $UserMainModel = D('UserMain');
            $UserMainModel->where(array('user_id' => $user_id))->save($save);

            // 同步修改CRM
            $CrmUserModel = D('Crm/User');
            $CrmUserModel->updateAccount($user_id, $save);
        } catch (\Exception $e) {
        }

        $log = $pre_mobile ? '修改绑定手机，修改前：'.$pre_mobile : '绑定手机';
        D('UserActionLog')->addLog($user_id, $user_id, 1, $log);

        return $this->apiReturn(0, '成功', ['point'=>$point]);
    }

    /**
     * 更改接收短信消息的手机号码
     * @param msg_mobile
     */
    public function changeMsgMobile()
    {
        $msg_mobile = I('msg_mobile', '', 'trim');
        if (!$msg_mobile) {
            return $this->apiReturn(12001, '手机号码不可为空');
        }
        if (!is_mobile($msg_mobile)) {
            return $this->apiReturn(23005, '手机号码格式不正确');
        }
        $user_id = cookie('uid');
        $user_info = S_user($user_id);
        $user_info['msg_mobile'] = $msg_mobile;
        S_user($user_id, $user_info);
        try {
            $UserInfoModel = D('UserInfo');
            $save['msg_mobile'] = $msg_mobile;
            $UserInfoModel->where(array('user_id' => $user_id))->save($save);
        } catch (\Exception $e) {

        }
        return $this->apiReturn(0, '成功');
    }

    /**
     * 用户类型 （有密码 或 无密码）
     * @param type 请求页面 1为会员中心首页 2为订单中心首页
     */
    public function getUserType()
    {
        $user_id = cookie('uid');
        $userInfo = S_user($user_id);

        $type = I('request.type', 1, 'intval');
        $no_show = 'no_show_'.$type;
        if (!$userInfo) {
            return $this->apiReturn(23016, '无相关用户信息');
        }
        if (!$userInfo['msg_mobile']) {
            if ($userInfo['mobile']) {
                $userInfo['msg_mobile'] = $userInfo['mobile'];
            } else {
                $userInfo['msg_mobile'] = '';
            }
        }

        //只显示部分数据
        $field = array_flip(array('qq','wx','user_type','user_head','browser','company_id','create_device','create_time','device','email','fax','grade','mobile','msg_mobile','nike_name','qq_oauth','sex','status','user_name','wechat_oauth','work_function','work_grade', 'intl_code'));
        $res['user_info'] = array_intersect_key($userInfo, $field);
        $res["user_info"]["user_name"] = $res["user_info"]["user_name"]?: "";
        $res["user_info"]["email"] = $res["user_info"]["email"]?: "";
        if(!isset($res['user_info']["user_head"])){
            $res['user_info']["user_head"] = "";
        }
        if ($res['user_info']['qq_oauth']) {
            unset($res['user_info']['qq_oauth']['open_id']);
        }
        if ($res['user_info']['wechat_oauth']) {
            unset($res['user_info']['wechat_oauth']['open_id']);
            unset($res['user_info']['wechat_oauth']['union_id']);
        }
        unset($field);

        $res['company_info'] = S_company($user_id);
        if (!is_array($res['company_info']['brand_list'])) {
            $brandRes = getBrandArrByStr($res['company_info']['brand_list']);
            $company_info = $res['company_info'];
            $company_info['brand_list'] = $brandRes ? $brandRes:[];
            S_company($user_id, $company_info);
            $res['company_info'] = S_company($user_id);
        }
        #todo 2023.6.12 补齐公司信息
        $companyFilter = ["user_id","com_name","type_id","type_name","com_logo","brand_list","com_province_id","com_city_id","com_area_id","com_address","com_telphone","com_fax","product_service","business_area","com_desc","stock_area","company_description","area_name","province_name","city_name"];
        foreach ($companyFilter as $b){
            if (!array_key_exists($b,$res['company_info']) ){
                $res['company_info'][$b] = "";
            }else if (!$res['company_info'][$b]){
                $res['company_info'][$b] = "";
            }
        }

        // $res["billing_info"]=[]; //结算信息
        // $res["billing_info"]["total_credit"]= "￥*********"; //结算信息
        // $res["billing_info"]["available_credit"]= "￥5000000"; //结算信息





        $res[$no_show] = $userInfo[$no_show] ? $userInfo[$no_show] : 0;
        //是否需要展示新手引导页面
        if (!$userInfo[$no_show]) {
            $userInfo[$no_show] = 1;
            S_user($user_id, $userInfo);
        }
        if ($userInfo['password']) {
            //有密码
            $res['type'] = 1;
            $res['msg'] = '有密码用户';
        } else {
            //无密码
            $res['type'] = 2;
            $res['msg'] = '无密码用户';
        }
        //任务体系
        $task = array();

        $info = S_user_point_info($user_id);
        $u_info = array();
        if(isset($info['nike_name'])){
            $u_info[] = $info['nike_name'];
        }
        if(isset($info['com_name'])){
            $u_info[] = $info['com_name'];
        }
        if(isset($info['type_id'])){
            $u_info[] = $info['type_id'];
        }
        //是否完善资料任务
        $len = $this->get_user_write_info();

        $task[0]['num'] = $len >= 3?1:0;
        $task[0]['flag'] = $len >= 3?true:false;
        $task[0]['desc'] = '是否完善资料任务';
        //处理是否已经有下首次订单
        $task_m    = D('TaskSystem');
        $task_list = $task_m->getData(array('user_id'=>$user_id,'create_time'=>array('gt',C('MKT_POINT_TIME'))));
        // dump($task_list);exit;
        $task_r = array();
        if($task_list){
            foreach ($task_list as $k=>$v){
                $task_r[$v['task_type']] = $v;
            }
        }

        unset($where);
        $where['user_id']          = $user_id;
        $where['flow_reason_type'] = 7;
        $where['flow_time']        = array('gt',strtotime(date('Y-m-d',time())));


        //登录搜索
        $search_num          =  D('TaskSystem')->today_search($user_id);
        $search_is_apply     =  D('point_log')->where($where)->count();

        $task[1]['desc']     = '是否今天已搜索';
        $task[1]['is_apply'] = $search_is_apply > 0 ? 1:0;
        $task[1]['desc']     = '是否今天已搜索';
        $task[1]['num']      = $search_num?1:0;
        $task[1]['flag']     = $search_num;

        //统计金额
        $order_amount = D("Order/PayLog")->where(array('user_id'=>$user_id,'create_time'=>array('gt',C('MKT_POINT_TIME'))))->sum('pay_amount');
        //统计次数
        $order_num    = D("Order/PayLog")->where(array('user_id'=>$user_id,'create_time'=>array('gt',C('MKT_POINT_TIME'))))->count();

        //统计以前的次数
        $order_num_old    = D("Order/PayLog")->where(array('user_id'=>$user_id,'create_time'=>array('lt',C('MKT_POINT_TIME'))))->count();

        //统计以前的金额
        $order_amount_old = D("Order/PayLog")->where(array('user_id'=>$user_id,'create_time'=>array('lt',C('MKT_POINT_TIME'))))->sum('pay_amount');


        $task[2]['order_num_old'] = $order_num_old;
        $task[2]['order_amount_old'] = $order_amount_old;

        if($order_num > 0){
            $task[2]['desc']    = '是否有下单';
            $task[2]['num']     = 1;
            $task[2]['flag']    = true;
        }else{
            $task[2]['desc']    = '是否有下单';
            $task[2]['num']     = 0;
            $task[2]['flag']    = false;
        }

        $task[4]['order_num_old'] = $order_num_old;
        $task[4]['order_amount_old'] = $order_amount_old;

        //有两次下单并满足5000任务
        if($order_num >=2 && $order_amount>=5000){
            $task[4]['desc']    = '是否有两次下单并满足5000任务';
            $task[4]['num']     = 1;
            $task[4]['flag']    = true;
        }else{
            $task[4]['desc']    = '是否有两次下单并满足5000任务';
            $task[4]['num']     = 0;
            $task[4]['flag']    = false;
        }


       //查询是否签到
       $UserSignInModel    = D('UserSignIn');
       $is_sign_in_today   = $UserSignInModel->isSignIn($user_id);

        //是否有签到
        if($is_sign_in_today){
            $task[5]['is_apply']    = D('point_log')->where($where)->count() > 0 ? 1:0;
            $task[5]['desc']        = '是否有签到';
            $task[5]['num']         = 1;
            $task[5]['flag']        = true;
        }else{
            $task[5]['is_apply']    = D('point_log')->where($where)->count() > 0 ? 1:0;
            $task[5]['desc']        = '是否有签到';
            $task[5]['num']         = 0;
            $task[5]['flag']        = false;
        }

        $res['task_list'] = $task;


        #新增数据
        $OrderModel = new OrderModel();
        $OrderItemsModel = new OrderItemsModel();

        $sum_no_end_order = $OrderModel->where("user_id=$user_id and (status in (1,2,4,7,8) or sale_order_status in (2,3))")->count();
        $sum_messsage = (new MessageModel())->unmsgnum($user_id);

        #出库
        $UserCouponModel = D('UserCoupon');
        $map = array(
            'user_id' => $user_id,
            'status' => -1,
        );
        $sum_no_used_coupon = $UserCouponModel->where($map)->count();

        $data =[
            "kefu_url"=>"https://url.cn/uia2no5Z?_type=wpa&qidian=true",
            "sum_no_end_order"=> $sum_no_end_order, //未完结订单总数量（订单状态包含：待审核、待付款、待付尾款、待发货、部分发货、待收货）
            "sum_no_used_coupon"=>$sum_no_used_coupon, //未使用且有效的优惠券总数量
            "sum_messsage"=> $sum_messsage, // 未读消息数量
            "last_order"=>[]
        ];

        //只显示待付款，待付尾款的订单，没有时下方内容上移如果有多个订单时，就展示最早下单的那个。
        $lastOrder = $OrderModel->where("user_id=$user_id and (status in (2,3) ) ")->find();
        if (!$lastOrder){
            $lastOrder = $OrderModel->where(["user_id"=>$user_id])->order("order_id asc")->find();
        }

        if ($lastOrder){
            $order_id = $lastOrder["order_id"];
            $currency = $lastOrder["currency"];

            $OrderPriceModel = new OrderPriceModel();

            $shipping_price = $OrderPriceModel->getShippingPrice($order_id);
            $extend_fee     = $OrderPriceModel->getExtPrice($order_id);
            $coupon_price = $OrderPriceModel->getActivityPrice($order_id);
            $activity_price = $OrderPriceModel->getPreferentialPrice($order_id);

            $data["last_order"] = [
                "order_id" =>$order_id,  //订单id
                "order_sn" =>$lastOrder['order_sn'] ? $lastOrder['order_sn']:$lastOrder["sale_order_sn"] , //订单编号
                "order_pay_type" => $lastOrder['order_pay_type'],
                "create_time" =>date('Y-m-d H:i', $lastOrder['create_time']),  //创建时间
                "order_amount" =>$lastOrder["order_amount"],     //订单总额
                "user_id" =>$lastOrder["user_id"],     //订单总额
                "order_amount_format" =>price_format($lastOrder["order_amount"],$currency),  //订单总额 带币种符号
                "extend_fee_format" =>price_format($extend_fee,$currency),  //附加费  带币种符号
                "coupon_price_format" =>price_format($coupon_price,$currency),  //优惠券  带币种符号
                "activity_price_format" =>price_format($activity_price,$currency),//活动费 带币种符号
                "shipping_price_format" =>price_format($shipping_price,$currency), //运费  带币种符号
                "status_val" =>C('ORDER_STATUS.' . $lastOrder['status']), //状态
                "status" =>$lastOrder['status'], //状态  2  待付款,3 待付尾款,4  待发货,7 部分发货,8 待收货,10 交易成功
                "count_item"=>$OrderItemsModel->getSumCount($order_id), //商品总数
            ];
        }

        $res = array_merge($res,$data);
        
        return $this->apiReturn(0, '成功', $res);
    }

    /*
        设置/获取 用户是否领取奖品信息
    */
    public function userApplyPrize(){

        //type =1 设置奖品 type ==2 查看奖品状态
        $type =   I('request.type',2);

        //set =1 首次下单  type=2 完成2次订单及以上交易且实付金额满5000元
        $set  =   I('request.set',-1);

        $user_id = cookie('uid');

        $result = S('prize_apply_task..'.$user_id);

        //如果是查看奖品
        if($type == 2){

            //首次下单
            if($set == 1){

                if(!empty($result) && $result['full_5000'] == $user_id){
                    return $this->apiReturn(0, 'success', array('exists_apply'=>true));
                }

            }elseif ($set ==2) {
                //完成2次订单及以上交易且实付金额满5000元
                if(!empty($result) && $result['one_pay'] == $user_id){
                    return $this->apiReturn(0, 'success', array('exists_apply'=>true));
                }
            }

             return $this->apiReturn(0, 'error', array('exists_apply'=>false));

        }elseif ($type == 1) {
            //设置奖品
            $full_5000 = !empty($result['full_5000']) ? $result['full_5000']:false;
            $one_pay   = !empty($result['one_pay']) ? $result['one_pay']:false;

            if($set == 1){
                $full_5000 = $user_id;
            }else if($set == 2){
                $one_pay = $user_id;
            }

            S('prize_apply_task..'.$user_id,json_encode(array('full_5000'=>$full_5000,'one_pay'=>$one_pay)));
            return $this->apiReturn(0, 'success');
        }

    }

    /*
        返回用户是否已经完善信息
    */
    private function get_user_write_info(){
        $user_id = cookie('uid');
        $info = S_user_point_info($user_id);
        $u_info = array();
        if(isset($info['nike_name'])){
            $u_info[] = $info['nike_name'];
        }
        if(isset($info['com_name'])){
            $u_info[] = $info['com_name'];
        }
        if(isset($info['type_id'])){
            $u_info[] = $info['type_id'];
        }

        //是否完善资料任务
        return count($u_info);
    }


    /*
        给签到 搜索 完善个人信息发送积分
    */
    public function send_point(){

        $type = I('request.type', 1, 'intval');

        $result = $this->getUserType();

        //1.签到 2.搜索 3.完善个人信息
        if($type == 1){

            //检查是否已签到并且未领取积分
            if($result->task_list[5]['flag'] == 1  && $result->task_list[5]['is_apply'] == 0){
                $this->send_point_action(100);
            }

        }elseif($type == 2) {

            //检查是否已签到并且未领取积分
            if($result->task_list[1]['flag'] == 1  && $result->task_list[5]['is_apply'] == 0){
                $this->send_point_action(100);
            }

        }elseif($type == 3){

        }


    }

    /**
     * ERP根据公司获取用户ID
     * @return [type] [description]
     */
    public function getCompanyByName()
    {
        $name = I('request.name', '');
        $UserCompanyModel = D('UserCompany');
        if (empty($name)) {
            return $this->apiReturn(23037, '未找到相关公司');
        }
        $user_id = $UserCompanyModel->where(array('com_name' => $name))->getField('user_id');
        if (empty($user_id)) {
            return $this->apiReturn(23037, '未找到相关公司');
        }
        return $this->apiReturn(0, '成功', $user_id);
    }

    /**
     * ERP根据ID获取用户ID
     * @return [type] [description]
     */
    public function getErpCode()
    {
        $code = I('request.code', '');
        $UserMainModel = D('UserMain');
        if (empty($code)) {
            return $this->apiReturn(23038, '未找到ERP用户');
        }
        $user_id = $UserMainModel->where(array('user_code' => $code))->getField('user_id');
        if (empty($user_id)) {
            return $this->apiReturn(23038, '未找到ERP用户');
        }
        return $this->apiReturn(0, '成功', $user_id);
    }

    /**
     * 获取手机对应用户ID
     * @return [type] [description]
     */
    public function getMobile()
    {
        $mobile = I('request.mobile', '');
        $more = I('request.more', 0, 'intval');
        $user_id = S_account($mobile);
        if (empty($user_id)) {
            return $this->apiReturn(23040, '该手机号暂未被使用');
        }
        $db_mobile = D('UserMain')->getFieldByUserId($user_id, 'mobile');
        if (empty($db_mobile)) {
            S_user($user_id, null);
            S_account($mobile, null);
            return $this->apiReturn(23040, '该手机号暂未被使用');
        }
        if ($more) {
            $info = S_user($user_id);
            $data = array(
                'user_id' => $user_id,
                'sale_user_id' => $info['sale_user_id'],
            );
            return $this->apiReturn(0, '成功', $data);
        } else {
            return $this->apiReturn(0, '成功', $user_id);
        }
    }

    /**
     * 获取邮箱对应用户ID
     * @return [type] [description]
     */
    public function getEmail()
    {
        $email = I('request.email', '');
        $more = I('request.more', 0, 'intval');
        $user_id = S_account($email);
        if (empty($user_id)) {
            return $this->apiReturn(23041, '该邮箱暂未被使用');
        }
        $db_email = D('UserMain')->getFieldByUserId($user_id, 'email');
        if (empty($db_email)) {
            S_user($user_id, null);
            S_account($email, null);
            return $this->apiReturn(23041, '该邮箱暂未被使用');
        }
        if ($more) {
            $info = S_user($user_id);
            $data = array(
                'user_id' => $user_id,
                'sale_user_id' => $info['sale_user_id'],
            );
            return $this->apiReturn(0, '成功', $data);
        } else {
            return $this->apiReturn(0, '成功', $user_id);
        }
    }

    /**
     * 获取并绑定ERP用户与网站用户
     */
    public function bindErpCode()
    {
        $user_id = I('request.user_id', '');
        $code = I('request.code', '');

        $info = S_user($user_id);
        if ($info['user_code'] != $code) {
            $UserMainModel = D('UserMain');
            $save = array(
                'user_id' => $user_id,
                'user_code' => $code,
            );
            $res = $UserMainModel->save($save);
            if ($res === false) {
                return $this->apiReturn(23039, '绑定ERP用户失败', $save);
            }
            $info['user_code'] = $code;
            S_user($user_id, $info);
        }
        return $this->apiReturn(0, '成功', $user_id);
    }

    /**
     * 用户修改密码
     */
    public function changePassword()
    {
        $prePassword = I('request.prepassword', '', 'trim');
        $password = I('request.password', '', 'trim');
        $rePassword = I('request.repassword', '', 'trim');
        //原来密码是否为空
        if (!$prePassword) {
            return $this->apiReturn(23003, '原密码不能为空');
        }
        //新密码是否为空
        if (!$password) {
            return $this->apiReturn(23003, '新密码不能为空');
        }
        //密码位数是否正确 大于6
        if (intval(strlen($password)) < 6 ) {
            return $this->apiReturn(12007, '密码格式6-20字符，请重新设置');
        }
        //两次密码是否一致
        if ($password !== $rePassword) {
            return $this->apiReturn(23004, '两次输入的密码不一致');
        }
        //原始密码和修改密码是否一样
        $user_id = cookie('uid');
        $ucid = cookie('ucid');
        $userInfo = S_user($user_id);
        $salt = $userInfo['salt'];
        $prePassword = pwdhash($prePassword, $salt);
        if ($userInfo['password'] !== $prePassword) {
            return $this->apiReturn(23003, '原密码有误');
        }
        $modPassword = pwdhash($password, $salt);
        if ($userInfo['password'] == $modPassword) {
//            return $this->apiReturn(23013, '新密码不能与当前密码一样');
        }
        $userInfo['password'] = $modPassword;
        S_user($user_id, $userInfo);
        $userMainModel = D('UserMain');
        try {
            $userMainModel->startTrans();
            $save = array('password' => $modPassword);
            D('UserMain')->where(array('user_id'=>$user_id))->save($save);
            $ucenterResult = UcenterService::changePassword($ucid,$password);
            if($ucenterResult["code"] != 0){
                $userMainModel->rollback();
                return $this->apiReturn(0, '修改密码失败');
            }
            $userMainModel->commit();
        } catch (\Exception $e) {
            $userMainModel->rollback();
        }
        return $this->apiReturn(0, '修改密码成功');
    }

    /**
     * 用户新增密码 （仅适用于短信登录，且无密码的用户）
     */
    public function changePasswordFromMsg()
    {
        $password = I('request.password', '', 'trim');
        $rePassword = I('request.repassword', '', 'trim');
        //密码是否为空
        if (!$password) {
            return $this->apiReturn(23003, '密码不能为空');
        }
        //密码位数是否正确 大于6
        if (intval(strlen($password)) < 6 ) {
            return $this->apiReturn(12007, '密码格式6-20字符，请重新设置');
        }
        //两次密码是否一致
        if ($password !== $rePassword) {
            return $this->apiReturn(23004, '两次输入的密码不一致');
        }
        $user_id = cookie('uid');
        $ucId = cookie('ucid');
        $userInfo = S_user($user_id);
        $salt = $userInfo['salt'];
        $modPassword = pwdhash($password, $salt);
        $userInfo['password'] = $modPassword;
        S_user($user_id, $userInfo);
        try {
            $save = array('password' => $modPassword);
            $ucenterResult = UcenterService::changePassword($ucId,$password);
            if($ucenterResult["code"] != 0){
                return $this->apiReturn(0, '修改密码失败');
            }

            D('UserMain')->where(array('user_id'=>$user_id))->save($save);

        } catch (\Exception $e) {
        }
        return $this->apiReturn(0, '修改密码成功');
    }

    /**
     * 修改或绑定邮箱
     */
    public function bindEmail()
    {
        //是否是短信登录
        $res = $this->getUserType();
        // $password = I('request.password', '', 'trim');
        $account = I('request.account', '', 'trim');
        $user_id = cookie('uid');
        $userInfo = S_user($user_id);
        if ($res['err_code'] !== 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        // if (strval($res['data']['type']) === '1') { //有密码
        //     //需要输入密码进行校验
        //     $modPassword = pwdhash($password, $userInfo['salt']);
        //     if ($userInfo['password'] !== $modPassword) {
        //         return $this->apiReturn(23003, '原密码错误');
        //     }
        // }
        //邮箱格式有误
        if (!is_email($account)) {
            return $this->apiReturn(12010, '邮箱格式不正确');
        }
        //该邮箱是否已经注册
        if (S_account($account)) {
            return $this->apiReturn(12011, '该邮箱已经注册过了');
        }
        //该邮箱今天是否已经发送三次了
        $type = 2;
        $res = A('Reg')->activateOperationEmail($type, $user_id, $account);
        if ($res['err_code'] !== 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        $log = $userInfo['email'] ? '绑定邮箱，修改前：'.$userInfo['email'] : '绑定邮箱：'.$account;
        D('UserActionLog')->addLog($user_id, $user_id, 1, $log.'（未激活）');

        return $this->apiReturn(0, '邮箱发送成功');
    }

    /**
     * 激活会员中心的绑定邮箱
     */
    public function activateBindEmail()
    {
        $userTokenInfo = A('Public')->checkVerifyEmail();
        if ($userTokenInfo['err_code'] == '0') {
                $userTokenInfo = $userTokenInfo['data'];
            //改redis
                //清空原来的email->user_id
                $preEmail = $userTokenInfo['preEmail'];
                $email = $userTokenInfo['email'];
                $user_id = $userTokenInfo['user_id'];
                $userInfo = S_user($user_id);
                $ucId = !empty($userInfo['uc_id']) ? $userInfo['uc_id'] : 0;
                if($ucId && $email){
                    $ucenteResult = UcenterService::ucenterBindEmail($ucId,$email);
                    if($ucenteResult["code"] != 0){
                        return $this->apiReturn(1, '绑定邮箱失败');
                    }
                }
                S_account($preEmail, null);
                //userInfo email
                S_account($email, $user_id);

                $userInfo['email'] = $email;
                // 判断是否需要加积分（首次绑定邮件送积分）
                $preUserInfo = S_user($user_id);
                S_user($user_id, $userInfo);

            //数据库填写数据 成功与否不影响流程
                try {
                    $save = array('email' => $email);
                    $UserMainModel = D('UserMain');
                    $UserMainModel->where(array('user_id' => $user_id))->save($save);
                    // 同步修改CRM
                    $CrmUserModel = D('Crm/User');
                    $CrmUserModel->updateAccount($user_id, $save);

                    $point_info['email'] = !empty($preUserInfo['email'])?1:'';
                    $point = $this->mktScore($user_id,$point_info);
                } catch (\Exception $e) {
                }

                $log = $preUserInfo['email'] ? '激活邮箱，修改前：'.$preUserInfo['email'] : '激活邮箱：'.$email;
                D('UserActionLog')->addLog($user_id, $user_id, 1, $log);

                return $this->apiReturn(0, '成功', ['point'=>$point]);
        } else {
            return $this->apiReturn($userTokenInfo['err_code'], $userTokenInfo['err_msg'], '');
        }
    }

    // 会员统计--- 注册、登录
    public function getUserCount()
    {
        $user = D('UserMain');

        $data = array();

        $data = $user->getAllUserCount();

        return $this->apiReturn(0, '', $data);
    }

    // 会员统计--- 自定义查询
    public function customSearch()
    {
        $map = I('map', '');

        $data = D('UserMain')->getCustomData($map);

        return $this->apiReturn(0, '', $data);
    }

    /**
     * 记录用户行为首页滚动展示接口
     */
    public function recordUserBehaviorToShow(){
        if (!$this->auth()) {
            exit();
        }
//        $redis = new \Think\Cache\Driver\RedisRW();

//        $input_data = I('request.');
//        $today = date('Y-m-d', time());
//        file_put_contents('./'.$today.'.txt',json_encode($input_data),FILE_APPEND);

        $redis = new \Redis();
        $redis->connect(C('REDIS_HOME_ROLL_WRITE'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        //todo-------------------------------------------------->调整方式。。。。。。。。。。。。。。。。。。。。。

        //公共信息
        $type = I('type',0);//行为类型 1加车 2下单 3获得积分 4兑换奖品 5领券
        $user_id = I('user_id',0);
        if (0==$type){
            return $this->apiReturn(26001, '传入类型错误', '');
        }
        if (0==$user_id || 128688==$user_id){
            return $this->apiReturn(26002, '缺少用户id或为内部账号', '');
        }
        $info = S_user($user_id);
        if (empty($info)){
            return $this->apiReturn(26003, '未获取到用户信息', '');
        }
        $beh['account'] = $info['mobile']?$info['mobile']:$info['email'];
        $beh['behavior'] = C('BEHAVIOR_TYPE.'.$type);
        $beh['behavior_time'] = time();
        $beh['avoid_repeat']  = rand(1,10000);

        //行为类型
        switch ($type){
            case 1://加车
                $goods_count = I('goods_count',0);
                if (empty($goods_count)){
                    return $this->apiReturn(26004, '加入购物车的商品数量不能为0', '');
                }
                $beh['detail'] = '将'.$goods_count.'个电子元器件加入了购物车';
                break;
            case 2://下单
                $order_amount = I('order_amount',0);
                $order_goods_type = I('order_goods_type',0);
                $currency = I('currency',0);
                $coupon_type = I('coupon_type',0);
                $coupon_amount = I('sale_amount',0);
                if (empty($order_amount)){
                    return $this->apiReturn(26005, '订单金额不能为0', '');
                }
                if (empty($order_goods_type) || !in_array($order_goods_type,array(1,2))){
                    return $this->apiReturn(26006, '订单类型错误', '');
                }
                if (empty($currency) || !in_array($currency,array(1,2))){
                    return $this->apiReturn(26007, '汇率类型错误', '');
                }
                if(!in_array($coupon_type,array(0,1,2))){
                    return $this->apiReturn(26019, '优惠券类型参数错误', '');
                }
                if(0!=$coupon_type){
                    if($coupon_amount<=0){
                        return $this->apiReturn(26020, '优惠券金额参数错误', '');
                    }
                    if(1==$coupon_type){
                        $coupon_amount_text = strval($coupon_amount).'元';
                    }else{
                        $coupon_amount_text = strval($coupon_amount*10).'折';
                    }
                    $beh['detail'] = '使用'.$coupon_amount_text.'优惠券下单购买'.$order_amount.C('CURRENCY.'.$currency).C('ORDER_GOODS_TYPE.'.$order_goods_type).'商品';
                    break;
                }
                $beh['detail'] = '下单购买'.$order_amount.C('CURRENCY.'.$currency).C('ORDER_GOODS_TYPE.'.$order_goods_type).'电子元件';
                break;
            case 3://获得积分
                $flow_type = I('flow_type',0);
                $flow_reason_type = I('flow_reason_type',0);
                $flow_point = I('flow_point',0);
                if(empty($flow_type) || 1!=$flow_type){
                    return $this->apiReturn(26008, '积分流向类型参数错误', '');
                }
                if(!in_array($flow_reason_type,array(1,2,3,4))){
                    return $this->apiReturn(26009, '获得积分原因类型错误', '');
                }
                if(empty($flow_point)){
                    return $this->apiReturn(26010, '获得积分数量参数错误', '');
                }
                $beh['detail'] = C('GET_POINT_TYPE.'.$flow_reason_type).'获得'.$flow_point.'积分';
                break;
            case 4://兑换奖品
                $flow_type = I('flow_type',0);
                $flow_reason_type = I('flow_reason_type',0);
                $flow_point = I('flow_point',0);
                $point_prize_name = I('point_prize_name','');
                if(!in_array($flow_type,array(2))){
                    return $this->apiReturn(26011, '积分流向类型参数错误', '');
                }
                if(!in_array($flow_reason_type,array(2))){
                    return $this->apiReturn(26012, '消耗积分原因类型错误', '');
                }
                if(empty($flow_point)){
                    return $this->apiReturn(26013, '消耗积分数量参数错误', '');
                }
                if(empty($point_prize_name)){
                    return $this->apiReturn(26014, '兑换奖品名称参数错误', '');
                }
                $beh['detail'] = '用'.$flow_point.'个积分兑换了'.$point_prize_name.'一个';
                break;
            case 5://领券
//                $coupon_condition = I('coupon_condition','');
                $coupon_type = I('coupon_type',0);
                $coupon_amount = I('sale_amount',0);
                $require_amount = I('require_amount',0);
                if(!in_array($coupon_type,array(1,2))){
                    return $this->apiReturn(26015, '优惠券类型参数错误', '');
                }
                if(empty($coupon_amount)){
                    return $this->apiReturn(26016, '优惠券减免值参数错误', '');
                }
                if(empty($require_amount)){
                    return $this->apiReturn(26017, '优惠券使用门槛参数错误', '');
                }
                if(1==$coupon_type){
                    $tt = '减'.$coupon_amount.'元';
                }else{
                    $tt = '打'.($coupon_amount*10).'折';
                }
                $beh['detail'] = '获得了满'.$require_amount.'元'.strval($tt).'电子元件优惠券一张';
                break;
            default:
                return $this->apiReturn(26018, '用户行为类型参数错误', '');
                break;
        }

        //zadd到redis
        $last_behavior = $redis->zRange('api_roll_behavior',-1,-1,true);
        if(empty($last_behavior)){
            $res = $redis->zAdd('api_roll_behavior',1,json_encode($beh));
            if (!$res){
                return $this->apiReturn(26021, '用户行为数据加入redis失败', '');
            }
        }else{
            foreach ($last_behavior as $v){//$v 为score
                $res = $redis->zAdd('api_roll_behavior',$v+1,json_encode($beh));
                if (!$res){
                    return $this->apiReturn(26022, '用户行为数据加入redis失败', '');
                }
            }
        }
        return $this->apiReturn(0, 'success', '');
    }

    /**
     * 获取用户行为展示接口
     */
    public function getUserBehaviorToShow(){
        if (!$this->auth()) {
            exit();
        }
//        $redis = new \Think\Cache\Driver\RedisRW();
        $redis = new \Redis();
        $redis->connect(C('REDIS_HOME_ROLL_WRITE'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));

        //取出最近50条
        $real_data = $redis->zRevRange('api_roll_behavior',0,C('SHOW_BEHAVIOR_COUNT')-1);
        //如果redis挂了 直接取假数据
        if(empty($real_data)){
            $real_data = $this->getFakeData(C('SHOW_BEHAVIOR_COUNT'),0,true);
        }

        //最终要显示的数据
        $show_data = array();
        $left_real_data = array();
        foreach ($real_data as $v){
            $v = json_decode($v,true);
            if((time()-$v['behavior_time'])<60*60){
                $show_data[]      = $v;
            }else{
                $left_real_data[] = $v;
            }
        }

        //如果是Mon-Fri 9:00-21:00
        //要保证 一小时内数据 >= 30 条  如果不够，从假数据中补足  假数据必须都为内部账号  剩余20条读取额外真实数据  时间均显示为1小时前
        //如果不是Mon-Fri 9:00-21:00
        //要保证 一小时内数据 >= 10 条  如果不够，从假数据中补足  假数据必须都为内部账号  剩余40条读取额外真实数据  时间均显示为1小时前
        if(count($show_data)<C('SHOW_BEHAVIOR_COUNT')){
            $least_active_count = isWorkTime()?C('LEAST_ACTIVE_COUNT.'.'ACTIVE'):C('LEAST_ACTIVE_COUNT.'.'NOT_ACTIVE');
            $show_data = $this->constructShowData($redis,$show_data, $least_active_count, $left_real_data,C('SHOW_BEHAVIOR_COUNT'));
        }
        //格式化输出 时间：XX前  账号：加*
        $show_data = $this->handleShowDataTimeAndAccount($show_data);

        return $this->apiReturn(0, 'success', $show_data);
    }

    /** 要展示的数据 小于50条 的话 补齐数据
     * @param $show_data            - 要显示的数据
     * @param $least_active_count   - 所需1小时内最少数据数
     * @param $left_real_data       - 超过一小时的真实数据
     * @param $show_count           - 要显示的数据条数 50
     * @return mixed                - 最终要显示的50条数据
     */
    private function constructShowData($redis,$show_data,$least_active_count,$left_real_data,$show_count){
        //获取假数据的起始偏移量
        $fake_offset = 0;
        if (count($show_data)<$least_active_count) { //未达到 所需活跃数据数 用假数据补齐 所需活跃数据数
            $pre_fake_data = json_decode($redis->get('roll_fake_in_hour'),true);
            if($pre_fake_data){
                $cur_fake_data = array();
                foreach ($pre_fake_data as $k=>$pfd){
                    if(time()-$pfd['behavior_time']<60*60){
                        $show_data[] = $pfd;
                        $cur_fake_data[] = $pfd;
                    }else{
                        $fake_offset = $k+1;
                        break;
                    }
                }
                if(count($show_data)<$least_active_count){
                    $fake_data  = $this->getFakeData($least_active_count-count($show_data),$fake_offset,true);
                    $fake_offset = $fake_offset + count($fake_data)+1;
                    $show_data  = array_merge($show_data,$fake_data);
                }
                if(!empty($fake_data)){
                    $cur_fake_data = array_merge($cur_fake_data,$fake_data);
                }
                $res = $redis->set('roll_fake_in_hour',json_encode($cur_fake_data));
            }else{
                $fake_data  = $this->getFakeData($least_active_count-count($show_data),$fake_offset,true);
                $res = $redis->set('roll_fake_in_hour',json_encode($fake_data));
                //获取完 则后移偏移量
                $fake_offset = count($fake_data);
                $show_data  = array_merge($show_data,$fake_data);
            }
            //将剩余真实数据取出20个 补齐50   如果剩余真实数据不够了  继续从假数据中补齐
            if(count($left_real_data)>=($show_count-$least_active_count)){
                $show_data = array_merge($show_data,array_slice($left_real_data,0,$show_count-$least_active_count));
            }else{
                $show_data = array_merge($show_data,$left_real_data);
                $show_data = array_merge($show_data,$this->getFakeData(($show_count-$least_active_count)-count($left_real_data),$fake_offset,false));
            }
        }else{
            //已达到 所需活跃数据数 用假数据补齐 要展示的数量即可
            foreach ($left_real_data as $left_real_dt){
                $show_data[] = $left_real_dt;
            }
            if(count($show_data)<$show_count){
                $fake_data = $this->getFakeData($show_count-count($show_data),$fake_offset,false);
                $res = $redis->set('roll_fake_before_hour',json_encode($fake_data));
                $show_data = array_merge($show_data,$fake_data);
            }
        }
        return $show_data;
    }

    /**
     * 获取假数据
     * @param $fake_count   -获取假数据的条数
     * @param $fake_offset  -当前假数据的偏移量 获取数据的起始index
     * @param $is_active    -是否需要为1小时内的数据
     * @return mixed
     */
    private function getFakeData($fake_count,$fake_offset,$is_active){
        //本地假数据
        $file_path = './public/fake_data.json';
        $fake_data_arr = json_decode(file_get_contents($file_path),true);
        $return_data = array();
        if($fake_count>0){//获取条数
            if($fake_offset>=0 && ($fake_offset+$fake_count)<C('SHOW_BEHAVIOR_COUNT')){//数组偏移量检测
                $return_data = array_slice($fake_data_arr,$fake_offset,$fake_count);
            }
        }
        if(count($return_data)>0){
            //根据 条件 将假数据 时间改为 一小时内/一小时前
            if($is_active){//一小时内的数据
                $rand_numbers = getRandNumbers(1,60*60,50);
                foreach ($return_data as $k=>&$v){
                    $v['behavior_time'] = time()-$rand_numbers[$k];
                }
                unset($v);
            }else{
                foreach ($return_data as &$v){
                    $v['behavior_time'] = time()-(60*61);//一小时前
                }
                unset($v);
            }
        }
        return $return_data;
    }

    /** 处理数据时间和账号    将时间戳 变为XX分钟/小时前   账号中间变*
     * @param $show_datas
     * @return array
     */
    private function handleShowDataTimeAndAccount($show_datas)
    {
        //按照行为时间倒序排序
        $sort = array();
        foreach ($show_datas as $show_data) {
            $sort[] = $show_data['behavior_time'];
        }
        array_multisort($sort, SORT_DESC, $show_datas);

        //添加 将时间戳转为 文字的值  XX前
        $cur_time = time();
        foreach ($show_datas as $k => &$fd) {
            $is_an_hour_ago = (($cur_time - $fd['behavior_time']) <= 60 * 60) ? false : true;
            $fd['behavior_time_text'] = $this->transTimeToString($cur_time - $fd['behavior_time'], $is_an_hour_ago);
            $account = trim($fd['account']);
            if (is_numeric($account)) {
//                $hide_str = substr($account,3,4);
//                $fd['account'] = str_replace($hide_str,'****',$account);
                $fd['account'] = substr_replace($account, '****', 3, 4);
            } else {
                if (is_email($account)) {
                    $email_arr = explode('@', $account);
                    $first_char = substr($account, 0, 1);
                    $fd['account'] = $first_char . '****@' . $email_arr[1];
                } else {
                    unset($show_datas[$k]);
                }

            }
        }
        unset($fd);

        return $show_datas;
    }

    /** 将时间戳转为文字  XX前
     * @param int $diff_time
     * @param bool $is_an_hour_ago
     * @return string
     */
    private function transTimeToString($diff_time,$is_an_hour_ago=false){
        if($is_an_hour_ago){
            return '一小时前';
        }
        $min = intval($diff_time/60);
        if(0===$min){
            return '刚刚';
        }else{
            return $min.'分钟前';
        }
    }

    /** 每天定时 执行删除 多余Redis中的用户行为日志 的任务 00:00:00
     * @return array
     */
    public function crontabDeleteBehaviorInRedis(){
        //todo:当前接口为了测试方便  加入了 无需登录  即可访问  正常是传k1,k2即可
        //todo:连接redis 暂时使用方式
        $redis = new \Redis();
        $redis->connect(C('REDIS_HOME_ROLL_WRITE'),C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        $count = $redis->zCard('api_roll_behavior');
        if($count>C('SHOW_BEHAVIOR_COUNT')){
            $res = $redis->zRemRangeByRank('api_roll_behavior',0,$count-C('SHOW_BEHAVIOR_COUNT')-1);
            if(!$res){
                return $this->apiReturn(90001,'fail');
            }
        }
        return $this->apiReturn(0, 'success');
    }

    // 会员系统批量导入公司信息 (若公司信息已存在，则不能修改)
    public function importComInfo()
    {
        $account     = I('account', '');
        $user_id     = S_account($account); // 获取用户ID

        if (!$user_id) return $this->apiReturn(90002, '未获取到会员账号');

        $company     = S_company($user_id); // 获取公司信息
        $companyInfo = [];

        if ($company && $company['com_name'] && $company['com_address'] && $company['com_telphone'] && $company['com_desc']) {
            return $this->apiReturn(90003, '公司信息已存在', $account);
        } else {
            $companyInfo['com_name']     = $company['com_name']     = I('com_name', '');
            $companyInfo['com_address']  = $company['com_address']  = I('com_address', '');
            $companyInfo['com_telphone'] = $company['com_telphone'] = I('com_telphone', '');
            $companyInfo['com_desc']     = $company['com_desc']     = I('com_desc', '');

            $UserCompany = D('UserCompany');

            $res = $UserCompany->where(['user_id'=>$user_id])->find();

            if ($res) {
                $UserCompany->where(['user_id'=>$user_id])->data($companyInfo)->save(); // 更新数据
            } else {
                $companyInfo['user_id'] = $user_id;
                $UserCompany->data($companyInfo)->add(); // 新增数据
            }

            S_company($user_id, $company); // 加入缓存
        }

        return $this->apiReturn(0, 'success');
    }


    // 设置用户缓存字段
    public function setCacheField()
    {
        $str = I('field', '');
        $p = I('p', 1);
        $limit = 1000;
        $first = $limit * ($p - 1);

        if (!$str) {
            echo '无添加字段';
            die;
        }

        if (strpos($str, ',') !== false) {
            echo '只能添加一个字段';
            die;
        }

        $field = 'user_id, ' . $str;

        $UserMainModel = D('UserMain');

        $map = [];
        $map['is_type'] = 0; // 获取所有真实用户

        $count = $UserMainModel->where($map)->count();

        $total = ceil($count / $limit); // 总页数

        if ($p <= $total) {
            $p++;
        } else {
            echo '缓存字段设置完毕';
            die;
        }

        $user = $UserMainModel->where($map)->field($field)->limit($first . ',' . $limit)->order('user_id desc')->select();

        if ($user) {
            foreach ($user as $v) {
                $user_info = S_user($v['user_id']);

                if (!$user_info) continue; // 缓存不存在，则跳过

                $user_info[$str] = $v[$str];

                S_user($v['user_id'], $user_info); // 添加缓存字段
            }
        }

        header('Refresh: 1;url=' . API_DOMAIN . '/user/setcachefield?field=' . $str . '&p=' . $p);
        die;
    }

    // 获取用户领取机会、邀约人数
    public function getUserSampleInfo()
    {
        $user_id = cookie('uid');

        $data['apply_count'] = D('UserInfo')->where(['user_id' => $user_id])->getField('apply_count'); // 领取机会
        $data['invite_count'] = D('UserSampleInvite')->where(['user_id' => $user_id, 'type' => 1])->count(); // 邀约人数

        return $this->apiReturn(0, 'success', $data);

    }

    // 获取用户工具尺等特殊活动的相关信息
    public function getUserSpecialInfo()
    {
        $user_id = cookie('uid');

        $data['apply_count'] = D('UserInfo')->where(['user_id' => $user_id])->getField('apply_count'); // 领取机会
        $data['apply_count'] = (int)$data['apply_count'];
        $data['invite_count'] = D('UserSampleInvite')->where(['user_id' => $user_id, 'type' => 2])->count(); // 邀约人数
        $data['invite_count'] = (int)$data['invite_count'];
        $sampleModel = new SampleModel();
        //是否可以领取工具尺
        $data['can_get_ruler'] = $sampleModel->getCanGetRuler($user_id, $data['invite_count']) ? 1 : 0;
        //邀请人数大于等于5,可以跳转PCB
        $data['can_get_pcb'] = $data['invite_count'] >= 5 ? 1 : 0;
        $data['goods_id'] = C('RULER_ACTIVITY_GOODS_ID');
        $data['has_order'] = $sampleModel->checkHasRulerOrder($user_id) ? 1 : 0;
        return $this->apiReturn(0, 'success', $data);
    }

    // 添加用户信息表
    public function addUserInfo()
    {
        $num   = I('num', 0); // 标记修改的数量
        $p     = I('p', 1);
        $limit = 1000;
        $first = $limit * ($p - 1);

        $UserMainModel = D('UserMain');
        $UserInfoModel = D('UserInfo');

        $map = [];
        $map['is_type'] = 0; // 获取所有真实用户

        $count = $UserMainModel->where($map)->count();

        $total = ceil($count / $limit); // 总页数

        if ($p <= $total) {
            $p++;
        } else {
            echo '用户信息表设置完毕'; die;
        }

        $user = $UserMainModel->where($map)->field('user_id')->limit($first . ',' . $limit)->select();

        if ($user) {
            $add_batch = [];

            foreach ($user as $v) {
                $res = $UserInfoModel->where(['user_id' => $v['user_id']])->getField('ui_id');

                if ($res) continue;

                $add_batch[] = ['user_id' => $v['user_id']];
                ++$num;
            }

            $UserInfoModel->addAll($add_batch);
        }

        header('Refresh: 1;url=' . API_DOMAIN . '/user/adduserinfo?p='.$p.'&num='.$num); die;
    }

    // 将正常用户同步到CRM，过滤已同步到CRM的用户
    public function sysUserToCrm()
    {
        $num   = I('num', 0); // 标记修改的数量
        $p     = I('p', 1);
        $limit = 1000;
        $first = $limit * ($p - 1);

        $UserMainModel = D('UserMain');
        $CrmModel = D('Crm/User');

        $map = [];
        $map['is_type'] = 0; // 获取所有真实用户
        $map['is_test'] = 0;

        $count = $UserMainModel->where($map)->count();

        $total = ceil($count / $limit); // 总页数

        if ($p <= $total) {
            $p++;
        } else {
            echo '用户信息表设置完毕'; die;
        }

        $user = $UserMainModel->where($map)->field('user_id')->limit($first . ',' . $limit)->select();

        if ($user) {
            $add_batch = [];

            // 推入到队列
            $queue_name = C('CRM_PUSH_USER');
            $RbmqModel = D('Common/Rbmq');

            foreach ($user as $v) {
                $crm_user_id = $CrmModel->where(['outter_uid' => $v['user_id']])->getField('user_id');

                if ($crm_user_id) continue;

                $data['user_id'] = intval($v['user_id']);
                $RbmqModel->queue($queue_name)->push($data, $queue_name);

                ++$num;
            }
        }

        header('Refresh: 1;url=' . API_DOMAIN . '/user/sysusertocrm?is_liexin=1&p='.$p.'&num='.$num); die;
    }

    //用户浏览历史记录接口
    public function getUserBrowseHistory()
    {
        $userId = cookie('uid');

    }

    /**
     * 
     * 该账户下对应公司的账期总金额、账期剩余金额，若存在多个，取创建时间最近的公司即可
     */
    public function getUserBillInfo(){
        $user_id = I('user_id') ? I('user_id') : cookie('uid');
        $uc_id = I('uc_id') ? I('uc_id') : cookie('ucid');
        $org_id = I('org_id') ? I('org_id') : 1;
  

        $taxinfoModel = D("Invoice/Taxinfo");

        $taxList = $taxinfoModel->where(['user_id'=>$user_id,"uc_id"=>$uc_id,"org_id"=>$org_id,"audit_status"=>2])->order("tax_id desc")->field("tax_id,tax_title")->select();

        $tax_id_arr = array_column($taxList,"tax_id");

        $list = D("Crm/Crm")->getCreditList($tax_id_arr);

        $res["billing_info"]=[]; //结算信息
        $res["billing_info"]["total_credit"]= "￥0.00"; //结算信息
        $res["billing_info"]["available_credit"]= "￥0.00"; //结算信息

        if(empty($list)){
            return $this->apiReturn(0,'success', $res);
        }

        $erpSn = D("Crm/Crm")->getCompanyErpComSn($list[0]["tax_title"]);
        if(empty($erpSn)){
            return $this->apiReturn(0,'success', $res);
        }
        $syncData = [];
        $syncData["CU_NUMBER"] = dataGet(CrmModel::ERP_COM_NO,$list[0]["sale_com_id"],'MY001');
        $syncData["CUSTOMER"] = $list[0]["tax_title"];
        $syncData["CUSTOMER_ERP_NUMBER"] =  $erpSn;

        if(empty($com_info["erp_com_sn"])){
            return $this->apiReturn(0,'success', $res);
        }
        
        try {
            $orderErp = new \SoapClient(ERP_DOMAIN.'/ormrpc/services/WSCreditPeriodFacade?wsdl');
            $erp_res = $orderErp->getCreditAmount($syncData);
            $erp_res = json_decode($erp_res, true);
            
            if(empty($erp_res) || empty($erp_res["code"]) || $erp_res["code"] != 0){
                return $this->apiReturn(0,'success', $res);
            }
            $return_data = $erp_res["data"];
            $res["billing_info"]["total_credit"]= $return_data["Amt"]; //结算信息
            $res["billing_info"]["available_credit"]= $return_data["availableAmt"]; //结算信息
            return $erp_res;
        } catch (\Exception $e) {
            // p($e->getMessage());exit;
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn(0,'success', $res);
        }
    }
}