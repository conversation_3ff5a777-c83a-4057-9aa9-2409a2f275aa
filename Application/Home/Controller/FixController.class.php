<?php
namespace Home\Controller;

use Home\Controller\BaseController;
use Home\Model\UserMainModel;
use Home\Services\UcenterService;
use mysql_xdevapi\Exception;

//api新增接口 hcy 2023.5.23
class FixController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), array('status', 'transstatus', 'getallorder', 'getorderdetails', 'admincheck', 'applyadjust', 'changeorder', 'deletegoods', 'test', 'ordercount', 'cancelpay', 'send', 'invshipping', 'crontabconfirmsend', 'alllist', 'contractinfo', 'sampleinfo', 'selfcancel', 'crontaborderpoint','updateusercreatetime', 'handleorderrefund', 'handlepaylog', 'sysiteminfo','checktuangouorderisok', 'lockskuaction'))) {
            // 检查登录
            if (in_array(strtolower(ACTION_NAME), array('lists', 'info', 'count', 'invoice', 'shipping', 'lastshipping'))) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }


    /**
     * 会员基本信息新增或修改
     */
    public function updateUserInfo()
    {
        $user_id = cookie('uid');
        $ucId = cookie('ucid');
        $email = I('email', '', 'trim');
        $qq = I('qq', '', 'trim');
        $wx = I('wx', '', 'trim');

        if (!empty($email)) {
            $check = "/\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/";
            if (!preg_match($check, $email)) {
                return $this->apiReturn(1, "邮箱格式不正确！");
            }
        }
        if ($qq) {
            if (!is_numeric($qq)) {
                return $this->apiReturn(23034, '公司传真仅能为数字');
            }
        }

        try {

            $UserMainModel = D('UserMain');
            $UserInfoModel = D('UserInfo');

            $userWhere = ['user_id' => $user_id];

            if ($email){
                $ucenteResult = UcenterService::ucenterBindEmail($ucId,$email);
                if($ucenteResult["code"] != 0){
                    return $this->apiReturn(1, '绑定邮箱失败');
                }
                 $UserMainModel->where($userWhere)->save(["email"=>$email]);
            }

            $upInfo = [];
            if ($qq){
                $upInfo["qq"] = $qq;
            }
            if ($wx){
                $upInfo["wx"] = $wx;
            }

            if ($upInfo){
                 $UserInfoModel->where($userWhere)->save($upInfo);
            }

            #刷新缓存
            $rs_info = S_user($user_id);
            $info = $UserMainModel->find($user_id);

            $user_info = $UserInfoModel->where($userWhere)->find();
            $info["qq"] = data_get($user_info,"qq");
            $info["wx"] = data_get($user_info,"wx");

            $type_id = D("UserCompany")->where($userWhere)->getField("type_id");
            $info["user_type"] = $type_id > 0 ? 1:2; //用户类型:  1 企业  2 个人

            $info = array_merge($rs_info, $info);


            S_user($user_id, $info);

            return $this->apiReturn(0, '成功');

        } catch (\Exception $e) {

            return $this->apiReturn(1001, '失败:'.$e->getMessage());
        }
    }


}