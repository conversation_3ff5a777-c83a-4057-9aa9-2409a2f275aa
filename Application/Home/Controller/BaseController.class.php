<?php
namespace Home\Controller;

use Payment\Cpcn;

class BaseController extends \Common\Controller\BaseController
{
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 发送短信(使用模板)
     * @param  [type]  $mobile [description]
     * @param  integer $tmpl   [description]
     * @param  array   $param  [description]
     * @return [type]          [description]
     */
    public function sendSms($mobile, $tmpl = 1, $param = array(), $smstype = '1', $channel = null)
    {
        //选择模板ID
        switch (strval($tmpl)) {
            case '1': // 注册 验证码
                $keyword = 'reg-code';
                break;
            case '2': // 忘记密码
                $keyword = 'forget-password';
                $user_id = S_account($mobile);
                if ($user_id) {
                    $user_info = S_user($user_id);
                    $com_info = S_company($user_id);
                    if ($user_info['mobile']) {
                        $param['mobile'] = $user_info['mobile'];
                        if ($user_info['intl_code']) {
                            $param['mobile'] = $user_info['intl_code'] . '+' . $user_info['mobile'];
                        }
                    }
                    if ($user_info['email']) {
                        $param['email'] = $user_info['email'];
                    }
                    if ($user_info['user_head']) {
                        $param['user_head'] = $user_info['user_head'];
                    }
                    if ($com_info['com_name']) {
                        $param['com_name'] = $com_info['com_name'];
                    }
                    if ($com_info['type_id']) {
                        $param['type_name'] = C("SUPPLIER_TYPE.{$com_info['type_id']}");
                    }
                }
                break;
            case '3': // 进口报关 已登录
                $keyword = 'custom-login';
                break;
            case '4': // 进口报关 未登录
                $keyword = 'custom-unlogin';
                break;
            case '5': // 活动 邀约
                $keyword = 'activity-inviter';
                $smstype = 2;
                break;
            case '6': // 活动 被邀约
                $keyword = 'activity-invitee';
                $smstype = 2;
                break;
            case '7': // 免密登录
                $keyword = 'login-code';
                break;
            case '8': // 普通验证码
                $keyword = 'common-code';
                break;
            case '9':
                $keyword = 'supplier-remind';
                break;
            case '10':
                $keyword = 'login-code-activity';
                break;
            case '20'://供应链免密登录 短信新签名
                $keyword = 'supply-login-code';
                break;
            case '21'://信用申请
                $keyword = 'fengkong-zhangqi-check-code';
                break;
            case '22'://供应链信用申请
                $keyword = 'fengkong-zhangqi-check-code-gyl';
                break;
            default:
                # code...
                break;
        }

        $code = '';
        if (isset($param['code'])) {
            // $type = !empty($param['type']) ? $param['type'] : 1;
            //记录等候验证
            $code = pwdhash($param['code'], C('SMS_SALT'));//加密
            $value = array(
                'overtime' => $_SERVER['REQUEST_TIME'] + 300,//有效期5分钟
                'code' => $code,
            );
            if (empty($channel)) {
                session_sms($mobile, $value);//共用验证码
            } else {
                session_sms($mobile.'.'.$channel, $value);//分渠道验证码
            }
        }
        $is_oversea = false;
        if (strstr($mobile, '+')) {
            $is_oversea = true;
        }
        // 录入消息列表
        $param_json =  json_encode($param);
        $check['pf'] = platform();
        $check['keyword'] = $keyword;
        $check['touser'] = json_encode($mobile);

        $check['data'] = $param_json;
        $check['channel_type'] = 2; // 短信
        $check['is_ignore'] = true;
        $check['is_oversea'] = $is_oversea;
        $check['ex_int'] = $smstype;
        $check['ex_str'] = $code;
        $check = array_merge($check, authkey());
        $authkey = authkey();
        $_REQUEST["k1"] = $authkey["k1"] ;
        $_REQUEST["k2"] = $authkey["k2"] ;
        $_REQUEST["pf"] = $authkey["pf"] ;
        $res = A("Message/Message")->sendMessageByAutoV2($check);
        // $res = post_curl(API_DOMAIN.'/msg/sendMessageByAuto', $check);
        if (!empty($res)) {
            $res = json_decode($res, true);
            //记录某渠道发送的次数
            if (!is_null($channel)) {
                $count = intval(S_sms($mobile, $channel));
                S_sms($mobile, $channel, ++$count);
            }
        }

        $this->add_msg_count();

        return true;
    }

    /*
        1.记录发短信的总次数并在原有基础上+1 (存活时间 一天)
        2.记录该用户ip在一个小时内的发信次数 
    */
    private function add_msg_count(){

        $redis = redis_init();
        $spider_msg_count = $redis->get('spider_msg_count');
       
        if(!empty($spider_msg_count)){

            //先获取时间,避免被覆盖成-1
            $time = $redis->ttl('spider_msg_count');

            $redis->set('spider_msg_count',($spider_msg_count+1),$time);
           
        }else{
            
            $redis->set('spider_msg_count',1,86400);
        }

        //记录该ip的发信次数到redis
        $ip = getip();


        $ip_msg_count = $redis->get('spider_msg_count_'.$ip);

        if(!empty($ip_msg_count)){
            //先获取时间,避免被覆盖成-1
            $time = $redis->ttl('spider_msg_count_'.$ip);
            $redis->set('spider_msg_count_'.$ip,($ip_msg_count+1),$time);

        }else{
            $redis->set('spider_msg_count_'.$ip,1,3600);
        }



    }

    protected function setBindUser($pf = -1)
    {
        $res = post_curl(API_DOMAIN.'/cart/mergecart', authkey($pf), array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 更新购物车
     * @return [type] [description]
     */
    protected function updateCart($pf = -1)
    {
        $res = post_curl(API_DOMAIN.'/cart/updateusercart', authkey($pf), array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    protected function setCartCount()
    {
        $res = post_curl(API_DOMAIN.'/cart/setcount', authkey(), array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取退款信息
     * @param  integer $refund_id [description]
     * @param  string  $refund_sn [description]
     * @return [type]             [description]
     */
    protected function getRefund($refund_id = 0, $refund_sn = '')
    {   
        if (!empty($refund_id)) {
            $data['refund_id'] = $refund_id;
        }
        if (!empty($refund_sn)) {
            $data['refund_sn'] = $refund_sn;
        }
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN.'/refund/info', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 根据流水号获取退款信息
     * @param  [type] $order_sn [description]
     * @return [type]           [description]
     */
    protected function getRefundSerialInfo($serial_number)
    {
        $data['serial_number'] = $serial_number;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN.'/refund/serialInfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 设置退款流水号
     * @param  [type] $order_sn [description]
     * @return [type]           [description]
     */
    protected function setRefundFinish($refund_id, $serial_number, $refund_time)
    {
        $data['refund_id'] = $refund_id;
        $data['serial_number'] = $serial_number;
        $data['refund_time'] = $refund_time;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN.'/refund/setfinish', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }


    /**
     * 获取中金实例
     * @return [type] [description]
     */
    protected function getCpcnInstance($mode = 2)
    {
        static $cpcn_object;
        if (!isset($cpcn_object[$mode])) {
            Vendor('payment.cpcn.cpcn');//中金支付sdk引入
            $cpcn_object[$mode] = new Cpcn($mode);
        }
        return $cpcn_object[$mode];
    }

    /**
     * 获取用户可用余额
     * @param  [type] $user_id [description]
     * @return [type]          [description]
     */
    protected function getBalance($user_id)
    {
        $cpcn = $this->getCpcnInstance();
        $today = date('Y-m-d');
        $info = $cpcn->queryOrder(get_wallet($user_id), $today, $today);
        if ($info === false) {
            return $info;
        }
        return floatval($info->Balance) / 100;
    }

    /**
     * 发起校验银行卡申请，中金发送短信验证码
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    protected function verifyBankCard($data)
    {
        $cpcn = $this->getCpcnInstance();
        $data['card_type'] = $data['card_type'] == 1 ? 10 : 20;
        $res = $cpcn->verifyCard($data);
        if ($res === false) {
            $res = $cpcn->error;
        }
        return $res;
    }

    /**
     * 校验中金验证码，获取银行卡类型信息
     * @param  [type] $serial_number [description]
     * @param  [type] $sms_code      [description]
     * @return [type]                [description]
     */
    protected function verifyBindBankCard($serial_number, $sms_code)
    {
        $cpcn = $this->getCpcnInstance();
        $data = array(
            'serial_number' => $serial_number,
            'sms_code' => $sms_code
        );
        $res = $cpcn->verifyBindCard($data);
        return $res;
    }

    /**
     * 发送安全手机短信验证码
     * @param  [type] $mobile  [description]
     * @param  [type] $verify  [description]
     * @param  [type] $channel [description]
     * @param  [type] $type    [图形验证码ID]
     * @return [type]          [description]
     */
    protected function toSafeSms($mobile, $verify, $channel, $type)
    {
        $data['mobile'] = $mobile;
        $data['verify'] = $verify;
        $data['channel'] = $channel;
        $data['type'] = $type;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN.'/public/smsverify', $data, array($this->loginCookie(cookie())));
        echo $res;die();
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 检测安全手机短信发放次数
     * @param  [type] $mobile    [description]
     * @param  [type] $intl_code [description]
     * @param  [type] $channel   [description]
     * @return [type]            [description]
     */
    protected function toCheckSafeSms($mobile, $intl_code, $channel)
    {
        $data['mobile'] = $mobile;
        $data['intl_code'] = $intl_code;
        $data['channel'] = $channel;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN.'/public/checksms', $data, array($this->loginCookie(cookie())));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }
    
    /**
     * 自营订单钱包退款充值回调至财务系统
     * @param [type] $data [description]
     */
    protected function setRefundCallback($datas)
    {
        $data['data'] = urlencode(json_encode($datas));
        $data['timestamp'] = time();
        $res = post_curl(FINANCE_DOMAIN. '/webapi/refundCallback?token='.service_token($data['data'], $data['timestamp']), $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 自营订单钱包退款充值回调至财务系统
     * @param [type] $data [description]
     */
    protected function setPayLogRefundAmount($datas)
    {
        $data['data'] = urlencode(json_encode($datas));
        $data['timestamp'] = time();
        $res = post_curl(API_DOMAIN. '/order/intraface/setpaylogrefundamount?token='.service_token($data['data'], $data['timestamp']), $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }
}