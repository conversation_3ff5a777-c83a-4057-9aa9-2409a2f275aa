<?php

namespace Home\Controller;

use Home\Controller\BaseController;
use Home\Services\UcenterService;

class PublicController extends BaseController
{
    /**
     *  F('brandList', $brandList);
     */
    public function getBrandList()
    {

        $BrandModel = D('Brand');
        $where['status'] = array('eq', 1);
        $brandName = I("brand_name");
        if ($brandName) {
            $where['brand_name'] = ["like", "%" . $brandName . "%"];
        }
        $brandList = $BrandModel->where($where)->field('brand_id,brand_name,brand_logo')->select();

        return $this->apiReturn(0, '成功', $brandList);
    }

    /**
     * [checkAccountExists description]
     * @return [type] [description]
     */
    public function checkAccountExists()
    {
        $account = I('request.account', '', 'trim');
        $type = I('request.type', '', 'trim');//1：注册，2：登录，3：忘记密码
        $intlCode = I('request.intl_code', '', 'trim'); // 国际手机代号
        $account = get_inte_mobile($account, $intlCode);
        if (!$account) {
            return $this->apiReturn(11001, '请输入手机号/邮箱');
        }
        if (!$type) {
            return $this->apiReturn(23038, '缺少参数');
        }
        $UserMainModel = D('UserMain');
        if ($type == '1') {
            $user_id = $UserMainModel->getAccountUserId($account);
            $info = $UserMainModel->getUserInfo($user_id);
            if ($info['is_type'] == 1) {//竞调用户 -> 进入注册阶段，将竞调账号转普通账号
                return $this->apiReturn(0, '成功');
            }
            // if ($user_id && $info['channel_type'] == 1) { // channel_type 1-线上用户，2-线下用户，线下用户注册时不用校验是否存在
            if ($user_id) {
                return $this->apiReturn(23037, '该账号已经存在');
            }
        } else if ($type == '2' || $type == '3') {
            $user_id = $UserMainModel->getAccountUserId($account);
            if (!$user_id) {
                //还要去个人中心判断是否存在账号
                $exists = UcenterService::checkUserExists($account);
                if (!$exists) {
                    return $this->apiReturn(11013, '该账号不存在');
                }
            }
        } else {
            return $this->apiReturn(23038, '请先设置参数type');
        }
        return $this->apiReturn(0, '成功');
    }

    /**
     * 邮箱激活校验 (仅用于内部调用)
     */
    public function checkVerifyEmail()
    {
        $token = I('token', '', 'trim');
        $userTokenInfo = S_token($token,"",["prefix"=>"api_"]);
        $type = $userTokenInfo['type'];
        $user_id = $userTokenInfo['user_id'];
        if (!$userTokenInfo['token']) {
            return $this->apiReturn(12017, '该token不存在，请重新获取', '');
        }
        if ($userTokenInfo['status'] == '1') {
            return $this->apiReturn(12015, '该token已使用，请重新获取', '');
        }
        if ($userTokenInfo['expire_time'] < time()) {
            return $this->apiReturn(12016, '该token已过期，请重新获取', '');
        }
        try {
            $saveData = array(
                'status' => '1',
                'mod_time' => $_SERVER['REQUEST_TIME'],
            );
            $res = D('UserToken')->where(array('token' => $token, 'type' => $type))->save($saveData);
        } catch (Exception $e) {
        }
        $userTokenInfo['status'] = $saveData['status'];
        $userTokenInfo['mod_time'] = $saveData['mod_time'];
        S_token($token, $userTokenInfo);
        return $this->apiReturn(0, '成功', $userTokenInfo);
    }

    /**
     * 发送短信验证码(图形验证码非必须)
     * @return [type] [description]
     */
    public function smsVerify()
    {
        $mobile = I('request.mobile', '');
        $verify = I('verify', '');
        $channel = I('channel', 0, 'intval');//0共用次数，1登录，2注册
        //该手机号需要登录还是注册
        $type = I('type');
        if (!$type) {
            $type = 7; // 默认是获取免密登录的验证码
        }
        $res = $this->checkSms($mobile);
        // if ($res['err_code'] == 23019) {
        if ($res['err_code'] == 10001) {
            if (empty($verify)) {
                $this->pushReportMonitorLog(
                    [
                        "interface_type" => "2",
                        "err_msg" => "请输入图形验证码",
                        "err_code" => "10001",
                        "user_name" => $mobile,

                    ]
                );
                return $this->apiReturn(10001, '请输入图形验证码');
            }
            $res = A('Home/Reg')->regGateway($verify);
            if ($res !== true) {
                $this->pushReportMonitorLog(
                    [
                        "interface_type" => "2",
                        "err_msg" => "图形验证码错误",
                        "err_code" => $res['err_code'],
                        "user_name" => $mobile,

                    ]
                );
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        } elseif ($res['err_code'] != 0) {
            $this->pushReportMonitorLog(
                [
                    "interface_type" => "2",
                    "err_msg" => $res['err_msg'],
                    "err_code" => $res['err_code'],
                    "user_name" => $mobile,

                ]
            );
            return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
        }

        //验证是否已达上限
        $intlCode = I('request.intl_code', '');
        $mobile = get_inte_mobile($mobile, $intlCode);
        $count = intval(S_sms($mobile, $channel));
        if ($count >= C('SMS_LIMIT_NUMBER')) {
            return $this->apiReturn(23018, '短信验证码已达到上限');
        }
        //发送
        $key = hash_key(5, 2);
        $res = $this->sendSms($mobile, $type, array('code' => $key, 'type' => 1), '1', $channel);
        if (!$res) {
            return $this->apiReturn(23015, '网络出现问题，请稍后再试');
        }
        
        if (APP_DEBUG) {
            // return $this->apiReturn(0, '发送成功:'.$key, $key);
            return $this->apiReturn(0, '发送成功', $key);
        }
        return $this->apiReturn(0, '发送成功');
    }

    /**
     * 发送短信验证码（图形验证码必须）
     * @return [type] [description]
     */
    public function findSmsVerify()
    {
        $mobile = I('request.mobile', '');
        $intlCode = I('request.intl_code', '');
        $channel = I('channel', 0, 'intval');
        $mobile = get_inte_mobile($mobile, $intlCode);
        $verify = I('verify', '');
        $type = I('type'); //短信验证码模板 1：注册，2：忘记密码
        if (empty($type)) {
            $type = 1;
        }
        if (empty($verify)) {
            return $this->apiReturn(10001, '请确认图形验证码');
        }
        $Verify = new \Think\VerifyApi();
        if (!$Verify->check($verify, 2)) {
            return $this->apiReturn(10002, '验证码错误，请重新获取');
        }

        // if (platform() == 2) {
        //     $res = $this->verifyCaptcha();
        //     if ($res['err_code'] != 0) {
        //         return $this->apiReturn(11008, '验证码错误，请重新获取');
        //     }
        // } else {
        // if (empty($verify)) {
        //     return $this->apiReturn(23019, '请确认图形验证码');
        // }
        // $Verify = new \Think\VerifyApi();
        // if (!$Verify->check($verify, 2)) {
        //     return $this->apiReturn(11008, '验证码错误，请重新获取');
        // }
        // }

        //验证是否已达上限
        $count = intval(S_sms($mobile, $channel));
        if ($count > C('SMS_LIMIT_NUMBER')) {
            return $this->apiReturn(23018, '短信验证码已达到上限');
        }
        //发送
        $key = hash_key(5, 2);
        $res = $this->sendSms($mobile, $type, array('code' => $key, 'type' => 2), '1', $channel);
        if (!$res) {
            return $this->apiReturn(23015, '网络出现问题，请稍后再试');
        }
        if (APP_DEBUG) {
            return $this->apiReturn(0, '发送成功', $key);
        }
        return $this->apiReturn(0, '发送成功');
    }

    /*
    * 获取gid 和 csrf
    */
    public function apiInit()
    {
        $gid = $this->addGid(1);
        return $this->apiReturn(0, "获取成功", [
            "Yo4teW_uid" => 0,
            "Yo4teW_gid" => $gid,
            "Yo4teW_csrf" => $this->addCsrf($gid)
        ]);
    }

    /**
     * 检查短信发送次数
     * @return [type] [description]
     */
    public function checkSms($mobile = '')
    {
        empty($mobile) && $mobile = I('request.mobile', '');
        $intlCode = I('request.intl_code', '');
        $channel = I('channel', 0, 'intval');
        $mobile = get_inte_mobile($mobile, $intlCode);
        if (!is_mobile($mobile)) {
            return $this->apiReturn(23005, '手机号码格式不正确');
        }

        $ip = getip();
        // 获取redis中的总次数 这个hash有效时间为86400秒内次数超过500,就返回需要填写验证码
        $msg_count = Sredis('spider_msg_count', '', C('REDIS_LIST.search'));

        //获取某个ip在一小时内发送短信的次数
        $msg_ip_count = Sredis('spider_msg_count_' . $ip, '', C('REDIS_LIST.search'));


        //一天内最多允许指定次数的无需验证码发短信
        $total_msg_num = !empty(C('TOTAL_MSG_NUM')) ? C('TOTAL_MSG_NUM') : 500;

        //一个小时内最多允许某个ip的无需验证码的发信次数
        $hour_msg_num = !empty(C('HOUR_MSG_NUM')) ? C('HOUR_MSG_NUM') : 10;


        if (($msg_count && $msg_count >= $total_msg_num) || ($msg_ip_count && $msg_ip_count >= $hour_msg_num)) {
            return $this->apiReturn(10001, '请确认图形验证码');
        }

        try {
            $user_id = S_account($mobile);
        } catch (\Exception $e) {
            $UserMainModel = D('Usermain');
            $user_id = $UserMainModel->isHasAccount($mobile);
        }
        if (empty($user_id)) {//存在用户的发短信
            //注册网关验证
            $res = A('Home/Reg')->regGateway(true);
            if ($res !== true) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
        //同个手机号验证次数
        $count = intval(S_sms($mobile, $channel));
        if ($count > C('VERIFY_NUM')) {
            return $this->apiReturn(10001, '请确认图形验证码');
        }
        return $this->apiReturn(0, '状态正常');
    }

    /**
     * 进口报关发送短信验证码(图形验证码非必须)
     * @return [type] [description]
     */
    public function customSmsVerify()
    {
        $mobile = I('request.mobile', '');
        $channel = 0;
        $count = intval(S_sms($mobile, $channel));

        $verify = I('verify', '');


//        $res = $this->verifyCaptcha();
//        if ($res['err_code'] != 0) {
//            return $this->apiReturn(11008, '验证码错误，请重新获取');
//        }
        $res = $this->checkSms($mobile);
        // if ($res['err_code'] == 23019) {
        if ($res['err_code'] == 10001) {
            if (empty($verify)) {
                return $this->apiReturn(10001, '请输入图形验证码');
            }
            $res = A('Home/Reg')->regGateway($verify);
            if ($res !== true) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        } elseif ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
        }


        //发送
        $key = hash_key(5, 2);
        $res = $this->sendSms($mobile, 8, array('code' => $key, 'type' => 1), '1', $channel);
        if (!$res) {
            return $this->apiReturn(23015, '网络出现问题，请稍后再试');
        }
        if (APP_DEBUG) {
            return $this->apiReturn(0, '发送成功', array('key' => $key, 'count' => $count));
        }
        return $this->apiReturn(0, '发送成功', array('count' => $count));
    }

    // 内部请求进口报关短信提示
    public function customSms()
    {
        $mobile = I('telphone', '');
        $login = I('login', '');
        $template = I('template', '');
        $type = I('type', '');
        $channel = 0;
        if (!empty($type)) { // 有值的话表明是供应商页面
            $res = $this->sendSms($mobile, 9, array('mobile' => $mobile, 'type' => 1), '1', $channel);
        } else {
            if ($login == 1) {
                $res = $this->sendSms($mobile, 3, array('mobile' => $mobile, 'type' => 1), '1', $channel);
            } else {
                $res = $this->sendSms($mobile, 4, array('mobile' => $mobile, 'type' => 1), '1', $channel);
            }
        }

        if (!$res) {
            return $this->apiReturn(23015, '网络出现问题，请稍后再试');
        }
        if (APP_DEBUG) {
            return $this->apiReturn(0, '发送成功', $key);
        }
        return $this->apiReturn(0, '发送成功');
    }


    //3.0获取短信验证码值
    public function getMsgCode()
    {
        $mobile = I("mobile", "");
        return $this->apiReturn(0, '发送成功', session_sms($mobile));
    }

    // 进口报关发送邮件通知
    public function customSendEmail()
    {
        $page_type = I('type', '');
        if ($page_type == 2) { // 说明是供应商页面，邮件通知人员需要另外读取配置信息
            $list = C('SUPPLIER_EMAIL_SEND');
            $type = '5';
        } else if ($page_type == 3) { // SMT贴片页面
            $list = C('STM_EMAIL_SEND');
            $type = '6';
        } else { // 供应链页面 SC
            $where['status'] = array('eq', 1);
            $list = D('ChainEmailSend')->where($where)->getField('ces_id, ces_email', true);
            $type = '4';
        }


        foreach ($list as $k => $v) {
            if ($page_type == 2) {
                $chainId = I('chain_id');
                $chain = M('Chain')->where(['chain_id' => $chainId])->find();
                $nature = [
                    7 => '原厂',
                    1 => '代理商',
                    2 => '现货商',
                    3 => '其它',
                ];

                $region = [
                    1 => '欧美',
                    2 => '大陆',
                    3 => '日韩',
                    4 => '港台',
                    5 => '其它',
                ];
                $mainProductType = $chain['main_product_type'];
                if (strpos($mainProductType, '其他')!==false) {
                    $mainProductType = str_replace('其他', '', $mainProductType);
                    $mainProductType = '其它 - ' . $mainProductType;
                }
                D('EmailSend')->send(
                    $k,
                    $type,
                    [
                        'email' => $v,
                        'com_name' => $chain['com_name'],
                        'link_name' => $chain['link_name'],
                        'company_nature' => array_get($nature, $chain['company_nature'], ''),
                        'region' => array_get($region, $chain['region'], ''),
                        'main_product_type' => $mainProductType,
                        'main_brand' => $chain['main_brand'],
                        'position' => $chain['position'],
                        'mobile' => $chain['mobile'],
                        //防止和上面的email冲突
                        'company_email' => $chain['email'],
                    ]
                );
            } else {
                D('EmailSend')->send(
                    $k,
                    $type,
                    [
                        'email' => $v,
                        'link_name' => I('link_name', ''),
                        'need' => I('need', ''),
                        'telphone' => I('telphone', ''),
                        'com_name' => I('com_name', ''),
                        'main_brand' => I('main_brand', ''),
                    ]
                );
            }

        }
    }

    // 进口报关发送短信通知
    public function customSendMsg()
    {
        try {
            $data = I("post.");
//            \Think\Log::write(print_r($data,true));
            $touers = C("GONGYINGLIAN_GUANGAO_PUSH_USER");
            $currentWeek = date("w", time());
            $touer = isset($touers[$currentWeek]) ? $touers[$currentWeek] : $touers[0];
            $json_data = [];
            $json_data['link_name'] = isset($data['link_name']) ? $data['link_name'] : '';
            $json_data['telphone'] = isset($data['telphone']) ? $data['telphone'] : '';
            $check['pf'] = platform();
            $check['template_id'] = I('template_id');
            $check['keyword'] = "gongyinglian-guanggao-push";
            $check['touser'] = json_encode([$touer]);
            $check['data'] = json_encode($json_data, JSON_UNESCAPED_UNICODE);
            $check['channel_type'] = 2; // 邮箱
            $check['is_ignore'] = true;
            $check = array_merge($check, authkey());
            $res = post_curl(API_DOMAIN . '/msg/sendMessageByAuto', $check);
            if (!empty($res)) {
                $res = json_decode($res, true);
            }
        } catch (\Exception $e) {

        }
    }

    /**
     * 点击客服行为记录
     * @return [type] [description]
     */
    public function customsrService()
    {
        $res = $this->behavior_push(8, 0);
        return $this->apiReturn();
        // echo intval($res);
    }

    /**
     * 立即结算行为记录
     * @return [type] [description]
     */
    public function confirmBehavior()
    {
        $res = $this->behavior_push(6, 0);
        return $this->apiReturn();
        // echo intval($res);
    }

    /**
     * 抽奖行为记录
     * @return [type] [description]
     */
    public function lotteryBehavior()
    {
        $error = I('error', 0, 'intval');
        $param = I('param', '');
        $res = $this->behavior_push(10, $error, $param);
        return $this->apiReturn();
        // echo intval($res);
    }

    /**
     * 生成二维码图片（注意只对内部使用，请勿公布外网，临时提供给活动使用）
     * @return [type] [description]
     */
    public function qrcode()
    {
        $data = I('data', '', 'trim');
        if (/*!$this->auth() || */ empty($data)) {
            exit();
        }
        Vendor('phpQrCode.phpqrcode');
        \QRcode::png($data, false, QR_ECLEVEL_L, 6, 2);
    }

    /**
     * 钉钉机器人通知
     * @return [type] [description]
     */
    public function dingtalkRobot()
    {
        $access_token = I('get.access_token');
        $res = post_curl('https://oapi.dingtalk.com/robot/send?access_token=' . $access_token, $GLOBALS['HTTP_RAW_POST_DATA'], array('Content-Type: application/json', 'Content-Length: ' . strlen($GLOBALS['HTTP_RAW_POST_DATA'])));
        echo $res;
    }


    /**
     * 显示跳转HTML
     * @return [type] [description]
     */
    public function redirectHtml()
    {
        $code = I('request.code', '', 'trim');
        echo base64_decode($code);
    }

    /**
     * TX验证码校验
     * @param string $ticket [description]
     * @param string $randstr [description]
     * @return [type]          [description]
     */
    public function verifyCaptcha($ticket = '', $randstr = '')
    {
        $data = array(
            'aid' => C('TX_CAPTCHA.appid'),
            'AppSecretKey' => C('TX_CAPTCHA.secretkey'),
            'Ticket' => I('ticket', $ticket),
            'Randstr' => I('randstr', $randstr),
            'UserIP' => get_client_ip(0, true),
        );
        foreach ($data as $k => $v) {
            $str .= $k . '=' . $v . '&';
        }
        $json = get_curl('https://ssl.captcha.qq.com/ticket/verify', rtrim($str, '&'));
        $arr = json_decode($json, true);

        if (empty($json) || $arr['response'] != 1) {
            return $this->apiReturn(-1, '验证不通过，请重试');
        }
        if ($arr['evil_level'] > 40) {
            $path = RUNTIME_PATH . 'Captcha/';
            $name = $path . date('Ymd') . '.log';
            @mkdir($path);
            $content = array(
                'time' => date('Y-m-d H:i:s'),
                'ip' => $data['UserIP'],
                'evil_level' => $arr['evil_level'],
                'agent' => $_SERVER['HTTP_USER_AGENT'],
            );
            file_put_contents($name, json_encode($content) . "\r", FILE_APPEND);
        }
        return $this->apiReturn(0, '验证通过');
    }

    // 校验注册邀请码
    public function verifyInviteCode()
    {
        $invite_code = I('invite_code', '');

        if (!$invite_code) {
            return $this->apiReturn(-1, '参数缺失');
        }

        $invite_code = strtolower($invite_code);

        if (strlen($invite_code) != 8) {
            return $this->apiReturn(-2, '输入错误，邀请码为八位字符');
        }

        $sale_id = $this->decodeInviteCode($invite_code);

        if (!$sale_id) {
            return $this->apiReturn(-3, '未解析到业务员ID');
        }

        return $this->apiReturn(0, '成功', $sale_id);
    }

    // 解析注册邀请码
    public function decodeInviteCode($invite_code)
    {
        $sale_id = "";
        for ($i = 4; $i < strlen($invite_code); $i++) {
            $sale_id .= chr(ord($invite_code[$i]) - 49);
        }

        $sale_id = intval($sale_id);

        $CmsModel = D('Order/Cms');
        $sale_info = $CmsModel->getUserInfo($sale_id);

        if (empty($sale_info)) {
            return false;
        }

        return $sale_id;
    }


}
