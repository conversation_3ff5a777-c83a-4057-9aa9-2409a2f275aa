<?php
namespace Home\Controller;

use Home\Controller\BaseController;

class IntrafaceController extends BaseController
{
    /**
     * 重写初始化，使用接口服务加密方式非api内部加密方式
     * @return [type] [description]
     */
    public function _initialize()
    {
        $token = I('get.token', '', 'trim');
        if (CONTROLLER_NAME == 'Intraface') {
            $res = service_auth(I('request.'), $token);
            if ($res !== true) {
                return $this->apiReturn(-1, $res);
            }
        }
        $data = I('post.data', '', 'trim');
        $this->data = json_decode(urldecode($data), true);
    }

    /**
     * 获取提现申请需同步列表
     * @return [type] [description]
     */
    public function getWithdrawSyn()
    {
        $UserWalletModel = D('UserWallet');
        $map = array(
            'wallet_type' => 2,
            'status' => 1,
            'cpcn_syn' => 1,
        );
        $data = $UserWalletModel->getList($map, 'wallet_id');
        return $this->apiReturn(0, '成功', $data);
    }

    /**
     * 获取需要校验的用户
     * @return [type] [description]
     */
    public function getVerifyUser()
    {
        $UserInfoModel = D('UserInfo');
        $UserWalletLogModel = D('UserWalletLog');
        $type = $this->data['type'];
        $p = $this->data['p'];
        switch ($type) {
            case 'day'://日
                $map['create_time'] = array('egt', time() - 86400);
                $data = $UserWalletLogModel->getGroupUser($map, $p.',10000');
                break;
            case 'month'://月
                $map['create_time'] = array('egt', time() - 2592000);
                $data = $UserWalletLogModel->getGroupUser($map, $p.',10000');
                break;
            default://有效钱包
                $data = $UserInfoModel->getWalletUser($p.',10000');
                break;
        }
        return $this->apiReturn(0, '成功', $data);
    }

    /**
     * 校验余额
     * @return [type] [description]
     */
    public function verifyBalance()
    {
        $UserInfoModel = D('UserInfo');
        $UserWalletLogModel = D('UserWalletLog');
        $user_id = $this->data['user_id'];
        if ($user_id = 137281) {//数据错误，暂时不校验该用户
            return $this->apiReturn(0, '校准成功');
        }
        $wallet = S_wallet($user_id);
        if (empty($wallet)) {
            return $this->apiReturn(180021, '无效用户');
        }
        $useable_balance = $this->getBalance($user_id);//可用余额
        if ($useable_balance === false) {
            return $this->apiReturn(180021, '中金接口请求失败');
        }
        $wallet_useable_balance = $wallet['wallet_balance'] - $wallet['wallet_freeze'];
        if (bccomp($useable_balance, $wallet_useable_balance, 2) == 0) {//可用余额对比
            return $this->apiReturn(0, '校准成功');
        }
        $UserInfoModel->startTrans();
        //日志统计
        $log_balance = $UserWalletLogModel->getUserBalance($user_id, 'WALLET_RECHARGE_BALANCE');//日志充值性金额
        $log_freeze_balance = $UserWalletLogModel->getUserFreezeBalance($user_id, 'WALLET_FREEZE_BALANCE');//日志充值性冻结额
        $log_useable_balance = $log_balance - $log_freeze_balance;//日志可用额
        $redis_balance = $wallet['wallet_balance'];
        $wallet['wallet_balance'] = $useable_balance + $log_freeze_balance;
        if ($useable_balance == $log_useable_balance) {//日志与中金一致
            $msg = '无损';

        } else {
            //有损-》日志不完整
            $msg = '有损';

            $surplus = $useable_balance - $log_useable_balance;
            $data = array(
                'log_type' => $surplus > 0 ? 98 : 99,
                'user_id' => $user_id,
                'amount' => $surplus,
                'user_balance' => $useable_balance,
                'create_time' => $_SERVER['REQUEST_TIME'],
            );
            $res = $UserWalletLogModel->add($data);
            if ($res === false) {
                $UserInfoModel->rollback();
                return $this->apiReturn(180023, '有损日志添加失败');
            }

            if ($surplus != 0) {
                //统计收入金额
                $wallet_earning = $UserWalletLogModel->getUserBalance($user_id, array('log_type' => array('in', C('WALLET_LOG_EARNING'))));
                $wallet['wallet_earning'] = $wallet_earning;

                //统计支出金额
                $wallet_expend = $UserWalletLogModel->getUserBalance($user_id, array('log_type' => array('in', C('WALLET_LOG_EXPEND'))));
                $wallet['wallet_expend'] = $wallet_expend;
            }
        }

        //用户余额
        $res = $UserInfoModel->saveUserInfo($user_id, array('wallet_balance' => $wallet['wallet_balance']));
        if ($res === false) {
            $UserInfoModel->rollback();
            return $this->apiReturn(180024, '修复用户余额失败');
        }

        //redis
        try {
            S_wallet($user_id, $wallet);
        } catch (\Exception $e) {
            $UserInfoModel->rollback();
            return $this->apiReturn(180025, '修复redis失败');
        }

        $UserInfoModel->commit();
        
        $msg_notice['data'] = array(
            'user_id' => $user_id,
            'msg' => sprintf('%s修复: 中金可用余额:%s => 日志可用余额:%s => redis余额:%s', $msg, $useable_balance, $log_useable_balance, $redis_balance)
        );
        sendMsg('user_balance_verify', $msg_notice, 'INNER_PERSON', true);
        return $this->apiReturn(0, $msg.'修复成功');
    }

    /**
     * 发放非充值金额
     * @return [type] [description]
     */
    public function issueWallet()
    {
        global $_POST;
        $_POST['user_id'] = $this->data['user_id'];
        $_POST['amount'] = $this->data['amount'];
        $_POST['relevance_sn'] = $this->data['relevance_sn'];
        $_POST['remark'] = $this->data['remark'];
        $res = A('Home/Wallet')->issue();
        return $this->apiReturn($res['err_code'], $res['err_msg'], $res['data']);
    }
}
