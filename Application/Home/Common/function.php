<?php
/**
 * Created by PhpStorm.
 * User: leo
 * Date: 2018/11/21
 * Time: 13:56
 */

/** 是否为 周一 ~ 周五 的 9点 ~ 21点
 * @return bool
 */
function isWorkTime(){
    $week = date('w');
    $hour = date('G');
    if (in_array($week,array(0,6)) || $hour<9 || $hour > 21) {
        return false;
    } else {
        return true;
    }
}

/** 获取一定范围内的多个随机数字
 * @param int $begin
 * @param int $end
 * @param int $limit
 * @return array
 */
function getRandNumbers($begin = 0, $end = 100, $limit = 0){
    $rand_array = range($begin, $end);
    shuffle($rand_array); //打乱顺序
    return array_slice($rand_array, 0, $limit); //截取前几个
}
