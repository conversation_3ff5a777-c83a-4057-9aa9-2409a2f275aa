<?php

namespace Home\Services;

use Home\Model\CubeMinOrderRuleModel;
use Home\Model\InquiryItemModel;
use Order\Model\SupplierModel;

class InquiryService
{

    /**
     * 询价明细
     * Summary of inquiry
     * @param mixed $inquiryItemIdArr
     * @return array<array>
     */
    public  function inquiry($inquiryItemIdArr = [])
    {
        $webInquiryConfirmedQuoteList = (new FrqService())->getQuoteByInquiryItemIds($inquiryItemIdArr);
        $webInquiryConfirmedQuoteMap = \collect($webInquiryConfirmedQuoteList)->keyBy('source_item_id')->toArray();
        $list = [];
        $inquiryItemList = D('Home/InquiryItem')->where(['id' => ['in', $inquiryItemIdArr]])->select();
        $inquiryItemListMap = \collect($inquiryItemList)->keyBy('id')->toArray();
        $redis = \fs_redis_init();
        foreach ($inquiryItemIdArr as $key => $inquiryItemId) {
            $frqItem = !empty($webInquiryConfirmedQuoteMap[$inquiryItemId]) ? $webInquiryConfirmedQuoteMap[$inquiryItemId] : [];
            $inquiryItem = !empty($inquiryItemListMap[$inquiryItemId]) ? $inquiryItemListMap[$inquiryItemId] : [];
            if (empty($frqItem)) {
                continue;
            }
            $listItem = [];
            $listItem["inquiry_item_id"] = $inquiryItemId; //询价明细ID
            $listItem["goods_name"] = array_get($frqItem, 'goods_name');
            $listItem["goods_number"] = array_get($frqItem, 'quote_number');
            $listItem["goods_price"] = array_get($frqItem, 'quote_price');
            $listItem["brand_id"] = array_get($frqItem, 'brand_id');
            $listItem["brand_name"] = array_get($frqItem, 'brand_name');
            $listItem["standard_brand_id"] = array_get($frqItem, 'standard_brand_id');
            $listItem["standard_brand_name"] = array_get($frqItem, 'standard_brand_name');
            $listItem["supplier_id"] = array_get($frqItem, 'supplier_id');
            $listItem["supplier_name"] = array_get($frqItem, 'supplier_name');
            $listItem["delivery_place"] = array_get($inquiryItem, 'delivery_place', 1); //交货地 1 大陆 2 香港;
            $listItem["currency"] = $listItem['delivery_place'] == 1 ? 1 : 2;
            $listItem["batch"] = array_get($frqItem, 'batch');
            $listItem["delivery_time"] = array_get($frqItem, 'delivery_time');
            //渠道
            $listItem['canal'] = '';
            if (!empty($listItem['goods_id'])) {
                $sku = $redis->hget('sku', $listItem['goods_id']);
                $sku = \json_decode($sku);
                $listItem['canal'] = $sku['canal'];
            }
            $inquiryData = FrqService::getInquiryDataByQuoteId($frqItem['quote_id']);
            $listItem['inquiry_id'] = $inquiryData['inquiry_id'];
            $listItem['inquiry_item_id'] = $inquiryData['inquiry_item_id'];
            $listItem['inquiry_sn'] = $inquiryData['inquiry_sn'];
            $listItem['quote_id'] = $inquiryData['quote_id'];
            $listItem['quote_sn'] = $inquiryData['quote_sn'];
            $listItem['buyer_id'] = $inquiryData['buyer_id'];
            $listItem['goods_id'] = $inquiryData['goods_id'];
            $list[] = $listItem;
        }
        return $list;
    }

    public function delInquiryItem($inquiryItemIdArr = [])
    {
        $inquiryItemModel = D('Home/InquiryItem');
        $inquiryItemIdArr = implode(',', $inquiryItemIdArr);
        $inquiryItemModel->where(['id'=> ['in', $inquiryItemIdArr]])->save([
            'has_order' => 1,
            'update_time' => time()
        ]);
    }


    //将数据同步到询报价系统
    public function syncInquiryToFrq($inquiryId)
    {
        $inquiryItemModel = D('Home/InquiryItem');
        $inquiry = D('Home/Inquiry')->where(['id' => $inquiryId])->find();
        $inquiryItems = $inquiryItemModel->where(['inquiry_id' => $inquiryId])->select();
        if (empty($inquiryItems)) {
            return;
        }
        $inquiry = D('Home/Inquiry')->where(['id' => $inquiryId])->find();
        $data = [];
        $data['web_inquiry_sn'] = $inquiry['sn'];
        $data['source_id'] = 1;
        $user = D('Home/UserMain')->where(['user_id' => $inquiry['user_id']])->find();
        $data['customer_account'] = !empty($user['mobile'])?$user['mobile']:$user['email'];
        $data['customer_name'] = $user['user_name'] ?: 'none';
        $data['user_id'] = $inquiry['user_id'];
        $data['delivery_place'] = $inquiryItems[0]['delivery_place'];
        $data['currency'] = $inquiryItems[0]['delivery_place'];

        $goodsIdList = array_column($inquiryItems, 'goods_id');
        if (!empty($goodsIdList)) {
            $goodsInfoList = self::getSkuListFromSkuServer($goodsIdList);
        }
        foreach ($inquiryItems as $inquiryItem) {
            $item = [];
            $item['source_item_id'] = $inquiryItem['id'];
            $item['goods_name'] = $inquiryItem['goods_name'];
            $item['brand_name'] = $inquiryItem['brand_name'];
            $item['inquiry_number'] = $inquiryItem['goods_number'];
            $item['expect_price'] = $inquiryItem['target_price'];
            $item['delivery_time'] = $inquiryItem['delivery_time'];
            $item['batch'] = $inquiryItem['batch'];
            $item['special_request'] = $inquiryItem['special_request']?:'无';
            $item['sku_id'] = $inquiryItem['goods_id'];
            $item['web_show_price'] = 0;
            if (!empty($item['sku_id'])) {
                $sku = array_get($goodsInfoList, $item['sku_id']);
                if (!empty($sku)) {
                    $goodsNumber = $inquiryItem['goods_number'];
                    //判断阶梯价是否为空,不为空的话根据数量去获取对应的价格
                    if (!empty($sku['ladder_price'])) {
                        usort($sku['ladder_price'], function ($a, $b) {
                            return $b['purchases'] - $a['purchases'];
                        });
                        foreach ($sku['ladder_price'] as $ladder) {
                            if ($goodsNumber >= $ladder['purchases']) {
                                $item['web_show_price'] = $inquiryItem['delivery_place'] == 1 ? $ladder['price_cn'] : $ladder['price_us'];
                                break;
                            }
                        }
                    }
                }
            }
            $data['web_inquiry_items'][] = $item;
        }
        $result = post_curl(FRQ_DOMAIN . '/inner/webInquiry/addWebInquiry', $data);
        $result = json_decode($result, true);
        if ($result['code'] != 0) {
            return $result['msg'];
        }
        return true;
    }

    public static function getSkuListFromSkuServer($goodsIds)
    {
        if (empty($goodsIds)) {
            return [];
        }
        $result = post_curl(NEW_GOODS_DOMAIN . '/synchronization', ['goods_id' => implode(',', $goodsIds)]);
        $result = \GuzzleHttp\json_decode($result, true);
        $skuList = [];
        if (isset($result['errcode']) && $result['errcode'] == 0) {
            $skuList = $result['data'];
        }
        return $skuList;
    }


    /**
     *
     * 判断渠道是否可以下单
     * @param mixed $inquiryId
     * @return void
     */
    public function checkCanalIsAddOrder($requestParams = [])
    {
        if (!is_array($requestParams) || empty($requestParams)) {
            return true;
        }

        $minOrderRuleModel = new CubeMinOrderRuleModel();
        foreach ($requestParams as $item) {
            if (!isset($item['supplier_id']) || !isset($item['currency']) || !isset($item['total_amount'])) {
                throw new \Exception('缺少校验参数');
            }
            $supplierId = (int)$item['supplier_id'];
            $supplierCode = $item['canal'];
            if ($supplierId == 17 && empty($supplierCode)) {
                throw new \Exception('专营供渠道的商品缺少供应商编码');
            }
            $supplierName = D('Order/Supplier')->where(['supplier_id' => $supplierId])->getField('supplier_name');
            $currency = (int)$item['currency'];
            $totalAmount = (float)$item['total_amount'];

            $where = ['supplier_id' => $supplierId, 'status' => 1];
            if ($supplierId == 17) {
                $where['supplier_code'] = $supplierCode;
            }
            $rule = $minOrderRuleModel->where($where)->find();

            if ($rule) {
                $minRequiredAmount = 0.0;

                if ($currency == 1) {
                    $minRequiredAmount = (float)$rule['cny_amount'];
                } elseif ($currency == 2) {
                    $minRequiredAmount = (float)$rule['usd_amount'];
                }
                if ($minRequiredAmount > 0 && $totalAmount < $minRequiredAmount) {
                    if ($supplierId != 17) {
                        throw new \Exception('渠道' . $supplierName . '下单金额不能小于' . $minRequiredAmount);
                    }else{
                        throw new \Exception('专营供渠道' . $supplierCode . '下单金额不能小于' . $minRequiredAmount);
                    }
                }
            }
        }
        return true;
    }

    public function checkIsHasSameOrder($inquiryItemIdArr = []){
        if (empty($inquiryItemIdArr)) {
            return true;
        }
        $inquiryItemModel = D('Home/InquiryItem');
        $list =  $inquiryItemModel->where(['id'=> ['in', $inquiryItemIdArr]])->select();
        if (!empty($list)) {
            foreach ($list as $item) {
                if ($item['has_order'] == 1) {
                    // throw new \Exception('询价单型号:'. $item['goods_name']. '已下单，不能重复下单');
                }
            }
        }
    }
}
