<?php

namespace Home\Services;

use Home\Model\FrqWebInquiryItemsModel;

//询报价系统那边的服务操作
class FrqService
{
    //获取报价信息
    public static function getQuoteByInquiryItemIds($inquiryItemIds = [])
    {
        $inquiryItemIds = implode(',', $inquiryItemIds);
        //先去获取询价详情
        $webInquiryItemList = (new FrqWebInquiryItemsModel())->where(['source_item_id' => ['in', $inquiryItemIds]])->select();
        if (empty($webInquiryItemList)) {
            return [];
        }
        $sourceItemIdMap = \collect($webInquiryItemList)->keyBy('id')->toArray();
        $webInquiryItemIds = array_column($webInquiryItemList, 'id');
        $webInquiryItemIds = implode(',', $webInquiryItemIds);
        $webInquiryConfirmedQuoteList = D('Home/FrqWebInquiryConfirmedQuote')->where(['web_inquiry_items_id' => ['in', $webInquiryItemIds]])->select();
        $quoteIds = array_column($webInquiryConfirmedQuoteList, 'quote_id');
        $quoteIds = implode(',', $quoteIds);
        $quoteList = D('Home/FrqQuote')->where(['id' => ['in', $quoteIds]])->select();
        $quoteMap = \collect($quoteList)->keyBy('id')->toArray();
        foreach ($webInquiryConfirmedQuoteList as $key => &$webInquiryConfirmedQuote) {
            $webInquiryConfirmedQuote['quote'] = !empty($quoteMap[$webInquiryConfirmedQuote['quote_id']]) ? $quoteMap[$webInquiryConfirmedQuote['quote_id']] : [];
            $webInquiryConfirmedQuote['source_item_id'] = $sourceItemIdMap[$webInquiryConfirmedQuote['web_inquiry_items_id']]['source_item_id'];
        }
        unset($webInquiryConfirmedQuote);
        return $webInquiryConfirmedQuoteList;
    }

    //根据报价id获取询价信息
    public static function getInquiryDataByQuoteId($quoteId)
    {
        $quote = D('Home/FrqQuote')->where(['id' => $quoteId])->find();
        return [
            'quote' => $quote,
            'inquiry_item_id' => $quote['inquiry_items_id'],
            'inquiry_id' => $quote['inquiry_id'],
            'goods_id' => $quote['goods_id'],
            'inquiry_sn' => $quote['inquiry_sn'],
            'quote_id' => $quote['id'],
            'quote_sn' => $quote['quote_sn'],
            'buyer_id' => $quote['create_uid'],
        ];
    }
}
