<?php

namespace Home\Services;


use Think\Log;

class UcenterService
{

    public static function ucenterRegister($params = [])
    {
        $data = [];
        $data["mobile"] = $params["mobile"] ?: "";
        $data["email"] = !empty($params["email"]) ? $params["email"] : "";
        $data["password"] = !empty($params["password"]) ? $params["password"] : "";
        $data["salt"] = !empty($params["salt"]) ? $params["salt"] : "";
        $data["init_org_id"] = 1; //初始组织id,1是猎芯,2是华云
        if (empty($data["mobile"]) && empty($data["email"])) {
            return [
                "code" => 1,
                "data" => [],
                "msg" => "注册用户手机号或者邮箱不能为空",
            ];
        }
        $res = post_curl(trim(C("UCENTER_URL"), "/") . "/api/auth/register", $data);
        $res = json_decode($res, true);
        return $res;
    }

    public static function changePassword($ucId, $password)
    {
        $data = [];
        $data["uc_id"] = $ucId;
        $data["password"] = $password;
        if (empty($data["uc_id"]) || empty($data["password"])) {
            return [
                "code" => 1,
                "data" => [],
                "msg" => "修改密码失败",
            ];
        }

        $res = post_curl(trim(C("UCENTER_URL"), "/") . "/api/auth/changePassword", $data);
        $res = json_decode($res, true);
        return $res;
    }

    public static function ucenterBindEmail($ucId, $email)
    {
        $data = [];
        $data["uc_id"] = $ucId;
        $data["email"] = $email;
        if (empty($data["uc_id"]) || empty($data["email"])) {
            return [
                "code" => 1,
                "data" => [],
                "msg" => "绑定邮箱失败",
            ];
        }

        $res = post_curl(trim(C("UCENTER_URL"), "/") . "/api/user/bindEmail", $data);
        $res = json_decode($res, true);
        return $res;
    }

    public static function ucenterBindMobile($ucId, $mobile)
    {
        $data = [];
        $data["uc_id"] = $ucId;
        $data["mobile"] = $mobile;
        if (empty($data["uc_id"]) || empty($data["mobile"])) {
            return [
                "code" => 1,
                "data" => [],
                "msg" => "绑定手机号失败",
            ];
        }

        $res = post_curl(trim(C("UCENTER_URL"), "/") . "/api/user/bindMobile", $data);
        $res = json_decode($res, true);
        return $res;
    }

    public static function ucenterBindOrgUserId($ucId, $userId)
    {
        $data = [];
        $data["uc_id"] = $ucId;
        $data["org_user_id"] = $userId;
        $data["org_id"] = 1;
        $res = post_curl(trim(C("UCENTER_URL"), "/") . "/api/user/bindOrgUserId", $data);
        $res = json_decode($res, true);
        return $res;
    }

    public static function getUcenterLoginToken($account, $password)
    {
        //个人中心登录,有返回用户信息的话,就直接写入猎芯
        $data = [
            'mobile' => is_email($account) ? '' : $account,
            'email' => is_email($account) ? $account : '',
            'password' => $password,
            'init_org_id' => 1,
        ];
        $res = post_curl(trim(C("UCENTER_URL"), "/") . "/api/auth/login", $data);
        $res = json_decode($res, true);
        $token = '';
        //个人中心登录成功的话,代表存在这个账号,就要往猎芯里面写
        if ($res['code'] == 0) {
            $token = $res['data']['token'];
        }
        return $token;
    }

    //个人中心登录
    public static function ucenterLogin($account, $password)
    {
        //如果是手机验证码登录的话,猎芯的老代码会去自动判断的,如果存在,就直接走验证登录,不存在的话,就会去个人中心请求了,不需要这边操作
        if (empty($password)) {

        } else {
            //个人中心登录,有返回用户信息的话,就直接写入猎芯
            $data = [
                'mobile' => is_email($account) ? '' : $account,
                'email' => is_email($account) ? $account : '',
                'password' => $password,
                'init_org_id' => 1,
            ];
            $res = post_curl(trim(C("UCENTER_URL"), "/") . "/api/auth/login", $data);
            $res = json_decode($res, true);
            $res = $res['data'];
            $userId = 0;
            //个人中心登录成功的话,代表存在这个账号,就要往猎芯里面写
            if ($res['code'] == 0) {
                $userId = self::addUserMain($res['uc_id'], [
                    'password' => $res['user']['password'],
                    'salt' => $res['user']['salt'],
                    'mobile' => $res['user']['mobile'],
                    'email' => $res['user']['email'],
                    'intl_code' => $res['user']['intl_code'],
                ]);
            }
            return $userId;
        }
    }

    //个人中心绑定微信
    public static function ucenterBindWechat($userId, $openId, $unionId)
    {
        $ucId = D('UserMain')->where(['user_id' => $userId])->getField('uc_id');
        $data = [
            'open_id' => $openId,
            'union_id' => $unionId,
            'org_user_id' => $userId,
            'uc_id' => $ucId,
            'org_id' => 3,
        ];
        $res = post_curl(trim(C("UCENTER_URL"), "/") . "/api/user/bindWechat", $data);
        $res = json_decode($res, true);
        if ($res['code'] != 0) {
            Log::write("个人中心绑定微信失败,绑定失败的参数为:" . json_encode($data), Log::ERR);
        }

    }

    //广播登录接收接口
    public static function autoLogin($token)
    {
        $data['token'] = $token;
        $res = post_curl(trim(C("UCENTER_URL"), "/") . "/api/auth/loginByToken", $data);
        $res = json_decode($res, true);
        if (!empty($res['code']) && $res['code'] != 0) {
            return $res['msg'];
        }

        if (empty($res)) {
            return false;
        }
        $res = $res['data'];
        //上面可以获取个人中心的信息
        //判断是否存在,不存在的话,要自动注册
        $userModel = D('UserMain');
        $userExists = false;
        if (!empty($res['user']['mobile'])) {
            $userId = $userModel->where(['mobile' => $res['user']['mobile']])->getField('user_id');
            $userExists = (bool)$userId;
        }

        if (!empty($res['user']['email']) && !$userExists) {
            $userId = $userModel->where(['email' => $res['user']['email']])->getField('user_id');
            $userExists = (bool)$userId;
        }
        if (!$userExists) {
            $userId = self::addUserMain($res['uc_id'], [
                'password' => $res['user']['password'],
                'salt' => $res['user']['salt'],
                'mobile' => $res['user']['mobile'],
                'email' => $res['user']['email'],
                'intl_code' => $res['user']['intl_code'],
            ]);
        }
        return $userId;
    }

    //新增用户操作
    public static function addUserMain($ucId, $user)
    {
        $userData = [
            'password' => $user['password'],
            'mobile' => $user['mobile'],
            'email' => $user['email'],
            'salt' => $user['salt'],
            'intl_code' => $user['intl_code'],
            'reg_ip' => get_client_ip(0, true),
            'reg_entrance' => 'ucenter',//注册入口(来源url)
            'create_time' => $_SERVER['REQUEST_TIME'],
            'fake_create_time' => $_SERVER['REQUEST_TIME'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '--',
            'channel_type' => 1,//1 线上用户 ，2线下用户
            'status' => 1,
            'uc_id' => $ucId,
            'create_device' => 31,
            'device' => 0,
        ];
        $userId = D('UserMain')->data($userData)->add();
        //去绑定user_id到用户中心
        UcenterService::ucenterBindOrgUserId($ucId, $userId);
        $UserMainModel = D('Home/UserMain');
        $UserMainModel->pushUserToCrm($userId, $ucId);
        return $userId;
    }

    //判断用户是否存在
    public static function checkUserExists($mobile)
    {
        $data['mobile'] = $mobile;
        $res = post_curl(trim(C("UCENTER_URL"), "/") . "/api/user/checkExist", $data);
        $res = json_decode($res, true);
        if (isset($res['code']) && $res['code'] == 0) {
            return false;
        }
        return true;
    }
}
