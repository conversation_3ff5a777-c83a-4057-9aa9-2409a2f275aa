<?php
namespace Home\Model;

use Think\Model;

class UserActionLogModel extends Model 
{
	/**
    * 操作记录
    * @param  [Integer] $user_id       [用户ID]
    * @param  [Integer] $operator_id   [操作人]
    * @param  [Integer] $operator_type [操作人类型：1.网站用户，2.CMS用户, 3-系统定时任务，4-ERP，5-WMS，6-中金]
    * @param  [string]  $event         [操作事件]
    * @return [type]           [description]
    */
    public function addLog($user_id, $operator_id, $operator_type = 1, $event='')
    {
        $log['user_id']       = $user_id;
        $log['operator_id']   = $operator_id;
        $log['operator_type'] = $operator_type; 
        $log['event']         = $event;
        $log['ip']            = get_client_ip(0, true);
        $log['create_time']   = time();

        $actionLog = $this->add($log);

        if ($actionLog === false) return false;

        return $actionLog;
    }
}