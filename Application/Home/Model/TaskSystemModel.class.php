<?php
namespace Home\Model;

use Think\Model;

class TaskSystemModel extends Model {

    public function addData($data)
    {

        $res = $this->where($map)->add($data);
        return $res;
    }

    /**
     * 根据表名和相应条件查询相关表数据
     * @param  [type] $table [description]
     * @param  [type] $where [description]
     * @param  string $limit [description]
     * @param  string $group [description]
     * @return [type]        [description]
     */
    public function getData($where, $field = '*', $limit = '',$order='',$groupby='')
    {
        $datas = $this->where($where)->field($field)->limit($limit)->order($order)->group($groupby)->select();
        return $datas;
    }


    /*
        返回用户今天是否有过搜索行为
        @param  $user_id int  用户id
    */
    public function today_search($user_id){
        
        $where['user_id']     = $user_id;
        $where['update_time'] = array('gt',strtotime(date('Y-m-d',time())));
        $where['task_type']   =2;
        $search_num           = M('task_system')->where($where)->count();

        if($search_num > 0){
            return true;
        }else{
            return false;
        }

    }


    
}