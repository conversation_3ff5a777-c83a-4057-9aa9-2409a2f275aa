<?php
namespace Home\Model;

use Think\Model;

class LdbModel extends Model
{
    protected $autoCheckFields = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.CMS');
    }

    /**
     * 获取猎单宝配置信息
     * @param  [type] $supplier_id [description]
     * @return [type]              [description]
     */
    public function getLdbConfig()
    {
        $collert_post=I('post.');
        $collert_get=I('get.');
        $order='create_time desc';
        $collert=array_merge($collert_post,$collert_get);
        foreach ($collert as $k=>$v){
            if(empty($v)){
                continue;
            }
            if($k=='create_time'){
                $order='create_time '.$v;
            }
            if($k=='end_time'){
                $order='end_time '.$v;
            }
            if($k=='num'){
                $order='num '.$v;
            }
            if($k=='goods_name'){
                $map['goods_name']=array('like','%'.trim($v).'%');
            }
            if($k=='release'){
                $time=time();
                if($v=='today'){
                    $map['create_time']=array('gt',date('Y-m-d',$time));
                }
                if($v=='three_day'){
                    $map['create_time']=array('gt',date('Y-m-d',$time-(3600*24*3)));
                }
                if($v=='seven_day'){
                    $map['create_time']=array('gt',date('Y-m-d',$time-(3600*24*7)));
                }
                if($v=='expire_1'){
                    $map['end_time']=array('lt',date('Y-m-d H:i:s',$time+(3600*24*1)));
                }
                if($v=='expire_3'){
                    $map['end_time']=array('lt',date('Y-m-d H:i:s',$time+(3600*24*3)));
                }

            }
        }
        $map['end_time']=array('gt',date('Y-m-d H:i:s',time()));

        $count =$this->table('lie_ldb')->where($map)->order($order)->count();
        //分页
        $pagesize = 10;
        $pagecur = !empty($collert['p']) ? $collert['p']:1;
        $first = $pagesize*($pagecur-1);
        $Page = new \Think\Page($count,$pagesize);

        $Page->setConfig('prev','<');
        $Page->setConfig('next','>');
        $Page->parameter='';
        $list['page'] = str_replace('href=','data-url=',$Page->show());
        $list['page'] = str_replace('/home/<USER>/index/','',$list['page']);
        $list['page'] = str_replace('.html','',$list['page']);
        $list['page'] = str_replace('p/','',$list['page']);
        $list['list'] = $this
            ->where($map)
            ->order($order)
            ->field('ldb_id,goods_name,brand_name,num,local,mobile,delivery_time,price,status,end_time,create_time')
            ->limit($first.','.$pagesize)
            ->select();
        foreach ($list['list'] as $k=>$v){
            if(!empty($v['mobile'])){
                $list['list'][$k]['mobile']=substr_replace($v['mobile'], '****', 3, 4);
            }
            if(!empty($v['local']) && $v['local']=='国内'){
                $list['list'][$k]['local']='国内(￥)';
                if(!empty($v['price']) && $v['price']!=0){
                    $list['list'][$k]['price']='￥'.sprintf("%.4f", $v['price']);
                }else{
                    $list['list'][$k]['price']='--';
                }

            }
            if(!empty($v['local']) && $v['local']=='香港'){
                $list['list'][$k]['local']='香港($)';
                if(!empty($v['price']) && $v['price']!=0){
                    $list['list'][$k]['price']='$'.sprintf("%.4f", $v['price']);
                }else{
                    $list['list'][$k]['price']='--';
                }

            }
        }
        return $list;
    }
}