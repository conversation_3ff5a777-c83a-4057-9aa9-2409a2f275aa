<?php
/**
 * 
 * @authors yc ()
 * @date    2018-11-29 17:57:43
 * @version $Id$
 */
namespace Home\Model;

use Think\Model;

class UserWalletModel extends Model {

    /**
     * 创建订单主信息
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    public function createOrder($data, $prefix = 2)
    {
        $add = array(
            'order_type' => 1,
            'status' => 1,
            'create_time' => time(),
        );
        if (!isset($data['wallet_sn'])) {
            $data['wallet_sn'] = $this->findSn($prefix);
        }
        $add = array_merge($add, $data);
        $order_id = $this->add($add);
        return $order_id;
    }

    /**
     * 获取唯一充值提现单据号
     * @param  string $prefix [description]
     * @param  string $suffix [description]
     * @return [type]         [description]
     */
    public function findSn($prefix = '', $suffix = '')
    {
        $sn = order_sn($prefix, $suffix);
        $order_id = $this->where(array('wallet_sn' => $sn))->getField('wallet_id');
        if (!empty($order_id)) {
            $sn = $this->findSn($prefix, $suffix);
        }
        return $sn;
    }

    /**
     * 获取用户充值提现单据列表
     * @param  [type] $user_id [description]
     * @param  string $where   [description]
     * @param  string $page    [description]
     * @param  string $order   [description]
     * @return [type]          [description]
     */
    public function getUserList($user_id, $where = '', $page = '', $order = 'create_time DESC')
    {
        $map = array(
            'user_id' => $user_id,
        );
        is_array($where) && $map = array_merge($map, $where);
        limit_page($this, $page);
        $datas = $this->where($map)->order($order)
                        ->field('wallet_id, wallet_sn, wallet_type, user_id, amount, bank_name, bank_account, bank_province, bank_city, bank_address, status, create_time')
                        ->select();
        return $datas;
    }

    /**
     * 获取列表数
     * @param  [type] $user_id [description]
     * @param  string $where   [description]
     * @return [type]          [description]
     */
    public function getUserCount($user_id, $where = '')
    {
        $map = array(
            'user_id' => $user_id,
        );
        is_array($where) && $map = array_merge($map, $where);
        $datas = intval($this->where($map)->count());
        return $datas;
    }

    /**
     * 获取列表
     * @param  string $where [description]
     * @param  string $field [description]
     * @return [type]        [description]
     */
    public function getList($where = '', $field = '*')
    {
        $datas = $this->where($where)->field($field)->select();
        return $datas;
    }

    /**
     * 获取单据信息
     * @param  [type] $wallet_id [description]
     * @param  string $field     [description]
     * @return [type]            [description]
     */
    public function getInfo($wallet_id = '', $wallet_sn = '', $field = '*')
    {
        if (!empty($wallet_id)) {
            $map['wallet_id'] = $wallet_id;
        } elseif (!empty($wallet_sn)) {
            $map['wallet_sn'] = $wallet_sn;
        } else {
            return false;
        }
        $data = $this->where($map)->field($field)->find();
        return $data;
    }

    /**
     * 根据条件获取一条数据
     * @param  [type] $where [description]
     * @param  string $field [description]
     * @return [type]        [description]
     */
    public function getInfoWhere($where, $field = '*')
    {
        $data = $this->where($where)->field($field)->find();
        return $data;
    }
}