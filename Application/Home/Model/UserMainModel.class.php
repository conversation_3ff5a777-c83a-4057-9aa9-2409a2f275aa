<?php
namespace Home\Model;

use Home\Services\UcenterService;
use Think\Model;

class UserMainModel extends Model
{
    /**
     * 注册成功
     * @param [type]  $type      [1：手机注册，2：邮箱注册，3：账号注册]
     * @param integer $reg_user_info['account']  [账号]
     * @param integer $reg_user_info['password'] [密码]
     * @param string  $reg_user_info['pf']      [平台号]
     * @param integer $reg_user_info['invite_uid'] [邀请人user_id]
     */
    public function regSuccess($type, $reg_user_info, $is_reg_by_admin=false, $is_from_chain=0)
    {
        $account      = $reg_user_info['account'];
        $password     = $reg_user_info['password'];
        $user_name    = isset($reg_user_info['user_name']) ? $reg_user_info['user_name'] : '';
        $pf           = $reg_user_info['pf'];
        $invite_uid   = !empty($reg_user_info['invite_uid']) ? $reg_user_info['invite_uid'] : '';
        $user_code    = !empty($reg_user_info['user_code']) ? $reg_user_info['user_code'] : '';
        $parent_id    = !empty($reg_user_info['parent_id']) ? $reg_user_info['parent_id'] : '';
        $erp_sale_uid = !empty($reg_user_info['erp_sale_uid']) ? $reg_user_info['erp_sale_uid'] : '';
        $anonymous_id = !empty($reg_user_info['anonymous_id']) ? $reg_user_info['anonymous_id'] : '';
        $channel_type = isset($reg_user_info['channel_type']) ? intval($reg_user_info['channel_type']) : 1;
        $agentInfo    = getAgentInfo();//获取浏览器和系统信息
        //手机免密码登录时自动注册没密码账号
        if (!empty($password)) {
            $salt     = hash_key(32);//加密盐
            $password = pwdhash($password, $salt);
        } else {
            $password = '';
            $salt     = '';
        }
        //检查邀请人uid是否有效
        $user_check = S_user($invite_uid);
        if (!$user_check) {
            $invite_uid = '';
        }


        $pf      = platform();

        $referer = $_SERVER['HTTP_REFERER'];
        $adtag   = $_COOKIE['adtag'];
        $ptag    = $_COOKIE['ptag'];

        if($_REQUEST['ptag'] == 'edcp'){
            $ptag = 'edcp';
        }

        $url_arr = parse_url($referer);
        parse_str($url_arr['query'], $param_arr);
        if (!isset($param_arr['adtag']) && !empty($adtag)) {
            $param_arr['adtag'] = $adtag;
        }
        unset($param_arr['ptag']);
        if (!empty($ptag)) {
            $param_arr['ptag'] = $ptag;
        }
        $param = str_replace('&', ',', urldecode(http_build_query($param_arr)));

        //注册新账号
        $addData = array(
            'password'         => $password,
            'user_name'        => $user_name,
            'salt'             => $salt,
            'reg_ip'           => get_client_ip(0, true),
            'reg_entrance'     => $url_arr['host'].$url_arr['path'],//注册入口(来源url)
            'reg_remark'       => $param,//注册备注参数（url携带的参数）
            'device'           => $agentInfo['sys'], //（操作系统）
            'create_device'    => $pf, //注册来源平台（1：pc:2：移动端）
            'browser'          => $agentInfo['bro'],//注册浏览器相关信息
            'create_time'      => $_SERVER['REQUEST_TIME'],
            'fake_create_time' => $_SERVER['REQUEST_TIME'],
            'invite_uid'       => $invite_uid,
            'parent_id'        => $parent_id ? $parent_id : 0,
            'user_code'        => $user_code ? $user_code : '',
            'erp_sale_uid'     => $erp_sale_uid ? $erp_sale_uid : 0,
            'user_agent'       => $_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '--',
            'is_test'          => isset($reg_user_info['is_test']) ? $reg_user_info['is_test'] : 0,
            'user_nature'      => isset($reg_user_info['user_nature']) ? $reg_user_info['user_nature'] : '',
            'is_sendmsg'       => isset($reg_user_info['is_sendmsg']) ? $reg_user_info['is_sendmsg'] : 1,
            'is_new'           => 0,
            'channel_type'     => $channel_type,//1 线上用户 ，2线下用户
            'invite_code'      => I('yqcode', '') ? strtolower(I('yqcode')) : '', // CRM用户邀请码
        );

        if ($type == 1) {
            $addData['mobile'] = $account;
            $addData['intl_code'] = isset($reg_user_info['intl_code']) ? $reg_user_info['intl_code'] : '';

            if (strstr($account, "+")) { // 国际手机
                $account_arr          = explode("+", $account);
                $addData['intl_code'] = $account_arr[0] ? $account_arr[0] : '';
                $addData['mobile']    = $account_arr[1];
            }
            $addData['status'] = '1';
        } elseif ($type == 2) {
            $addData['email']  = $account;
            $addData['status'] = ($is_reg_by_admin)?'1':'5';
        } elseif ($type == 3) {
            $addData['user_name'] = $account;
            $addData['status']    = '1';
        }

        //请求用户中心注册
        $ucenter = UcenterService::ucenterRegister($addData);
        if(empty($ucenter) || $ucenter["code"] != 0){
            $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件
            \Think\Log::write('请求用户中心注册失败，参数：' . json_encode($addData) . '返回数据：' . json_encode($ucenter), INFO, '', $path);

            return false;
        }
        $ucId = !empty($ucenter["data"]["uc_id"]) ? $ucenter["data"]["uc_id"] : 0;
        if (!empty($ucenter["data"]["token"])) {
            cookie('ucenter_token', $ucenter["data"]["token"]);
        }

        if(!$ucId){
            return false;
        }
        $addData["uc_id"] = $ucId;

        $user_id = D('UserMain')->data($addData)->add();
        D('UserInfo')->data(['user_id' => $user_id])->add(); // 添加用户扩展信息

        //还要去绑定user_id
        //UcenterService::ucenterBindOrgUserId($ucId, $user_id);

        // 添加操作日志
        $operator = $is_reg_by_admin ? $reg_user_info['sale_id'] : $user_id;
        $operator_type = $is_reg_by_admin ? 2 : 1;
        D('UserActionLog')->addLog($user_id, $operator, $operator_type, '新增用户');

        // 添加活动ID
        $activity_id = $_COOKIE['lx_act_id'];
        // 判断是否有专题活动id,有的话,也要写入
        $special_activity_ids = cookie('activity_ids');

        $hasActivityRecord = false;
        $activity_record = [];
        if (!empty($activity_id)) {
            $activity_record = [
                'user_id'     => $user_id,
                'activity_id' => $activity_id,
            ];
            $hasActivityRecord = true;
        }

        if (!empty($special_activity_ids)) {
            $activity_record['user_id'] = $user_id;
            $activity_record['special_activity_ids'] = $special_activity_ids;
            $hasActivityRecord = true;
        }

        if ($hasActivityRecord) {
            D('UserActivity')->data($activity_record)->add();
        }


        S_user($user_id, $addData); //获取userInfo 不作限制
        S_account($account, $user_id); //根据email或mobile 获取user_id
        if((1==$type || (2==$type && $is_reg_by_admin)) && !$is_from_chain){
            $this->addUserPoint($user_id);
        }
        if (2!=$type){//邮箱不发优惠券 也不上报注册成功 解决重复上报的问题
            //供应链注册不发优惠券
            if (!$is_from_chain) {
                regIssueCoupon($user_id,$pf);
            }
        }

        // 平台用户推送到CRM队列  7-CRM，2-供应链
        if ($pf != 20 && $pf != 7) {
            $this->pushUserToCrm($user_id, $ucId);
        }

        // 仅前台手机，后台导入手机、邮箱，非供应链会员，上报到神策，否则直接返回user_id ---********
        if ($pf == 20 || (!$is_reg_by_admin && $type == 2)) {
            return $user_id;
        }

        //神策数据上报
        // $properties = array(
        //     'event'             => 'signUp',
        //     'user_id'           => $user_id,
        //     'Phone'             => !empty($addData['mobile'])?$addData['mobile']:'',
        //     'anonymous_id'      => !empty($reg_user_info['anonymous_id'])?$reg_user_info['anonymous_id']:'',
        //     'email'             => !empty($addData['email'])?$addData['email']:'',
        //     'register_type'     => 'mobile',
        //     'signUp_ptag_click' => ptag(),
        //     'is_free_password'  => empty($reg_user_info['password'])?true:false,
        //     'mob_type'          => (empty($addData['intl_code']) || $addData['intl_code'] == '0086')?false:true,
        //     'login_status'      => '',
        //     'first_send_email'  => false,
        //     'test_account'      => $reg_user_info['is_test']==1? true:false,
        //     'contact_name'      => $user_name,
        //     'signUp_source'     => $is_reg_by_admin? '来源于后台':'来源于前端',
        //     'user_nature'       => isset($reg_user_info['user_nature']) ? $reg_user_info['user_nature'] : '',
        // );
        // $this->report_data($properties);

        return $user_id;
    }

    /**
     * 推送注册用户到CRM队列
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    public function pushUserToCrm($user_id, $uc_id)
    {
        $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件

        try {
            // 推入到队列
            $crm_sys_api = CRM_V2_DOMAIN . '/open/pushCrmQueue';

            $data = [
                'user_id' => $user_id,
                'uc_id' => $uc_id,
                'org_id' => 1,
            ];

            $res = json_decode(post_curl($crm_sys_api, $data), true);

            if ($res) {
                $msg_text = '新增用户，推送到队列任务成功，用户ID：' . $user_id;
            } else {
                $msg_text = '新增用户，推送到队列任务失败，用户ID：' . $user_id;
            }

            // dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.crm_mq'), $msg_text);

            \Think\Log::write($msg_text, INFO, '', $path);
        } catch (\Exception $e) {
            // dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.crm_mq'), '新增用户(' . $user_id . ')，推送到队列任务失败，错误信息：' . $e->getMessage());

            $msg_text = '新增用户(' . $user_id . ')，推送到队列任务失败，错误信息：' . $e->getMessage();
            \Think\Log::write($msg_text, INFO, '', $path);
        }
    }

    // 更新用户信息
    public function updateUserPwd($user_info)
    {
        $data = [];

        $data['salt'] = hash_key(32);//加密盐
        $data['password'] = pwdhash($user_info['password'], $data['salt']);

        $res = D('UserMain')->where(['user_id' => $user_info['user_id']])->save($data);

        if ($res === false) return false;

        $addData = $this->find($user_info['user_id']);

        S_user($user_info['user_id'], $addData);
        S_account($user_info['account'], $user_info['user_id']);

        return true;
    }

    private function report_data($properties){
        $RbmqModel = D('Common/Rbmq');
        $RbmqModel->queue(C('QUEUE_REPORT_DATA'))->push($properties, C('QUEUE_REPORT_DATA'));
    }

    private function addUserPoint($user_id){
        //rbmq入队
        $queue = C('QUEUE_MKT_POINT');
        $RbmqModel = D('Common/Rbmq');
        $push_data = array(
            'user_id' => $user_id,
            'flow_type' => 1,
            'flow_reason_type' => 1,
            'flow_pf' => platform()
        );
        $RbmqModel->queue($queue)->push($push_data, $queue);
    }

    /**
     * 判断是否有账号
     * @param  [type]  $account [description]
     * @return boolean          [description]
     */
    public function isHasAccount($account)
    {
        if (is_email($account)) {
            $map = array(
                'mobile' => $account,
            );
        } elseif (is_mobile($account)) {
            $map = array(
                'email' => $account,
            );
        } else {
            $map = array(
                'user_name' => $account,
            );
        }
        $user_id = $this->where($map)->getField('user_id');
        return $user_id;
    }

    /**
     * 获取用户数据
     * @param  [type] $user_id [description]
     * @return [type]          [description]
     */
    public function getInfo($user_id)
    {
        $datas = $this->find($user_id);
        return $datas;
    }

    //根据类型返回where的条件
    public function getConditionByType($type = 1)
    {
        switch ($type) {
            case '1':
                $where['create_time'] = array('gt',strtotime(date('Y-m-d', time())));
                break;
            case '2':
                $beginTime = strtotime(date("Y-m-d",strtotime("-1 day")));
                $endTime = strtotime(date('Y-m-d', time()));
                $where['create_time'] = array('between',"$beginTime, $endTime");
                break;
            case '3':
                $beginTime = strtotime(date("Y-m-d",strtotime("-7 day")));
                $endTime = strtotime(date('Y-m-d', time()));
                $where['create_time'] = array('between',"$beginTime, $endTime");
                break;
            case '4':
                $where['last_login_time'] = array('gt',strtotime(date('Y-m-d', time())));
                break;
            case '5':
                $beginTime = strtotime(date("Y-m-d",strtotime("-1 day")));
                $endTime = strtotime(date('Y-m-d', time()));
                $where['last_login_time'] = array('between',"$beginTime, $endTime");
                break;
            case '6':
                $beginTime = strtotime(date("Y-m-d",strtotime("-7 day")));
                $endTime = strtotime(date('Y-m-d', time()));
                $where['last_login_time'] = array('between',"$beginTime, $endTime");
                break;
            default:
                # code...
                break;
        }

        return $where;
    }

    // 处理会员
    public function getCount($type)
    {
        $where = $this->getConditionByType($type);

        // 过滤测试账号
        $where['user_id'] = ['not in', $this->testAccount()];

        if (in_array($type, array(1, 2, 3))) {
            $count = $this->where($where)->count();
        } else {
            $count = D('UserLoginLog')->where($where)->count('DISTINCT user_id');
        }

        return $count;
    }

    // 测试帐号
    public function testAccount()
    {
        $testId = array();

        $user = D('UserMain')->field('user_id')->where(['is_test' => 1])->select();

        foreach ($user as $k => $v) {
            $testId[$k] = $v['user_id'];
        }

        return $testId;
    }

    // 获取会员人数
    public function getAllUserCount()
    {
        $data['regUserCurrent']     = $this->getCount(1); // 今日注册
        $data['regUserYesterday']   = $this->getCount(2); // 昨日注册
        $data['regUserWeek']        = $this->getCount(3); // 一周注册
        $data['loginUserCurrent']   = $this->getCount(4); // 今日登录
        $data['loginUserYesterday'] = $this->getCount(5); // 昨日登录
        $data['loginUserWeek']      = $this->getCount(6); // 一周登录

        return $data;
    }

    // 获取自定义会员
    public function getCustomData($map, $pagesize=10)
    {
        $page = !empty($map['p']) ? $map['p'] : 1;

        $first = $pagesize * ($page-1);

        // 注册
        if ($map['search_type'] == 1) {
            $where = $this->handleTimeCondition($map, 'create_time');

            // 过滤测试账号
            $where['user_id'] = ['not in', $this->testAccount()];

            $count = $this->where($where)->count();

            $data = $this->where($where)->limit($first.','.$pagesize)->order('create_time desc')->select();
        } else {
            $where = $this->handleTimeCondition($map, 'last_login_time');

            // 过滤测试账号
            $where['user_id'] = ['not in', $this->testAccount()];

            $count = D('UserLoginLog')->where($where)->count('DISTINCT user_id');

            $data = D('UserLoginLog')->where($where)->limit($first.','.$pagesize)->group('user_id')->order('last_login_time desc')->select();
        }

        $response['count'] = $count;
        $response['data']  = $data;

        return $response;
    }

    // 时间筛选
    public function handleTimeCondition($map, $field)
    {
        if ($map['begin_time'] && $map['end_time']) {
            $where[ $field] = ['between', [$map['begin_time'], $map['end_time']]];
        } else if ($map['begin_time']) {
            $where[ $field] = ['gt', $map['begin_time']];
        } else {
            $where[ $field] = ['lt', $map['end_time']];
        }

        return $where;
    }


    /**
     * 获取账号的ID (带缓存)
     * @param  [type]  $account [description]
     * @param  boolean $cache   [description]
     * @return [type]           [description]
     */
    public function getAccountUserId($account, $cache = true)
    {
        if (empty($account)) {
            return false;
        }
        if ($cache) {
            $user_id = S_account($account);
            if (!empty($user_id)) {
                return $user_id;
            }
        }
        if (is_email($account)) {
            $map['email'] = $account;
        } elseif (is_mobile($account)) {
            $map['mobile'] = $account;
        }
        $user_id = $this->where($map)->getField('user_id');
        if (!empty($user_id) && $cache) {
            S_account($account, $user_id);
        }
        return $user_id;
    }

    /**
     * 获取账号信息（带缓存）
     * @param  [type]  $user_id [description]
     * @param  boolean $cache   [description]
     * @return [type]           [description]
     */
    public function getUserInfo($user_id, $cache = true)
    {
        if (empty($user_id)) {
            return false;
        }
        if ($cache) {
            $rs_info = S_user($user_id);
            if (!empty($rs_info) && isset($rs_info['user_id'])) {
                return $rs_info;
            }
        }
        $info = $this->find($user_id);
        if (!empty($rs_info) && is_array($rs_info)) {//补充数据
            $info = array_merge($rs_info, $info);
        }

        if (!empty($info) && $cache) {
            S_user($user_id, $info);
        }

        return $info;
    }

    /**
     * 修改用户信息（带缓存）
     * @param [type]  $user_id [description]
     * @param [type]  $data    [description]
     * @param boolean $cache   [description]
     */
    public function setUserInfo($user_id, $data, $cache = true)
    {
        if (empty($user_id) || empty($data)) {
            return false;
        }
        $info = $this->getUserInfo($user_id);
        $map['user_id'] = $user_id;
        $res = $this->where($map)->save($data);
        if ($res !== false && $cache) {
            $rs_info = S_user($user_id);
            if (!empty($rs_info) && is_array($rs_info)) {
                $info = array_merge($rs_info, $info, $data);
            } else {
                $info = array_merge($info, $data);
            }
            S_user($user_id, $info);
        }
        return $res;
    }
}
