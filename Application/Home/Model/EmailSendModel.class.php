<?php
namespace Home\Model;

use Think\Model;

class EmailSendModel extends Model
{
    protected $autoCheckFields = false;
    public function sendMail($email, $keyword, $data)
    {
        // $send_data = urldecode(json_encode($data));
        $send_data = json_encode($data, JSON_UNESCAPED_UNICODE);
        $check['pf'] = platform();
        $check['template_id'] = I('template_id');
        $check['keyword'] = $keyword;
        $check['touser'] = json_encode($email);
        $check['data'] = $send_data;
        $check['channel_type'] = 3; // 邮箱
        $check['is_ignore'] = true;
        $check = array_merge($check, authkey());
        //为什么这里写死线上的地址,因为本地的消息服务负责人压根不愿意起来跑,导致每次测试都只能连线上的
        $res = post_curl('https://api.ichunt.com/msg/sendMessageByAuto', $check);
        //$res = post_curl(API_DOMAIN.'/msg/sendMessageByAuto', $check);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
    }
    /**
     * 发送邮箱
     * @param user_id, type (发送类型 1：激活邮件，2：绑定邮箱，3：忘记密码, 4: 进口报关(供应链), 5: 供应商, 6: SMT贴片)
     */
    public function send($user_id, $type = 1, $info = array())
    {
        if (strval($type) !== '4' && strval($type) !== '5' && strval($type) !== '6') {
            if (!$user_id) {
                return false;
            }
            $userInfo = S_user($user_id);
            $email = $userInfo['email'];
            if ($type == 2) {
                $email = $info['email'];
            }
            if (!$email) {
                return false;
            }
        } else {
            $email = $info['email'];
        }
        $time = date("Y-m-d H:i:s",time());
        $data['url'] = $info['verifyEmailUrl'];
        $data['time'] = $time;

        switch (strval($type)) {
                    case '1': //发送激活邮箱链接
                        $keyword = 'eamil-reg-verify';
                        break;
                    case '2':
                        $keyword = 'email-bind-verify'; // 猎芯网-邮箱绑定验证激活
                        break;
                    case '3':
                        $keyword = 'email-fgpwd-verify'; // 猎芯网-忘记密码邮箱验证激活
                        break;
                    case '4':
                        $data['link_name'] = $info['link_name'];
                        $data['need'] = $info['need'];
                        $data['telphone'] = $info['telphone'];
                        $keyword = 'custom-admin-message';
                        break;
                    case '5':
                        $data = $info;
                        // $data['main_brand'] = $info['main_brand'];
                        $keyword = 'supplier-admin-message-new';
                        break;
                    case '6':
                        $data['link_name'] = $info['link_name'];
                        $data['telphone'] = $info['telphone'];
                        $keyword = 'smt-admin-message';
                        break;
                    default:
                        break;
        }

        $res = $this->sendMail($email, $keyword, $data);
        /*switch (strval($type)) {
            case '1': //发送激活邮箱链接
                $title = '猎芯网-邮箱注册验证激活';
                $url = $info['verifyEmailUrl'];
                $text = "感谢您注册猎芯网IChunt，请<a href=".$url." target='_blink' >点击此处完成注册</a>进入页面以完成激活验证，24小时内有效。尽情开启芯旅程吧~";
                $html = $this->outputhtml($url, $text);
                $res = $this->sendMail($email, );
                break;
            case '2':
                $title = '猎芯网-邮箱绑定验证激活';
                $url = $info['verifyEmailUrl'];
                $text = "您已申请修改绑定邮箱，请<a href=".$url." target='_blink'>点击此处</a>进入完成本邮箱激活，半小时内有效。激活后您的猎芯账号将换成本邮箱绑定，如果未做任何操作，系统将保留原绑定邮箱。";
                $html = $this->outputhtml($url, $text);
                $res = $this->sendMail($email, $title, $html);
                break;
            case '3':
                $title = '猎芯网-忘记密码邮箱验证激活';
                $url = $info['verifyEmailUrl'];
                $text = "您已申请修改登录密码，请<a href=".$url." target='_blink'>点击此处</a>进入修改密码页面，24小时内有效。如果未做任何操作，系统将保留原密码。";
                $html = $this->outputhtml($url, $text);
                $res = sendMail($email, $title, $html);
                break;
            case '4':
                $time = date("Y-m-d H:i:s",time());
                $title = '猎芯网-系统后台通知';
                $html = '<p>【进口报关】有新的进口报关需求</p><br><p>'.$time.'&nbsp;&nbsp;有客户提交进口报关需求，快去处理吧！~</p>';
                $res = sendMail($email, $title, $html);
                break;
            default:
                break;
        }*/
        return true;
    }

    public function outputhtml($url, $text)
    {
        return <<<EOT
        <html>
        <head><title>主页</title></head>
            <body style="font-family:"Open Sans",Arial,"Hiragino Sans GB","Microsoft YaHei","微软雅黑","STHeiti","WenQuanYi Micro Hei",SimSun,sans-serif; text-decoration: none;}
                            body,html{ width: 100%; height: 100%; padding: 0; margin: 0; color: #252525; font-size: 12px;">
                <div style="width:736px;min-width: 736px; max-width: 736px; padding: 0 32px; margin:0 auto; border-top: 4px solid #23a8f6; border-radius: 2px; box-shadow: 0 2px 3px #eeeeee;
                -webkit-box-shadow: 0 2px 3px #EEEEEE;">
                    <div style="height: 61px; padding-top: 38px; border-bottom: 2px solid #dddcdc;">
                        <ul style="float: right; text-align: right;">
                            <li style="display: inline-block; list-style: none; color: #434343;"><a href="http://www.ichunt.com/" target="_blink" style="color: #23a8f6; font-weight: bold; margin-right: 18px; margin-left: 15px;">猎芯网</a><span style="color: #c4c4c4;">|</span></li>
                            <li style="display: inline-block; list-style: none; color: #434343;"><a href="javascript:void(0);" target="_blink" style="color: #23a8f6; font-weight: bold; margin-right: 18px; margin-left: 15px;">会员中心</a></li>
                            <li style="display: inline-block; list-style: none; color: #434343;">客服电话：<b style="font-weight: bold; font-size: 16px;">0755-88914841</b></li>
                        </ul>
                        <div style="float: left;">
                        <a href="http://www.ichunt.com/" target="_blink">
                            <img width="300px" src="http://www.ichunt.com/Public/Home/images/logo.png">
                        </a>
                        </div>
                    </div>
                    <div style="min-height: 480px; *height: 480px;">
                        <h1 style="font-weight: bold; color: #434343; padding: 20px 0; font-size: 12px;">亲爱的会员：</h1>
                        <h1 style="font-weight: bold; color: #434343; padding: 20px 0; font-size: 12px;">{$text}</h1>
                        <h1 style="font-weight: bold; color: #434343; padding: 20px 0; font-size: 12px;">若链接点击无效，请复制链接<a target="_blink" href="{$url}">{$url}</a>至浏览器地址栏打开。</h1>
                        <p style="color: #434343; padding-bottom: 25px; line-height: 23px; font-size: 12px;">
                        <br>安全提醒：猎芯网不会以付款异常、系统升级等为由要求您退款或再次支付，同时也不会发送虚拟网址、中奖、抽奖的短信让您点击进入；请您不要将银行卡资料、验证码、个人资料等提供他人，谨防诈骗！遇此情况请及时联系客服人员或报警。为了您的会员账户安全，建议您不要用公共场所的网络记住您的登录账号及密码。</p>
                        <p style="color: #434343; padding-bottom: 25px; line-height: 23px; font-size: 12px;">防诈骗提醒须知<br>请认准唯一官方网址：<a style="color: #23a8f6;" href="http://www.ichunt.com/" target="_blink">www.ichunt.com</a></p>
                    </div>
                    <dl style="height: 79px; padding: 15px 0; border-bottom: 1px dashed #dadada;  border-top: 1px dashed #dadada;">
                        <dt style="float: left; height: 79px; width: 80px;">
                        <img style="border: none; padding: 0; margin: 0; display: block; height: 77px; width: 77px; border: 1px solid #eeeeee;  border-radius:3px;" src="http://www.ichunt.com/Public/Home/images/code.jpg">
                        </dt>
                        <dd>
                            <p><b>了解更多IC资讯、猎芯活动，关注猎芯网</b></p>
                            <p>猎芯网( www.ichunt.com）电子元器件全产业链B2B电商平台</p>
                        </dd>
                    </dl>
                    <div style="padding: 20px 0;">
                        <p style="color: #515151;">此邮件为猎芯网系统自动发出，请勿直接回复</p>
                        <p style="color: #bababa;">深圳市猎芯科技有限公司   粤ICP备15024702号-1 Copyright®深圳市猎芯科技有限公司 </p>
                    </div>
                </div>
            </body>
        </html>
EOT;
    }
}
