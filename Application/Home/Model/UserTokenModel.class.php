<?php
namespace Home\Model;

use Think\Model;

class UserTokenModel extends Model
{
    /**
     * 插入数据库
     * @param type 1：注册邮箱，2：绑定邮箱，3：忘记密码
     * @param user_id
     * @param email 需要绑定的邮箱
     */
    public function activateEmail($user_id, $type = 1, $email = '')
    {
        if (!$user_id) {
            return false;
        }
        //清空该用户之前的status 为0的邮件
        $map = array(
            'user_id'       => $user_id,
            'type'          => $type,
        );
        $save = array('status' => '1');
        D('UserToken')->where($map)->save($save);
        $token = hash_key(64);
        $emailExpireTime = C('EMAIL_EXPIRE_TIME');
        $emailType = $type;
        if ($type !== 2) { //默认除绑定邮箱之外其他邮箱操作都一天
            $emailType = 1;
        }

        $emailExpireTime = $emailExpireTime[$emailType];
        $emailExpireTime = intval($emailExpireTime);
        $userTokenData = array(
            'user_id'       => $user_id,
            'token'         => $token,
            'status'        => 0,
            'type'          => $type, //注册邮箱
            'expire_time'   => $_SERVER['REQUEST_TIME'] + $emailExpireTime,
            'create_time'   => $_SERVER['REQUEST_TIME'],
        );

        try {
            D('UserToken')->data($userTokenData)->add();
        } catch (Exception $e) {

        }
        if ($email) {
            $userInfo = S_user($user_id);
            $preEmail = $userInfo['email'];
            $userTokenData['preEmail'] = $preEmail;
            $userTokenData['email'] = $email;
        }
        S_token($token, $userTokenData);
        //邮箱添加发送次数
        $userInfo = S_user($user_id);
        $emailErrorType = 'email_error_'.$type;
        $emailErrorTimeType = 'email_error_time_'.$type;
        $userInfo[$emailErrorTimeType] = mktime(0, 0, -1, date('m'), date('d') + 1, date('Y'));//一天内
        $userInfo[$emailErrorType] = intval($userInfo[$emailErrorType]) + 1;
        S_user($user_id, $userInfo);
        //合成验证链接
        $http_host = $_SERVER['HTTP_ORIGIN'].'/v3';
        switch (strval($type)) {
            case '1':
                $backurl = I('request.backurl', '', 'trim');
                //$backurl = urldecode($backurl);
                $backurlstr = '';
                if ($backurl) {
                    $backurlstr = '&backurlstr=' . $backurl;
                }
                $verifyEmailUrl = $http_host.'/reg/success?token='.$token . $backurlstr;
                $verifyEmailUrl = str_replace('&amp;', '&', $verifyEmailUrl);

                break;
            case '2':
                $verifyEmailUrl = $http_host.'/user/emailbind?token='.$token;//待定
                break;
            case '3':
                $verifyEmailUrl = $http_host.'/forget.html?token='.$token;//待定
                break;
            default:
                # code...
                break;
        }
        $info['verifyEmailUrl'] = $verifyEmailUrl;
        if ($type == 2) {
            $info['email'] = $userTokenData['email'];
        }
        $res = D('EmailSend')->send($user_id, $type, $info);
        return $res;
    }
}