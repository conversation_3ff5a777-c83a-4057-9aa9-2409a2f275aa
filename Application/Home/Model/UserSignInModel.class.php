<?php
/**
 * Created by PhpStorm.
 * User: leo
 * Date: 2018/10/17
 * Time: 19:36
 */

namespace Home\Model;
use Think\Model;

class UserSignInModel extends Model
{
    /** 签到
     * @param $user_id
     * @param $pf
     * @return mixed
     */
    public function signIn($user_id,$pf){
        $data['user_id'] = $user_id;
        $data['platform'] = $pf;
        $data['create_time'] = time();
        $yesterday_start = strtotime(date("Y-m-d"),time())-60*60*24;
        $yesterday_end = strtotime(date("Y-m-d"),time());
        //检验昨天是否签到
        $is_sign_in_yesterday = $this->isSignIn($user_id,$yesterday_start,$yesterday_end);
        //昨天未签到则连续签到次数为1
        if(false===$is_sign_in_yesterday){
            $data['continuous_sign_day'] = 1;
        }else{//已签到则连续签到天数+1
            $map['create_time'] = array('between',array($yesterday_start,$yesterday_end));
            $map['user_id'] = $user_id;
            $res = $this->where($map)->getField('continuous_sign_day');
            if($res){
                $data['continuous_sign_day'] = $res+1;
            }
        }
        $sign_in_data = false;
        if($sign_in_id=$this->add($data)){
            $sign_in_data['user_sign_id'] = $sign_in_id;
            $sign_in_data['create_time'] = $data['create_time'];
            $sign_in_data['continuous_sign_day'] = $data['continuous_sign_day'];
            $sign_in_data['user_id'] = $user_id;
            $sign_in_data['platform'] = $pf;
        }
        return $sign_in_data;
    }

    /**
     * 判断某一天是否签到过（默认当天）   找最近的一条用户签到数据！！！！然后比对是否是某一天?  这样效率才最高！
     * @param $user_id
     * @param $start_time
     * @param $end_time
     * @return bool
     */
    public function isSignIn($user_id,$start_time=0,$end_time=0){
        $map['user_id'] = $user_id;
        if(empty($start_time) || empty($end_time)){//默认当天
            $start_time = strtotime(date("Y-m-d"),time());
            $end_time = $start_time+60*60*24;
            $res = $this->where($map)->order('create_time DESC')->limit(1)->select();
            if($res && 1===count($res)){
                if($res[0]['create_time']>=$start_time && $res[0]['create_time']<=$end_time){
                    return true;
                }else{
                    return false;
                }
            }
        }
        //其他天
        $map['create_time'] = array('between',array($start_time,$end_time));
        $is_sign_in = $this->where($map)->select();
        return !empty($is_sign_in)?true:false;
    }
}