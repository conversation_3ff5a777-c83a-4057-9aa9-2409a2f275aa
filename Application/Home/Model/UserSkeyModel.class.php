<?php
namespace Home\Model;

use Think\Model;

class UserSkeyModel extends Model {

    /**
     * 根据user_id获取基本信息
     * @param  integer $user_id [description]
     * @return [type]           [description]
     */
    public function getInfo($user_id = 0)
    {
        $map = array(
            'user_id' => $user_id
        );
        $datas = $this->where($map)->find();
        return $datas;
    }

    /**
     * 设置登录态
     * @param integer $user_id [description]
     * @param [type]  $skey    强登录态 当为空字符串时，不更新
     * @param [type]  $lskey   弱登录态 当为空字符串时，不更新
     */
    public function setSkey($user_id = 0, $skey = null, $lskey = null)
    {
        is_null($skey) && $skey = hash_key();
        is_null($lskey) && $lskey = hash_key(32);
        $key_arr = array();
        if (!empty($skey)) {
            $key_arr['skey'] = $skey;
            $key_arr['skey_create_time'] = $_SERVER['REQUEST_TIME'];
            $key_arr['skey_expire_time'] = $_SERVER['REQUEST_TIME'] + C('SKEY_EXPIRE_TIME');
        }
        if (!empty($lskey)) {
            $key_arr['lskey'] = $lskey;
            $key_arr['lskey_create_time'] = $_SERVER['REQUEST_TIME'];
            $key_arr['lskey_expire_time'] = $_SERVER['REQUEST_TIME'] + C('LSKEY_EXPIRE_TIME');
        }
        if (empty($key_arr)) {
            return false;
        }
        $datas = $this->getInfo($user_id);
        if (!empty($datas)) {
            $save = array(
                'id' => $datas['id'],
            );
            $save = array_merge($save, $key_arr);
            $res = $this->save($save);
        } else {
            $add = array(
                'user_id' => $user_id,
            );
            $save = array_merge($save, $key_arr);
            $res = $this->add($add);
        }
        return $res;
    }
}