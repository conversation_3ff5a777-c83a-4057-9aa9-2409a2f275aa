<?php
/**
 * 
 * @authors yc ()
 * @date    2018-11-29 17:57:43
 * @version $Id$
 */
namespace Home\Model;

use Think\Model;

class UserWalletBankModel extends Model {

    public function getValidInfo($wallet_bank_id)
    {
        $map = array(
            'wallet_bank_id' => $wallet_bank_id,
            'status' => 1,
        );
        $data = $this->where($map)->field('wallet_bank_id, bank_user, bank_name, bank_account, bank_province, bank_city, bank_address, bank_mobile')->find();
        return $data;
    }

    public function getInfo($wallet_bank_id)
    {
        $map = array(
            'wallet_bank_id' => $wallet_bank_id,
        );
        $data = $this->where($map)->field('wallet_bank_id, user_id, bank_name, bank_account, bank_province, bank_city, bank_address, bank_mobile, status')->find();
        return $data;
    }
}