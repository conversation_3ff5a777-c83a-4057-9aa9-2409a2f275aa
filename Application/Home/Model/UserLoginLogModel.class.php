<?php
namespace Home\Model;

use Think\Model;

class UserLoginLogModel extends Model {

    /**
     * 日志记录
     * @param [type]  $user_id      [description]
     * @param integer $platform     [description]
     * @param integer $account_type [description]
     * @param string  $open_id      [description]
     * @param array   $extend       [description]
     */
   public function addLog($user_id, $platform = -1, $account_type = -1, $open_id = '', $extend = array())
   {
        $map = array(
            'user_id' => $user_id,
            'last_login_time' => array('gt', mktime(0,0,0, date('m'), date('d'), date('Y')))
        );
        $info = $this->where($map)->find();

        $referer = $_SERVER['HTTP_REFERER'];
        $adtag = $_COOKIE['adtag'];
        $ptag = $_COOKIE['ptag'];
        $url_arr = parse_url($referer);
        parse_str($url_arr['query'], $param_arr);
        if (!isset($param_arr['adtag']) && !empty($adtag)) {
            $param_arr['adtag'] = $adtag;
        }
        unset($param_arr['ptag']);
        if (!empty($ptag)) {
            $param_arr['ptag'] = $ptag;
        }
        if (isset($extend['k'])) {
            $param_arr['k'] = $extend['k'];
        }
        $param_arr['behavior'] = get_scene(3);
        $param = str_replace('&', ',', urldecode(http_build_query($param_arr)));
        $agent = getAgentInfo();
        $data = array(
            'user_id' => $user_id,
            'last_login_time' => $_SERVER['REQUEST_TIME'],
            'login_ip' => get_client_ip(0, true),
            'platform' => $platform,
            'device' => $agent['sys'],
            'browser' => $agent['bro'],
            'login_source' => $url_arr['host'].$url_arr['path'],
            'login_remark' => $param,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ? $_SERVER['HTTP_USER_AGENT'] : '',
            'open_id' => !empty($open_id) ? $open_id : '',
            'account_type' => $account_type,
            'login_flag' => empty($info) ? 1 : 0,
        );
        $res = $this->add($data);
        return $res;
   }

   /**
    * 查询用户在指定时间段内是否登录过
    */
   public function getUserLastLogin($user_id,$start_time,$end_time)
   {
        $map = array(
            'user_id' => $user_id,
        );
        $map['last_login_time']=['between',[$start_time,$end_time]];
        $info = $this->where($map)->find();
        return empty($info)?false:true;
   }
}