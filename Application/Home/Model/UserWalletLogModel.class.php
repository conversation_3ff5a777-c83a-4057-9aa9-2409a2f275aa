<?php
/**
 * 
 * @authors yc ()
 * @date    2018-11-29 17:57:43
 * @version $Id$
 */
namespace Home\Model;

use Think\Model;

class UserWalletLogModel extends Model {

    /**
     * 创建订单主信息
     * @param  [type] $data [description]
     * @param  [int]  $prefix  单号前缀
     * @return [type]       [description]
     */
    public function createOrder($data, $prefix = 2)
    {
        $add = array(
            'order_type' => 1,
            'status' => 1,
            'create_time' => time(),
        );
        if (!isset($data['wallet_sn'])) {
            $data['wallet_sn'] = $this->findSn($prefix);
        }
        $add = array_merge($add, $data);
        $order_id = $this->add($add);
        return $order_id;
    }

    /**
     * 从日志统计用户余额
     * @param  [type] $user_id [description]
     * @param  string $where   [description]
     * @return [type]          [description]
     */
    public function getUserBalance($user_id, $where = array())
    {
        $map = array(
            'user_id' => $user_id,
        );
        if (is_string($where)) {
            $map['log_type'] = array('in', C($where));
        } else {
            $map = array_merge($map, $where);
        }
        $datas = $this->where($map)->sum('amount');
        return $datas;
    }

    /**
     * 从日志统计冻结余额
     * @param  [type] $user_id [description]
     * @param  string $where   [description]
     * @return [type]          [description]
     */
    public function getUserFreezeBalance($user_id, $where = array())
    {
        $map = array(
            'user_id' => $user_id,
        );
        if (is_string($where)) {
            $map['log_type'] = array('in', C($where));
        } else {
            $map = array_merge($map, $where);
        }
        $datas = $this->where($map)->sum('freeze_amount');
        return $datas;
    }

    /**
     * 合计某类型的单据的金额
     * @return [type] [description]
     */
    public function sumRelevanceAmount($relevance_id, $relevance_sn, $log_type = '')
    {
        $map = array(
            'relevance_id' => $relevance_id,
            'relevance_sn' => $relevance_sn,
            'log_type' => array('in', $log_type),
        );
        $data = $this->where($map)->sum('amount');
        return $data;
    }

    /**
     * 获取列表
     * @param  string $where [description]
     * @param  string $field [description]
     * @return [type]        [description]
     */
    public function getList($where = '', $field = '*')
    {
        $datas = $this->where($where)->field($field)->select();
        return $datas;
    }

    /**
     * 获取日志里用户列表
     * @param  string $where [description]
     * @return [type]        [description]
     */
    public function getGroupUser($where = '', $p = '')
    {
        limit_page($this, $p);
        $datas = $this->where($where)->group('user_id')->getField('user_id', true);
        return $datas;
    }

    /**
     * 获取单据信息
     * @param  [type] $wallet_id [description]
     * @param  string $field     [description]
     * @return [type]            [description]
     */
    public function getInfo($wallet_id = '', $wallet_sn = '', $field = '*')
    {
        if (!empty($wallet_id)) {
            $map['wallet_id'] = $wallet_id;
        } elseif (!empty($wallet_sn)) {
            $map['wallet_sn'] = $wallet_sn;
        } else {
            return false;
        }
        $data = $this->field($field)->find($wallet_id);
        return $data;
    }

    /**
     * 获取用户充值提现支付日志列表
     * @param  [type] $user_id [description]
     * @param  string $where   [description]
     * @param  string $page    [description]
     * @param  string $order   [description]
     * @return [type]          [description]
     */
    public function getUserList($user_id, $where = '', $page = '', $order = 'create_time DESC')
    {
        $map = array(
            'user_id' => $user_id,
        );
        is_array($where) && $map = array_merge($map, $where);
        limit_page($this, $page);
        $datas = $this->where($map)->order($order)
                        ->field('wallet_log_id, log_type, user_id, relevance_id, relevance_sn, serial_number, pay_name, amount, user_balance, freeze_amount, bank_name, bank_account, create_time, remark')
                        ->select();
        return $datas;
    }

    /**
     * 获取列表数
     * @param  [type] $user_id [description]
     * @param  string $where   [description]
     * @return [type]          [description]
     */
    public function getUserCount($user_id, $where = '')
    {
        $map = array(
            'user_id' => $user_id,
        );
        is_array($where) && $map = array_merge($map, $where);
        $datas = intval($this->where($map)->count());
        return $datas;
    }

    /**
     * 根据条件获取一条数据
     * @param  [type] $where [description]
     * @param  string $field [description]
     * @return [type]        [description]
     */
    public function getInfoWhere($where, $field = '*')
    {
        $data = $this->where($where)->field($field)->find();
        return $data;
    }
}