<?php
namespace Home\Model;

use Think\Model;

class UserInfoModel extends Model {

    public function saveUserInfo($user_id, $info)
    {
        $map = array(
            'user_id' => $user_id,
        );
        $res = $this->where($map)->save($info);
        return $res;
    }

    /**
     * 获取有效钱包用户
     * @return [type] [description]
     */
    public function getWalletUser($p = '')
    {
        $map = array(
            'wallet_status' => 1,
        );
        limit_page($this, $p);
        $data = $this->where($map)->getField('user_id', true);
        return $data;
    }

    /**
     * 获取用户附加表信息
     * @param  [type] $user_id [description]
     * @param  string $field   [description]
     * @return [type]          [description]
     */
    public function getUserInfo($user_id, $field = '*')
    {
        $map = array(
            'user_id' => $user_id
        );
        $data = $this->where($map)->field($field)->find();
        return $data;
    }

    /**
     * 获取安全手机号是否已存在
     * @param  [type]  $mobile  [description]
     * @param  integer $user_id [description]
     * @return [type]           [description]
     */
    public function getOnlySafeMobile($mobile)
    {
        $map = array(
            'safe_mobile' => $mobile,
        );
        $data = $this->where($map)->getField('ui_id');
        return $data;
    }


    public function sumNotRechargeUsage()
    {
        $map = array(
            'wallet_not_recharge' => array('gt', 0),
        );
        $sum = floatval($this->where($map)->sum('wallet_not_recharge - wallet_freeze_not_recharge'));
        return $sum;
    }
}