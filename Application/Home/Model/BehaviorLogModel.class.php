<?php

namespace Home\Model;

use Think\Model;

class BehaviorLogModel extends Model
{

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('BEHAVIOR_DB_CONFIG');
    }

    //根据类型获取记录
    public function getBehaviorLogList($userId, $type, $pageSize, $page)
    {
        $offset = ($page - 1) * $pageSize;
        return $this->field([
            'behavior_id',
            'user_id',
            'behavior',
            'param',
            'create_time',
        ])->where([
            'user_id' => $userId,
            'behavior' => $type,
        ])->order('behavior_id desc')->limit($offset, $pageSize)->select();
    }

    public function transformViewGoodsBehavior($logList)
    {
        //先去获取所有goods_id
        $goodsIdList = [];
        foreach ($logList as $key => $log) {
            $goodsId = array_get(\GuzzleHttp\json_decode($log['param'], true), 'goods_id');
            $logList[$key]['goods_id'] = $goodsId;
            $goodsIdList[] = $goodsId;
        }
        $params = implode(',', $goodsIdList);
        //通过基石获取商品信息
        $goodsServerUrl = GOODS_DOMAIN . '/synchronization';
        $goodsList = post_curl($goodsServerUrl, ['goods_id' => $params]);
        $goodsList = $goodsList ? \GuzzleHttp\json_decode($goodsList, true) : [];
        $goodsList = array_get($goodsList, 'data', []);
        //然后请求商品服务
        foreach ($logList as $key => &$log) {
            $log['create_time'] = date("m月d日", $log['create_time']);
            if (array_get($goodsList, $log['goods_id'], '')) {
                $log = array_merge($log, array_get($goodsList, $log['goods_id'], ''));
            }
            if (!empty($log['ladder_price'])) {
                foreach ($log['ladder_price'] as $k => $price) {
                    if (!empty($price['purchases'])) {
                        $log['ladder_price'][$k]['purchases'] = numberToHtml($price['purchases']);
                    }
                }
            }
            if (!empty($log['stock'])) {
                $log['stock'] = numberToHtml($log['stock']);
            }
            if (!empty($log['moq'])) {
                $log['moq'] = numberToHtml($log['moq']);
            }
            if (!empty($log['mpq'])) {
                $log['mpq'] = numberToHtml($log['mpq']);
            }
        }
        unset($log);
        return $logList;
    }
}