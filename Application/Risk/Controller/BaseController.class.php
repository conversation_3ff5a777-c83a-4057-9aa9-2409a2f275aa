<?php
namespace Risk\Controller;
use \Common\Controller\BaseController as Controller;
use Illuminate\Container\Container;

class BaseController extends Controller
{
    public $client =  null;
    public $container = null;
    public static $DB;
    public function _initialize()
    {
        parent::_initialize();

//        require "./vendor/autoload.php";
        $container = new Container();
        //普通绑定
//        $container->bind('\Pcb\Model\LARAVELDB',null);
        //单例绑定
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
            return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');

    }
}