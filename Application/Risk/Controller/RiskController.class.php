<?php
namespace Risk\Controller;

use Risk\Controller\BaseController;
use Risk\Model\CreditModel;
use Illuminate\Database\Capsule\Manager as DB;

class RiskController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), array('test','create_credit_apply','get_credit_apply','get_company_name'))) {
            // 检查登录
            if (in_array(strtolower(ACTION_NAME), array('test','create_credit_apply','get_credit_apply','get_company_name'))) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    public function test() 
    {
        $uid = cookie();
        p($uid);

    }


    /**
     * 创建用户信用申请
     */
    public function create_credit_apply(){
        try{
            $creditApplyId = (new CreditModel())->createCreditApply(I('get.'));
            return $this->apiReturn(0, '', ["credit_apply_id"=>$creditApplyId]);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", $e->getMessage());
        }
    }

    /**
     * 获取用户信用申请
     */
    public function get_credit_apply(){
        try{
            $creditApplyInfo = (new CreditModel())->getCreditApplyInfo();
            return $this->apiReturn(0, '', ["credit_apply_info"=>$creditApplyInfo]);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", $e->getMessage());
        }
    }

    /**
     * 获取公司名称
     */
    public function get_company_name(){
        try{
            $creditApplyInfo = (new CreditModel())->getCompanyName();
            return $this->apiReturn(0, '', ["com_name_arr"=>$creditApplyInfo]);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "获取数据失败");
        }
    }


}
