<?php
/**
 * Created by 2019/11/6.
 * User: Joneq
 * Info: 2019/11/6
 * Time: 上午11:16
 */

namespace Risk\Model;

use Pcb\Model\BaseModelLaravel;
class UserCompanyModel extends BaseModelLaravel
{
    protected $connection="LIEXIN";
    protected $table = 'user_company';
    protected $primaryKey = 'com_id';
    protected $guarded = ['com_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
}
