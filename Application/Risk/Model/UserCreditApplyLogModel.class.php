<?php
/**
 * Created by 2019/11/6.
 * User: Joneq
 * Info: 2019/11/6
 * Time: 下午3:21
 */

namespace Risk\Model;

use Pcb\Model\BaseModelLaravel;
class UserCreditApplyLogModel extends BaseModelLaravel
{
    protected $connection="LIEXIN";
    protected $table = 'user_credit_apply_log';
    protected $primaryKey = 'log_id';
    protected $guarded = ['log_id'];
    const CREATED_AT = 'create_time';
    public $timestamps = true;
}