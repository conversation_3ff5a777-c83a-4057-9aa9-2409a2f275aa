<?php
namespace Risk\Model;
class CreditModel
{

    //创建信用数据
    public function createCreditApply($postData)
    {
        $uid = cookie('uid');
        $checkEmpty = ['com_name','com_telphone','legal_person','tax_identify_no','pf','msg_code'];
        foreach ($checkEmpty as $key){
            if (!isset($postData[$key]) || empty($postData[$key])){
                throw new \Exception($key.' is null');
            }
        }

        $account = get_inte_mobile($postData['com_telphone'],'0086');
        
        $code = session_sms($account.'.1');

        if ($code !== pwdhash($postData['msg_code'], C('SMS_SALT'))) {
            throw new \Exception('短信验证码错误，请重新获取');
        }

        if (UserCreditApplyModel::where('user_id',$uid)->whereIn('status', [1,2])->first()){
            throw new \Exception('该用户申请已经存在');
        }

        $insertData['com_name'] = $postData['com_name'];
        $insertData['com_telphone'] = $postData['com_telphone'];
        $insertData['legal_person'] = $postData['legal_person'];
        $insertData['tax_identify_no'] = $postData['tax_identify_no'];
        $insertData['pf'] = $postData['pf'];
        $insertData['create_time'] = time();
        $insertData['user_id'] = $uid;
        $insertId = UserCreditApplyModel::insertGetId($insertData);
        UserCreditApplyLogModel::insertGetId(['credit_apply_id'=>$insertId,'operator_id'=>$uid,'operator_type'=>1,'event'=>'申请成功','ip'=>$_SERVER["REMOTE_ADDR"],'create_time'=>time()]);
        //该公司没有名称就添加
        if (empty(UserCompanyModel::where('user_id',$uid)->value('user_id'))){
            UserCompanyModel::insertGetId(['user_id'=>$uid,'com_name'=>$postData['com_name']]);
        }
        return $insertId;
    }

    //获取信用数据
    public function getCreditApplyInfo()
    {
        if (empty($data = UserCreditApplyModel::where('user_id',cookie('uid'))->whereIn('status', [1,2])->orderBy('create_time','desc')->first())){
            return null;
        }
        return $data;
    }

    public function getCompanyName()
    {
        if (empty($comName = S_company(cookie('uid')))){
            if (empty($comName = UserCompanyModel::where('user_id',cookie('uid'))->value('com_name'))){
                return [];
            }
        }else{
            $comName = array_get($comName,'com_name','');
        }
        return [$comName];
    }

}