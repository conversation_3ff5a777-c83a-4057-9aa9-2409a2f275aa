<?php
/**
 * 过滤文件，发布请手动补充修改内容
 */
return array(
    //mongo数据过期时间
    'MONGO_INFO_OVERTIME' => array(
        'chip1stop'     => 86400*500,//12
        'element14'     => 86400*2,//13
        'future'        => 86400*2,//14
        'digikey'       => 86400*500,
        'verical'       => 86400*500,
        'alliedelec'    => 86400*500,
        'rs'            => 86400*500,
        'avnet'         => 86400*500,
        'arrow'         => 86400*500,
        'online'        => 86400*500,
        'rutronik24'    => 86400*500,
        'mouser'        => 86400*1,//25
        'rochester'     => 86400*500,
        'tti'           => 86400*500,
        'company'       => 86400*500,
        'tme'           => 86400*500,
        'peigenesis'    => 86400*2,//29
    ),

);