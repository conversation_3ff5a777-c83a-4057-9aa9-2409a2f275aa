<?php
namespace Goods\Controller;

class BaseController extends \Common\Controller\BaseController
{
    /**
     * 获取用户ID
     * @return [type] [description]
     */
    public function getUid()
    {
        $uid = 0;
        if ($this->auth()) {
            $uid = I('request.uid', 0, 'intval');
        }

        if (!$uid || empty($uid)) {
            $uid = cookie('uid');
        }

        return $uid ? $uid : 0;
    }

    static public function isExistsHeader($name=""){
        $header = getallheaders();
        if($name && isset($header[$name])){
            return $header[$name];
        }
        return false;
    }

    protected function getStoneGoods($goods_id, $power = array(), $new = true)
    {
        if ($new) {
            $default = array(
                'newCustomer' => false,
                'member' => false,
            );
            $power = array_intersect_key($power, $default);
            $power['member'] = $power['member'] !== 'false' && boolval($power['member']) ? 'true' : 'false';//参与会员价
            $power['newCustomer'] = $power['newCustomer'] !== 'false' && boolval($power['newCustomer']) ? 'true' : 'false';//参与新客价
            try{
                $user_id = $this->getUid();
                if($user_id && $user_id > 0){
                    $power["user_id"] = $user_id;
                    $userInfo = S_user($user_id);
                    $power["mobile"] = isset($userInfo['mobile']) ? $userInfo['mobile'] : '';
                    $power["email"] = isset($userInfo['email']) ? $userInfo['email'] : '';
                }
            }catch(\Exception $e){}

            $data['goods_id'] = $goods_id;
            $data['power'] = $power;
            $url = self::getStoneUrl($goods_id, $new).'/synchronization';
        } else {
            $data['sku_id'] = $goods_id;
            $url = self::getStoneUrl($goods_id, $new).'/webapi/goods_details';
        }
//        dump($url);
        $res = post_curl($url, $data);
        if (!empty($res) && is_string($res)) {
            $res = json_decode($res, true);

            if (I("pf") == 2){ //倒叙价格
                foreach ($res["data"] as $k=>&$v){
                    #梯度倒叙
                    $ladder_price = $v["ladder_price"];
                    usort($ladder_price, function ($a, $b) {
                        return $a['purchases'] < $b['purchases'];
                    });
                    $v["ladder_price"] = $ladder_price;
                }
            }
        }
        return $res;
    }

    /**
     * 获取基石具体数量价格
     * @param  [type]  $goods_id [description]
     * @param  [type]  $num      [description]
     * @param  integer $currency [description]
     * @param  array   $power    [description]
     * @return [type]            [description]
     */
    protected function getStoneLadderGoods($goods_id, $num, $currency = 1, $power = array())
    {
        $default = array(
            'newCustomer' => false,
            "assemble"=>1,
            'member' => false,
            "mobile"=>"",
            "email"=>"",
            "user_id"=>0,
            "invoice"=>"",//增值税普通发票公司名字,活动价时必须，否则可能导致用户无法享受活动价
            "special_invoice"=>"",//增值税专用发票公司名字,活动价时需要，否则可能导致用户无法享受活动价
            "verify_blacklist"=>false,//是否验证黑名单，用于折扣活动提交订单页面与后台下单
        );
        $power = array_intersect_key($power, $default);
        $power['member'] = $power['member'] !== 'false' && boolval($power['member']) ? 'true' : 'false';//参与会员价
        $power['newCustomer'] = $power['newCustomer'] !== 'false' && boolval($power['newCustomer']) ? 'true' : 'false';//参与新客价
        $data['goods_id'] = $goods_id;
        $data['num'] = $num;
        $data['currency'] = $currency;
        $data['power'] = $power;
        $url = self::getStoneUrl($goods_id).'/ladderprice';
//        $url = "http://*************:60014".'/ladderprice';
        $res = post_curl($url, $data);
        if (!empty($res) && is_string($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 基石获取品牌信息
     * @param  [type]  $brand_id   [description]
     * @param  integer $brand_type 1联营(默认) 2自营
     * @return [type]              [description]
     */
    protected function getStoneBrand($brand_id, $brand_type = 1)
    {
        $check['brand_id'] = $brand_id;
        $check['BrandType'] = $brand_type;
        $res = post_curl(STONE_DOMAIN.'/webapi/Handle_brand', $check);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取基石地址 根据类型判断
     * @param  [type] $goods_id [description]
     * @return [type]           [description]
     *
     */
    static public function getStoneUrl($goods_id, $new = true)
    {
        //判断是否来自购物车更新脚本
        $fromUpderCart  = static::isExistsHeader("Ichunt-Action-Type");
        $second_domain = explode('.', $_SERVER['SERVER_NAME'], 2);
        if ($second_domain[0] == 'szapi') {
            if (is_array($goods_id)) {
                $goods_id = current($goods_id);
            } elseif (strpos($goods_id, ',') !== false) {
                $goods_id = substr($goods_id, 0, strpos($goods_id, ','));
            }
            if (strlen($goods_id) < 11) {//自营访问SZ基石
                return $new ? SZGOODS_DOMAIN : SZ_STONE_DOMAIN;
            } else {//联营访问正式基石
                return $new ? NEW_GOODS_DOMAIN : STONE_DOMAIN;
            }
        } else {
           if($fromUpderCart == "ichunt-update-cart"){
                //如果请求来自于python 更新购物车脚本  则去其它服务器获取商品信息
                //为了解决商品服务性能太差的问题
                //有一台专门为购物车更新 请求的商品服务器
                return $new ? UPDATECART_GOODS_DOMAIN : STONE_DOMAIN;
            }else{
                return $new ? NEW_GOODS_DOMAIN : STONE_DOMAIN;
            }

        }
    }

    /**
     * 获取用户参与活动资格
     * @param  [type] $user_id [description]
     * @return [type]          [description]
     */
    protected function getUserAc($user_id)
    {
        static $user;
        if (!isset($user[$user_id])) {
            $data['uid'] = $user_id;
            $data = array_merge($data, authkey());
            $res = post_curl(API_DOMAIN. '/cart/usercanac', $data);
            $result = [];
            if (!empty($res)) {
                $res = json_decode($res, true);
                if ($res['err_code'] == 0) {
                    $result = $user[$user_id] = $res['data'];
                }
            }
        } else {
            $result = $user[$user_id];
        }
        return $result;
    }


}