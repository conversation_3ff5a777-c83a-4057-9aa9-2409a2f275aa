<?php

namespace Goods\Controller;

use Goods\Controller\BaseController;

class GoodsInfoController extends BaseController
{
    private $esUrl = ES_DOMAIN . '/search/es/searchSku';
    private $goodsUrl = NEW_GOODS_DOMAIN . '/synchronization';
    private $goods_id = [];

    public function _initialize()
    {
        parent::_initialize();

        // if (!in_array(strtolower(ACTION_NAME), array('info', 'transstatus'))) {
        //     //检查登录
        //     $res = $this->checkLogin();
        //     if ($res['err_code'] != 0) {
        //         return $this->apiReturn($res['err_code'], $res['err_msg']);
        //     }
        // }
//        if (!$this->auth() && in_array(strtolower(ACTION_NAME), array('info', 'infos'))) {
//            die();
//        }
        $this->goods_id = I('request.goods_id', '');
    }

    public function HdGoodsInfo()
    {
        $Auth = checkSignApi();
        if ($Auth !== true) {
            return $this->apiReturn(1, '安全验证不通过，请刷新页面重试');
        }
        if (empty($this->goods_id)) {
            $info = $this->Search();
        }
        if (empty($this->goods_id)) {
            return $this->apiReturn(10001, '没有查找到数据');
        }
        $data = $this->HdGoodsInfoToService();
        if (!$data) {
            return $this->apiReturn(10002, '没有查找到数据');
            //test
        }

        foreach ($data as $k => &$v) {//混淆加密
            if (!empty($v['ladder_price'])) {
                foreach ($v['ladder_price'] as $k1 => $v1) {
                    if (!empty($v1['purchases'])) {
                        $v['ladder_price'][$k1]['purchases'] = numberToHtml($v1['purchases']);
                    }
                }
            }
            if (!empty($v['stock'])) {
                $v['stock'] = numberToHtml($v['stock']);
            }
            if (!empty($v['moq'])) {
                $v['moq'] = numberToHtml($v['moq']);
            }
            if (!empty($v['mpq'])) {
                $v['mpq'] = numberToHtml($v['mpq']);
            }

            //todo 2023.2.1 标准品牌
            if (@$v["standard_brand"]["standard_brand_id"] > 0) {
                $v["brand_id"] = $v["standard_brand"]["standard_brand_id"];
                $v["brand_name"] = $v["standard_brand"]["brand_name"];
            }


            #批次判断
            $s = json_decode($v["batch_sn"], true);
            if (is_array($s)){
                $v["batch_sn"] = $s;
            }elseif($v["batch_sn"] != ""){
                $v["batch_sn"] =  strpos($v["batch_sn"],"+") !== false ? $v["batch_sn"] : (string)$v["batch_sn"]."+";
            }
        }
        $info = [
            'goods_list' => $data,
            'total'      => $info['total']
        ];
        return $this->apiReturn(0, 'ok', $info);
    }

    /*


    */
    public function getBrandInfo()
    {
        $redis = $this->fs_redis = fs_redis_init();

        $select_brand = I('post.select_brand');

        if (!empty($select_brand)) {
            //获取品牌信息
            $brand = $redis->Hmget('brand_info', $select_brand);

            $flag = 0;

            foreach ($brand as $key => $value) {
                //只要有一个不为false
                if ($value != false) {
                    $flag = 1;
                }
            }


            if ($flag == 0) {
                $brand = $this->getBrandDBinfo();
            }
        }

        foreach ($brand as $key => $value) {
            if ($value != false) {
                $brand_info[] = json_decode($value);
            }
        }

        return $this->apiReturn(0, 'ok', $brand_info);
    }


    /*
    从数据库中获取brand信息
*/
    public function getBrandDBinfo()
    {
        $SupplierModel = D('Common/ZyGoods');
        $redis = $this->fs_redis = fs_redis_init();

        $select_brand = I('post.select_brand');

        $where = array('status' => 1);


        $SupplierInfo = $SupplierModel->table('lie_brand')->where($where)->field($field)->limit($limit)->select();

        //存入redis
        foreach ($SupplierInfo as $key => $value) {
            $redis->HSET('brand_info', $value['brand_id'], json_encode($value));
        }

        return $redis->Hmget('brand_info', $select_brand);
    }

    private function Search()
    {
        $map = I('request.');
        foreach ($map as $k => $v) {
            if (empty($v)) {
                unset($map[$k]);
            }
        }
        if (empty($map['p'])) {
            $map['p'] = 1;
        }
        if (empty($map['offset'])) {
            $map['offset'] = 10;
        }
        if ($map['p'] > 1000) {
            $map['p'] = rand(1, 1000);
        }//最多1000页
        if ($map['offset'] > 100) {
            $map['offset'] = 100;
        }//最大支持100条每页

        if (empty($map['ignore_status'])) {
            $map['status/condition'] = 1;
        }

        if (!empty($map['sku_activity_id'])) {
            $map['activity_ids/eq'] = $map['sku_activity_id'];
            unset($map['sku_activity_id']);
            //有活动id的,不需要判断状态
            unset($map['status/condition']);
        } else {
//            $map['status/condition'] = 1;
        }
//        var_dump(\GuzzleHttp\json_encode($map));
        $result = json_decode(post_curl($this->esUrl, $map), true);
        if (!$result || !isset($result['error_code']) || $result['error_code'] !== 0 || empty($result['data']['goods_id'])) {
            return false;
        }
        $this->goods_id = $result['data']['goods_id'];
        $data = [
            'goods_id' => $result['data']['goods_id'],
            'total'    => $result['data']['total']
        ];
        return $data;
    }

    private function HdGoodsInfoToService()
    {
        $data['goods_id'] = $this->goods_id;
        $result = json_decode(post_curl($this->goodsUrl, $data), true);
        if (!$result || !isset($result['errcode']) || $result['errcode'] !== 0 || empty($result['data'])) {
            return false;
        }
        return $result['data'];
    }

    //获取样片列表
    public function getSampleList()
    {
        $post = I('');
        $Url = GOODS_DOMAIN . '/self/sample/list';
        $result = json_decode(post_curl($Url, $post), true);
        if (empty($result['data'])) {
            return $this->apiReturn(0, 'ok', ['data' => '', 'total' => 0]);
        }
        return $this->apiReturn(0, 'ok', $result['data']);
    }

    public function getSampleClassList()
    {
        $Url = GOODS_DOMAIN . '/self/sample/class/list';
        $result = json_decode(post_curl($Url, []), true);
        if (empty($result['data'])) {
            return $this->apiReturn(0, 'ok', ['data' => '']);
        }
        return $this->apiReturn(0, 'ok', $result['data']);
    }

    public function getSkuMarketPrice()
    {
        $collert = I('param.');
        if (!$collert['module_name']) {
            return $this->apiReturn(-1, '参数缺少模块名称', '');
        }
        $data['data'] = urlencode(json_encode($collert));
        $data['timestamp'] = time();
        $Token = WMSencryption($data);
        $Url = CUBE_DOMAIN . '/WebApi/ApiGetSkuMarketPrice?Token=' . $Token . '&module_name=' . $collert['module_name'];
        $data['data'] = urlencode(json_encode($collert));
        $data['timestamp'] = time();
        $result = json_decode(post_curl($Url, $data), true);
        if (empty($result['data'])) {
            return $this->apiReturn(0, 'ok', '');
        }

        return $this->apiReturn(0, 'ok', ['market_price' => $result['data']]);
    }

    public function getCubeActivityGoodsList()
    {
        $data = I("request.");
        if (empty($data['page'])){
            $data = file_get_contents("php://input");
            $data = json_decode($data, true);
        }
        $Url = CUBE_NEW_DOMAIN . '/sync/activity/getGoodsList';
        $body = curlPostJson($Url, $data);
        if (isset($data['flag']) && $data['flag'] == 100) {
            echo $body;
            die;
        }

        $result = json_decode($body);
        if (empty($result->data)) {
            return $this->apiReturn(0, 'ok', '');
        }
        return $this->apiReturn(0, 'ok', $result->data);
    }


}
