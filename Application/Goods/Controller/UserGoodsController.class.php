<?php

namespace Goods\Controller;

use Goods\Controller\BaseController;
use Goods\Service\UserGoodsGroupService;
use Goods\Service\UserGoodsService;
use Think\Upload;

class UserGoodsController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();

        if (!in_array(strtolower(ACTION_NAME), [])) {
            //检查登录
            $res = $this->checkLogin();
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    //获取用户物料列表
    public function getUserGoodsList()
    {
        if (!I('group_id')) {
            return $this->apiReturn(-1, '组别ID不能为空');
        }
        $userId = cookie('uid');
        $map = [
            'user_id' => $userId,
            'group_id' => I('group_id'),
            'goods_name' => I('goods_name'),
            'custom_sn' => I('custom_sn'),
            'is_buy' => I('is_buy'),
            'page' => I('page', 1),
            'limit' => I('limit', 10),
        ];
        $data = (new UserGoodsService())->getUserGoodsList($map);
        return $this->apiReturn(0, 'ok', $data);
    }

    //新增用户物料
    public function addUserGoods()
    {
        $goodsName = I('goods_name');
        $goodsNumber = I('goods_number');
        $groupId = I('group_id');
        $currency = I('currency', 1);
        $map = [
            'goods_name' => $goodsName,
            'goods_number' => $goodsNumber,
            'group_id' => $groupId,
            'currency' => $currency,
        ];
        $exists = (new UserGoodsService())->checkUserGoodsExists($groupId, cookie('uid'), $goodsName);
        if ($exists) {
            return $this->apiReturn(-1, '对应的物料已经存在,请重新设置');
        }
        $goods = (new UserGoodsService())->matchUserGoods($map);
        if (!$goods) {
            return $this->apiReturn(-1, '无法查询到相应的物料');
        }
        $result = (new UserGoodsService())->addUserGoods($goods);
        if (!$result) {
            return $this->apiReturn(-1, '新增物料失败');
        }
        return $this->apiReturn(0, '新增物料成功');
    }

    //新增用户物料(根据goods_id)
    public function addUserGoodsByGoodsId()
    {
        $goodsId = I('goods_id');
        $groupId = I('group_id');
        $groupName = I('group_name');
        $goodsNumber = I('goods_number');
        if (empty($goodsId) && empty($groupName)) {
            return $this->apiReturn(-1, '分组id和名称至少传一个');
        }
        if (empty($goodsId)) {
            return $this->apiReturn(-1, 'goods_id不能为空');
        }
        if (empty($goodsNumber)) {
            return $this->apiReturn(-1, '商品数量不能为空');
        }
        if (empty($groupId)) {
            $data = [
                'group_name' => I('group_name'),
                'user_id' => cookie('uid'),
                'create_time' => time(),
            ];
            if ((new UserGoodsGroupService())->checkGroupNameExist(cookie('uid'), $groupName)) {
                return $this->apiReturn(-1, '该分组名已经存在,请重新设置');
            }
            $groupId = (new UserGoodsGroupService())->addUserGoodsGroup($data);
        }
        $exists = (new UserGoodsService())->checkUserGoodsExistsByGoodsId($groupId, cookie('uid'), $goodsId);
        if ($exists) {
            return $this->apiReturn(-1, '对应的物料已经存在,请重新设置');
        }
        try {
            $goods = (new UserGoodsService())->transformMatchGoodsByGoodsId($goodsId, $groupId, $goodsNumber);
        } catch (\Exception $exception) {
            return $this->apiReturn(0, $exception->getMessage());
        }
        if (!$goods) {
            return $this->apiReturn(-1, '无法查询到相应的物料');
        }
        $result = (new UserGoodsService())->addUserGoods($goods);
        if (!$result) {
            return $this->apiReturn(-1, '新增物料失败');
        }
        return $this->apiReturn(0, '新增物料成功', $groupId);
    }


    //修改用户物料
    public function updateUserGoods()
    {
        $id = I('id');
        $data = [
            'goods_id' => I('goods_id'),
            'custom_sn' => I('custom_sn'),
            'goods_number' => I('goods_number'),
            'update_time' => time(),
        ];
        try {
            $result = (new UserGoodsService())->updateUserGoods($id, $data);
        } catch (\Exception $exception) {
            return $this->apiReturn(-1, $exception->getMessage());
        }
        if (!$result) {
            return $this->apiReturn(-1, '更新物料失败');
        }
        return $this->apiReturn(0, '修改物料成功');
    }

    //删除用户物料
    public function deleteUserGoods()
    {
        $ids = I('id');
        if (empty($ids)) {
            return $this->apiReturn(-1, '需要删除的ID不能为空');
        }
        $ids = explode(',', $ids);
        $result = (new UserGoodsService())->batchDeleteUserGoods($ids);
        if (!$result) {
            return $this->apiReturn(-1, '删除物料失败');
        }
        return $this->apiReturn(0, '删除物料成功');
    }

    public function importUserGoods()
    {
        $groupId = I('group_id');
        vendor("PHPExcel.PHPExcel"); //引入PHPExcel扩展类库
        header("Content-type:text/html;charset=utf-8");
        $filename = $_FILES["file"]["tmp_name"];
        $objPHPExcel = \PHPExcel_IOFactory::load($filename);
        $sheet = $objPHPExcel->getSheet(0);
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();
        $rowData = [];
        for ($row = 1; $row <= $highestRow; $row++) {
            $rowValue = [];
            for ($col = 'A'; $col <= $highestColumn; $col++) {
                $value = $sheet->getCell($col . $row)->getValue();
                $rowValue[] = $value;
            }
            $rowData[] = $rowValue;
        }
        if (count($rowData) > 100) {
            return $this->apiReturn(-1, '需要匹配的物料不能超过100个');
        }
        $mappingData = [];
        foreach ($rowData as $key => $data) {
            if ($key == 0) {
                continue;
            }
            $goodsNumber = sprintf('%d', $data[2]);
            if (!$goodsNumber) {
                return $this->apiReturn(-1, '导入失败,第' . ($key + 1) . '行,数量必填');
            }
            $mappingData[] = [
                'custom_sn' => sprintf('%s', $data[0]),
                'goods_name' => sprintf('%s', $data[1]),
                'goods_number' => sprintf('%d', $data[2]),
                'currency' => 1,
                'group_id' => $groupId,
            ];
        }
        //然后去匹配匹配数据
        $goodsList = (new UserGoodsService())->batchMatchUserGoods($mappingData);
        //覆盖已经存在的型号
        $batchInsertGoodsList = [];
        foreach ($goodsList as $goods) {
            if ((new UserGoodsService())->checkUserGoodsExists($goods['group_id'], $goods['user_id'], $goods['goods_name'])) {
                D('Goods/UserGoods')->where(['group_id' => $goods['group_id'], 'user_id' => $goods['user_id'], 'goods_name' => $goods['goods_name']])->save($goods);
            } else {
                $batchInsertGoodsList[] = $goods;
            }
        }
        if ($batchInsertGoodsList) {
            D('Goods/UserGoods')->addAll($batchInsertGoodsList);
        }

        return $this->apiReturn(0, '导入成功');
    }

    public function reMatchUserGoods()
    {
        $groupId = I('group_id');
        $goodsList = (new UserGoodsService())->getUserGoodsListByGroupId($groupId);
        if (empty($goodsList)) {
            return $this->apiReturn(-1, '分组中暂无物料，请先添加物料');
        }
        $matchDataList = [];
        foreach ($goodsList as $goods) {
            $matchDataList[] = [
                'goods_id' => $goods['goods_id'],
                'goods_name' => $goods['goods_name'],
                'goods_number' => $goods['goods_number'],
                'custom_sn' => $goods['custom_sn'],
                'currency' => $goods['currency'],
                'group_id' => $groupId,
            ];
        }
        //然后去匹配匹配数据
        $goodsList = (new UserGoodsService())->batchMatchUserGoods($matchDataList);

        foreach ($goodsList as $goods) {
            D('Goods/UserGoods')->where(['goods_id' => $goods['goods_id'], 'group_id' => $goods['group_id']])->save($goods);
        }

        return $this->apiReturn(0, '重新匹配成功');
    }

    //导出用户物料
    public function exportUserGoods()
    {
        $groupId = I('group_id');
        $check = (new UserGoodsService())->checkCanExportUserGoods($groupId);
        if ($check !== true) {
            return $this->apiReturn(-1, $check);
        }
        (new UserGoodsService())->exportUserGoodsList($groupId);
    }

    //修改币种
    public function updateUserGoodsCurrency()
    {
        $id = I('id');
        if (empty($id)) {
            return $this->apiReturn(-1, 'ID不能为空');
        }
        $id = explode(',', $id);
        $userGoodsList = D('Goods/UserGoods')->where(['id' => ['in', $id]])->select();
        foreach ($userGoodsList as $userGoods) {
            $currency = $userGoods['currency'] == 1 ? 2 : 1;
            $map = [
                'goods_name' => $userGoods['goods_name'],
                'goods_number' => $userGoods['goods_number'],
                'group_id' => $userGoods['group_id'],
                'custom_sn' => $userGoods['custom_sn'],
                'currency' => $currency,
            ];
            $goods = (new UserGoodsService())->matchUserGoods($map);
            if (!$goods) {
                $goods = [
                    'goods_id' => 0,
                    'brand_id' => 0,
                    'brand_name' => 0,
                    'price' => 0,
                    'is_buy' => 0,
                ];
            }
            $goods['currency'] = $currency;
            $goods['update_time'] = time();
            $result = D('Goods/UserGoods')->where(['id' => $userGoods['id']])->save($goods);
            if (!$result) {
                return $this->apiReturn(-1, '修改币种失败');
            }
        }
        return $this->apiReturn(0, '修改币种成功');
    }
}
