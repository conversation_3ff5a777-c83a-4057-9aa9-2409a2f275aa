<?php

namespace Goods\Controller;

use Goods\Controller\BaseController;
use Goods\Service\UserGoodsGroupService;
use Goods\Service\UserGoodsService;

class UserGoodsGroupController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();

        if (!in_array(strtolower(ACTION_NAME), [])) {
            //检查登录
            $res = $this->checkLogin();
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    //获取分组列表
    public function getUserGoodsGroupList()
    {
        $userId = cookie('uid');
        $map = [
            'user_id' => $userId,
            'page' => I('page', 1),
            'limit' => I('limit', 10),
        ];
        $data = (new UserGoodsGroupService())->getUserGoodsGroupList($map);
        return $this->apiReturn(0, 'ok', $data);
    }

    //新建分组
    public function addUserGoodsGroup()
    {
        $data = [
            'group_name' => I('group_name'),
            'user_id' => cookie('uid'),
            'create_time' => time(),
        ];

        if ((new UserGoodsGroupService())->checkGroupNameExist(cookie('uid'), I('group_name'))) {
            return $this->apiReturn(-1, '该分组名已经存在,请重新设置');
        }
        $result = (new UserGoodsGroupService())->addUserGoodsGroup($data);
        if (!$result) {
            return $this->apiReturn(-1, '新增个人物料分组失败');
        }
        return $this->apiReturn(0, '新增分组成功', $result);
    }

    //修改分组
    public function updateUserGoodsGroup()
    {
        $id = I('id');
        $data = [
            'group_name' => I('group_name'),
            'user_id' => cookie('uid'),
            'update_time' => time(),
        ];
        $result = (new UserGoodsGroupService())->updateUserGoodsGroup($id, $data);
        if (!$result) {
            return $this->apiReturn(-1, '新增个人物料分组失败');
        }
        return $this->apiReturn(0, '更新分组名称成功');
    }
}
