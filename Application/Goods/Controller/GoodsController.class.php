<?php

namespace Goods\Controller;

use Goods\Controller\BaseController;

class GoodsController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();

        // if (!in_array(strtolower(ACTION_NAME), array('info', 'transstatus'))) {
        //     //检查登录
        //     $res = $this->checkLogin();
        //     if ($res['err_code'] != 0) {
        //         return $this->apiReturn($res['err_code'], $res['err_msg']);
        //     }
        // }
        if (!$this->auth() && in_array(strtolower(ACTION_NAME), array('info', 'infos', 'finalinfo'))) {
            die();
        }
    }


    /**
     * 获取指定商品应付阶梯信息
     * @return [type] [description]
     */
    public function finalInfo($id = 0, $num = 0, $currency = 1, $power = [])
    {
//        $goods_id = I('request.id', 0);
//        $num = I('request.num', 0, 'intval');
//        $currency = I('request.currency', 1, 'intval');
//        $power = I('request.power', array());
        $goods_id = $id;
        $num = intval($num);
        $currency = intval($currency);
        $power = $power;

        $goods = $this->getStoneLadderGoods($goods_id, $num, $currency, $power);
        if ($goods['errcode'] != 0) {
            return $this->apiReturn(80003, '商品已下架');
        }

        $goods['data']['goods_info'] = $this->format($goods['data']['goods_info']);
        $goods['data']['enough_stock'] = $num <= $goods['data']['goods_info']['goods_number'] ? true : false;//是否有足够库存支持
        return $this->apiReturn(0, '获取成功', $goods['data']);
    }

    /**
     * 批量获取信息
     * @return [type] [description]
     */
    public function infos($id = "", $power = [])
    {
        $goods_id = $id;

        if (is_array($goods_id)) {
            $ids = &$goods_id;
        } elseif (strpos($goods_id, ',')) {
            $ids = explode(',', $goods_id);
        } else {
            $ids[] = $goods_id;
        }

        $goods = $this->getStoneGoods($goods_id, $power);
        if ($goods['errcode'] != 0) {
            return $this->apiReturn(80003, '所有商品已下架');
        }
        $datas = array();
        foreach ($goods['data'] as $k => $v) {
            if ($v !== false) {
                $datas[$k] = $this->format($v);
            } else {
                $datas[$k] = $v;
            }
        }
        return $this->apiReturn(0, '获取成功', $datas);
    }

    /**
     * 根据ID获取商品信息（自动区分专卖联营）
     * 阶梯依次为 数量、美金、人民币
     * @return [type] [description]
     */
    public function info()
    {
        $goods_id = I('request.id', 0);
        $power = I('request.power', array());
        //通过基石获取商品信息
        $goods = $this->getStoneGoods($goods_id, $power);
        if ($goods['errcode'] != 0) {
            return $this->apiReturn(80003, '商品已下架');
        }
        $data = $this->format($goods['data'][$goods_id]);

        return $this->apiReturn(0, '获取成功', $data);
    }

    /**
     * 批量获取信息，价格已处理 (校验不通过auth 会进行混淆)
     * @return [type] [description]
     */
    public function details()
    {
        $uid = cookie('uid');
        $is_ac = $this->getUserAc($uid);
        global $_REQUEST;
        $_REQUEST['power'] = $is_ac;
        $goods = $this->infos();
        if ($goods['err_code'] != 0) {
            return $this->apiReturn(80003, '所有商品已下架');
        }
        foreach ($goods['data'] as $k => &$v) {
            if ($v !== false) {
                $data = $this->detail($v);
                $v = $data['data'];
            }
        }
        return $this->apiReturn(0, '获取成功', $goods['data']);
    }

    /**
     * 网站商详接口，价格已处理 (校验不通过auth 会进行混淆)
     * @return [type] [description]
     */
    public function detail($data = null)
    {
        $uid = $this->getUid();
        $is_ac = $this->getUserAc($uid);
        global $_REQUEST;
        $_REQUEST['power'] = $is_ac;
        if (is_null($data)) {
            $info = $this->info();
            if ($info['err_code'] != 0) {
                return $this->apiReturn($info['err_code'], $info['err_msg']);
            }
            $data = &$info['data'];
        }

        // $coeff = $this->getConf('order', 'PRICE_MULTI');

        // $supplier = getSupplier($data['supplier_id']);
        // $coeff = current($supplier['price_json']);

        //广告词
        $adwords = $this->adWord($data['goods_id']);
        $data['ad_words'] = $adwords['data'];

        //品牌图片
        $data['brand_logo'] = '';
        if (!empty($data['brand_id'])) {
            $goods_type = in_array($data['goods_type'], array(1, 2, 6)) ? 1 : 2;
            $brand = $this->getStoneBrand($data['brand_id'], $goods_type);
            if ($brand['err_code'] == 0) {
                $data['brand_logo'] = $brand['data']['brand_logo'];
            }
        }

        if (is_string($data['delivery_time'])) {
            list($delivery_time['1'], $delivery_time['2']) = explode('|', $data['delivery_time'], 2);
            if (!isset($delivery_time['2'])) {
                $delivery_time['2'] = $delivery_time['1'];
            }
            $data['delivery_time'] = $delivery_time;
        }

        //价格阶梯，判断活动价是否适用
        // $tiered = ladder_transform($data['tiered'], $data['supplier_id'], $data['goods_type'], $data['ratio'], $data['ac_type']);

        // $data['tiered'] = $tiered['tiered'];
        // $data['ac_type'] = $tiered['ac_type'];

        //交期
        foreach ($data['tiered'] as &$v) {
            $data['delivery_hk'] = $v['price_us'] > 0 ? 1 : 0;//是否能交货香港
            $data['delivery_cn'] = $v['price_cn'] > 0 ? 1 : 0;//是否能交货大陆
        }

        //标准品牌
        // $standInfo = getStardarBrand($data['goods_id']);
        // $data["brand_id"] = $standInfo["brand_id"];
        // $data["brand_name"] = $standInfo["brand_name"];

        foreach ($data["delivery_time"] as $a => &$c) {
            $c = deleteDay($c);
        }

        //混淆数据
        !$this->auth() && $data = dataConfuse($data);
        return $this->apiReturn(0, '获取成功', $data);
    }

    /**
     * 基石接口返回数据格式化成网站通用格式
     * @param  [type] $goods [description]
     * @return [type]        [description]
     */
    private function format($goods)
    {
        $ladder_price = array();
        $goods_type = C('FOOTSTONE_GOODS_TYPE.' . $goods['goods_type']);//商品类型 基石 映射
        if (in_array($goods_type, array(3, 4))) { //自营、寄售
            $change_place = 0;
            $data = array(
                'goods_id' => $goods['goods_id'],
                'goods_sn' => !empty($goods['goods_sn']) ? $goods['goods_sn'] : '',
                'goods_name' => $goods['goods_name'],
                'sku_name' => !empty($goods['sku_name']) ? $goods['sku_name'] : $goods['goods_name'],
                //自定义商品名
                'brand_id' => $goods['brand_id'],
                'brand_name' => !empty($goods['brand_name']) ? $goods['brand_name'] : '',
                //品牌名
                'goods_desc' => $goods['goods_brief'],
                //简介
                'supplier_id' => $goods['supplier_id'],
                //spu供应商表ID
                'supplier_name' => $goods['supplier_name'],
                'min_buy' => $goods['moq'],
                'min_mpq' => $goods['mpq'],
                'mpl' => !empty($goods['mpl']) ? $goods['mpl'] : 1,

                //倍数
                'encap' => $goods['encap'],
                //封装
                'class1_name' => $goods['class_id1_name'],
                //一级分类
                'class_id1' => $goods['class_id1'],
                //一级分类
                'class2_name' => $goods['class_id2_name'],
                //二级分类
                'class_id2' => $goods['class_id2'],
                //一级分类
                'goods_unit_name' => $goods['goods_unit_name'],
                //商品单位
                'packing_name' => $goods['packing_name'],
                //包装单位
                'mpq_unit_name' => $goods['mpq_unit_name'],
                'multiple' => $goods['multiple'],
                //圆盘单位
                'product_batch' => !empty($goods['batch_sn']) ? json_decode($goods['batch_sn'], true) : '',
                'goods_number' => $goods['stock'],
                'supplier_stock' => $goods['supplier_stock'],
                'tiered' => $goods['ladder_price'],
                'pdf' => isset($goods['pdf']) && !empty($goods['pdf']) ? $goods['pdf'] : '',
                'delivery_time' => $goods["cn_delivery_time"] ? $goods["cn_delivery_time"] : "现卖",
                'change_place' => $change_place,
                'goods_type' => $goods_type,
                'types' => 0,
                //标签
                'tariffRate' => '',
                //关税百分比
                'canal' => !empty($goods['canal']) ? $goods['canal'] : '',
                'ac_type' => $goods['ac_type'],
                //活动类型 0无活动，1限时限量，2活动价，3会员价
                "ratio" => isset($goods['ratio']) && $goods['ratio'] ? $goods['ratio'] : 0,
                //团购优惠价
                'allow_coupon' => $goods['allow_coupon'],
                //使用优惠券，1允许2不允许
                'allow_presale' => isset($goods['allow_presale']) ? intval($goods['allow_presale']) : 1,
                //允许下预售单 1不允许 2允许
                'is_buy' => $goods['status'] == 1 && $goods['is_buy'] == 1 ? 1 : 0,
                //是否能购买
                'goods_images' => $goods['goods_images'],
                'sku_img' => isset($goods['sku_img']) ? $goods['sku_img'] : '',
                'is_quota' => isset($goods['is_quota']) ? $goods['is_quota'] : 0,
                'quota_num' => isset($goods['quota_num']) ? $goods['quota_num'] : 0,
                'self_supplier_type' => isset($goods['self_supplier_type']) ? $goods['self_supplier_type'] : '',
                //1是自采， 2是立创
                'activity_info' => isset($goods["activity_info"]) ? $goods["activity_info"] : [],
                'encoded' => isset($goods["encoded"]) ? $goods["encoded"] : 0,//关联采购员
            );

        } else { //联营、专卖
            $change_place = false;
            if (is_string($goods['ladder_price'])) {
                $goods['ladder_price'] = json_decode($goods['ladder_price'], true);
            }
            foreach ($goods['ladder_price'] as &$v) {
                if ($goods_type == 2 && $goods['supplier_id'] != 21) { //联营  #rs除外
                    $change_place = 1;
                } else {
                    if ($change_place === false) {
                        $change_place = floatval($v['price_cn']) == 0 || floatval($v['price_us']) == 0 ? 0 : 1;
                    }
                }
                $v['purchases'] = intval($v['purchases']);
            }
            //默认交期
            $supplier = getSupplier($goods['supplier_id']);
            $delivery_time = array(
                '1' => !empty($goods['cn_delivery_time']) ? $goods['cn_delivery_time'] : $supplier['cn_delivery'],
                '2' => !empty($goods['hk_delivery_time']) ? $goods['hk_delivery_time'] : $supplier['hk_delivery'],
            );
            empty($delivery_time['1']) && $delivery_time['1'] = '';
            empty($delivery_time['2']) && $delivery_time['2'] = '';
            $data = array(
                'goods_id' => $goods['goods_id'],
                'goods_sn' => '',
                'goods_name' => $goods['goods_name'],
                'sku_name' => $goods['goods_name'],
                'brand_id' => $goods['brand_id'],
                'brand_name' => $goods['brand_name'],
                //品牌名
                'goods_desc' => $goods['spu_brief'],
                //简介
                'supplier_id' => $goods['supplier_id'],
                //spu供应商表ID
                'supplier_name' => $goods['supplier_name'],
                'min_buy' => $goods['moq'],
                'min_mpq' => $goods['mpq'],
                // 'mpl' => empty($goods['mpl']) ? $goods['mpl'] : 1,//倍数
                // 'multiple' => $goods['moq'] > $goods['mpq'] ? $goods['mpq'] : $goods['moq'],
                'multiple' => $goods['multiple'],
                //联营特有叠加倍数
                'encap' => $goods['encap'],
                'class1_name' => $goods['class_name1'],
                'class2_name' => $goods['class_name2'],
                'goods_unit_name' => '',
                'packing_name' => @$goods["packing"],
                'mpq_unit_name' => '',
                'product_batch' => $goods['batch_sn'],
                'goods_number' => $goods['stock'],
                'tiered' => $goods['ladder_price'],
                'delivery_time' => $delivery_time,
                'change_place' => $change_place,
                'pdf' => isset($goods['pdf']) && !empty($goods['pdf']) ? $goods['pdf'] : '',
                'goods_type' => $goods_type,
                'types' => !empty($goods['erp_tax']['types']) ? $goods['erp_tax']['types'] : 0,
                //标签
                'tariffRate' => !empty($goods['erp_tax']['tariffRate']) ? $goods['erp_tax']['tariffRate'] . '%' : '',
                //关税百分比
                'canal' => !empty($goods['canal']) ? $goods['canal'] : '',
                'ac_type' => $goods['ac_type'],
                //活动类型 0无活动，1限时限量，2活动价，3会员价
                'allow_coupon' => $goods['allow_coupon'],
                'ratio' => floatval($goods['ratio']),
                'is_buy' => $goods['goods_status'] == 1 && $goods['is_buy'] == 1 ? 1 : 0,
                //是否能购买
                'goods_images' => $goods['goods_images'],
                'sku_img' => isset($goods['sku_img']) ? $goods['sku_img'] : '',
                'scm_brand' => $goods['scm_brand'],
                'standard_brand' => $goods['standard_brand'],
                'activity_info' => isset($goods["activity_info"]) ? $goods["activity_info"] : [],
                'encoded' => isset($goods["encoded"]) ? $goods["encoded"] : 0,//关联采购员
                'coo' => !empty($goods['coo']) ? $goods['coo'] : '',
                'tariff' => !empty($goods['tariff']) ? $goods['tariff'] : '',
                'tariff_format' => !empty($goods['tariff_format']) ? $goods['tariff_format'] : '',

            );

            // 京东MRO - 商品名称
            if ($goods_type == 6) {
                $data['sku_name'] = !empty($goods['sku_name']) ? $goods['sku_name'] : $goods['goods_name'];
            }
        }

        $data["has_gift_activity"] = isset($goods['has_gift_activity']) ? $goods['has_gift_activity'] : 0;
        $data["gift_activity"] = isset($goods['gift_activity']) ? $goods['gift_activity'] : '';
        $data['standard_brand_id'] = isset($goods['standard_brand']['standard_brand_id']) ? $goods['standard_brand']['standard_brand_id'] : 0;
        $data['standard_brand_name'] = isset($goods['standard_brand']['brand_name']) ? $goods['standard_brand']['brand_name'] : '';
        $data['goods_tag'] = isset($goods['goods_tag']) ? $goods['goods_tag'] : '';
        $data['source'] = isset($goods['source']) ? $goods['source'] : 1;
        $data['ability_level'] = isset($goods['ability_level']) ? $goods['ability_level'] : -1;

        return $data;
    }

    /**
     * 基石接口返回数据格式化成网站通用格式
     * @param  [type] $goods [description]
     * @return [type]        [description]
     */
    private function format1($goods)
    {
        $ladder_price = array();
        $goods_type = isset($goods['goods_type']) ? C('FOOTSTONE_GOODS_TYPE.' . $goods['goods_type']) : C('FOOTSTONE_GOODS_TYPE.' . $goods['sku']['goods_type']);//商品类型 基石 映射
        if (in_array($goods_type, array(3, 4))) { //自营、寄售
            $change_place = 0;
            // foreach ($goods['ladder_price'] as &$v) {
            //     $v = array($v['purchases'], $v['price_us'], $v['price_cn']);
            // }
            $data = array(
                'goods_id' => $goods['goods_id'],
                'goods_sn' => !empty($goods['goods_sn']) ? $goods['goods_sn'] : '',
                'goods_name' => $goods['goods_name'],
                'sku_name' => !empty($goods['sku_name']) ? $goods['sku_name'] : $goods['goods_name'],//自定义商品名
                'brand_id' => $goods['brand_id'],
                'brand_name' => !empty($goods['brand_name']) ? $goods['brand_name'] : '',//品牌名
                'goods_desc' => $goods['goods_brief'],//简介
                'supplier_id' => $goods['supplier_id'],//spu供应商表ID
                'supplier_name' => $goods['supplier_name'],
                'min_buy' => $goods['moq'],
                'min_mpq' => $goods['mpq'],
                'mpl' => !empty($goods['mpl']) ? $goods['mpl'] : 1,//倍数
                'encap' => $goods['encap'],//封装
                'class1_name' => $goods['class_id1_name'],//一级分类
                'class2_name' => $goods['class_id2_name'],//二级分类
                'goods_unit_name' => $goods['goods_unit_name'],//商品单位
                'packing_name' => $goods['packing_name'],//包装单位
                'mpq_unit_name' => $goods['mpq_unit_name'],//圆盘单位
                'product_batch' => !empty($goods['batch_sn']) ? json_decode($goods['batch_sn'], true) : '',
                'goods_number' => $goods['stock'],
                'tiered' => $goods['ladder_price'],
                'delivery_time' => '现货',
                'change_place' => $change_place,
                'goods_type' => $goods_type,
                'types' => 0,//标签
                'tariffRate' => '',//关税百分比
                'canal' => !empty($goods['canal']) ? $goods['canal'] : '',
                'ac_type' => $goods['ac_type'],//活动类型 0无活动，1限时限量，2活动价，3会员价
                'allow_coupon' => $goods['allow_coupon'],//使用优惠券，1允许2不允许
                'is_buy' => $goods['status'] == 1 && $goods['is_buy'] == 1 ? 1 : 0,//是否能购买
            );

        } else { //联营、专卖
            $change_place = false;
            if (is_string($goods['sku']['ladder_price'])) {
                $goods['sku']['ladder_price'] = json_decode($goods['sku']['ladder_price'], true);
            }
            foreach ($goods['sku']['ladder_price'] as &$v) {
                if ($goods_type == 2 && $goods['sku']['supplier_id'] != 21) { //联营  #rs除外
                    $change_place = 1;
                } else {
                    if ($change_place === false) {
                        $change_place = floatval($v['price_cn']) == 0 || floatval($v['price_us']) == 0 ? 0 : 1;
                    }
                }
                $v['purchases'] = intval($v['purchases']);
                // $v = array($v['purchases'], $v['price_us'], $v['price_cn']);
            }
            //默认交期
            $supplier = getSupplier($goods['sku']['supplier_id']);
            $delivery_time = array(
                '1' => !empty($goods['sku']['cn_delivery_time']) ? $goods['sku']['cn_delivery_time'] : $supplier['cn_delivery'],
                '2' => !empty($goods['sku']['hk_delivery_time']) ? $goods['sku']['hk_delivery_time'] : $supplier['hk_delivery'],
            );
            empty($delivery_time['1']) && $delivery_time['1'] = '';
            empty($delivery_time['2']) && $delivery_time['2'] = '';
            $data = array(
                'goods_id' => $goods['sku']['goods_id'],
                'goods_sn' => $goods['sku']['goods_sn'],
                'goods_name' => $goods['sku']['goods_name'],
                'sku_name' => $goods['sku']['goods_name'],
                'brand_id' => $goods['spu']['brand_id'],
                'brand_name' => $goods['sku']['brand_name'],
                //品牌名
                'goods_desc' => $goods['spu']['spu_brief'],
                //简介
                'supplier_id' => $goods['sku']['supplier_id'],
                //spu供应商表ID
                'supplier_name' => $goods['sku']['supplier_name'],
                'min_buy' => $goods['sku']['moq'],
                'min_mpq' => $goods['sku']['mpq'],
                // 'mpl' => empty($goods['mpl']) ? $goods['mpl'] : 1,//倍数
                #'multiple' => $goods['sku']['moq'] > $goods['sku']['mpq'] ? $goods['sku']['mpq'] : $goods['sku']['moq'],
                'multiple' => $goods['multiple'],
                //联营特有叠加倍数
                'encap' => $goods['spu']['encap'],
                'class1_name' => '',
                'class2_name' => '',
                'goods_unit_name' => '',
                'packing_name' => '',
                'mpq_unit_name' => '',
                'product_batch' => $goods['sku']['batch_sn'],
                'goods_number' => $goods['sku']['stock'],
                'tiered' => $goods['sku']['ladder_price'],
                'delivery_time' => $delivery_time,
                'change_place' => $change_place,
                'goods_type' => $goods_type,
                'types' => !empty($goods['spu']['erp_tax']['types']) ? $goods['spu']['erp_tax']['types'] : 0,
                //标签
                'tariffRate' => !empty($goods['spu']['erp_tax']['tariffRate']) ? $goods['spu']['erp_tax']['tariffRate'] . '%' : '',
                //关税百分比
                'canal' => !empty($goods['sku']['canal']) ? $goods['sku']['canal'] : '',
                'ac_type' => $goods['sku']['ac_type'],
                //活动类型 0无活动，1限时限量，2活动价，3会员价
                'allow_coupon' => $goods['sku']['allow_coupon'],
                'ratio' => floatval($goods['sku']['ratio']),
                'is_buy' => $goods['sku']['goods_status'] == 1 && $goods['sku']['is_buy'] == 1 ? 1 : 0,
                //是否能购买
            );
        }
        return $data;
    }

    /**
     * 获取商品或供应商的广告词
     * @return [type] [description]
     */
    public function adWord()
    {
        $goods_id = I('request.id', 0);
        $supplier_id = I('request.sid', 0, 'intval');
        $CmsModel = D('Cms');
        if (empty($supplier_id) && empty($goods_id)) {
            return $this->apiReturn(80005, '缺少商品ID或供应商ID');
        } elseif (empty($supplier_id)) {
            $info = $this->info($goods_id);
            if ($info['err_code'] != 0) {
                return $this->apiReturn($info['err_code'], $info['err_msg']);
            }
            $supplier_id = $info['data']['supplier_id'];
        }
        $bcat_id = $CmsModel->getBaseCat('ad_words');
        $data = $CmsModel->getAdWord($supplier_id, $bcat_id);
        if (!empty($data)) {
            $title = $data['title'];
        }
        $datas = array(
            'title' => !empty($data['title']) ? $data['title'] : '',
            'url' => !empty($data['url']) ? $data['url'] : '',
            'window_open' => !empty($data['window_open']) ? $data['window_open'] : 1,
            'color' => !empty($data['class']) ? $data['class'] : 1,
        );
        return $this->apiReturn(0, '成功', $datas);
    }

    //获取相似物料
    public function similar()
    {
        $goodsId = I('goods_id');
        if (empty($goodsId)) {
            return $this->apiReturn(80005, '缺少商品ID');
        }
        $goodsSimilarModel = D('GoodsSimilar');
        $similarGoodsIds = $goodsSimilarModel->getSimilarGoodsIds($goodsId);
        $similarGoodsList = $this->getStoneGoods($similarGoodsIds, []);
        $similarGoodsList['data'] = array_values($similarGoodsList['data']);
        return $this->apiReturn(0, '成功', $similarGoodsList['data']);
    }

    //添加有货提醒
    public function add_goods_remind()
    {
        $goodsId = I('goods_id');
        if (empty($goodsId)) {
            return $this->apiReturn(-1, '到货提醒的商品Id不能为空');
        }
        $userId = cookie('uid');
        if (empty($userId)) {
            return $this->apiReturn(-1, '请先登陆');
        }
        $data = [
            'goods_id' => $goodsId,
            'add_time' => time(),
            'user_id' => $userId,
        ];
        //先查询这个用户有多少个提醒,如果超过50个就不插入
        $goodsRemindModel = D('GoodsRemind');
        $count = $goodsRemindModel->where([
            'user_id' => $data['user_id'],
            'status' => 0
        ])->count();
        if ($count >= 50) {
            return $this->apiReturn(-1, '您设置的有货提醒已经到达50个的上限');
        }
        //不存在再插入
        if (empty($goodsRemindModel->where(['user_id' => $data['user_id'], ['goods_id' => $goodsId]])->count())) {
            $result = $goodsRemindModel->add($data);
            if (!$result) {
                return $this->apiReturn(-1, '添加到货提醒失败');
            }
        }
        return $this->apiReturn(0, '添加到货提醒成功!');
    }
}
