<?php
namespace Goods\Model;

use Think\Model;

class GoodsModel extends Model
{
    public function getInfo($goods_id)
    {
        $map = array(
            'goods_id' => $goods_id,
        );
        $datas = $this->where($map)->alias('G')
                        ->join(C('DB_PREFIX').'brand B ON B.brand_id = G.brand_id')
                        ->join(C('DB_PREFIX').'company C ON C.com_id = G.company_id')
                        ->field('G.goods_id, G.cat_id, G.company_id, C.com_name, G.brand_id, B.brand_name, G.goods_name, G.goods_number, G.mpq, G.moq, G.pdf_url, G.goods_desc, G.default_img, G.mpq, G.is_on_sale, G.delivery_time, G.product_batch')
                        ->find();
        return $datas;
    }
}