<?php
namespace Goods\Model;
use Think\Model\MongoModel;

class CommonMongoModel extends MongoModel  {

    protected $tablePrefix = '';

    public function _initialize()
    {
        ini_set('mongo.long_as_object', 1);
        parent::_initialize();
        $this->connection = C('DB_MONGO');
        $this->dbName = C('DB_MONGO.db_name');
    }

    /**
     * 修复设置了mongo.long_as_object变对象问题
     * @param  [type] &$data [description]
     * @return [type]        [description]
     */
    public function fixObject(&$data)
    {
        $data = array_map(function($k){
            if (is_object($k)) {
                if (intval($k->value) != $k->value) {
                    if (strval(floatval($k->value)) != number_format($k->value, 0, '', '')) {
                        return strval($k->value);
                    } else {
                        return floatval($k->value);
                    }
                }
                return intval($k->value);
            } elseif (is_array($k)) {
                $this->fixObject($k);
            }
            return $k;
        }, $data);
    }

    public function getList($collection, $where = '', $limit = '')
    {
        $map = array(
            'is_error' => 0,
        );
        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->where($map)->limit($limit)->select(array('table'=>$collection));
        foreach ($datas as &$v) {
            $this->fixObject($v);
        }
        return $datas;
    }

    public function getInfoById($collection, $id)
    {
        $map = array(
            'goods_id' => $id,
        );
        $data = $this->where($map)->find(array('table'=>$collection));
        $this->fixObject($data);
        return $data;
    }

    public function getInfoByOldId($collection, $id)
    {
        $map = array(
            'old_goods_id' => $id,
        );
        $data = $this->where($map)->find(array('table'=>$collection));
        $this->fixObject($data);
        return $data;
    }
}