<?php
namespace Goods\Model;

use Think\Model;

class CmsModel extends Model
{
    protected $autoCheckFields = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.CMS');
    }

    /**
     * 获取供应商的广告营销词
     * @param  [type] $supplier_id [description]
     * @return [type]              [description]
     */
    public function getAdWord($supplier_id, $bcat_id)
    {
        $map = array(
            'tem_id' => $supplier_id,
            'bcat_id' => $bcat_id,
            'status' => 1,
        );
        $data = $this->table('lie_base')->where($map)->order('sort DESC')->find();
        return $data;
    }

    /**
     * 获取分类信息
     * @param  [type] $tags [description]
     * @return [type]       [description]
     */
    public function getBaseCat($tags)
    {
        $data = $this->table('lie_base_cat')->getFieldByTags($tags, 'bcat_id');
        return $data;
    }
}