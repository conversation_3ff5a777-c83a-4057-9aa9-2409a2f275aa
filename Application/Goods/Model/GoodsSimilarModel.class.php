<?php

namespace Goods\Model;

use Think\Model;

class GoodsSimilarModel extends Model
{
    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.ZYGOODS');
    }

    //获取相似物料
    public function getSimilarGoodsIds($goodsId)
    {
        $goodsList  = $this->where(['goods_id' => $goodsId])->select();
        $similarGoodsIds = array_column($goodsList, 'similar_goods_id');
        //然后去商品服务获取信息
        return $similarGoodsIds;
    }
}