<?php

namespace Goods\Service;

use Think\Model;

//分组服务类
class UserGoodsGroupService
{

    public function getUserGoodsGroupList($map)
    {
        $where = ['user_id' => $map['user_id']];
        $offset = intval($map['page'] - 1) * $map['limit'];
        $groupList = D('Goods/UserGoodsGroup')->where($where)->limit($offset, $map['limit'])->order('id desc')->select();
        $groupIds = array_column($groupList, 'id');
        $groupGoodsCount = $this->getGroupGoodsCount($groupIds);
        foreach ($groupList as &$group) {
            $group['goods_count'] = array_get($groupGoodsCount, $group['id'], 0);
            $group['create_time'] = date('Y-m-d H:i:s', $group['create_time']);
            $group['update_time'] = date('Y-m-d H:i:s', $group['update_time']);
        }
        unset($group);
        $count = D('Goods/UserGoodsGroup')->where($where)->count();
        return [
            'list' => $groupList,
            'total' => $count,
        ];
    }


    public function checkGroupNameExist($userId, $groupName)
    {
        return D('Goods/UserGoodsGroup')->where([
            'user_id' => $userId,
            'group_name' => $groupName
        ])->count();
    }

    public function addUserGoodsGroup($data)
    {
        return D('Goods/UserGoodsGroup')->add($data);
    }


    public function updateUserGoodsGroup($id, $data)
    {
        return D('Goods/UserGoodsGroup')->where(['id' => $id])->save($data);
    }

    //根据组id获取每个组的商品数量
    public function getGroupGoodsCount($groupIds = [])
    {
        if (!$groupIds) {
            return [];
        }
        $inSql = implode(',', $groupIds);
        $model = new \Think\Model();
        $result = $model->query("SELECT group_id,count(goods_id) as goods_count FROM lie_user_goods WHERE group_id IN ($inSql) GROUP BY group_id");
        $result = collect($result)->pluck('goods_count', 'group_id')->toArray();
        return $result;
    }
}
