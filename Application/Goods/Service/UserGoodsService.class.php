<?php

namespace Goods\Service;

use Dompdf\Exception;
use Goods\Model\UserGoodsGroupModel;
use Org\Net\Http;
use Think\Model;

//用户物料服务类
class UserGoodsService
{

    public function getUserGoodsList($map)
    {
        //进来的时候,先去商品服务刷新是否能购买状态
        $where['group_id'] = $map['group_id'];
        $where['user_id'] = $map['user_id'];
        if (!empty($map['goods_name'])) {
            $where['goods_name'] = $map['goods_name'];
        }
        if (!empty($map['custom_sn'])) {
            $where['custom_sn'] = $map['custom_sn'];
        }
        if ((isset($map['is_buy']) && $map['is_buy'] === '0') || $map['is_buy']) {
            $where['is_buy'] = $map['is_buy'];
        }
        $offset = intval($map['page'] - 1) * $map['limit'];
        $goodsList = M('UserGoods')->where($where)->order('id desc')->limit($offset, $map['limit'])->select();
        $redis = fs_redis_init();
        $goodsIds = array_column($goodsList, 'goods_id');
        $goodsIds = array_filter($goodsIds, function ($goodsId) {
            return !empty($goodsId);
        });
        $skuList = $this->getSkuListFromSkuServer($goodsIds);
        foreach ($goodsList as &$goods) {
            $currencySymbol = $goods['currency'] == 1 ? ' ¥' : ' $';
            $goods['create_time'] = date('Y-m-d H:i:s', $goods['create_time']);
            $goods['update_time'] = date('Y-m-d H:i:s', $goods['update_time']);
            $goods['price_total'] = $goods['goods_id'] ? $currencySymbol . $goods['goods_number'] * $goods['price'] : '';
            $goods['currency_name'] = $goods['currency'] == 1 ? '人民币' : '美元';
            $goods['price'] = $goods['goods_id'] ? $currencySymbol. $goods['price'] : '';
            $sku = array_get($skuList, $goods['goods_id']);
            $supplier = $redis->hget('supplier', $sku['supplier_id']);
            $goods['moq'] = array_get($sku, 'moq');
            $goods['stock'] = array_get($sku, 'stock');
            $goods['supplier_name'] = $supplier ?: '';
            $goods['brand_name'] = empty($goods['brand_name']) ? '' : $goods['brand_name'];
            $goods['delivery_time'] = $goods['currency'] == 1 ? $sku['cn_delivery_time'] : $sku['hk_delivery_time'];
            if (!empty($sku) && $goods['is_buy'] != $sku['is_buy']) {
                M('UserGoods')->where(['id' => $goods['id']])->save([
                    'is_buy' => $sku['is_buy']
                ]);
            }
        }
        $groupName = M('UserGoodsGroup')->where(['id' => $where['group_id']])->getField('group_name');
        unset($goods);
        $currentTotal = M('UserGoods')->where($where)->count();
        unset($where['is_buy']);
        $count = M('UserGoods')->where($where)->count();
        $where['is_buy'] = 1;
        $canBuyCount = M('UserGoods')->where($where)->count();
        $where['is_buy'] = 0;
        $canNotBuyCount = M('UserGoods')->where($where)->count();
        return [
            'list' => $goodsList,
            'total' => $currentTotal,
            'amount' => [
                'total' => $count,
                'can_buy' => $canBuyCount,
                'can_not_buy' => $canNotBuyCount,
            ],
            'group_name' => $groupName,
        ];
    }

    public function getUserGoodsListByGroupId($groupId)
    {
        return D('Goods/UserGoods')->where(['group_id' => $groupId])->select();
    }

    //单独匹配用户物料
    public function matchUserGoods($matchData)
    {
        //去搜索服务匹配出goods_id
        $url = ES_DOMAIN . '/detail/getlowerPriceSkus';
        $items = [[
            'goods_name' => $matchData['goods_name'],
            'stock' => $matchData['goods_number'],
            'moq' => $matchData['goods_number'],
            'currency' => $matchData['currency'],
        ]];
        $params = ['query' => json_encode($items)];
        $result = json_decode(post_curl($url, $params), true);
        if (isset($result['code']) && $result['code'] == 0) {
            $goodsResult = $result['data'];
        }
        if (empty($goodsResult)) {
            return false;
        }
        $matchData['goods_id'] = $goodsResult[$matchData['goods_name']];
        $goodsList = $this->transformMatchGoods([$matchData]);
        return $goodsList ? $goodsList[0] : [];
    }

    //批量匹配用户物料
    public function batchMatchUserGoods($matchDataList)
    {
        //去搜索服务匹配出goods_id
        $url = ES_DOMAIN . '/detail/getlowerPriceSkus';
        $items = [];
        foreach ($matchDataList as $matchData) {
            $items[] = [
                'goods_name' => $matchData['goods_name'],
                'stock' => $matchData['goods_number'],
                'moq' => $matchData['goods_number'],
                'currency' => $matchData['currency'],
            ];
        }
        $params = ['query' => \GuzzleHttp\json_encode($items)];
        $result = \GuzzleHttp\json_decode(post_curl($url, $params), true);
        if (isset($result['code']) && $result['code'] == 0) {
            $goodsResult = $result['data'];
        }
        if (empty($goodsResult)) {
            return false;
        }

        //替换掉原来匹配数据的goods_id
        foreach ($matchDataList as &$matchData) {
            $matchData['goods_id'] = array_get($goodsResult, $matchData['goods_name'], 0);
        }
        unset($matchData);

        $matchDataList = collect($matchDataList)->chunk(20)->toArray();
        $goodsList = [];
        foreach ($matchDataList as $matchData) {
            $result = $this->transformMatchGoods($matchData);
            $resultKeyByGoodsId = collect($result)->keyBy('goods_id')->toArray();
            $matchGoodsList = [];
            foreach ($matchData as $data) {
                $matchResult = array_get($resultKeyByGoodsId, $data['goods_id']);
                $matchGoodsList[] = $matchResult;
            }
            $goodsList = array_merge($goodsList, $matchGoodsList);
        }
        return $goodsList;
    }

    //去商品服务拼装数据
    public function transformMatchGoods($matchData = [])
    {
        if (empty($matchData)) {
            return [];
        }
        $goodsIds = array_column($matchData, 'goods_id');
        $matchDataKeyByGoodsId = collect($matchData)->keyBy('goods_id')->toArray();
        //去商品服务获取sku信息
        $skuList = $this->getSkuListFromSkuServer($goodsIds);
        $goodsList = [];
        foreach ($matchDataKeyByGoodsId as $goodsId => $match) {
            $sku = array_get($skuList, $goodsId);
            $data = [
                'custom_sn' => array_get($match, 'custom_sn', ''),
                'goods_id' => $match['goods_id'],
                'group_id' => $match['group_id'],
                'goods_number' => $match['goods_number'],
                'user_id' => cookie('uid'),
                'create_time' => time(),
            ];
            if (!empty($sku)) {
                $data['goods_name'] = $sku['goods_name'];
                $data['is_buy'] = $sku['is_buy'];
                if (!empty($sku['standard_brand'])) {
                    $data['brand_id'] = $sku['standard_brand']['standard_brand_id'];
                    $data['brand_name'] = $sku['standard_brand']['brand_name'];
                } else {
                    $data['brand_id'] = 0;
                    $data['brand_name'] = '';
                }
            } else {
                $data['goods_name'] = '';
                $data['is_buy'] = 0;
                $data['brand_id'] = 0;
                $data['brand_name'] = '';
            }
            $currency = isset($match['currency']) ? $match['currency'] : 1;
            $ladderPrice = $sku['ladder_price'];
            $data['price'] = 0;
            if ($ladderPrice) {
                $matchLadder = [];
                if (count($ladderPrice) == 1) {
                    $matchLadder = $ladderPrice[0];
                } else {
                    foreach ($ladderPrice as $key => $ladder) {
                        if ($match['goods_number'] < $ladder['purchases']) {
                            continue;
                        }
                        $matchLadder = $ladder;
                    }
                    if (empty($matchLadder)) {
                        $matchLadder = $ladderPrice[0];
                    }
                }
                if ($currency == UserGoodsGroupModel::CURRENCY_RMB) {
                    if ($sku['ratio']) {
                        $data['price'] = $matchLadder['price_ac'];
                    } else {
                        $data['price'] = $matchLadder['price_cn'];
                    }
                } else {
                    if ($sku['ratio_us']) {
                        $data['price'] = $matchLadder['price_ac_us'];
                    } else {
                        $data['price'] = $matchLadder['price_us'];
                    }
                }
            }
            $data['currency'] = $currency;
            $goodsList[] = $data;
        }
        return $goodsList;
    }

    public function transformMatchGoodsByGoodsId($goodsId, $groupId, $goodsNumber)
    {
        //去商品服务获取sku信息
        $result = post_curl(NEW_GOODS_DOMAIN . '/synchronization', ['goods_id' => $goodsId]);
        $result = \GuzzleHttp\json_decode($result, true);
        if (isset($result['errcode']) && $result['errcode'] == 0) {
            $skuList = $result['data'];
        }
        $sku = array_get($skuList, $goodsId);
        $data = [
            'custom_sn' => '',
            'goods_id' => $goodsId,
            'group_id' => $groupId,
            'goods_number' => $goodsNumber,
            'user_id' => cookie('uid'),
            'create_time' => time(),
        ];
        if (!empty($sku)) {
            if ($goodsNumber > $sku['stock']) {
                throw new \Exception('库存不足');
            }
            if ($goodsNumber < $sku['moq']) {
                throw new \Exception('数量要满足最小起订量');
            }
            $data['goods_name'] = $sku['goods_name'];
            $data['is_buy'] = $sku['is_buy'];
            if (!empty($sku['standard_brand'])) {
                $data['brand_id'] = $sku['standard_brand']['standard_brand_id'];
                $data['brand_name'] = $sku['standard_brand']['brand_name'];
            } else {
                $data['brand_id'] = 0;
                $data['brand_name'] = '';
            }
        } else {
            $data['goods_name'] = '';
            $data['is_buy'] = 0;
            $data['brand_id'] = 0;
            $data['brand_name'] = '';
        }
        $currency = isset($match['currency']) ? $match['currency'] : 1;
        $ladderPrice = $sku['ladder_price'];
        $data['price'] = 0;
        if ($ladderPrice) {
            $matchLadder = [];
            if (count($ladderPrice) == 1) {
                $matchLadder = $ladderPrice[0];
            } else {
                foreach ($ladderPrice as $key => $ladder) {
                    if ($goodsNumber < $ladder['purchases']) {
                        continue;
                    }
                    $matchLadder = $ladder;
                }
            }
            if ($currency == UserGoodsGroupModel::CURRENCY_RMB) {
                if ($sku['ratio']) {
                    $data['price'] = $matchLadder['price_ac'];
                } else {
                    $data['price'] = $matchLadder['price_cn'];
                }
            } else {
                if ($sku['ratio_us']) {
                    $data['price'] = $matchLadder['price_ac_us'];
                } else {
                    $data['price'] = $matchLadder['price_us'];
                }
            }
        }
        $data['currency'] = $currency;
        return $data;
    }

    public function addUserGoods($data)
    {
        return D('Goods/UserGoods')->add($data);
    }

    public function batchDeleteUserGoods($ids = [])
    {
        return D('Goods/UserGoods')->where(['id' => ['in', $ids]])->delete();
    }

    public function updateUserGoods($id, $data)
    {
        $goods = D('Goods/UserGoods')->where(['id' => $id])->find();
        if (empty($data['goods_number'])) {
            $data['goods_number'] = $goods['goods_number'];
        }
        if (!empty($data['goods_id'])) {
            $result = post_curl(NEW_GOODS_DOMAIN . '/synchronization', ['goods_id' => [$data['goods_id']]]);
            $result = \GuzzleHttp\json_decode($result, true);
            if (isset($result['errcode']) && $result['errcode'] == 0) {
                $skuList = $result['data'];
                $sku = array_first($skuList);
                if (!$sku) {
                    throw new \Exception('对应的商品找不到');
                }
                $goods['goods_id'] = $data['goods_id'];
                $goods['goods_name'] = $sku['goods_name'];
                $goods['is_buy'] = $sku['is_buy'];
                if (!empty($sku['standard_brand'])) {
                    $goods['brand_id'] = $sku['standard_brand']['standard_brand_id'];
                    $goods['brand_name'] = $sku['standard_brand']['brand_name'];
                } else {
                    $goods['brand_id'] = 0;
                    $goods['brand_name'] = '';
                }
                $ladderPrice = $sku['ladder_price'];
                if ($ladderPrice) {
                    $matchLadder = [];
                    if (count($ladderPrice) == 1) {
                        $matchLadder = $ladderPrice[0];
                    } else {
                        foreach ($ladderPrice as $key => $ladder) {
                            if ($data['goods_number'] < $ladder['purchases']) {
                                continue;
                            }
                            $matchLadder = $ladder;
                        }
                        if (empty($matchLadder)) {
                            $matchLadder = $ladderPrice[0];
                        }
                    }
                    if ($goods['currency'] == UserGoodsGroupModel::CURRENCY_RMB) {
                        if ($sku['ratio']) {
                            $goods['price'] = $matchLadder['price_ac'];
                        } else {
                            $goods['price'] = $matchLadder['price_cn'];
                        }
                    } else {
                        if ($sku['ratio_us']) {
                            $goods['price'] = $matchLadder['price_ac_us'];
                        } else {
                            $goods['price'] = $matchLadder['price_us'];
                        }
                    }
                }
            } else {
                throw new \Exception('对应的商品找不到');
            }
            $goods['goods_number'] = $data['goods_number'];
        } else {
            if (!empty($data['goods_number'])) {
                $map = [
                    'goods_name' => $goods['goods_name'],
                    'goods_number' => $data['goods_number'],
                    'group_id' => $goods['group_id'],
                    'currency' => $goods['currency'],
                ];
                $goods = (new UserGoodsService())->matchUserGoods($map);
                if (!$goods) {
                    $goods = [
                        'goods_id' => 0,
                        'brand_id' => 0,
                        'brand_name' => '',
                        'price' => 0,
                        'is_buy' => 0,
                        'currency' => 1,
                        'goods_number' => $data['goods_number'],
                    ];
                    D('Goods/UserGoods')->where(['id' => $id])->save($goods);
                    throw new \Exception('没有匹配到满足该库存或者起订量的商品');
                }
                $goods['goods_number'] = $data['goods_number'];
            }
        }

        if ($data['custom_sn']) {
            $goods['custom_sn'] = $data['custom_sn'];
        }
        $goods['update_time'] = time();
        return D('Goods/UserGoods')->where(['id' => $id])->save($goods);
    }

    public function checkUserGoodsExists($groupId, $userId, $goodsName)
    {
        return (bool)D('Goods/UserGoods')->where(['group_id' => $groupId, 'user_id' => $userId, 'goods_name' => $goodsName])->find();
    }

    public function checkUserGoodsExistsByGoodsId($groupId, $userId, $goodsId)
    {
        return (bool)D('Goods/UserGoods')->where(['group_id' => $groupId, 'user_id' => $userId, 'goods_id' => $goodsId])->find();
    }

    public function checkCanExportUserGoods($groupId)
    {
        $userGoods = D('Goods/UserGoods')->where(['group_id' => $groupId])->find();
        if (empty($userGoods)) {
            return '导出的用户物料不能为空';
        }
        $userGoodsUserId = $userGoods['user_id'];
        if (cookie('uid') != $userGoodsUserId) {
            return '你需要导出的物料不属于你自己';
        }
        return true;
    }

    public function exportUserGoodsList($groupId)
    {
        //先去找出对应的数据
        $userGoodsList = D('Goods/UserGoods')->where(['group_id' => $groupId])
            ->select();
        $header = ['自定义编码', '型号', '品牌', '推荐单价', '需求数量', '商品链接'];
        $data = [];
        foreach ($userGoodsList as $goods) {
            $data[] = [
                "\t".$goods['custom_sn'],
                $goods['goods_name'],
                $goods['brand_name'],
                $goods['price'],
                $goods['goods_number'],
                getGoodsUrl($goods['goods_id']),
            ];
        }

        export_csv($data, $header, '物料导出.csv');
    }

    public function getSkuListFromSkuServer($goodsIds)
    {
        if (empty($goodsIds)) {
            return [];
        }
        $result = post_curl(NEW_GOODS_DOMAIN . '/synchronization', ['goods_id' => implode(',', $goodsIds)]);
        $result = \GuzzleHttp\json_decode($result, true);
        $skuList = [];
        if (isset($result['errcode']) && $result['errcode'] == 0) {
            $skuList = $result['data'];
        }
        return $skuList;
    }
}
