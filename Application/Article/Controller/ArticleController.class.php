<?php

namespace Article\Controller;

use Article\Controller\BaseController;

use Article\Services\ArticleService;
use Help\Model\ArticleModel;


class ArticleController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        $this->login = $this->checkLogin();
    }

    public function test()
    {
        $res = footstone_curl('https://api.weixin.qq.com/sns/oauth2/access_token?appid=wxe19a900d68b29e65&secret=c614159878b5b3e959248f26f04c6370&code=0017cRkl2DlWwf4oVNkl2AvwF627cRkK&grant_type=authorization_code');
        dd(\GuzzleHttp\json_decode($res,true));
        dd($res);
        dd(session('wx_state'));
    }

    // 获取需要替换链接词库的文章（消费）
    public function crobWord()
    {
        $RBMQModel = D('Common/Rbmq');
        $ArticleModel = D('Article');
        $artQueueKey = C('ARTICLE_WORD');
        $limit = C('ART_SEND_NUM');
        while ($limit-- > 0) {
            $data = $RBMQModel->connect('RBMQ_MSG_CONFIG')->queue($artQueueKey)->pull(false);
            if ($data !== false) {
                $data = json_decode($data, true);
                // 处理逻辑
                $ArticleModel->replaceWord($data);
                $RBMQModel->ack();
                echo "success<h1>";
            } else {
                echo "no data";
                die;
            }
        }
    }

    public function tags()
    {
        $ArticleModel = D('Article');
        $res = $ArticleModel->replaceWord();
        return $this->apiReturn(0, '处理成功', $res);
    }

    /**
     * 获取新闻列表接口
     * @return [type] [description]
     */
    public function lists()
    {
        //筛选
        $p = I('request.p', 1, 'intval');
        $filter = array('type_id');

        $ArticleModel = D('Article');
        //条件
        $map = map_filter($filter);
        $count = $ArticleModel->getCount($map);
        if ($count == 0) {
            return $this->apiReturn(21004, '未找到相关订单');
        }
        $page = $p . ',7';
        $data = $ArticleModel->getList($map, $page);
        foreach ($data as &$v) {
            $v['pub_time'] = !empty($v['pub_time']) ? date('Y-m-d', $v['pub_time']) : '';
            $v['url'] = WWW_DOMAIN . '/v3/about/detail.html?art_id=' . $v['art_id'];
        }
        $datas = page_data($data, $count, $p);
        return $this->apiReturn(0, '获取成功', $datas);
    }

    /**
     * 资讯列表
     * @return [type] [description]
     */
    public function index()
    {
        //筛选
        $p = I('request.p', 1, 'intval');
        $cat_id = I('request.cat_id', -1, 'intval');
        $art_m = D('Article');
        $list = array();
        $arr_data = array();
        $res_p = $art_m->getData('lie_arctype', array('type_name' => array('in', '资讯,猎芯动态')), 'type_id', 2);
        //获取二级标题
        $ids = array();
        foreach ($res_p as $v) {
            $ids[] = $v['type_id'];
        }
        $where = array();
        $where['parent_id'] = array('in', $ids);
        $where['status'] = 1;
        $arr_cat = $art_m->getData('lie_arctype', $where, 'type_id as cat_id,type_name as cat_name', '', 'type_id asc');
        $tempc = array();
        $hua = array();
        foreach ($arr_cat as $k => $v) {
            if ($v['cat_name'] == '行业热点') {
                $a = $v;
            } else {
                if ($v['cat_name'] == '满天芯') {
                    $b = $v;
                } else {
                    if ($v['cat_name'] == '芯三板') {
                        $c = $v;
                    } else {
                        $hua[] = $v;
                    }
                }
            }
        }
        if ($a) {
            $tempc[] = $a;
        }
        if ($b) {
            $tempc[] = $b;
        }
        if ($c) {
            $tempc[] = $c;
        }
        $cat_list = array_merge($tempc, $hua);
        $list['cat_list'] = $cat_list;
        // 轮播图
        $cms_m = D('Cms');
        $rollbanner = $cms_m->getBaseList('news_center_banner', '', '');
        $list['rollbanner'] = $rollbanner;

        //}

        $where = array();
        if ($cat_id == 1) {
            $ids = array();
            foreach ($res_p as $v) {
                $ids[] = $v['type_id'];
            }
            $where['top_type_id'] = array('in', $ids);
        } else {
            if ($cat_id <= 0) {
                $where['type_id'] = $cat_list[0]['cat_id'];
            } else {
                $where['type_id'] = $cat_id;
            }

        }
        $where['status'] = 1;
        $searchKeyword = I('keyword');
        $page_num = !empty($_REQUEST['limit']) ? $_REQUEST['limit'] + 0 : 10;
        $start = ($p - 1) * $page_num;
        $searchWhere = [];
        if ($searchKeyword) {
            $searchWhere['lie_article.title'] = ['like', "%$searchKeyword%"];
            $searchWhere['lie_article.description'] = ['like', "%$searchKeyword%"];
            $searchWhere['_logic'] = 'or';
        }
        if ($searchWhere) {
            $where['_complex'] = $searchWhere;
        }

        $query = M('Article')->where($where)
            ->field('art_id,type_id as cat_id,title,description,litpic,click,writer,pub_time,publish_time,start_review,art_type')
            ->limit($start, $page_num);
        $count = $art_m->table('lie_article')->where($where)->count();
        if (!$searchWhere) {
            $query->order('is_top desc,sort_order desc,pub_time desc');
        }

        $arr_data_tmp = $query->select();
        if (!empty($arr_data_tmp)) {
            $ids = '';
            foreach ($arr_data_tmp as $k => &$v) {
                $v['tag'] = array();
                if (($v['pub_time'] + 5 * 60) < time()) {
                    $v['click'] += $v['start_review'];
                }
                $arr_data[$v['art_id']] = $v;
                $ids .= ',' . $v['art_id'];
            }
            $where = array();
            $where['art_id'] = array('in', trim($ids, ','));
            $arr_tag = $art_m->getData('lie_article_tag', $where, '*', '', 'art_id desc');
            if ($arr_tag) {
                foreach ($arr_tag as $k => &$v) {
                    if (count($arr_data[$v['art_id']]['tag']) < 3) {
                        $arr_data[$v['art_id']]['tag'][] = $v;
                    }
                }
            }
            foreach ($arr_data_tmp as $k => &$v) {
                if (isset($arr_data[$v['art_id']]['tag']) && count($arr_data[$v['art_id']]['tag']) > 0) {
                    $v['tag'] = $arr_data[$v['art_id']]['tag'];
                }

            }
        }

        if ($p > 1 && empty($arr_data_tmp)) {
            return $this->apiReturn(1, '暂无数据', $list);
        }
        $list['content_list'] = $arr_data_tmp;
        $list['tatol_num'] = $count;
        return $this->apiReturn(0, '获取成功', $list);
    }

    //获取文章的内容
    public function details()
    {
        //筛选
        $art_id = I('request.art_id', 0, 'intval');
        if (!$art_id) {
            return $this->apiReturn(1, 'id不能为空', '');
        }

        $art_m = D('Article');
        $list = array();
        $where = array();
        $where['art_id'] = $art_id;
        if (!isset($_REQUEST['is_preview']) || $_REQUEST['is_preview'] != 2) {
            $where['status'] = 1;
        }

        $arr_data = $art_m->getData('lie_article', $where,
            'art_id,type_id as cat_id,title,description,litpic,click,writer,pub_time,publish_time,start_review,art_type',
            1, 'art_id asc');
        if (!empty($arr_data)) {
            $cat_id = $arr_data[0]['cat_id'];
            //获取标签
            $where = array();
            $where['art_id'] = $art_id;
            $where['flag'] = 0;
            $arr_tag = $art_m->getData('lie_article_tag', $where, '*', '', 'id asc');
            $arr_data[0]['tag'] = $arr_tag;

            //获取正文内容
            $where = array();
            $where['art_id'] = $art_id;
            $arr_body = $art_m->getData('lie_article_addon', $where, '*', 1);
            $arr_data[0]['body'] = $arr_body[0]['body'];
            if (!empty($arr_tag)) {
                foreach ($arr_tag as $v) {
                    if ($v['status']) {
                        $str = '<a class="f-blue hhs_tag" href="' . $v['url'] . '" target="_blank">' . $v['tag'] . '</a>';
                    } else {
                        $str = '<a class="f-blue hhs_tag" href="' . $v['url'] . '">' . $v['tag'] . '</a>';
                    }

                    $arr_data[0]['body'] = preg_replace('/' . $v['tag'] . '/', $str, $arr_data[0]['body']);
                }
            }
            if (($arr_data[0]['pub_time'] + 5 * 60) < time()) {
                $arr_data[0]['click'] += $arr_data[0]['start_review'];
            }
            if ($arr_data[0]['art_type'] == 1) {
                $arr_data[0]['art_type_text'] = '原创';
            } else {
                if ($arr_data[0]['art_type'] == 2) {
                    $arr_data[0]['art_type_text'] = '转载';
                } else {
                    $arr_data[0]['art_type_text'] = '网络整理';
                }
            }
            $where = array();
            $where['art_id'] = $art_id;
            $res = $art_m->updateData('article', $where, array('click' => array('exp', 'click+1')));
            $res_p = $art_m->getData('lie_arctype', array('type_id' => $cat_id),
                'type_name as cat_name,type_id as cat_id', 1);
            $list['cat_info'] = $res_p[0];
            $list['current_info'] = $arr_data ? $arr_data[0] : false;
            //获取上一页和下一页
            $articles = (new ArticleModel())->getPrevAndNextArticle($art_id);
            $list['current_info']['prev_art_id'] = $articles['prev_art_id'];
            $list['current_info']['next_art_id'] = $articles['next_art_id'];
            $list['current_info']['prev_art_title'] = $articles['prev_art_title'];
            $list['current_info']['next_art_title'] = $articles['next_art_title'];
        } else {
            return $this->apiReturn(1, '没有找到相关资讯', '');
        }


        return $this->apiReturn(0, '获取成功', $list);
    }

    /*
        展示分类信息
    */
    public function show_class()
    {
        $model = D('dataClass');
        $classList = $model->where(['is_del' => 0])->where(['parent_id' => 0])->order('sort desc')->select();
        $parentClassList = [];
        foreach ($classList as $key => $class) {
            if ($class['parent_id'] == 0) {
                $parentClassList[] = $class;
            }
        }
        $totalClass = [
            'class_id' => -1,
            'class_name' => '全部',
            'parent_id' => 0,
            'child_class' => []
        ];
        foreach ($classList as $key => $class) {
            if ($class['parent_id'] != 0) {
                $totalClass['child_class'][] = $class;
            }
            foreach ($parentClassList as $index => $parentClass) {
                if ($class['parent_id'] == $parentClass['class_id']) {
                    $parentClassList[$index]['child_class'][] = $class;
                }
            }
        }
        array_unshift($parentClassList, $totalClass);
        return $this->apiReturn(0, '获取成功', $parentClassList);
    }


    /*
        展示下载资料信息
    */
    public function showData()
    {

        $model = D('dataUpdate');

        if (!empty($_REQUEST['class_id'])) {
            if ($_REQUEST['class_id'] == -1) {

            } else {
                //判断是否是顶级分类
                $classModel = M('dataClass');
                $classIds = $classModel->where(['parent_id' => $_REQUEST['class_id']])
                    ->getField('class_id', true);
                if ($classIds) {
                    $classIds = implode(',', $classIds);
                    $where['lie_data_class.class_id'] = ['in', $classIds];
                } else {
                    $where['lie_data_class.class_id'] = $_REQUEST['class_id'] + 0;
                }
            }
        }

        if (empty($_REQUEST['page'])) {
            $_REQUEST['page'] = 1;
        }

        if (empty($_REQUEST['limit'])) {
            $_REQUEST['limit'] = 10;
        }

        if (!empty($_REQUEST['title'])) {
            $title = $_REQUEST['title'];
            $where['title'] = ['like', "%$title%"];
            unset($where['lie_data_class.class_id']);
        }

        $download_num = S('download_num..' . $_COOKIE['Yo4teW_uid']);

        //获取最后一次下载时间的 晚上23:59:59
        $time = date('Y-m-d', $download_num['time']);
        $time = $time . ' 23:59:59';
        $time = strtotime($time);

        $daily_total_count = 5;
        if ($download_num['num'] >= $daily_total_count && time() < $time) {
            // return $this->apiReturn(-1, '您今天下载次数已达上限，明天再来试试吧~');
            $file_url = '';
        } else {
            $file_url = ',file_url';
        }

        //强登录
        if ($this->login['err_code'] == 0) {
            $limit = ($_REQUEST['page'] - 1) * $_REQUEST['limit'] . ',' . $_REQUEST['limit'];
            $result = $model->field('data_id,title,(download_num + rand_count) as download_num,top,update_time,sort_data,class_name' . $file_url)
                ->join('LEFT JOIN lie_data_class on lie_data_class.class_id = lie_data_update.class_id')
                ->where($where)->order('top desc,create_time desc,update_time desc')->limit($limit)->select();
            $count = $model->join('LEFT JOIN lie_data_class on lie_data_class.class_id = lie_data_update.class_id')->where($where)->limit($limit)->count();
        } else {/*弱登录或无登录*/

            if (empty($where['lie_data_class.class_id'])) {
                $limit = 12;
            } else {
                $limit = 3;
            }
            $result = $model->field('data_id,title,(download_num + rand_count) as download_num,top,update_time,sort_data,class_name' . $file_url)->join('LEFT JOIN lie_data_class on lie_data_class.class_id = lie_data_update.class_id')->where($where)->order('top desc,create_time desc,update_time desc')->limit($limit)->select();
            $count = $limit;
        }

        $data['total'] = $count;
        $data['result'] = $result;
        $data['daily_download_count'] = $daily_total_count;
        $data['remain_download_count'] = empty($_COOKIE['Yo4teW_uid']) ? 5 : ($daily_total_count - $download_num['num']);


        return $this->apiReturn(0, '获取成功', $data);
    }

    /*
        下载文件并计数
    */
    public function DownloadFile()
    {

        //读取redis检查下载次数 达到次数就终止执行
        $download_num = S('download_num..' . $_COOKIE['Yo4teW_uid']);

        //获取最后一次下载时间的 晚上23:59:59
        $time = date('Y-m-d', $download_num['time']);
        $time = $time . ' 23:59:59';
        $time = strtotime($time);


        if ($download_num['num'] >= 5 && time() < $time) {
            return $this->apiReturn(-1, '您今天下载次数已达上限，明天再来试试吧~');
        }

        //如果大于3次还能下载说明过了一天了
        if ($download_num['num'] >= 5) {
            $num = 1;
        } else {
            $num = $download_num['num'] + 1;
        }

        //该用户下载次数加1
        S('download_num..' . $_COOKIE['Yo4teW_uid'], json_encode(array('num' => $num, 'time' => time())));

        $data_id = $_REQUEST['data_id'];
        $model = D('dataUpdate');
        $result = $model->field('file_url,title')->where(array('data_id' => $data_id))->select();
        if (!empty($result[0]['file_url'])) {
            $result[0]['file_url'] = changeImgDomainToHttps($result[0]['file_url']);
        }

        //数据库文章总下载次数+1
        if (!empty($result)) {
            $model->where(array('data_id' => $data_id))->setInc('download_num', 1);
            //新增一条下载记录
            $user_id = $_COOKIE['Yo4teW_uid'];
            if (!empty($user_id)) {
                $logModel = M('DataUpdateLog');
                $data = [
                    'user_id' => $user_id,
                    'user_sn' => 'U' . $user_id,
                    'data_update_id' => $data_id,
                    'create_time' => time(),
                ];
                $logModel->data($data)->add();
            }

        }
        return $this->apiReturn(0, '获取成功', $result);

    }


    /**
     * 创建留言
     * @return [type] [description]
     */
    public function createArticleMessage()
    {
        $content = I('content');

        $ArticleMessageModel = M('ArticleMessage');

        $userInfo = S_user(cookie('uid'));

        if (empty($userInfo['user_name'])) {
            $userInfo['user_name'] = '匿名用户';
        }

        $time = time();

        $count = $ArticleMessageModel->add([
            'content' => $content,
            'user_account' => $userInfo['user_name'],
            'user_id' => intval($userInfo['user_id']),
            'create_time' => $time,
            'message_time' => $time,
            'user_head' => empty($userInfo['user_head']) ? '' : $userInfo['user_head'],
            'user_ip' => $userInfo['reg_ip']
        ]);

        if ($count == 0) {
            return $this->apiReturn(21004, '创建失败');
        }

        return $this->apiReturn(0, '创建成功', []);
    }

    /**
     * 点赞留言
     * @return [type] [description]
     */
    public function goodArticleMessage()
    {
        $aemeId = I('aeme_id');

        $ArticleMessageModel = M('ArticleMessage');
        $ArticleMessageGoodModel = M('ArticleMessageGood');

        $userId = cookie('uid');
        if (empty($articleGoodsInfo = $ArticleMessageGoodModel->where(['user_id' => $userId])->where(['aeme_id' => $aemeId])->find())) {
            $ArticleMessageModel->where(['aeme_id' => $aemeId])->setInc('user_good_num');
            $ArticleMessageGoodModel->add([
                'user_id' => $userId,
                'aeme_id' => $aemeId,
                'create_time' => time()
            ]);
        } else {

            if (intval($articleGoodsInfo['is_del']) === 0) {
                $ArticleMessageModel->where(['aeme_id' => $aemeId])->setDec('user_good_num');
                $ArticleMessageGoodModel->where(['user_id' => $userId])->where(['aeme_id' => $aemeId])->save(['is_del' => 1]);
            } else {
                $ArticleMessageModel->where(['aeme_id' => $aemeId])->setInc('user_good_num');
                $ArticleMessageGoodModel->where(['user_id' => $userId])->where(['aeme_id' => $aemeId])->save(['is_del' => 0]);
            }

        }

        return $this->apiReturn(0, '点赞成功', []);
    }


    /**
     * 获取留言列表
     * @return [type] [description]
     */
    public function articleMessageList()
    {
        $page = I('page');
        $limit = I('limit');

        $ArticleMessageModel = M('ArticleMessage');
        $ArticleMessageGoodModel = M('ArticleMessageGood');

        $messageData = $ArticleMessageModel->page($page, $limit)->order('is_top DESC,reply_time DESC')
            ->where(['show_status' => 1])
            ->where(['is_del' => 0])
            ->field('user_account,user_head,user_id, content,user_head, aeme_id,reply_content,reply_time,create_time,user_good_num')
            ->select();

        $returnData = [];
        $userId = cookie('uid');
        foreach ($messageData as $key => $value) {
            if ($ArticleMessageGoodModel->where(['user_id' => $userId])->where(['is_del' => 0])->where(['aeme_id' => $value['aeme_id']])->find()) {
                $isGood = 1;
            } else {
                $isGood = 0;
            }
            $returnData[$key] = [
                'user_account' => $value['user_account'], 'user_content' => $value['content'], 'user_area' => '广东', 'user_time' => date('Y-m-d H:i:s', $value['create_time']), 'user_good_num' => $value['user_good_num'],
                'is_good' => $isGood, 'aeme_id' => $value['aeme_id'], 'user_pic' => $value['user_head'],

                'reply_account' => '管理员',
                'reply_content' => $value['reply_content'],
                'reply_area' => '广东',
                'reply_time' => empty($value['reply_time']) ? '' : date('Y-m-d H:i:s', $value['reply_time']),

            ];
        }
        return $this->apiReturn(0, '获取成功', ['total' => count($messageData), 'result' => $returnData]);
    }


    /** 下面都是新写的提供给H5的接口,上面的接口不清楚在哪使用到 **/

    //获取文章详情
    public function articleDetail()
    {
        //筛选
        $artId = I('request.art_id', 0, 'intval');
        $article = (new ArticleService())->getArticleDetail($artId);
        return $this->apiReturn(0, '获取成功', $article);
    }

    //获取文章列表(根据类型,目前是二级分类id和标签id)
    public function getArticleList()
    {
        $tagId = I('tag_id');
        $typeId = I('type_id');
        $functionTagId = I('function_tag_id');
        if (empty($tagId) && empty($typeId) && empty($functionTagId)) {
            return $this->apiReturn(-1, '请传入tag_id或者type_Id');
        }
        $p = I('p', 1);
        $limit = I('limit', 10);
        $type = $typeId ? 'type' : 'tag';
        $map = [
            'function_tag_id' => $functionTagId,
            'tag' => $tagId,
            'type_id' => $typeId,
            'p' => $p,
            'limit' => $limit,
            'title' => I('title'),
        ];
        $articleList = (new ArticleService())->getArticleListByType($type, $map);
        return $this->apiReturn(0, '获取成功', $articleList);
    }
}
