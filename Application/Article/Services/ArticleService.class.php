<?php

namespace Article\Services;


use Common\Model\RedisModel;

class ArticleService
{

    public function getArticleDetail($artId)
    {
        //seo指定的特殊跳转逻辑
        $model = D('Article/Article');
        $article = $model->where(['art_id' => $artId])->find();
        $article = $this->getArticleTagsInfo($article);
        $articleAddon = D('Article/ArticleAddon')->where(['art_id' => $artId])->find();
        $article = $this->transformArticle($article);
        $article['h5_news_detail_ad'] = $this->getAdList();
        $article['hot_article_list'] = $this->getHotArticleList();
        $article['content'] = $articleAddon['body'];
        //新增点击次数
        $where['art_id'] = $artId;
        $articleModel = D('Article/Article');
        $articleModel->where($where)->setInc('click');
        return $article;
    }


    //根据类型去获取文章列表.目前是分类和标签
    public function getArticleListByType($type, $map)
    {
        $where = [];
        $typeName = $tagName = '';
        $showAd = $type ? true : false;
        switch ($type) {
            case "type":
                $where['type_id'] = $map['type_id'];
                $typeName = D('Article/Arctype')->where(['type_id' => $map['type_id']])->getField('type_name');
                $typeName = $typeName ?: '';
                break;
            case "tag":
                if (!empty($map['tag'])) {
                    $tag = D('ArticleTag')->where(['id' => $map['tag']])->find();
                    $articleIds = D('ArticleTag')->where(['tag' => $tag['tag']])->getField('art_id', true);
                    $articleIds = array_unique($articleIds);
                    if ($articleIds) {
                        $where['art_id'] = ['in', $articleIds];
                    } else {
                        $where['art_id'] = ['in', [-1]];
                    }
                } elseif (!empty($map['function_tag_id'])) {
                    $where['standard_brand_ids|sku_class_ids'] = $map['function_tag_id'];
                }
                break;
        }
        if (!empty($map['title'])) {
            $where['title'] = ['like', '%' . $map['title'] . '%'];
        }
        $where['status'] = 1;
        $query = D('Article');
        $articleList = $query->where($where)->order('is_top desc,art_id desc')
            ->page($map['p'] . ',' . $map['limit'])->select();
        //获取tag列表
        foreach ($articleList as $key => &$article) {
            if (strlen($article['description']) > 0) {
                $article['guide_reading'] = mb_substr($article['description'], 0, 80, 'utf-8');
            }
            $article = $this->getArticleTagsInfo($article);
            $article['litpic'] = changeImgDomainToHttps($article['litpic']);
            $article['publish_time'] = date('Y-m-d H:i:s', $article['publish_time']);
            $article['url'] = getMainDomain() . 'article/' . $article['art_id'] . '.html';
        }
        $count = D('Article')->where($where)->count();
        $articleList = array_map(function ($article) {
            return $this->transformArticle($article);
        }, $articleList);
        unset($article);
        return [
            'list' => $articleList,
            'count' => $count,
            'h5_news_ad' => $showAd ? $this->getAdList() : [],
            'type_id' => $map['type_id'],
            'type_name' => $typeName,
        ];
    }

    public function getArticleTagsInfo($article)
    {
        $article['tag'] = D('ArticleTag')->where(['art_id' => $article['art_id']])->select();
        foreach ($article['tag'] as $k => $item) {
            $item['url'] = getMainDomain() . 'news.html?tag=' . $item['tag'];
            $item['tag_id'] = $item['id'];
            unset($item['id']);
            $article['tag'][$k] = $item;
        }
        $redis = fs_redis_init();
        //处理标签url
        $article['function_tags'] = [];
        if ($article['standard_brand_ids']) {
            $standardBrands = $redis->hmget('standard_brand', explode(',', $article['standard_brand_ids']));
            foreach ($standardBrands as $brand) {
                $brand = json_decode($brand, true);
                $article['function_tags'][] = [
                    'function_tag_id' => $brand['standard_brand_id'],
                    'name' => $brand['brand_name'],
                    'url' => getMainDomain() . 'news.html?standard_brand_id=' . $brand['standard_brand_id'],
                ];
            }
        }
        if ($article['sku_class_ids']) {
            $skuClassList = $redis->hmget('pool_class_info', explode(',', $article['sku_class_ids']));
            foreach ($skuClassList as $class) {
                $class = json_decode($class, true);
                $article['function_tags'][] = [
                    'function_tag_id' => $class['class_id'],
                    'name' => $class['class_name'],
                    'url' => getMainDomain() . 'news.html?sku_class_id=' . $class['class_id'],
                ];
            }
        }
        return $article;
    }

    //转换文章数据
    public function transformArticle($article)
    {
        $article['type_name'] = D('Article/Arctype')->where(['type_id' => $article['type_id']])->getField('type_name');
        $article['publish_time'] = $article['publish_time'] ? date('Y-m-d H:i:s', $article['publish_time']) : '';
        if (($article['pub_time'] + 5 * 60) < time()) {
            $article['click'] += $article['start_review'];
        }
        return $article;
    }

    //获取配置的广告
    public function getAdList()
    {
        $cmsModel = D('Cms');
        $data = $cmsModel->getBaseList('h5_news_ad', '', '');
        return $data;

    }

    public function getHotArticleList()
    {
        $selectFields = 'art_id,title';
        $where['status'] = 1;
        $where['is_top'] = 1;
        $where['publish_time'] = ['GT', time() - 3600 * 24 * 60];
        //先去取置顶的
        $topArticleList = D('Article/Article')->field($selectFields)
            ->where($where)->limit(5)->select();
        //不够才去补全
        $topArticleCount = count($topArticleList);
        $hotArticleList = [];
        $remainCount = 5 - $topArticleCount;
        if ($remainCount) {
            $where['is_top'] = ['NEQ', 1];
            $hotArticleList = D('Article')->field($selectFields)
                ->where($where)->order('click desc')->limit($remainCount)->select();
        }

        $articleList = array_merge($topArticleList, $hotArticleList);
        $articleList = array_map(function ($article) {
            $article['url'] = getMainDomain() . 'article/' . $article['art_id'] . '.html';
            return $article;
        }, $articleList);
        return $articleList;
    }
}




