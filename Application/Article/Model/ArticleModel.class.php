<?php

namespace Article\Model;

use Think\Log;
use Think\Model;

class ArticleModel extends Model
{
    public function getCount($where = '')
    {
        $map = array(
            'status' => 1,
        );
        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->where($map)->count();
        return $datas;
    }

    /**
     * 根据表名和相应条件查询相关表数据总条数
     * @param  [type] $table [description]
     * @param  [type] $where [description]
     * @param string $limit [description]
     * @param string $group [description]
     * @return [type]        [description]
     */
    public function updateData($table, $where, $data)
    {
        $datas = M($table)->where($where)->save($data);
        return $datas;
    }

    public function getList($where = '', $page = '', $order = 'sort_order DESC,pub_time DESC')
    {
        $map = array(
            'status' => 1,
        );
        list($p, $limit) = explode(',', $page);
        empty($p) && $p = 1;
        empty($limit) && $limit = C('DEFAULT_PAGE_LIMIT');
        !empty($where) && $map = array_merge($map, $where);
        $datas = $this->where($map)->page($p, $limit)->order($order)
            ->field('art_id, title, description, pub_time,publish_time, is_top')
            ->select();
        return $datas;
    }

    //根据表名table，条件where等获取信息
    public function getData($table, $where, $field = '*', $limit = '', $order = '')
    {
        $datas = $this->table($table)->where($where)->limit($limit)->order($order)->field($field)->select();
        return $datas;
    }

    /**
     * [replaceWord 处理词库替换内容]
     * @param  [type] $data [art_id,文章id，content,文章内容]
     *
     * @return
     */
    public function replaceWord()
    {
        $art_id = $_REQUEST['art_id'];
        //if (!$art_id || ($art_id==1022 && !isset($_REQUEST['hhs']))) {
        if (!$art_id || C('REPLACEWORD_BUTTON') == 1) {
            return false;
        }
        // 文章修改
        $ArticleAddonModel = D('ArticleAddon');
        $art_body = $ArticleAddonModel->field('body')->where(array('art_id' => $art_id))->find();
        if (empty($art_body)) {
            return false;
        }
        //echo $art_body['body'];
        //$content = preg_replace('/<a class="f-blue hhs_tag" href=.+?news\.detail.+?target="_blank">(.+?)<\/a>/', '$1', $art_body['body'],2);
        $content = preg_replace('/<a class="f-blue hhs_tag" href=[^>]+?>(.+?)<\/a>/', '$1', $art_body['body']);

        // 获取品牌（有图片且有型号的才符合(品牌之家)）
        $Url = GenerateApiUrl('', 'footstone') . '/webapi/Handle_brand';
        $result = post_curl($Url);
        $result = json_decode($result, true);
        $result = $result['data'];
        $brandListArrFin = [];
        foreach ($result as $key => $value) {
            foreach ($value as $ke => $v) {
                if ($v['brand_logo']) {
                    $brandListArrFin[$v['brand_id']] = $v['brand_name'];
                }
            }
        }

        // 获取分类
        $SpuModel = D('Common/ZyGoods');
        $classifyArr = $SpuModel->table('lie_self_classify')->field('class_name,class_id')->where(array('status' => '1'))->select();
        foreach ($classifyArr as $key => $value) {
            $classifyArrFin[$value['class_id']] = $value['class_name'];
        }

        //替换指定的关键词,并按照长尾关键词优先
        $this->tag = $this->changeClass($classifyArrFin);

        $bigData = [];
        $bigData['product'] = ['猎芯网自营', '自营'];
        $bigData['liexin'] = ['电子元器件商城', '猎芯网电子元器件', '元器件商城', '电子元器件', '电子元件', '猎芯网', '元器件', '元件']; //猎芯网
//        $bigData['activity'] = ['活动'];
        $bigData['shop'] = ['商城'];
        $bigData['supplier'] = C('SUPPLIER_ART_NAME'); //供应商
        $bigData['classify'] = $classifyArrFin;
        $bigData['brand'] = $brandListArrFin;
        $linkNumber = $this->getLinkNumber($content);
        if ($linkNumber <= 3) {
            $arr = $this->filterWord($bigData, $content, $linkNumber);
        }
        //dump($arr);
        if (is_array($arr) && count($arr) < 2) { //型号 （由于型号数量已达两万多，所以后面匹配未完成时再单独获取循环）
            // 获取型号词库
            $ZyGoodsModel = D('Common/ZyGoods');
            $map['status'] = '1';
            $re = $ZyGoodsModel->table('lie_goods')->field('goods_name')->where($map)->select(); // 从x开始1000条

            foreach ($re as $key => $value) {
                $bigGoodsData['goods_name'][] = $value['goods_name'];
            }
            $arr1 = $this->filterWord($bigGoodsData, $content);
            if ($arr1) {
                $arr = array_merge($arr, $arr1);
            }

        }
        //echo json_encode($arr);
        //echo $content;exit;
        //dump($arr);exit;
//        $len = count($arr);
//        if($len==1){
//            //$content = preg_replace('/<a class="f-blue hhs_tag" href=.+?news\.detail.+?target="_blank">(.+?)<\/a>/', '$1', $content,2);
//
//            //$content = preg_replace($arr[0]['patterm'], $arr[0]['url'], $content,2);
//            $res = preg_match($arr[0]['patterm'],$content,$mc);
//            if($res>0){
//                $temp = array();
//                $temp['type'] = $arr[0]['type'];
//                $temp['word'] = $mc[0];
//                $temp['keyword'] = $arr[0]['keyword'];
//                $url = $this->getLinkTagByType($temp);
//                $content = preg_replace($arr[0]['patterm'], $url, $content,1);
//            }
//
//        }

        $artMap = array();
        $artMap['art_id'] = $art_id;
        $save['body'] = $content;
        $re = $ArticleAddonModel->where($artMap)->save($save);

        return $re;
    }

    //获取带有系统自动生成的超链接的个数
    protected function getLinkNumber($content)
    {
        return substr_count($content, '<a class="f-blue hhs_tag"');
    }

    /*
        替换指定的关键词,并按照长尾关键词优先
    */
    private function changeClass(&$class)
    {
        $SpuModel = D('ArticleTag');

        //获取指定要转换的
        $tag = $SpuModel->table('lie_article_tag')->field('tag,url')->where(array('art_id' => 0))->select();

        //把这些要转换的添加到id里面去,以便后面进行转换
        foreach ($tag as $key => $value) {
            //拆出来id
            $tmp = explode('-', $value['url']);
            if (!empty($tmp[1])) {
                //提取出对应的标签需要跳转的id值
                $tmp = explode('.', $tmp[1]);
                //判断$value['tag'],就是tag关键字是否重复,重复了就跳过
                $hasConflict = $this->checkHasConflictTag($value['tag'], $class);
                if (!$hasConflict) {
                    $class[$tmp[0]] = $value['tag'];
                }
            }
        }
        unset($tmp);

        //把长尾关键词排在前面
        foreach ($class as $key => $value) {
            unset($tmp);
            $tmp['k'] = $key;
            $tmp['v'] = $value;
            $tmp['len'] = strlen($value);

            $data[] = $tmp;
        }

        $data = arraySequence($data, 'len', 'SORT_DESC');

        $class = array();

        foreach ($data as $key => $value) {
            $class[$value['k']] = $value['v'];
        }

        return $tag;

    }

    //检查是否有和分类名称冲突的tag
    public function checkHasConflictTag($tag, $class = [])
    {
        foreach ($class as $k => $v) {
            if ($tag == $v) {
                return true;
            }
        }
        return false;
    }


    public function checkArticleArrNum($art_id)
    {
        $artInfo = S_article('pre_' . $art_id);
        $res = false;
        if (is_array($artInfo) && count($artInfo) == '1') {
            $res = '1';
        }
        if (is_array($artInfo) && count($artInfo) == '2') {
            $res = '2';
        }
        return $res;
    }

    // 筛选
    public function filterWord($bigData, &$content, $existedLinkNumber = 0)
    {

        $temp_word = '';
        $word_option = array();//return $word_option;
        $start = '';
        //$end = '(?!(?!<a class="f-blue hhs_tag" href=").+?<\/a>)';
        $end = '(?![^<]*(<\/a>|>))';
        $filterWordNumber = 0;
        foreach ($bigData as $key => $value) {
            foreach ($value as $k => $v) {
                if (mb_strlen($v) <= 1 || $v == '其它') {
                    continue;
                }
                if ($key !== 'supplier') {
//                    if($v == 'EUCHNER'){
//                        $flag = true;
//                        echo $content;
//                    }else{
//                        $flag = false;
//                    }
                    //$v = preg_replace('/(\+)|(\*)|(\?)|(\.)|(\/)/', '$1', $v);

                    if (strlen($v) <= 3) {
                        $v = '/' . $start . quotemeta($v) . $end . '/';//htmlspecialchars  addslashes
                    } else {
                        $arr = explode(' ', $v);
                        $pat = '';
                        foreach ($arr as $a => $b) {
                            if ($a == 0) {
                                $pat = quotemeta($b);
                            } else {
                                $pat .= '&nbsp;' . quotemeta($b);
                            }
                        }
                        $pat = str_replace('/', '\/', $pat);
                        $v = '/' . $start . $pat . $end . '/i';
                    }
                }
                $res = preg_match($v, $content, $mc);

                if ($res > 0 && $temp_word != $mc[0]) {
                    //最多只设置3个关键词
                    $maxFilterWordNumber = 3 - $existedLinkNumber;
                    //全部链接加起来只能有3个
                    if ($filterWordNumber == $maxFilterWordNumber || $maxFilterWordNumber <= 0) {
                        break 2;
                    }
                    $temp = array();
                    $temp['type'] = $key;
                    $temp['word'] = $mc[0];
                    $temp['keyword'] = $k;
                    $url = $this->getLinkTagByType($temp);
                    //这里面判断是否已经存在对应的关键字链接了
                    //如果存在,就不去替换了
                    if (substr_count($content, $url)) {
                        continue;
                    }
                    $temp['url'] = $url;
                    $temp['patterm'] = $v;
                    if (!empty($url)) {
                        $content = preg_replace($v, $url, $content, 1);
                        $filterWordNumber++;
                        if (!empty($content)) {
                            $word_option[] = $temp;
                            $temp_word = $temp['word'];
                            //相同的词最多添加几个锚文本控制
//                            if (count($word_option) > 1) {
//                                return $word_option;
//                            }
                        }
                    }

                }
            }
        }
        return $word_option;
    }


    public function getLinkTagByType($wordInfo)
    {
        // var_dump($wordInfo);
        // var_dump($this->tag);exit;

        //检查是否是其他的域名
        foreach ($this->tag as $key => $value) {
            if ($wordInfo['word'] == $value['tag']) {
                // var_dump($value);exit;
                // echo $value['url'];exit;
                $is_other = strstr($value['url'], 'www.ichunt.com');
                if ($is_other == false) {
                    $domain = explode('.com', $value['url']);
                    $domain = $domain[0] . '.com';
                } else {
                    $domain = WWW_DOMAIN;
                }

                break;
            }
        }


        switch (strval($wordInfo['type'])) {
            case 'liexin':
                $returnLink = $domain;
                break;
            case 'supplier':
                $returnLink = $domain . "/v3/activity/" . $wordInfo['keyword'] . "hd?ptag=news.detail";
                break;
            case 'brand':
                $returnLink = $domain . "/v3/brand/list/" . $wordInfo['keyword'] . ".html?ptag=news.detail";
                break;
            case 'classify':
                $returnLink = $domain . "/product-" . $wordInfo['keyword'] . ".html";
                break;
            case 'goods_name':
                $returnLink = $domain . "/s/?k=" . $wordInfo['word'] . "&kNums=&ptag=news.detail";
                break;
            case 'product':
                $returnLink = $domain . "/product.html";
                break;
            case 'activity':
                $returnLink = $domain . "/v3/activity/activityhz.html";
                break;

            case 'shop':
                $returnLink = $domain;
                break;

            default:
                # code...
                break;
        }
        $returnLinkFin = '<a class="f-blue hhs_tag" title="' . $wordInfo['word'] . '" href="' . $returnLink . '" target="_blank">' . $wordInfo['word'] . '</a>';
        return $returnLinkFin ? $returnLinkFin : '';
    }
}