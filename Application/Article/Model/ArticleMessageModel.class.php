<?php
/**
 * Created by 2023/3/10.
 * User: Jon<PERSON>
 * Info: 2023/3/10
 * Time: 下午2:26
 */

namespace Article\Model;


use Think\Model;

class ArticleMessageModel extends Model
{

    public function getCount($where = '')
    {
        $datas = $this->where($where)->count();
        return $datas;
    }


    public function getList($where = '', $page = '', $order = 'is_top DESC,reply_time DESC')
    {
        list($p, $limit) = explode(',', $page);
        empty($p) && $p = 1;
        empty($limit) && $limit = C('DEFAULT_PAGE_LIMIT');
        $datas = $this->where($where)->page($p, $limit)->order($order)
            ->field('user_account, content, reply_status, reply_content,reply_time, is_top')
            ->select();
        return $datas;
    }

}