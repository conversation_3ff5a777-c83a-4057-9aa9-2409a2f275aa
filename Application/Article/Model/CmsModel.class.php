<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace Article\Model;
use Think\Model;
/**
 * Description of CmsModel
 *
 * <AUTHOR>
 */
class CmsModel extends Model{
    protected $autoCheckFields = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.CMS');
    }

    /**
     * 获取供应商的广告营销词
     * @param  [type] $supplier_id [description]
     * @return [type]              [description]
     */
    public function getData($table,$where,$field='*',$limit='',$order=''){
        $datas = $this->table($table)->where($where)->limit($limit)->order($order)->field($field)->select();
        return $datas;
    }
    
    /**
     * 获取基础分类ID
     * @param  [type] $tags  [description]
     * @param  string $field [description]
     * @return [type]        [description]
     */
    public function getBaseCat($tags, $field = 'bcat_id')
    {
        static $bcat;
        if (!isset($bcat[$tags])) {
            $map = array(
                'tags' => $tags,
            );
            $bcat[$tags] = $this->table('lie_base_cat')->where($map)->field('bcat_id, bcat_name')->find();
        }
        return $bcat[$tags][$field];
    }

    /**
     * 获取配置分类值
     * @param  [type] $tags  [description]
     * @param  string $where [description]
     * @param  string $limit [description]
     * @return [type]        [description]
     */
    public function getBaseList($tags, $where = '', $limit = '')
    {
        $bcat_id = $this->getBaseCat($tags);
        $map = array(
            'bcat_id' => $bcat_id,
            '_string' => "(status = 1) OR (status = 2 AND open_time >= {$_SERVER['REQUEST_TIME']} AND close_time <= {$_SERVER['REQUEST_TIME']})",
        );
        $map = $this->formatwhere($map, $where);
        $datas = $this->table('lie_base')->where($map)->limit($limit)->order('sort DESC')->field('*')->select();
        return $datas;
    }
    private function formatwhere($map, $where)
    {
        if (is_string($where)) {
            if (!empty($where)) {
                $where = array('_string' => $where);
            } else {
                $where = array();
            }
        }
        $map = array_merge($map, $where);
        return $map;
    }
}
