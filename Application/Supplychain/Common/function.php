<?php
if(!function_exists('s_import_excel')){
    function s_import_excel($file){
        Vendor('PHPExcel.PHPExcel');    //包的存放是\ThinkPHP\Extend\Vendor\PHPExcel
        // 判断文件是什么格式
        $type = pathinfo($file);
        $type = strtolower($type["extension"]);
        $type=$type==='csv' ? $type : 'Excel2007';     //这里需要与格式对应
        ini_set('max_execution_time', '0');

        // 判断使用哪种格式
        $objReader = \PHPExcel_IOFactory::createReader($type);
        $objPHPExcel = $objReader->load($file);
        $sheet = $objPHPExcel->getSheet(0);
        // 取得总行数
        $highestRow = $sheet->getHighestRow();
        // 取得总列数
        $highestColumn = $sheet->getHighestColumn();
        //循环读取excel文件,读取一条,插入一条

        $allColumn = $sheet->getHighestColumn();        //**取得最大的列号*/
        $allRow = $sheet->getHighestRow();        //**取得一共有多少行*/
        $ColumnNum = \PHPExcel_Cell::columnIndexFromString($allColumn);     // 列号 转 列数

        $data = array();
        for($rowIndex=1;$rowIndex<=$allRow;$rowIndex++){        //循环读取每个单元格的内容。注意行从1开始，列从A开始
            for($colIndex=0;$colIndex<=$ColumnNum;$colIndex++){
                $data[$rowIndex][] =(string)$sheet->getCellByColumnAndRow($colIndex, $rowIndex)->getFormattedValue();
            }
        }
        return $data;
    }
}
