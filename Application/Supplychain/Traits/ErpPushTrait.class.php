<?php

namespace Supplychain\Traits;

use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Exception\ErpException;
use Supplychain\Model\BaoGuanOrderListModel;
use Supplychain\Model\BaoGuanOrderModel;
use Supplychain\Model\HKDeliveryNoteListModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderStatusModel;
use Supplychain\Model\SZDeliveryNoteListModel;
use Supplychain\Model\SZShipmentsNoteListModel;


trait ErpPushTrait
{


    /*
 * 获取到正确的手机号
 */
    public function getMobile($mobile){
        if(strlen($mobile) > 11){
            $_mobile = substr($mobile,intval(strlen($mobile)-11),11);
            $intl_code = substr($mobile,0,intval(strlen($mobile)-11));
            $intl_code = $intl_code ? trim($intl_code,"-") : '';
            return [$intl_code,$_mobile];
        }
        return ['0086',$mobile];
    }


    /*
     * 获取到正确的座机号
     */
    public function getPhone($phone){
        return ['',$phone];
        if(strlen($phone) > 8){
            $_phone = substr($phone,intval(strlen($phone)-8),8);
            $intl_code = substr($phone,0,intval(strlen($phone)-8));
            $intl_code = $intl_code ? trim($intl_code,"-") : '';
            return [$intl_code,$_phone];
        }
        return ['',$phone];
    }


    /*
     * 修改订单状态
     * $data
     */
    public function changeOrderStatus($order_id,$type=''){
        try{
            if(!$type)return false;
            $_model = [
                'baoguan_status'=>new BaoGuanOrderListModel(),//报关
                'hk_delivery_status'=> new HKDeliveryNoteListModel(),//香港收货
                'sz_delivery_status'=>new SZDeliveryNoteListModel(),//深圳收货
                'sz_send_status'=>new SZShipmentsNoteListModel(),//深圳发货
            ];
            $orderGoodsNums = OrderGoodsModel::where("order_id",$order_id)->where("status",OrderGoodsModel::$PASS_STATUS)->sum('numbers');
            if(!$orderGoodsNums) return false;

            $model = $_model[$type];
            $nums = $model->getGoodsNums($order_id);
            $nums  = $nums ? $nums : 0;
            $value = 0;
            if($nums < $orderGoodsNums && $nums > 0){
                $value = 1;
            }elseif($nums >= $orderGoodsNums){
                $value = 2;
            }
            $orderInfo = OrderModel::where("order_id",$order_id)->select("user_id","company_id")->first();
            if(!$orderInfo) throw new ErpException('修改订单状态失败');
            $bk = OrderStatusModel::updateOrCreate(['order_id'=>$order_id],[
                $type=>$value,
                'user_id'=>$orderInfo->user_id,
                'company_id'=>$orderInfo->company_id,
            ]);
            if($bk === false) throw new ErpException('修改订单状态失败');
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("修改订单状态失败");
        }
    }




}