<?php

namespace Supplychain\Traits;

use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Exception\ErpException;
use Supplychain\Model\BaoGuanOrderListModel;
use Supplychain\Model\HKDeliveryNoteListModel;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Message\MessageModel;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\SZDeliveryNoteListModel;
use Supplychain\Model\SZDeliveryNoteModel;
use Supplychain\Model\SZShipmentsNoteListModel;
use Supplychain\Model\UserMessageModel;


trait SenMsgTrait
{
    public $sendMsg=[
        1=>[
            1=>'orderAuditTrait',
            2=>'hkDeliveryTrait',
            3=>'hkBaoGuanTrait',
            4=>'szDeliveryTrait',
            5=>'szSendTrait',
        ],
        2=>[
            1=>'orderAuditTraitEmail',
            2=>'hkDeliveryTraitEmail',
            3=>'hkBaoGuanTraitEmail',
            4=>'szDeliveryTraitEmail',
            5=>'szSendTraitEmail',
        ]
    ];

    public $order_audit_duanxin_key = 'supply-duanxin-order';
    public $order_email_key = 'supply-email-order';

    public $cache_send_msg_time = 3600;


    public function getComId($data){
        if(isset($data['erp_order_sn'])){
            $user = OrderModel::where("erp_order_sn",trim($data['erp_order_sn']))->select('user_id','company_id')->firstOrFail();
            return $user->company_id ? $user->company_id : false;
        }
        return false;
    }

    /*
     * 发送消息
     * $custom_func
     *   1=>'订单审核',
     *   2=>'香港收货',
     *   3=>'香港报关',
     *   4=>'深圳收货',
     *   5=>'深圳发货',
     *
     * $data[erp_order_sn]
     */
    public function sendMsg($custom_func,$data){

        \Think\Log::write("供应链消息推送:".$custom_func.'推送入仓号'.$data['erp_order_sn'],'WARN');

        try{
            $company_id = $this->getComId($data);
            if(!in_array($custom_func,array_keys(UserMessageModel::$CUSTOM_FUN))) throw new \Exception('消息发送类型未知');
            $list = UserMessageModel::where("company_id",$company_id)->whereRaw("FIND_IN_SET({$custom_func},custom_fun)")->select("send_user","type")->get()->toArray();
//            dump($list);
            if(empty($list)) {
                \Think\Log::write("供应链消息推送:".$custom_func.'用户查询失败'.$data['erp_order_sn'],'WARN');
                return true;
            }
            $order = OrderModel::where("erp_order_sn",trim($data['erp_order_sn']))->select('user_id','company_id',"order_id","order_sn","erp_order_sn","status",'hongkong_delivery_type')->firstOrFail();
            $data['order'] = $order;
            foreach($list as $item){
                $func = $this->sendMsg[$item['type']][$custom_func];
//                dump($func);
                call_user_func_array([$this,$func],['obuser'=>$item['send_user'],'data'=>$data]);
            }
            return true;
        }catch(\Exception $e){
//            dump($e->getMessage());
            \Think\Log::write("供应链消息推送:".$e->getMessage(),'WARN');
            return false;
        }
    }


    /**
     * 推送短信服务
     */
    protected function supply_push_duanxin($obuser,$data,$contents){
        if (!CommonLogic::isPhoneNumberValid($obuser)){
            return [];
        }
        CommonLogic::sendDuanXin(['contents'=>$contents],$obuser,$this->order_audit_duanxin_key,1);
    }

    protected function supply_push_duanxin_old($obuser,$data,$contents){
        //恢复短信提醒 20220726

        $tpl_info = MessageModel::where("description",$this->order_audit_duanxin_key)->select('tpl_id')->first();

        $params['contents'] = $contents;
        $_data['is_oversea'] = false;
        $_data['template_id'] = $tpl_info['tpl_id'];
        $_data['keyword'] = $this->order_audit_duanxin_key;
        $_data['channel_type'] = 2;
        $_data['touser'] = strval($obuser);
        $_data['data'] = $params;
        $_data['url'] = '';
        $_data['wechat_data'] = null;
        $_data['is_ignore'] = 1;
        $_data['ex_int'] = 0;
        $_data['ex_str'] = "";
        $_data['pf'] = "20";
        $_data['fromuser'] = "";
        $_data['delay'] = 0;
        $_data['rbmq'] = [
            'expire_time'=>time()+3600*2
        ];
//        dump($_data);
        return $this->onQueue($_data);
    }


    /**
     * 推送邮件服务
     */

    protected function supply_push_email($obuser,$data,$contents){
        CommonLogic::sendDuanXin(['contents'=>$contents],$obuser,$this->order_email_key);
    }

    protected function supply_push_email_old($obuser,$data,$contents){

        $tpl_info = MessageModel::where("description",$this->order_email_key)->select('tpl_id')->first();

        $params['contents'] = $contents;
        $_data['is_oversea'] = false;
        $_data['template_id'] = $tpl_info['tpl_id'];
        $_data['keyword'] = $this->order_email_key;
        $_data['channel_type'] = 3;
        $_data['touser'] = strval($obuser);
        $_data['data'] = $params;
        $_data['url'] = '';
        $_data['wechat_data'] = null;
        $_data['is_ignore'] = 1;
        $_data['ex_int'] = 0;
        $_data['ex_str'] = "";
        $_data['pf'] = "20";
        $_data['fromuser'] = "";
        $_data['delay'] = 0;
        $_data['rbmq'] = [
            'expire_time'=>time()+3600*2
        ];
//        dump($_data);
        return $this->onQueue($_data);
    }


    /*
     * 订单审核
     */
    protected function orderAuditTrait($obuser,$data){
        try{
            $order = $data['order'];
            $contents = sprintf("您的入仓单【%s】已通过审核!",$order->erp_order_sn);
            $this->supply_push_duanxin($obuser,$data,$contents);
        }catch(\Exception $e){
           throw new \Exception($e->getMessage());
        }
    }


    /*
     * 订单审核
     * 邮件发送
     *
     */
    protected function orderAuditTraitEmail($obuser,$data){
        try{
            $order = $data['order'];
            $contents = sprintf("您的入仓单【%s】已通过审核!",$order->erp_order_sn);
            $this->supply_push_email($obuser,$data,$contents);
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }

    protected function getOrderGoodsNums($order){
        if(!S('supply_order_goods_nums'.$order->order_id)){
            $order_goods_nums = OrderGoodsModel::where("order_id",$order->order_id)->where("status",OrderGoodsModel::$PASS_STATUS)->sum('numbers');
            S('supply_order_goods_nums'.$order->order_id,$order_goods_nums,['expire'=>$this->cache_send_msg_time]);
        }

        return S('supply_order_goods_nums'.$order->order_id);
    }


    /*
     * 香港收货
     */
    protected function hkDeliveryTrait($obuser,$data){
        try{
            $order = $data['order'];
            $order_goods_nums = $this->getOrderGoodsNums($order);
            $nums = HKDeliveryNoteListModel::where("order_id",$order->order_id)->sum('qty');
            if($order_goods_nums == 0) throw new \Exception('订单暂时没有审核通过的商品');

            //修改发送短信内容，20220726
            $contents = '您的入仓号:'.$order->erp_order_sn.' 交货方式:'.array_get(OrderModel::$HongkongDeliveryType,$order->hongkong_delivery_type).' 总件数:'.$nums.' 已到达香港仓库，请知悉';

            $this->supply_push_duanxin($obuser,$data,$contents);
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }

    /*
     * 香港收货
     * 邮件
     */
    protected function hkDeliveryTraitEmail($obuser,$data){
        try{
            $order = $data['order'];
            $order_goods_nums = $this->getOrderGoodsNums($order);
            $nums = HKDeliveryNoteListModel::where("order_id",$order->order_id)->sum('qty');
            if($order_goods_nums == 0) throw new \Exception('订单暂时没有审核通过的商品');
            if($nums < $order_goods_nums){
                $contents = sprintf("您的入仓单【%s】香港部分收货!",$order->erp_order_sn);
            }else{
                $contents = sprintf("您的入仓单【%s】香港完全收货!",$order->erp_order_sn);
            }
            $this->supply_push_email($obuser,$data,$contents);
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }


    /*
     * 香港报关
     * 短信
     */
    protected function hkBaoGuanTrait($obuser,$data){
        try{
            $order = $data['order'];

            //报关短信更新20220726
            $contents = '您的入仓号:'.$order->erp_order_sn.' 香港仓库装载完成即将报关，请知悉';
            $this->supply_push_duanxin($obuser,$data,$contents);
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }

    /*
     * 香港报关
     * 邮件
     */
    protected function hkBaoGuanTraitEmail($obuser,$data){
        try{
            $order = $data['order'];
            $order_goods_nums = $this->getOrderGoodsNums($order);
            $nums = BaoGuanOrderListModel::where("order_id",$order->order_id)->where("status",1)->sum('qty');
            if($order_goods_nums == 0) throw new \Exception('订单暂时没有审核通过的商品');
            if($nums < $order_goods_nums){
                $contents = sprintf("您的入仓单【%s】部分报关!",$order->erp_order_sn);
            }else{
                $contents = sprintf("您的入仓单【%s】完全报关!",$order->erp_order_sn);
            }
            $this->supply_push_email($obuser,$data,$contents);
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }


    /*
     * 深圳收货
     * 短信
     */
    protected function szDeliveryTrait($obuser,$data){
        try{
            $order = $data['order'];
            //更新短信内容20220726
            $contents = '您的入仓号:'.$order->erp_order_sn.' 已在到达深圳仓库，请知悉';
            $this->supply_push_duanxin($obuser,$data,$contents);
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }

    /*
     * 深圳收货
     * 邮件
     */
    protected function szDeliveryTraitEmail($obuser,$data){
        try{
            $order = $data['order'];
            $order_goods_nums = $this->getOrderGoodsNums($order);
            $nums = SZDeliveryNoteListModel::where("order_id",$order->order_id)->sum('qty');
            if($order_goods_nums == 0) throw new \Exception('订单暂时没有审核通过的商品');
            if($nums < $order_goods_nums){
                $contents = sprintf("您的入仓单【%s】深圳部分收货!",$order->erp_order_sn);
            }else{
                $contents = sprintf("您的入仓单【%s】深圳完全收货!",$order->erp_order_sn);
            }
            $this->supply_push_email($obuser,$data,$contents);
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }


    /*
     * 深圳发货
     */
    protected function szSendTrait($obuser,$data){
        try{
            $order = $data['order'];
            $order_goods_nums = $this->getOrderGoodsNums($order);
            $nums = SZShipmentsNoteListModel::where("order_id",$order->order_id)->sum('qty');
            if($order_goods_nums == 0) throw new \Exception('订单暂时没有审核通过的商品');

            $szShipmentInfo = SZDeliveryNoteModel::where("order_id",$order->order_id)->first()->toArray();
            //尊敬的客户:您 B93161/B93162 订单货物已由$_快递类型发出，物流单号$_logisticCode，收件人$_receiverName，$_面单件数，请留意收货
            $contents = '尊敬的客户:您 '.$szShipmentInfo['erp_order_sn'].' 订单货物已由'.$szShipmentInfo['carrier'].'发出，物流单号'.$szShipmentInfo['stream_number'].'，收件人'.$szShipmentInfo['attention'].'，'.$szShipmentInfo['count'].'，请留意收货';
//            if($nums < $order_goods_nums){
//                $contents = sprintf("您的入仓单【%s】深圳部分发货!",$order->erp_order_sn);
//            }else{
//                $contents = sprintf("您的入仓单【%s】深圳完全发货!",$order->erp_order_sn);
//            }
            $this->supply_push_duanxin($obuser,$data,$contents);
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }

    /*
     * 深圳发货
     * 邮件
     */
    protected function szSendTraitEmail($obuser,$data){
        try{
            $order = $data['order'];
            $order_goods_nums = $this->getOrderGoodsNums($order);
            $nums = SZShipmentsNoteListModel::where("order_id",$order->order_id)->sum('qty');
            if($order_goods_nums == 0) throw new \Exception('订单暂时没有审核通过的商品');

            $szShipmentInfo = SZDeliveryNoteModel::where("order_id",$order->order_id)->first()->toArray();
            //尊敬的客户:您 B93161/B93162 订单货物已由$_快递类型发出，物流单号$_logisticCode，收件人$_receiverName，$_面单件数，请留意收货
            $contents = '尊敬的客户:您 '.$szShipmentInfo['erp_order_sn'].' 订单货物已由'.$szShipmentInfo['carrier'].'发出，物流单号'.$szShipmentInfo['stream_number'].'，收件人'.$szShipmentInfo['attention'].'，'.$szShipmentInfo['count'].'，请留意收货';

//            if($nums < $order_goods_nums){
//                $contents = sprintf("您的入仓单【%s】深圳部分发货!",$order->erp_order_sn);
//            }else{
//                $contents = sprintf("您的入仓单【%s】深圳完全发货!",$order->erp_order_sn);
//            }
            $this->supply_push_email($obuser,$data,$contents);
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }






    //普通消息队列
    public function onQueue($data=[]){
        try {
            // 入队
            $RbmqModel = new \Supplychain\Model\SupplyRbmqModel;
            $push_data = array(
                'job' => 'api.supply.all',
                'data' => json_encode($data)
            );
            $mq= $RbmqModel->exchange(C("EXCHANGE_NAME_MESSAGE"))->queue(C("QUEUE_MESSAGE_SUPPLY"))
                ->exchangeBind(C('EXCHANGE_NAME_MESSAGE'), C('QUEUE_MESSAGE_SUPPLY'));
            //        dump($a);
            $bk = $mq->push($push_data,C("QUEUE_MESSAGE_SUPPLY"));
            if(!$bk) throw new \Exception('插入队列对失败');
            return true;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('插入队列对失败');
        }
    }

}