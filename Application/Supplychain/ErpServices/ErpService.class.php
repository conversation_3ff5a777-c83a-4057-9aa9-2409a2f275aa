<?php
namespace  Supplychain\ErpServices;
use Exception;
use GuzzleHttp\Client;
use Supplychain\Model\ActionLogModel;
use Supplychain\Model\CmsModel;
use Supplychain\Model\CustomsCarLoadingModel;
use Supplychain\Model\HongKongDeliveryModel;
use Supplychain\Model\InlandDeliveryModel;
use Supplychain\Model\Logic\OrderLogic;
use Supplychain\Model\Logic\WmsLogic\OrderTrackingLogLogic;
use Supplychain\Model\OrderAddressModel;
use Supplychain\Exception\ErpException;

use Supplychain\Model\BaoGuanOrderListModel;
use Supplychain\Model\BaoGuanOrderModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\ComInvoiceModel;
use Supplychain\Model\ContactModel;
use Supplychain\Model\HKDeliveryNoteListModel;
use Supplychain\Model\HKDeliveryNoteModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\OrderTrackingLogModel;
use Supplychain\Model\PayCostModel;
use Supplychain\Model\PayCostListModel;
use Supplychain\Model\PaymentApplyModel;
use Supplychain\Model\SupplierBankModel;
use Supplychain\Model\SZDeliveryNoteListModel;
use Supplychain\Model\SZDeliveryNoteModel;
use Supplychain\Model\SZShipmentsNoteListModel;
use Supplychain\Model\SZShipmentsNoteModel;
use Supplychain\Model\UserDeliveryModel;
use Supplychain\Model\SupplierModel;
use Supplychain\Model\CountryModel;
use Supplychain\Model\ProductModel;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderRecordModel;
use Supplychain\Model\ServiceAgreementListModel;
use Supplychain\Model\ServiceAgreementModel;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\ReceiptModel;
use Supplychain\Model\VerificationModel;
use Supplychain\Model\GoodsRegisterModel;
use Supplychain\Model\WmsTallyDetailModel;
use Supplychain\Model\WmsTallyModel;
use Supplychain\Model\WmsTodayTrayModel;
use Supplychain\Model\WmsTrayBoxScanDetailModel;
use Supplychain\Service\DingNotify;

/**
 * ERP相关数据服务
 */
class ErpService
{
    use \Supplychain\Traits\ErpPushTrait;
    use \Supplychain\Traits\SenMsgTrait;

    //平台对应erp的 货到付款方式
    public static $GOODSPAY=[
        1=>0,//货前
        3=>1,//货到
        2=>2,//货后
    ];


    //平台对应erp的 货到付款方式
    public static $ERPGOODSPAY=[
        0=>1,//货前
        1=>3,//货到
        2=>2,//货后
    ];

    public function __construct()
    {
    }


    /**
     * 客户资料 委托方 开票 联系人信息推送
     * 推送字段
     * https://www.tapd.cn/21461081/prong/stories/view/1121461081001002458
     */
    public function pushCustomerInfo($user_id){
        try{
            $data = [];
            if(!$user_id) throw  new ErpException("用户不存在");
            $user_id = intval($user_id);

            //客户资料
            $customer = CustomerModel::where("user_id",$user_id)->select("customer_id","customer_name","mnemonic_code","customer_short_name","follow_people","company_id")->firstOrFail();
            if(!$customer) throw  new ErpException("客户资料不存在");
//            $data['CUSTOMER'] = $customer->customer_name ? $customer->customer_name :'';//客户名
            $data['HELPCODE'] = $customer->mnemonic_code ? $customer->mnemonic_code :'';//委托方的助记码
            $data['SIMPLENAME'] = $customer->customer_short_name ? $customer->customer_short_name :'';//简称 <客户、委托方>
            $data['CUSTOMEFOLLOWER'] = $customer->follow_people ? $customer->follow_people :'';//商务


            //公司信息
            $com = CompanyModel::where("company_id",$customer->company_id)
                ->select("company_id","organization_code","company_full_name","unified_social_credit_code","registered_capital","legal_person","trade_registration")
                ->firstOrFail();
            if(!$com) throw  new ErpException("公司信息不存在");
            $data['PTID'] = strval($com->company_id);
            $data['ORGANIZATCODE'] = $com->organization_code ? $com->organization_code :'';//组织机构代码
            $data['UNIFYCREDITCODE'] = $com->unified_social_credit_code ? $com->unified_social_credit_code :'';//统一社会信用代码
            $data['REGISTCAPITAL'] = $com->registered_capital ? $com->registered_capital :'';//委托方的注册资金
            $data['CORPORATER'] = $com->legal_person ? $com->legal_person :'';//委托方的法人
            $data['ICNUMBER'] = $com->trade_registration ? $com->trade_registration :'';//委托方的工商注册号
            $data['CUSTOMER'] = $com->company_full_name ? $com->company_full_name :'';//客户名


            //发票信息
            $comInvoice = ComInvoiceModel::where("company_id",$customer->company_id)
                ->select("invoice_address","invoice_mobile","tax_certificate_no","bank_name","bank_number")->firstOrFail();
            if(!$comInvoice) throw  new ErpException("发票信息不存在");
            $data['INVOICEDES'] = '';//开票备注
            $data['ADDRESS'] = $comInvoice->invoice_address ? $comInvoice->invoice_address :'';//开票地址
            $data['INVOICEPHONE'] = $comInvoice->invoice_mobile ? $comInvoice->invoice_mobile :'';//开票电话
            $data['FTXREGISTERNO'] = $comInvoice->tax_certificate_no ? $comInvoice->tax_certificate_no :'';//纳税登记证号
            $data['INVOICEBANK'] = $comInvoice->bank_name ? $comInvoice->bank_name :'';//开票银行
            $data['INVOICEACCOUNT'] = $comInvoice->bank_number ? $comInvoice->bank_number :'';//开票银行账号


            //收货地址联系人 列表
            $deliverys = UserDeliveryModel::where("company_id",$customer->company_id)->where("status",">=",0)->get();
            $data['RECEIVING'] = [];
            foreach($deliverys as $k=>$item){
                $data['RECEIVING'][$k]['PTENTRYID'] = strval($item->com_delivery_id);//本地主键
                $data['RECEIVING'][$k]['CONTACTER'] = $item->consignee;//收货人姓名
                $data['RECEIVING'][$k]['PHONE'] = $item->mobile;//手机号
                $data['RECEIVING'][$k]['MOBILE'] = $item->seat_number;//座机号
                $city_info = (new \Supplychain\Repository\UserRepository)->getRegionInfo($item->province,$item->city,$item->district,$item->address_area);
                $data['RECEIVING'][$k]['SADDRESS'] = $city_info." ".$item->detail_address;//详细地址
            }

            //业务联系人
            $data['BUSINESS'] = [];
            $contact = ContactModel::where("company_id",$customer->company_id)->select("contact_id","contact_name","contact_phone","special_plane")->first();
            if(!$contact) throw  new ErpException("业务联系人不存在");
            $data['BUSINESS'][0]['PTENTRYID'] = strval($contact->contact_id);
            $data['BUSINESS'][0]['PHONE'] = $contact->contact_phone;
            $data['BUSINESS'][0]['MOBILE'] = $contact->special_plane;
            $data['BUSINESS'][0]['CONTACTER'] = $contact->contact_name;
            return $data;
        }catch(ErpException $e){
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
            return false;
        }
    }

    public function checkBoxPushed($data){
        return !WmsTallyDetailModel::where('box_sn', $data['box_sn'])->where('sync_status', '!=', 3)->exists();
    }
    public function sendWechatMsg($data){
        $client = new Client();
        if (isset($data['url']) && !empty($data['url'])) {
            $client->request('post', $data['url'],[
                'json'=>[
                    "msgtype"=>"text",
                    "text"=>[
                        "content"=>$data['msg']
                    ]
                ]
            ]);
        } else {
            $client->request('post', 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=80874d10-85d7-4e36-aa08-ca2dca961d24',[
                'json'=>[
                    "msgtype"=>"text",
                    "text"=>[
                        "content"=>$data['msg'],
                        "mentioned_mobile_list"=>[(new CmsModel())->getData('user_info',['name'=>$data['user_name']],'find','mobile')['mobile']],
                    ]
                ]
            ]);
        }
        return 0;
    }

    /**
     * 同步客户资料成功后回写数据
     */
    public function updateCustomerInfo($company_id,$data){
        return CustomerModel::where("company_id",$company_id)->update($data);
    }

    /**
     * 获取装载卡板数
     * @param $data
     * @return array
     */
    public function getPlateQty($data){
        $loadedPlateQtyCache = [];
        foreach($data as $goods_no) {
            $entrust_nos = CustomsCarLoadingModel::where('load_goods_no', $goods_no)
                ->where('status', 1)
                ->groupBy(['entrust_no'])
                ->pluck('entrust_no')->toArray();
            if (!empty($entrust_nos)) {
                $entrust_nos = array_unique($entrust_nos);
                foreach ($entrust_nos as &$value){
                    $value =  str_replace('-','#',$value);
                }
                $loadedPlateQtyCache[$goods_no] = count(WmsTrayBoxScanDetailModel::where('is_del',0)->where('is_scan',1)->where('wstyty_id','!=',0)
                    ->whereIn('erp_order_sn',$entrust_nos)->groupBy(['wstyty_id'])->pluck('wstyty_id')->toArray());
            }
        }
        return $loadedPlateQtyCache;
    }

    /**
     * 同步客户资料成功后回写数据
     * 回写收货地址信息
     */
    public function updateCustomerDelivery($data){
        return UserDeliveryModel::where("com_delivery_id",$data['PTENTRYID'])->update(['erp_entry_id'=>$data['ENTRYID']]);
    }

    /**
     * 同步客户资料成功后回写数据
     * 回写客户联系人
     */
    public function updateCustomerContact($data){
        return ContactModel::where("contact_id",$data['PTENTRYID'])->update(['erp_entry_id'=>$data['ENTRYID']]);
    }


    /**
     * 同步供应商数据
     *
     */
    public function pushSupply($user_id,$supplier_id){
        try{
            $data = [];
            if(!$user_id) throw  new ErpException("用户不存在");
            $user_id = intval($user_id);
            $company_id = CustomerModel::where("user_id",$user_id)->value(company_id);
            if(!$company_id) throw  new ErpException("没找到用户对应的公司信息");
            $suppliers = SupplierModel::where("company_id",$company_id)->where("supplier_id",$supplier_id)
                ->select("supplier_id","supplier_name","supplier_short_name","supplier_id",'supplier_address','supplier_file')->firstOrFail();
//            if(!$suppliers->supplier_banks) throw new ErpException("没找到供应商对应的银行信息");
//            $banks = $suppliers->supplier_banks->where("status",">",0)->get()->toArray();
//            if(empty($banks)) throw new ErpException("没找到供应商对应的银行信息");
            $data['SIMPLENAME'] = $suppliers->supplier_short_name ? $suppliers->supplier_short_name : '';
            $data['SUPPLIER'] = $suppliers->supplier_name ? htmlspecialchars_decode($suppliers->supplier_name) : '';
            $data['PTID'] = strval($suppliers->supplier_id);
            $data['address'] = $suppliers->supplier_address;
            $data['attachmentUrl'] = $suppliers->supplier_file;

//            foreach($banks as $key=>$item){
//                $data['BANKS'][$key]['PTENTRYID'] = strval($item['supplier_bank_id']);
//                $data['BANKS'][$key]['BANK'] = $item['bank_name'] ? $item['bank_name'] : '';
//                $data['BANKS'][$key]['BANKADDRESSS'] = $item['bank_address'] ?  $item['bank_address'] : '';
//                $data['BANKS'][$key]['SWIFCODE'] =$item['swift_code'] ? $item['swift_code'] : '';
//                $data['BANKS'][$key]['DESTINA'] = '';
//                $data['BANKS'][$key]['COUNTRY'] = $item['recipient_country'] ? $item['recipient_country'] : '';
//                $data['BANKS'][$key]['BANKACCOUNT'] = $item['bank_account'] ? $item['bank_account'] :'';
//                $data['BANKS'][$key]['RECEIVERNAME'] = $data['SIMPLENAME'];
//            }
            return $data;

        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
//            return false;
        }
    }


    /**
     * 推送物料数据给 诚意（后台php）
     */
    public function pushGoodsInfoToCY($data){
        try{
            $arr['goods_name'] = $data['goods_type'];//型号
            $arr['brand_name'] = $data['brand'];//品牌
            $arr['goods_title'] = $data['goods_title'];//品名
            $arr['goods_id'] = $data['goods_id'];//品名
            $arr['create_uid'] = $data['user_id'];

//            $user_name = (new \Supplychain\Model\UserMainModel)->where("user_id",$data['user_id'])->value("mobile");
//            $arr['create_name'] = $user_name ? $user_name : '';
//            $arr['create_account'] = $user_name ? $user_name : '';
//            $company = CompanyModel::getCompany($data['user_id']);

//            $user_name = (new \Supplychain\Model\UserMainModel)->where("user_id",$data['user_id'])->value("mobile");
            $customer = CustomerModel::where('user_id',$data['user_id'])->select("company_id","customer_name")->first();
            $arr['create_name'] = $customer ? $customer->customer_name : '';
            $arr['create_account'] = $customer ? $customer->customer_name : '';
            $company = CompanyModel::getCompany($customer->company_id);

            $arr['entrust_name'] = $company ? $company->company_full_name : '';//公司
            vendor('Hprose.HproseHttpClient');
            $client = new \HproseHttpClient(C('pushGoodsUrl'));
            $bk = $client->AddCustomsItems(json_encode([$arr]));
            \Think\Log::write($bk,'WARN');
            $bk = json_decode($bk,true);
            return ($bk['err_code'] == 0) ? true : false;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }

    }

    /**
     * 更新供应商 erp关联数据
     */
    public function syncSupplyErp($data){
        return SupplierModel::where("supplier_id",$data['supplier_id'])->update([
            'erp_supplier_id'=>$data['ID'],
            'erp_supplier_code'=>$data['NUMBER'],
        ]);
    }

    /**
     * 更新供应商银行 erp关联数据
     */
    public function syncSupplyBankErp($data){
        return SupplierBankModel::where("supplier_bank_id",$data['PTENTRYID'])->update([
            'erp_entry_id'=>$data['ENTRYID'],
        ]);
    }


    /**
     * 回写物料数据
     * 海关编码 物料号 税率 监管条件
     */
    public function syncGoodsInfo($arr){
        try{
            \Think\Log::write("接收物料数据推送服务",'WARN');
            foreach($arr as $item){
                \Think\Log::write(json_encode($item),'WARN');
                DB::connection('SUPPLYCHAIN')->transaction(function()use($item){
                    $goods = ProductModel::where("goods_id",$item['goods_id'])
                        ->update([
                            'goods_title'=>$item['goods_title'],
                            'brand'=>$item['brand_name'],
                            'material_sn'=>$item['material_sn'],
                            'customs_code'=>$item['number'],
                            'tariff_rate'=>$item['tax_rate_low'],//关税
                            'tariff_rate_land'=>$item['tax_rate_land'],//产地税
                            'regulatory_condition'=>$item['supervision_con'],//监管条件
                            'status'=>5,
                        ]);
                    if($goods === false) throw new \Exception('同步物料数据失败');

//                    $orderGoods = OrderGoodsModel::where("goods_id",$item['goods_id'])
//                        ->update([
//                            'goods_title'=>$item['goods_title'],
//                            'brand'=>$item['brand_name'],
//                            'erp_material_sn'=>$item['material_sn'],
//                            'customs_code'=>$item['number'],
//                            'tariff_rate'=>$item['tax_rate_low'],
//                            'regulatory_condition'=>$item['supervision_con'],
//                        ]);
//                    if($orderGoods === false) throw new \Exception('同步物料数据失败');
                });

            }
            return true;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    /*
     * 获取供应商信息
     */
    public function getSupplyInfo($supplier_id){
        try{
            return SupplierModel::where('supplier_id',$supplier_id)->firstOrFail();
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("供应商数据为空");
        }
    }

    /**
     * 推送订单数据到erp
     */
    public function pushOrderInfoToErp($data){
        try{
            $order_id = $data['order_id'];
            $order = OrderModel::findOrFail(intval($order_id));
            $order_goods = $order->order_goods;
            if(!$order_goods) throw new \Exception("订单商品数据为空");
            $erp_client_id = collect($order->customer)->get("erp_client_id");
            $erp_supplier_id = $this->getSupplyInfo($order->supplier_id)->erp_supplier_id;
            if(!$erp_client_id) throw new \Exception("委托方id为空");
//            dump($order);
//            dump($order);
            $arr = [];
            $arr['PTORDERID'] = $order->order_id;//平台委托单id
            $arr['PRINCIPALID'] = $erp_client_id;//委托方id
            $arr['SUPPLIERID'] = $erp_supplier_id;//供应商id
            $arr['ORDERINVOICEFILE'] = $order->order_invoice_file;//发票文件
            $arr['attachment'] = $order->attachment;//附件
            $arr['pickBoxFileAttach'] = $order->pick_box_file;//装箱单文件
            $arr['paymentApplyFileAttach'] = $order->payment_apply_file;//付汇委托书文件
            $arr['ISDK2'] = 0;//是否垫货款 0
            $arr['ISDKS'] = 0;//是否垫税 0
            $arr['ORDERTYPE'] = 7;
            $arr['BILLNUMBER'] = $order->erp_order_sn;//入仓单号
            $arr['CURRENCY'] = array_get(C("supply_currentcy_key"),$order->currency_id,'');
            $arr['BIZDATE'] = $order->create_time->format("Y-m-d H:i:s");
            $arr['GOODSPAY'] = array_get(static::$GOODSPAY,$order->overseas_settlement_type,0);
            $arr['DES'] = $order->taking_delivery." ".$order->invoice_remark;
            $arr['oldNumber'] = $order->old_erp_order_sn;//原入仓号
            $arr['orderRemark'] = $order->order_remark;//订单备注
            //新增订单类型
            $arr['BillType'] = $order->order_type;


            $arr = OrderAddressModel::getOrderAddress($order_id,$arr);

            //20200817 增加对香港交货相关数据，国内物流相关信息进行存储
            $arr['HONGKONGDELIVERYTYPE'] = $order->hongkong_delivery_type;
            $arr['INLANDDELIVERYTYPE'] = $order->inland_delivery_type;
            $arr = HongKongDeliveryModel::getOrderDelivery($order_id,$arr);
            $arr = InlandDeliveryModel::getOrderInlandDelivery($order_id,$arr);

            foreach($order_goods as $key=>$item){
                $arr['ENTRYS'][$key]['MATERIALID'] = $item->erp_material_sn;//erp物料号
                $arr['ENTRYS'][$key]['QTY'] = $item->numbers;
                $arr['ENTRYS'][$key]['MEASUREUNIT'] = $item->measurement;//计量单位
                $arr['ENTRYS'][$key]['PRICE'] = $item->unit_price;//单价
                $arr['ENTRYS'][$key]['BRAND'] = $item->brand;//品牌
                $arr['ENTRYS'][$key]['MODEL'] = htmlspecialchars_decode($item->goods_type);//型号
                $arr['ENTRYS'][$key]['GOODSNANME'] = $item->goods_title;//品名
                $arr['ENTRYS'][$key]['PTENTRYID'] = $item->order_goods_id;//平台分录id
                $arr['ENTRYS'][$key]['COUNTRY'] = $item->origin;//原产地

                $arr['ENTRYS'][$key]['sellingPrice'] = $item->hk_unit_price;//卖出单价
            }
            return $arr;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
            return false;
        }


    }


    /**
     * 推送改单订单信息到erp
     */
    public function pushChangeOrderToErp($data){
        try{
            $order_id = $data['order_id'];
            $order = OrderModel::findOrFail(intval($order_id));

            $order_goods = $order->order_goods;
            if(!$order_goods) throw new \Exception("订单商品数据为空");
            $erp_client_id = collect($order->customer)->get("erp_client_id");
            $erp_supplier_id = $this->getSupplyInfo($order->supplier_id)->erp_supplier_id;
            if(!$erp_client_id) throw new \Exception("委托方id为空");
            $arr = [];
            $arr['BILLNUMBER'] = $order->erp_order_sn;//ERP订单号
            $arr['PRINCIPALID'] = $erp_client_id;//委托方id
            $arr['SUPPLIERID'] = $erp_supplier_id;//供应商id
            $arr['ORDERINVOICEFILE'] = $order->order_invoice_file;//发票文件
            $arr['attachment'] = $order->attachment;//附件
            $arr['pickBoxFileAttach'] = $order->pick_box_file;//装箱单文件
            $arr['paymentApplyFileAttach'] = $order->payment_apply_file;//付汇委托书文件
            $arr['oldNumber'] = $order->old_erp_order_sn;//原入仓号
            $arr['ISDK2'] = 0;//是否垫货款 0
            $arr['ISDKS'] = 0;//是否垫税 0
            $arr['ORDERTYPE'] = 7;
            $arr['BILLNUMBER'] = $order->erp_order_sn;//入仓单号
            $arr['CURRENCY'] = array_get(C("supply_currency"),$order->currency_id,'');
            $arr['BIZDATE'] = $order->create_time->format("Y-m-d H:i:s");
            $arr['GOODSPAY'] = array_get(static::$GOODSPAY,$order->overseas_settlement_type,0);
            $arr['DES'] = $order->taking_delivery." ".$order->invoice_remark;
            $arr['orderRemark'] = $order->order_remark;//订单备注

            $arr = OrderAddressModel::getOrderAddress($order_id,$arr);


            //20200817 增加对香港交货相关数据，国内物流相关信息进行存储
            $arr['HONGKONGDELIVERYTYPE'] = $order->hongkong_delivery_type;
            $arr['INLANDDELIVERYTYPE'] = $order->inland_delivery_type;
            $arr = HongKongDeliveryModel::getOrderDelivery($order_id,$arr);
            $arr = InlandDeliveryModel::getOrderInlandDelivery($order_id,$arr);

            foreach($order_goods as $key=>$item){
                $arr['ENTRYS'][$key]['MATERIALID'] = $item->erp_material_sn;//erp物料号
                $arr['ENTRYS'][$key]['QTY'] = $item->numbers;
                $arr['ENTRYS'][$key]['MEASUREUNIT'] = $item->measurement;//计量单位
                $arr['ENTRYS'][$key]['PRICE'] = $item->unit_price;//单价
                $arr['ENTRYS'][$key]['BRAND'] = $item->brand;//品牌
                $arr['ENTRYS'][$key]['MODEL'] = htmlspecialchars_decode($item->goods_type);//型号
                $arr['ENTRYS'][$key]['GOODSNANME'] = $item->goods_title;//品名
                $arr['ENTRYS'][$key]['PTENTRYID'] = $item->order_goods_id;//平台分录id
                $arr['ENTRYS'][$key]['COUNTRY'] = $item->origin;//产地
            }
            return $arr;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
            return false;
        }


    }

    /**
     * 同步服务协议到本地
     */
    public function syncFuWuXieYiToLocal($data){
        return true;
    }

    /**
     * 下单 同步订单数据到本地
     */
    public function syncOrderInfoLocal($order_id,$data){
        try{
            $bk = OrderModel::where("order_id",$order_id)->update([
                'erp_order_id'=>$data['ORDERID'],
                'erp_order_sn'=>$data['ORDERNUMBER'],
                'is_push'=>1,
            ]);
            if($bk === false) throw new \Exception("修改订单状态失败");
            //查找出所有子订单 更新子订单的关联的erp id和编码
            OrderModel::where("id_edit_order",1)->where("edit_order_id",$order_id)->update([
                'erp_order_id'=>$data['ORDERID'],
                'erp_order_sn'=>$data['ORDERNUMBER'],
            ]);
            return $bk;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("供应商数据为空");
        }
    }

    /**
     * 下单 同步订单商品数据到本地
     */
    public function syncOrderGoodsLocal($order_id,$data){
        try{
            foreach($data['ENTRYS'] as $k=>$item){
                if(isset($item['PTENTRYID']) && $item['PTENTRYID']){
                    $bk = OrderGoodsModel::where("order_id",$order_id)->where("order_goods_id",intval(trim($item['PTENTRYID'])))->update([
                        'sales_price'=>$item['SALESPRICE'],//销售单价
                        'sales_amount'=>$item['SALESAMOUNT'],//销售金额
                        'tariff_rate'=>$item['TARIFFRATE'],//关税率
                        'tariff_amount'=>$item['TARIFFAMOUNT'],//关税金额
                        'erp_material_id'=>$item['MATERIALID'],//物料id
                        'erp_material_sn'=>$item['MATERIALNO'],//erp对应产品物料号
                        'erp_entery_id'=>$item['ENTERYID'],//erp对应商品id 分录id
                        'vat_rate'=>$item['VATRATE'],//增值税率
                        'vat_amount'=>$item['VATAMOUNT'],//增值税金额
                    ]);
                    if($bk === false) throw new \Exception("回写订单商品数据失败");
                }
            }
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("订单商品数据回写失败");
        }
    }


    /**
     * 改单 同步订单商品数据到本地
     */
    public function syncChangeOrderGoodsLocal($ChangeOrder,$data){
        try{
            $order_id = $ChangeOrder->order_id;
            foreach($data['ENTRYS'] as $k=>$item){
                if(isset($item['PTENTRYID']) && $item['PTENTRYID']){
                    $bk = OrderGoodsModel::where("order_id",$order_id)->where("order_goods_id",intval(trim($item['PTENTRYID'])))->update([
                        'sales_price'=>$item['SALESPRICE'],//销售单价
                        'sales_amount'=>$item['SALESAMOUNT'],//销售金额
                        'tariff_rate'=>$item['TARIFFRATE'],//关税率
                        'tariff_amount'=>$item['TARIFFAMOUNT'],//关税金额
                        'erp_material_id'=>$item['MATERIALID'],//物料id
                        'erp_material_sn'=>$item['MATERIALNO'],//erp对应产品物料号
                        'erp_entery_id'=>$item['ENTERYID'],//erp对应商品id 分录id
                        'vat_rate'=>$item['VATRATE'],//增值税率
                        'vat_amount'=>$item['VATAMOUNT'],//增值税金额
                    ]);
                    if($bk === false) throw new \Exception("回写订单商品数据失败");
                }
            }
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("订单商品数据回写失败");
        }
    }




    /**
     * 审核订单-删除商品
     */
    protected function auditOrderDeleteGoods($order,$order_goods_ids,$erp_entery_ids){
        try{
            $bk = OrderGoodsModel::where("order_id",$order->order_id)
                ->where(function($query)use($order_goods_ids,$erp_entery_ids){
                    $query->whereNotIn("order_goods_id",$order_goods_ids)->whereNotIn("erp_entery_id",$erp_entery_ids);
                })->update(['status'=>-1]);
            if($bk === false) throw new \Exception("审核订单-删除商品数据失败");
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 审核订单
     * 修改商品参数
     */
    protected function auditOrderEditGoods($order,$item,&$returnData){
        try{
            $bk = OrderGoodsModel::where("order_id",$order->order_id)->where("order_goods_id",intval(trim($item['PTENTRYID'])))->update([
                'goods_title'=>$item['PRODUCTNAME'],//物料品名
                'goods_type'=>$item['SPECIFICATION'],//型号
                'brand'=>$item['MANUFACTURER'],//品牌
                'customs_code'=>$item['PRODUCTPRECODE']?$item['PRODUCTPRECODE']:'',//海关编码
                'regulatory_condition'=>$item['SVISIONCD']?$item['SVISIONCD']:'',//监管条件
                'sales_price'=>$item['SALESPRICE'],//销售单价
                'sales_amount'=>$item['SALESAMOUNT'],//销售金额
                'tariff_rate'=>$item['TARIFFRATE'],//关税率
                'tariff_amount'=>$item['TARIFFAMOUNT'],//关税金额
                'erp_material_id'=>$item['MATERIALID'],//物料id
                'erp_material_sn'=>$item['MATERIALNO'],//erp对应产品物料号
                'erp_entery_id'=>$item['ENTERYID'],//erp对应商品id 分录id
                'vat_rate'=>$item['VATRATE'],//增值税率
                'vat_amount'=>$item['VATAMOUNT'],//增值税金额
                'unit_price'=>$item['PRICE'],//单价
                'total_price'=>$item['AMOUNT'],//总价
                'numbers'=>$item['QTY'],//数量
                'dlf_amount'=>$item['DLFAMOUNT'],//代理费金额
                'status'=>5,//审核通过
                'measurement'=>trim($item['MEASUREUNIT']),//计量单位
                'origin'=>trim($item['COUNTRY']),//产地

                //1009新增erp推送商品的其他信息
                'erp_other_param'=>json_encode([
                    'ISINSPORG'=>$item['ISINSPORG'],'DLFAMOUNT'=>$item['DLFAMOUNT'],'ORIGINTAX'=>$item['ORIGINTAX'],
                    'ORIGINAMT'=>$item['ORIGINAMT'],'TAXAMT'=>$item['TAXAMT'],'TAXPRICE'=>$item['TAXPRICE']
                ]),
                'is_insp_org' => $item['ISINSPORG']?1:0,
                'tax_sn'=>trim($item['tax_sn']),//税号
                'tariff'=>trim($item['tariff']),//关税
                'origin_tax'=>trim($item['origin_tax']),//产地税
                'declaration_type'=>isset($item['declaration_type'])?$item['declaration_type']:'4',
            ]);

            if($bk === false) throw new \Exception("审核订单-回写订单商品数据失败");
            $returnData['PTENTRYID'] = $bk->order_goods_id;
            $returnData['ENTERYID'] = $bk->erp_entery_id;
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * 审核订单
     * 新增商品
     */
    protected function auditOrderInsertGoods($order,$item,&$returnData){
        try{
            $bk = OrderGoodsModel::updateOrcreate(
                [
                    'erp_entery_id'=>$item['ENTERYID'],//erp对应商品id 分录id
                ]
                ,
                [
                    'goods_title'=>$item['PRODUCTNAME'],//品名
                    'goods_type'=>$item['SPECIFICATION'],//型号
                    'brand'=>$item['MANUFACTURER'],//品牌
                    'customs_code'=>$item['PRODUCTPRECODE']?$item['PRODUCTPRECODE']:'',//海关编码
                    'regulatory_condition'=>$item['SVISIONCD']?$item['SVISIONCD']:'',//监管条件
                    'order_id'=>$order->order_id,//订单id
                    'sales_price'=>$item['SALESPRICE'],//销售单价
                    'sales_amount'=>$item['SALESAMOUNT'],//销售金额
                    'tariff_rate'=>$item['TARIFFRATE'],//关税率
                    'tariff_amount'=>$item['TARIFFAMOUNT'],//关税金额
                    'erp_material_id'=>$item['MATERIALID'],//物料id
                    'erp_material_sn'=>$item['MATERIALNO'],//erp对应产品物料号
                    'vat_rate'=>$item['VATRATE'],//增值税率
                    'vat_amount'=>$item['VATAMOUNT'],//增值税金额
                    'unit_price'=>$item['PRICE'],//单价
                    'total_price'=>$item['AMOUNT'],//总价
                    'numbers'=>$item['QTY'],//数量
                    'dlf_amount'=>$item['DLFAMOUNT'],//代理费金额
                    'status'=>5,//审核通过
                    'measurement'=>'007',//审核通过
                    'user_id'=>$order->user_id,
                    'company_id'=>$order->company_id,
                    'measurement'=>trim($item['MEASUREUNIT']),//计量单位
                    'origin'=>trim($item['COUNTRY']),//产地
                    //1009新增erp推送商品的其他信息
                    'erp_other_param'=>json_encode([
                        'ISINSPORG'=>$item['ISINSPORG'],'DLFAMOUNT'=>$item['DLFAMOUNT'],'ORIGINTAX'=>$item['ORIGINTAX'],
                        'ORIGINAMT'=>$item['ORIGINAMT'],'TAXAMT'=>$item['TAXAMT'],'TAXPRICE'=>$item['TAXPRICE']
                    ]),
                    'is_insp_org' => $item['ISINSPORG']?1:0,
                    'tax_sn'=>trim($item['tax_sn']),//税号
                    'tariff'=>trim($item['tariff']),//关税
                    'origin_tax'=>trim($item['origin_tax']),//产地税
                    'declaration_type'=>isset($item['declaration_type'])?$item['declaration_type']:'4',
                ]);
            if(!$bk) throw new \Exception("审核订单-新增商品数据失败");
            $returnData['PTENTRYID'] = $bk->order_goods_id;
            $returnData['ENTERYID'] = $bk->erp_entery_id;
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }



    /**
     * erp推送审核数据到本地
     * 同步订单商品数据到本地
     */
    public function auditSyncOrderGoodsLocal($data,&$returnData){
        $order = OrderModel::where("erp_order_id",$data['ORDERID'])->where("erp_order_sn",$data['ORDERNUMBER'])->firstOrFail();
        $order->status = 1;//审核通过
        if(!$order->save()) throw new \Exception("审核订单-修改订单状态失败");
        try{
            //获取平台分录id
            $order_goods_ids = collect($data['ENTRYS'])->pluck('PTENTRYID')->all();
            $order_goods_ids = array_filter($order_goods_ids,function(&$v){
                return $v ? true : false;
            });

            //获取erp商品所有的分录id
            $erp_entery_ids = collect($data['ENTRYS'])->pluck('ENTERYID')->all();
            $erp_entery_ids = array_filter($erp_entery_ids,function(&$v){
                return $v ? true : false;
            });

            //首先判断是否有需要删除的商品
            //删除商品
            $this->auditOrderDeleteGoods($order,$order_goods_ids,$erp_entery_ids);

            $amount = 0;

            //新增或者修改商品参数
            foreach($data['ENTRYS'] as $k=>$item){
                $amount += $item['PRICE']*$item['QTY'];
                if(isset($item['PTENTRYID']) && $item['PTENTRYID'] && $item['PTENTRYID'] != " "){
                    //修改  存在平台分录id 就修改
                    $this->auditOrderEditGoods($order,$item,$returnData['ENTRYS'][$k]);
                }else{
                    //新增  不存在平台分录id就新增
                    $this->auditOrderInsertGoods($order,$item,$returnData['ENTRYS'][$k]);
                }
            }

            $order->order_price = $amount;
            if(!$order->save()) throw new \Exception("审核订单-修改订单总金额失败");

            $returnData['PTID'] = $order->order_id;
            $returnData['ORDERID'] = $order->erp_order_id;
            $returnData['ORDERNUMBER'] = $order->erp_order_sn;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("审核订单-订单商品数据回写失败");
        }
    }


    /**
     * @param $data
     * @throws Exception
     * 审核订单 修改
     */
    protected function auditOrderInfoLocal($data){
        try{
            $order = OrderModel::where("erp_order_id",$data['ORDERID'])->where("erp_order_sn",$data['ORDERNUMBER'])->firstOrFail();
            $order->order_price = $data['ARTOTAL'];

            if (isset($data['CARRIER']) && !empty($data['CARRIER'])){
                $order->carrier = $data['CARRIER'];
            }

            if (isset($data['LOGISTICSNO']) && !empty($data['LOGISTICSNO'])){
                $order->logistics_no = $data['LOGISTICSNO'];
            }

            //业务名称
            if (isset($data['salesman']) && !empty($data['salesman'])){
                $order->salesman = $data['salesman'];
            }

            if (isset($data['orderRemark']) && !empty($data['orderRemark'])){
                $order->order_remark = $data['orderRemark'];
            }



            //1009新增erp推送商品的其他信息
            $order->erp_other_param =  json_encode([
                'RATE'=>$data['RATE'],'CUSTOMSRATE'=>$data['CUSTOMSRATE'],'DLFRATE'=>$data['DLFRATE'],'MISCELLANEOUSFEE'=>$data['MISCELLANEOUSFEE'],
                'EXPORTCOUNT'=>$data['EXPORTCOUNT'],'MAINBUSINESS'=>$data['MAINBUSINESS'],
                'INVOICEAMOUNT'=>$data['INVOICEAMOUNT'],'ARTORAL'=>$data['ARTORAL'],'DLFDIFFERENCE'=>$data['DLFDIFFERENCE']
            ]);

            if(isset($data['GOODSPAY'])){
                $order->overseas_settlement_type = array_get(static::$ERPGOODSPAY,$data['GOODSPAY'],0);
            }
            $order->status = OrderModel::$PassStatus;
            $currency_id = array_search($data['CURRENCY'],C("supply_currency"));
            //存在就修改 否则不修改本地币别了
            if($currency_id){
                $order->currency_id = array_search($data['CURRENCY'],C("supply_currency"));
            }
            if(!$order->save()){
                throw new \Exception("审核修改订单信息失败");
            }
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("审核修改订单信息失败");
        }
    }

    /**
     * 审核订单  新增订单信息
     */
    protected function addOrderInfoLocal($data){
        try{
            $order_sn =  OrderModel::findSn();
            $currency_id = 1;//默认美元
            if(!isset($data['CURRENCY']) && $data['CURRENCY']){
                $currency_id_tmp = array_search($data['CURRENCY'],C("supply_currency"));
                $currency_id = $currency_id_tmp ? $currency_id_tmp : $currency_id;
            }

            //供应商
            $supply = null;
            if(isset($data['SUPPLIERID']) && $data['SUPPLIERID']){
                $supply = SupplierModel::where("erp_supplier_id",trim($data['SUPPLIERID']))->first();
            }

            $overseas_settlement_type = 0;//境外结算方式
            if(isset($data['GOODSPAY'])){
                $overseas_settlement_type = array_get(static::$ERPGOODSPAY,$data['GOODSPAY'],0);
            }


            //通过erp委托方id找到本地公司id
            $company_id = 0;
            if(isset($data['PRINCIPALID']) && $data['PRINCIPALID']){
                $customer = CustomerModel::where("erp_client_id",trim($data['PRINCIPALID']))->first();
                $company_id = ($customer && $customer->company_id)  ? $customer->company_id : $company_id;
            }
            $Insertarr = [
                "order_sn"=>$order_sn,
                "currency_id"=>$currency_id,//币别关联id
                "order_price"=>0,
                "supplier_id"=>$supply ? $supply->supplier_id : 0,//供应商关联平台id
                "supplier_name"=>$supply ? $supply->supplier_name : '',//供应商名称
                "overseas_settlement_type"=>$overseas_settlement_type,//境外结算方式
                "overseas_settlement_days"=>0,//境外结算期限 0 现结
                "hongkong_delivery_type"=>1,//香港交货方式 1 供应商配送  2代为提货
                "inland_delivery_type"=>1,//1整批
                "company_id"=>$company_id,//公司id
                "is_push"=>1,
                "id_edit_order"=>0,
                "audit_time"=>time(),
                "status"=>OrderModel::$PassStatus,
                "erp_order_id"=>$data['ORDERID'],//erp订单id
                "erp_order_sn"=>$data['ORDERNUMBER'],//erp订单编码
                'carrier'=>isset($data['CARRIER'])?$data['CARRIER']:'',
                'logistics_no'=>isset($data['LOGISTICSNO'])?$data['LOGISTICSNO']:'',
                "salesman"=>$data['salesman']?$data['salesman']:'',//业务名称


                'order_remark'=>isset($data['orderRemark'])?$data['orderRemark']:'',

                //1009新增erp推送商品的其他信息
                'erp_other_param'=>json_encode([
                    'RATE'=>$data['RATE'],'CUSTOMSRATE'=>$data['CUSTOMSRATE'],'DLFRATE'=>$data['DLFRATE'],'MISCELLANEOUSFEE'=>$data['MISCELLANEOUSFEE'],
                    'EXPORTCOUNT'=>$data['EXPORTCOUNT'],'MAINBUSINESS'=>$data['MAINBUSINESS'],
                    'INVOICEAMOUNT'=>$data['INVOICEAMOUNT'],'ARTORAL'=>$data['ARTORAL'],'DLFDIFFERENCE'=>$data['DLFDIFFERENCE']
                ])
            ];

            $bk = OrderModel::create($Insertarr);
            if(!$bk) throw new Exception("新增订单失败");

        }catch(\Exception $e){
//            dump($e->getMessage());
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("审核订单信息失败");
        }
    }


    /**
     * erp
     *推送订单审核数据到平台
     * 审核订单除了修改本地订单 还有 erp新增的订单
     */
    public function erpPullOrderAudit($data){
        try{
            //判断erp推送的订单数据是修改还是新增
            if(!$data['ORDERID'] || !$data['ORDERNUMBER']) throw new \Exception("erp订单号或者订单id不能为空");
            $order = OrderModel::where("erp_order_id",$data['ORDERID'])->where("erp_order_sn",$data['ORDERNUMBER'])->count("order_id");
            if(!$order){
                //新增
                $returnData = [];
                DB::connection('SUPPLYCHAIN')->transaction(function()use($data,&$returnData){
                    //新增订单信息
                    $this->addOrderInfoLocal($data);
                    //回写订单商品数据
                    $this->auditSyncOrderGoodsLocal($data,$returnData );
                    // 增加操作日期
                    (new ActionLogModel())->addLog($returnData['PTID'], '创建订单', 'admin', 1);

                    OrderTrackingLogLogic::addLog([
                        'log_type'=>OrderTrackingLogModel::log_type,
                        'bill_type'=>OrderTrackingLogModel::order,
                        'bill_id'=>$returnData['PTID'],
                        'content'=>$data['ORDERNUMBER'].'委托单审核成功',
                        'create_user_id'=>OrderTrackingLogModel::admin_user_id,
                        'create_user_name'=>OrderTrackingLogModel::admin_user_name
                    ]);
                });

                $returnArr['PTORDERID'] = "";//平台委托单id
                $returnArr['ORDERID'] = "";//erp委托单id
                return $returnData;
            }
            //修改
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data,&$returnData){
                //回写订单信息
                $this->auditOrderInfoLocal($data);
                //回写订单商品数据
                $this->auditSyncOrderGoodsLocal($data,$returnData);
                // 增加操作日期
                (new ActionLogModel())->addLog($returnData['PTID'], '再次审核订单', 'admin', 1);
                OrderTrackingLogLogic::addLog([
                    'log_type'=>OrderTrackingLogModel::log_type,
                    'bill_type'=>OrderTrackingLogModel::order,
                    'bill_id'=>$returnData['PTID'],
                    'content'=>$data['ORDERNUMBER'].'委托单再次审核成功',
                    'create_user_id'=>OrderTrackingLogModel::admin_user_id,
                    'create_user_name'=>OrderTrackingLogModel::admin_user_name
                ]);
            });

            //延迟推送
            $order = OrderModel::where("erp_order_id",$data['ORDERID'])->where('erp_order_sn',$data['ORDERNUMBER'])->first();
            //供应链传来的用户ID都是0
            if (empty($order->is_apply_payment) && !empty($order->user_id)){
//                try{
//                    $client = new \GuzzleHttp\Client();
//                    // 发送一个异步请求
//                    $request = new \GuzzleHttp\Psr7\Request('GET', API_DOMAIN.'supply/erp_push/fiveAutoPaymentApply?order_id='.$order->order_id);
//                    $promise = $client->sendAsync($request,['timeout' => 2])->then(function($response){
//                        echo 'I completed! ' . $response->getBody();
//                    });
//                    $promise->wait();
//                }catch (\Exception $exception){
//                    return $returnData;
//                }

                try{
                    PaymentApplyModel::autoInsertData($order);
                }catch (Exception $exception){
                    DingNotify::autoCreatePaymentApplyFailNotify($data['ORDERNUMBER'],$exception->getMessage());
                }
            }


            //推送消息给用户
            $this->sendMsg($custom_func='1',$data=['erp_order_sn'=>$data['ORDERNUMBER']]);

            return $returnData;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * erp审核订单后会推送
     * 预计应收款 也就是付款通知书
     * 新增订单对应的付款通知书
     */
    public function auditOrderPayCost($data){
        try{
            $order = OrderModel::where("erp_order_sn",trim($data['ORDERNUMBER']))->firstOrFail();
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data,$order){
                $pay_cost = PayCostModel::updateOrcreate(
                    [
                        'erp_order_sn'=>$data['ORDERNUMBER'],//erp对应订单号 委托单编码
                        'order_id'=>$order->order_id,
                    ],
                    [
                    'order_sn'=>$order->order_sn,
                    'user_id'=>$order->user_id,
                    'company_id'=>$order->company_id,
                    'amount'=>$data['REDAMOUNT'] ? $data['REDAMOUNT'] : 0,//应收金额
                    'invocie_amount'=>$data['INVOICEAMOUNT'] ? $data['INVOICEAMOUNT'] : 0,//开票金额
                    'erp_create_time'=>strtotime($data['CREATETIME']),//预计应收款创建时间
                    'create_user'=>$data['CREATOR']?$data['CREATOR']:'',//创建人
                    'bus_date'=>strtotime($data['BIZDATE']),//业务日期
                    'remark'=>$data['DES']?$data['DES']:'',//备注
                    'currency'=>$data['CURRENCY']?$data['CURRENCY']:'',//备注
                ]);
                if(!$pay_cost) throw new \Exception('新预计应收款新增失败');

                PayCostListModel::where("order_id",$order->order_id)->delete();
                foreach($data['ENTRYS'] as $k=>$item){
                    $bk = PayCostListModel::create([
                        'order_id'=>$order->order_id,
                        'seller_currency'=>$item['CURRENCY'],//卖方销售币别
                        'payables'=>$item['PROJECT'],//应付项目名称
                        'payables_parities'=>$item['PROJECTEXRATE'],//计费项目汇率
                        'currency'=>$item['CURRENCY'] ? $item['CURRENCY'] :'',//备注
                        'org_amount'=>$item['ORGAMOUNT'],//原币金额
                        'stanamount'=>$item['STANAMOUNT'],//本位币金额
                        'pay_cost_id'=>$pay_cost->id,//收款通知书主键id
                    ]);
                    if(!$bk) throw new \Exception('新预计应收款新增失败');
                }
            });

        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("预计应收款新增失败");
        }
    }

    /**
     * 改单记录
     */
    public function erpPullEditOrderRecord($data){
        try{
            $order = OrderModel::where("erp_order_sn",trim($data['ORDERNUMBER']))->select("order_id","order_sn","supplier_id","supplier_name","user_id","company_id")->firstOrFail();
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data,$order) {
                foreach ($data['ENTRYS'] as $item) {
                    $bk = OrderRecordModel::create([
                        'order_id' => $order->order_id,
                        'erp_order_sn' => $data['ORDERNUMBER'],//委托单号
                        'biz_date' => strtotime($data['BIZDATE']),//业务日期
                        'bills_sn' => $data['NUMBER'],//单据编号
                        'bills_id' => $data['ID'],//单据id
                        'change_type' => trim($data['CHANGETYPE']),//改单方式
                        'entry_id' => trim($item['ENTRYID']),//erp分录ID
                        'old_goods_title' => trim($item['PRODUCTNAMEOLD']),//原品名
                        'goods_title' => trim($item['PRODUCTNAME']),//品名
                        'old_brand' => trim($item['MANUFACTUREROLD']),//原品牌
                        'brand' => trim($item['MANUFACTURER']),//品牌
                        'old_goods_type' => trim($item['MODELOLD']),//原型号
                        'goods_type' => trim($item['MODEL']),//型号
                        'old_qty' => trim($item['QTYOLD']),//原数量
                        'qty' => trim($item['QTY']),//数量
                        'old_price' => trim($item['PRICEOLD']),//原单价
                        'price' => trim($item['PRICE']),//单价
                        'old_amount' => trim($item['AMOUNTOLD']),//原金额
                        'amount' => trim($item['AMOUNT']),//金额
                        'material_no' => trim($item['MATERIALNO']),//物料编码
                        'supplier_id' => $order->supplier_id,//供应商id
                        'user_id' => $order->user_id,
                        'company_id' => $order->company_id,
                        'supplier_name' => $order->supplier_name,//供应商名称
                    ]);
                    if (!$bk) throw new \Exception("插入改单记录失败");
                }
            });
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("插入改单记录失败");
        }
    }

    /**
     * erp删除订单
     */
    public function erpDeleteOrder($data){
        try{

            $order = OrderModel::where("erp_order_id",trim($data['ORDERID']))->firstOrFail();

            //判断是否理货
            if (WmsTallyDetailModel::where('erp_order_sn',$order['erp_order_sn'])->value('wstydl_id')){
                throw new \Exception('该入仓号存在已理货的明细，不能继续操作');
            }

            $bk = PayCostModel::where("order_id",$order->order_id)->update(['status'=>0]);
            if($bk === false) throw new \Exception("删除订单失败");
            $order->status = OrderModel::$CancelStatus;
            if(!$order->save()) throw new \Exception("删除订单失败");
            (new ActionLogModel())->addLog($order['order_id'], '作废订单', $data['opreator'], 1);
            OrderTrackingLogLogic::addLog([
                'log_type'=>OrderTrackingLogModel::log_type_1,
                'bill_type'=>OrderTrackingLogModel::order,
                'bill_id'=>$order->order_id,
                'content'=>$order['erp_order_sn'].'委托单已取消',
                'create_user_id'=>OrderTrackingLogModel::admin_user_id,
                'create_user_name'=>OrderTrackingLogModel::admin_user_name
            ]);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /*
     * erp推送客户委托方资料到平台
     * 联系人
     */
    protected function syncErpCustomerContact($data,$customer){
        try{
            foreach($data as $item){
                $bk = ContactModel::where("company_id",$customer->company_id)->update([
                    'erp_entry_id'=>isset($item['ERPID']) ? $item['ERPID'] : '',
                    'contact_name'=>isset($item['CONTACTER']) ? $item['CONTACTER'] : '',
                    'contact_phone'=>isset($item['MOBILE'])?$item['MOBILE']:'',//手机
                    'special_plane'=>isset($item['PHONE']) ? $item['PHONE'] : '',//座机
                ]);
                if($bk === false) throw new ErpException("客户联系人同步失败");
                break;
            }
        }catch(ErpException $e){
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /*
     * erp推送客户委托方资料到平台
     * 收货地址
     */
    protected function syncErpCustomerDelivery($data,$customer){
        try{
            foreach($data as $item){
//                \Think\Log::write(json_encode($this->getMobile($item['MOBILE'])),'WARN');
//                \Think\Log::write(json_encode($this->getPhone($item['PHONE'])),'WARN');
                list($intl_code,$mobile)=$this->getMobile($item['MOBILE']);
                list($seat_number_code,$seat_number)=$this->getPhone($item['PHONE']);
                $bk = UserDeliveryModel::updateOrCreate(
                    [
                        'company_id'=>$customer->company_id,
                        'erp_entry_id'=>$item['ERPID']
                    ],
                    [
                        'detail_address'=>$item['SADDRESS'],
                        'intl_code'=>$intl_code,
                        'mobile'=>$mobile,
                        'seat_number_code'=>$seat_number_code,
                        'seat_number'=>$seat_number,
                        'consignee'=>$item['CONTACTER'],
                        'status'=>0,
                    ]
                );
            }
        }catch(ErpException $e){
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }



    /**
     * @param $data
     * @throws ErpException
     * @throws Exception
     * erp同步客户资料
     */
    public function erpUpdateCustomerInfo($data){
        try{
            if(!isset($data['CUSTOMERID'])) throw new ErpException('没找到对应客户');
            $customer = CustomerModel::where("erp_cuntomer_id",$data['CUSTOMERID'])->firstOrFail();
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data,$customer) {
                $bk = CustomerModel::where("erp_cuntomer_id",$data['CUSTOMERID'])->update([
                    'customer_short_name'=>isset($data['SIMPLENMAE']) ? $data['SIMPLENMAE'] : '',//客户简称
                    'mnemonic_code'=>isset($data['HELPERCODE']) ? $data['HELPERCODE'] : '',//助记码
                    'follow_people'=>isset($data['CUSTOMEFOLLOWER']) ? $data['CUSTOMEFOLLOWER'] : '',//商务员姓名
                ]);
                if($bk === false) throw new ErpException("客户资料同步失败");

                $bk = CompanyModel::where("company_id",$customer->company_id)->update([
                    'company_full_name'=>isset($data['CUSTOMERNAME']) ? $data['CUSTOMERNAME'] : '',//客户名（公司名）
                    'organization_code'=>isset($data['ORGANIZATCODE']) ? $data['ORGANIZATCODE'] : '',//组织机构代码
                    'unified_social_credit_code'=>isset($data['UNIFYCREDITCODE']) ? $data['UNIFYCREDITCODE'] : '',//统一社会信用代码
                    'registered_capital'=>isset($data['REGISTCAPITAL']) ? $data['REGISTCAPITAL'] : 0,//注册资金
                    'legal_person'=>isset($data['CORPORATER']) ? $data['CORPORATER'] : '',//公司法人
                    'trade_registration'=>isset($data['ICNUMBER']) ? $data['ICNUMBER'] : '',//工商注册号
                ]);

                if($bk === false) throw new ErpException("委托方同步失败");

                $bk = ComInvoiceModel::where("company_id",$customer->company_id)->update([
                    'invoice_mobile'=>isset($data['INVOICEPHONE']) ? $data['INVOICEPHONE'] : '',//开票电话
                    'tax_certificate_no'=>isset($data['FTXREGISTERNO']) ? $data['FTXREGISTERNO'] : '',//纳税登记证号
                    'tax_identifying_sn'=>isset($data['FTXREGISTERNO']) ? $data['FTXREGISTERNO'] : '',//税务编号
                    'bank_number'=>isset($data['INVOICEACCOUNT']) ? $data['INVOICEACCOUNT'] : '',//开票账号
                    'invoice_address'=>isset($data['ADDRESS']) ? $data['ADDRESS'] : '',//开票地址
                    'bank_name'=>isset($data['INVOICEBANK']) ? $data['INVOICEBANK'] : '',//开户行名称
                    'status'=>1,
                ]);

                if($bk === false) throw new ErpException("开票资料同步失败");

                //同步联系人
                if(isset($data['BUSINESS'])){
                    $this->syncErpCustomerContact($data['BUSINESS'],$customer);
                }

                //同步收货地址
                if(isset($data['RECEIVER'])){
                    $this->syncErpCustomerDelivery($data['RECEIVER'],$customer);
                }


            });
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("erp同步客户资料");
        }
    }


    /**
     * @param $data
     * @throws ErpException
     * @throws Exception
     * erp同步服务协议
     */
    public function erpSyncServiceAgreement($data){
        try{
            if(!isset($data['PID'])) throw new ErpException("没找到对应的委托方id");
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data) {

                $type = isset($data['TYPE'])?$data['TYPE']:7;

                $serviceAgreement = ServiceAgreementModel::where(erp_client_id,trim($data['PID']))->where('service_type',$type)->first();
                $bk = ServiceAgreementModel::updateOrCreate([
                    'erp_client_id'=>trim($data['PID']),
                    'service_type'=>$type
                ],[
                    'effect_date'=>strtotime($data['EFFECTDATE']),//生效日期
                    'invalid_date'=>strtotime($data['INVALIDDATE']),//失效日期
                    'status'=>1,
                    'service_type'=>$type
                ]);

                if($bk === false) throw new ErpException("ERP同步服务协议数据失败");
                $service_agree_id = $serviceAgreement ? $serviceAgreement->id : $bk->id;
                ServiceAgreementListModel::where("service_agree_id",$service_agree_id)->delete();
                foreach($data['ENTRYS'] as $item){
                    $bk = ServiceAgreementListModel::create([
                        'service_agree_id'=>$service_agree_id,
                        'effect_date'=>strtotime($item['EFFDATE']),
                        'invalid_date'=>strtotime($item['INDIVDATE']),
                    ]);
                    if($bk === false) throw new ErpException("ERP同步服务协议数据失败");
                }
            });
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("ERP同步服务协议数据失败");
        }
    }

    /**
     * @param $data
     * @throws ErpException
     * @throws Exception
     * erp删除服务协议
     */
    public function erpSynDelServiceAgreement($data){
        try{
            $type = isset($data['TYPE'])?$data['TYPE']:7;

            $obj = ServiceAgreementModel::where(erp_client_id,trim($data['PID']))->where('service_type',$type)->first();
            if (empty($obj)){
                return [];
            }
            $obj->status = 0;
            if(!$obj->save()){
                throw new ErpException('erp删除服务协议失败');
            }
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("erp删除服务协议失败");
        }
    }

    /**
     * @param $data
     * @throws ErpException
     * @throws Exception
     * erp同步收款单到本地
     */
    public function erpSyncReceiptRecord($data){
        try{
            if (!$data['CUSTOMER']) throw new ErpException('ERP客户ID缺失');

            $customer = CustomerModel::where('erp_cuntomer_id', $data['CUSTOMER'])->select("user_id","company_id")->first();

            if (!$customer) throw new ErpException('没有找到指定客户');

            $currency_id = array_keys(C('supply_currentcy_key'), $data['CURRENCY']);

            $res = ReceiptModel::updateOrCreate(
                ['bill_id' => $data['ID'], 'bill_sn' => $data['NUMBER']],
                [
                    'currency_id'     => $currency_id ? $currency_id[0] : 0,
                    'erp_customer_id' => $data['CUSTOMER'],
                    'rec_com_name'    => $data['COMPANY'],
                    'rec_time'        => strtotime($data['BIZDATE']),
                    'remit_amount'    => floatval($data['REMITAMOUNT']),
                    'exchange_rate'   => floatval($data['EXCHANGERATE']),
                    'exchange_amount' => floatval($data['AMOUNT']),
                    'customer_rate'   => floatval($data['CERATE']),
                    'customer_amount' => floatval($data['CAMOUNT']),
                    'user_id'         => $customer->user_id,
                    'company_id'         => $customer->company_id,
                    'status'         => 0,

                ]
            );

            if ($res === false) throw new ErpException('同步收款单失败');
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("ERP同步收款单到本地失败");
        }
    }

    /**
     * @param $data
     * @throws ErpException
     * @throws Exception
     * erp删除收款单
     */
    public function erpSynDelReceiptRecord($data){
        try{
            if (!$data['ID']) throw new ErpException('ERP收款单单据ID缺失');

            $where['bill_id'] = $data['ID'];
            $receipt = ReceiptModel::where($where)->get();

            if (!$receipt) throw new ErpException('没有找到对应的收款单');

            $res = ReceiptModel::where($where)->update(['status'=>-1]); // 更改状态

            if ($res === false) throw new ErpException('删除收款单失败'); 
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("ERP删除收款单失败");
        }
    }

    /**
     *  ERP修改供应商数据
     */
    public function erpUpdateSupplier($data)
    {
        try{
            if (!$data['PTID']) throw new ErpException("ERP供应商ID缺失");

            // 查询供应商资料
            $supplier = SupplierModel::where('supplier_id', $data['PTID'])->first();
            if (!$supplier) throw new ErpException("未找到供应商资料");

            // 更新供应商资料
            $save_supplier['supplier_name'] = $data['SUPPLIER'];
            isset($data['SIMPLENAME']) && $save_supplier['supplier_short_name'] = $data['SIMPLENAME'];
            $res = SupplierModel::where('supplier_id', $data['PTID'])->update($save_supplier);

            if ($res === false) throw new ErpException("更新供应商失败，supplier_id：".$data['PTID']);

            // if (!$data['PTID'] || empty($data['BANKS'])) throw new ErpException("ERP供应商ID或银行数据缺失");

            // DB::connection('SUPPLYCHAIN')->transaction(function() use ($data) {
            //     // 查询供应商资料
            //     $supplier = SupplierModel::where('supplier_id', $data['PTID'])->first();
            //     if (!$supplier) throw new ErpException("未找到供应商资料");

            //     // 更新供应商资料
            //     $save_supplier['supplier_name'] = $data['SUPPLIER'];
            //     isset($data['SIMPLENAME']) && $save_supplier['supplier_short_name'] = $data['SIMPLENAME'];
            //     $res = SupplierModel::where('supplier_id', $data['PTID'])->update($save_supplier);

            //     if ($res === false) throw new ErpException("更新供应商失败，supplier_id：".$data['PTID']); 

            //     // 获取ERP传递过来的ERP关联ID
            //     $erp_entry_ids = collect($data['BANKS'])->pluck('ENTRYID')->all();
            //     $erp_entry_ids = array_filter($erp_entry_ids, function(&$v) {
            //         return $v ? true : false;
            //     });

            //     // 查询平台供应商银行账号ERP关联ID
            //     $supplier_banks = SupplierBankModel::where('supplier_id', $data['PTID'])->select('erp_entry_id')->get()->toArray();
            //     $supplier_entry_ids = array_column($supplier_banks, 'erp_entry_id');

            //     // 判断平台关联ID是否存在于ERP传递过来的关联ID中，不存在则删除（更新状态为-2 作废）
            //     foreach ($supplier_entry_ids as $v) {
            //         if (!in_array($v, $erp_entry_ids)) {
            //             $del = SupplierBankModel::where('erp_entry_id', $v)->update(['status' => -2]);

            //             if ($del === false) throw new ErpException("删除供应商银行失败，erp_entry_id：".$v); 
            //         }
            //     }       

            //     // 判断ERP传递过来的关联ID是否存在于平台关联ID中
            //     foreach ($data['BANKS'] as $k=>$banks) {   
            //         $temp = array();  
            //         $temp['supplier_id'] = $supplier['supplier_id'];
            //         $temp['user_id']     = $supplier['user_id'];

            //         isset($banks['BANK']) && $temp['bank_name']            = $banks['BANK'];
            //         isset($banks['BANKACCOUNT']) && $temp['bank_account']  = $banks['BANKACCOUNT'];
            //         isset($banks['BANKADDRESSS']) && $temp['bank_address'] = $banks['BANKADDRESSS'];
            //         isset($banks['COUNTRY']) && $temp['recipient_country'] = $banks['COUNTRY'];
            //         isset($banks['SWIFCODE']) && $temp['swift_code']       = $banks['SWIFCODE'];
            //         isset($banks['ENTRYID']) && $temp['erp_entry_id']      = $banks['ENTRYID'];
                    
            //         if (isset($banks['DESTINA'])) {
            //             $temp['area'] = in_array($banks['DESTINA'], ['香港', 'HK', 'hk']) ? 1 : 2; // 区分香港境内、香港境外
            //         }

            //         if (in_array($banks['ENTRYID'], $supplier_entry_ids)) { // 存在则更新
            //             $temp['update_time'] = time();

            //             $update = SupplierBankModel::where('erp_entry_id', $banks['ENTRYID'])->update($temp);

            //             if ($update === false) throw new ErpException("更新供应商银行失败，erp_entry_id：".$banks['ENTRYID']); 
            //         } else { // 新增供应商银行
            //             $temp['create_time'] = time();

            //             $add = SupplierBankModel::insert($temp);

            //             if ($add === false) throw new ErpException("新增供应商银行失败，erp_entry_id：".$banks['ENTRYID']); 
            //         }
            //     }          
            // });
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("ERP修改供应商数据失败");
        }
    }

    /**
     *  ERP修改客户余额
     */
    public function erpUpdateCustomerAmount($data)
    {
        try{
            if (!$data['CUSTOMERID']) throw new ErpException("ERP客户ID缺失");

            $customer = CustomerModel::where('erp_cuntomer_id', $data['CUSTOMERID'])->first();
            if (!$customer) throw new ErpException("未找到指定客户"); 

            $customer->rmb_account = floatval($data['CNYAMOUNT']); // 更新余额
            $customer->have_rmb_account = floatval($data['CNYAMOUNT2']); // 更新已走账余额

            $res = $customer->save();

            if ($res === false) throw new ErpException("客户余额更新失败");
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("ERP修改客户余额失败");
        }
    }

    public function synCustomsPlanExpCar($data){
        DB::connection('SUPPLYCHAIN')->beginTransaction();
        try {
            $train_id = $data['train_id'];
            unset($data['train_id']);
            $data['update_time'] = time();
            CustomsCarLoadingModel::where('train_id', $train_id)
                ->where('status', 1)
                ->update($data);
            DB::connection('SUPPLYCHAIN')->commit();
        } catch(\Exception $e){
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($e->getMessage());
        }
    }

    public function synCustomsCode($data){
        foreach ($data as $value){
            $updateData = ['customs_code' => $value['customs_code']];
            if (isset($value['company'])){
                $updateData['company'] = $value['company'];
            }
            BaoGuanOrderModel::where('bills_id', $value['id'])->update($updateData);
        }
    }

    /**
     * 同步归类信息
     * @param $data
     * @return void
     */
    public function synClassifyInfo($data){
        $errMsg = "";
        foreach ($data as $value){
            $orderDetail = OrderGoodsModel::where('erp_entery_id', $value['erp_order_detail_id'])->first();
            if (!empty($orderDetail)){
                if (WmsTallyDetailModel::where('is_base', 0)->where('order_goods_id', $orderDetail['order_goods_id'])->exists()) {
                    // 存在已理货数据
                    $errMsg .= "第".$value['seq']."行记录已理货，无法更新归类信息!\n";
                } else {
                    // 更新订单明细和锁定的理货明细
                    // 明细ID：000001，原品名：接插件，原海关编码：8536901100；明细ID：000002：原品名：接插件，原海关编码：8536901100；
                    $content = '通过ERP【更新归类信息】变更了委托单信息。明细ID：'.$orderDetail['order_goods_id'].'原型号：'.$orderDetail['goods_type'];
                    if ($orderDetail['tax_sn'] != $value['tax_sn']) {
                        $content .= '；原海关编码：'.$orderDetail['tax_sn'];
                    }
                    if ($orderDetail['goods_title'] != $value['goods_name']) {
                        $content .= '；原品名：'.$orderDetail['goods_title'];
                    }
                    (new ActionLogModel())->addLog($orderDetail['order_id'], $content, 'admin', 1);

                    $erp_other_param = $orderDetail['erp_other_param'];
                    if (!empty($erp_other_param)) {
                        $erp_other_param = json_decode($erp_other_param, true);
                        $erp_other_param['ISINSPORG'] = !empty($value['isInspOrg']);
                    }
                    OrderGoodsModel::where('order_goods_id', $orderDetail['order_goods_id'])->update([
                        'goods_type' => $value['model'],
                        'brand'=>$value['brand'],
                        'goods_title' => $value['goods_name'],
                        'tax_sn' => $value['tax_sn'],
                        'customs_code' => $value['tax_sn'],
                        'declaration_type' => $value['declaration_type'],
                        'tariff' => $value['tariff'],
                        'tariff_rate' => $value['tariff'],
                        'origin_tax' => $value['origin_tax'],
                        'is_insp_org' => $value['isInspOrg'],
                        'erp_other_param' => empty($erp_other_param)?'':json_encode($erp_other_param),
                        'regulatory_condition' => $value['svisioncd'],
                    ]);
                    // 更新锁定理货记录
                    WmsTallyDetailModel::where('is_base', 1)
                        ->where('order_goods_id',$orderDetail['order_goods_id'])
                        ->update([
                            'goods_type'=>$value['model'],
                            'brand'=>$value['brand'],
                            'goods_title'=>$value['goods_name'],
                            'tax_sn'=>$value['tax_sn'],
                            'tariff'=>$value['tariff'],
                            'origin_tax'=>$value['origin_tax'],
                            'is_goods_check'=>$value['isInspOrg'],
                        ]);
                }
            }
        }
        if (!empty($errMsg)) {
            throw new \Exception($errMsg);
        }
    }

    /**
     * 报关计划同步
     * @param $data
     * @return void
     */
    public function synCustomsPlanExp($data){
        $syn_type = $data['syn_type'];
        if ($syn_type) {
            // 1 审核同步
            DB::connection('SUPPLYCHAIN')->beginTransaction();
            try {
                $car = $data['car'];
                $details = $data['details'];
                unset($data['syn_type']);
                unset($data['car']);
                unset($data['details']);
                $baseInsertData = array_merge($data, $car);
                $baseInsertData['create_user_name'] = 'admin';
                $baseInsertData['update_user_name'] = 'admin';
                foreach ($details as $detail) {
                    $insertData = array_merge($baseInsertData, $detail);
                    $now = time();
                    $insertData['update_time'] = $now;
                    $insertData['create_time'] = $now;
                    $car_loading_id = CustomsCarLoadingModel::insertGetId($insertData);
                    if (!$car_loading_id) {
                        throw new \Exception('装车清单新增失败');
                    }
                }
                // 更新 清单箱数, 已装载卡板数, 已装板箱数, 装板状态
//                $sum_box_qty = CustomsCarLoadingModel::where('load_goods_no', $data['load_goods_no'])
//                    ->where('status', 1)
//                    ->sum('box_qty');
//                CustomsCarLoadingModel::where('load_goods_no', $data['load_goods_no'])
//                    ->where('status', 1)
//                    ->update([
//                        'list_box_qty' => $sum_box_qty,
//                    ]);
                DB::connection('SUPPLYCHAIN')->commit();
            } catch(\Exception $e){
                DB::connection('SUPPLYCHAIN')->rollback();
                throw new \Exception($e->getMessage());
            }
        } else {
            // 0 反审核作废
            DB::connection('SUPPLYCHAIN')->beginTransaction();
            try {
                // 变更状态
                CustomsCarLoadingModel::where('plan_exp_id', $data['plan_exp_id'])->update([
                    'status'=> -1
                ]);
                // 更新清单箱数
//                $sum_box_qty = CustomsCarLoadingModel::where('load_goods_no', $data['load_goods_no'])
//                    ->where('status', 1)
//                    ->sum('box_qty');
//                CustomsCarLoadingModel::where('load_goods_no', $data['load_goods_no'])
//                    ->where('status', 1)
//                    ->update([
//                        'list_box_qty' => $sum_box_qty,
//                    ]);
                DB::connection('SUPPLYCHAIN')->commit();
            } catch(\Exception $e){
                DB::connection('SUPPLYCHAIN')->rollback();
                throw new \Exception($e->getMessage());
            }
        }
    }

    public function synCustomsLog($data){
        try{
            foreach ($data as $erp_order_entry_id){
                // 根据批次和入仓号定位到理货记录里的订单明细id
                $order_goods_ids = OrderGoodsModel::where('erp_entery_id',$erp_order_entry_id)
                    ->pluck('order_goods_id')->toArray();
                if (!empty($order_goods_ids)) {
                    $content = '供应链完成报关，等待装载发货。';
                    foreach ($order_goods_ids as $order_goods_id) {
                        OrderTrackingLogLogic::addLog([
                            'log_type'=>OrderTrackingLogModel::log_type_11,
                            'bill_type'=>OrderTrackingLogModel::orderDetail,
                            'bill_id'=>$order_goods_id,
                            'content'=>$content,
                            'create_user_id'=>OrderTrackingLogModel::admin_user_id,
                            'create_user_name'=>OrderTrackingLogModel::admin_user_name
                        ]);
                    }
                }
            }
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @param $data
     * @throws ErpException
     * @throws Exception
     * ERP同步报关
     */
    public function erpSynBaoGuan($data){
        try{
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data){


                $BaoGuanOrder = BaoGuanOrderModel::updateOrCreate(
                    [
                        'bills_id'=>$data['ID'],//单据id
                        'bills_sn'=>$data['NUMBER'],//单据编号
                    ],
                    [
                        'biz_date'=>strtotime($data['BIZDATE']),//业务日期
                        'impex_date'=>strtotime($data['ImpExpDate']),//进出口日期
                        'declaration_date'=>strtotime($data['DeclarationDate']),//申报日期
                        'status'=>1,
                        'cfdlearance_type'=>isset($data['CFDeclarationTypeI']) ? $data['CFDeclarationTypeI'] : '',//报关类型
                        'cfclearance_type'=>isset($data['CFClearanceType']) ? $data['CFClearanceType'] : 0,//通关方式 0汇总征税 1先征后方
                        'customs_code'=>isset($data['customs_code']) ? $data['customs_code'] : '',//海关编码
                        'company'=>isset($data['company']) ? $data['company'] : '',//消费使用单位
                    ]
                );
                if(!$BaoGuanOrder) throw new ErpException('ERP同步报关失败');

                if(BaoGuanOrderListModel::where("baoguan_order_id",$BaoGuanOrder->baoguan_order_id)->count()){
                    $bk = BaoGuanOrderListModel::where("baoguan_order_id",$BaoGuanOrder->baoguan_order_id)->delete();
                    if(!$bk) throw new ErpException('ERP同步报关失败');
                }

                foreach($data['ENTRYS'] as $item){
                    if(!isset($item['ORDERNUMBER']) || !$item['ORDERNUMBER']) throw new ErpException('没找到委托单号');
                    if(!isset($item['MATERIALNO']) || !$item['MATERIALNO']) throw new ErpException('没找到物料编号');
                    $order = OrderModel::where("erp_order_sn",trim($item['ORDERNUMBER']))->select("user_id","company_id","order_id")->first();

                    if (empty($order)){
                        throw new ErpException($item['ORDERNUMBER'].'订单不存在，请先同步订单');
                    }
                    $bk = BaoGuanOrderListModel::create([
                        'status'=>1,
                        'user_id'=>$order->user_id,
                        'company_id'=>$order->company_id,
                        'order_id'=>$order->order_id,
                        'bills_sn'=>$data['NUMBER'],//单据编号
                        'baoguan_order_id'=>$BaoGuanOrder->baoguan_order_id,
                        'erp_order_sn'=>$item['ORDERNUMBER'],//委托单号
                        'material_sn'=>$item['MATERIALNO'],//物料编号
                        'qty'=>isset($item['QTY']) ? $item['QTY'] : 0,//数量
                        'price'=>isset($item['PRICE']) ? $item['PRICE'] : 0,//单价
                        'amount'=>isset($item['AMOUNT']) ? $item['AMOUNT'] : 0,//金额
                        'currency'=>isset($item['CURRENCY']) ? $item['CURRENCY'] : 0,//成交币别
                        'preight'=>isset($item['FREIGHT']) ? $item['FREIGHT'] : 0,//运费
                        'insurance'=>isset($item['INSURANCE']) ? $item['INSURANCE'] : 0,//保费
                        'incidentals'=>isset($item['INCIDENTALS']) ? $item['INCIDENTALS'] : 0,//杂费
                        'vat'=>isset($item['VAT']) ? $item['VAT'] : 0,//增值税
                        'tariff'=>isset($item['TARIFF']) ? $item['TARIFF'] : 0,//关税
                        'dlf_amount'=>isset($item['DLFAMOUNT']) ? $item['DLFAMOUNT'] : 0 ,//代理费
                        'landtax'=>isset($item['LANDTAX']) ? $item['LANDTAX'] : 0,//产地税
                        'goods_title'=>isset($item['PRODUCTNAME']) ? $item['PRODUCTNAME'] : '',//品名
                        'goods_type'=>isset($item['MODEL']) ? $item['MODEL'] : '',//型号
                        'brand'=>isset($item['MANUFACTURER']) ? $item['MANUFACTURER'] : '',//品牌
                        'batch'=>isset($item['batch']) ? $item['batch'] : '',//批次
                        'is_baoche'=>isset($data['IsBaoChe']) ? $data['IsBaoChe'] : 0,//是否包车
                        'six_no'=>isset($data['SixNo']) ? $data['SixNo'] : '',//六联单号

                        'is_goods_check'=>OrderLogic::getOrderIsGoodsCheck(['order_id'=>$order->order_id]),//是否商检 0否1是2暂时没有数据
                    ]);
                    if(!$bk) throw new ErpException('ERP同步报关失败');

                    //修改深圳关别
                    if (isset($data['Port']) && !empty($data['Port'])){
                        HongKongDeliveryModel::where('order_id',$order->order_id)->update(['hk_port'=>$data['Port']]);
                    }

                    //修改订单报关状态
                    $this->changeOrderStatus($order->order_id,'baoguan_status');
                }
            });

            //推送消息给用户
            foreach($data['ENTRYS'] as $_item){
                $this->sendMsg($custom_func='3',$data=['erp_order_sn'=>$_item['ORDERNUMBER']]);
            }
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /*
     * 删除报关
     */
    public function erpDelBaoGuan($data){
        try{
            if(!isset($data['ID'])) throw new ErpException('ERP删除报关失败');
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data){
                $obj = BaoGuanOrderModel::where("bills_id",$data['ID'])->firstOrFail();
                $obj->status = 0;
                if(!$obj->save())  throw new ErpException('ERP删除报关失败');

                $bk = BaoGuanOrderListModel::where("baoguan_order_id",$obj->baoguan_order_id)->update(['status'=>0]);
                if($bk === false)  throw new ErpException('ERP删除报关失败');

                //清除订单的报关状态
                if($obj->baoguan_order_list){
                    foreach($obj->baoguan_order_list as $item){
                        $this->changeOrderStatus($item->order_id,'baoguan_status');
                    }
                }

            });

        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("ERP删除报关失败");
        }
    }

    /**
     * @param $data
     * @throws ErpException
     * @throws Exception
     * 香港收货
     */
    public function erpSynHKDelivery($data){

         try{
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data){
                $order = OrderModel::where("erp_order_sn",trim($data['ORDERNUMBER']))->firstOrFail();
                if(!isset($data['ID']) || !isset($data['NUMBER']))  throw new ErpException('ERP同步香港收货失败');
                $hk_delivery_note = HKDeliveryNoteModel::updateOrCreate(
                    [
                        'bills_id'=>$data['ID'],//单据id
                        'bills_sn'=>$data['NUMBER'],//单据编号
                    ],
                    [
                    'total_grossweight'=>isset($data['TOTALGROSSWEIGHT']) ? sprintf("%.2f",substr(sprintf("%.3f", $data['TOTALGROSSWEIGHT']), 0, -2)) : 0,//总毛重
                    'total_num'=>isset($data['TOTALNUM']) ? intval($data['TOTALNUM']) : 0,//总件数
                    'erp_order_sn'=>$data['ORDERNUMBER'],//erp订单号
                    'warehouse'=>isset($data['WAREHOUSE']) ? $data['WAREHOUSE'] : '',//仓库
                    'auditor'=>isset($data['AUDITOR']) ? $data['AUDITOR'] : '',//收货人
                    'audit_date'=>isset($data['AUDITDATE'])? strtotime($data['AUDITDATE']) : 0,//收货时间
                    'user_id'=>$order->user_id,//用户ID
                    'company_id'=>$order->company_id,//用户ID
                    'order_id'=>$order->order_id,//订单id
                    'status'=>1,
                ]);
                if(!$hk_delivery_note)throw new ErpException('ERP同步香港收货失败');

                if(HKDeliveryNoteListModel::where("hk_delivery_note_id",$hk_delivery_note->id)->count()){
                    $bk = HKDeliveryNoteListModel::where("hk_delivery_note_id",$hk_delivery_note->id)->delete();
                    if(!$bk) throw new ErpException('ERP同步香港收货失败');
                }

                foreach($data['ENTRYS'] as $item){
                    $bk = HKDeliveryNoteListModel::create([
                        'hk_delivery_note_id'=>$hk_delivery_note->id,
                        'bills_sn'=>$data['NUMBER'],//单据编号
                        'user_id'=>$order->user_id,
                        'company_id'=>$order->company_id,
                        'order_id'=>$order->order_id,//订单id
                        'goods_title'=>isset($item['PRODUCTNAME']) ? $item['PRODUCTNAME'] : '',//品名
                        'goods_type'=>isset($item['MODEL']) ? $item['MODEL'] : '',//型号
                        'brand'=>isset($item['MANUFACTURER']) ? $item['MANUFACTURER'] : '',//品牌
                        'material_sn'=>$item['MATERIALNO'],//物料编号
                        'qty'=>$item['QTY'],//入库数量
                        'gross_weight'=>$item['GROSSWEIGHT'],//毛重
                        'net_weight'=>$item['NETWEIGHT'],//净值
                        'amount'=>isset($item['AMOUNT']) ? $item['AMOUNT'] : 0,//金额
                    ]);
                    if(!$bk) throw new ErpException('ERP同步香港收货失败');
                }

                //修改订单状态
                $this->changeOrderStatus($order->order_id,'hk_delivery_status');

            });


            //推送消息给用户
             //$this->sendMsg($custom_func='2',$data=['erp_order_sn'=>$data['ORDERNUMBER']]);

         }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("ERP同步香港收货失败");
        }
    }

    /**
     * @param $data
     * @throws ErpException
     * @throws Exception
     * @throws \Throwable
     * 删除香港收货单
     */
    public function erpDelHKDelivery($data){
        try{
            if(!isset($data['ID'])) throw new ErpException('ERP删除香港收货单失败');
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data){
                $obj = HKDeliveryNoteModel::where("bills_id",$data['ID'])->firstOrFail();
                $obj->status = 0;
                if(!$obj->save())  throw new ErpException('ERP删除香港收货单失败');

                if(HKDeliveryNoteListModel::where("hk_delivery_note_id",$obj->id)->count()){
                    $bk = HKDeliveryNoteListModel::where("hk_delivery_note_id",$obj->id)->delete();
                    if(!$bk) throw new ErpException('ERP删除香港收货单失败');
                }
                //清除订单的报关状态
                if($obj->order_id){
                    $this->changeOrderStatus($obj->order_id,'hk_delivery_status');
                }

            });

        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("删除香港收货单失败");
        }
    }

    /**
     * @param $data
     * 同步深圳收货信息
     */
    public function erpSynSZDelivery($data){
        try{
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data){
                $order = OrderModel::where("erp_order_sn",trim($data['ORDERNUMBER']))->firstOrFail();
                if(!isset($data['ID']) || !isset($data['NUMBER']))  throw new ErpException('ERP同步深圳收货失败');
                $sz_delivery_note = SZDeliveryNoteModel::updateOrCreate(
                    [
                        'bills_id'=>$data['ID'],//单据id
                        'bills_sn'=>$data['NUMBER'],//单据编号
                    ],
                    [
                        'total_grossweight'=>isset($data['TOTALGROSSWEIGHT']) ? sprintf("%.2f",substr(sprintf("%.3f", $data['TOTALGROSSWEIGHT']), 0, -2)) : 0,//总毛重
                        'total_num'=>isset($data['TOTALNUM']) ? intval($data['TOTALNUM']) : 0,//总件数
                        'erp_order_sn'=>$data['ORDERNUMBER'],//erp订单号
                        'warehouse'=>isset($data['WAREHOUSE']) ? $data['WAREHOUSE'] : '',//仓库
                        'auditor'=>isset($data['AUDITOR']) ? $data['AUDITOR'] : '',//收货人
                        'audit_date'=>isset($data['AUDITDATE'])? strtotime($data['AUDITDATE']) : 0,//收货时间
                        'company_id'=>$order->company_id,//用户ID
                        'user_id'=>$order->user_id,//用户ID
                        'order_id'=>$order->order_id,//订单id
                        'status'=>1,
                    ]);
                if(!$sz_delivery_note)throw new ErpException('ERP同步深圳收货失败');

                if(SZDeliveryNoteListModel::where("sz_delivery_note_id",$sz_delivery_note->id)->count()){
                    $bk = SZDeliveryNoteListModel::where("sz_delivery_note_id",$sz_delivery_note->id)->delete();
                    if(!$bk) throw new ErpException('ERP同步深圳收货失败');
                }

                foreach($data['ENTRYS'] as $item){
                    $bk = SZDeliveryNoteListModel::create([
                        'sz_delivery_note_id'=>$sz_delivery_note->id,
                        'bills_sn'=>$data['NUMBER'],//单据编号
                        'user_id'=>$order->user_id,
                        'company_id'=>$order->company_id,
                        'order_id'=>$order->order_id,//订单id
                        'goods_title'=>isset($item['PRODUCTNAME']) ? $item['PRODUCTNAME'] : '',//品名
                        'goods_type'=>isset($item['MODEL']) ? $item['MODEL'] : '',//型号
                        'brand'=>isset($item['MANUFACTURER']) ? $item['MANUFACTURER'] : '',//品牌
                        'material_sn'=>$item['MATERIALNO'],//物料编号
                        'qty'=>$item['QTY'],//入库数量
                        'gross_weight'=>$item['GROSSWEIGHT'],//毛重
                        'net_weight'=>$item['NETWEIGHT'],//净值
                        'amount'=>isset($item['AMOUNT']) ? $item['AMOUNT'] : 0,//金额
                    ]);
                    if(!$bk) throw new ErpException('ERP同步深圳收货失败');
                }

                //修改订单状态
                $this->changeOrderStatus($order->order_id,'sz_delivery_status');

            });

            //推送消息给用户
            $this->sendMsg($custom_func='4',$data=['erp_order_sn'=>$data['ORDERNUMBER']]);


        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("ERP同步深圳收货信息失败");
        }
    }

    /**
     * @param $data
     * @throws ErpException
     * @throws Exception
     * @throws \Throwable
     * 深圳收货删除
     */
    public function erpDelSZDelivery($data){
        try{
            if(!isset($data['ID'])) throw new ErpException('ERP删除深圳收货单失败');
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data){
                $obj = SZDeliveryNoteModel::where("bills_id",$data['ID'])->firstOrFail();
                $obj->status = 0;
                if(!$obj->save())  throw new ErpException('ERP删除深圳收货单失败');

                if(SZDeliveryNoteListModel::where("sz_delivery_note_id",$obj->id)->count()){
                    $bk = SZDeliveryNoteListModel::where("sz_delivery_note_id",$obj->id)->delete();
                    if(!$bk) throw new ErpException('ERP删除深圳收货单失败');
                }


                if($obj->order_id){
                    $this->changeOrderStatus($obj->order_id,'sz_delivery_status');
                }

            });

        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("删除深圳收货单失败");
        }
    }

    /**
     * @param $data
     * @throws ErpException
     * @throws Exception
     * 深圳发货
     */
    public function erpSynSZSendDelivery($data){
        try{
            $order = OrderModel::where("erp_order_sn",trim($data['ORDERNUMBER']))->firstOrFail();
            if(!isset($data['ID']) || !isset($data['NUMBER']))  throw new ErpException('ERP同步深圳发货失败');
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data,$order){
                $sz_shipments_note = SZShipmentsNoteModel::updateOrCreate([
                    'bills_id'=>$data['ID'],//单据id
                    'bills_sn'=>$data['NUMBER'],//单据编号
                ],[
                    'erp_order_sn'=>$data['ORDERNUMBER'],//erp订单号
                    'warehouse'=> isset($data['WAREHOUSESETUP']) ? $data['WAREHOUSESETUP'] : '',//仓库
                    'shipments_man_code'=>isset($data['CUSTOMER']) ? $data['CUSTOMER'] : '',//发货人编码
                    'biz_date'=>isset($data['BIZDATE']) ? strtotime($data['BIZDATE']) : 0,//发货时间
                    'carrier'=>isset($data['SUPPLIER']) ? trim($data['SUPPLIER']) : 0,//承运商
                    'stream_number'=>isset($data['STREAMNUMBER']) ? trim($data['STREAMNUMBER']) : '',//物流单号
                    'receiving_address'=>isset($data['RECEIVINGADDRESS']) ? trim($data['RECEIVINGADDRESS']) : '',//收货地址
                    'attention'=>isset($data['ATTENTION']) ? trim($data['ATTENTION']) : '',//收货联系人
                    'tele_phone'=>isset($data['TELEPHONE']) ? trim($data['TELEPHONE']) : '',//收货电话
                    'count'=>isset($data['Count']) ? trim($data['Count']) : '',//收货件数
                    'user_id'=>$order->user_id,//用户ID
                    'company_id'=>$order->company_id,//用户ID
                    'order_id'=>$order->order_id,//订单id
                    'status'=>1,
                ]);

                if(!$sz_shipments_note)throw new ErpException('ERP同步深圳发货失败');

                if(SZShipmentsNoteListModel::where("sz_shipments_note_id",$sz_shipments_note->id)->count()){
                    $bk = SZShipmentsNoteListModel::where("sz_shipments_note_id",$sz_shipments_note->id)->delete();
                    if(!$bk) throw new ErpException('ERP同步深圳发货失败');
                }

                foreach($data['ENTRYS'] as $item){
                    $bk = SZShipmentsNoteListModel::create([
                        'sz_shipments_note_id'=>$sz_shipments_note->id,
                        'bills_sn'=>$data['NUMBER'],//单据编号
                        'user_id'=>$order->user_id,
                        'company_id'=>$order->company_id,
                        'order_id'=>$order->order_id,//订单id
                        'goods_title'=>isset($item['PRODUCTNAME']) ? $item['PRODUCTNAME'] : '',//品名
                        'goods_type'=>isset($item['MODEL']) ? $item['MODEL'] : '',//型号
                        'brand'=>isset($item['MANUFACTURER']) ? $item['MANUFACTURER'] : '',//品牌
                        'material_sn'=>$item['MATERIALNO'],//物料编号
                        'qty'=>$item['INQTY'],//发货数量
                        'amount'=>isset($item['AMOUNT']) ? $item['AMOUNT'] : 0,//金额
                    ]);
                    if(!$bk) throw new ErpException('ERP同步深圳发货失败');
                }
                //更新订单状态
                $this->changeOrderStatus($order->order_id,'sz_send_status');

            });

            //推送消息给用户
            //$this->sendMsg($custom_func='5',$data=['erp_order_sn'=>$data['ORDERNUMBER']]);

        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("ERP同步深圳发货信息失败");
        }
    }

    /*
     * erp删除深圳发货信息
     */
    public function erpDelSZSendDelivery($data){
        try{
            if(!isset($data['ID'])) throw new ErpException('ERP删除深圳发货信息失败');
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data){
                $obj = SZShipmentsNoteModel::where("bills_id",$data['ID'])->firstOrFail();
                $obj->status = 0;
                if(!$obj->save())  throw new ErpException('ERP删除深圳发货信息失败');

                if(SZShipmentsNoteListModel::where("sz_shipments_note_id",$obj->id)->count()){
                    $bk = SZShipmentsNoteListModel::where("sz_shipments_note_id",$obj->id)->delete();
                    if(!$bk) throw new ErpException('ERP删除深圳发货信息失败');
                }

                //更新订单状态
                $this->changeOrderStatus($obj->order_id,'sz_send_status');
            });

        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("删除深圳发货信息失败");
        }

    }

    /**
     * @param $data
     * 核销明细
     */
    public function erpSyncVerification($data){
        try{
            if(!$data['ENTRYS']) return true;
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data){
                $deleteIdArr = VerificationModel::where(["erp_cuntomer_id"=>$data['CUSTOMERID']])->pluck('id')->toArray();
                $bk = VerificationModel::whereIn('id',$deleteIdArr)->delete();
                if($bk === false) throw new ErpException('erp同步核销明细失败');
                foreach($data['ENTRYS'] as $item){
                    $order = OrderModel::where("erp_order_sn",trim($item['ORDERNUMBER']))->first();
                    if(!$order) continue;
                    $bk = VerificationModel::create([
                        "erp_cuntomer_id"=>$data['CUSTOMERID'],
                        "order_sn"=>$order->order_sn,
                        "order_id"=>$order->order_id,
                        "user_id"=>$order->user_id,
                        "company_id"=>$order->company_id,
                        "erp_order_sn"=>$item['ORDERNUMBER'],
                        'biz_date'=>isset($item['BIZDATE']) ? strtotime($item['BIZDATE']) : 0,//业务日期
                        'hxbiz_date'=>isset($item['HXBIZDATE']) ? strtotime($item['HXBIZDATE']) : 0,//核销时间
                        'project'=>isset($item['PROJECT']) ? trim($item['PROJECT']) : '',//计费项目
                        'exchange_rate'=>isset($item['EXCHANGERATE']) ? $item['EXCHANGERATE'] : '',//汇率
                        'currency'=>isset($item['CURRENCY']) ? $item['CURRENCY'] : '',//币别
                        'amount'=>isset($item['AMOUNT']) ? $item['AMOUNT'] : 0,//原币金额
                        'local_amount'=>isset($item['LOCALAMOUNT']) ? $item['LOCALAMOUNT'] : 0,//本位币金额
                        'hx_amount'=>isset($item['HXAMOUNT']) ? $item['HXAMOUNT'] : 0,//核銷原币金額
                        'hx_local_amount'=>isset($item['HXLOCALAMOUNT']) ? $item['HXLOCALAMOUNT'] : 0,//核銷原币金額
                    ]);
                    if($bk === false) throw new ErpException('erp同步核销明细失败');
                }
            });

        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("erp同步核销明细失败");
        }
    }

    /**
     * 解绑用户
     */
    public function adminUnBindUser($customer_id){
        $customer = CustomerModel::where("customer_id",$customer_id)->select("user_id")->first();
        if(!$customer) return false;
        S("supply:user_id:".$customer->user_id.":company_id",null);
    }

    /**
     * erp推送 客户和委托方到本地
     */
    public function synGetCustomerAndPrincipal($data){
        try{
            $arr['PERPID'] = "";//erp委托方id
            $arr['PPTID'] = "";//平台委托方id
            $arr['CID'] = "";//erp客户id
            $arr['CPID'] = "";//客户平台id
            $arr['BERPID'] = "";//业务联系人erpid
            $arr['BPTENTRYID'] = "";//业务联系人平台id
            $arr['RPTENTRYID'] = "";//收货平台id
            $arr['RERPID'] = "";//收货联系人erpid

            DB::connection('SUPPLYCHAIN')->transaction(function()use($data,&$arr){
                if(!$data['CID'] || $data['CID'] == " "){
                    throw new ErpException("erp客户id为空");
                }
                //新增公司信息
                $companyData['company_full_name'] = trim($data['CUSTOMERNAME']);//公司全称
                $companyData['address'] = isset($data['PBUSINESSADDR']) ? trim($data['PBUSINESSADDR']) : '';//公司地址
                $companyData['organization_code'] = isset($data['ORGANIZATCODE']) ? trim($data['ORGANIZATCODE']) : '';//组织机构代码
                $companyData['establishment_date'] = isset($data['ADDDATE']) ? strtotime(trim($data['ADDDATE'])) : 0;//成立日期
                $companyData['registered_currency'] = isset($data['PCURRENCY']) ? trim($data['PCURRENCY']) : '';//注册币别
                $companyData['trade_registration'] = isset($data['ICNUMBER']) ? trim($data['ICNUMBER']) : '';//工商注册号
                $companyData['registered_capital'] = isset($data['REGISTCAPITAL']) ? floatval($data['REGISTCAPITAL']) : '';//注册资金
                $companyData['unified_social_credit_code'] = isset($data['PUNIFYCREDITCODE']) ? trim($data['PUNIFYCREDITCODE']) : '';//统一社会信用代码
                $companyData['legal_person'] = isset($data['CORPORATER']) ? trim($data['CORPORATER']) : '';//公司法人
                //创建公司信息
                if($companyData['company_full_name'] && $companyData['company_full_name'] != " "){
                    $company = CompanyModel::updateOrCreate([
                        "company_id"=>isset($data['PPTID']) ? $data['PPTID'] : 0,
                    ],$companyData);
                }

                //创建客户信息
                $customerData['status'] = 4;
                $customerData['is_push'] = 1;
                $customerData['company_id'] = $company->company_id;
                $customerData['auditor'] = "admin";
                $customerData['auditor_time'] = time();
                $customerData['erp_cuntomer_id'] = $data['CID'];//erp客户id
                $customerData['customer_code'] = $data['CNUMBER'];///客户编码
                $customerData['erp_client_id'] = $data['PERPID'];///erp委托方id
                $customerData['erp_client_code'] = $data['PNUMBER'];///委托方编码
                $customerData['customer_name'] = $data['CUSTOMERNAME'];///客户名
                $customerData['customer_short_name'] = isset($data['SIMPLENMAE']) ? $data['SIMPLENMAE'] : '';///客户简称
                $customerData['mnemonic_code'] = isset($data['HELPERCODE']) ? $data['HELPERCODE'] : '';///助记码
                $customerData['follow_people'] = isset($data['CUSTOMERFOLLOWER']) ? $data['CUSTOMERFOLLOWER'] : '';///跟进人
                CustomerModel::updateOrCreate([
                    "company_id"=>$company->company_id,
                ],$customerData);

                //创建业务联系人
                if(isset($data['BCONTACTER']) && $data['BCONTACTER']){
                    $contactData['contact_name'] = $data['BCONTACTER'];//联系人称呼
                    $contactData['contact_phone'] = isset($data['BMOBILE']) ? $data['BMOBILE'] : '';//业务联系人手机
                    $contactData['special_plane'] = isset($data['BPHONE']) ? $data['BPHONE'] : '';//座机
                    $contactData['company_id'] =  $company->company_id;
                    $contactData['erp_entry_id'] =  $data['BERPID'];//业务联系人erpid
                    $bk = ContactModel::updateOrCreate([
                        "contact_id"=>$data['BPTENTRYID'],
                        "company_id"=>$company->company_id,
                    ],$contactData);
                    if($bk){
                        $arr['BERPID'] = $data['BERPID'];//业务联系人erpid
                        $arr['BPTENTRYID'] = $bk->contact_id;//业务联系人平台id
                    }
                }

                //创建发票
                ComInvoiceModel::updateOrCreate(["company_id"=>$company->company_id],[
                    "type"=>2,
                    "status"=>1,
                    "tax_identifying_sn"=>isset($data['FTXREGISTERNO']) ? $data['FTXREGISTERNO'] : '',//税务编号
                    "tax_certificate_no"=>isset($data['FTXREGISTERNO']) ? $data['FTXREGISTERNO'] : '',//纳税登记证号
                    "invoice_address"=>isset($data['ADDRESS']) ? $data['ADDRESS'] : '',//开票地址
                    "invoice_mobile"=>isset($data['INVOICEPHONE']) ? $data['INVOICEPHONE'] : '',//开票电话
                    "bank_name"=>isset($data['INVOICEBANK']) ? $data['INVOICEBANK'] : '',//开户行名称
                    "bank_number"=>isset($data['INVOICEACCOUNT']) ? $data['INVOICEACCOUNT'] : '',//开户行账号
                ]);


                //创建收货地址
                if(isset($data['RERPID']) && $data['RERPID'] && isset($data['RCONTACTTER']) && $data['RCONTACTTER'] != " " &&  $data['RCONTACTTER']) {
                    list($intl_code, $mobile) = $this->getMobile($data['RMOBILE']);//手机
                    list($seat_number_code, $seat_number) = $this->getPhone($data['RPHONE']);//座机
                    $bk = UserDeliveryModel::updateOrCreate([
                        "com_delivery_id" => isset($data['RPTENTRYID']) ? $data['RPTENTRYID'] : '',
                    ], [
                        "erp_entry_id" => $data['RERPID'],
                        "consignee" => $data['RCONTACTTER'],
                        "company_id" => $company->company_id,
                        "detail_address" => isset($data['SADDRESS']) ? $data['SADDRESS'] : '',
                        "mobile" => $mobile ? $mobile : '',
                        "intl_code" => $intl_code ? $intl_code : '',
                        "seat_number" => $seat_number ? $seat_number : '',
                        "seat_number_code" => $seat_number_code ? $seat_number_code : '',

                    ]);
                    if($bk){
                        $arr['RPTENTRYID'] = $bk->com_delivery_id;//收货平台id
                        $arr['RERPID'] = $data['RERPID'];//收货联系人erpid
                    }
                }


                $arr['PERPID'] = $data['PERPID'];//erp委托方id
                $arr['PPTID'] = $company->company_id;//平台委托方id
                $arr['CID'] = $data['CID'];//erp客户id
                $arr['CPID'] = $company->company_id;//客户平台id

            });

            return $arr;
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $arr;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("erp推送 客户和委托方到本地失败");
        }
    }


    /**
     * erp推送 供应商和银行信息到本地
     */
    public function synGetSupplierAndBank($data){
        try{
            $arr=[];
            DB::connection('SUPPLYCHAIN')->transaction(function()use($data,&$arr){
                if(!isset($data['SUPPLIER']) || !$data['SUPPLIER']) throw new ErpException("推送的供应商名称为空");
                $supplyData['supplier_name'] = $data['SUPPLIER'];//供应商名称
                $supplyData['supplier_short_name'] = isset($data['SIMPLENAME']) ? $data['SIMPLENAME'] : '';//供应商简称
                $supplyData['erp_supplier_code'] = isset($data['SUPPNUMBER']) ? $data['SUPPNUMBER'] : '';//erp关联供应商编码
                $supplyData['erp_supplier_id'] = isset($data['ERPID']) ? $data['ERPID'] : 0;//erp关联供应商id
                $supplyData['bus_license_number'] = isset($data['BUSILICENCE']) ? $data['BUSILICENCE'] : '';//营业执照号
                $supplyData['is_push'] = 1;
                $supplyData['status'] = 1;
                $supplyData['auditor'] = 'admin';
                $supplyData['company_id'] = 0;
                $supplyData['audit_time'] = time();
                $supply = SupplierModel::updateOrCreate([
                    "erp_supplier_id"=>$supplyData['erp_supplier_id']
                ],$supplyData);


                //更新或新增银行信息
                if(isset($data['BERPID']) && $data['BERPID'] && isset($data['BANKACCOUNT']) && $data['BANKACCOUNT'] != " " && !empty($data['BANKACCOUNT'])
                    && isset($data['BANKACCOUNT']) && $data['BANKACCOUNT'] != " " && !empty($data['BANKACCOUNT']) ){
                    $supplyBankData['bank_name'] = isset($data['BANK']) ? $data['BANK'] : '';//银行名称
                    $supplyBankData['bank_account'] = isset($data['BANKACCOUNT']) ? $data['BANKACCOUNT'] : '';//银行账号
                    $supplyBankData['bank_address'] = isset($data['BANKADDRESSS']) ? $data['BANKADDRESSS'] : '';//银行地址
                    //********新增银行信息
                    $supplyBankData['bank_user'] = isset($data['RECEIVERNAME']) ? $data['RECEIVERNAME'] : '';//收款账号名称

                    if(isset($data['CURRENCY']) && $data['CURRENCY']){
                        $currency = array_search($data['CURRENCY'],C("supply_currency"));
                        $supplyBankData['currency_id'] = $currency ? $currency : 12;//币别
                    }

                    if(isset($data['ISHK']) && in_array($data['ISHK'],[0,1])){
                        $supplyBankData['area'] = ($data['ISHK'] == 1)  ? 1 : 2;//1香港境内 2香港境外
                    }

                    $supplyBankData['swift_code'] = isset($data['SWIFCODE']) ? $data['SWIFCODE'] : '';//swift_code

                    if(isset($data['COUNTRY'])){
                        $code = CountryModel::getCodeByName(trim($data['COUNTRY']));
                        $supplyBankData['recipient_country'] = $code ? $code : 142;//收款方所在国家
                    }
                    $supplyBankData['supplier_id'] = $supply->supplier_id;//供应商id
                    $supplyBankData['status'] = 1;
                    $supplyBankData['auditor'] = 'admin';
                    $supplyBankData['erp_entry_id'] = $data['BERPID'];//erp关联主键id
                    $bk = SupplierBankModel::updateOrCreate([
                        'erp_entry_id'=>$data['BERPID'],
                        'supplier_bank_id'=>$data['PTENTRYID'],
                    ],$supplyBankData);
                    $arr['BERPID'] = $data['BERPID'];//供应商对应银行erpid
                    $arr['BPTID'] = $bk->supplier_bank_id;//供应商对应银行平台id
                }

                $arr['ERPID'] = $supplyData['erp_supplier_id'];//供应商erpid
                $arr['PTID'] = $supply->supplier_id;//平台供应商id

            });
            return $arr;
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new ErpException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception("erp推送 供应商和银行信息到本地失败");
        }
    }



    /**
     * erp推送 供应商和银行信息到本地
     */
    public function syncSupplier($data)
    {

        if(!isset($data['SUPPLIER']) || !$data['SUPPLIER']) {
            throw new Exception("推送的供应商名称不能为空");
        }

        if(!isset($data['ERPID']) || !$data['ERPID']) {
            throw new Exception("推送的erp关联供应商id不能为空");
        }

        if(!isset($data['ACTIVE']) || !$data['ACTIVE']) {
            throw new Exception("操作类型不能为空");
        }

        $supplierName = $supplyData['supplier_name'] = $data['SUPPLIER'];//供应商名称
        $supplyData['supplier_short_name'] = isset($data['SIMPLENAME']) ? $data['SIMPLENAME'] : '';//供应商简称
        $supplyData['erp_supplier_code'] = isset($data['SUPPNUMBER']) ? $data['SUPPNUMBER'] : '';//erp关联供应商编码
        $erpSupplierId = $supplyData['erp_supplier_id'] = isset($data['ERPID']) ? $data['ERPID'] : 0;//erp关联供应商id
        $supplyData['bus_license_number'] = isset($data['BUSILICENCE']) ? $data['BUSILICENCE'] : '';//营业执照号
        $supplyData['is_push'] = 1;
        $supplyData['status'] = 1;
        $supplyData['auditor'] = 'admin';
        $supplyData['company_id'] = 0;
        $supplyData['audit_time'] = time();
        $supplyData['create_time'] = time();

        $supplyModel = new SupplierModel();
        if ($data['ACTIVE'] == 'INSERT'){

            //如果供应商名称，或者供应商id存在，则不允许新增
            if ( $supplierId = $supplyModel->where('erp_supplier_id',$erpSupplierId)->where('status',1)->value('supplier_id')){
                throw new Exception('已经存在已审核的erp关联供应商id，请确认平台ID:'.$supplierId);
            }
            //如果供应商名称，或者供应商id存在，则不允许新增
            if ( $supplierId = $supplyModel->where('supplier_name',$supplierName)->where('status',1)->value('supplier_id')){
                throw new Exception('已经存在已审核的供应商名称，请确认平台ID:'.$supplierId);
            }

            $supplierId = $supplyModel->insertGetId($supplyData);

        }elseif ($data['ACTIVE'] == 'UPDATE'){

            if (empty($supplierId = $supplyModel->where('erp_supplier_id',$erpSupplierId)->where('status',1)->value('supplier_id'))){
                throw new Exception('暂未存在已审核的erp关联供应商id，请确认平台信息');
            }
            unset($supplyData['erp_supplier_id']);
            $supplyModel->where('erp_supplier_id',$erpSupplierId)->update($supplyData);
        }else{
            throw new Exception('暂无该种操作类型');
        }

        return ['supplier_id'=>$supplierId];
    }

    /**
     * erp推送 供应商和银行信息到本地
     */
    public function syncSupplierBank($data)
    {

        $supplyModel = new SupplierModel();
        $supplyBankModel = new SupplierBankModel();
        if (empty($supplierId = $supplyModel->where('erp_supplier_id',$data['ERPID'])->where('status',1)->value('supplier_id'))){
            throw new Exception('暂未存在已审核的erp关联供应商id，请确认平台信息');
        }


        $supplyBankData['bank_name'] = isset($data['BANK']) ? $data['BANK'] : '';//银行名称
        $supplyBankData['bank_account'] = isset($data['BANKACCOUNT']) ? $data['BANKACCOUNT'] : '';//银行账号
        $supplyBankData['bank_address'] = isset($data['BANKADDRESSS']) ? $data['BANKADDRESSS'] : '';//银行地址
        $supplyBankData['bank_user'] = isset($data['RECEIVERNAME']) ? $data['RECEIVERNAME'] : '';//收款账号名称

        if(isset($data['CURRENCY']) && $data['CURRENCY']){
            $currency = array_search($data['CURRENCY'],C("supply_currency"));
            $supplyBankData['currency_id'] = $currency ? $currency : 12;//币别
        }

        if(isset($data['ISHK']) && in_array($data['ISHK'],[0,1])){
            $supplyBankData['area'] = ($data['ISHK'] == 1)  ? 1 : 2;//1香港境内 2香港境外
        }

        $supplyBankData['swift_code'] = isset($data['SWIFCODE']) ? $data['SWIFCODE'] : '';//swift_code

        if(isset($data['COUNTRY'])){
            $code = CountryModel::getCodeByName(trim($data['COUNTRY']));
            $supplyBankData['recipient_country'] = $code ? $code : 142;//收款方所在国家
        }
        $supplyBankData['supplier_id'] = $supplierId;//供应商id
        $supplyBankData['status'] = 1;
        $supplyBankData['auditor'] = 'admin';
        $supplyBankData['erp_entry_id'] = $data['BERPID'];//erp关联主键id
        $supplyBankData['create_time'] = time();
        $supplyBankData['audit_time'] = time();

        if ($data['ACTIVE'] == 'INSERT'){

            //如果该供应商下免银行名称&银行账号，则不允许新增
            if ( $supplierBankId = $supplyBankModel
                ->where('supplier_id',$supplierId)
                ->where('bank_name',$supplyBankData['bank_name'])
                ->where('bank_account',$supplyBankData['bank_account'])
                ->where('status',1)
                ->value('supplier_bank_id')){
                throw new Exception('该供应商下面已经存在已审核的供应商账号，请确认平台银行ID:'.$supplierBankId);
            }

            if ( $supplierBankId = $supplyBankModel
                ->where('erp_entry_id',$data['BERPID'])
                ->where('status',1)
                ->value('supplier_bank_id')){
                throw new Exception('已经存在已审核EasId，请确认平台银行ID:'.$supplierBankId);
            }

            $supplierBankId = $supplyBankModel->insertGetId($supplyBankData);

        }elseif ($data['ACTIVE'] == 'UPDATE'){

            if (empty($supplierBankId = $supplyBankModel->where('erp_entry_id',$data['BERPID'])->where('status',1)->value('supplier_id'))){
                throw new Exception('暂未存在已审核的erp关联供应商id，请确认平台信息');
            }

            unset($supplyBankModel['BERPID']);

            $supplyBankModel->where(['erp_entry_id'=>$data['BERPID']])->update($supplyBankData);
        }else{
            throw new Exception('暂无该种操作类型');
        }

        return ['supplier_bank_id'=>$supplierBankId];
    }


    public function getErpOrderSnTray($data)
    {
        $returnArr = [];

        foreach ($data['erpOrderSnArr'] as $value){
            $wstytyIdArr = WmsTrayBoxScanDetailModel::where('erp_order_sn',$value)->where('is_del',0)->groupBy('wstyty_id')->pluck('wstyty_id')->toArray();
            if (empty($returnArr[$value] = $wstytyIdArr)){
                 continue;
            }
            $returnArr[$value] = WmsTodayTrayModel::whereIn('wstyty_id',$wstytyIdArr)->pluck('tray_name')->toArray();
        }
        return $returnArr;
    }



    public function synGetGoodsRegister($data)
    {

        if (!isset($data['GOODSREGISTERS'])){
            throw new ErpException("暂无需要修改的数据");
        }

        foreach ($data['GOODSREGISTERS'] as $value){

            if (empty($value['ENTRUSTBILL']))throw new ErpException("ENTRUSTBILL is Null");
            if (empty($value['REGISTERDATE']))throw new ErpException("REGISTERDATE is Null");

            if (empty(GoodsRegisterModel::where('entrust_bill','=',$value['ENTRUSTBILL'])->where('get_time','=',strtotime($value['REGISTERDATE']))->where('is_del','=',0)->value('id'))){
                GoodsRegisterModel::insert(['entrust_bill'=>$value['ENTRUSTBILL'],'get_time'=>strtotime($value['REGISTERDATE'])]);
            }
        }
        return ['添加成功'];
    }


    public function synDelGoodsRegister($data)
    {

        if (empty($data)){
            throw new ErpException("暂无需要修改的数据");
        }

        if (empty($data['ENTRUSTBILL']))throw new ErpException("ENTRUSTBILL is Null");
        if (empty($data['REGISTERDATE']))throw new ErpException("REGISTERDATE is Null");

        if(empty(GoodsRegisterModel::where('entrust_bill','=',$data['ENTRUSTBILL'])
            ->where('get_time','=',strtotime($data['REGISTERDATE']))
            ->where('is_del','=',0)
            ->update(['is_del'=>1,'update_time'=>time()]))){
            throw new ErpException("没有修改任何数据");
        }

        return ['修改成功'];
    }


    public function sendErpPayNotifyEmail($data)
    {

        if (empty($data['order_id'])){
            throw new ErpException("订单ID不能为空");
        }

        $requestUrl = CRM_DOMAIN.'/api/sendErpPayNotifyEmail';

        $headers[] = "api-Key:crm a1b2c3d4e5f6g7h8i9jk";
        $body   = ['order_id' => $data['order_id']];
        $postBody    = $body;

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $requestUrl);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);//设置请求头
        curl_setopt($curl, CURLOPT_POSTFIELDS, $postBody);//设置请求体
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');//使用一个自定义的请求信息来代替"GET"或"HEAD"作为HTTP请求。(这个加不加没啥影响)

        $datajson = curl_exec($curl);
        $dataArr = json_decode($datajson,true);

        //如果是-1，则是错误
        if ($dataArr['err_code'] == -1){
            throw new ErpException($dataArr['msg']);
        }

        return '发送成功';
    }

}