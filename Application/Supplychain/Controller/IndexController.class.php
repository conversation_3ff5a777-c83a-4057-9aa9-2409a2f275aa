<?php
namespace Supplychain\Controller;

use Pcb\Model\LiexinUserMain;
use \Supplychain\Controller\BaseController;
use \Pcb\Service\Guzzle;
use Illuminate\Validation\Validator;
use \Exception;
use Supplychain\Exception\SupplyException;
use Illuminate\Container\Container;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\CountryModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\ExchangeRate;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\CustomerLogic;
use Supplychain\Model\Logic\ExportLogic;
use Supplychain\Model\Logic\HongKongOrderLogic;
use Supplychain\Model\Logic\LxJdJrLogic;
use Supplychain\Model\OrderModel;
use Supplychain\Model\PaymentApplyModel;
use Supplychain\Model\ProductModel;
use Supplychain\Model\SupplierBankModel;
use Supplychain\Model\SupplierModel;
use Supplychain\Model\TakeNumberModel;
use Supplychain\Repository\UserRepository;
use Supplychain\Service\DingNotify;


class IndexController extends BaseController
{
    public static $page_size = 10;
    public $userId = null;
    public function _initialize()
    {
        parent::_initialize();
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), ["productlsit"])) {
            // 检查登录
            if (in_array(strtolower(ACTION_NAME), ["setFields"])) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }


        try{
            call_user_func_array([$this,'decorator'],[]);
            $this->customerService = LaravelApp('\Supplychain\Service\CustomerService');
            $this->orderService = LaravelApp('\Supplychain\Service\OrderService');
            $this->userRepository = LaravelApp('\Supplychain\Repository\UserRepository');
            $this->orderRepository = LaravelApp('\Supplychain\Repository\OrderRepository');
        }catch(\Exception $e){
            return $this->apiReturn($e->getCode(), $e->getMessage());
        }

    }

    protected function decorator(){
        $this->userId = cookie('uid');
        if(!$this->userId) throw new \Exception("请登陆后在查看",20001);
        if(!in_array(strtolower(ACTION_NAME),['add_customer_info','user_config','mydetails','get_user_audit','get_user_audit_status','createiccredit','get_user_credit','test','get_origin_goods','userinfo'])){
            //判断是否是报关协议用户 不是的需要录入信息
            $CustomerService = LaravelApp('\Supplychain\Service\CustomerService');
            $hasCustomer = $CustomerService->checkUser($this->userId);
            if(!$hasCustomer){
                throw new \Exception("请先录入基本信息",20002);
            }
        }
    }


    /**
     * 报关首页
     */
    public function index()
    {
        echo '在线报关首页';exit;
    }

    /**
     * 新增用户基本资料
     */
    protected function __add_customer_info($data){
        try{
            DB::connection('SUPPLYCHAIN')->transaction(function() use($data){

                $InsertCustomer =  $this->userRepository->addCustomer($data['company']);
                if(!$InsertCustomer){
                    throw new \Exception('录入信息失败');
                }

                $InsertCompany =  $this->userRepository->addCompany($data['company']);
                if(!$InsertCompany){
                    throw new \Exception('录入公司信息失败');
                }


                //20200331增加开票资料不必填
                if (!empty(array_filter($data['invoice']))){
                    $InsertComInvoice =  $this->userRepository->addComInvoice($data['invoice']);
                    if(!$InsertComInvoice){
                        throw new \Exception('录入开票资料');
                    }
                }

                $InsertContact =  $this->userRepository->addContact($data['contact']);
                if(!$InsertContact){
                    throw new \Exception('录入联系人信息');
                }
                $InsertCustomer->company_id = $InsertCompany->company_id;
                if(!$InsertCustomer->save()) throw new \Exception('录入信息失败');

                //20200331增加开票资料不必填
                if (!empty(array_filter($data['invoice']))){
                    $InsertComInvoice->company_id = $InsertCompany->company_id;
                    if(!$InsertComInvoice->save()) throw new \Exception('录入信息失败');
                }

                $InsertContact->company_id = $InsertCompany->company_id;
                if(!$InsertContact->save()) throw new \Exception('录入信息失败');

            });
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('录入信息失败');
        }
    }


    /**
     * 审核未通过 重新提交资料
     */
    protected function __edit_customer_info($data){
        try{
            DB::connection('SUPPLYCHAIN')->transaction(function() use($data){


                $InsertCompany =  $this->userRepository->edit_company($data['company']);
                if($InsertCompany === false){
                    throw new \Exception('录入公司信息失败');
                }

                if (!empty(array_filter($data['invoice']))){
                $InsertComInvoice =  $this->userRepository->edit_invoice($data['invoice']);
                if($InsertComInvoice  === false){
                    throw new \Exception('录入开票资料');
                }
                }

                $InsertContact =  $this->userRepository->edit_cotact($data['contact']);
                if($InsertContact  === false){
                    throw new \Exception('录入联系人信息');
                }

                $edit_customer_status = $this->userRepository->edit_customer_status();
                if($edit_customer_status  === false){
                    throw new \Exception('修改用户审核状态失败');
                }

            });
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('录入信息失败');
        }
    }

    /**
     * 新增客户信息资料
     */
    public function add_customer_info(){
        $company = I('company',[]);//公司信息
        $invoice = I('invoice',[]);//开票资料
        $contact = I('contact',[]);//联系人信息

        try{

            if($this->userRepository->checkUserPass()){
                throw new SupplyException("您已提交资料并且审核通过!");
            }

            $data =  $this->customerService->customerValidatorDatas($company,$invoice,$contact);



            //判断用户是否已经提交过过资料
            if($this->userRepository->checkUserHasInfo()){
                if($this->userRepository->checkUserEdit()) return $this->apiReturn("0", "正在审核中，请耐心等待");
//                return $this->apiReturn("400", "抱歉，暂不支持修改！");
                $this->__edit_customer_info($data);
            }else{
                if($this->userRepository->checkCompanyExists($data['company'])){
                    throw new SupplyException("公司名已经存在，请联系商务!");
                }
                $this->__add_customer_info($data);
            }

            //20200221 新增邮件和钉钉消息
            $postBody   = ['customer_id' => $this->userId,'customer_name' => $data['company']['company_full_name']];
            post_curl(CRM_DOMAIN.'/api/sendCustomerRegister', $postBody, array('api-key: crm a1b2c3d4e5f6g7h8i9jk'));

            return $this->apiReturn("0", "录入信息成功");

        }catch(SupplyException $e){
            return $this->apiReturn("20003", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20003", "录入信息失败");
        }
    }


    /**
     * 获取用户select选项配置
     */
    public function user_config(){
        $data['industry_involved'] = C("industry_involved");//所属行业
        $data['invoice_type'] = C("invoice_type");//发票类型
        $data['contact_source'] = C("contact_source");//从哪里了解到猎芯供应链的
        $data['payment_freights'] = C("payment_freights");//运费支付
        $data['transports'] = C("transports");//运输方式
        $data['carriers'] = C("carriers");//承运商
        $data['supplier_bank_area'] = C("supplier_bank_area");//供应商银行账号区域
        $data['hongkong_delivery_address'] = C("hongkong_delivery_address");//香港交货方式
        $data['overseas_settlement_type'] = C("overseas_settlement_type");//境外结算方式
        $data['inland_delivery_type'] = C("inland_delivery_type");//批次选择
        $data['outbound_type'] = C("outbound_type");//出库方式
        return $this->apiReturn("0", "",$data);
    }

    /**
     * 个人中心首页
     *
     */
    public function personal(){

    }

    /*
     * 公司资料详情
     */
    public function mydetails(){
        try{
            $list = [];
            $status = $this->userRepository->getUserAuditStatus();
            $company_id = $this->userRepository->getCompanyId($this->userId);
            $list['company'] = $this->userRepository->getCompany($this->userId,$company_id);
            $list['invoice'] = $this->userRepository->getComInvoice($this->userId,$company_id);
            $list['contact'] = $this->userRepository->getContact($this->userId,$company_id);
            $list['head_pic'] = CustomerModel::where('user_id',$this->userId)->value('head_pic');
            $list['company']['customer_status'] = $status;
            $list['contact']['customer_status'] = $status;
            $list['invoice']['customer_status'] = $status;
            $list = collect($list)->toArray();
            $list = $this->customerService->dealUserInfoField($list);
            return $this->apiReturn("0", "",$list);

        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20004", "获取信息失败");
        }

    }


    /**
     * 获取审核状态
     */
    public function get_user_audit(){
        try{
            $list = $this->userRepository->get_user_audit();
            return $this->apiReturn("0", "",$list);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20004", "获取审核状态失败");
        }
    }

    /**
     * 获取用户审核状态值
     */
    public function get_user_audit_status(){
        try{
            $list = $this->userRepository->getUserAuditStatus();
            return $this->apiReturn("0", "",$list);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20004", "获取审核状态失败");
        }
    }

    /*
     * 编辑公司信息
     * 编辑开票资料
     * 编辑联系人
     */
    public function edit_myinfo(){
        $type = I('action_type',null,'trim');
        if(!in_array($type,['edit_company','edit_contact','edit_invoice'])){
            return $this->apiReturn("20005", "编辑类型错误");
        }
        try{
            $bk = call_user_func_array([$this,$type],[]);
            if(!$bk) throw new \Exception('修改失败');
            return $this->apiReturn("0", "修改成功");
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20005", "修改失败");
        }
    }

    /**
     * 编辑公司信息
     */
    protected function edit_company(){
        $companyData = I('company',[]);
        try{
            $data =  $this->customerService->companyValidatorDatas(['company'=>$companyData]);
            $bk = $this->userRepository->edit_company($data['company']);
            if($bk === false) throw new \Exception('编辑信息失败');
            return true;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    /**
     * 编辑联系人
     */
    protected function edit_contact(){
        $contactData = I('contact',[]);
        try{
            $data =  $this->customerService->contactValidatorDatas(['contact'=>$contactData]);
            $bk = $this->userRepository->edit_cotact($data['contact']);
            if(!$bk) throw new \Exception('编辑信息失败');
            return true;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }


    /**
     * 编辑发票
     */
    protected function edit_invoice(){
        $invoiceData = I('invoice',[]);
        try{
            $data =  $this->customerService->invoiceValidatorDatas(['invoice'=>$invoiceData]);
            $bk = $this->userRepository->edit_invoice($data['invoice']);
            if(!$bk) throw new \Exception('编辑信息失败');
            return true;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }


    /*
     * 收货地址列表
     */
    public function shippinglist(){
        try{
            $shipModel = new \Supplychain\Model\UserDeliveryModel;
            $company_id = $this->userRepository->getCompanyId($this->userId);
            if(!$company_id) throw new \Exception('没有更多数据');
            $data = I('get.');
            if (empty($data['address_area'])){
                $data['address_area'] = [1,2,3];
            }else{
                $data['address_area'] = [$data['address_area']];
            }
            $list = $shipModel->where("company_id",$company_id)
                ->select("com_delivery_id","consignee","seat_number","intl_code","mobile","detail_address","is_default","province","city","district",'com_name','com_name_hk','address_area')
                ->where("status",">=",0)
                ->whereIn('address_area',$data['address_area'])
                ->where("address_area",I('address_area',1,'intval'))
                ->orderBy("is_default",'desc')
                ->orderBy("create_time",'desc')
                ->get();
            if(!$list) throw new \Exception('没有更多数据');
            foreach($list as &$item){
                $item->zuo_ji = $item->zuo_ji;
                $item->city_info = $this->userRepository->getRegionInfo($item->province,$item->city,$item->district,$item->address_area);
            }
            $arr = collect($list)->except(['consignee','seat_number'])->toArray();

            if (isset($data['checked_com_delivery_id']) && !empty($data['checked_com_delivery_id'])){
                foreach ($arr as $key=>$value){
                    if ($value['com_delivery_id'] == $data['checked_com_delivery_id'] ){
                        unset($arr[$key]);
                    }
                }
                $firstData = $shipModel->where('com_delivery_id',$data['checked_com_delivery_id'])
                    ->select("com_delivery_id","consignee","seat_number","intl_code","mobile","detail_address","is_default","province","city","district",'com_name','com_name_hk','com_name_hk','address_area')
                    ->first()->toArray();
                $firstData['zuo_ji'] = "";
                $firstData['city_info'] = $this->userRepository->getRegionInfo($firstData['province'],$firstData['city'],$firstData['district'],$item->address_area);
                array_unshift($arr,$firstData);
            }

            if(empty($arr)) throw new \Exception('没有更多数据');
            return $this->apiReturn("0", "",$arr);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20009", "没有更多数据");
        }
    }

    /*
     * 新增 修改
     * 收货地址
     */
    public function shipping(){
        $type = I('action_type',null,'trim');
        if(!in_array($type,['edit_shipping','add_shipping'])){
            return $this->apiReturn("20008", "未知错误");
        }
        try{
            $bk = call_user_func_array([$this,$type],[]);
            if(!$bk) throw new \Exception('操作失败');
            return $this->apiReturn("0", "操作成功",$bk);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20005", $e->getMessage());
        }
    }

    /*
     * 新增收货地址
     */
    protected function add_shipping(){
        try{
            $data = I('post.',[]);
            $data =  $this->customerService->shippingValidatorDatas($data);
            $bk = $this->userRepository->add_shipping($data);
            if(!$bk) throw new \Exception('添加收货地址失败');
            return $bk;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    /*
     * 修改收货地址
     */
    protected function edit_shipping(){
        try{
            $data = I('post.',[]);
            if(!isset($data['com_delivery_id'])) throw new \Exception('没找到对应的收货地址信息');
            $data =  $this->customerService->shippingValidatorDatas($data);
            $bk = $this->userRepository->edit_shipping($data);
            if(!$bk) throw new \Exception('修改收货地址失败');
            return $bk;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }


    /*
     * 设为默认
     * 收货地址
     */
    public function ship_setdefault()
    {
        $com_delivery_id = I('com_delivery_id', 0, 'intval');
        try {
            $bk = $this->userRepository->ship_setdefault($com_delivery_id);
            if($bk === false){
                throw new SupplyException('设为默认失败');
            }
            $this->apiReturn("0", 'ok');
        }catch(SupplyException $e){
            $this->apiReturn("20010", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write('设为默认失败','WARN');
            return false;
        }
    }

    /*
     * 删除
     * 收货地址
     */
    public function ship_delete()
    {
        $com_delivery_id = I('com_delivery_id', 0, 'intval');
        try {
            $bk = $this->userRepository->deleteSupply($com_delivery_id);
            if(!$bk){
                throw new SupplyException('删除失败');
            }
            $this->apiReturn("0", 'ok');
        }catch(SupplyException $e){
            $this->apiReturn("20010", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    /**
     * 获取收货地址详情
     */
    public function get_user_delivery(){
        $data = I("get.",[]);
        try{
            $returnData = $this->userRepository->getUserDelivery($data);
            if(!$returnData) throw new SupplyException('没有找到对应数据');
            return $this->apiReturn("0", "成功",$returnData);
        }catch(SupplyException $e) {
            $this->apiReturn("20012", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20012", "没有找到对应数据");
        }
    }


    /*
     * 公司银行账号列表
     */
    public function banklist(){
        try{
            $model = new \Supplychain\Model\ComBankModel;
            $company_id = $this->userRepository->getCompanyId($this->userId);
            $list =$model->where("company_id",$company_id)
                ->select("company_bank_id","account_name","bank_name","bank_number","status")
                ->where("status",">=",0)
                ->orderBy("create_time","desc")->get();
            if(!$list) throw new \Exception('没有更多数据');
            $data = [];
            foreach($list as &$item){
                $item->status_cn = $item->status_cn;
            }
            $arr = collect($list)->toArray();
            if(empty($arr)) throw new \Exception('没有更多数据');
            return $this->apiReturn("0", "",$arr);
        }catch(SupplyException $e) {
            $this->apiReturn("20010", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20005", "操作失败");
        }
    }

    /*
     * 新增公司银行账号
     */
    public function add_bank(){
        $data = I("post.",[]);
        try{
            $data =  $this->customerService->bankValidator($data);
            $bk = $this->userRepository->add_bank($data);
            if(!$bk) throw new SupplyException('添加失败');
            return $this->apiReturn("0", "操作成功");
        }catch(SupplyException $e) {
            $this->apiReturn("20011", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20011", "操作失败");
        }
    }

    /**
     * @return array
     * 公司银行账号详情获取
     */
    public function getComBankInfo(){
        $data = I("get.",[]);
        try{
            $returnData = $this->userRepository->getComBankInfo($data);
            if(!$returnData) throw new SupplyException('没有找到对应数据');
            return $this->apiReturn("0", "成功",$returnData);
        }catch(SupplyException $e) {
            $this->apiReturn("20012", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20012", "没有找到对应数据");
        }
    }

    /*
     * 编辑公司银行账号
     */
    public function edit_bank(){
        $data = I("post.",[]);
        try{
            if(!isset($data['company_bank_id'])) throw new SupplyException('参数错误');
            $data =  $this->customerService->bankValidator($data);
            $bk = $this->userRepository->edit_bank($data);
            if(!$bk) throw new SupplyException('修改失败');
            return $this->apiReturn("0", "修改成功");
        }catch(SupplyException $e) {
            $this->apiReturn("20012", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20012", "修改失败");
        }
    }

    /**
     * 删除公司银行账户
     */
    public function delete_com_bank(){
        $company_bank_id = I('company_bank_id', 0, 'intval');
        try {
            $bk = $this->userRepository->deleteComBank($company_bank_id);
            if(!$bk){
                throw new SupplyException('删除失败');
            }
            $this->apiReturn("0", 'ok');
        }catch(SupplyException $e){
            $this->apiReturn("20010", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    /**
     * 产品列表
     */
    public function productlsit(){
        try{
            list($data,$count) = $this->userRepository->getProductList(I("get."));
            $data = $this->userRepository->checkProductField($data);
            if(!$count || !$data) throw new SupplyException('没有更多数据');
            $this->apiReturn("0",'ok',["data"=>$data,"count"=>$count,'order_num'=>ProductModel::getOrderStatusNum(cookie('uid'))]);
        }catch(SupplyException $e){
            return $this->apiReturn("20016", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20016", $e->getMessage());
        }
    }

    /*
     * 新增产品
     */
    public function add_product(){
        $postdata = I("post.",[]);
        try{
            foreach ($postdata['goods_type'] as $key=>$val){
                $data['goods_type'] = $val;
                $data['brand'] = $postdata['brand'][$key];
                $data['goods_title'] = $postdata['goods_title'][$key];
                $data['origin'] = $postdata['origin'][$key];
                $data['measurement'] = $postdata['measurement'][$key];
                $data['description'] = $postdata['description'][$key];
                $data['product_specification'] = $postdata['product_specification'][$key];
                $data =  $this->customerService->productValidator($data);
                $bk = $this->userRepository->add_product($data);
                if(!$bk) throw new SupplyException('添加失败或商品已存在');
            }
            //推送物料数据 此处有时间需要改成消息推送
//            $data['user_id'] = cookie('uid');
//            $this->pushGoodsInfo($data);
            return $this->apiReturn("0", "添加成功");
        }catch(SupplyException $e) {
            $this->apiReturn("20013", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20013", "添加失败或商品已存在");
        }
    }

    /**
     * @return array
     */
    protected function pushGoodsInfo($data){
        try{
            $arr['goods_type'] = $data['goods_type'];
            $arr['brand'] = $data['brand'];//品牌
            $arr['goods_title'] = $data['goods_title'];//品名
            $arr['user_id'] = $data['user_id'];//型号
            $arr['goods_id'] = $data['goods_id'];
            (new \Supplychain\Repository\OrderRepository)->addUserGoodsToMq(json_encode($arr));
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    /*
     * 修改产品
     */
    public function edit_product(){
        $data = I("post.",[]);
        try{
            $data =  $this->customerService->productValidator($data);
            $bk = $this->userRepository->edit_product($data);
            if($bk === false) throw new SupplyException('修改失败');
            //推送物料数据 此处有时间需要改成消息推送
//            if($bk){
//                $data['user_id'] = cookie('uid');
//                $this->pushGoodsInfo($data);
//            }
            return $this->apiReturn("0", "修改成功");
        }catch(SupplyException $e) {
            $this->apiReturn("20014", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20014", "修改失败");
        }
    }


    /**
     * 产品详情
     */
    public function show_product(){
        $goods_id = I("goods_id",0,'intval');
        try{
            $info = $this->userRepository->show_product($goods_id);
            if(!$info)throw new SupplyException('没找到对应产品');
            $data = $info->toArray();
            if(!empty($data['unit'])){
                $data['unit'] = isset($data['unit']['name']) ? $data['unit']['name'] : '';
            }else{
                $data['unit'] = '';
            }
            return $this->apiReturn("0", "",$data);
        }catch(SupplyException $e) {
            $this->apiReturn("20016", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20016", "没找到对应产品");
        }
    }

    /**
     * 删除产品
     */
    public function del_user_goods(){
        $goods_id = I("goods_id",0,'intval');
        try{
            $bk = $this->userRepository->del_user_product($goods_id);
            if(!$bk)throw new SupplyException('删除失败');
            return $this->apiReturn("0", "");
        }catch(SupplyException $e) {
            $this->apiReturn("20016", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20016", "删除失败");
        }
    }

    /**
     * 用户 产品提交审核
     */
    public function user_audit(){
        $goods_ids = I("ids",[]);
        try{
            $bk =  $this->customerService->changeGoodsStatus($goods_ids);
            if($bk){
                return $this->apiReturn("0", "");
            }else{
                throw new SupplyException('提交审核失败');
            }
        }catch(SupplyException $e) {
            $this->apiReturn("20017", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20017", "提交审核失败");
        }
    }

    /**
     * 新增产品-获取产地列表
     */
    public function get_origin_goods(){
        try{
            $data = $this->orderRepository->getOriginGoods();
            return $this->apiReturn("0",'', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20017", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20017", "获取产地失败");
        }
    }



    /**
     * 搜索产品信息
     */
    public function search_good_info(){
        try{
            $data = $this->userRepository->searchGoodInfo(I("post."));
            if(!$data) throw new SupplyException('没有更多数据');
            return $this->apiReturn("0",'', array_values($data));
        }catch(SupplyException $e) {
            $this->apiReturn("20017", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20017", "没有更多数据");
        }
    }

    /**
     * 搜索产品扩展信息-产地-计量单位
     * 如果产品存在
     */
    public function get_good_extension_info(){
        try{
            $data['goods_title'] = I('goods_title','','trim');
            $data['goods_type'] = I('goods_type','','trim');
            $data['brand'] = I('brand','','trim');
            $data = $this->userRepository->getGoodExtensionInfo($data);
            if(!$data) throw new SupplyException('没有更多数据');
            return $this->apiReturn("0",'', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20017", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20017", "没有更多数据");
        }
    }


    /**
     *
     * 供应商列表
     */
    public function supplylist(){
        try{
            list($data,$count) = $this->userRepository->getsupplierList(I("get."));
            if(!$count || !$data) throw new SupplyException('没有更多数据');
            $this->apiReturn("0",'ok',["data"=>$data,"count"=>$count]);
        }catch(SupplyException $e){
            return $this->apiReturn("20016", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20016", "没有更多数据");
        }
    }

    /**
     * 新增供应商
     */
    public function addsupply(){
        $data = I('post.');
        try{
            $bk =  $this->customerService->addSupplier($data);
            if($bk){
                return $this->apiReturn("0", "");
            }else{
                throw new SupplyException('新增交易方失败');
            }
        }catch(SupplyException $e) {
            $this->apiReturn("20018", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20018", $e->getMessage());
        }
    }

    /**
     * 新增供应商
     */
    public function editsupply(){
        try{
            $bk =  $this->customerService->editSupplier(I('post.'));
            if($bk){
                return $this->apiReturn("0", "");
            }else{
                throw new SupplyException('编辑交易方失败或没有权限修改');
            }
        }catch(SupplyException $e) {
            $this->apiReturn("20018", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20018", "编辑交易方失败或交易方已存在");
        }
    }

    /**
     *
     * 供应商银行账号列表
     */
    public function supply_bank_list(){
        try{
            list($data,$count) = $this->userRepository->getsupplierBrankList(I("get."));
            if(!$count || !$data) throw new SupplyException('没有更多数据');
            $supplier_name = $this->userRepository->getsupplierName(I('supplier_id',0,'intval'));
            $this->apiReturn("0",'ok',["supplier_name"=>$supplier_name,"data"=>$data,"count"=>$count]);
        }catch(SupplyException $e){
            return $this->apiReturn("20016", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20016", "没有更多数据");
        }
    }

    /**
     * 新增供应商银行账号
     */
    public function add_supply_bank_list(){
        $data = I('post.',[]);
        try{
            $data =  $this->customerService->supplyBankValidator($data);
            $bk =  $this->userRepository->addSupplierBank($data);
            if($bk){
                return $this->apiReturn("0", "");
            }else{
                throw new SupplyException('新增供应商银行账户失败');
            }
        }catch(SupplyException $e) {
            $this->apiReturn("20019", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20019", "新增供应商银行账户失败");
        }

    }

    /**
     * 获取供应商银行账号详情
     */
    public function get_supply_bank_info(){
        $data = I('get.',[]);
        try{
            $data =  $this->userRepository->getSupplyBankInfo($data);
            if($data){
                return $this->apiReturn("0", "",$data);
            }else{
                throw new SupplyException('没有找到对应数据');
            }
        }catch(SupplyException $e) {
            $this->apiReturn("20019", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20019", "没有找到对应数据");
        }
    }

    /**
     * 编辑供应商银行账号
     */
    public function edit_supply_bank_list(){
        $data = I('post.',[]);
        try{
            $data =  $this->customerService->supplyBankValidator($data);
            $bk =  $this->userRepository->editSupplierBank($data);
            if($bk){
                return $this->apiReturn("0", "");
            }else{
                throw new SupplyException('编辑供应商银行账户失败');
            }
        }catch(SupplyException $e) {
            $this->apiReturn("20019", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20019", "编辑供应商银行账户失败");
        }
    }

    /**
     * 获取国家
     *
     */
    public function get_country_list(){
        try{
            $list  = $this->userRepository->getCountryList(I("get."));
            $this->apiReturn("0",'ok',$list);
        }catch(SupplyException $e){
            return $this->apiReturn("20020", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20020", "没有更多数据");
        }
    }


    /**
     * 获取币别
     */
    public function getcurrency(){
        try{
            $this->apiReturn("0",'ok',C(supply_currency));
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20021", "没有更多数据");
        }
    }

    /**
     * 获取计量单位
     */
    public function getunit(){
        try{
            $list  = $this->userRepository->getunit();
            $this->apiReturn("0",'ok',$list);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20022", "没有更多数据");
        }
    }






    /**
     * *************************订单相关*************************
     */


    /*
     * 录入订单产品信息
     */
    public function add_ordergoods(){
        $data = I("post.",[]);
        if (isset($data['hk_unit_price'])){
            HongKongOrderLogic::$isHongKong = 1;
        }
        try{
            $data = $this->orderService->getCreateOrderGoodsFields($data);
            //检测商品是否重复
            $repeatArr = [];
            $errMsg = [];
            $errData = [];
            foreach ($data as $key=>$val){
                $str = $val['brand'] . $val['goods_type'] . $val['goods_title'];
                if (in_array($str,$repeatArr)){
                    $errMsg[] = $key+1;
                    $errData[] = $key;
                }
                $repeatArr[] = $str;
            }

            //是否为空
            if (!empty($errData)){
                return $this->ajaxReturn( ['err_code'=>'20023','err_msg'=>'第'.implode(',',$errMsg).'行有重复数据','data'=>$errData]);
            }
            $bk = $this->orderRepository->batchAddGoods($data);
            if(!$bk) throw new SupplyException('添加订单失败');
            return $this->apiReturn("0", "添加订单成功",['order_id'=>$bk]);
        }catch(SupplyException $e) {
            $this->apiReturn("20023", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20023", $e->getMessage());
        }
    }

    /**
     * 录入订单基础信息
     */
    public function add_basic_order_info(){
        $data = I("post.",[]);
        try{
            $data = $this->orderService->OrderInfoValidator($data);
            $bk = $this->orderRepository->add_basic_order_info($data);
            if(!$bk) throw new SupplyException('录入订单基础信息');
            return $this->apiReturn("0", "录入订单基础信息成功",['order_id'=>$bk]);
        }catch(SupplyException $e) {
            $this->apiReturn("20026", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20026", $e->getMessage());
        }
    }

    /*
     * 复制订单
     * */
    public function copy_order(){
        $data = I("post.",[]);
        try{
            if(!($is_okay = $this->orderRepository->copyOrder($data))){
                throw new SupplyException('复制订单基础信息失败');
            }
            return $this->apiReturn("0", "复制订单基础信息成功",['order_id'=>$is_okay]);
        }catch(SupplyException $e) {
            $this->apiReturn("20026", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20026", $e->getMessage());
        }
    }


    /**
     * 录入订单香港交货方式
     */
    public function add_hongkong_delivery(){
        $data = I("post.",[]);
        try{
            CommonLogic::checkField('add_hongkong_delivery',$data);
            $bk = $this->orderRepository->add_hongkong_delivery($data);
            if(!$bk) throw new SupplyException('录入订单香港交货方式失败');
            return $this->apiReturn("0", "成功",['order_id'=>$bk]);
        }catch(SupplyException $e) {
            $this->apiReturn("20027", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20027", $e->getMessage());
        }
    }


    /*
     * 录入国内物流信息
     */
    public function add_inland_delivery(){
        $data = I("post.",[]);
        try{
            $bk = $this->orderRepository->add_inland_delivery($data);
            if(!$bk) throw new SupplyException('请选择国内收货地址');
            return $this->apiReturn("0", "成功",['order_id'=>$bk]);
        }catch(SupplyException $e) {
            $this->apiReturn("20028", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20028", $e->getMessage());
        }
    }


    /**
     * 下单
     */
    public function add_order(){
        $data = I("post.",[]);
        try{
            $bk = $this->orderRepository->add_order($data);
            if(!$bk) throw new SupplyException('添加失败');
            return $this->apiReturn("0", "添加成功");
        }catch(SupplyException $e) {
            $this->apiReturn("20013", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20013", "添加失败");
        }
    }

    /**
     * 录入其他信息
     */
    public function add_other_order_info(){
        $data = I("post.",[]);
        try{
            $bk = $this->orderRepository->add_other_order_info($data);
            if(!$bk) throw new SupplyException('录入订单其它信息审失败');
            return $this->apiReturn("0", "成功",['order_id'=>$bk]);
        }catch(SupplyException $e) {
            $this->apiReturn("20029", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20029", "录入订单其它信息审失败");
        }
    }


    /**
     * 下单获取用户供应商列表
     */
    public function getuser_suppliers(){
        $supplier_name = I("supplier_name",'','trim');
        try{
            $bk = $this->orderRepository->getuser_suppliers($supplier_name);
            if(!$bk) throw new SupplyException('没有更多数据');
            return $this->apiReturn("0", "成功",$bk);
        }catch(SupplyException $e) {
            $this->apiReturn("20030", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20030", "没有更多数据");
        }
    }



    /**
     * 订单商品列表
     */
    public function get_order_goods_list(){
        try{
            $data = $this->orderRepository->getOrderGoodsList();
            $this->apiReturn("0",'ok',$data);
        }catch(SupplyException $e){
            return $this->apiReturn("20024", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20024", "没有更多数据");
        }
    }


    /**
     * 获取订单基础信息
     */
    public function get_order_basicinfo(){
        try{
            $data = $this->orderRepository->getOrderBasicInfo(I("order_id",0,'intval'));
            $this->apiReturn("0",'ok',$data);
        }catch(SupplyException $e){
            return $this->apiReturn("20024", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20024", "没有更多数据");
        }
    }


    /**
     * 获取订单 香港发货方式
     */
    public function get_order_hongkong(){
        try{
            $data = $this->orderRepository->get_order_hongkong(I("order_id",0,'intval'));
            $this->apiReturn("0",'ok',$data);
        }catch(SupplyException $e){
            return $this->apiReturn("20024", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20024", "没有更多数据");
        }
    }

    /**
     * 获取订单 国内物流信息
     */
    public function get_inland_delivery(){
        try{
            $data = $this->orderRepository->get_inland_delivery(I("order_id",0,'intval'));
            $this->apiReturn("0",'ok',$data);
        }catch(SupplyException $e){
            return $this->apiReturn("20024", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20024", "没有更多数据");
        }
    }



    /*
     * 已录入订单产品信息详情
     * 订单某个商品的详情
     */
    public function show_ordergoods(){
        $order_goods_id = I("order_goods_id",0,'intval');
        try{
            $bk = $this->orderRepository->show_order_goods($order_goods_id);
            if(!$bk) throw new SupplyException('没找到相关数据');
            return $this->apiReturn("0", "ok",$bk);
        }catch(SupplyException $e) {
            $this->apiReturn("20024", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20024", "没找到相关数据");
        }
    }


    /**
     * 订单商品删除
     * 删除订单某个商品数据
     */
    public function del_ordergoods(){
        $order_goods_id = I("order_goods_id",0,'intval');
        $order_id = I("order_id",0,'intval');
        try{
            $bk = $this->orderRepository->del_order_goods($order_id,$order_goods_id);
            if(!$bk) throw new SupplyException('删除失败');
            return $this->apiReturn("0", "ok");
        }catch(SupplyException $e) {
            $this->apiReturn("20025", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20025", "删除失败");
        }
    }


    /**
     * 下单 提交订单操作
     */
    public function adding_order(){
        $order_id = I("order_id",0,'intval');
        try{
            if(!$order_id) throw new SupplyException('提交订单失败');
            $bk = $this->orderRepository->goto_adding_order($order_id);
            if(!$bk) throw new SupplyException('提交订单失败');
            return $this->apiReturn("0", "提交订单成功");
        }catch(SupplyException $e) {
            $this->apiReturn("20030", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20030", "提交订单失败");
        }
    }


    /**
     * 订单列表
     */
    public function order_list(){
        try{
            list($data,$count) = $this->orderRepository->getUserOrderkList(I("get."));
            if(!$count || !$data) throw new SupplyException('没有更多数据');
            $this->apiReturn("0",'ok',["data"=>$data,"count"=>$count]);
        }catch(SupplyException $e){
            return $this->apiReturn("20031", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20031", "没有更多数据");
        }
    }

    /**
     * 下单 提交订单操作
     */
    public function cancel_order(){
        $order_id = I("order_id",0,'intval');
        try{
            if(!$order_id) throw new SupplyException('操作失败');
            $bk = $this->orderRepository->cancelOrder($order_id);
            if(!$bk) throw new SupplyException('操作失败');
            return $this->apiReturn("0", "成功");
        }catch(SupplyException $e) {
            $this->apiReturn("20032", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20032", "操作失败");
        }
    }


    /**addOneOrderGoods
     * 修改订单的时候 或者 回到订单下单第一步 可以新增 修改 删除单个商品
     * 新增单个订单商品
     */
    public function add_one_order_goods(){
        try{
            $data= I("post.",[]);
            $data =  $this->orderRepository->getOrderGoodsParamsData($data);
            if(!isset($data['order_id'])) throw new SupplyException('操作失败');
            $bk = $this->orderRepository->addOneOrderGoods($data);
            if(!$bk) throw new SupplyException('操作失败');
            return $this->apiReturn("0", "成功",['order_id'=>$bk->order_id,"order_goods_id"=>$bk->order_goods_id]);
        }catch(SupplyException $e) {
            $this->apiReturn("20032", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20032", "操作失败");
        }
    }

    /**
     * 修改订单的时候 或者 回到订单下单第一步 可以新增 修改 删除单个商品
     * 编辑单个订单商品
     */
    public function edit_one_order_goods(){
        try{
            $data= I("post.",[]);
            $data =  $this->orderRepository->getOrderGoodsParamsData($data);
            if(!isset($data['order_id'])) throw new SupplyException('操作失败');
            if(!isset($data['order_goods_id'])) throw new SupplyException('操作失败');
            $bk = $this->orderRepository->editOneOrderGoods($data);
            if(!$bk) throw new SupplyException('操作失败');
            return $this->apiReturn("0", "成功",['order_id'=>$bk->order_id,"order_goods_id"=>$bk->order_goods_id]);
        }catch(SupplyException $e) {
            $this->apiReturn("20032", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20032", "操作失败");
        }
    }

    // 获取应付款报表
    public function get_pay_cost_list()
    {
        try{
            list($data,$count) = $this->orderRepository->payCostList(I("get."));

            if(!$count || !$data) throw new SupplyException('没有更多数据');

            return $this->apiReturn(0, '获取成功', ["data"=>$data, "count"=>$count]);
        }catch(SupplyException $e) {
            $this->apiReturn("20033", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20033", "获取应付款报表失败");
        }
    }

    // 新增水单
    public function add_water_single()
    {
        try{
            $params = I('post.');
            $data = $this->orderService->WaterSingleValidator($params); // 校验字段

            $this->orderRepository->addWaterSingle($data); // 新增

            return $this->apiReturn(0, '新增水单成功');
        }catch(SupplyException $e) {
            $this->apiReturn("20034", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20034", "新增水单失败");
        }
    }

    // 编辑水单
    public function edit_water_single()
    {
        try{
            $params = I('post.');
            $data = $this->orderService->WaterSingleValidator($params); // 校验字段

            $this->orderRepository->editWaterSingle($data); // 编辑

            return $this->apiReturn(0, '编辑水单成功');
        }catch(SupplyException $e) {
            $this->apiReturn("20035", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20035", "编辑水单失败");
        }
    }

    // 获取水单详情
    public function get_water_single()
    {
        try{          
            $data = $this->orderRepository->getWaterSingle(I('get.'));

            return $this->apiReturn(0, '获取水单成功', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20036", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20036", "获取水单失败");
        }
    }

    // 获取水单列表
    public function get_water_list()
    {
        try{
            list($data, $count) = $this->orderRepository->getWaterList(I("get."));

            if(!$count || !$data) throw new SupplyException('没有更多数据');

            return $this->apiReturn(0, '获取成功', ["data"=>$data, "count"=>$count]);
        }catch(SupplyException $e) {
            $this->apiReturn("20037", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20037", "获取应付款报表失败");
        }
    }

    // 删除水单
    public function del_water_single()
    {
        try{          
            $data = $this->orderRepository->delWaterSingle(I('get.'));

            return $this->apiReturn(0, '删除水单成功');
        }catch(SupplyException $e) {
            $this->apiReturn("20038", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20038", "删除水单失败");
        }
    }

    // 获取应付款单详情 （通知单）
    public function get_pay_cost()
    {
        try{          
            $data = $this->orderRepository->getPayCostById(I('get.'));

            return $this->apiReturn(0, '获取应付款单成功', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20039", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20039", "获取应付款单失败");
        }
    }

    /**
     * 获取订单总价格总数量
     */
    public function get_order_count(){
        try{
            $order_id = I("order_id",0,'intval');
            $bk = $this->orderRepository->getOrderCountInfo($order_id);
            if(!$bk) throw new SupplyException('没有更多数据');
            return $this->apiReturn("0", "成功",$bk);
        }catch(SupplyException $e) {
            $this->apiReturn("20032", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20032", "没有更多数据");
        }
    }

    /**
     * 获取订单状态
     */
    public function is_edit_order(){
        try{
            $order_id = I("order_id",0,'intval');
            $bk = $this->orderRepository->isEditOrder($order_id);
            return $this->apiReturn("0", "",$bk);
        }catch(SupplyException $e) {
            return $this->apiReturn("0", "",0);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("0", "",0);
        }
    }

    // 获取供应链首页信息(我的资产、订单信息)
    public function get_home_info()
    {
        try{
            $user_id = cookie('uid');
            $data = $this->userRepository->getHomeInfo($user_id);
            return $this->apiReturn(0, "成功", $data);
        }catch(SupplyException $e) {
            return $this->apiReturn("20040", "获取供应链首页信息失败");
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20040", "获取供应链首页信息失败");
        }
    }

    // 获取收款单列表
    public function get_receipt_list()
    {
        try{
            list($data, $count) = $this->userRepository->getReceiptList(I("get."));

            if(!$count || !$data) throw new SupplyException('没有更多数据');

            return $this->apiReturn(0, '获取成功', ["data"=>$data, "count"=>$count]);
        }catch(SupplyException $e) {
            $this->apiReturn("20041", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20041", "获取付款记录失败");
        }
    }

    // 获取改单记录列表
    public function get_order_edit_list()
    {
        try{
            list($data, $count) = $this->orderRepository->getOrderEditList(I("get."));

            if(!$count || !$data) throw new SupplyException('没有更多数据');

            return $this->apiReturn(0, '获取成功', ["data"=>$data, "count"=>$count]);
        }catch(SupplyException $e) {
            $this->apiReturn("20042", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20042", "获取改单记录失败");
        }
    }

    // 获取改单详情
    public function get_order_edit_details()
    {
        try{
            $data = $this->orderRepository->getOrderEditDetails(I('get.'));

            return $this->apiReturn(0, '获取成功', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20043", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20043", "获取改单详情失败");
        }
    }

    // 获取应付明细列表
    public function get_verification_list()
    {
        try{
            list($data, $count) = $this->orderRepository->getVerificationList(I("get."));

            if(!$count || !$data) throw new SupplyException('没有更多数据');

            return $this->apiReturn(0, '获取成功', ["data"=>$data, "count"=>$count]);
        }catch(SupplyException $e) {
            $this->apiReturn("20044", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20044", "获取改单记录失败");
        }
    }

    // 订单跟踪 列表
    public function get_order_tracking()
    {
        try{
            $data = $this->orderRepository->getOrderTracking(I("get."));

            if(!$data) throw new SupplyException('没有更多数据');

            return $this->apiReturn(0, '获取成功', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "获取订单跟踪记录失败");
        }
    }

    // 订单跟踪 详情
    public function get_order_tracking_page()
    {
        try{
            $data = $this->orderRepository->getOrderTrackingPage(I("get."));
            if(!$data || count($data) <= 0) throw new SupplyException('没有更多数据');

            return $this->apiReturn(0, '获取成功', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "获取订单跟踪记录失败");
        }
    }


    //获取供应商对应的银行账号列表
    public function get_supply_banks(){
        try{
            $data = $this->userRepository->getSupplyBankList(I("supply_id"));

            if(!$data) throw new SupplyException('没有更多数据');

            return $this->apiReturn(0, '获取成功', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", '没有更多数据');
        }
    }


    //获取供应商对应的银行信息详情
    public function get_supply_bankinfo(){
        try{
            $data = $this->userRepository->getOrderSupplyBankInfo(I("supply_id"));

            if(!$data) throw new SupplyException('没有更多数据');

            return $this->apiReturn(0, '获取成功', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "没有更多数据");
        }
    }



    //下单 批量导入商品
    public function upload_goods(){

        try{
            if(!IS_POST) throw new SupplyException('请上传正确的文件');
            $arr = [];
            if(IS_POST && isset($_FILES["file"]) && ($_FILES["file"]["error"] == 0)){
                $file = $_FILES['file']['tmp_name'];
                $arr = s_import_excel($file);
            }
            if(empty($arr)) throw new SupplyException('请上传正确的文件');
            $data = $this->orderRepository->getExportGoods($arr);
            return $this->apiReturn(0, '上传成功', array_merge($data));
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "导入失败");
        }
    }


    /**
     * 获取商品归类信息
     */
    public function search_goods_class(){
        try{
            $data = $this->userRepository->searchGoodsClass(I("post.",[]));
            if(empty($data)) throw new SupplyException('没有更多数据');
            return $this->apiReturn(0, 'ok', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "没有更多数据");
        }
    }


    /**
     * 新增消息定制
     */
    public function add_user_msg(){
        try{
            $bk = $this->userRepository->addUserMessage(I("post.",[]));
            return $this->apiReturn(0, 'ok', []);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "操作失败");
        }
    }

    /**
     * 获取消息定制配置
     */
    public function getuser_msgconfig(){
        try{
            $data = $this->userRepository->getuserMsgconfig();
            return $this->apiReturn(0, 'ok', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "没有更多数据");
        }
    }

    /**
     * 删除定制的消息
     */
    public function del_msginfo(){
        try{
            $bk = $this->userRepository->delMsginfo(I("get."));
            return $this->apiReturn(0, 'ok');
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20045", "操作失败");
        }
    }


    /**
     * 获取消息定制详情
     */
    public function getuser_msginfo(){
        try{
            $data = $this->userRepository->getuserMsgInfo(I("get."));
            return $this->apiReturn(0, 'ok', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "没有更多数据");
        }
    }


    //获取用户消息定制列表
    public function getuser_msglist(){
        try{
            $data = $this->userRepository->getuserMsglist();

            if(!$data) throw new SupplyException('没有更多数据');

            return $this->apiReturn(0, '获取成功', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "没有更多数据");
        }
    }


    /**
     * 获取订单的总金额
     */
    public function get_order_price(){
        try{
            $data = $this->orderRepository->getOrderPrice(I("order_id"));
            return $this->apiReturn(0, '获取成功', $data);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "没有更多数据");
        }
    }


    /**
     * 审核中订单 改单 生成改单记录表
     * 把订单相关信息克隆一份 包括订单主表和所有订单相关的附表
     * 因为审核中的订单可以多次审核和编辑 而且要支持回滚到某一次编辑的状态的信息
     */
    public function changeOrder(){
        try{
            $order_id = $this->orderRepository->addChangeOrderInfoToMq(I("order_id"));
            return $this->apiReturn(0, '', ["new_order_id"=>$order_id]);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn("20045", "获取数据失败");
        }
    }


    /**
     * 获取erp订单号
     */
    public function getErpOrderSn(){
        try{
            $post = I('get.');
            if (isset($post['type']) && $post['type'] == 'A'){
                HongKongOrderLogic::$isHongKong = 1;
            }
            $erp_order_sn = $this->orderRepository->getErpOrderSn();
            if(!$erp_order_sn) throw new SupplyException("获取入仓单号失败");
            (new TakeNumberModel())->insertInfo(['user_id'=>$this->userId,'erp_order_sn'=>$erp_order_sn]);
            return $this->apiReturn(0, '', ["erp_order_sn"=>$erp_order_sn]);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20045", $e->getMessage());
        }
    }

    /**
     * 获取erp订单号的列表
     */
    public function getErpOrderSnList(){
        try{
            $post = I('get.');
            HongKongOrderLogic::$isHongKong = $post['is_hk_order'];
            if (HongKongOrderLogic::$isHongKong){
                $post['type'] ='A';
            }
            $returnData = TakeNumberModel::getList(array_merge($post,['user_id'=>$this->userId]));
            return $this->apiReturn(0, '',$returnData);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20045",$e->getMessage());
        }
    }

    /*提交ic信用贷企业信息*/
    public function createIcCredit()
    {
        try{
            $post = I('post.');
            $id = $this->userRepository->createCreditApply($post);
            if(!$id) throw new SupplyException("添加失败");
            return $this->apiReturn(0, '', ["id"=>$id]);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20045",$e->getMessage());
        }
    }

    //获取ic信用贷信息
    public function get_user_credit()
    {
        try{
            $creditData = $this->userRepository->getUserCredit();
            if(!$creditData) throw new SupplyException("暂无数据");
            return $this->apiReturn(0, '', $creditData);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20045",$e->getMessage());
        }
    }

    //获取本地的汇率
    public function getPayLocalGood()
    {
        try{
            $res = (new CommonLogic())->getPayLocalGood();
            if(!$res) throw new SupplyException("当天数据未更新");
            return $this->apiReturn(0, '', $res);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20045",$e->getMessage());
        }
    }

    //重新获取汇率
    public function delErpExchangeRate()
    {
        $Redis = new \Redis();
        $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $Redis->auth(C('REDIS_PASSWORD'));
        $Redis->del('ErpExchangeRate');
    }


    //获取本地付汇申请
    public function getPaymentApplyDetail()
    {
        try{
            $get = I('get.');
            if (empty($returnData = PaymentApplyModel::where('user_id',$this->userId)->whereIn('status',[0,1])->where('id',$get['id'])->first() )){
                throw new SupplyException("暂无数据");
            }
            $returnData = $returnData->toArray();
            $returnData['status_cn'] = array_get(PaymentApplyModel::$status,$returnData['status']);
            $returnData['create_time_cn'] = date('Y-m-d H:i:s',$returnData['create_time']);
            $settlementTypeCn = [3=>'现金支票',1=>'电汇',4=>'京东金融'];
            $returnData['settlement_type_cn'] = array_get($settlementTypeCn,$returnData['settlement_type'],'');
            if (!empty($id = SupplierBankModel::where('erp_entry_id',$returnData['bank_info'])->first())){
                $bankInfo = $id;
                if (!empty($recipientCountry = CountryModel::where('code',$bankInfo->recipient_country)->value('name'))){
                    $bankInfo->recipient_country = $recipientCountry;
                }
            }elseif(!empty($id = SupplierModel::where('supplier_name',$returnData['bank_info'])->first()) ){
                $bankInfo = $id;
            }else{
                throw  new Exception('供应商ID不存在');
            }
            $returnData['bank_info'] = $bankInfo;
            if (empty($returnData['complete_time'])){
                $returnData['complete_time_cn'] = "";
            }else{
                $returnData['complete_time_cn'] = date('Y-m-d H:i:s',$returnData['complete_time']);
            }
            $supplyInfo = SupplierModel::where('supplier_name',$returnData['supplier'])->select('supplier_id','user_id')->first();
            if (empty($supplyInfo)){
                $returnData['erpClientCode'] = "";
                $returnData['supplierId'] = "";
            }else{
                $returnData['erpClientCode'] = CustomerModel::where('user_id',$supplyInfo->user_id)->value('erp_client_code');
                $returnData['supplierId'] = $supplyInfo->supplier_id;
            }

            $returnData['detail'] = [];
            if (!empty($returnData['number'])){
                $returnData['detail'] = PaymentApplyModel::where('number',$returnData['number'])->whereIn('status',[0,1])->get();
                if (!empty($returnData['detail'])){
                    foreach ($returnData['detail'] as $key=>$value){
                        $returnData['detail'][$key]->create_time_cn = date('Y-m-d H:i:s',$value->create_time);
                    }
                }
            }

            return $this->apiReturn(0, '',$returnData);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20045",$e->getMessage());
        }
    }


    //获取本地付汇申请
    public function getPaymentApply()
    {
        try{
            $post = I('get.');
            if (empty($returnData = PaymentApplyModel::getList(array_merge($post,['user_id'=>$this->userId])))){
                throw new SupplyException("暂无数据");
            }
            return $this->apiReturn(0, '',$returnData);
        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20045",$e->getMessage());
        }
    }





    //获取付汇申请列表
    public function getPayLoaclGoods()
    {
        try{
            $post = I('get.');
            if (empty($companyId = CustomerModel::where('user_id',$this->userId)->value('company_id'))){
                throw new Exception('没有绑定的公司');
            }
            if (empty($companyName = CompanyModel::where('company_id',$companyId)->value('company_full_name'))){
                throw new Exception('公司名称不完整');
            }
            $post['settleMentUnit'] = $companyName;

            if (!isset($post['entrustNumber']) || empty($post['entrustNumber'])){
                $post['entrustNumber'] = '';
            }
            if (!isset($post['dateTo']) || empty($post['dateTo'])){
                $post['dateTo'] = date('Y-m-d',time());
            }
            if (!isset($post['dateFrom']) || empty($post['dateFrom'])){
                $post['dateFrom'] = date('Y-m-d',0);
            }

            //获取汇率数组
            $Redis = new \Redis();
            $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
            $Redis->auth(C('REDIS_PASSWORD'));
            $exchangeRate = $Redis->get('ErpExchangeRate');
            $exchangeRate = json_decode($exchangeRate,true);
            $rateArr = [];
            foreach($exchangeRate as $value){
                foreach ($value as $k=>$v){
                    $rateArr[$k] = $v;
                }
            }

            $creditData = (new \Supplychain\Controller\ErpPushController)->getPayLoaclGoods($post);
            if(!$creditData) throw new SupplyException("暂无数据");
            foreach ($creditData as $key=>$val){
                $supplierId = OrderModel::where('erp_order_sn',$val['entrustBillNumber'])->value('supplier_id');
                $supplyInfo = SupplierModel::where('supplier_id',$supplierId)->select('supplier_id','user_id')->first();
                if (empty($supplyInfo)){
                    $creditData[$key]['erpClientCode'] = "";
                    $creditData[$key]['supplierId'] = "";
                }else{
                    $creditData[$key]['erpClientCode'] = CustomerModel::where('user_id',$supplyInfo->user_id)->value('erp_client_code');
                    $creditData[$key]['supplierId'] = $supplyInfo->supplier_id;
                }
                    $creditData[$key]['exchangeRate'] = isset($rateArr[$creditData[$key]['settlementCurrency']])?$rateArr[$creditData[$key]['settlementCurrency']]:1;
            }

            if (isset($post['export']) && ($post['export'] == 1)){
                ExportLogic::exportExcelNormal('getForPaymentList',$creditData,'待付汇列表');
                die;
            }else{
                return $this->apiReturn(0,'', $creditData);
            }

        }catch(SupplyException $e) {
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20045",$e->getMessage());
        }
    }


    //新增付汇申请单接口
    public function createPayLocalGood()
    {
        try{
            $post = I('post.');

            $post['user_id'] = $this->userId;
            if (!isset($post['bankInfo']) || empty($post['bankInfo'])){
                throw new SupplyException("银行数据不能为空");
            }

            $orderInfo = OrderModel::where('erp_order_sn',$post['details'][0]['entrustBillNumber'])->first();

            if (empty($orderInfo)){
                throw new SupplyException("该订单不存在");
            }

            if (intval($post['settlement_type']) === 1){
                $post['bankInfo'] = SupplierBankModel::where('supplier_bank_id',$post['bankInfo'])->where('status',1)->value('erp_entry_id');
                if (empty($post['bankInfo'])){
                    throw new SupplyException("银行数据未审核");
                }
            }else{
                if (empty($supplierName = SupplierModel::where('supplier_id',$post['supplier_id'])->value('supplier_name'))){
                    throw new SupplyException("该订单供应商名称不存在");
                }
                $post['bankInfo'] = $supplierName;
            }


            $post['attachment'] = $post['fileUrl'];
            $post['order_id'] = $orderInfo->order_id;
            $returnData = PaymentApplyModel::insertPayMentApply($post);
            $returnData['total_amount'] = $post['total_amount'];

            return $this->apiReturn(0, '付汇创建成功', $returnData);
        }catch(SupplyException $e) {
            DingNotify::createPaymentApplyFailNotify(UserRepository::getCustomerName(),$post['details'][0]['entrustBillNumber'],$e->getMessage());
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20045",$e->getMessage());
        }
    }

    //修改付汇申请单接口
    public function updatePayLocalGood()
    {
        try{
            $post = I('post.');
            $post['user_id'] = $this->userId;

            //需要的数据 ['number'=>'银行信息','details'=>[['poolNumber'=>'test','verityAmt'=>1]]] ,传给供应链
            $post['bankInfo'] = SupplierBankModel::where('supplier_bank_id',$post['bankInfo'])->value('erp_entry_id');
            $createDataSource['bankInfo'] = $post['bankInfo'];
            $createDataSource['attachment'] = $post['fileUrl'];


            $supplierId = SupplierModel::where('supplier_id',$post['supplier_id'])->value('erp_supplier_id');
            if(!$supplierId) throw new \Exception("委托方id为空");
            $createDataSource['supplierID'] = $supplierId;

            foreach ($post['details'] as $value){
                $createDataSource['details'][] = ['poolNumber'=>$value['pool_number'],'verityAmt'=>$value['verifi_amount']];
            }

            $creditData = (new \Supplychain\Controller\ErpPushController)->updatePayLocalGood(json_encode($createDataSource));
            if(!$creditData) throw new SupplyException("暂无数据");

            //本地更新
            PaymentApplyModel::updateData($post);
            PaymentApplyModel::where('number',$post['number'])->update(['file_url'=>$post['fileUrl']]);
            return $this->apiReturn(0, '更新成功', 1);
        }catch(SupplyException $e) {
            DingNotify::updatePaymentApplyFailNotify(UserRepository::getCustomerName(),$post['number'],$e->getMessage());
            $this->apiReturn("20045", $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn("20045",$e->getMessage());
        }
    }


    public function test(){
    }


}
