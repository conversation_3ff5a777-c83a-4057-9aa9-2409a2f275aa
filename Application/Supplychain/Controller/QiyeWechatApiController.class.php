<?php
namespace Supplychain\Controller;



use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\QiyeWechatLogic\BaseLogic;
use Supplychain\Model\Logic\WechatSignforLogic;
use Think\Log;
use Illuminate\Container\Container;

class QiyeWechatApiController extends BaseController
{

    //企业微信接口
    public $client =  null;
    public $container = null;
    public static $DB;

    public function _initialize()
    {
        $this->setHeaders();
        $container = new Container();
        //单例绑定
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
            return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');
    }



    /**
     * 跨域头部设置
     */
    private function setHeaders()
    {
        $origin = isset($_SERVER['HTTP_ORIGIN'])? $_SERVER['HTTP_ORIGIN'] : '';
        $origin_arr = explode('//', $origin);
        $allow_origin = C('ALLOW_ORIGIN');
        if(in_array($origin_arr['1'], $allow_origin)){
            header('Access-Control-Allow-Origin:'.$origin);
            header('Access-Control-Allow-Credentials:true');
            header('Access-Control-Allow-Methods:POST');
//            header('Access-Control-Allow-Headers:x-requested-with,content-type');
            header('Access-Control-Allow-Headers:x-requested-with,content-type,hunt_types');
        }
        header("Content-type: text/html; charset=utf-8");
    }



    //发送群消息
    public function sendGroupMsg()
    {
        try{

            $data = I('post.');
            CommonLogic::logsw(\GuzzleHttp\json_encode($data),__FUNCTION__,Log::NOTICE);

            $returnData = (new BaseLogic())->sendGroupMsg($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }



    //统一错误发送信息接口
    public function sendCommonErrorNotify()
    {
        try{

            $data = I('post.');
            CommonLogic::logsw(\GuzzleHttp\json_encode($data),__FUNCTION__,Log::NOTICE);

            $returnData = (new BaseLogic())->sendCommonErrorNotify($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }


    //接受企业微信的推送，然后根据类别进行记录
    public function acceptQiyeWechatNotify()
    {
        try{
            $inputData = file_get_contents('php://input');
            CommonLogic::logsw($inputData,__FUNCTION__,Log::NOTICE.'>php://input');

            $returnData = (new BaseLogic())->acceptQiyeWechatNotify();
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }

        //如果有传来的检测，就返回
        if (isset($_GET['echostr'])){
            echo $_GET['echostr'];
        }
    }



}