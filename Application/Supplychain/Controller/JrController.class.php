<?php
/**
 * Created by 2021/8/5.
 * User: Joneq
 * Info: 2021/8/5
 * Time: 下午7:51
 */

namespace Supplychain\Controller;
use GuzzleHttp\Psr7\BufferStream;
use GuzzleHttp\Psr7\Response;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\JdparamLogic;
use Supplychain\Model\Logic\JdpayLogic;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\Logic\JdpayRequest\HttpServletRequest;
use Think\Controller;
use Think\Log;

class JrController extends Controller
{



    public function _initialize()
    {

    }


    //京东的联合登录
    public function unionJdLogin()
    {

        $systemInfo = [
            'sysId' => '123',
            'token' => '456'
        ];

        $param = [
            "processId" => "262",//配置 id，申请的
            "openSys" => "A20200702110",//平台 id，申请的
            "openId" => "ltest009",//平台登录账号 id，人的维度，自己的
            "openUser" => "ltest009",//平台用户 id，企业维度，自己的
            "successUrl" => "",//成功后回调地址
            "clientIp" => $_SERVER['REMOTE_ADDR'],//客户端 ip
            "clientAgent" => $_SERVER['HTTP_USER_AGENT'],//浏览器代理
            "failUrl" => "",//失败后返回地址
            "openApiParam" => '{"backUrl":"http://loan.jd.com/cgrz/portal?productCode=18001"}',//其他参数 json 串
        ];

        import('Jdpay.aop.client.DefaultHapiJddClient');

        $client = new \DefaultHapiJddClient();
//        $client->appIdType = C('JDPAY.appId_type');
//        $client->openPublicKey = C('JDPAY.public_key');
//        $client->md5Salt = C('JDPAY.md5_salt');
//        $client->appId = C('JDPAY.appid');
//        $client->appPrivateKey = C('JDPAY.private_key');
//        $client->signType = C('JDPAY.sign_type');
//        $client->encryptType = C('JDPAY.encrypt_type');
        $client->appIdType = "0";
        $client->openPublicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHmBFArlz4wQ0Qz3iFmPG2YHWYJt8dbgJybd6rMykcB/D+HBDZ+S+EpB0zOTst/Y4k8niZe1PvqzjZ2+Npq2f9jTCUJQ4nnTXk6lXd5utFNvA+y6uTfQ1YdbiZAD+OkvYdgL5Wl1LQ6MuWCF4ZI/fm8zDL9zVtCvfYQHm3MnZJNwIDAQAB';
        $client->md5Salt = '74bf4f4d92ea566b0820fffe69a04d20';
        $client->appId = 'A20200702110';
        $client->appPrivateKey = 'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL6eI/e8amibnrMH0ap3/kdifYjVDZxpheEs0Y8czDavbavW1VmDD17MgLcJKs8Sqp3H3hbOndJOKTsHzCMy16wTD23G+2F0uYPKbKpBR4A8IlX76gu7M4fZWY7ojAElXChnIjCHZ6rAgTxtYauC/ddp0lgqKJx6MT7XnGL/Ja9DAgMBAAECgYEAtHAUEg1MGOu+xTAQm7JshHxJ4r3y3W3SKn33fOZAtT9IrJJ3cP0sDou9CAZofI3p7eNlaud447vTMarG5UbaeciPFjWSyaH+b3C/DDoOoxvbOd0IJs35oYwFxQ6sFZcG72zQj7wsyM3D8II5djfkKsBIjRwjkosqkpkv2ZRYGpkCQQDr3/aLOLWXFDqvIFFRupYpumQR+wmuMYxvR2Obgks/yPXJJjqSGjCRb/XI50T14uJEOUGhYcI57zdoUFbp+H6VAkEAzuGpBZuDc3obg/qiGGM15lMbYXFyB2TQvi+Hg/uNGuzcsdl+TzXPvewQxxeHwIqZa2wk2LSikoENDVBeYdl4dwJARaHJM5JbMS18oYRl3T265LisoA4+7licP6GQizDsq/jUbjxF4CmxGs41fcigOAJxj3hjopOsddPjxHyrG8kK/QJATH/xaltpLkhW6GTDj9UP102f8FZs3gMPlWQp7koUkYJI0ZMlO9EgMpCaW6R91FrsBGcG8QNKonYKB4RSgkhSXQJAcdYWicTcJFCYOFxrRz4Ag7L6bqT57hiDf9cLLox/NGQ/ZIlOt7CTmv1/sy4yVfF1pKh/9GJM3fb0GGrAAOE8Wg==';
        $client->signType = 'MD5_RSA';
        $client->encryptType = 'ENV_RSA';
        $result = $client->getData($systemInfo, $param);
        $result['url'] = 'http://*************:1058/shapi/v1/user/register';

        $html = '<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Messages : View all</title>
</head>
<body>
<div class="container">
<!--http://*************:1058/shapi/v1/user/register -->
<!--http://************:8080/shapi/v1/user/register -->
    <form action="'.$result['url'].'" method="post" id="form">
        jrgw-request-time：<input name="jrgw-request-time" id="jrgw-request-time" value='.$result['time'].'><br>
        jrgw-user-id-type：<input name="jrgw-user-id-type" value="0"/><br>
        gw-encrypt-type：<input name="gw-encrypt-type" id="gw-encrypt-type" value='.$result['encryptType'].'><br>
        gw-sign-type：<input name="gw-sign-type" id="gw-sign-type" value='.$result['signType'].'><br>
        encrypt：<input name="encrypt" id="encrypt" value='.$result['encrypt'].'><br>
        gw-sign：<input name="gw-sign" id="gw-sign" value='.$result['sign'].'><br>
        jrgw-env-key：<input name="jrgw-env-key" id="jrgw-env-key" value='.$result['envelope'].'><br>
        jrgw-enterprise-user-id：<input readonly="readonly" name="jrgw-enterprise-user-id" id="jrgw-enterprise-user-id" value='.$result['userId'].'><br><br>
        <input type="submit" value="提交">
    </form>
</div>

<script type="text/javascript" th:src="@{/js/jquery.min.js}"></script>

</body>
</html>';
        echo $html;
    }


    public function index()
    {
        CommonLogic::signDb();
        $data = DB::connection('supplychan')->table('order')->first();
        $data2 = DB::connection('credit')->table('com_credits')->first();
        var_dump($data);
        var_dump($data2);
    }

    //京东魔盒请求
    public function jdMoheRequest()
    {
        try{
            //从数据库获取的data
            $data = JdparamLogic::jdMoheRequest();
            foreach ($data as $value) {
                Log::write('请求原始参数:'.\GuzzleHttp\json_encode($value),Log::INFO);
                $returnData = (new JdpayLogic())->jdMoheRequest($value);
                Log::write('京东响应参数:'.\GuzzleHttp\json_encode($returnData),Log::INFO);
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage(),1,'操作失败');
        }
        return JdpayLogic::rJson($returnData,0);
    }




    //array(4) { ["setCustCode"]=> string(7) "0000583" ["setCustName"]=> string(15) "有一方公司" ["setSettleUserCode"]=> string(7) "0000583" ["setSettleUserName"]=> string(15) "有一方公司" }
    public function preCreditWhitelist()
    {
        try{
            //从数据库获取的data
            $data = JdparamLogic::preCreditWhitelist();
            foreach ($data as $value){
                Log::write('请求原始参数:'.\GuzzleHttp\json_encode($value),Log::INFO);
                $requestJd = (new JdpayLogic)->preCreditWhitelist($value);
                Log::write('请求京东参数:'.\GuzzleHttp\json_encode($requestJd),Log::INFO);
                $returnData = (new JdpayLogic())->execute($requestJd['method'],$requestJd['request']);
                Log::write('京东响应参数:'.\GuzzleHttp\json_encode($returnData),Log::INFO);
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::ERR);
            return JdpayLogic::rJson($e->getMessage(),1,'操作失败');
        }
        return JdpayLogic::rJson($returnData,0);
    }

    //预授信名单
    public function accessCustInfo()
    {
        try{
            CommonLogic::signDb();
            $companyData = DB::connection('credit')->table('com_credits')->where('erp_company_code','!=','')
                ->select('id')->limit(JdparamLogic::$defaltLimit)->offset(JdparamLogic::$defaltOffset)->get()->toArray();

            foreach ($companyData as $value){
                $requestData = JdparamLogic::accessCustInfo($value->id);
                Log::write('请求原始参数:'.\GuzzleHttp\json_encode($requestData),Log::INFO);
                $requestJd = JdpayLogic::accessCustInfo($requestData);
                Log::write('请求京东参数:'.\GuzzleHttp\json_encode($requestJd),Log::INFO);
                $returnData = (new JdpayLogic())->execute($requestJd['method'],$requestJd['request']);
                Log::write('京东响应参数:'.\GuzzleHttp\json_encode($returnData),Log::INFO);
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage().\GuzzleHttp\json_encode($requestData));
        }
        return JdpayLogic::rJson($returnData);
    }


    public function queryCustInfo()
    {
        try{
            //从数据库获取的data
            $data = JdparamLogic::queryCustInfo();
            $requestJd = JdpayLogic::queryCustInfo($data);
            $returnData = (new JdpayLogic())->execute($requestJd['method'],$requestJd['request']);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage());
        }
        return JdpayLogic::rJson($returnData);
    }


    public function payOrder()
    {
        try{
            //从数据库获取的data
            $data = JdparamLogic::payOrder();
            Log::write('请求原始参数:'.\GuzzleHttp\json_encode($data),Log::INFO);
            $requestJd = JdpayLogic::payOrder($data);
            Log::write('请求京东参数:'.\GuzzleHttp\json_encode($requestJd),Log::INFO);
            $returnData = (new JdpayLogic())->execute($requestJd['method'],$requestJd['request']);
            Log::write('京东响应参数:'.\GuzzleHttp\json_encode($returnData),Log::INFO);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage());
        }
        return JdpayLogic::rJson($returnData);
    }

    public function orderStatus()
    {
        try{
            //从数据库获取的data
            $data = [];
            $requestJd = JdpayLogic::orderStatus($data);
            $returnData = (new JdpayLogic())->execute($requestJd['method'],$requestJd['request']);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage());
        }
        return JdpayLogic::rJson($returnData);
    }

    public function queryBillDetail()
    {
        try{
            //从数据库获取的data
            $data = [];
            $requestJd = JdpayLogic::queryBillDetail($data);
            $data = (new JdpayLogic())->execute($requestJd['method'],$requestJd['request']);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage());
        }
        return JdpayLogic::rJson($data);
    }

    public function applyRepay()
    {
        try{
            //从数据库获取的data
            $data = [];
            $requestJd = JdpayLogic::applyRepay($data);
            $data = (new JdpayLogic())->execute($requestJd['method'],$requestJd['request']);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage());
        }
        return JdpayLogic::rJson($data);
    }

    public function refundApply()
    {
        try{
            //从数据库获取的data
            $data = [];
            $requestJd = JdpayLogic::refundApply($data);
            $data = (new JdpayLogic())->execute($requestJd['method'],$requestJd['request']);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage());
        }
        return JdpayLogic::rJson($data);
    }





    //------------------------------------------以下是通知我们---------------------------//


    /*
     *
     * ={traceId=5813298057145729025, jrgw-request-time=20210810154228, content-length=342, Connection=Keep-Alive, User-Agent=okhttp/4.0.0, Accept-Encoding=gzip, gw-encrypt-type=RSA, jrgw-user-id-type=0, jrgw-service-url=https://jrapi.ichunt.com/index.php?m=supplychain&c=jr&a=accessResultNofity, jrgw-enterprise-user-id=A20200415182, host=*************:8095, content-type=application/json; charset=utf-8, gw-sign-type=MD5_RSA},

请求参数体={"biz-content":{"bizNo":"bizno48de4f162856489797","reqDate":"20210810154228","resCode":"0000000","resDesc":"准入成功","contractNo":"A2021081001002001","custCode":"0000012","creditLimit":2000000.23,"remainLimit":100000.23,"creditEndDate":"20220810154228","auditDate":"20210810154228","ext":{"dayRate":"0.0004000000","custCode":"0000012"}}},
请求path=/spi/v1/co.yfk/accessResultNofity,
请求query参数={},
请求method=POST


    //        $HttpServletRequest->setJrgwRequestTime('20210810154228');
//        $HttpServletRequest->setGwEncryptType('RSA');
//        $HttpServletRequest->setEncrypt('b3Mjjvdp04lEx3mO83MELGfaJ1goucF6d7z2XljXBkgot+IPTgQSfo5XxqnqCnYgA83HWjsBLSZvg02MtAdGuu6HOUpU9jr1o0oNCOOZDpwTao0SeEBLK0IRjAM7MHstGlQeU3HILuDvk8i8BBukVi4HZtqXq0r93DUD28zY9MkGGRZYnSDAtduHpmlFzdJdG1zZH+5/GiyOPastmEm2BKViFayEjifwtP/sYI3J/fiFYNI1huCjmSVBoR0cYw6cW9Swr9nHyYub9v2vOkzduXHuoRw4cUDnYThgBxdzkexhfFu1DK9c6wff9Oqq3GqwJ+p+t6/Bg31zG5HufgIZyQ+zE8peenLsT5v2I7bU3jIGDqeSE9Uu0zT40ha/M7NGOlsWbiA4eqz0m+jcdJTkcr5shxiu4RoZZSJkRNmSJSy8I1HbX/mUeKMbE8nWAOfKsVsXAro/c/LJ9H0aTJY+njzMxcMDMPSmWmqXavsASwh7fns8wVHyG8jZmLbpSGAm');
//        $HttpServletRequest->setGwSign('ZjYuInPHwswgsILy8Y0kwpJe4iPgW5kwdcXNUqbuij9H+***************************+BKhQLXkcmW8rsiRmB2XjJ6dj8gkfD+NfMA9G5VR9ext3e8Lglm14iUyUjPuEcnpQlAryHjOsS2/jneUYfQT6/3zY1CVzlN+Fdg=');
        //$HttpServletRequest->setJrgwEnvKey('RSA'); 暂时不需要
        //$HttpServletRequest->setJrgwNotifyNo('RSA');暂时不需要

     *
     */

    public function accessResultNofity()
    {
        $inputData = file_get_contents('php://input');
        CommonLogic::logsw($inputData,__FUNCTION__,Log::NOTICE);
        CommonLogic::logsw(\GuzzleHttp\json_encode($header = getallheaders()),__FUNCTION__,Log::NOTICE);
        $inputData = \GuzzleHttp\json_decode($inputData,true);

        //测试参数
        $HttpServletRequest = new HttpServletRequest();

        $HttpServletRequest->setJrgwRequestTime($header['Jrgw-Request-Time']);
        $HttpServletRequest->setGwEncryptType($header['Gw-Encrypt-Type']);
        $HttpServletRequest->setGwSign($header['Gw-Sign']);
        $HttpServletRequest->setEncrypt($inputData['encrypt']);

        import('Jdpay.aop.client.DefaultSpiJddClient');
        $client = new \DefaultSpiJddClient();
        $client->appIdType =  C('JDPAY.appId_type');
        $client->appId = C('JDPAY.appid');
        $client->md5Salt = C('JDPAY.md5_salt');
        $client->openPublicKey = C('JDPAY.public_key');
        $client->appPrivateKey = C('JDPAY.private_key');
        $data = $client->receive($HttpServletRequest);

        $client = new \DefaultSpiJddClient();
        //测试参数
        $client->appIdType =  C('JDPAY.appId_type');
        $client->appId = C('JDPAY.appid');
        $client->md5Salt = C('JDPAY.md5_salt');
        $client->openPublicKey = C('JDPAY.public_key');
        $client->appPrivateKey = C('JDPAY.private_key');


        $responseData = $client->callback($HttpServletRequest, $data);

        $stream = new BufferStream();
        $stream->write(\GuzzleHttp\json_encode(['encrypt'=>$responseData['encrypt']]));

        $response = new Response();
        $response = $response->withBody($stream);
        $response = $response->withStatus('200');
        $response = $response->withHeader("Content-type", "application/json");
        foreach ($responseData['responseHeaders'] as $key=>$value){
            $response = $response->withHeader($key, $value);
        }

        //Outputing the response
        http_response_code($response->getStatusCode());

        foreach ($response->getHeaders() as $strName => $arrValue)
        {
            foreach ($arrValue as $strValue)
            {
                if ($strName == 'jrgw-resp-msg'){
                    $strValue = urlencode($strValue);
                }
                header("{$strName}:{$strValue}");
            }
        }

        echo $response->getBody()->getContents();die;
    }



    //查询订单状态
    public function queryOrderStatus()
    {
        $inputData = file_get_contents('php://input');
        CommonLogic::logsw($inputData,__FUNCTION__,Log::NOTICE);
        CommonLogic::logsw(\GuzzleHttp\json_encode($header = getallheaders()),__FUNCTION__,Log::NOTICE);
        $inputData = \GuzzleHttp\json_decode($inputData,true);

        //测试参数
        $HttpServletRequest = new HttpServletRequest();

        $HttpServletRequest->setJrgwRequestTime($header['Jrgw-Request-Time']);
        $HttpServletRequest->setGwEncryptType($header['Gw-Encrypt-Type']);
        $HttpServletRequest->setGwSign($header['Gw-Sign']);
        $HttpServletRequest->setEncrypt($inputData['encrypt']);

        import('Jdpay.aop.client.DefaultSpiJddClient');
        $client = new \DefaultSpiJddClient();
        $client->appIdType =  C('JDPAY.appId_type');
        $client->appId = C('JDPAY.appid');
        $client->md5Salt = C('JDPAY.md5_salt');
        $client->openPublicKey = C('JDPAY.public_key');
        $client->appPrivateKey = C('JDPAY.private_key');
        $data = $client->receive($HttpServletRequest);

        $client = new \DefaultSpiJddClient();
        //测试参数
        $client->appIdType =  C('JDPAY.appId_type');
        $client->appId = C('JDPAY.appid');
        $client->md5Salt = C('JDPAY.md5_salt');
        $client->openPublicKey = C('JDPAY.public_key');
        $client->appPrivateKey = C('JDPAY.private_key');

        //根据传来的data进行判断返回 {"reqNo":"70b96e6429ef45699196a79ad29f4d53","reqDate":"220222174024","productCode":"25041","orderCodes":["order48de4f164552137419"]}
        $data = \GuzzleHttp\json_decode($data,true);
        $data['resDesc'] = 'SUCCESS';
        $data['resDate'] = date('YmdHis');
        $data['resCode'] = '0000000';
        $data['ext']['reqNo'] = $data['reqNo'];
        $data['ext']['reqDate'] = $data['reqDate'];
        $data['ext']['orderCodes'] = $data['orderCodes'];
        $data['ext']['productCode'] = $data['productCode'];
        $data['orders'][] = [
            "orderAmount" =>100,
            "orderStatus" =>'CREATED',
            "payStatus" =>'UNPAY',
            "orderCode" =>$data['orderCodes'][0],
        ];

        $data = \GuzzleHttp\json_encode($data);
        $responseData = $client->callback($HttpServletRequest, $data);

        $stream = new BufferStream();
        $stream->write(\GuzzleHttp\json_encode(['encrypt'=>$responseData['encrypt']]));

        $response = new Response();
        $response = $response->withBody($stream);
        $response = $response->withStatus('200');
        $response = $response->withHeader("Content-type", "application/json");
        foreach ($responseData['responseHeaders'] as $key=>$value){
            $response = $response->withHeader($key, $value);
        }

        //Outputing the response
        http_response_code($response->getStatusCode());

        foreach ($response->getHeaders() as $strName => $arrValue)
        {
            foreach ($arrValue as $strValue)
            {
                if ($strName == 'jrgw-resp-msg'){
                    $strValue = urlencode($strValue);
                }
                header("{$strName}:{$strValue}");
            }
        }

        echo $response->getBody()->getContents();die;
    }




    public function billPeriodNotify()
    {
        CommonLogic::logsw(\GuzzleHttp\json_encode($_REQUEST),__FUNCTION__,Log::NOTICE);
        try{
            //从数据库获取的data
            $data = [];
            $resposeJd = JdpayLogic::billPeriodNotify($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage());
        }
        return JdpayLogic::rJson($data);
    }


    public function refundNotify()
    {
        CommonLogic::logsw(\GuzzleHttp\json_encode($_REQUEST),__FUNCTION__,Log::NOTICE);
        try{
            //从数据库获取的data
            $data = [];
            $resposeJd = JdpayLogic::refundNotify($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage());
        }
        return JdpayLogic::rJson($data);
    }

    public function billNotify()
    {
        CommonLogic::logsw(\GuzzleHttp\json_encode($_REQUEST),__FUNCTION__,Log::NOTICE);
        try{
            //从数据库获取的data
            $data = [];
            $resposeJd = JdpayLogic::billNotify($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage());
        }
        return JdpayLogic::rJson($data);
    }


    public function payResult()
    {
        $inputData = file_get_contents('php://input');
        CommonLogic::logsw($inputData,__FUNCTION__,Log::NOTICE);
        CommonLogic::logsw(\GuzzleHttp\json_encode($header = getallheaders()),__FUNCTION__,Log::NOTICE);
        $inputData = \GuzzleHttp\json_decode($inputData,true);

        //测试参数
        $HttpServletRequest = new HttpServletRequest();

        $HttpServletRequest->setJrgwRequestTime($header['Jrgw-Request-Time']);
        $HttpServletRequest->setGwEncryptType($header['Gw-Encrypt-Type']);
        $HttpServletRequest->setGwSign($header['Gw-Sign']);
        $HttpServletRequest->setEncrypt($inputData['encrypt']);

        import('Jdpay.aop.client.DefaultSpiJddClient');
        $client = new \DefaultSpiJddClient();
        $client->appIdType =  C('JDPAY.appId_type');
        $client->appId = C('JDPAY.appid');
        $client->md5Salt = C('JDPAY.md5_salt');
        $client->openPublicKey = C('JDPAY.public_key');
        $client->appPrivateKey = C('JDPAY.private_key');
        $data = $client->receive($HttpServletRequest);

        $client = new \DefaultSpiJddClient();
        //测试参数
        $client->appIdType =  C('JDPAY.appId_type');
        $client->appId = C('JDPAY.appid');
        $client->md5Salt = C('JDPAY.md5_salt');
        $client->openPublicKey = C('JDPAY.public_key');
        $client->appPrivateKey = C('JDPAY.private_key');

        $responseData = $client->callback($HttpServletRequest, $data);

        $stream = new BufferStream();
        $stream->write(\GuzzleHttp\json_encode(['encrypt'=>$responseData['encrypt']]));

        $response = new Response();
        $response = $response->withBody($stream);
        $response = $response->withStatus('200');
        $response = $response->withHeader("Content-type", "application/json");
        foreach ($responseData['responseHeaders'] as $key=>$value){
            $response = $response->withHeader($key, $value);
        }

        //Outputing the response
        http_response_code($response->getStatusCode());

        foreach ($response->getHeaders() as $strName => $arrValue)
        {
            foreach ($arrValue as $strValue)
            {
                if ($strName == 'jrgw-resp-msg'){
                    $strValue = urlencode($strValue);
                }
                header("{$strName}:{$strValue}");
            }
        }

        echo $response->getBody()->getContents();die;
    }


    public function repayNotify()
    {
        CommonLogic::logsw(\GuzzleHttp\json_encode($_REQUEST),__FUNCTION__,Log::NOTICE);
        try{
            //从数据库获取的data
            $data = [];
            $resposeJd = JdpayLogic::repayNotify($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            return JdpayLogic::rJson($e->getMessage());
        }
        return JdpayLogic::rJson($data);
    }



}