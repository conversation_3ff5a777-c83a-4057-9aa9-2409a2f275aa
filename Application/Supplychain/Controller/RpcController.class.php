<?php

namespace Supplychain\Controller;

use Supplychain\Model\AskCustomerModel;
use Supplychain\Model\AskCustomerProblemListModel;
use Supplychain\Model\InlandDeliveryModel;
use Supplychain\Model\InvoiceMailModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\PaymentApplyModel;
use Supplychain\Model\SupplierModel;
use Supplychain\Model\SZShipmentsNoteModel;
use Supplychain\Model\UserMessageModel;
use Supplychain\Model\WmsTallyDetailModel;
use Supplychain\Model\WmsTallyGoodsModel;
use Supplychain\Model\WmsTallyModel;
use Think\Controller;
use Exception;
use Illuminate\Container\Container;
use Supplychain\Exception\ErpException;
use \Supplychain\ErpServices\ErpService;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\ProductModel;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\ActionLogModel;

class RpcController extends Controller
{
    public static $DB;
    public $erpService;
    public function __construct()
    {
        $container = new Container();
        //普通绑定
//        $container->bind('\Pcb\Model\LARAVELDB',null);
        //单例绑定
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
            return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');
        $this->erpService = new \Supplychain\ErpServices\ErpService;
    }


    protected function apiLog($code, $msg, $log)
    {
        $disable_log = C('DISABLE_LOG');
        $param = I('request.');
        $cookie = cookie();
        $data = array(
            'param' => $param,
            'cookie' => $cookie,
            'log' => $log,
        );
        \Think\Log::record(sprintf('code=%s, msg=%s, data=%s;', $code, $msg, json_encode($data)));
    }

    /**
     * 统一格式返回
     * @param  integer $code   [description]
     * @param  string  $msg    [description]
     * @param  array   $extend [description]
     * @return [type]          [description]
     */
    protected function apiReturn($code = 0, $msg = '', $extend = array())
    {
        $data = array(
            'err_code' => $code,
            'err_msg' => $msg,
            'data' => $extend,
        );
        if ($code != 0) {
            $this->apiLog($code, $msg, $extend);
            if ($code > 0) {
                unset($data['data']);
            }
            $data['err_code'] = abs($data['err_code']);
            \Think\Log::save();
        }
        return json_encode($data);
    }

    public function index(){

        vendor('Hprose.HproseHttpServer');
        $server = new \HproseHttpServer();
        $methods = get_class_methods($this);
        $methods = array_diff($methods, array('__construct', '__call', '_initialize'));
        $server->addMethod('syncGoodsInfo',$this);
        $server->addMethod('erpPullOrderAudit',$this);
        $server->addMethod('erpPullExpectPayees',$this);
        $server->addMethod('erpPullCollectOrder',$this);
        $server->addMethod('erpPullExpectPayees',$this);
        $server->addMethod('erpPullDeletectOrder',$this);
        $server->addMethod('erpPullEditOrderRecord',$this);
        $server->addMethod('erpPullDeletectEditOrderRecord',$this);
        $server->addMethod('erpUpdateSupplier',$this);
        $server->addMethod('erpUpdateCustomerInfo',$this);
        $server->addMethod('erpSyncServiceAgreement',$this);
        $server->addMethod('erpSyncReceiptRecord',$this);
        $server->addMethod('erpSynDelReceiptRecord',$this);
        $server->addMethod('erpSynDelServiceAgreement',$this);
        $server->addMethod('erpUpdateCustomerAmount',$this);
        $server->addMethod('erpSynBaoGuan',$this);
        $server->addMethod('erpDelBaoGuan',$this);
        $server->addMethod('erpSynHKDelivery',$this);
        $server->addMethod('erpDelHKDelivery',$this);
        $server->addMethod('erpSynSZDelivery',$this);
        $server->addMethod('erpDelSZDelivery',$this);
        $server->addMethod('erpSynSZSendDelivery',$this);
        $server->addMethod('erpDelSZSendDelivery',$this);
        $server->addMethod('erpSyncVerification',$this);
        $server->addMethod('adminUnBindUser',$this);
        $server->addMethod('synGetCustomerAndPrincipal',$this);
        $server->addMethod('synGetSupplierAndBank',$this);
        $server->addMethod('synGetGoodsRegister',$this);
        $server->addMethod('synDelGoodsRegister',$this);
        $server->addMethod('sendErpPayNotifyEmail',$this);
        $server->addMethod('updatePaymentApplyStatus',$this);
        $server->addMethod('deletePaymentApply',$this);
        $server->addMethod('updatePaymentApplyOriginStatus',$this);
        $server->addMethod('cancelOrder',$this);
        $server->addMethod('asyncOrderlogisticsInfo',$this);
        $server->addMethod('checkCanChangePurOrderNew',$this);
        $server->addMethod('updateSupplyCommonStatus',$this);


        //增加erp订单金额统计
        $server->addMethod('sumPlatformOrderMoney',$this);
        //增加禁用供应商
        $server->addMethod('stopSupply',$this);
        $server->addMethod('startSupply',$this);
        //增加一一同步供应商
        $server->addMethod('syncSupplier',$this);
        $server->addMethod('syncSupplierBank',$this);

        $server->addMethod('getErpOrderSnTray',$this);
        $server->addMethod('syncInvoiceMail',$this);


        $server->addMethod('checkCanChangePurOrder',$this);
        $server->addMethod('changeWmsTallyGoodsSyncStatus',$this);

        $server->addMethod('getDeliveryPhone',$this);


        $server->addMethod('updateBoxSnBatch',$this);
        $server->addMethod('updateOrderInfo',$this);

        $server->addMethod('synCustomsLog',$this);

        $server->addMethod('synCustomsPlanExp',$this);
        $server->addMethod('synCustomsPlanExpCar',$this);
        $server->addMethod('synClassifyInfo',$this);
        $server->addMethod('synCustomsCode',$this);
        $server->addMethod('getPlateQty',$this);
        $server->addMethod('sendWechatMsg',$this);
        $server->addMethod('checkBoxPushed',$this);
        $server->handle();

    }

    public function checkBoxPushed($data)
    {
        try{
            \Think\Log::write('判断箱号已推送金蝶-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->checkBoxPushed(json_decode($data,true));
            return $this->apiReturn('0','判断箱号已推送金蝶',$data);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('10000',$e->getMessage());
        }
    }


    public function sendWechatMsg($data){
        try{
            \Think\Log::write('发送微信消息-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->sendWechatMsg(json_decode($data,true));
            return $this->apiReturn('0','发送微信消息成功',$data);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('10000',$e->getMessage());
        }
    }

    public function getPlateQty($data){
        try{
            \Think\Log::write('获取装载卡板数-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->getPlateQty(json_decode($data,true));
            return $this->apiReturn('0','获取装载卡板数成功',$data);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('10000',$e->getMessage());
        }
    }

    /**
     * @param $data
     * @return string
     * 同步物料数据
     */
    public function syncGoodsInfo($data){
        try{
            \Think\Log::write('同步物料数据到本地-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $bk = $this->erpService->syncGoodsInfo(json_decode($data,true));
            if(!$bk) return $this->apiReturn('10000','同步物料数据失败');
            return $this->apiReturn('0','成功');
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('10000','同步物料数据失败');
        }
    }


    /**
     * @param $data
     * @return string
     * 推送服务协议到本地
     * 废弃
     */
//    public function syncFuWuXieYiToLocal($data){
//        try{
//            \Think\Log::write('同步服务协议到本地-----------------','WARN');
//            \Think\Log::write($data,'WARN');
//            $bk = $this->erpService->syncFuWuXieYiToLocal(json_decode($data,true));
//            if(!$bk) return $this->apiReturn('10000','同步服务协议失败');
//            return $this->apiReturn('0','成功');
//        }catch(\Exception $e){
//            \Think\Log::write($e->getMessage(),'WARN');
//            return $this->apiReturn('10000','同步服务协议失败');
//        }
//    }



    /*
     * erp推送订单审核数据到本地
     */
    public function erpPullOrderAudit($data){
        try{
            \Think\Log::write('erp推送订单审核数据到本地-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->erpPullOrderAudit(json_decode($data,true));
            return $this->apiReturn('0','订单审核数据推送成功',$data);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('4444',$e->getMessage());
        }
    }

    /*
     * erp推送数据到本地
     * 预计应收款
     * 付款通知书
     */
    public function erpPullExpectPayees($data){
        try{
            \Think\Log::write('erp推送预计应收款数据到本地-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->auditOrderPayCost(json_decode($data,true));
            return $this->apiReturn('0','预计应收款推送成功');
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('4444','预计应收款推送失败');
        }
    }

    /*
    * erp推送数据到本地
    * 收款单
    */
//    public function erpPullCollectOrder($data){
//        try{
//            \Think\Log::write('收款单收款单收款单收款单-----------------','WARN');
//            \Think\Log::write($data,'WARN');
//            return $this->apiReturn('0','收款单推送成功');
//        }catch(\Exception $e){
//            \Think\Log::write($e->getMessage(),'WARN');
//            return $this->apiReturn('4444','收款单推送失败');
//        }
//    }

    /*
    * erp推送数据到本地
    * 删除订单
    */
    public function erpPullDeletectOrder($data){
        try{
            \Think\Log::write('删除订单删除订单删除订单删除订单-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpDeleteOrder(json_decode($data,true));
            return $this->apiReturn('0','删除订单成功');
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('1',$e->getMessage());
        }
    }


    /*
    * erp推送数据到本地
    * 改单记录
    */
    public function erpPullEditOrderRecord($data){
        try{
            \Think\Log::write('改单记录改单记录改单记录改单记录改单记录-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpPullEditOrderRecord(json_decode($data,true));
            return $this->apiReturn('0','新增改单记录成功');
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('10000','新增改单记录失败');
        }
    }

    /*
    * erp推送数据到本地
    * 改单记录删除
    */
//    public function erpPullDeletectEditOrderRecord($data){
//        try{
//            \Think\Log::write('改单记录删除-----------------','WARN');
//            \Think\Log::write($data,'WARN');
//            return $this->apiReturn('0','改单记录删除成功');
//        }catch(\Exception $e){
//            \Think\Log::write($e->getMessage(),'WARN');
//            return $this->apiReturn('10000','改单记录删除失败');
//        }
//    }

    /*
    * erp推送数据到本地
    * 修改供应商数据
    */
    public function erpUpdateSupplier($data){
        try{
            \Think\Log::write('ERP修改供应商数据-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpUpdateSupplier(json_decode($data, true));
            return $this->apiReturn('0', 'ERP修改供应商数据成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(), 'WARN');
            return $this->apiReturn('10000','ERP修改供应商数据失败');
        }
    }

    /*
    * erp推送数据到本地
    * 修改客户余额
    */
    public function erpUpdateCustomerAmount($data){
        try{
            \Think\Log::write('修改客户余额-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpUpdateCustomerAmount(json_decode($data, true));
            return $this->apiReturn('0', '修改客户余额成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(), 'WARN');

            return $this->apiReturn('10000','修改客户余额失败');
        }
    }

    /**
     * erp推送数据到本地
     * 客户 委托方修改
     */
     public function erpUpdateCustomerInfo($data){
        try{
            \Think\Log::write('ERP修改客户委托方修改数据-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpUpdateCustomerInfo(json_decode($data, true));
            return $this->apiReturn('0', 'ERP修改客户委托方信息成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(), 'WARN');
            return $this->apiReturn('10000','ERP修改客户委托方信息失败');
        }
    }

    /**
     * erp推送数据到本地
     * 同步服务协议到本地
     */
    public function erpSyncServiceAgreement($data){
        try{
            \Think\Log::write('ERP同步服务协议数据-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpSyncServiceAgreement(json_decode($data, true));
            return $this->apiReturn('0', 'ERP同步服务协议数据成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(), 'WARN');
            return $this->apiReturn('10000',$e->getMessage());
        }
    }


    /**
     * erp推送数据到本地
     * 删除服务协议
     */
    public function erpSynDelServiceAgreement($data){
        try{
            \Think\Log::write('ERP删除服务协议-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpSynDelServiceAgreement(json_decode($data, true));
            return $this->apiReturn('0', 'ERP删除服务协议成功');
        }catch(ErpException $e){
            return $this->apiReturn('0', $e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(), 'WARN');
            return $this->apiReturn('10000','ERP删除服务协议失败');
        }
    }


    /**
     * erp推送数据到本地
     * 同步收款单到本地
     */
    public function erpSyncReceiptRecord($data){
        try{
            \Think\Log::write('ERP同步收款单到本地-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpSyncReceiptRecord(json_decode($data, true));
            return $this->apiReturn('0', 'ERP同步收款单到本地成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','ERP同步收款单到本地失败');
        }
    }

    /**
     * erp推送数据到本地
     * 删除收款单
     */
    public function erpSynDelReceiptRecord($data){
        try{
            \Think\Log::write('ERP删除收款单-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpSynDelReceiptRecord(json_decode($data, true));
            return $this->apiReturn('0', 'ERP删除收款单成功');
        }catch(ErpException $e){
            return $this->apiReturn('0', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','ERP删除收款单失败');
        }
    }

    /**
     * erp推送数据到本地
     * 报关 等于香港发货
     */
    public function erpSynBaoGuan($data){
        try{
            \Think\Log::write('ERP同步报关-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpSynBaoGuan(json_decode($data, true));
            return $this->apiReturn('0', 'ERP同步报关成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000',$e->getMessage());
        }
    }

    /**
     * erp推送数据到本地
     * 删除报关
     */
    public function erpDelBaoGuan($data){
        try{
            \Think\Log::write('ERP删除报关-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpDelBaoGuan(json_decode($data, true));
            return $this->apiReturn('0', 'ERP删除报关成功');
        }catch(ErpException $e){
            return $this->apiReturn('0', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','ERP删除报关失败');
        }
    }

    /**
     * erp推送数据到本地
     * 香港收货
     */
    public function erpSynHKDelivery($data){
        try{
            \Think\Log::write('ERP同步香港收货-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpSynHKDelivery(json_decode($data, true));
            return $this->apiReturn('0', 'ERP同步香港收货成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','ERP同步香港收货失败');
        }
    }
    /**
     * erp推送数据到本地
     * 香港收货删除
     */
    public function erpDelHKDelivery($data){
        try{
            \Think\Log::write('ERP删除香港收货-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpDelHKDelivery(json_decode($data, true));
            return $this->apiReturn('0', 'ERP删除香港收货成功');
        }catch(ErpException $e){
            return $this->apiReturn('0', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','ERP删除香港收货失败');
        }
    }


    /**
     * erp推送数据到本地
     * 深圳收货通知单
     */
    public function erpSynSZDelivery($data){
        try{
            \Think\Log::write('ERP同步深圳收货通知单-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpSynSZDelivery(json_decode($data, true));
            return $this->apiReturn('0', 'ERP同步深圳收货通知单成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','ERP同步深圳收货通知单失败');
        }
    }

    /**
     * erp推送数据到本地
     * 深圳删除收货通知单
     */
    public function erpDelSZDelivery($data){
        try{
            \Think\Log::write('ERP深圳删除收货通知单-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpDelSZDelivery(json_decode($data, true));
            return $this->apiReturn('0', 'ERP深圳删除收货通知单成功');
        }catch(ErpException $e){
            return $this->apiReturn('0', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','ERP深圳删除收货通知单失败');
        }
    }

    /**
     * erp推送数据到本地
     * 深圳发货通知单
     */
    public function erpSynSZSendDelivery($data){
        try{
            \Think\Log::write('ERP同步深圳发货通知单-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpSynSZSendDelivery(json_decode($data, true));
            return $this->apiReturn('0', 'ERP同步深圳发货通知单成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','ERP同步深圳发货通知单失败');
        }
    }


    /**
     * erp推送数据到本地
     * 删除深圳发货通知单
     */
    public function erpDelSZSendDelivery($data){
        try{
            \Think\Log::write('ERP删除深圳发货通知单-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpDelSZSendDelivery(json_decode($data, true));
            return $this->apiReturn('0', 'ERP删除深圳发货通知单成功');
        }catch(ErpException $e){
            return $this->apiReturn('0', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','ERP删除深圳发货通知单失败');
        }
    }

    /**
     * erp推送数据到本地
     * 核销记录
     */
    public function erpSyncVerification($data){
        try{
            \Think\Log::write('ERP同步核销记录-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->erpSyncVerification(json_decode($data, true));
            return $this->apiReturn('0', 'ERP同步核销记录成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','ERP同步核销记录失败');
        }
    }


    /**
     * 后台操作  解绑用户 清除用户缓存
     */
    public function adminUnBindUser($customer_id){
        try{
            $this->erpService->adminUnBindUser($customer_id);
            return $this->apiReturn('0', 'ok');
        }catch(\Exception $e){
            return $this->apiReturn('10000','解绑用户失败');
        }
    }

    /**
     * 前台用户订单 改单 生成新的订单相关附表
     * $data array
     */
    public function EorderCNewOrder($data){
        try{
            if(is_string($data)) $data = \GuzzleHttp\json_decode($data);
            $order_id = $data['order_id'];
            (new \Supplychain\Service\OrderService)->EorderCNewOrder($order_id);
        }catch(\Exception $e){
            return false;
        }
    }

    /**
     * erp推送 新增 或者修改 客户和委托方到本地
     */
    public function synGetCustomerAndPrincipal($data){
        try{
            \Think\Log::write('erp推送 客户和委托方到本地-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->synGetCustomerAndPrincipal(json_decode($data, true));
            return $this->apiReturn('0', 'ok',$data);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送 客户和委托方到本地失败');
        }
    }


    /**
     * erp推送  新增或者修改  供应商和银行信息到本地
     * 去除失误操作 ********
     */
    public function synGetSupplierAndBank($data){
        try{
            return 1;
            \Think\Log::write(' erp推送  新增或者修改  供应商和银行信息到本地-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->synGetSupplierAndBank(json_decode($data, true));
            return $this->apiReturn('0', 'ok',$data);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送  新增或者修改  供应商和银行信息到本地');
        }
    }


    /**
     * eas  新增或者修改  供应商银行至本地
     */
    public function syncSupplier($data){
        try{
            \Think\Log::write(' erp推送  新增供应商信息到本地-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->syncSupplier(json_decode($data, true));
            return $this->apiReturn('0', 'ok',$data);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送  新增或者修改  供应商到本地'.$e->getMessage());
        }
    }


    /**
     * eas  新增或者修改  供应商银行至本地
     */
    public function syncSupplierBank($data){
        try{
            \Think\Log::write(' erp推送  新增供应商银行信息到本地-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->syncSupplierBank(json_decode($data, true));
            return $this->apiReturn('0', 'ok',$data);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送  新增或者修改  供应商银行信息到本地'.$e->getMessage());
        }
    }

    /**
     * eas  新增或者修改  供应商银行至本地
     */
    public function getErpOrderSnTray($data){
        try{
            \Think\Log::write(' erp推送  获取入仓号托盘-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->getErpOrderSnTray(json_decode($data, true));
            return $this->apiReturn('0', 'ok',$data);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送  获取入仓号托盘错误'.$e->getMessage());
        }
    }

    //收货登记新增推送接口
    public function synGetGoodsRegister($data)
    {
        try{
            \Think\Log::write(' erp推送  新增或者修改  收货登记推送新增-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->synGetGoodsRegister(json_decode($data, true));
            return $this->apiReturn('0', 'ok',$data);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送 收货登记推送新增到本地失败');
        }
    }

    //收货登记接口删除
    public function synDelGoodsRegister($data)
    {
        try{
            \Think\Log::write(' erp推送  新增或者修改  收货登记接口删除-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->synDelGoodsRegister(json_decode($data, true));
            return $this->apiReturn('0', 'ok',$data);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送 收货登记接口删除推送到本地失败');
        }
    }

    //收货登记接口删除
    public function sendErpPayNotifyEmail($data)
    {
        try{
            \Think\Log::write(' erp推送  新增或者修改  付款成功发送通知邮件-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = $this->erpService->sendErpPayNotifyEmail(json_decode($data, true));
            return $this->apiReturn('0', 'ok',$data);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送 收货登记接口删除推送到本地失败');
        }
    }


    //修改为已经审核
    public function updatePaymentApplyStatus($data)
    {
        try{
            \Think\Log::write(' erp推送  修改付汇申请-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = json_decode($data, true);

            PaymentApplyModel::where('number',$data['poolNumber'])->whereIn('status',[0,1])
                ->update(['complete_time'=>strtotime($data['completeTime']),'status'=>1]);
            return $this->apiReturn('0', 'ok','修改成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送 修改付汇申请失败');
        }
    }


    //删除付汇申请
    public function deletePaymentApply($data)
    {
        try{
            \Think\Log::write(' erp推送  删除付汇申请-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = json_decode($data, true);

            if (!isset($data['remark'])){
                $data['remark'] = '';
            }

            $erpOrderSnArr = PaymentApplyModel::where('number',$data['poolNumber'])->whereIn('status',[0,1])->pluck('entrust_bill_number')->toArray();
            \Think\Log::write(\GuzzleHttp\json_encode($erpOrderSnArr),'WARN');
            if (!empty($erpOrderSnArr)){
                OrderModel::whereIn('erp_order_sn',$erpOrderSnArr)->update(['is_apply_payment'=>0]);
            }

            PaymentApplyModel::where('number',$data['poolNumber'])->whereIn('status',[0,1])->update(['complete_time'=>0,'status'=>2,'eas_remark'=>$data['remark']]);
            return $this->apiReturn('0', 'ok','修改成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送 删除付汇申请失败');
        }
    }


    //修改为成反审，也就是未审核
    public function updatePaymentApplyOriginStatus($data)
    {
        try{
            \Think\Log::write(' erp推送  反审付汇申请-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = json_decode($data, true);

            PaymentApplyModel::where('number',$data['poolNumber'])->whereIn('status',[0,1])
                ->update(['complete_time'=>0,'status'=>0]);

            return $this->apiReturn('0', 'ok','修改成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送 反审付汇申请失败');
        }
    }


    //修改订单为已经取消
    public function cancelOrder($data)
    {
        try{
            \Think\Log::write(' erp推送  修改订单为已经取消-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = json_decode($data, true);

            OrderModel::where('erp_order_sn',$data['erp_order_sn'])->update(['status'=>-3]);
            return $this->apiReturn('0', 'ok','修改成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送 修改订单为已经取消');
        }
    }

    //同步订单物流信息
    public function asyncOrderlogisticsInfo($data)
    {
        try{
            \Think\Log::write(' erp推送  同步订单国内物流信息-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = json_decode($data, true);
            if (!isset($data['entrustNo']) || empty($data['entrustNo']) ){
                throw new Exception('erp 入仓号不能为空');
            }

            //{"logisticCode":"SF1331353288297","printDate":"2021-09-23 10:00:55","logisCompany":"顺丰速运","entrustNo":"B50123","status":"已发货","count":1,"receiverName":"乔**"}

//            SZShipmentsNoteModel::where('erp_order_sn',$data['entrustNo'])->update([
//                'carrier'=>$data['logisCompany'],'stream_number'=>$data['logisticCode'],'create_time'=>strtotime($data['printDate']),
//                'status'=>1,'count'=>$data['count'],
//            ]);
            return $this->apiReturn('0', 'ok','修改成功');
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp推送 同步订单国内物流信息失败：'.$e->getMessage());
        }
    }


    //统计平台金额订单
    public function sumPlatformOrderMoney($data)
    {
        try{
            \Think\Log::write(' erp请求  sumPlatformOrderMoney-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $data = json_decode($data, true);
            $time = strtotime($data['date']);
            $money = OrderModel::where('user_id','!=',0)
                ->where('status','>',0)
                ->where('create_time','<',($time+86400))
                ->where('create_time','>=',$time)
                ->sum('order_price');
            return $this->apiReturn('0', 'ok',['money'=>$money]);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  sumPlatformOrderMoney失败');
        }
    }


    //禁用供应商
    public function stopSupply($data)
    {
        try{
            \Think\Log::write(' erp请求  stopSupply-----------------','WARN');
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            SupplierModel::where('erp_supplier_id',$data['ErpSupplyID'])->orWhere('supplier_name',$data['ErpSupplyName'])->update([
                'status'=>-2,
                'auditor'=>'admin',
                'audit_time'=>time(),
            ]);
            return $this->apiReturn('0', 'ok','');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  stopSupply');
        }
    }

    //启用供应商
    public function startSupply($data)
    {
        try{
            \Think\Log::write(' erp请求  startSupply-----------------','WARN');
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            SupplierModel::where('erp_supplier_id',$data['ErpSupplyID'])->update([
                'status'=>1,
                'auditor'=>'admin',
                'audit_time'=>time(),
            ]);
            return $this->apiReturn('0', 'ok','');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  startSupply');
        }
    }


    //同步发票邮寄
    public function syncInvoiceMail($data)
    {
        try{
            \Think\Log::write(' erp请求  syncInvoiceMail-----------------','WARN');
            \Think\Log::write($data,'WARN');

            $data = \GuzzleHttp\json_decode($data,true);
            $data['create_time'] = time();
            $data['create_order_time'] = intval($data['create_order_time']/1000);
            $data['invoice_time'] = intval($data['invoice_time']/1000);
            //有发票就修改，没有就新增
            if (empty(InvoiceMailModel::where('invoice_sn',$data['invoice_sn'])->value('ieml_id'))){
                InvoiceMailModel::insertGetId($data);
            }else{
                InvoiceMailModel::where('invoice_sn',$data['invoice_sn'])->update($data);
            }
            return $this->apiReturn('0', 'ok','');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  syncInvoiceMail');
        }
    }


    //检测是否可以修改采购订单
    public function checkCanChangePurOrder($data)
    {
        try{
            \Think\Log::write(' erp请求  checkCanChangePurOrder-----------------','WARN');
            \Think\Log::write($data,'WARN');

            $data = \GuzzleHttp\json_decode($data,true);

            if (empty($askCustomerId = AskCustomerModel::where('erp_order_sn',$data['erp_order_sn'])->value('ask_customer_id'))){
                throw new ErpException($data['erp_order_sn'].'暂无问客数据，不可以修改');
            }

            if (!empty(WmsTallyGoodsModel::where('erp_order_sn',$data['erp_order_sn'])->value('erp_order_sn'))){
                throw new ErpException($data['erp_order_sn'].'已存在理货记录，不可以修改，请先作废理货纪录');
            }

            return $this->apiReturn('0', 'ok','');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  syncInvoiceMail');
        }
    }

    //检测是否可以修改采购订单
    public function checkCanChangePurOrderNew($data)
    {
        try{
            \Think\Log::write(' erp请求  checkCanChangePurOrderNew-----------------','WARN');
            \Think\Log::write($data,'WARN');

            $data = \GuzzleHttp\json_decode($data,true);


            if (!empty(WmsTallyDetailModel::where('erp_order_sn',$data['erp_order_sn'])->value('erp_order_sn'))){
                throw new ErpException($data['erp_order_sn'].'仓库已锁定理货，请联系仓库取消释放');
            }

            return $this->apiReturn('0', 'ok','');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  syncInvoiceMail');
        }
    }


    //修改理货记录同步状态
    public function changeWmsTallyGoodsSyncStatus($data)
    {
        try{
            \Think\Log::write(' erp请求  changeWmsTallyGoodsSyncStatus-----------------','WARN');
            \Think\Log::write($data,'WARN');

            $data = \GuzzleHttp\json_decode($data,true);

            if (empty(WmsTallyGoodsModel::where('erp_order_sn',$data['erp_order_sn'])->value('erp_order_sn'))){
                throw new ErpException($data['erp_order_sn'].'不存在理货记录，请确认');
            }

            WmsTallyGoodsModel::where('erp_order_sn',$data['erp_order_sn'])->update([
                'sync_status'=>$data['sync_status'],
                'sync_msg'=>$data['sync_msg'],
            ]);

            return $this->apiReturn('0', 'ok','');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  changeWmsTallyGoodsSyncStatus');
        }
    }

    //获取发货手机号码
    public function getDeliveryPhone($data)
    {
        try{
            \Think\Log::write(' erp请求  getDeliveryPhone-----------------','WARN');
            \Think\Log::write($data,'WARN');

            $data = \GuzzleHttp\json_decode($data,true);

            $companyId = OrderModel::where('erp_order_sn',$data['erp_order_sn'])->where('company_id','!=',0)->value('company_id');
            if (empty($companyId)){
                return $this->apiReturn('0', 'ok',[]);
            }

            $list = UserMessageModel::where("company_id",$companyId)->whereRaw("FIND_IN_SET(5,custom_fun)")->where('type',1)->pluck("send_user")->toArray();

            return $this->apiReturn('0', 'ok',$list);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  changeWmsTallyGoodsSyncStatus');
        }
    }

    //修改订单信息
    //[
    //	{
    //		"orderId": "金蝶委托单ID",
    //		"CARRIER": "承运商",
    //		"LOGISTICSNO": "物流单号",
    //		"originCountrys": [
    //			{
    //				"orderDetailId": "金蝶委托单明细ID",
    //				"originCountry": "产地"
    //			}
    //		]
    //	}
    //]
    public function updateOrderInfo($data)
    {
        try{
            \Think\Log::write(' erp请求  getDeliveryPhone-----------------','WARN');
            \Think\Log::write($data,'WARN');

            $data = \GuzzleHttp\json_decode($data,true);

            foreach ($data as $order){
                $orderData = OrderModel::where('erp_order_id',$order['orderId'])->first();
                if (empty($orderData)){
                    continue;
                }
                OrderModel::where('erp_order_id',$order['orderId'])->update([
                    'carrier'=>$order['CARRIER'],
                    'logistics_no'=>$order['LOGISTICSNO'],
                ]);
                $content = '修改物流信息，原物流信息：'.$orderData['carrier'].':'.$orderData['logistics_no'];
                (new ActionLogModel())->addLog($orderData['order_id'], $content, isset($order['opreator'])?$order['opreator']:'admin', 1);
                foreach ($order['originCountrys'] as $orderGoods){
                    // 修改产地
                    $orderGoodsData = OrderGoodsModel::where('erp_entery_id',$orderGoods['orderDetailId'])->first();
                    if (empty($orderGoodsData)){
                        continue;
                    }
                    OrderGoodsModel::where('erp_entery_id',$orderGoods['orderDetailId'])->update([
                       'origin'=>$orderGoods['originCountry']
                    ]);
                    $content = '修改产地，明细ID:'.$orderGoodsData['order_goods_id'].'原产地：'.$orderGoodsData['origin'].'；';
                    (new ActionLogModel())->addLog($orderData['order_id'], $content, isset($order['opreator'])?$order['opreator']:'admin', 1);
                }
            }

            return $this->apiReturn('0', 'ok',[]);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
//            return $this->apiReturn('10000','erp请求  changeWmsTallyGoodsSyncStatus');
            return $this->apiReturn('10000',$e->getMessage());
        }
    }

    public function synCustomsLog($data)
    {
        try{
            \Think\Log::write(' erp请求  synCustomsLog-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->synCustomsLog(json_decode($data, true));
            return $this->apiReturn('0', '报关日志同步成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  synCustomsLog');
        }
    }

    public function synCustomsPlanExp($data)
    {
        try{
            \Think\Log::write(' erp请求  synCustomsPlanExp-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->synCustomsPlanExp(json_decode($data, true));
            return $this->apiReturn('0', '报关计划同步成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  synCustomsPlanExp');
        }
    }

    public function synCustomsPlanExpCar($data)
    {
        try{
            \Think\Log::write(' erp请求  synCustomsPlanExpCar-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->synCustomsPlanExpCar(json_decode($data, true));
            return $this->apiReturn('0', '排班管理同步成功');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  synCustomsPlanExpCar');
        }
    }

    public function synClassifyInfo($data)
    {
        try{
            \Think\Log::write(' erp请求  synClassifyInfo-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->synClassifyInfo(json_decode($data, true));
            return $this->apiReturn('0', '同步归类信息成功');
        }catch(Exception $e){
            return $this->apiReturn('10000', $e->getMessage());
        }
    }

    public function synCustomsCode($data)
    {
        try{
            \Think\Log::write(' erp请求  synCustomsCode-----------------','WARN');
            \Think\Log::write($data,'WARN');
            $this->erpService->synCustomsCode(json_decode($data, true));
            return $this->apiReturn('0', '同步海关编码成功');
        }catch(Exception $e){
            return $this->apiReturn('10000', $e->getMessage());
        }
    }

    //修改箱子批次
    //{
    //	"sync_status": 0,
    //	"sync_errer_msg": "成功",
    //	"sync_data": {
    //		"ctnNo": "X240428001",
    //		"details": [
    //			{
    //				"entrustNo": "B00001",
    //				"lot": "B00001-1"
    //			}
    //		]
    //	}
    //}
    public function updateBoxSnBatch($data)
    {
        try{
            \Think\Log::write(' erp请求  updateBoxSnBatch-----------------','WARN');
            \Think\Log::write($data,'WARN');

            $data = \GuzzleHttp\json_decode($data,true);

            if ($data['sync_status'] === 0){
                $tallyStatus = 2;
                $detailTallyStatus = 3;
            }else{
                $tallyStatus = 2;
                $detailTallyStatus = 4;
            }
            $boxSn = $data['sync_data']['ctnNo'];


            WmsTallyModel::where('box_sn',$boxSn)->update([
                //'tally_status'=>$tallyStatus,
                'sync_msg'=>$data['sync_errer_msg']
            ]);


            foreach ($data['sync_data']['details'] as $value){
                $lot = explode('-',$value['lot']);

                WmsTallyDetailModel::where('box_sn',$boxSn)
                    ->where('erp_order_sn',$value['entrustNo'])->update([
                        'sync_status'=>$detailTallyStatus,
                        'batch'=>isset($lot[1])?$lot[1]:''
                    ]);
            }
            return $this->apiReturn('0', 'ok','');
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  changeWmsTallyGoodsSyncStatus');
        }
    }



    public function updateSupplyCommonStatus($data)
    {
        try{
            \Think\Log::write(' erp请求  updateSupplyCommonStatus-----------------','WARN');
            \Think\Log::write($data,'WARN');

            $data = \GuzzleHttp\json_decode($data,true);

            SupplierModel::where('supplier_name',$data['companyName'])->update([
               'risk_status'=>$data['result']
            ]);

            return $this->apiReturn('0', 'ok',[]);
        }catch(ErpException $e){
            return $this->apiReturn('10000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('10000','erp请求  changeWmsTallyGoodsSyncStatus');
        }
    }

}