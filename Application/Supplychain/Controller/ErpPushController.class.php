<?php
namespace Supplychain\Controller;

//use Server\Controller\BaseController;
use Exception;
use Supplychain\Exception\ErpException;
use \Supplychain\ErpServices\ErpService;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\ActionLogModel;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\OrderModel;
use Supplychain\Model\PaymentApplyModel;
use Supplychain\Model\PushLogModel;
use Supplychain\Model\SupplierBankModel;
use Supplychain\Repository\HproseRepository;
use Supplychain\Service\DingNotify;
use Think\Controller\HproseController;
//ini_set ( "soap.wsdl_cache_enabled" ,"0" );
class ErpPushController extends \Supplychain\Controller\ErpBaseController
{
    use \Supplychain\Traits\SenMsgTrait;
    public $log = '';
    public $isTest = true;

    public function _initialize()
    {
        parent::_initialize();
        try{
            //登录
            //********去除登陆
            //$soap = new \SoapClient(ERP_SUPPLY_DOMAIN.'/ormrpc/services/EASLogin?wsdl');
            //$res = $soap->login('WBYH', '123456', 'eas', C('ERP_SUPPLY_DB_NAME'),  'L2', 1, 'BaseDB');

            //接口
            $this->erp = new \SoapClient(ERP_SUPPLY_DOMAIN.'/ormrpc/services/WSIchuntDocking?wsdl');
            $this->orderErp = new \SoapClient(ERP_SUPPLY_DOMAIN.'/ormrpc/services/WSIchuntCustomTaskFacade?wsdl');
            //如果是测试
            if($_SERVER['SERVER_NAME']=== 'api.ichunt.com'){
                $this->isTest = false;
            }
            $this->orderErpTest = new \SoapClient(ERP_SUPPLY_DOMAIN.'/ormrpc/services/WSInventoryManagementFacade?wsdl');
        }catch(\SoapFault $e){
            return $this->apiReturn('30000', $e->getMessage());
        }catch(\Exception $e){
            return $this->apiReturn('30000', $e->getMessage());
        }

        $this->erpServices = new ErpService;
    }

    /**
     * 判断erp返回数据成功或者失败
     */
    public function returnOk($arr){
        if(isset($arr['0000'])){
            return true;
        }elseif(isset($arr['4444'])){
            return false;
        }
        return false;
    }

    /**
     * 客户资料同步
     */
    public function syncCustomerInfo()
    {
        try {
            $user_id = I("user_id",0,'intval');
            $data = [];
            $data = $this->erpServices->pushCustomerInfo($user_id);
            \Think\Log::write(json_encode($data),'WARN');
            $res = $returstr = $this->erp->ceatePrincipalAndCustomer(json_encode($data,JSON_UNESCAPED_UNICODE));
            $res = json_decode($res,true);
            $isok = $this->returnOk($res);

            //签署临时服务协议
            $customerInfo = CustomerModel::where('user_id',$user_id)->first();
            $createTFProtocolData['customerName'] = CompanyModel::where('company_id',$customerInfo->company_id)->value('company_full_name');
            $createTFProtocolData['invalidDate'] = date('Y-m-d H:i:s',time()+(86400 * 30));
            $createTFProtocolData['dlfcpRate'] = $customerInfo->dlfcp_rate;
            $createTFProtocolData['lowDlfAmt'] = $customerInfo->low_dlfAmt;
            \Think\Log::write(json_encode($createTFProtocolData),'WARN');
            $res = $returstr = $this->erp->createTFProtocol(json_encode($createTFProtocolData,JSON_UNESCAPED_UNICODE));
            $res = json_decode($res,true);
            $isok =  $isok && $this->returnOk($res);

//            dump($res);exit;

            if($isok){

                DB::connection('SUPPLYCHAIN')->transaction(function()use($res,$user_id){
                    $customer = CustomerModel::where("user_id",$user_id)->first();
                    //回写客户在erp关联的信息
                    $bk = $this->erpServices->updateCustomerInfo($customer->company_id,[
                        'erp_cuntomer_id'=>$res['CUSTOMERID'],//客户id
                        'customer_code'=>$res['CUSTOMERNO'],//客户编码
                        'erp_client_id'=>$res['ID'],//委托方id
                        'erp_client_code'=>$res['NUMBER'],//委托方编码
                    ]);
                    if($bk === false) throw new \Exception('同步客户资料失败');

                    //回写收货地址信息
                    foreach($res['RECEIVING'] as $key=>$val){
                        $bk = $this->erpServices->updateCustomerDelivery($val);
                        if($bk === false) throw new \Exception('同步客户资料失败-同步收货地址失败');
                    }

                    //回写业务联系人
                    foreach($res['BUSINESS'] as $key=>$val){
                        $bk = $this->erpServices->updateCustomerContact($val);
                        if($bk === false) throw new \Exception('同步客户资料失败-同步');
                    }

                });
                $this->insertPushLog([
                    'type_id'=>$user_id,
                    'type'=>1,
                    'params'=>$data ? json_encode($data,JSON_UNESCAPED_UNICODE) : '',
                    'erp_return'=>isset($returstr) ? $returstr : '',
                    'status'=>1,
                ]);
                return $this->apiReturn('0','ok');
            }else{
                \Think\Log::write($res['4444'],'WARN');
                $this->insertPushLog([
                    'type_id'=>$user_id,
                    'type'=>1,
                    'params'=>$data ? json_encode($data,JSON_UNESCAPED_UNICODE) : '',
                    'erp_return'=>isset($returstr) ? $returstr : '',
                    'status'=>0,
                ]);
                return $this->apiReturn('30000',$res['4444']);
            }


        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            $this->insertPushLog([
                'type_id'=>$user_id,
                'type'=>1,
                'params'=>$data ? json_encode($data,JSON_UNESCAPED_UNICODE) : '',
                'erp_return'=>isset($returstr) ? $returstr : $e->getMessage(),
                'status'=>0,
            ]);
            return $this->apiReturn('30000','同步客户资料失败');
        }

    }


    /**
     * 供应商数据同步
     */
    public function syncSupplier(){
        try {
            $user_id = I("user_id",0,'intval');
            $supplier_id = I("supplier_id",0,'intval');
            $data = [];
            $data = $this->erpServices->pushSupply($user_id,$supplier_id);

            //增加对公司性质和海内海外的数据录入 20220809
            $otherData = I('post.');
            $data['region'] = $otherData['region'];
            $data['supplierKind'] = $otherData['supplier_kind'];

            \Think\Log::write("供应商数据同步",'WARN');
            \Think\Log::write(json_encode($data),'WARN');

            $res = $this->erp->createSupplier(preg_replace('/\xc2\xa0/', '', json_encode($data)));
            $result = json_decode($res,true);
            \Think\Log::write($res,'WARN');

            if(isset($result['0000']) && isset($result['NUMBER']) && isset($result['ID'])){
                //成功回写数据
                DB::connection('SUPPLYCHAIN')->transaction(function()use($result,$user_id,$supplier_id){
                    $bk = $this->erpServices->syncSupplyErp([
                        'user_id'=>$user_id,
                        'supplier_id'=>$supplier_id,
                        'ID'=>$result['ID'],
                        'NUMBER'=>$result['NUMBER'],
                        'status'=>1
                    ]);
                    if($bk === false) throw new ErpException('同步供应商资料失败');
                   foreach($result['BANKS'] as $k=>$v){
                       $v['user_id'] = $user_id;
                       $bk = $this->erpServices->syncSupplyBankErp($v);
                       if($bk === false) throw new ErpException('同步供应商银行资料失败失败');
                   }
                });


                return $this->apiReturn('0','ok');
            }
            if (isset($result['4444'])){
                $errMsg = $result['4444'];
            }else{
                $errMsg = $res;
            }
            return $this->apiReturn('30001','同步供应商资料失败:'.$errMsg);
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('30001',$e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');

            return $this->apiReturn('30001','同步供应商资料失败:'.$e->getMessage());
        }
    }



    /**
     * 供应商数据同步
     */
    public function syncSupplierBank(){
        try {
            $supplier_id = I("supplier_bank_id",0,'intval');
            $supplierBankInfo = SupplierBankModel::where('supplier_bank_id',$supplier_id)->first()->toArray();

            \Think\Log::write("供应商银行数据同步",'WARN');
            \Think\Log::write(json_encode($supplierBankInfo),'WARN');

            $data['PTID'] = $supplierBankInfo['supplier_id'];
            $data['BANKS'][0]['PTENTRYID'] = strval($supplierBankInfo['supplier_bank_id']);
            $data['BANKS'][0]['BANK'] = $supplierBankInfo['bank_name'] ? $supplierBankInfo['bank_name'] : '';
            $data['BANKS'][0]['BANKADDRESSS'] = $supplierBankInfo['bank_address'] ?  $supplierBankInfo['bank_address'] : '';
            $data['BANKS'][0]['SWIFCODE'] = $supplierBankInfo['swift_code'] ? $supplierBankInfo['swift_code'] : '';
            $data['BANKS'][0]['DESTINA'] = $supplierBankInfo['area'] == 2 ? '香港境外' : '香港境内';
            $data['BANKS'][0]['COUNTRY'] = $supplierBankInfo['recipient_country'] ? $supplierBankInfo['recipient_country'] : '';
            $data['BANKS'][0]['BANKACCOUNT'] = $supplierBankInfo['bank_account'] ? $supplierBankInfo['bank_account'] :'';
            $data['BANKS'][0]['RECEIVERNAME'] = $supplierBankInfo['bank_user'];
            if (!empty($supplierBankInfo['erp_entry_id'])){
                $data['BANKS'][0]['ENTRYID'] = $supplierBankInfo['erp_entry_id'];
            }


            \Think\Log::write(json_encode($data),'WARN');

            $res = $this->erp->updateSupplierBank(preg_replace('/\xc2\xa0/', '', json_encode($data)));
            $result = json_decode($res,true);
            \Think\Log::write($res,'WARN');
            if(isset($result['BANKS']) && isset($result['BANKS'][0]['ENTRYID'])){
                return $this->apiReturn('0',$result['BANKS'][0]['ENTRYID']);
            }
            return $this->apiReturn('30001','同步供应商资料失败');
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('30001',$e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');

            return $this->apiReturn('30001','同步供应商资料失败');
        }
    }

    protected function addLog($type_id,$type,$params,$erp_return,$status){
        try{
            $this->insertPushLog([
                'type_id'=>$type_id,
                'type'=>$type,
                'params'=>$params,
                'erp_return'=>$erp_return,
                'status'=>$status,
            ]);
        }catch(\Exception $e){

        }
    }


    /*
     * 推送产品数据到erp
     */
    public function syncGoodsToErp(){
        try{
            set_time_limit(500);
            \Think\Log::write("-------同步物料数据到erp",'WARN');
            \Think\Log::write(json_encode(I('post.')),'WARN');
            $params = I('post.','');
            \Think\Log::write(json_encode($params),'WARN');
            $bk = $this->erpServices->pushGoodsInfoToCY($params);
            if(!$bk) throw new ErpException('同步物料数据失败');
            return $this->apiReturn('0','ok');
        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('30001',$e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('30001','同步物料数据失败');
        }
    }


    /**
     * 同步委托单数据到erp
     * 订单数据
     */
    public function syncOrderInfo(){
        try{
            set_time_limit(500);
//            \Think\Log::write("-------同步委托单数据到erp",'WARN');
//            \Think\Log::write(json_encode(I('post.')),'WARN');
            $params = I('post.','');
            \Think\Log::write(json_encode($params),'WARN');
            $order = OrderModel::findOrFail(intval($params['order_id']));

            if($order->id_edit_order == 0){
                //正常订单
                $this->syncNewOrder($params,$order);
            }else{
                //改单订单
                $this->syncChangeOrder($params);
            }

        }catch(ErpException $e){
            return $this->apiReturn('30001','同步委托单数据失败');
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            DingNotify::pushOrderFailNotify($params['order_id'],$e->getMessage());
            return $this->apiReturn('30001','同步委托单数据失败');
        }

    }


    /**
     * 同步委托单到erp
     */
    public function syncNewOrder($params,$order){
        try{
            $data = $this->erpServices->pushOrderInfoToErp($params);

            \Think\Log::write(json_encode($data),'WARN');
            if ($order->is_hk_order){
                $res = $this->orderErp->createHKEntrustBill(json_encode($data,JSON_UNESCAPED_UNICODE));
            }else{
                $res = $this->orderErp->createEntrustBill(json_encode($data,JSON_UNESCAPED_UNICODE));
            }
            \Think\Log::write($res,'WARN');

            $result = json_decode($res,true);

            if(isset($result['4444'])){
                throw new ErpException($result['4444']);
            }

            if(isset($result['0000'])){

                DB::connection('SUPPLYCHAIN')->transaction(function()use($data,$result){
                    //回写订单的 委托单编码 委托单id
                    $this->erpServices->syncOrderInfoLocal($data['PTORDERID'],$result);
                    //回写订单商品数据
                    $this->erpServices->syncOrderGoodsLocal($data['PTORDERID'],$result);
                });

            }else{
                throw new ErpException($res);
            }


            $this->insertPushLog([
                'type_id'=>$params['order_id'],
                'type'=>3,
                'params'=>json_encode($data,JSON_UNESCAPED_UNICODE),
                'erp_return'=>$res,
                'status'=>1,
            ]);

            return $this->apiReturn('0','成功');

        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            $this->insertPushLog([
                'type_id'=>$params['order_id'],
                'type'=>3,
                'params'=>$data ? json_encode($data,JSON_UNESCAPED_UNICODE) : '',
                'erp_return'=>isset($res) ? $res : $e->getMessage(),
                'status'=>0,
            ]);
            post_curl(CRM_DOMAIN.'/api/sendSyncOrderFaile', ['msg'=>$e->getMessage(),'order_id'=>$params['order_id']], array('api-key: crm a1b2c3d4e5f6g7h8i9jk'));
            return $this->apiReturn('30001',$e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            $this->insertPushLog([
                'type_id'=>$params['order_id'],
                'type'=>3,
                'params'=>json_encode($data,JSON_UNESCAPED_UNICODE),
                'erp_return'=>isset($res) ? $res : $e->getMessage(),
                'status'=>0,
            ]);
            return $this->apiReturn('30001','同步委托单数据失败');
        }
    }


    /*
     * 改单推送
     */
    public function syncChangeOrder($params){
        try{
            $data = $this->erpServices->pushChangeOrderToErp($params);
//            dump($data);exit;
            $res = $this->orderErp->modifyEntrustBill(json_encode($data,JSON_UNESCAPED_UNICODE));
            \Think\Log::write($res,'WARN');
//            dump($res);
            $result = json_decode($res,true);
//            dump($data);
//            dump($result);exit;

            $ChangeOrder = OrderModel::where("order_id",$params['order_id'])->firstOrFail();
            $MasterOrder = OrderModel::where("order_id",$ChangeOrder->edit_order_id)->firstOrFail();

            //审核不通过
            if(isset($result['4444'])){
                //转人工审核
                $ChangeOrder->status = OrderModel::$AuditINGStatus;//子单状态审核中
                $ChangeOrder->is_push = 1;//改单记录已推送
                $ChangeOrder->save();
                throw new ErpException($result['4444']);
            }else if(isset($result['0000'])){
                //审核通过
                DB::connection('SUPPLYCHAIN')->transaction(function()use($ChangeOrder,$MasterOrder,$result){
                    //更新商品分录id
                    $this->erpServices->syncChangeOrderGoodsLocal($ChangeOrder,$result);

                    //改单状态为审核通过
                    $ChangeOrder->status = OrderModel::$PassStatus;
                    if(!$ChangeOrder->save()) throw new ErpException("同步改单数据失败");
                    //改单推送成功 判断主订单状态是否为已审核通过
                    if($MasterOrder->status != OrderModel::$PassStatus){
                        //同步改单数据到主订单 修改主订单状态为审核中 主订单又可以再次被修改了
                        $MasterOrder->status = OrderModel::$AuditINGStatus;
                        if(!$MasterOrder->save()) throw new ErpException("同步改单数据失败");
                        //同步改单数据到主订单
                        (new \Supplychain\Service\OrderService)->syncChangeOrderToMasterOrder($MasterOrder,$ChangeOrder);
                    }
                });
            }else{
                throw new ErpException("erp返回异常");
            }

            return $this->apiReturn('0','成功');

        }catch(ErpException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('30001',$e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('30001','同步改单数据失败');
        }
    }

    /**
     * 记录推送日志
     */
    public function insertPushLog($logs){
        PushLogModel::create([
            'type_id'=>$logs['type_id'],
            'type'=>$logs['type'],
            'params'=>$logs['params'],
            'erp_return'=>$logs['erp_return'],
            'status'=>$logs['status'],
        ]);
    }

    /**
     * 同步物料数据 test
     * 物料数据通过hopose请求 关税系统 诚意做的
     *
     */
    public function testErpPush(){
//        (new \Supplychain\Service\OrderService)->EorderCNewOrder(134);
//        exit;
        $input=file_get_contents("php://input");
        $rpcfunc  = I('get.rpcfunc');

//        $data = (new \Supplychain\ErpServices\ErpService)->{$rpcfunc}(json_decode($input,true));
//        dump($data);
//        exit;


        vendor('Hprose.HproseHttpClient');
        $client = new \HproseHttpClient("http://api.liexin.com/Supplychain/Rpc/index");
        $bk = $client->{$rpcfunc}($input);
        dump($bk);
//        $client->close();
    }




    /**
     * 测试推送数据到队列
     *
     */
    public function pushMq(){
        $RbmqModel = D('Common/Rbmq');
        $push = array(
            'job' => 'gongyinglian.add.order',
            'data' => array('order_id' => 151)
        );
        $a= $RbmqModel->exchange(C("EXCHANGE_NAME_PCB"))->queue(C("QUEUE_GONGYINGLIAN_ORDER"))
            ->exchangeBind(C('EXCHANGE_NAME_PCB'), C('QUEUE_GONGYINGLIAN_ORDER'));
//        dump($a);
        $bk = $a->push($push, C("QUEUE_GONGYINGLIAN_ORDER"));
        dump($bk);
    }

    /**
     * 订单审核
     * erp推送订单数据到平台
     */
    public function pullErpOrderAudit(){

    }

    // 查找ERP商务
    public function getBusinessFromErp()
    {
        try {
            $data['PRINCIPAL'] = I('com_name', '', 'trim');

            \Think\Log::write(json_encode($data),'WARN');
            $obj = $this->isTest ?$this->orderErp:new HproseRepository();
            $res = $obj->getPrincipalMainFollower(json_encode($data));

            $arr = json_decode($res, true);

            if (empty($arr) || !isset($arr['0000'])) {
                $log = __METHOD__."\t".$res;
                \Think\Log::write($log,'INFO');
                return $this->apiReturn(30002, '获取失败，原因：'.$arr['4444']);
            }

            return $this->apiReturn(0, '获取成功', $arr);
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn(30003, '获取ERP商务数据失败');
        }       
    }

    /**
     * 获取erp订单号
     */
    public function getErpOrderSn($erp_client_id){
        try {
            $arr = CommonLogic::orderDiffGetErpOrderSn($erp_client_id);
            $res = $this->orderErp->getBillNumber(json_encode($arr));
            $res = json_decode($res, true);
            if (isset($res['0000'])) {
               return isset($res['BILLNUMBER']) ? $res['BILLNUMBER'] : false;
            }
            return false;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }


    /**
     * 获取本地汇率
     */
    public function getPayLocalGood(){
        try {
            $res = $this->orderErp->getErpExchangeRate();
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }


    /**
     * 获取付汇申请列表
     */
    public function getPayLoaclGoods($post){
        try {
            if(isset($post['is_hk_order']) && $post['is_hk_order']){
                $post['bizType'] = '其他';
            }else{
                $post['bizType'] = '执行采购';
            }
            \Think\Log::write(json_encode($post),'WARN');
            $obj = $this->isTest ?$this->orderErpTest:new HproseRepository();
            $res = $obj->getPayLoaclGood(json_encode($post));
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    /**
     * 新增付汇申请单接口
     */
    public function createPayLocalGood($createData){
        try {
            $res = $this->orderErp->createPayLocalGood($createData);
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    /**
     * 修改付汇申请单接口
     */
    public function updatePayLocalGood($createData){
        try {
            $res = $this->orderErp->updatePayLocalGood($createData);
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    //检测入仓号是否可以下单
    public function  checkOrderNo($createData)
    {
        try {
            $res = $this->erp->checkOrderNo($createData);
            $res = json_decode($res, true);
            if (!isset($res['0000'])){
                throw new Exception(\GuzzleHttp\json_encode($res).'|||'.$createData);
            }
            return $res;
        } catch (Exception $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }



    //待开票清单
    public function getNoInvoiceList($data){
        try {
            \Think\Log::write($data,'WARN');
            $obj = $this->isTest ?$this->erp:new HproseRepository();
            $res = $obj->getNoInvoiceList($data);
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }



    //付款记录
    public function getPaymentList($data){
        try {
            \Think\Log::write($data,'WARN');
            $obj = $this->isTest ?$this->erp:new HproseRepository();
            $res = $obj->getPaymentList($data);
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    //订单列表
    public function getOrderList($data){
        try {
            \Think\Log::write($data,'WARN');
            $obj = $this->isTest ?$this->erp:new HproseRepository();
            $res = $obj->getOrderList($data);
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    //订单详情
    public function getOrderDetails($data){
        try {
            \Think\Log::write($data,'WARN');
            $obj = $this->isTest ?$this->erp:new HproseRepository();
            $res = $obj->getOrderDetails($data);
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    //付款详情
    public function getPaymentDetails($data){
        try {
            $res = $this->erp->getPaymentDetails($data);
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    //添加附件
    public function addOrderAttachment($data){
        try {
            $res = $this->erp->addOrderAttachment($data);
            $res = json_decode($res, true);
            if (!isset($res['0000'])){
                throw new Exception(\GuzzleHttp\json_encode($res));
            }
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }


    //获取付款通知书数据
    public function getPaymentNoticePrintData($data){
        try {
            \Think\Log::write($data,'WARN');
            $obj = $this->isTest ?$this->erp:new HproseRepository();
            $res = $obj->getPaymentNoticePrintData($data);
            $res = json_decode($res, true);
            if (empty($res['Details'])){
                throw new Exception(\GuzzleHttp\json_encode($res));
            }
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }



    //校验是否能改单
    public function checkChangeBill($data){
        try {
            $res = $this->erp->checkChangeBill($data);
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    //待付汇列表
    public function getForPaymentList($data){
        try {
            \Think\Log::write($data,'WARN');
            $obj = $this->isTest ?$this->erp:new HproseRepository();
            $res = $obj->getForPaymentList($data);
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    //同步物流信息接口
    public function synLogisticsInfo($data){
        try {
            $res = $this->erp->synLogisticsInfo($data);
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    //香港入仓接口
    public function getHKInWoreHouse($data){
        try {
            \Think\Log::write($data,'WARN');
            $obj = $this->isTest ?$this->erp:new HproseRepository();
            $res = $obj->getHKInWoreHouse($data);
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    //取消付汇 {"number":"FK2021071636070"}
    public function cancelPayLocalGood($data){
        $res = $this->erp->cancelPayLocalGood($data);
        $res = json_decode($res, true);
        if (!isset($res['0000'])){
            throw new Exception(\GuzzleHttp\json_encode($res));
        }
    }


    /*
     * 后台手动推送审核，自动生成付汇
     */
    public function supplierAdminAutoPaymentApply()
    {
        $orderId = I("order_id",0,'intval');
        $order = OrderModel::where('order_id',$orderId)->where('is_apply_payment',0)->where('user_id','!=',0)->first();

        if (empty($order)){
            return $this->apiReturn('30001','订单查询失败');
        }

        //供应链传来的用户ID都是0
        try{
            PaymentApplyModel::autoInsertData($order);
        }catch (Exception $exception){
            return $this->apiReturn('30001',$exception->getMessage());
        }
        return $this->apiReturn('0','ok');
    }

    /*
     * 后台手动推送审核，自动生成付汇
     */
    public function fiveAutoPaymentApply()
    {
        set_time_limit(350);
        sleep(300);
        $orderId = I("order_id",0,'intval');
        $order = OrderModel::where('order_id',$orderId)->where('is_apply_payment',0)->where('user_id','!=',0)->first();

        if (empty($order)){
            \Think\Log::write("订单查询失败",'WARN');
            return $this->apiReturn('30001','订单查询失败');
        }

        //供应链传来的用户ID都是0
        try{
            PaymentApplyModel::autoInsertData($order);
        }catch (Exception $exception){
            DingNotify::autoCreatePaymentApplyFailNotify($order->erp_order_sn,$exception->getMessage());
            \Think\Log::write($exception->getMessage(),'WARN');
            return $this->apiReturn('30001',$exception->getMessage());
        }
        return $this->apiReturn('0','ok');
    }

    //sendAskGuestMsg
    //{"number":"B99987","type":"问题类型","time":"2021-11-22 15:50:50","person":"周红丹"}
    //给商务发送消息
    public function sendAskGuestMsg()
    {
        $data = I('post.','');
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->erp->sendAskGuestMsg(json_encode($data));
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    //synReceiptAttach 同步作业
    //{"number":"B99987","type":"问题类型","time":"2021-11-22 15:50:50","person":"周红丹"}
    public function synReceiptAttach($data)
    {
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->erp->synReceiptAttach(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }



    //getInWarehouseDetail 同步作业
    //{"number":"B95871"}
    public function getInWarehouseDetail()
    {
        $data = I('post.','');
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErpTest->getInWarehouseDetail(\GuzzleHttp\json_encode($data));
            return $this->apiReturn('0','ok',\GuzzleHttp\json_decode($res));
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }


    //getGoodsRegisterData 获取收货登记接口
    //{"number":"", "startDate":"2022-02-24", "endDate":"2022-02-24"}
    public function getGoodsRegisterData($data)
    {
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->getGoodsRegisterData(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['4444']);
        }
    }

    //getGoodsRegisterData 获取收货登记接口
    //{"number":"", "startDate":"2022-02-24", "endDate":"2022-02-24"}
    public function getGoodsRegisterDataAdmin()
    {
        try {
            $data = I('post.','');
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->getEntrustBillRelationsInfo (\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new Exception($res['4444']);
            }
            return $this->apiReturn('0','ok',$res);
        } catch (Exception $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('-1',$e->getMessage());
        }
    }


    //新增收货登记 {"entrustNo":"Bxxxxx", "registerTime":"2022-04-12 15:23:50", "recType":"物流", "company":"公司", "logisticsNo":"SF9527", "qty":"2", "unit":"件", "imageUrl":"www://xxxxxx.png", "remark":"梁大郎牛逼" }
    public function createGoodsRegisterData($data)
    {

        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->createGoodsRegisterData(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['4444']);
        }
    }

    public function createGoodsRegisterDataAdmin()
    {
        $data = I('post.','');
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->createGoodsRegisterData(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');
            return $this->apiReturn('0','ok',\GuzzleHttp\json_decode($res));

        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    public function removeGoodsRegisterData()
    {
        $data = I('post.','');
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->removeGoodsRegisterData(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');
            return $this->apiReturn('0','ok',\GuzzleHttp\json_decode($res));

        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    //getGoodsArrangeData 获取理货登记接口
    //{"number":"", "startDate":"2022-02-24", "endDate":"2022-02-24"}
    public function getGoodsArrangeData($data=[])
    {
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->getGoodsArrangeData(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['4444']);
        }
    }

    //getGoodsArrangeData 获取理货登记接口
    //{"number":"", "startDate":"2022-02-24", "endDate":"2022-02-24"}
    public function getGoodsArrangeDataSupply($data=[])
    {
        try {
            $data = I('post.','');
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->getGoodsArrangeData(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $this->apiReturn('0','ok',$res);
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('-1',$e->getMessage());
        }
    }


    //updateGoodsArrangeData 更新理货数据
    //{"id":"采购订单ID”", "num":件数, "entrys":[{"entryID":"采购订单明细ID", "netWeight":净重}]}
    public function updateGoodsArrangeData($data)
    {
        try {
            var_dump(\GuzzleHttp\json_encode($data));die;
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->updateGoodsArrangeData(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['4444']);
        }
    }

    public function getCustomsBoxesNum()
    {
        $data = I('post.','');
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->getCustomsBoxesNum(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $this->apiReturn('0','ok',$res);
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('-1',$e->getMessage());
        }
    }

    //{"status":1, "entrustNos":"B000001,B00002-1"}
    public function pushCustoms()
    {
        $data = I('post.','');
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->pushCustoms(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $this->apiReturn('0','ok',$res);
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('-1',$e->getMessage());
        }
    }




    //{"entrustNo":"B143323-1"}
    public function getOrderCheckDetailsApi($data)
    {
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->getOrderCheckDetails(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['4444']);
        }
    }

    //{"entrustNo":"B143323-1"}
    public function getOrderCheckDetails()
    {
        $data = I('post.','');

        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $requestData = ['entrustNo'=>$data['entrustNo']];
            \Think\Log::write(\GuzzleHttp\json_encode($requestData),'WARN');
            $res = $this->orderErp->getOrderCheckDetails(\GuzzleHttp\json_encode($requestData));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $this->apiReturn('0','ok',$res);
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('-1',$e->getMessage());
        }
    }


    public function getBatchOrderCheckDetails()
    {
        $data = I('post.','');

        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $requestData = ['entrustNo'=>$data['entrustNo']];
            \Think\Log::write(\GuzzleHttp\json_encode($requestData),'WARN');
            $res = $this->orderErp->getBatchOrderCheckDetails(\GuzzleHttp\json_encode($requestData));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $this->apiReturn('0','ok',$res);
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('-1',$e->getMessage());
        }
    }



    //产地税确认
    //orderEntryID 采购订单明显ID
    //originCountry 产地
    //参数：{"orderEntryID":"XdOdOyInR8ahSgw7z6n+wISKC2A=", "originCountry":"美国"}
    public function confirmOriginTaxAdmin()
    {
        $data = I('post.','');

        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $requestData = ['orderEntryID'=>$data['orderEntryID'],'originCountry'=>$data['originCountry']];
            \Think\Log::write(\GuzzleHttp\json_encode($requestData),'WARN');
            $res = $this->orderErp->confirmOriginTax(\GuzzleHttp\json_encode($requestData));
            \Think\Log::write($res,'WARN');
            $res = \GuzzleHttp\json_decode($res,true);
            if (!isset($res['code']) || intval($res['code']) !== 0){
                throw new ErpException($res['msg']);
            }
            return $this->apiReturn('0','ok',$res);
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('-1',$e->getMessage());
        }
    }


    //产地税取消
    //参数：["B51445","B51612"]
    public function cancelTallyAdmin()
    {
        $data = I('post.','');

        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->cancelTally(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');
            $res = \GuzzleHttp\json_decode($res,true);
            if (!isset($res['code']) || intval($res['code']) !== 0){
                throw new ErpException($res['msg']);
            }
            return $this->apiReturn('0','ok',$res);
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('-1',$e->getMessage());
        }
    }

    public function synScanLoadStatus()
    {
        $data = I('post.','');
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->synScanLoadStatus(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');
            $res = \GuzzleHttp\json_decode($res,true);
            if (!isset($res['code']) || intval($res['code']) !== 0){
                throw new ErpException($res['msg']);
            }
            return $this->apiReturn('0','ok',$res);
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('-1',$e->getMessage());
        }
    }

    public function getErpFepData()
    {
        $data = I('post.','');
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->getErpFepData(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');
            $res = \GuzzleHttp\json_decode($res,true);
            if (!isset($res['code']) || intval($res['code']) !== 0){
                throw new ErpException($res['msg']);
            }
            return $this->apiReturn('0','ok',$res['data']);
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('-1',$e->getMessage());
        }
    }

    //获取待入库数据
    //[{"entrustNo":"B123456","customerName":"梁大郎集团","productName":"abandon","carOrder":"XX一车","boxesNum":4,"customsDate":"2022-07-30","inWoreStatus":"未入库","outWoreStatus":"未出库"}]
    public function getInStorage($data)
    {
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->getInStorage(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['4444']);
        }
    }

    //获取件数 {"entrustNo":"B123456"}
    public function getBoxesNumByNo($data)
    {
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->getBoxesNumByNo(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['4444']);
        }
    }

    //获取件数 更新 已入库、已出
    //参数：{"type":"入库", "entrusNos":["B123456", "B123457", "B123458"]}
    //{"type":"出库", "entrusNos":["B123456", "B123457", "B123458"]}
    public function updateStorageStatus($data)
    {
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->updateStorageStatus(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['4444']);
        }
    }


    //获取业绩数据
    //参数：{"type":"PerformanceData", "bizDate":"2022-08-22"}
    public function getSCMDataByType()
    {
        try {
            $data = I('post.');
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->getSCMDataByType(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            return $this->apiReturn('30001',$res['4444'],$res);
        }
        return $this->apiReturn('0','操作成功',$res);
    }

    //
    //接口 checkPIUpload
    //参数 {"entrustNo":"B51526"}
    //返回 {"msg":"成功","code":0,"data":{"entrustNo":"B51526","isUpload":true}}
    public function checkPiUpload()
    {
        try {
            $data = I('post.');
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->checkPiUpload(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');

            $res = json_decode($res, true);

            if (!isset($res['code']) || intval($res['code']) !== 0){
                throw new ErpException($res['msg']);
            }

        } catch (ErpException $e) {
            return $this->apiReturn('30001',$e->getMessage(),$res);
        }
        return $this->apiReturn('0','操作成功',$res);
    }



    public function createCdityBill()
    {
        try {
            $data = I('post.');
            $data['BRAND'] = urldecode(str_replace('&amp;', '&', $data['BRAND']));
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->erp->createCdityBill(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');

            $res = json_decode($res, true);

            if (!isset($res['code']) || intval($res['code']) !== 0){
                throw new ErpException($res['msg']);
            }

        } catch (ErpException $e) {
            return $this->apiReturn('30001',$e->getMessage(),$res);
        }
        return $this->apiReturn('0','操作成功',$res);
    }




    public function updateTallyBrand()
    {
        try {
            $data = I('post.');
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->updateTallyBrand(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');

            $res = json_decode($res, true);

            if (!isset($res['code']) || intval($res['code']) !== 0){
                throw new ErpException($res['msg']);
            }

        } catch (ErpException $e) {
            return $this->apiReturn('30001',$e->getMessage(),$res);
        }
        return $this->apiReturn('0','操作成功',$res);
    }


    public function updateTallyModel()
    {
        try {
            $data = I('post.');
            $data[0]['brand'] = urldecode(str_replace('&amp;', '&', $data[0]['brand']));
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->updateTallyModel(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');

            $res = json_decode($res, true);

            if (!isset($res['code']) || intval($res['code']) !== 0){
                throw new ErpException($res['msg']);
            }

        } catch (ErpException $e) {
            return $this->apiReturn('30001',$e->getMessage(),$res);
        }
        return $this->apiReturn('0','操作成功',$res);
    }

    public function synKJTallyData()
    {
        try {
            $data = I('post.');
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->synKJTallyData(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');

            $res = json_decode($res, true);

            if (!isset($res['code']) || intval($res['code']) !== 0){
                throw new ErpException($res['msg']);
            }

        } catch (ErpException $e) {
            return $this->apiReturn('30001',$e->getMessage(),$res);
        }
        return $this->apiReturn('0','操作成功',$res);
    }

    public function scmPTCallErp()
    {
        try {
            $data = I('post.');
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->scmPTCallErp(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');

            $res = json_decode($res, true);

            if (!isset($res['code']) || intval($res['code']) !== 0){
                throw new ErpException($res['msg']);
            }

        } catch (ErpException $e) {
            return $this->apiReturn('30001',$e->getMessage(),$res);
        }
        return $this->apiReturn('0','操作成功',$res);
    }


    public function synTallyDate()
    {
        try {
            $data = I('post.');
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->synTallyDate(\GuzzleHttp\json_encode($data));
            \Think\Log::write($res,'WARN');

            $res = json_decode($res, true);

            if (!isset($res['0000'])){
                throw new ErpException('请求金蝶接口失败'.$res['4444']);
            }

            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
        } catch (ErpException $e) {
            return $this->apiReturn('30001',$e->getMessage(),$res);
        }
        return $this->apiReturn('0','操作成功',$res);
    }



    //获取未来货登记
    //{"customsDateFrom":"2022-10-01", "customsDateTo":"2023-10-01"}
    public function getFutureCargoData($data)
    {
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $res = $this->orderErp->getFutureCargoData(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new ErpException($res['4444']);
            }
            return $res;
        } catch (ErpException $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['4444']);
        }
    }

}