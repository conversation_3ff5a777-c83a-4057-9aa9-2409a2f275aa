<?php
namespace Supplychain\Controller;

//use \Common\Controller\BaseController as Controller;
use Illuminate\Container\Container;
use Think\Controller;

class ErpBaseController extends Controller
{
    public $client =  null;
    public $container = null;
    public static $DB;
    public function _initialize()
    {
        $container = new Container();
        //普通绑定
//        $container->bind('\Pcb\Model\LARAVELDB',null);
        //单例绑定
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
            return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');

    }


    protected function apiLog($code, $msg, $log)
    {
        $disable_log = C('DISABLE_LOG');
        $param = I('request.');
        $cookie = cookie();
        $data = array(
            'param' => $param,
            'cookie' => $cookie,
            'log' => $log,
        );
        \Think\Log::record(sprintf('code=%s, msg=%s, data=%s;', $code, $msg, json_encode($data)));
    }

    /**
     * 统一格式返回
     * @param  integer $code   [description]
     * @param  string  $msg    [description]
     * @param  array   $extend [description]
     * @return [type]          [description]
     */
    protected function apiReturn($code = 0, $msg = '', $extend = array())
    {
        $data = array(
            'err_code' => $code,
            'err_msg' => $msg,
            'data' => $extend,
        );
        if ($code != 0) {
            $this->apiLog($code, $msg, $extend);
            if ($code > 0) {
                unset($data['data']);
            }
            $data['err_code'] = abs($data['err_code']);
            \Think\Log::save();
        }

        header('Content-type:application/json');
        echo json_encode($data);
        exit;
    }
}