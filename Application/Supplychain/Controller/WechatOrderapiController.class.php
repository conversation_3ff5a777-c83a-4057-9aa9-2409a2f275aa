<?php
/**
 * Created by 2021/10/28.
 * User: Joneq
 * Info: 订单小程序的api接口
 * Time: 下午4:37
 */

namespace Supplychain\Controller;
use Illuminate\Container\Container;
use Supplychain\Model\BaoGuanOrderListModel;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\Logic\CustomerLogic;
use Supplychain\Model\Logic\ExportLogic;
use Supplychain\Model\Logic\FinanceLogic;
use Supplychain\Model\Logic\OrderLogic;
use Supplychain\Model\Logic\WechatOrderLogic;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\SupplierModel;
use Supplychain\Repository\HproseRepository;
use Supplychain\Repository\OrderRepository;
use Supplychain\Repository\UserRepository;
use Think\Controller\HproseController;
use Think\Log;
use Supplychain\Model\Logic\AskCustomerWechatLogic;
class WechatOrderapiController extends BaseController
{


    public $client =  null;
    public $container = null;
    public static $DB;



    public function _initialize()
    {
        $this->setHeaders();
        $container = new Container();
        //单例绑定
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
            return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');

        if (!in_array(ACTION_NAME,['login','getopenid'])){
            $header = getallheaders();
            if (!isset($header['Authorization']) || empty($token =$header['Authorization'])){
                return $this->apiReturn(20045,'token不能为空','');
            }else{
                CommonLogic::$loginUid = WechatOrderLogic::passport_decrypt($token,'liexin');
                if (empty(intval(CommonLogic::$loginUid))){
                    return $this->apiReturn(20045,'token异常','');
                }
            }
        }
    }



    /**
     * 小程序端调用wx.login获取到code，把code发送给服务端，服务端去请求该接口
     */
    public function getOpenId(){
        try{
            $data = WechatOrderLogic::getParam();
            $code = $data['code'];

            $client = new \GuzzleHttp\Client();
            $response = $client->request('GET','https://api.weixin.qq.com/sns/jscode2session',[
                'query' => [
                    'appid' => WechatOrderLogic::$appid,
                    'secret' =>WechatOrderLogic::$secret,
                    'js_code' => $code,
                    'grant_type' => 'authorization_code',
                ]
            ]);

            $res = json_decode($response->getBody()->getContents(),true);


            if(!isset($res['unionid']) || empty($res['unionid'])) {
                throw new \Exception('获取微信返回数据失败');
            }

            $res['token'] = '';
            //如果绑定了，直接返回用户的token
            if (!empty($userId = CustomerModel::where('wechat_unique_id',$res['unionid'])->value('user_id'))){
                $res['token'] =  WechatOrderLogic::passport_encrypt($userId,'liexin');
            }

        }catch (\Exception$e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }

        return $this->apiReturn(0,'操作成功', $res);
    }




    public function login()
    {
        try{
            $data = WechatOrderLogic::getParam();
            $returnData = WechatOrderLogic::login($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }



    //获取会员中心
    public function userInfo()
    {
        try{
            $returnData = AskCustomerWechatLogic::getUserInfo(['user_id'=>CommonLogic::$loginUid ]);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //订单列表
    public function orderList()
    {
        try{
            $data = I('get.');
            if (empty($returnData = OrderLogic::getOrderList(array_merge($data,['user_id'=>CommonLogic::getLoginUid()])))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //获取订单供应商
    public function getUserSuppliers()
    {
        try{
            $supplier_name = I("supplier_name",'','trim');
            $bk = (new OrderRepository())->getuser_suppliers($supplier_name);
            if(!$bk) throw new \Exception('没有更多数据');
            return $this->apiReturn("0", "成功",$bk);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
    }

    //订单详情
    public function orderDetail()
    {
        try{
            $data = WechatOrderLogic::getParam();

            if (empty($orderId = $data['order_id'])){
                throw new \Exception('订单id不能为空');
            }

            $orderInfo = OrderModel::where("company_id",CustomerLogic::getUserCompanyId(CommonLogic::$loginUid))->where("order_id",$orderId)->first();
            if (empty($orderInfo)){
                throw new \Exception("该订单不存在");
            }


            if (!empty($orderInfo->supplier_id)){
                $returnData['basic_info'] = (new OrderRepository())->getOrderBasicInfo($orderId);
                $returnData['inland_delivery'] = (new OrderRepository())->get_inland_delivery($orderId);
                $returnData['order_hongkong'] = (new OrderRepository())->get_order_hongkong($orderId);
                $returnData['order_price'] = (new OrderRepository())->getOrderPrice($orderId);
                $returnData['order_goods'] = (new OrderRepository())->getOrderGoodsList();
                $returnData['order_status'] = OrderLogic::getOrderStatus($orderId);
                $returnData['order_tracking'] = (new OrderRepository())->getOrderTracking();
                if (empty($baoguanTime = BaoGuanOrderListModel::where('order_id',$orderId)->value('create_time'))){
                    $returnData['order_status']['baguan_date'] = '';
                }else{
                    $returnData['order_status']['baguan_date'] = date('Y-m-d H:i:s',strtotime($baoguanTime));
                }
            }else{
                $returnData['basic_info'] = $orderInfo;
                $returnData['order_goods'] = (new OrderRepository())->getOrderGoodsList();
            }

        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //等待付汇列表
    public function waitApplyPaymentList()
    {
        try{
            $post = I('get.');

            if (empty($companyId = CustomerModel::where('user_id',CommonLogic::$loginUid)->value('company_id'))){
                throw new \Exception('没有绑定的公司');
            }
            if (empty($companyName = CompanyModel::where('company_id',$companyId)->value('company_full_name'))){
                throw new \Exception('公司名称不完整');
            }
            $post['settleMentUnit'] = $companyName;

            if (!isset($post['entrustNumber']) || empty($post['entrustNumber'])){
                $post['entrustNumber'] = '';
            }
            if (!isset($post['dateTo']) || empty($post['dateTo'])){
                $post['dateTo'] = date('Y-m-d',time());
            }
            if (!isset($post['dateFrom']) || empty($post['dateFrom'])){
                $post['dateFrom'] = date('Y-m-d',0);
            }

            //获取汇率数组
            $Redis = new \Redis();
            $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
            $Redis->auth(C('REDIS_PASSWORD'));
            $exchangeRate = $Redis->get('ErpExchangeRate');
            $exchangeRate = json_decode($exchangeRate,true);
            $rateArr = [];
            foreach($exchangeRate as $value){
                foreach ($value as $k=>$v){
                    $rateArr[$k] = $v;
                }
            }


            $creditData = (new \Supplychain\Controller\ErpPushController)->getPayLoaclGoods($post);
            if(!$creditData) throw new \Exception("暂无数据");
            foreach ($creditData as $key=>$val){
                $supplierId = OrderModel::where('erp_order_sn',$val['entrustBillNumber'])->value('supplier_id');
                $supplyInfo = SupplierModel::where('supplier_id',$supplierId)->select('supplier_id','user_id')->first();
                if (empty($supplyInfo)){
                    $creditData[$key]['erpClientCode'] = "";
                    $creditData[$key]['supplierId'] = "";
                }else{
                    $creditData[$key]['erpClientCode'] = CustomerModel::where('user_id',$supplyInfo->user_id)->value('erp_client_code');
                    $creditData[$key]['supplierId'] = $supplyInfo->supplier_id;
                }
                $creditData[$key]['exchangeRate'] = isset($rateArr[$creditData[$key]['settlementCurrency']])?$rateArr[$creditData[$key]['settlementCurrency']]:1;
            }

            if (isset($post['export']) && ($post['export'] == 1)){
                ExportLogic::exportExcelNormal('getForPaymentList',$creditData,'待付汇列表');
                die;
            }else{
                return $this->apiReturn(0,'操作成功', $creditData);
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $creditData);
    }


    //费用明细列表
    public function applyPaymentList()
    {
        try{
            $get = I('get.');
            $get['companyName'] = CommonLogic::getCompnayName();
            if (empty($returnData = (new FinanceLogic())->getOrderList($get))){
                throw new \Exception('暂无数据');
            }
            if (isset($get['export']) && ($get['export'] == 1)){
                ExportLogic::exportExcelNormal('getOrderList',$returnData,'费用明细');
                die;
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //费用详情
    public function applyPaymentDetails()
    {
        try{
            $get = I('get.');
            if (empty($returnData = (new FinanceLogic())->getOrderDetails($get))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //付款通知书
    public function getPaymentNoticePrintData()
    {
        try{
            $get = I('get.');
            $jsonData['number'] = $get['number'];
            if (empty(OrderModel::where('company_id',(new UserRepository())->getCompanyId(CommonLogic::$loginUid))->where('erp_order_sn',$jsonData['number'])->value('order_id'))){
                throw new \Exception('暂无数据');
            }
            if (empty($returnData = (new FinanceLogic())->getPaymentNoticePrintDataWechatPdf($get))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //付款列表
    public function getPaymentList()
    {
        try{
            $get = I('get.');
            $get['companyName'] = CommonLogic::getCompnayName();
            if (empty($returnData = (new FinanceLogic())->getPaymentList($get))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //付款详情
    public function getPaymentDetails()
    {
        try{
            $get = I('get.');
            if (empty($returnData = (new FinanceLogic())->getPaymentDetails($get))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }



    //获取订单物流信息
    public function getOrderlogistics()
    {
        try{
            $get = I('get.');
            $returnData = OrderLogic::getOrderlogistics($get);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }



    //测试
    public function test()
    {
        try{
            $get['companyName'] = CommonLogic::getCompnayName();
            if (empty($returnData = (new HproseRepository())->getPrincipalMainFollower(\GuzzleHttp\json_encode($get)))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //测试
    public function getcurrency()
    {
        try{
            $returnData = C('supply_currency');
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }







    /**
     * 跨域头部设置
     */
    private function setHeaders()
    {
        $origin = isset($_SERVER['HTTP_ORIGIN'])? $_SERVER['HTTP_ORIGIN'] : '';
        $origin_arr = explode('//', $origin);
        $allow_origin = C('ALLOW_ORIGIN');
        if(in_array($origin_arr['1'], $allow_origin)){
            header('Access-Control-Allow-Origin:'.$origin);
            header('Access-Control-Allow-Credentials:true');
            header('Access-Control-Allow-Methods:POST');
//            header('Access-Control-Allow-Headers:x-requested-with,content-type');
            header('Access-Control-Allow-Headers:x-requested-with,content-type,hunt_types');
        }
        header("Content-type: text/html; charset=utf-8");
    }



}