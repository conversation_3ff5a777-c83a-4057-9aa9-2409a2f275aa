<?php
/**
 * Created by 2023/5/17.
 * User: <PERSON><PERSON><PERSON>
 * Info: ...
 * Time: 下午5:08
 */

namespace Supplychain\Controller;
use Illuminate\Container\Container;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\OpenLogic;
use Supplychain\Model\Logic\WechatWmsLogic;
use Think\Log;


class OpenApiController extends BaseController
{




    public function _initialize()
    {
        $this->setHeaders();

        $container = new Container();
        //单例绑定
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
            return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');
    }


    /**
     * 跨域头部设置
     */
    private function setHeaders()
    {
        $origin = isset($_SERVER['HTTP_ORIGIN'])? $_SERVER['HTTP_ORIGIN'] : '';
        $origin_arr = explode('//', $origin);
        $allow_origin = C('ALLOW_ORIGIN');
        if(in_array($origin_arr['1'], array_merge(['liebao.liexinlocal.net','liebaoch.com'],$allow_origin))){
            header('Access-Control-Allow-Origin:'.$origin);
            header('Access-Control-Allow-Credentials:true');
            header('Access-Control-Allow-Methods:POST');
//            header('Access-Control-Allow-Headers:x-requested-with,content-type');
            header('Access-Control-Allow-Headers:x-requested-with,content-type,hunt_types');
        }
        header("Content-type: text/html; charset=utf-8");
    }

    public function test()
    {
    }


    //获取未收到货的物流单号
    public function getUnreceivedShippingSn()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = OpenLogic::getUnreceivedShippingSn($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            echo \GuzzleHttp\json_encode(['code'=>1,'msg'=>'查询错误']);die;
        }
        echo \GuzzleHttp\json_encode(['code'=>0,'data'=>$returnData,'msg'=>'']);die;

    }

    //签收物流
    public function signShipping()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = OpenLogic::signShipping($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            echo \GuzzleHttp\json_encode(['code'=>1,'msg'=>'查询错误']);die;
        }
        echo \GuzzleHttp\json_encode(['code'=>0,'data'=>$returnData,'msg'=>'']);die;

    }

    //新增猎豹出海的联系方式
    public function addCheetahContact()
    {
        try{
            $requestData = I('post.');
            $returnData = OpenLogic::addCheetahContact($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            echo \GuzzleHttp\json_encode(['code'=>1,'msg'=>'查询错误']);die;
        }
        echo \GuzzleHttp\json_encode(['code'=>0,'data'=>$returnData,'msg'=>'']);die;

    }

    //修改产地状态
    public function updateOriginStatus()
    {
        try{
            $requestData = file_get_contents("php://input");
            CommonLogic::logsw($requestData,__FUNCTION__,Log::WARN);
            $requestData = json_decode($requestData, true);
            $returnData = OpenLogic::updateOriginStatus($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            echo \GuzzleHttp\json_encode(['code'=>1,'msg'=>$e->getMessage()]);die;
        }
        echo \GuzzleHttp\json_encode(['code'=>0,'data'=>$returnData,'msg'=>'']);die;

    }

    //检测是否能够修改
    public function checkIsCanChange()
    {
        try{
            $requestData = I('get.');
            CommonLogic::logsw(\GuzzleHttp\json_encode($requestData),__FUNCTION__,Log::WARN);
            $returnData = OpenLogic::checkIsCanChange($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            echo \GuzzleHttp\json_encode(['code'=>1,'msg'=>$e->getMessage()]);die;
        }
        echo \GuzzleHttp\json_encode(['code'=>0,'data'=>$returnData,'msg'=>$returnData]);die;

    }
}
