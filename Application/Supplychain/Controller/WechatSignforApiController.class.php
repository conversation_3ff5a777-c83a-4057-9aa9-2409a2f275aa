<?php
namespace Supplychain\Controller;




//签收小程序
use Supplychain\Model\CustomerModel;
use Supplychain\Model\Logic\AskCustomerWechatLogic;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\CustomerLogic;
use Supplychain\Model\Logic\WechatLoginLogic;
use Supplychain\Model\Logic\WechatPublicLogic;
use Supplychain\Model\Logic\WechatSignforLogic;
use Supplychain\Model\NoticeModel;
use Think\Log;
use Illuminate\Container\Container;

class WechatSignForapiController extends BaseController
{

    //客户签收的小程序


    public $client =  null;
    public $container = null;
    public static $DB;

    public function _initialize()
    {
        $this->setHeaders();
        $container = new Container();
        //单例绑定
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
            return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');

        if (!in_array(ACTION_NAME,['login'])){
            $header = getallheaders();
            if (!isset($header['Authorization']) || empty($token =$header['Authorization'])){
                    return $this->apiReturn(20045,'token不能为空','');
            }else{
                WechatSignforLogic::$userId = WechatSignforLogic::passport_decrypt($token);
                if (empty(intval(WechatSignforLogic::$userId))){
                    return $this->apiReturn(20045,'token异常','');
                }
            }
        }
    }



    /**
     * 跨域头部设置
     */
    private function setHeaders()
    {
        $origin = isset($_SERVER['HTTP_ORIGIN'])? $_SERVER['HTTP_ORIGIN'] : '';
        $origin_arr = explode('//', $origin);
        $allow_origin = C('ALLOW_ORIGIN');
        if(in_array($origin_arr['1'], $allow_origin)){
            header('Access-Control-Allow-Origin:'.$origin);
            header('Access-Control-Allow-Credentials:true');
            header('Access-Control-Allow-Methods:POST');
//            header('Access-Control-Allow-Headers:x-requested-with,content-type');
            header('Access-Control-Allow-Headers:x-requested-with,content-type,hunt_types');
        }
        header("Content-type: text/html; charset=utf-8");
    }



    public function login()
    {
        try{
            $data = WechatSignforLogic::getParam();
            $returnData = WechatSignforLogic::login($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    public function userInfo()
    {
        try{
            $returnData = WechatSignforLogic::userInfo();
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }




    public function uploadSignForInfo()
    {
        try{
            $data = WechatSignforLogic::getParam();
            $returnData = (new ErpPushController())->synReceiptAttach([
                'number'=>$data['number'],'attachAddress'=>$data['attachAddress'],
                'remark'=>$data['remark']
            ]);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }






}