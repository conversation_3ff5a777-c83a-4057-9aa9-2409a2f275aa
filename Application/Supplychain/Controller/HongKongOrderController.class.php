<?php
/**
 * Created by 2022/6/9.
 * User: Jone
 * Info: 2022/6/9
 * Time: 下午4:09
 */

namespace Supplychain\Controller;


use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\ErrorNotifyLogic;
use Supplychain\Model\Logic\HongKongOrderLogic;
use Supplychain\Model\OrderModel;
use Supplychain\Repository\OrderRepository;
use Think\Log;

class HongKongOrderController extends BaseController
{


    //本港单的控制器

    public function _initialize()
    {

        if (!in_array(strtolower(ACTION_NAME), ["getnotice"])){

            parent::_initialize();
            if (!$this->auth() && !in_array(strtolower(ACTION_NAME), ["productlsit"])) {
                // 检查登录
                if (in_array(strtolower(ACTION_NAME), ["setFields"])) {//弱校验
                    $verify_mode = false;
                } else {//强校验
                    $verify_mode = true;
                }
                $res = $this->checkLogin($verify_mode);
                if ($res['err_code'] != 0) {
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
            }


            try{
                call_user_func_array([$this,'decorator'],[]);
                $this->customerService = LaravelApp('\Supplychain\Service\CustomerService');
                $this->orderService = LaravelApp('\Supplychain\Service\OrderService');
                $this->userRepository = LaravelApp('\Supplychain\Repository\UserRepository');
                $this->orderRepository = LaravelApp('\Supplychain\Repository\OrderRepository');
            }catch(\Exception $e){
                return $this->apiReturn($e->getCode(), $e->getMessage());
            }
        }else{
            parent::_initialize();
        }


    }

    protected function decorator(){
        $this->userId = cookie('uid');
        if(!$this->userId) throw new \Exception("请登陆后在查看",20001);
        if(!in_array(strtolower(ACTION_NAME),['add_customer_info','user_config','get_user_audit','get_user_audit_status','createiccredit','get_user_credit','test','userinfo','getnotice'])){
            //判断是否是报关协议用户 不是的需要录入信息
            $CustomerService = LaravelApp('\Supplychain\Service\CustomerService');
            $hasCustomer = $CustomerService->checkUser($this->userId);
            if(!$hasCustomer){
                throw new \Exception("请先录入基本信息",20002);
            }
        }
    }




    //创建订单
    public function createOrder()
    {
        try{
            $post = I('post.');
            HongKongOrderLogic::$isHongKong = 1;
            if (empty($returnData = HongKongOrderLogic::createOrder($post))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            if (!empty(ErrorNotifyLogic::$notifyType) ){
                $this->apiReturn("20043", ErrorNotifyLogic::$notifyMsg,"");
            }else{
                $this->apiReturn("20045", $e->getMessage(),"");
            }

        }
        return $this->apiReturn(0,'', $returnData);
    }





    public function changeOrder()
    {
        try{
            $post = I('post.');
            HongKongOrderLogic::$isHongKong = 1;

            if (intval(OrderModel::where('order_id',$post['order_id'])->value('order_type')) == 2){
                throw new \Exception('京东金融订单不允许改单');
            }

            if (empty($returnData = HongKongOrderLogic::changeOrder($post))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    public function getInternationalAddress()
    {
        try{
            if (empty($returnData = HongKongOrderLogic::getInternationalAddress(I('post.')))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }






}