<?php
namespace Supplychain\Controller;




//问客小程序的小程序的api接口
use Supplychain\Model\CustomerModel;
use Supplychain\Model\Logic\AskCustomerWechatLogic;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\CustomerLogic;
use Supplychain\Model\Logic\WechatLoginLogic;
use Supplychain\Model\Logic\WechatPublicLogic;
use Supplychain\Model\NoticeModel;
use Think\Log;
use Illuminate\Container\Container;

class WechatapiController extends BaseController
{


    public $client =  null;
    public $container = null;
    public static $DB;



    public function _initialize()
    {
        $this->setHeaders();
        $container = new Container();
        //单例绑定
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
            return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');

        if (!in_array(ACTION_NAME,['login','getnotice','getopenid','locationminip','getsupplyloginwechatqr','loginforwechatqr','wechatpubilc',
            'getsupplyloginwechatpublicqr','getloginwechatpublicqrstatus','getlogisticsinfo'])){
            $header = getallheaders();
            if (!isset($header['Authorization']) || empty($token =$header['Authorization'])){
                    return $this->apiReturn(20045,'token不能为空','');
            }else{
                AskCustomerWechatLogic::$userId = AskCustomerWechatLogic::passport_decrypt($token,'liexin');
                if (empty(intval(AskCustomerWechatLogic::$userId))){
                    return $this->apiReturn(20045,'token异常','');
                }
            }
        }
    }



    /**
     * 跨域头部设置
     */
    private function setHeaders()
    {
        $origin = isset($_SERVER['HTTP_ORIGIN'])? $_SERVER['HTTP_ORIGIN'] : '';
        $origin_arr = explode('//', $origin);
        $allow_origin = C('ALLOW_ORIGIN');
        if(in_array($origin_arr['1'], $allow_origin)){
            header('Access-Control-Allow-Origin:'.$origin);
            header('Access-Control-Allow-Credentials:true');
            header('Access-Control-Allow-Methods:POST');
//            header('Access-Control-Allow-Headers:x-requested-with,content-type');
            header('Access-Control-Allow-Headers:x-requested-with,content-type,hunt_types');
        }
        header("Content-type: text/html; charset=utf-8");
    }



    public function login()
    {
        try{
            $data = AskCustomerWechatLogic::getParam();
            $returnData = AskCustomerWechatLogic::login($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }






    //获取公告信息
    public function getNotice()
    {
        try{
            $data = I('get.');
            $returnData = NoticeModel::getList($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //获取会员中心
    public function userInfo()
    {
        try{
            $returnData = AskCustomerWechatLogic::getUserInfo(['user_id'=>AskCustomerWechatLogic::$userId]);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }



    //问题列表
    public function askCustomerProblemList()
    {
        try{
            $data = I('get.');
            $returnData = AskCustomerWechatLogic::getProblemList($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //问题详情
    public function askCustomerProblemDetail()
    {
        try{
            $data = I('get.');
            $returnData = AskCustomerWechatLogic::askCustomerProblemDetail($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //客户确认问题
    public function customerConfirmProblem()
    {
        try{
            $data = AskCustomerWechatLogic::getParam();
            $returnData = AskCustomerWechatLogic::customerConfirmProblem($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //人工处理
    public function turnManualProcessing()
    {
        try{
            $data = AskCustomerWechatLogic::getParam();
            $returnData = AskCustomerWechatLogic::turnManualProcessing($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //取消确认
    public function customerCancelConfirmProblem()
    {
        try{
            $data = AskCustomerWechatLogic::getParam();
            $returnData = AskCustomerWechatLogic::customerCancelConfirmProblem($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //获取异常选项
    public function getAskCustomerExcepitonOption()
    {
        try{
            $data = AskCustomerWechatLogic::getParam();
            $returnData = AskCustomerWechatLogic::getAskCustomerExcepitonOption($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }



    /**
     * 小程序端调用wx.login获取到code，把code发送给服务端，服务端去请求该接口
     */
    public function getOpenId(){
        try{
            $data = AskCustomerWechatLogic::getParam();
            $code = $data['code'];

            $client = new \GuzzleHttp\Client();
            $response = $client->request('GET','https://api.weixin.qq.com/sns/jscode2session',[
                'query' => [
                    'appid' => AskCustomerWechatLogic::$appid,
                    'secret' =>AskCustomerWechatLogic::$secret,
                    'js_code' => $code,
                    'grant_type' => 'authorization_code',
                ]
            ]);

            $res = json_decode($response->getBody()->getContents(),true);


            if(!isset($res['unionid']) || empty($res['unionid'])) {
                throw new \Exception('获取微信返回数据失败');
            }

            $res['token'] = '';
            //如果绑定了，直接返回用户的token
            if (!empty($userId = CustomerModel::where('wechat_unique_id',$res['unionid'])->value('user_id'))){
                $res['token'] =  AskCustomerWechatLogic::passport_encrypt($userId,'liexin');
            }

        }catch (\Exception$e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }

        return $this->apiReturn(0,'操作成功', $res);
    }

    /**
     * 获取小程序的手机号返回给前端 作废
     */
    public function bindWechat(){
        try{
            echo '作废';die;
            $data = AskCustomerWechatLogic::getParam();
            AskCustomerWechatLogic::bindWechat($data);
        }catch (\Exception$e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', []);
    }

    //获取微信网页登录二维码
    public function getSupplyLoginWechatQr()
    {
        try{
            $url  = (new WechatLoginLogic())->getQr();
            var_dump($url);die;
        }catch (\Exception$e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $url);
    }


    //获取微信登公众号登录二维码
    public function getSupplyLoginWechatPublicQr()
    {
        try{
            $erm = WechatPublicLogic::getEwmRandKey();
            $url  = (new WechatPublicLogic())->getEwm($erm,2);
        }catch (\Exception$e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', ['url'=>$url,'key'=>$erm]);
    }

    //获取登录状态
    public function getLoginWechatPublicQrStatus()
    {
        try{
            $data = I('get.');
            if (!isset($data['key']) || empty($data['key'])){
                throw new \Exception('key is null');
            }
            $data   = (new WechatPublicLogic())->getLoginWechatPublicQrStatus($data['key']);
            if ($data['code'] !== 0){
                throw new \Exception($data['msg']);
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn($data['code'], $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', []);
    }



    //扫码登录之后获取unionid,并且生成cookie
    public function loginForWechatQr()
    {
        //081c5IGa1Vv6PB08nwHa1Vr0RW2c5IG8
        try{
            if(empty($code = I('code',''))){
                throw new \Exception('code is null');
            }
            $returnData = (new WechatLoginLogic())->loginForWechatQr(['code'=>$code]);

        }catch (\Exception$e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }

        //查看unionid里面有没有用户的
        if (empty($userId = CustomerLogic::where('wechat_unique_id',$returnData['unionid'])->value('user_id'))){
            $this->apiReturn("20003",'该用户暂未绑定微信',"");
        }

        //绑定了就生成用户信息

        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //获取微信公众号信息
    public function wechatpubilc()
    {
        \Think\Log::write(\GuzzleHttp\json_encode($_GET),'WARN');
        //1. 将timestamp , nonce , token 按照字典排序
        $timestamp = $_GET['timestamp'];
        $nonce = $_GET['nonce'];
        $token = "mwcvfgcwitemoxlrhart";
        $signature = $_GET['signature'];
        $array = array($timestamp,$nonce,$token);
        sort($array);

        //2.将排序后的三个参数拼接后用sha1加密
        $tmpstr = implode('',$array);
        $tmpstr = sha1($tmpstr);

        //接收微信信息
        $raw_data = $GLOBALS["HTTP_RAW_POST_DATA"];
        libxml_disable_entity_loader(true);//禁止xml中再次引用其他xml实体
        $arrays = json_decode(json_encode(simplexml_load_string($raw_data, 'SimpleXMLElement', LIBXML_NOCDATA)), true);

//        $json = '{"ToUserName":"gh_b49154d52467","FromUserName":"oR44lwbc1q8eOQ66OZ_Ag-NGk9Vo","CreateTime":"1633767425","MsgType":"event","Event":"subscribe","EventKey":"qrscene_202110091615111470","Ticket":"gQHr7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyV0x0b0FnN2VjQjExb2dhR2h4Y0gAAgSQT2FhAwSAOgkA","Encrypt":"\/a8tNjvUj29MqRPaLEMxAlvZ+Gx7Qit+kpfa9SlUxaDFh0rg6pbT785XBS\/S0pIaeP93gpwc4jaML3kt\/LF+xk2ejlSNru4H0y93z8eNHdNl\/4QZ+ln5IwcE+lsTCr1F5SPb+w\/14m3kKfrqyGEcuDZH6\/wvtYtx0b7799KBBAweoDkJr1KG6\/Xa\/ULU0k8xUdOtZUnZR3sW4882KyCZjgkUaAdcxT8h4FTI5GTVSMWUtCM6Iv3A6PPFHCpl7JHdnz121gRJlln9bvuxyKX4qLk06jKLhyRKhtqHiqDt67WsNPMniwRdyPrOcJcz\/p13iATadhnhaIrUTPakurFjhJV3Uqog1qNrOIou3o0GNEYcViilO+s+UaXL2n5bjz3NknO+dHa76+w\/CcvCc87R3x3TNZ9YwJ85RE4J6aXxW0nnHXDIGLZYS+qkVzlkgPMsVBJzuM8iG2mjOfzKCVHjrJfEDU9YSL0w5QxtxkQqEGcbPo8c28Wusx7T\/13bVvSQD1EGWHS\/LoE+FrMFDg\/9Q2Wqwf3z8HAvRUzP3q7PMuQVKA\/xvByydhp4p4fQOsZFxdpZFEkb9fw1JyXMwLTjx84BX57B8hm+bgcBuRl0sjlBvGdv8UhI2eN+\/Ql1DAEZ"}';
//        $arrays = \GuzzleHttp\json_decode($json,true);
        \Think\Log::write('微信xml信息 ： '.\GuzzleHttp\json_encode($arrays),'WARN');

        try{
            (new WechatPublicLogic())->handleWechatPublicCallback($arrays);
        }catch (\Exception $exception){
            \Think\Log::write('回调处理出错 ： '.$exception->getMessage(),'WARN');
        }


        //3. 将加密后的字符串与 signature 进行对比, 判断该请求是否来自微信
        if($tmpstr == $signature)
        {
            echo $_GET['echostr'];
            exit;
        }
    }





    //自动跳转小程序
    public function locationMiniP()
    {
        $str = '<!doctype html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>Document</title>
    <script>
            location.href = "weixin://dl/business/?t=xiad1DEHt1a";
    </script>
</head>
<body>
</body>
</html>';
        echo $str;
    }


    //获取快递信息
    public function getLogisticsInfo()
    {
        try{
            $data = I('get.');
            $returnData   = (new WechatPublicLogic())->getLogisticsInfo($data);

        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn($data['code'], $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }




}