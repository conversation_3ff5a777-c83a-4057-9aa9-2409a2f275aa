<?php
namespace Supplychain\Controller;

use Supplychain\Model\CmsModel;
use Illuminate\Container\Container;
use Supplychain\Model\AskCustomerExceptionModel;
use Supplychain\Model\Logic\AskCustomerWechatLogic;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\PurRequestLogic;
use Supplychain\Model\Logic\WechatWmsLogic;
use Supplychain\Model\Logic\WmsLogic\AbnormalManageLogic;
use Supplychain\Model\Logic\WmsLogic\AppVersionLogic;
use Supplychain\Model\Logic\WmsLogic\AskCustomerLogic;
use Supplychain\Model\Logic\WmsLogic\CheckInLogic;
use Supplychain\Model\Logic\WmsLogic\CustomsDeclarationAndReceiptLogic;
use Supplychain\Model\Logic\WmsLogic\TallyGoodsLogic;
use Supplychain\Model\Logic\WmsLogic\TallyLogic;
use Supplychain\Model\Logic\WmsLogic\TallyTagLogic;
use Supplychain\Model\Logic\WmsLogic\WarehouseLogic;
use Supplychain\Model\OrderModel;
use Supplychain\Model\WmsTallyAbnormalDetailModel;
use Supplychain\Model\WmsTallyDetailModel;
use Think\Log;

class WechatWmsApiController extends BaseController
{

    //wms的小程序 支持openid 支持uniond
    public $client =  null;
    public $container = null;
    public static $DB;


    public function _initialize()
    {

        $this->setHeaders();
        $container = new Container();
        //单例绑定
        $container->singleton('\Pcb\Model\LARAVELDB',function($container){
            return new \Pcb\Model\LARAVELDB($container);
        });
        //注册laravel db数据库驱动
        static::$DB = LaravelApp('\Pcb\Model\LARAVELDB');

        if (!in_array(ACTION_NAME,['login','printpdf','getwechatinfo','submitboxsnandnum','getboxsnandnum','identifyqrcodenumandsn','sendretentiondatamsg'])){

            $header = getallheaders();
            if (!isset($header['Authorization']) || empty($token =$header['Authorization'])){
                return $this->apiReturn(20045,'token不能为空','');
            }else{

                WechatWmsLogic::$userId = WechatWmsLogic::passport_decrypt($token);
                if (empty(intval(WechatWmsLogic::$userId))){
                    return $this->apiReturn(20045,'token异常','');
                }
            }
        }
    }


    /**
     * 跨域头部设置
     */
    private function setHeaders()
    {

        $origin = isset($_SERVER['HTTP_ORIGIN'])? $_SERVER['HTTP_ORIGIN'] : '';
        $origin_arr = explode('//', $origin);
        $allow_origin = C('ALLOW_ORIGIN');
        if(in_array($origin_arr['1'], $allow_origin)){
            header('Access-Control-Allow-Origin:'.$origin);
            header('Access-Control-Allow-Credentials:true');
            header('Access-Control-Allow-Methods:POST');
//            header('Access-Control-Allow-Headers:x-requested-with,content-type');
            header('Access-Control-Allow-Headers:x-requested-with,content-type,hunt_types');
        }
        header("Content-type: text/html; charset=utf-8");
    }


    //登陆
    public function login()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = WechatWmsLogic::login($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //绑定员工unionID
    public function bindUserInfo()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WechatWmsLogic())->bindUserInfo($data);
        }catch (\Exception$e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }

        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //解绑员工unionID
    public function unBindUserInfo()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WechatWmsLogic())->unBindUserInfo($data);
        }catch (\Exception$e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }

        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //获取微信信息
    public function getWechatInfo(){
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WechatWmsLogic())->getWechatInfo($data);
        }catch (\Exception$e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }

        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //登陆
    public function printPdf()
    {
        try{
            $data = $_REQUEST;
            $returnData = CommonLogic::printPdf($data['html']);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //检测是否重复打印
    public function checkRepeatPrintLabel()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WechatWmsLogic())->checkRepeatPrintLabel($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'可以打印',$returnData);
    }

    //生成打印标签
    public function printLabel()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WechatWmsLogic())->printLabel($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //获取打印数据
    public function getPrintLabel()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WechatWmsLogic())->getPrintLabel($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //获取打印类型
    public function getErpOrderSnPrintLabelType()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WechatWmsLogic())->getErpOrderSnPrintLabelType($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //获取托盘下拉列表
    public function getTraySelectOption()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WechatWmsLogic())->getTraySelectOption($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }


    //获取收货登记列表
    public function getCheckInList()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->getCheckInList($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //装箱复核时序蒲
    public function rePackageList()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->rePackageList($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }


    //获取托盘已经扫描的入仓号
    public function getTrayHaveScanErpOrderSn()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->getTrayHaveScanErpOrderSn($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //条码管理列表
    public function erpOrderSnCodeManage()
    {
        try{

            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->erpOrderSnCodeManage($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //作废条码
    public function delErpOrderSnCodeManage()
    {
        try{

            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->delErpOrderSnCodeManage($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //作废条码
    public function delWstyptllIdErpOrderSnCodeManage()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->delWstyptllIdErpOrderSnCodeManage($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //新增收货登记
    public function addCheckIn()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->addCheckIn($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,$returnData,[]);
    }

    //新增异常收货登记
    public function addUnusualCheckIn()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->addUnusualCheckIn($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //异常理货登记列表
    public function getUnusualSortGoodsList()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->getUnusualSortGoodsList($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }


    //理货列表
    public function getGoodsArrangeData()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->getGoodsArrangeData($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }


    //新增理货
    public function addSortGodds()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->addSortGodds($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //获取所有的异常
    public function getAllException()
    {
        try{
            $returnData = AskCustomerExceptionModel::getAllException();
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //获取异常对应的数据数组
    public function getExceptionData()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = AskCustomerExceptionModel::getExceptionData($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //新增问客
    public function addAskCustomer()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->addAskCustomer($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //获取问客
    public function getAskCustomerDetail()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CheckInLogic())->getAskCustomerDetail($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }



    //---------------------------------------------------仓内作业列表start---------------------------------------------------//


    //仓内作业列表
    public function warehouseOperationList()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WarehouseLogic())->warehouseOperationList($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //仓内作业详情
    public function warehouseOperationDetail()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WarehouseLogic())->warehouseOperationDetail($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //执行仓内作业
    public function execWarehouseOperation()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WarehouseLogic())->execWarehouseOperation($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }



    //---------------------------------------------------仓内作业列表end---------------------------------------------------//



    //---------------------------------------------------start深圳扫描出入库---------------------------------------------------//

    //深圳扫描出入库列表
    public function szScanOutInList()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WarehouseLogic())->szScanOutInList($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //深圳扫描入库
    public function szScanIn()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WarehouseLogic())->szScanIn($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //深圳扫描入库
    public function szScanOut()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WarehouseLogic())->szScanOut($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //---------------------------------------------------end深圳扫描出入库---------------------------------------------------//



    //---------------------------------------------------start报关到货通知---------------------------------------------------//



    //获取报关收货信息
    public function getTodayCustomsDeclarationAndReceipt()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CustomsDeclarationAndReceiptLogic())->getTodayCustomsDeclarationAndReceipt($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //签到今天的报关收货信息
    public function signTodayCustomsDeclarationAndReceipt()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CustomsDeclarationAndReceiptLogic())->signTodayCustomsDeclarationAndReceipt($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //获取金蝶的未来货登记
    public function getEasRegistrationOfNonArrivalGoods()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CustomsDeclarationAndReceiptLogic())->getEasRegistrationOfNonArrivalGoods($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //生成今天的未来-货登记
    public function createRegistrationOfNonArrivalGoods()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CustomsDeclarationAndReceiptLogic())->createRegistrationOfNonArrivalGoods($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }


    //获取报关收货统计
    public function getCustomsDeclarationAndReceiptStatistics()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new CustomsDeclarationAndReceiptLogic())->getCustomsDeclarationAndReceiptStatistics($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }


    //---------------------------------------------------end报关到货通知---------------------------------------------------//




    //---------------------------------------------------扫码收箱start---------------------------------------------------//

    //识别二维码的数量和型号
    public function identifyQrCodeNumAndSn()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WechatWmsLogic())->identifyQrCodeNumAndSn($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }


    //获取箱子里面的数量和型号
    public function getBoxSnAndNum()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WechatWmsLogic())->getBoxSnAndNum($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //保存箱子里面的数量和型号
    public function submitBoxSnAndNum()
    {
        try{
            $data = WechatWmsLogic::getParam();
            $returnData = (new WechatWmsLogic())->submitBoxSnAndNum($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }



    //---------------------------------------------------扫码收箱end---------------------------------------------------//



    //---------------------------------------------------理货2.0end---------------------------------------------------//



    public function getTallyGoods()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyGoodsLogic())->getTallyGoods($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }



    public function checkErpOrderSnIsTally()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyGoodsLogic())->checkErpOrderSnIsTally($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }



    public function getOrigin()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyGoodsLogic())->getOrigin($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    public function getOriginCn()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->getOriginCn($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }


    public function submitTallyGoods()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyGoodsLogic())->submitTallyGoods($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    public function getHistoryPic()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyGoodsLogic())->getHistoryPic($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //获取合箱子入仓号
    public function getFixErpOrderSn()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyGoodsLogic())->getFixErpOrderSn($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }

    //修改是否查看
    public function changeWatch()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyGoodsLogic())->changeWatch($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }


    //修改是否查看
    public function checkIsTallyGoods()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyGoodsLogic())->checkIsTallyGoods($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功',$returnData);
    }




    //问题列表
    public function askCustomerProblemList()
    {
        try{
            $data = I('get.');
            $returnData = TallyGoodsLogic::getProblemList($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //问题详情
    public function askCustomerProblemDetail()
    {
        try{
            $data = I('get.');
            $returnData = TallyGoodsLogic::askCustomerProblemDetail($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }







    //---------------------------------------------------理货2.0end---------------------------------------------------//


    //---------------------------------------------------问客2.0----------------------------------------------//

    //问题列表
    public function askCustomerProblemListV2()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $requestData['create_time'] = $requestData['store_execute_time'];
            unset($requestData['store_execute_time']);
            $returnData = (new AskCustomerLogic())->askCustomerProblemListV2($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //新增问客
    public function addAskCustomerV2()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AskCustomerLogic())->addAskCustomerV2($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //异常列表
    public function getAllExceptionV2()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AskCustomerLogic())->getAllExceptionV2($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //异常列表
    public function getErpOrderSnOrderGoodsSn()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AskCustomerLogic())->getErpOrderSnOrderGoodsSn($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //获取异常数据
    public function getExceptionDataV2()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AskCustomerLogic())->getExceptionDataV2($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //获取问客详情
    public function getAskCustomerDetailV2()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AskCustomerLogic())->getAskCustomerDetailV2($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //执行作业
    public function executeJobV2()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AskCustomerLogic())->executeJobV2($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //获取问客数量
    public function getAskNum()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $requestData['create_time'] = $requestData['store_execute_time'];
            unset($requestData['store_execute_time']);
            $returnData = (new AskCustomerLogic())->getAskNum($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //---------------------------------------------------问客2.0----------------------------------------------//




    //---------------------------------------------------理货3.0----------------------------------------------//

    //获取当前箱子号
    public function getBoxSn()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->getBoxSn($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //获取锁定理货信息
    public function getTallyData()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->getTallyData($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //获取锁定理货信息
    public function getBoxData()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->getBoxData($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }





    //理货明细提交
    public function submitTallyDetail()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->submitTallyDetail($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }

        if (empty($returnData)){
            return $this->apiReturn(0,'操作成功', $returnData);
        }else{
            return $this->apiReturn(10000,$returnData, $returnData);
        }

    }


    //理货明细撤销
    public function cancelTallyDetail()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->cancelTallyDetail($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function cancelRelease()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->cancelRelease($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function getBoxStatus()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->getBoxStatus($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //关单封箱
    public function closeBox()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->closeBox($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //重新开箱
    public function reOpenBox()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->reOpenBox($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //异常型号装箱
    public function submitAbnormalBox()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->submitAbnormalBox($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //获取异常理货明细
    public function getAbnormalTallyDetail()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->getAbnormalTallyDetail($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //合箱
    public function fixBox()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->fixBox($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }


    //扫描
    public function scanQrCode()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->scanQrCode($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function getAppLatestVersion()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AppVersionLogic())->getAppLatestVersion($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    // 通过物流单号获取入仓号
    public function getErpOrderSn()
    {
        try {
            $requestData = WechatWmsLogic::getParam();
            $returnData = OrderModel::getErpOrderSnByLogisticsNo($requestData['logistics_no']);
        } catch (\Exception $e) {
            CommonLogic::logsw($e->getMessage(), __FUNCTION__, Log::WARN);
            $this->apiReturn("20046", $e->getMessage(), "");
        }
        return $this->apiReturn(0, '操作成功', $returnData);
    }



    public function getShipmentList()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new PurRequestLogic())->getShipmentList($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function getTallyList()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->getTallyList($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }
    public function getTallyRecordList()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->getTallyRecordList($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function addOrigin()
    {
        $requestData = WechatWmsLogic::getParam();

        $wstydlId = $requestData['wstydl_id'];

        $wstydlInfo = WmsTallyDetailModel::where('wstydl_id',$wstydlId)->first();

        $wstydlInfo['abnormal_batch'] = WmsTallyAbnormalDetailModel::where('wstydl_id',$wstydlId)->value('abnormal_batch');
        $isUsa = TallyLogic::checkOriginSyncPur($wstydlInfo);

    }

    public function updateTallyNetWeight()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->updateTallyNetWeight($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function reTagPrint()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->reTagPrint($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    //---------------------------------------------------理货3.0----------------------------------------------//
    // 修改子箱数
    public function alterTallyTagBySubBoxQty()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyTagLogic())->alterTallyTagBySubBoxQty($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    // 获取待打印的标签号
    public function getPrintScanList()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyTagLogic())->getPrintScanList($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function getBoxNumByDetailId()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->getBoxNumByDetailId($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function updateTagPrintTimes()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyTagLogic())->updateTagPrintTimes($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }



    public function checkScTallyGoods()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyGoodsLogic())->checkScTallyGoods($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function abnormalManageList()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AbnormalManageLogic())->abnormalManageList($requestData);

        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }

        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function getAbnormalNumber()
    {
        try{
            $returnData = (new AbnormalManageLogic())->getAbnormalNumber();
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function getAllOrderDataByEntrustNo()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AbnormalManageLogic())->getAllOrderDataByEntrustNo($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function getOrderDetailById()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AbnormalManageLogic())->getOrderDetailById($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function addAbnormal()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AbnormalManageLogic())->addAbnormal($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function getAbnormalDetail()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AbnormalManageLogic())->getAbnormalDetail($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function confirmAbnormal()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AbnormalManageLogic())->confirmAbnormal($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function cancelAbnormal()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AbnormalManageLogic())->cancelAbnormal($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function getQuickExpressions()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AbnormalManageLogic())->getQuickExpressions($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function getStayHandleCount()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new AbnormalManageLogic())->getStayHandleCount();
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function getRetentionList()
    {
        try{
            $requestData = WechatWmsLogic::getParam();
            $returnData = (new TallyLogic())->getRetentionList($requestData);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

    public function sendRetentionDataMsg()
    {
        try{
            $returnData = (new TallyLogic())->sendRetentionDataMsg();
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20046", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'操作成功', $returnData);
    }

}

