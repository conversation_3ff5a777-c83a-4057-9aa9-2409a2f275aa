<?php
/**
 * Created by 2021/6/21.
 * User: Joneq
 * Info: 2021/6/21
 * Time: 下午3:49
 */

namespace Supplychain\Controller;


use Supplychain\Model\BaoGuanOrderListModel;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\ConfigModel;
use Supplychain\Model\ContactModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\ExchangeRate;
use Supplychain\Model\HongKongDeliveryModel;
use Supplychain\Model\LiexinModel;
use Supplychain\Model\Logic\BaiduAiPicLogic;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\CustomerLogic;
use Supplychain\Model\Logic\ErrorNotifyLogic;
use Supplychain\Model\Logic\ExportLogic;
use Supplychain\Model\Logic\FinanceLogic;
use Supplychain\Model\Logic\HongKongOrderLogic;
use Supplychain\Model\Logic\JdpayLogic;
use Supplychain\Model\Logic\OrderLogic;
use Supplychain\Model\NoticeModel;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\OrderStatusModel;
use Supplychain\Model\PaymentApplyModel;
use Supplychain\Model\ProductModel;
use Supplychain\Model\RegionModel;

use Supplychain\Model\SupplierBankModel;
use Supplychain\Model\SupplierModel;
use Supplychain\Model\UserMessageModel;
use Supplychain\Repository\OrderRepository;
use Supplychain\Repository\UserRepository;

use Think\Log;

class IndexnewController extends BaseController
{


    //新供应链的改版

    public function _initialize()
    {

        if (!in_array(strtolower(ACTION_NAME), ["getnotice"])){

            parent::_initialize();
            if (!$this->auth() && !in_array(strtolower(ACTION_NAME), ["productlsit"])) {
                // 检查登录
                if (in_array(strtolower(ACTION_NAME), ["setFields"])) {//弱校验
                    $verify_mode = false;
                } else {//强校验
                    $verify_mode = true;
                }
                $res = $this->checkLogin($verify_mode);
                if ($res['err_code'] != 0) {
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
            }


            try{
                call_user_func_array([$this,'decorator'],[]);
                $this->customerService = LaravelApp('\Supplychain\Service\CustomerService');
                $this->orderService = LaravelApp('\Supplychain\Service\OrderService');
                $this->userRepository = LaravelApp('\Supplychain\Repository\UserRepository');
                $this->orderRepository = LaravelApp('\Supplychain\Repository\OrderRepository');
            }catch(\Exception $e){
                return $this->apiReturn($e->getCode(), $e->getMessage());
            }
        }else{
            parent::_initialize();
        }


    }

    protected function decorator(){
        $this->userId = cookie('uid');
        if(!$this->userId) throw new \Exception("请登陆后在查看",20001);
        if(!in_array(strtolower(ACTION_NAME),['add_customer_info','user_config','get_user_audit','get_user_audit_status','createiccredit','get_user_credit','test','userinfo','getnotice'])){
            //判断是否是报关协议用户 不是的需要录入信息
            $CustomerService = LaravelApp('\Supplychain\Service\CustomerService');
            $hasCustomer = $CustomerService->checkUser($this->userId);
            if(!$hasCustomer){
                throw new \Exception("请先录入基本信息",20002);
            }
        }
    }


    /**
     * 报关首页
     */
    public function index()
    {
        $data = JdpayLogic::jdLogin();
        var_dump($data);die;
        var_dump((new JdpayLogic())->execute($data['method'],$data['request']));die;
        try{
            $returnData = I('post.');
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }


    //获取地址
    public function getRegion()
    {
        try{
            $data = I('post.');
            if (!isset($data['region_id'])){
                $data['region_id'] = 0;
            }
            $regionArr = RegionModel::where('parent_id',$data['region_id'])->pluck('region_name','region_id');
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $regionArr);
    }


    /**
     * 获取服务协议是否签约
     */
    public function checkServiceContract()
    {
        HongKongOrderLogic::$isHongKong = I('request.is_hk_order',0);
        try{
            $returnData = (new \Supplychain\Repository\OrderRepository)->checkUserServiceAgreement(cookie('uid'));
            $returnData['service_status'] = time()>['invalid_date']? '失效':'有效';
            $returnData['service_cycle'] = intval(date('Y',$returnData['invalid_date'])) - intval(date('Y',$returnData['effect_date']));
            $returnData['service_cycle'] .= '年';
            $returnData['tax_bill_type'] = '自营(单抬头)';
            $returnData['credit_score'] = '信用分暂未开通';
            $returnData['effect_date'] = date('Y-m-d H:i:s',$returnData['effect_date']);
            $returnData['invalid_date'] = date('Y-m-d H:i:s',$returnData['invalid_date']);
            $returnData['accout_end_time'] = date('Y-m-d H:i:s',CustomerLogic::getAccoutEndTime());
            unset($returnData['service_agreement_list']);
        }catch (\Exception $e){
            $returnData['service_status'] = '暂未签约';
            $returnData['service_cycle'] = '';
            $returnData['status'] = ContactModel::where('user_id',cookie('uid'))->value('contact_name')?2:0;
            $returnData['tax_bill_type'] = '';
            $returnData['credit_score'] = '信用分暂未开通';
            $returnData['effect_date'] = '';
            $returnData['invalid_date'] = '';
            $returnData['accout_end_time'] = date('Y-m-d H:i:s',CustomerLogic::getAccoutEndTime());
            return $this->apiReturn(0,'', $returnData);
        }
        return $this->apiReturn(0,'', $returnData);
    }


    //请求erp签约服务协议,请求erp接口
    public function signServiceContract()
    {
        try{
            $returnData = $this->apiReturn(0,'', (new CustomerLogic())->handleSignServiceContract());
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'签约成功', '签约成功');
    }

    //获取该用户对应的业务员
    public function getCustomerSalesman()
    {
        try{
            $returnData = CustomerLogic::getUserFollowPeopleInfo();
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //获取公告信息
    public function getNotice()
    {
        try{
            $data = I('get.');
            $returnData = NoticeModel::getList($data);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }


    //----------------个人中心开始------------------------//
    public function userInfo()
    {
        (new CommonLogic())->getPayLocalGood();
        try{
            $returnData = [];
            $userId = cookie('uid');
            HongKongOrderLogic::$isHongKong = I('request.is_hk_order',0);
            $returnData['basic_info'] = CustomerLogic::getUserBasicInfo($userId);
            $returnData['order'] =  OrderLogic::getUserInfoBasicOrder($userId);
            $returnData['trade'] =  CustomerLogic::getUserInfoBasicTrade($userId);
            $returnData['rate'] =  CustomerLogic::getExchange();
            $returnData['today_rate'] =  CustomerLogic::getTodayRate();
            //$returnData['jdjr'] = [];
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }


    //获取账号截止时间
    public function getAccoutEndTime()
    {
        try{
            $returnData = date('Y-m-d H:i:s',CustomerLogic::getAccoutEndTime());
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }


    //仓库信息
    public function warehouse()
    {
        try{
            $returnData = json_decode(ConfigModel::where('config_type','address')->value('config_json'),true);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //香港入仓列表
    public function hkWareHouse()
    {
        try{
            $get = I('get.');

            if (empty($returnData = HongKongDeliveryModel::getList(array_merge($get,['user_id'=>cookie('uid')])))){
                throw new \Exception("暂无数据");
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //修改公司发票信息
    public function updateCompanyBasicInfo()
    {
        try{
            $post = I('post.');

            if (empty($returnData = CustomerLogic::updateCompanyBasicInfo(array_merge($post,['user_id'=>cookie('uid')])))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'修改成功', '修改成功');
    }

    //修改公司发票信息
    public function updateComInvoice()
    {
        try{
            $post = I('post.');

            if (empty($returnData = CustomerLogic::updateComInvoice(array_merge($post,['user_id'=>cookie('uid')])))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'修改成功', '修改成功');
    }

    //增加合作意向
    public function addCooperationIntention()
    {
        try{
            $post = I('post.');
            if (empty($returnData = CustomerLogic::addCooperationIntention($post))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'添加成功', '添加成功');
    }

    //修改公司联系人信息
    public function updateComContact()
    {
        try{
            $post = I('post.');

            if (empty($returnData = CustomerLogic::updateComContact(array_merge($post,['user_id'=>cookie('uid')])))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'修改成功', '修改成功');
    }

    //修改订单通知邮箱
    public function updateCustomerNotifyEmail()
    {
        try{
            $post = I('post.');

            //如果没有邮箱就新增
            if(empty(UserMessageModel::where('user_id',cookie('uid'))->where('type',$post['type'])->value('id'))){
                UserMessageModel::insertGetId([
                    'send_user'=>$post['send_user'],
                    'user_id'=>cookie('uid'),
                    'type'=>$post['type'],
                ]);
            }else{
                if (empty($returnData = UserMessageModel::where('user_id',cookie('uid'))
                    ->where('id',$post['id'])
                    ->where('type',$post['type'])
                    ->update(['send_user'=>$post['send_user']]))
                ){
                    throw new \Exception('暂无数据');
                }
            }

        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //删除供应商
    public function deleteSupplier()
    {
        try{
            $post = I('post.');
            if (!isset($post['supplier_id'])){
                throw new \Exception('supplier_id is null');
            }
            if (empty(SupplierModel::where('supplier_id',$post['supplier_id'])->where('user_id',cookie('uid'))->where('status',0)->update(['status'=>-2]))){
                throw new \Exception('没有删除');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'删除成功', '删除成功');
    }

    //增加供应商搜索


    //删除供应商银行
    public function deleteSupplierBank()
    {
        try{
            $post = I('post.');
            if (!isset($post['supplier_bank_id'])){
                throw new \Exception('supplier_bank_id is null');
            }
            if (empty(SupplierBankModel::where('supplier_bank_id',$post['supplier_bank_id'])->where('user_id',cookie('uid'))->where('status',0)->update(['status'=>-2]))){
                throw new \Exception('没有删除');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'删除成功', '删除成功');
    }







    //--------------------------订单
    //创建订单
    public function createOrder()
    {
        try{
            $post = I('post.');
            if (empty($returnData = OrderLogic::createOrder($post))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);

            if (!empty(ErrorNotifyLogic::$notifyType) ){
                $this->apiReturn("20043", ErrorNotifyLogic::$notifyMsg,"");
            }else{
                $this->apiReturn("20045", $e->getMessage(),"");
            }

        }
        return $this->apiReturn(0,'', $returnData);
    }


    //搜索商品信息
    public function searchGoodInfo()
    {
        try{
            $post = I('get.');
            if (!isset($post['goods_type']) && empty($post['goods_type'])){
                throw new \Exception('请输入商品型号');
            }
            if (empty($returnData = OrderLogic::searchGoodInfo($post))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //创建修改订单
    public function createChangeOrder()
    {
        try{
            if (intval(OrderModel::where('order_id',I("order_id"))->value('order_type')) == 2){
                throw new \Exception('京东金融订单不允许改单');
            }
            if (empty($order_id = (new OrderRepository())->addChangeOrderInfoToMq(I("order_id")))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $order_id);
    }

    public function changeOrder()
    {
        try{
            $post = I('post.');

            if (intval(OrderModel::where('order_id',$post['order_id'])->value('order_type')) == 2){
                throw new \Exception('京东金融订单不允许改单');
            }

            if (empty($returnData = OrderLogic::changeOrder($post))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    public function orderList()
    {
        try{
            $get = I('get.');
            if (empty($returnData = OrderLogic::getOrderList(array_merge($get,['user_id'=>cookie('uid')])))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    public function orderDetail()
    {
        try{
            if (empty($orderId = I("order_id",0,'intval'))){
                throw new \Exception('订单id不能为空');
            }
            $orderInfo = OrderModel::where("company_id",CustomerLogic::getUserCompanyId(cookie('uid')))->where("order_id",$orderId)->first();
            if (empty($orderInfo)){
                throw new \Exception("该订单不存在");
            }


            if (!empty($orderInfo->supplier_id)){
                $returnData['basic_info'] = (new OrderRepository())->getOrderBasicInfo($orderId);
                $returnData['inland_delivery'] = (new OrderRepository())->get_inland_delivery($orderId);
                $returnData['order_hongkong'] = (new OrderRepository())->get_order_hongkong($orderId);
                $returnData['order_price'] = (new OrderRepository())->getOrderPrice($orderId);
                $returnData['order_goods'] = (new OrderRepository())->getOrderGoodsList();
                $returnData['order_status'] = OrderLogic::getOrderStatus($orderId);
                $returnData['order_tracking'] = (new OrderRepository())->getOrderTracking();
                if (empty($baoguanTime = BaoGuanOrderListModel::where('order_id',$orderId)->value('create_time'))){
                    $returnData['order_status']['baguan_date'] = '';
                }else{
                    $returnData['order_status']['baguan_date'] = date('Y-m-d H:i:s',strtotime($baoguanTime));
                }
            }else{
                $returnData['basic_info'] = $orderInfo;
                $returnData['order_goods'] = (new OrderRepository())->getOrderGoodsList();
            }


        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    public function oftenGoodSList()
    {
        try{
            $get = I('get.');
            if (empty($returnData = OrderGoodsModel::oftenGoodSList(array_merge($get,['user_id'=>cookie('uid')])))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    public function editOrderList()
    {
        try{
            $get = I('get.');
            if (empty($returnData = OrderLogic::getEditOrderList(array_merge($get,['user_id'=>cookie('uid')])))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //修改订单附件
    public function changeOrderAttachment()
    {
        try{
            $post = I('post.');
            if (empty($returnData = OrderModel::where('order_id',$post['order_id'])->where('user_id',cookie('uid'))->update(['attachment'=>$post['attachment']]))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //检测产品是否存在
    public function checkProductIsExist()
    {
        try{
            $post = I('get.');
            //`goods_title`,`goods_type`,`brand`,`user_id`,`company_id`
            $userId = cookie('uid');
            $companyId = CustomerLogic::getUserCompanyId($userId);
            $errKey = [];
            $errMsg = [];
            foreach ($post['goods_type'] as $key=>$val){
                $where['goods_title'] = $post['goods_title'][$key];
                $where['goods_type'] = $post['goods_type'][$key];
                $where['brand'] = $post['brand'][$key];
                $where['user_id'] = $userId;
                $where['company_id'] = $companyId;
                if (!empty(ProductModel::where($where)->value('goods_id'))){
                    $errKey[] = $key;
                    $errMsg[] = $key+1;
                }
            }

            if (!empty($errKey)){
                $this->apiReturn("20045", ['msg'=>'第'.implode(',',$errMsg).'条商品数据已经存在，请勿重复添加','data'=>$errKey],'');
            }

        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', []);
    }


    //修改商品product_specification
    public function changeGoodsProductSpecification()
    {
        try{
            $post = I('post.');
            if (empty($returnData = ProductModel::where('goods_id',$post['goods_id'])->update(['product_specification'=>$post['product_specification']]))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }


    //获取订单物流信息
    public function getOrderlogistics()
    {
        try{
            $get = I('get.');
            $returnData = OrderLogic::getOrderlogistics($get);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }


    //---------------------财务


    public function cancelPayment()
    {
        try{
            $get = I('get.');
            if (empty($returnData = PaymentApplyModel::where('id',$get['id'])->where('user_id',cookie('uid'))->where('status',0)->update(['status'=>3]))){
                throw new \Exception('暂无数据');
            }else{
                (new ErpPushController())->cancelPayLocalGood(\GuzzleHttp\json_encode(['number'=>PaymentApplyModel::where('id',$get['id'])->value('number')]));
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //待开票清单
    public function getNoInvoiceList()
    {
        try{
            $get = I('get.');
            $get['companyName'] = CommonLogic::getCompnayName();
            if (empty($returnData = (new FinanceLogic())->getNoInvoiceList($get))){
                throw new \Exception('暂无数据');
            }

            if (isset($get['export']) && ($get['export'] == 1)){
                ExportLogic::exportExcelNormal('getNoInvoiceList',$returnData,'待开票清单');
                die;
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //付款记录
    public function getPaymentList()
    {
        try{
            $get = I('get.');
            $get['companyName'] = CommonLogic::getCompnayName();
            if (empty($returnData = (new FinanceLogic())->getPaymentList($get))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }


    //订单列表
    public function getOrderList()
    {
        try{
            $get = I('get.');
            $get['companyName'] = CommonLogic::getCompnayName();
            if (empty($returnData = (new FinanceLogic())->getOrderList($get))){
                throw new \Exception('暂无数据');
            }
            if (isset($get['export']) && ($get['export'] == 1)){
                ExportLogic::exportExcelNormal('getOrderList',$returnData,'费用明细');
                die;
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //获取供应商列表
    public function getSupplyBankList()
    {
        try{
            $get = I('get.');
            if (empty($returnData = (new CustomerLogic())->getSupplyBankList($get))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //订单详情
    public function getOrderDetails()
    {
        try{
            $get = I('get.');
            if (empty($returnData = (new FinanceLogic())->getOrderDetails($get))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //付款详情
    public function getPaymentDetails()
    {
        try{
            $get = I('get.');
            if (empty($returnData = (new FinanceLogic())->getPaymentDetails($get))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }



    //添加附件
    public function addOrderAttachment()
    {
        try{
            $get = I('post.');
            if (empty($returnData = (new FinanceLogic())->addOrderAttachment($get))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }



    //待付汇列表
    public function getForPaymentList()
    {
        try{
            $get = I('get.');
            $get['supplierName'] = CommonLogic::getCompnayName();
            if (empty($returnData = (new FinanceLogic())->getForPaymentList($get))){
                throw new \Exception('暂无数据');
            }

        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //同步物流信息接口
    public function synLogisticsInfo()
    {
        try{
            $get = I('post.');

            if (empty($returnData = (new FinanceLogic())->synLogisticsInfo($get))){
                throw new \Exception('暂无数据');
            }

        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //香港入仓接口
    public function getHKInWoreHouse()
    {
        try{
            $get = I('get.');
            $get['company'] = CommonLogic::getCompnayName();
            if (empty($returnData = (new FinanceLogic())->getHKInWoreHouse($get))){
                throw new \Exception('暂无数据');
            }

        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }



    //获取打印
    public function getPaymentNoticePrintData()
    {
        try{
            $get = I('get.');
            $jsonData['number'] = $get['number'];
            if (empty(OrderModel::where('company_id',(new UserRepository())->getCompanyId(cookie('uid')))->where('erp_order_sn',$jsonData['number'])->value('order_id'))){
                throw new \Exception('暂无数据');
            }
            if (empty($returnData = (new FinanceLogic())->getPaymentNoticePrintDataPdf($get))){
                throw new \Exception('暂无数据');
            }
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }


    //获取图片文字
    public function getPicWord()
    {
        try{
            $post = I('post.');
            $returnData = BaiduAiPicLogic::getOcrData($post['image']);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }

    //搜索供应商
    public function searchSupply()
    {
        try{
            $get = I('get.');
            $returnData = CustomerLogic::searchSupply($get);
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);
    }



    //--------------------------------------------------京东金融前段接口----------------------------------------//

    //以公司为单位，获取京东金融信息
    public function getJdJrInfo()
    {
        try{
            $returnData =  CustomerLogic::getJdJrInfo();
        }catch (\Exception $e){
            CommonLogic::logsw($e->getMessage(),__FUNCTION__,Log::WARN);
            $this->apiReturn("20045", $e->getMessage(),"");
        }
        return $this->apiReturn(0,'', $returnData);

    }
}