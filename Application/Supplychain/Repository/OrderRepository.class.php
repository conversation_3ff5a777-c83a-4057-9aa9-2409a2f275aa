<?php
namespace Supplychain\Repository;

use Exception;
use Supplychain\Controller\ErpPushController;
use Supplychain\Exception\ExportGoodsException;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\CountryModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\GoodsRegisterModel;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\CustomerLogic;
use Supplychain\Model\Logic\ErrorNotifyLogic;
use Supplychain\Model\Logic\HongKongOrderLogic;
use Supplychain\Model\Logic\OrderLogic;
use Supplychain\Model\OrderBankInfoModel;
use Supplychain\Model\OrderStatusModel;
use Supplychain\Model\OriginGoodsModel;
use Supplychain\Model\PaymentApplyModel;
use Supplychain\Model\ServiceAgreementModel;
use Supplychain\Model\SupplierBankModel;
use Supplychain\Service\DingNotify;
use Supplychain\Service\OrderService;
use Supplychain\Service\CustomerService;
use Supplychain\Repository\UserRepository;
use Supplychain\Exception\SupplyException;
use Supplychain\Model\ProductModel;
use Supplychain\Model\UserDeliveryModel;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderAddressModel;
use Supplychain\Model\OrderAddressGoodsModel;
use Supplychain\Model\InlandDeliveryModel;
use Supplychain\Model\SupplierModel;
use Supplychain\Model\UnitModel;
use Supplychain\Model\HongKongDeliveryModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\instead_area_code;
use Supplychain\Model\Tax\CustomsItemsModel;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\WaterSingleModel;
use Supplychain\Model\PayCostModel;
use Supplychain\Model\PayCostListModel;
use Supplychain\Model\OrderRecordModel;
use Supplychain\Model\VerificationModel;
use Supplychain\Model\BaoGuanOrderModel;
use Supplychain\Model\BaoGuanOrderListModel;
use Supplychain\Model\HKDeliveryNoteModel;
use Supplychain\Model\HKDeliveryNoteListModel;
use Supplychain\Model\SZDeliveryNoteModel;
use Supplychain\Model\SZDeliveryNoteListModel;
use Supplychain\Model\SZShipmentsNoteModel;
use Supplychain\Model\SZShipmentsNoteListModel;
use GuzzleHttp\Client;

class OrderRepository
{

    public function __construct()
    {
        $this->user_id = CommonLogic::getLoginUid();
        $this->company_id = (new \Supplychain\Repository\UserRepository)->getCompanyId($this->user_id);
    }

    /**
     * 同步商品数据
     * 产地、计量单位
     */
    protected function syncGoods($user_goods_info,$data,$is_exists_goods){
        $user_goods_info->origin = $data['origin'];
        $user_goods_info->measurement = $data['measurement'];
        if($is_exists_goods > 0){
            $user_goods_info->status = ProductModel::$PassAuditStatus;//审核通过
        }else{
            $user_goods_info->status = ProductModel::$SubmittedAuditStatus;//已提交审核
        }
        $user_goods_info->customs_code = isset($data['customs_code']) ? $data['customs_code'] : '';
        $user_goods_info->tariff_rate = isset($data['tariff_rate']) ? $data['tariff_rate'] : 0;
        $user_goods_info->regulatory_condition = isset($data['regulatory_condition']) ? $data['regulatory_condition'] : '';
        if($user_goods_info->save()){
            return true;
        }else{
            return false;
        }

    }



    /**
     * 下单新增用户产品数据
     */
    protected function addGoods($data){

        //如果不存在设置为空
        if (!isset($data['description'])){
           $data['description'] = '';
        }
        if (!isset($data['product_specification'])){
            $data['product_specification'] = '';
        }

        //新增数据
        try{
            $userRepository = new UserRepository;
            $goods =  $userRepository->add_product([
                "goods_title"=>$data['goods_title'],
                "goods_type"=>$data['goods_type'],
                "brand"=>$data['brand'],
                "origin"=>$data['origin'],
                "measurement"=>$data['measurement'],
                "description"=>$data['description'],
                "product_specification"=>$data['product_specification'],
                "status"=>$data['status'],
                "customs_code"=>$data['customs_code'],
                "tariff_rate"=>$data['tariff_rate'],
                "regulatory_condition"=>$data['regulatory_condition'],
            ]);
            return $goods ? $goods->goods_id : false;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }

    }


    /**
     * 订单产品录入 用户产品表
     * 返回goods_id
     */
    public function addUserGoods($user_goods_info,$data,$is_exists_goods){
        try{
            if($user_goods_info){
                //同步数据
                $bk = call_user_func_array([$this,'syncGoods'],[$user_goods_info,$data,$is_exists_goods]);
                return $bk ? $user_goods_info->goods_id : false;
            }else{
                //新增数据
                $data['status'] = $is_exists_goods ? ProductModel::$PassAuditStatus : ProductModel::$SubmittedAuditStatus;
//                dump($data);
                return $this->addGoods($data);
            }
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }

    }


    /**
     * 新增用户订单关联产品表
     */
    public function addUserOrderGoods($data){
        try{
            $data = CommonLogic::orderDiffAddUserOrderGoods($data);
            $orderGoods = OrderGoodsModel::create($data);
            if(!$orderGoods) throw new \Exception("新增订单单个商品失败");
            $orderGoods->status_cns = "审核中";
            $orderGoods->jldw = $orderGoods->measurement ? UnitModel::getName($orderGoods->measurement) : '';
            return $orderGoods;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }

    }


    /**
     * 修改用户订单关联产品表
     */
    public function editUserOrderGoods($data){
        try{
            $data = CommonLogic::orderDiffAddUserOrderGoods($data);
            $order_id = intval($data['order_id']);
            $order_goods_id = intval($data['order_goods_id']);
            $OrderGoodsModel  = new OrderGoodsModel;
            foreach($data as $key=>$v){
                if(!$OrderGoodsModel->isFillable($key)) unset($data[$key]);
            }
            $bk = $OrderGoodsModel::where("company_id",$this->company_id)->where("order_goods_id",$order_goods_id)->update($data);
            if(!$bk) throw new \Exception("编辑订单失败");
            $obj = new \stdClass();
            $obj->order_id = $order_id;
            $obj->order_goods_id = $order_goods_id;
            return $obj;
        }catch(Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }

    }


    /**
     * 查找产品的 海关编码 关税率 监管条件
     */
    public  function getGoodsTax($data){
        $customsItems = CustomsItemsModel::where("goods_title",$data['goods_title'])
            ->where("goods_name",$data['goods_type'])
            ->where("brand_name",$data['brand'])
            ->select("customs_id","material_sn")
            ->first();

        $tariff_rate = $customsItems->customs ? $customsItems->customs->tax_rate_low : '';
        $customs_code = $customsItems->customs ? $customsItems->customs->number : '';
        $regulatory_condition = $customsItems->customs ? $customsItems->customs->supervision_con:'';
        return [$tariff_rate?$tariff_rate:0,$customs_code?$customs_code:'',$regulatory_condition?$regulatory_condition:''];
    }

    /**
     * 录入订单商品
     * type =  create  update
     */
    public function add_order_goods($data,$type='create'){
        try{
            //更新或新增用户关联的产品信息
            $user_goods_info = ProductModel::where("goods_title",$data['goods_title'])
                ->where("goods_type",$data['goods_type'])
                ->where("brand",$data['brand'])
                ->where("company_id",$this->company_id)
                ->first();

            //erp归类库产品信息 查询产品税率 海关编码等
            $is_exists_goods = CustomsItemsModel::where("goods_title",$data['goods_title'])
                ->where("goods_name",$data['goods_type'])
                ->where("brand_name",$data['brand'])
                ->count("id");
            if($is_exists_goods){
                list($data['tariff_rate'],$data['customs_code'],$data['regulatory_condition']) = $this->getGoodsTax($data);
            }else{
                $data['tariff_rate'] = 0;
                $data['customs_code'] = '';
                $data['regulatory_condition'] = '';
            }
            //用户产品表相关操作 存在就修改 不存在就新增
            $user_goods_id = $this->addUserGoods($user_goods_info,$data,$is_exists_goods);
            if(!$user_goods_id) throw new \Exception("更新用户产品数据失败");

            //用户订单关联产品表相关操作
            $data['user_id'] = $this->user_id;
            $data['company_id'] = $this->company_id;
            $data['goods_id'] = $user_goods_id;
            $data['status'] = $is_exists_goods ? ProductModel::$PassAuditStatus : ProductModel::$SubmittedAuditStatus;

            if($type == 'create'){
                return $this->addUserOrderGoods($data);
            }else if($type == 'update'){
                return $this->editUserOrderGoods($data);
            }


        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }



    /*
     * 删除订单关联的产品
     */
    public function delete_order_goods($order_id){
        $bk = OrderGoodsModel::where("company_id",$this->company_id)->where("order_id",$order_id)->count();
        if(!$bk) return true;
        return OrderGoodsModel::where("company_id",$this->company_id)->where("order_id",$order_id)->delete();
    }


    /**
     *更新订单价格
     */
    public function updateOrderPrice($order_id){
        try{
            $price = OrderGoodsModel::where("company_id",$this->company_id)->where("order_id",$order_id)->sum("total_price");
            return OrderModel::where("order_id",$order_id)->update([
                'order_price'=>$price ? $price : 0,
            ]);
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 批量添加订单商品 录入订单第一步
     * 操作的数据库有 order  order_goods  goods
     * 插入新订单空订单 返回订单id
     * 插入订单关联商品表
     * 新增或修改用户关联产品表
     */
    public function batchAddGoods($data){
        $customerService = new \Supplychain\Service\CustomerService;
        try{
            $order_id = I("request.order_id",null,'intval');

            DB::connection('SUPPLYCHAIN')->transaction(function() use($data,$customerService,&$order_id){
                if(!$order_id){
                    $insertData = ['status'=>OrderModel::$WaitToSubmitStatus,
                        "user_id"=>$this->user_id,
                        "company_id"=>$this->company_id,
                        'order_sn'=>OrderModel::findSn(),
                        'is_hk_order'=>HongKongOrderLogic::$isHongKong
                    ];
                    if (isset($_POST['erp_order_sn']) && !empty($_POST['erp_order_sn'])){
                        $insertData['erp_order_sn'] = $_POST['erp_order_sn'];
                    }
                    //第一次添加产品
                    //新增一个订单 状态位待提交 返回订单号 空订单
                    $order = OrderModel::create($insertData);
                    $order_id = $order->order_id;
                    if(!$order) throw new SupplyException("添加订单失败");
                }

                //判断该订单是否可以编辑
                if(!$this->checkOrderIsEdit($order_id)) throw new SupplyException("该订单不支持编辑");
                //删除所有订单关联商品
                $isdelete = $this->delete_order_goods($order_id);
                if(!$isdelete) throw new SupplyException("删除订单商品失败");

                //批量新增商品
                foreach($data as $key=>$item){
                    $item = $customerService->OrderGoodsValidator($item);
                    $item['order_id'] = $order_id;
                    $bk = $this->add_order_goods($item);
                }

                //如果是修改了订单关联的产品 需要先清空国内物流信息地址关联的产品  因为产品有可能不一致了
                $this->updateOrderPrice($order_id);
            });
            if(!$order_id) throw new SupplyException("添加订单事务失败");
            return $order_id;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    /**
     * 判断订单是否可以编辑
     * 只有待提交 审核不通过的订单可以编辑
     * 正常订单不支持编辑
     */
    public function checkOrderIsEdit($order_id){
        try{
            if(!$order_id) return false;
            $status = OrderModel::where("company_id",$this->company_id)->where("order_id",$order_id)->value("status");
            if($status <= 0) return true;
            return false;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }


    /**
     * 录入订单基础信息 第二步
     */
    public function add_basic_order_info($data){
        try{
            $order_id = $data['order_id'];
            if(!$order_id) throw new \Exception("暂无订单ID");

            if ($data['pay_amount'] <= 0 && intval($data['overseas_settlement_type']) !== 2){
                throw new \Exception("预付金额不能小于0");
            }


//            if (!preg_match(CommonLogic::orderDiffErpOrderSnMatch(),$data['erp_order_sn'])){
//                throw new \Exception("入仓号格式不正确");
//            }


            if ($firstOrder = OrderModel::where('erp_order_sn',$data['erp_order_sn'])->where('id_edit_order',0)->first()){
                if(!(( intval($firstOrder->order_id) == intval($order_id) ) || (intval($firstOrder->edit_order_id) == intval($order_id) ))){
                    throw new \Exception("入仓号已经使用，请更换入仓号");
                }
            }

            $checkData['orderNo'] = $data['erp_order_sn'];
            $checkData['principalName'] = CompanyModel::where('company_id',CustomerModel::where('user_id',cookie('uid'))->value('company_id'))->value('company_full_name');
            $checkData['companyName'] = CommonLogic::orderDiffCompanyName();


            if ( (intval(OrderModel::where('order_id',$order_id)->value('id_edit_order')) === 0) && ((new ErpPushController())->checkOrderNo(json_encode($checkData)) === false)){
                throw new \Exception("您的入仓号无效，请您获取新的入仓号或联系业务人员");
            }


            if (round(OrderGoodsModel::where("company_id",$this->company_id)->where("order_id",intval($order_id))->where("status",">=",0)
                    ->sum('total_price'),2) < $data['pay_amount']){
                throw new \Exception("预付金额不能大于订单金额");
            }

            //判断该订单是否可以编辑
            if(!$this->checkOrderIsEdit($order_id)) throw new \Exception("该订单不支持编辑");
            $orderModel = new OrderModel;
            //订单银行信息
            $orderBankField['supplier_bank_id'] = intval($data['supplier_bank_id']);
            $orderBankField['bank_charges'] = intval($data['bank_charges']);
            $orderBankField['pay_ratio'] = intval($data['pay_ratio']);
            $orderBankField['pay_amount'] = round($data['pay_amount'],2);
            if (intval($data['overseas_settlement_type']) == 2){
                $orderBankField['pay_amount'] = 0;
            }
            $orderBankField['pay_date'] = strtotime(trim($data['pay_date']));
            $orderBankField['pay_remark'] = trim($data['pay_remark']);
            $orderBankField['payable_to'] = trim($data['payable_to']);
            foreach($data as $key=>$v){
                if(!$orderModel->isFillable($key)) unset($data[$key]);
            }


            DB::connection('SUPPLYCHAIN')->transaction(function() use($data,$order_id,$orderBankField){
                $data['arrival_time'] = strtotime($data['arrival_time']);
                $data['erp_order_sn'] = $data['erp_order_sn'];
                $data['supplier_name'] = SupplierModel::where('supplier_id',$data['supplier_id'])->value('supplier_name');
                $bk = OrderModel::where("company_id",$this->company_id)->where("order_id",$order_id)->update($data);
                if($bk === false) throw new \Exception('录入订单基础信息失败');

                //********  去除公司id的筛选 where("company_id",$this->company_id)->
                $supplyBank = SupplierBankModel::where("supplier_bank_id",intval($orderBankField['supplier_bank_id']))
                    ->select("bank_account","area","bank_name","swift_code","bank_code","recipient_country","currency_id","bank_address")->first();
//                if(!$supplyBank) throw new \Exception('没找到对应银行信息');
                $OrderBankInfoModelField = $orderBankField;
                if($supplyBank)
                    $OrderBankInfoModelField = array_merge($orderBankField,$supplyBank->toArray());
                $orderBankInfoModel = new OrderBankInfoModel;
                foreach($OrderBankInfoModelField as $key=>$v){
                    if(!$orderBankInfoModel->isFillable($key)) unset($data[$key]);
                }

                $bk = OrderBankInfoModel::updateOrCreate([
                    'order_id'=>$order_id,
                    'user_id'=>$this->user_id,
                    'company_id'=>$this->company_id,
                ],$OrderBankInfoModelField);
                if($bk === false) throw new \Exception('录入订单基础信息失败');

            });


            return $order_id;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            DingNotify::createOrderFailNotify(UserRepository::getCustomerName(),$data['erp_order_sn'],$e->getMessage());
            throw new \Exception($e->getMessage());
        }
    }





    /*
     * 复制订单 add_ordergoods
     * */
    public function copyOrder($data){

        if (!isset($data['order_id']) || empty($data['order_id'])){
            throw new Exception('订单ID不能为空');
        }

        //获取订单信息，
        $orderInfo = OrderModel::where('order_id',$data['order_id'])->where('user_id',cookie('uid'))->first();

        if (empty($orderInfo)){
            throw new Exception('该订单不存在');
        }
        $orderInfo = $orderInfo->toArray();

        unset($orderInfo['order_id']);
        unset($orderInfo['cancel_time']);

        $orderInfo['order_sn'] = OrderModel::findSn();
        $orderInfo['status'] = -2;
        $orderInfo['erp_order_id'] = $orderInfo['erp_order_sn'] = $orderInfo['erp_other_param'] = '';
        $zeroArr = ['is_push','id_edit_order','edit_order_id','update_time','audit_time','is_apply_payment'];
        foreach ($zeroArr as $value){
            $orderInfo[$value] = 0;
        }

        $orderInfo['create_time'] = time();

        //开启事务
        DB::connection('SUPPLYCHAIN')->transaction(function() use($orderInfo,$data){

            if (empty($orderId = OrderModel::insertGetId($orderInfo))){
                throw new SupplyException('订单插入失败');
            }

            $orderOriginGoodsInfo = OrderGoodsModel::where('order_id',$data['order_id'])->select()->get()->toArray();
            foreach ($orderOriginGoodsInfo as $value){
                $value['create_time'] = time();
                $value['order_id'] = $orderId;
                OrderGoodsModel::create($value);
            }
        });

        return true;
    }



    /**
     * 第三部录入香港交货方式
     */
    public function add_hongkong_delivery($data){
        try{
            $order_id = intval($data['order_id']);
            if(!$order_id) throw new \Exception("录入香港交货方式失败");
            //判断该订单是否可以编辑
            if(!$this->checkOrderIsEdit($order_id)) throw new \Exception("该订单不支持编辑");
            $orderModel = new OrderModel;
            $hongKongDeliveryModel = new HongKongDeliveryModel;

            //供应商配送
            $order_model = $orderModel->where("company_id",$this->company_id)->where("order_id",$order_id);
            if($data['hongkong_delivery_type'] == HongKongDeliveryModel::$HongkongDeliveryBySupplierStatus){
                $bk = $order_model->update([
                    'hongkong_delivery_type'=> HongKongDeliveryModel::$HongkongDeliveryBySupplierStatus,
                    'hk_delivery_address_id'=>intval($data['hk_delivery_address_id']),
                ]);
                if($bk === false) throw new \Exception("录入香港交货方式失败");
            }else{
                //代为提货
                $bk = $order_model->update([
                    'hongkong_delivery_type'=> $data['hongkong_delivery_type'],
                    'hk_delivery_address_id'=>0,
                ]);
                if($bk === false) throw new \Exception("录入香港交货方式失败");

                //如果有香港交货地址ID，并且不为空，取出数据
                if (isset($data['hk_com_delivery_id']) && !empty($data['hk_com_delivery_id'])){
                    $hkAddressInfo = CustomerLogic::getAddressInfo($data['hk_com_delivery_id']);
                    $data['instead_contact'] = $hkAddressInfo->consignee;
                    $data['instead_address'] = $hkAddressInfo->city_info.$hkAddressInfo->detail_address;
                    $data['instead_mobile'] = $hkAddressInfo->mobile;

                }

                //新增代为提货信息
                foreach($data as $key=>$v){
                    if(!$hongKongDeliveryModel->isFillable($key)){
                        unset($data[$key]);
                    }
                }
                $data['get_time'] = strtotime($data['get_time']);
                $data['estimated_arrival_time'] = strtotime($data['estimated_arrival_time']);
                if($hongKongDeliveryModel->where("order_id",$order_id)->first()){
                    //修改
                    $bk = $hongKongDeliveryModel->where("order_id",$order_id)->update($data);
                    if($bk === false) throw new \Exception("录入香港交货方式失败");
                }else{
                    //新增
                    $bk = $hongKongDeliveryModel::create($data);
                    if(!$bk) throw new \Exception("录入香港交货方式失败");
                }
            }
            return $order_id;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('录入香港交货方式失败');
        }
    }


    /**
     * 新增订单收货地址
     */
    public function addOrderAddress($address_id,$order_id){
        try{
            $userAddress = UserDeliveryModel::where("company_id",$this->company_id)->where("com_delivery_id",$address_id)->first();

            if (empty($userAddress->com_name)){

                ErrorNotifyLogic::$notifyMsg = $address_id;
                ErrorNotifyLogic::$notifyType = 1;
                throw new \Exception('请补充公司名称');
            }

            if(!$userAddress) throw new \Exception('没找到对应收货地址');
            $bk =  OrderAddressModel::updateOrCreate(
                [
                    'order_id'=>$order_id,
                    'company_id'=>$this->company_id,
                ],
                [
                    'user_id'=>$this->user_id,
                    "com_delivery_id"=>$address_id,
                    'consignee'=>$userAddress->consignee,//收货人姓名
                    'zipcode'=>$userAddress->zipcode,//邮编
                    'mobile'=>$userAddress->mobile,//手机号
                    'intl_code'=>$userAddress->intl_code,//手机号 国际群号
                    'seat_number'=>$userAddress->seat_number,//座机号
                    'seat_number_code'=>$userAddress->seat_number_code,//座机号区段
                    'province'=>$userAddress->province,//省份
                    'city'=>$userAddress->city,//市
                    'district'=>$userAddress->district,//区
                    'detail_address'=>$userAddress->detail_address,//详细地址
                    'inland_delivery_type'=>OrderAddressModel::$Inland_delivery_zhengpi,//整批
                ]
            );
            if($bk === false) throw new \Exception('没找到对应收货地址');
            return $bk;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * 新增国内物流地址关联商品信息
     */
    public function batchOrderAddressGoods($order_address_id,$order_id){
        try{
            $order_goods = OrderGoodsModel::where("user_id",$this->user_id)->where("order_id",$order_id)
                ->select("order_goods_id","goods_title","goods_type","brand","unit_price","numbers")
                ->get()->toArray();
            if(!$order_goods) throw new \Exception('没找到对应订单商品信息');
            //判断是否存在订单地址关联的产品
            $ishas_orderaddress_goods = OrderAddressGoodsModel::where("order_id",$order_id)->count();
            if($ishas_orderaddress_goods){
                $isdelete = OrderAddressGoodsModel::where("order_id",$order_id)->delete();
                if(!$isdelete) throw new \Exception('关联国内物流信息地址商品失败');
            }
            //批量插入地址关联的商品数据
            foreach($order_goods as $key=>$value){
                $bk = OrderAddressGoodsModel::create([
                    "order_address_id"=>$order_address_id,
                    "order_id"=>$order_id,
                    "order"=>$key+1,
                    "order_goods_id"=>$value['order_goods_id'],
                    "goods_type"=>$value['goods_type'],
                    "brand"=>$value['brand'],
                    "goods_title"=>$value['goods_title'],
                    "not_out_nums"=>0,
                    "out_nums"=>$value['numbers'],
                ]);
                if(!$bk) throw new \Exception('新增国内物流信息失败');
            }
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 整批发货
     */
    protected function batchDelivery($data){
        try{
            if(!$data['outbound_type'] || !isset($data['inland_delivery_type']) || !isset($data['order_id'])){
                throw new \Exception('参数缺少');
            }

            if(intval($data['outbound_type']) === 3){
                if (empty($data['user_name']) || empty($data['card_mobile']) || empty($data['id_card']) ){
                    throw new \Exception('请填写完整仓库自提数据');
                }
            }

            if(intval($data['outbound_type']) === 2){
                if (empty($data['payment_freight']) || empty($data['carrier'])){
                    throw new \Exception('请填写完整快递物流数据');
                }
            }


            DB::connection('SUPPLYCHAIN')->transaction(function() use($data) {

                //回写订单主表 国内物流批次选择 整批
                $bk = OrderModel::where("company_id",$this->company_id)->where("order_id",intval($data['order_id']))->update([
                    'inland_delivery_type'=>intval($data['inland_delivery_type'])
                ]);
                if($bk === false) throw new \Exception('录入国内物流信息失败');



                if (isset($data['com_delivery_id']) && (intval($data['outbound_type']) !== 3)){

                    //新增整批发货 收货地址  order_address ,自提没有
                    $orderAddressId = $this->addOrderAddress(intval($data['com_delivery_id']),intval($data['order_id']))->order_address_id;
                }else{
                    $orderAddressId = 0;
                }

                if (intval($data['outbound_type']) === 1 && $orderAddressId === 0){
                    throw new \Exception('请选择收货地址');
                }


                //新增国内物流信息-整批发货 inland_delivery
                $bk = InlandDeliveryModel::updateOrCreate(
                    [
                        'order_id'=>$data['order_id'],
                    ],
                    [
                        'order_address_id'=>$orderAddressId,
                        'outbound_type'=> intval($data['outbound_type']),//出库方式 1人工配送  2快递    3自提
                        'outbound_file'=>isset($data['outbound_file']) ? $data['outbound_file'] : '',//出库文件
                        'delivery_ramark'=>isset($data['delivery_ramark']) ? $data['delivery_ramark']:'',//物流信息备注
                        'contact'=>isset($data['contact']) ? $data['contact'] : '',//联系人
                        'mobile'=>isset($data['mobile']) ? $data['mobile'] : '',//联系电话
                        'carrier'=>isset($data['carrier']) ? $data['carrier'] : '',//承运商
                        'transport'=>isset($data['transport']) ? $data['transport']:'',//运输方式
                        'card_mobile'=>isset($data['card_mobile']) ? $data['card_mobile']:'',//自提手机号
                        'id_card'=>isset($data['id_card']) ? $data['id_card']:'',//自提身份证号码
                        'user_name'=>isset($data['user_name']) ? $data['user_name']:'',//名字
                        'user_id'=>$this->user_id,
                        'payment_freight'=>isset($data['payment_freight']) ? $data['payment_freight'] : '',//运费支付
                    ]
                );
                if($bk === false) throw new \Exception('录入国内物流信息失败');
                //新增物流关联商品信息
//                $this->batchOrderAddressGoods($orderAddress->order_address_id,intval($data['order_id']));
            });
            return $data['order_id'];
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 第四步 录入国内物流信息
     */
    public function add_inland_delivery($data){
        try{
            //检验数据
            if(!in_array(intval($data['inland_delivery_type']),[1,2])){
                throw new \Exception('国内发货物流信息批次选择有误');
            }

            $order_id = isset($data['order_id']) ? $data['order_id'] : 0;
            //判断该订单是否可以编辑
            if(!$this->checkOrderIsEdit($order_id)) throw new \Exception("该订单不支持编辑");

            if(intval($data['inland_delivery_type']) == 1){
                //整批发货 只有一个收货地址  商品一次性全部发货
                return $this->batchDelivery($data);
            }else{
                throw new \Exception('目前只支持整批发货');
//                return $this->batchesDeliver($data);
            }
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * 录入新单其它信息
     */
    public function add_other_order_info($data){
        try{
            $order_id = $data['order_id'];
            if(!$order_id) throw new \Exception("录入订单其它信息审失败");
            //判断该订单是否可以编辑
            if(!$this->checkOrderIsEdit($order_id)) throw new \Exception("该订单不支持编辑");
            $orderModel = new OrderModel;
            foreach($data as $key=>$v){
                if(!$orderModel->isFillable($key)) unset($data[$key]);
            }
            $bk = OrderModel::where("company_id",$this->company_id)->where("order_id",$order_id)->update([
                'attachment'=>$data['attachment'],
                'taking_delivery'=>$data['taking_delivery'],
                'invoice_remark'=>$data['invoice_remark'],
                'pick_box_file'=>$data['pick_box_file'],
                'payment_apply_file'=>$data['payment_apply_file'],
                'order_remark'=>$data['order_remark'],
            ]);
            if($bk === false) throw new \Exception("录入订单其它信息审失败");
            return $order_id;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('录入订单其它信息审失败');
        }
    }

    /**
     * 获取供应商
     */
    public function getuser_suppliers($supplier_name){
        try{
            //20200224 增加未审核是0的供应商显示


            $list = SupplierModel::where("supplier_name",'like','%'.$supplier_name."%")
                ->whereIn('status',[0,1])
                ->orderBy('create_time','desc')
                ->select("supplier_name",  "supplier_id")->get()->toArray();

            //增加一条最新的使用供应商
            if (!empty($supplierId = OrderModel::where('user_id',CommonLogic::getLoginUid())->orderBy('create_time','desc')->value('supplier_id')) && !empty($lastSupplier = SupplierModel::where('supplier_id',$supplierId)->whereIn('status',[0,1])->orderBy('create_time','desc')->first())){


                foreach ($list as $key=>$value){
                    if ($value['supplier_id'] == $lastSupplier->supplier_id){
                        unset($list[$key]);
                    }
                }
                array_unshift($list,['supplier_id'=>$lastSupplier->supplier_id,'supplier_name'=>$lastSupplier->supplier_name]);
            }


            if(!$list)throw new \Exception('没有更多数据');
            return $list;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('没有更多数据');
        }
    }



    /**
     * 获取订单商品列表
     */
    public function getOrderGoodsList(){
        try{
            $order_id = I("order_id",0,'intval');
            if(!$order_id) throw new SupplyException("没有更多数据");
            $list = OrderGoodsModel::where("company_id",$this->company_id)
                ->where("order_id",$order_id)
                ->select("*")
                ->orderBy("order_goods_id","asc")->get();
            $list = $this->setOrderGoodsOtherField($list);
            $list = $list->toArray();
            if(empty($list)) throw new SupplyException("没有更多数据");

            foreach ($list as $key=>$value){
                $list[$key]['total_price'] = round($value['total_price'],2);
                $list[$key]['unit_price'] = $value['unit_price'];
                $list[$key]['measurement_cn'] =  UnitModel::getName($value['measurement']);
            }
            return $list;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * 获取订单基础信息
     */
    public function getOrderBasicInfo($order_id){
        try{

            $orderInfo = OrderModel::where("company_id",CustomerLogic::getUserCompanyId(CommonLogic::getLoginUid()))->where("order_id",$order_id)->with(["bankInfo"])->first();
            if(!$orderInfo) throw new SupplyException("该订单暂无银行信息");
            $orderInfo->currency = array_get(C("supply_currency"),$orderInfo->currency_id,'');
            $orderInfo->overseas_settlement = array_get(C("overseas_settlement_type"),$orderInfo->overseas_settlement_type,'');
            $orderInfo->overseas_settlement_days_cn = $orderInfo->overseas_settlement_days ? $orderInfo->overseas_settlement_days."天" : '现结';
            $orderInfo->phone_type = $orderInfo->landline_code ? 2 : 1;
            $company = CompanyModel::getCompany($orderInfo->company_id);
            $orderInfo->company = $company?$company->company_full_name:'';
            $orderInfo->order_detail_status = $orderInfo->orderStatus;
            $orderInfo->order_status_cn = array_get(OrderModel::$STATUS,$orderInfo->status,'');
            $orderInfo->order_price = round($orderInfo->order_price,2);
            $orderInfo = $orderInfo->toArray();
            if($orderInfo['bank_info']){
                $orderInfo['bank_info']['area'] = array_get(SupplierBankModel::$Area,$orderInfo['bank_info']['area'],'');
//            $orderInfo['bank_info']['bank_charges'] = array_get(OrderStatusModel::$BankCharges,$orderInfo['bank_info']['bank_charges'],'');
                $orderInfo['bank_info']['bank_charges_cn'] = array_get(OrderStatusModel::$BankCharges,$orderInfo['bank_info']['bank_charges'],'');
                $orderInfo['bank_info']['pay_date'] = $orderInfo['bank_info']['pay_date'] ? date('Y-m-d',$orderInfo['bank_info']['pay_date']): '';
                $orderInfo['bank_info']['recipient_country'] =  $orderInfo['bank_info']['recipient_country'] ? CountryModel::getCountryName($orderInfo['bank_info']['recipient_country']) : '';
                $orderInfo['bank_info']['currency_id'] = array_get(C("supply_currency"),$orderInfo['bank_info']['currency_id'],'');
                $orderInfo['bank_info']['bank_user'] = SupplierBankModel::where('supplier_bank_id',$orderInfo['bank_info']['supplier_bank_id'])->value('bank_user');
            }else{
                $orderInfo['bank_info']['area'] = '';
                $orderInfo['bank_info']['bank_charges_cn'] = '';
                $orderInfo['bank_info']['pay_date'] = '';
                $orderInfo['bank_info']['recipient_country'] = '';
                $orderInfo['bank_info']['currency_id'] = '';
                $orderInfo['bank_info']['bank_user'] = '';
            }

            //获取收货登记时间
            $getTimeArr = GoodsRegisterModel::where('entrust_bill','=',$orderInfo['erp_order_sn'])->where('is_del','=',0)->orderBy('id','desc')->get();
            if(empty($getTimeArr)){
                $getTimeStr = ' ';
            }else{
                foreach ($getTimeArr as $value){
                    $getTimeStr[] = date('Y-m-d H:i:s',$value->get_time);
                }
                $getTimeStr = implode(' ; ',$getTimeStr);
            }
            if (empty($getTimeStr)){
                $getTimeStr = '';
            }
            $orderInfo['erp_get_time'] = $getTimeStr;
            $orderInfo['audit_time'] = $orderInfo['audit_time']?date('Y-m-d H:i:s',$orderInfo['audit_time']):'';
            $orderInfo['cancle_time'] = $orderInfo['cancle_time']?date('Y-m-d H:i:s',$orderInfo['cancle_time']):'';
            $orderInfo['current_order_status'] = OrderLogic::getCurrentOrderStatus($orderInfo);

            if (empty($paymentInfo = PaymentApplyModel::where('entrust_bill_number',$orderInfo['erp_order_sn'])->select('status')->first())){
                $orderInfo['payment_status'] = '待申请';
            }else{
                $orderInfo['payment_status'] = array_get(PaymentApplyModel::$status,$paymentInfo->status);
            }

            return $orderInfo;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 获取订单基础信息
     */
    public function get_order_hongkong($order_id){
        try{

            $orderinfo = OrderModel::where("company_id",$this->company_id)->where("order_id",$order_id)
                ->select("hongkong_delivery_type","hk_delivery_address_id")->first();

            if(!$orderinfo) throw new SupplyException("没有更多数据");

            if($orderinfo->hongkong_delivery_type == 1){
                //供应商配送
                $orderinfo->delivery_type_cn = array_get(OrderModel::$HongkongDeliveryType,$orderinfo->hongkong_delivery_type,'');
                $orderinfo->address_list = array_get(C('hongkong_delivery_address'),$orderinfo->hk_delivery_address_id,'');
                return $orderinfo->toArray();
            }else{
                $hongKongDelivery = HongKongDeliveryModel::where("order_id",$order_id)->first();
                if(!$hongKongDelivery) throw new SupplyException("没有更多数据");
                $hongKongDelivery->hk_delivery_address_id = $orderinfo['hk_delivery_address_id'];
                $hongKongDelivery->hongkong_delivery_type = $orderinfo['hongkong_delivery_type'];
                $hongKongDelivery->delivery_type_cn = array_get(OrderModel::$HongkongDeliveryType,$orderinfo->hongkong_delivery_type,'');
                $hongKongDelivery->phone_type = $hongKongDelivery->instead_mobile ? 1 : 2;
                $hongKongDelivery->get_time = date('Y-m-d H:i:s',$hongKongDelivery->get_time);//自提时间
                $hongKongDelivery->estimated_arrival_time = date('Y-m-d H:i:s',$hongKongDelivery->estimated_arrival_time);//自提时间
                $hongKongDelivery->hk_com_delivery_id_cn = CustomerLogic::getAddressInfo($hongKongDelivery->hk_com_delivery_id);
                return $hongKongDelivery;
            }


        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * 获取物流地址详情 整批
     */
    public function getOrderAddressZP($order_id){
        $orderAddress = OrderAddressModel::where("order_id",$order_id)->first();
        if(!$orderAddress) return [];
        $orderAddress->city_info = (new \Supplychain\Repository\UserRepository)->getRegionInfo($orderAddress->province,$orderAddress->city,$orderAddress->district);
        return $orderAddress->toArray();
    }

    /**
     * 整批 获取订单地址对应产品新
     */
    public function getOrderGoodsZP(){
        return [];
    }


    /**
     * 获取订单国内物流信息
     */
    public function get_inland_delivery($order_id){
        try{
            $ordreinfo = OrderModel::where("company_id",$this->company_id)->where("order_id",$order_id)
                ->select("inland_delivery_type")->first();
            if(!$ordreinfo) throw new SupplyException("没有国内物流信息");

            if($ordreinfo->inland_delivery_type == 1){
                //整批发货
                $inlandDelivery = InlandDeliveryModel::where("order_id",$order_id)->first();
                if(!$inlandDelivery) throw new SupplyException("没有国内物流信息整批发货");
                $inlandDelivery->inland_delivery_type_cn = array_get(C('inland_delivery_type'),$ordreinfo->inland_delivery_type,'');
                $inlandDelivery->outbound_type_cn = array_get(C('outbound_type'),$inlandDelivery->outbound_type,'');
                $inlandDelivery->address = $this->getOrderAddressZP($order_id);
                $inlandDelivery->goods = [];
                $inlandDelivery->id_card = substr($inlandDelivery->id_card,0,6).'****'.substr($inlandDelivery->id_card,-4);
                $inlandDelivery = $inlandDelivery->toArray();
            }else{
                throw new SupplyException("没有国内物流信息");
            }
            return $inlandDelivery;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 获取订单总价
     */
    public function getOrderGoodsAllPrice($order_id){
        try{
            return OrderGoodsModel::where("company_id",$this->company_id)->where("order_id",$order_id)->sum("total_price");
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('新增订单数据失败');
        }
    }


    /**
     * 检查用户资料是否同步
     */
    public function checkUserComXieYi($user_id){
        try{
            $customer = CustomerModel::where("user_id",$user_id)->select("erp_cuntomer_id","customer_code","erp_client_id","erp_client_code")->first();
            if(!$customer->erp_cuntomer_id || !$customer->customer_code || !$customer->erp_client_id  || !$customer->erp_client_code){
                throw new SupplyException("客户资料暂未同步！");
            }
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }


    }

    /**
     * 检查供应商是否同步
     */
    public function checkUserSupplyXieYi($user_id,$supplier_id){
        try{
            $supply = SupplierModel::where("supplier_id",$supplier_id)->select("erp_supplier_code","erp_supplier_id",'status')->first();

            //0228去除供应商资料同步的判断
            //            if(!$supply->erp_supplier_code || !$supply->erp_supplier_id){
            //                throw new SupplyException("供应商资料暂未同步！");
            //            }
            return $supply;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }


    }


    /**
     * 检查服务协议
     */
    public function checkUserServiceAgreement($user_id){
        try{

            if(HongKongOrderLogic::$isHongKong){
                $type = 9;
            }else{
                $type = 7;
            }

            $customer = CustomerModel::where("user_id",$user_id)->select("erp_client_id")->first();
            $serviceAgreement = ServiceAgreementModel::where("erp_client_id",$customer->erp_client_id)->where('service_type',$type)->where("status",1)->first();
            if(!$serviceAgreement) throw new SupplyException('暂无服务协议，请联系商务!');
            if(time() >=  $serviceAgreement->effect_date && time() <= $serviceAgreement->invalid_date){
                if(!$serviceAgreement->service_agreement_list) throw new SupplyException('服务协议失效，请联系商务!');
                $switch = false;
                foreach($serviceAgreement->service_agreement_list as $item){
                    if(time() >=  $item->effect_date && time() <= $item->invalid_date){
                        $switch = true;
                        break;
                    }
                }

                if(!$switch) throw new SupplyException('服务协议失效，请联系商务!');
            }else{
                throw new SupplyException('服务协议失效，请联系商务!');
            }
            return $serviceAgreement;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }


    }




    /*
     * 提交订单 操作
     */
    public function goto_adding_order($order_id){
        try{
            $order = OrderModel::where('company_id',$this->company_id)->where("order_id",$order_id)->firstOrFail();
            if(!$order) throw new SupplyException("没找到对应的订单");
            //查看用户资料和供应商资料是否已经提交审核
            $this->checkUserComXieYi($order->user_id);
            $supplyInfo = $this->checkUserSupplyXieYi($order->user_id,$order->supplier_id);
            $this->checkUserServiceAgreement($order->user_id);
            //正常订单 不允许重复提交
            if($order->status >= 0) throw new SupplyException("该订单已经生成了，请勿重复提交");
            $order->status = 0;
            $order->order_price = $this->getOrderGoodsAllPrice($order_id);
            if(!$order->save()){
                throw new SupplyException("提交订单失败");
            }

            CommonLogic::logsw(\GuzzleHttp\json_encode($supplyInfo),'goto_adding_order');
            CommonLogic::logsw(\GuzzleHttp\json_encode($order),'goto_adding_order');

            //增加供应商和供应商银行判断,审核过了，才推送
            if (intval($supplyInfo->status) ===1 ){
                if (intval($order->overseas_settlement_type) !== 1){
                    $this->addOrderMsgToMq(['order_id' => $order_id]);
                }else{
                    $supplierBankId =  OrderBankInfoModel::where('order_id',$order_id)->value('supplier_bank_id');
                    if (intval(SupplierBankModel::where('supplier_bank_id',$supplierBankId)->value('status')) === 1){
                        $this->addOrderMsgToMq(['order_id' => $order_id]);
                    }
                }
            }

            //如果是改单的订单 那么要修改主订单状态为 调整中
            if($order->id_edit_order == 1){
                $masterOrder = OrderModel::where('company_id',$this->company_id)->where("order_id",$order->edit_order_id)->firstOrFail();
                $masterOrder->status =OrderModel::$AdjustmentStatus;
                if(!$masterOrder->save()) throw new SupplyException("提交改单订单失败");

                //新增改单记录
                $or = OrderRecordModel::create([
                    'bills_sn'=>$order->order_sn,
                    'bills_id'=>$order->order_id,
                    'order_id'=>$order_id,
                    'erp_order_sn'=>$order->erp_order_sn,
                    'user_id'=>$order->user_id,
                    'company_id'=>$order->company_id,
                    'supplier_id'=>$order->supplier_id,
                    'supplier_name'=>$order->supplier_name,
                    'biz_date'=>time(),
                    'change_type'=>OrderLogic::getChangeOrderType(),
                ]);
                if(!$or) throw new SupplyException("新增改单记录失败");
            }
            return true;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 提交订单成功后 发送消息到队列
     * 消息队列经常会阻塞，发送异步请求
     */
    public function addOrderMsgToMq($data){
        CommonLogic::logsw(\GuzzleHttp\json_encode($data),'goto_adding_order');
        (new CommonLogic())->sendAsyncRequest(API_DOMAIN.'/supply/erp_push/syncOrderInfo',[
            'order_id'=>$data['order_id']
        ]);
//        $RbmqModel = D('Common/Rbmq');
//        $push_data = array(
//            'job' => 'gongyinglian.add.order',
//            'data' => $data
//        );
//        $mq= $RbmqModel->exchange(C("EXCHANGE_NAME_PCB"))->queue(C("QUEUE_GONGYINGLIAN_ORDER"))
//            ->exchangeBind(C('EXCHANGE_NAME_PCB'), C('QUEUE_GONGYINGLIAN_ORDER'));
////        dump($a);
//        $bk = $mq->push($push_data, C("QUEUE_GONGYINGLIAN_ORDER"));
//        return $bk;
    }


    /**
     * 添加商品后 发送消息到队列
     */
    public function addUserGoodsToMq($data){
        $RbmqModel = D('Common/Rbmq');
        $push_data = array(
            'job' => 'gongyinglian.add.goods',
            'data' => $data
        );
        $mq= $RbmqModel->exchange(C("EXCHANGE_NAME_PCB"))->queue(C("QUEUE_GONGYINGLIAN_ORDER"))
            ->exchangeBind(C('EXCHANGE_NAME_PCB'), C('QUEUE_GONGYINGLIAN_ORDER'));
//        dump($a);
        $bk = $mq->push($push_data, C("QUEUE_GONGYINGLIAN_ORDER"));
        return $bk;
    }


    /**
     * 订单审核中 用户修改订单  发送消息到队列
     * 生成订单相关附表
     */
    public function addChangeOrderInfoToMq($order_id){
        try{
            $order = OrderModel::where("company_id",$this->company_id)->where("order_id",$order_id)->select("order_id","status","edit_order_id")->first();
            if(!$order) throw new SupplyException('没找到相关的订单数据');
            if($order->status != OrderModel::$AuditINGStatus) throw new SupplyException('抱歉，只有审核中订单才能编辑哦!');
            if($order->edit_order_id) return $order->edit_order_id;
            return (new \Supplychain\Service\OrderService)->EorderCNewOrder($order_id);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new $e;
        }

    }




    /**
     * 用户订单列表数据转换
     */
    protected function orderListCheckField($list){
        foreach($list as &$item){
            $item->warehousing_sn = $item->erp_order_sn;
            $item->status_cn = array_get(OrderModel::$STATUS,$item->status,'');
            $item->remaining_days = $item->OverseasSettlementDaysStr;
            $item->currency = array_get(C('supply_currency'),$item->currency_id,'');
            $item->is_apply_payment_cn = $item->is_apply_payment?'已申请':'未申请';
        }
        return $list;
    }

    /**
     * 获取用户订单列表
     */
    public function getUserOrderkList(){
        try{
            $page = I('page',0,'intval');
            $limit = I('limit',15,'intval');
            $supplier_id = I('supplier_id',0,'intval');//供应商id
            $status = I('status',0,'trim');//状态
            $order_type = I('order_type',0,'intval');//1 非账期  2账期
            $start_time = I('start_time','','trim');
            $end_time = I('end_time','','trim');
            $currency_id = I('currency_id',0,'intval');//币别id
            $order_sn = I('order_sn','','trim');//订单号
            $warehousing_sn = I('warehousing_sn','','trim');//入仓单号


            $query = OrderModel::where("order.company_id",$this->company_id)->where("id_edit_order",0)
                ->select("order.order_id","order_sn","erp_order_sn","order_price","currency_id","create_time","status","overseas_settlement_type","overseas_settlement_days",
                    "supplier_name",'is_apply_payment')
                ->OrderBy("order_id","desc");
            $query = $query->SearchSupplier($supplier_id)->SearchStatus($status,$this->company_id)
                ->SearchOrderType($order_type)->SearchTime($start_time,$end_time)->SearchCurrency($currency_id)
                ->SearchOrderSn($order_sn)->SearchWarehousingSn($warehousing_sn);
//            $tmp = str_replace('?', '"'.'%s'.'"', $query->toSql());
//            $sql = vsprintf($tmp, $query->getBindings());
//            echo $sql;
            $list = $query->paginate($limit,[],'page',$page);

            $list = $this->orderListCheckField($list)->toArray();
            $count = $list['total'];
            if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
            if(empty($list['data'])) throw new SupplyException('没有更多数据');
            return [$list['data'],$count];
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 取消订单
     */
    public function cancelOrder($order_id){
        try{
            $order = OrderModel::where('user_id',$this->user_id)->where("order_id",$order_id)->firstOrFail();
            if(!$order) throw new SupplyException("没找到对应的订单");
            $order->status = -3;
            if(!$order->save()){
                throw new SupplyException("取消订单失败");
            }
            return true;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * 处理参数 二位数组转一维数组
     */
    public function getOrderGoodsParamsData($data){
        try{
            if(is_array($data)){
                $arr = [];
                array_walk($data,function(&$value,$key)use(&$arr){
                    $arr[$key] = current(array_values($value));
                });
                return $arr;
            }
            return $data;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new SupplyException('参数解析错误');
        }
    }

    /**
     * 修改订单的时候 或者 回到订单下单第一步 可以新增 修改 删除单个商品
     * 新增单个订单商品
     */
    public function addOneOrderGoods($data){
        try{
            $order_id = intval($data['order_id']);
            //判断该订单是否可以编辑或者新增
            if(!$this->checkOrderIsEdit($order_id)) throw new \Exception("该订单不支持编辑");
            $data = (new \Supplychain\Service\OrderService)->checkOrderGoodsValidator($data);
            $ishasOrder = OrderModel::where("company_id",$this->company_id)->where("order_id",$data['order_id'])->count("order_id");
            if($ishasOrder <= 0 ) throw new SupplyException('订单不存在');
            //检查订单商品是否超过20个
            $goodsNums = OrderGoodsModel::where("company_id",$this->company_id)->where("order_id",$data['order_id'])->where("status",">",0)->count();
            if($goodsNums > 20) throw new SupplyException('最多只能添加20个商品哟!');
            $bk = null;
            DB::connection('SUPPLYCHAIN')->transaction(function() use($data,&$order_id,&$bk){
                $bk = $this->add_order_goods($data);
                if(!$bk) throw new SupplyException('新增订单失败');
                if(!$this->updateOrderPrice($order_id)) throw new SupplyException('修改订单价格');
            });
            return $bk;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * 修改订单的时候 或者 回到订单下单第一步 可以新增 修改 删除单个商品
     * 修改单个订单商品
     */
    public function editOneOrderGoods($data){
        try{
            $order_id = intval($data['order_id']);
            //判断该订单是否可以编辑或者新增
            if(!$this->checkOrderIsEdit($order_id)) throw new \Exception("该订单不支持编辑");
            $data = (new \Supplychain\Service\OrderService)->checkOrderGoodsValidator($data);
            $ishasOrder = OrderModel::where("company_id",$this->company_id)->where("order_id",$data['order_id'])->count("order_id");
            if($ishasOrder <= 0 ) throw new SupplyException('订单不存在');
            $bk = null;
            DB::connection('SUPPLYCHAIN')->transaction(function() use($data,&$order_id,&$bk){
                $bk = $this->add_order_goods($data,$type="update");
                if(!$bk) throw new SupplyException('编辑订单失败');
                if(!$this->updateOrderPrice($order_id)) throw new SupplyException('修改订单价格');
            });
            return $bk;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * 删除订单商品
     */
    public function del_order_goods($order_id,$order_goods_id){
        try{
            $bk = null;
            DB::connection('SUPPLYCHAIN')->transaction(function() use($order_id,$order_goods_id,&$bk){
                $bk =  OrderGoodsModel::where("company_id",$this->company_id)->where("order_goods_id",$order_goods_id)->delete();
                if(!$bk) throw new SupplyException('删除订单失败');
                if(!$this->updateOrderPrice($order_id))throw new SupplyException('删除订单失败');
            });
            return true;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }














    /**
     * 编辑订单商品
     *
     */
    public function edit_order_goods($data){
        try{
            $order_goods_id = intval($data['order_goods_id']);
            unset($data['order_goods_id']);

            //用户关联的产品信息
            $user_goods_info = ProductModel::where("goods_title",$data['goods_title'])
                ->where("goods_type",$data['goods_type'])
                ->where("brand",$data['brand'])
                ->where("user_id",cookie('uid'))
                ->first();

            //erp归类库产品信息
            $is_exists_goods = CustomsItemsModel::where("goods_title",$data['goods_title'])
                ->where("goods_name",$data['goods_type'])
                ->where("brand_name",$data['brand'])
                ->count("id");
            if($is_exists_goods){
                list($data['tariff_rate'],$data['customs_code'],$data['regulatory_condition']) = $this->getGoodsTax($data);
            }else{
                $data['tariff_rate'] = 0;
                $data['customs_code'] = '';
                $data['regulatory_condition'] = '';
            }
            //用户产品表相关操作
            $user_goods_id = $this->addUserGoods($user_goods_info,$data,$is_exists_goods);

//            //用户订单关联产品表相关操作
            $data['goods_id'] = $user_goods_id;
            $bk = $this->editUserOrderGoods($data,$order_goods_id);
            if(!$bk) throw new SupplyException("修改失败");
            $info = OrderGoodsModel::find($order_goods_id);
            $info->status_cn = $info->status_cn;
            $info->jldw = $info->measurement ? UnitModel::getName($info->measurement) : '';
            return $info;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }



    /**
     * 订单商品详情
     */
    public function show_order_goods($order_goods_id){
        try{
            $ordergoods = OrderGoodsModel::where("company_id",$this->company_id)->findOrFail($order_goods_id);
            $ordergoods->status_cn = $ordergoods->status_cn;
            $ordergoods->jldw = $ordergoods->measurement ? UnitModel::getName($ordergoods->measurement) : '';
            if(isset($ordergoods->customs_code)) unset($ordergoods->customs_code);
            return $ordergoods;
        }catch(\Exception $e){
            return false;
        }
    }



    /**
     * 订单商品表返回其他格式化字段
     */
    protected function setOrderGoodsOtherField($list){
        foreach($list as &$item){
            $item->status_cn = $item->status_cn;
            $item->jldw = $item->measurement ? UnitModel::getName($item->measurement) : '';
            $item->origin_code = $item->origin;
//            $item->origin = \Supplychain\Model\OriginGoodsModel::getOriginName($item->origin);
            $item->szBaoGuanNums = $this->getGoodsBaoGuanNums($item->order_id,[
                'goods_title' => $item->goods_title,
                'goods_type' => $item->goods_type,
                'brand' => $item->brand,
            ]);
        }
        return $list;
    }

    /**
     * 获取某个商品的报关数量
     */
    protected function getGoodsBaoGuanNums($order_id , $where){
        try{
            return BaoGuanOrderListModel::where("order_id",$order_id)->where($where)->sum('qty');
        }catch(\Exception $e){
            return 0;
        }

    }


    /**
     * 新增订单数据
     */
    protected function addOrderInfo($basic_info){
        try{
            $basic_info =  $data =  (new CustomerService)->OrderValidator($basic_info);
            return OrderModel::create($basic_info);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('新增订单数据失败');
        }
    }

    /**
     * 新增订单关联商品
     */
    protected function updateOrderGoodsInfo($order_id){
        try{
            return OrderGoodsModel::where("user_id",cookie('uid'))->where("order_id",0)
                ->update([
                'order_id'=>$order_id
            ]);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('新增订单数据失败');
        }
    }



    /**
     * 香港港交货方式
     */
    public function hongkongDelivery($hongkong_delivery,$order_id){
        try{
            //如果是供应商配送  不需要新增地址 地址固定为猎芯
            if($hongkong_delivery['delivery_type'] == HongKongDeliveryModel::$HongkongDeliveryBySupplierStatus){
                return true;
            }
            $hongkong_delivery =  $data =  (new CustomerService)->HongkongDeliveryValidator($hongkong_delivery);
            $hongkong_delivery['order_id'] = $order_id;
            return HongKongDeliveryModel::create($hongkong_delivery);
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('新增订单数据失败');
        }
    }

    // 应付款报表
    public function payCostList($data)
    {
        try{
            $page            = I('page', 0, 'intval');
            $limit           = I('limit', 10, 'intval');  
            $erp_order_sn    = I('erp_order_sn', '', 'trim'); //入仓单号
            $start_time      = I('start_time', '', 'trim');
            $end_time        = I('end_time', '', 'trim');
            $is_add          = I('is_add', 0, 'intval'); // 是否新增水单
            $is_edit         = I('is_edit', 0, 'intval'); // 是否编辑水单
            $water_single_id = I('water_single_id', 0, 'intval');

            $field = ["id", "erp_order_sn", "order_id", "order_sn", "amount", "invocie_amount", 
                    "erp_create_time", "create_user", "bus_date", "remark", "currency", "water_single_id", "create_time"];
                
            $query = PayCostModel::SearchErpOrderSn($erp_order_sn)->SearchTime($start_time, $end_time);

            if ($is_add) { // 新增水单只展示未分配的应付款数据
                $query->where('water_single_id', 0);
            }

            $map['company_id'] = $this->company_id; // 用户ID
            $map['status'] = 1; // 正常
            $list = $query->where($map)->select($field)->orderBy('id', 'desc');
            $list = $query->where($map)->select($field)->orderBy('id', 'desc')->paginate($limit,[],'page',$page)->toArray();

            $count = $list['total'];

            if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
            if(empty($list['data'])) throw new SupplyException('没有更多数据');

            if ($is_edit) { // 编辑水单时标记已选择的应付款
                foreach ($list['data'] as $k=>&$v) {
                    if ($v['water_single_id'] == $water_single_id) {
                        $mark['LAY_CHECKED'] = true; // 标记选中
                        $v = array_merge($mark, $v); // 添加到$v头部
                    }
                }
            }

            return [$list['data'], $count];
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    // 新增水单
    public function addWaterSingle($data)
    {
        try{
            DB::connection('SUPPLYCHAIN')->beginTransaction();

            $pay_cost = I('pay_cost', '');

            if (!$pay_cost) throw new SupplyException('缺少支付费用');

            $add['amount']     = I('amount', 0, 'intval');
            $add['remark']     = I('remark', '', 'trim');
            $add['attachment'] = I('attachment', '', 'trim');
            $add['user_id']    = $this->user_id;
            $add['company_id']    = $this->company_id;

            $water = WaterSingleModel::create($add);

            if ($water === false) {
                DB::connection('SUPPLYCHAIN')->rollback();
                throw new SupplyException('新增水单失败');
            }

            if (is_array($pay_cost)) {
                $pay_cost = array_filter($pay_cost, function(&$v) {
                    return $v ? true : false;
                });

                if (!$pay_cost) throw new SupplyException('缺少支付费用');
                
                foreach ($pay_cost as $k=>$v) {
                    $update = PayCostModel::where('id', $v)->update(['water_single_id' => $water->id]);

                    if ($update === false) {
                        DB::connection('SUPPLYCHAIN')->rollback();
                        throw new SupplyException('回写水单ID失败，应付款ID：'.$v);
                    }
                }
            } else {
                $update = PayCostModel::where('id', $pay_cost)->update(['water_single_id' => $water->id]);

                if ($update === false) {
                    DB::connection('SUPPLYCHAIN')->rollback();
                    throw new SupplyException('回写水单ID失败，应付款ID：'.$pay_cost);
                }
            }

            DB::connection('SUPPLYCHAIN')->commit();
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('新增水单数据失败');
        }
    }

    // 编辑水单
    public function editWaterSingle($data)
    {
        try{
            DB::connection('SUPPLYCHAIN')->transaction(function() use($data) {
                $water_single_id = I('water_single_id', 0, 'intval');
                $pay_cost        = I('pay_cost', '');

                if (!$water_single_id) throw new SupplyException('缺少水单ID参数');
                if (!$pay_cost) throw new SupplyException('缺少pay_cost参数');

                $edit['amount']     = I('amount', 0, 'intval');
                $edit['remark']     = I('remark', '', 'trim');
                $edit['attachment'] = I('attachment', '', 'trim');

                $water = WaterSingleModel::where('id', $water_single_id)->update($edit);

                if ($water === false) throw new SupplyException('编辑水单失败');

                // 将之前关联的应付款表water_single_id更新为0
                $res = PayCostModel::where('water_single_id', $water_single_id)->update(['water_single_id' => 0]);

                if ($res === false) throw new SupplyException('更新关联应付款表失败');

                // 更新应付款表
                foreach ($pay_cost as $k=>$v) {
                    $update = PayCostModel::where('id', $v)->update(['water_single_id' => $water_single_id]);

                    if ($update === false) throw new SupplyException('回写水单ID失败，预付款ID：'.$v);
                }
            });
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('编辑水单数据失败');
        }
    }

    // 获取水单
    public function getWaterSingle($data)
    {
         try{
            $water_single_id = I('water_single_id', '');

            if (!$water_single_id) throw new SupplyException('缺少参数');

            $datas['water_info'] = WaterSingleModel::find($water_single_id);
            $datas['pay_cost_info'] = WaterSingleModel::find($water_single_id)->hasManyPayCost;

            return $datas;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('新增水单数据失败');
        }
    }

    // 水单列表
    public function getWaterList($data)
    {
        try{
            $page         = I('page', 0, 'intval');
            $limit        = I('limit', 10, 'intval');  
            $status       = I('status', ''); 
            $start_time   = I('start_time', '', 'trim');
            $end_time     = I('end_time', '', 'trim');
            
            $field = ["id", "amount", "remark", "attachment", "status", "create_time"];
                
            $query = WaterSingleModel::SearchStatus($status)->SearchTime($start_time, $end_time);

            $list = $query->where('company_id', $this->company_id)->select($field)->orderBy('id', 'desc')->paginate($limit,[],'page',$page)->toArray();

            $count = $list['total'];

            if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
            if(empty($list['data'])) throw new SupplyException('没有更多数据');

            foreach ($list['data'] as &$v) {
                $v['status_cn'] = C('water_status')[$v['status']];
            }

            return [$list['data'], $count];
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    // 删除水单
    public function delWaterSingle($data)
    {
         try{
            $water_single_id = I('water_single_id', '');

            if (!$water_single_id) throw new SupplyException('缺少参数');

            DB::connection('SUPPLYCHAIN')->transaction(function () use ($water_single_id) {
                $delwater = WaterSingleModel::destroy($water_single_id);

                if ($delwater === false) throw new SupplyException('删除水单失败');

                $update = PayCostModel::where('water_single_id', $water_single_id)->update(['water_single_id' => 0]);

                if ($update === false) throw new SupplyException('更新应付款表失败');
            });
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('新增水单数据失败');
        }
    }

    // 获取应付款单
    public function getPayCostById($data)
    {
         try{
            $pay_cost_id = I('pay_cost_id', '');

            if (!$pay_cost_id) throw new SupplyException('缺少参数');

            $datas['pay_cost'] = PayCostModel::find($pay_cost_id);
            $datas['pay_cost_list'] = PayCostModel::find($pay_cost_id)->hasManyPayCostList;

            return $datas;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('新增水单数据失败');
        }
    }

    /**
     * 获取订单数量和总价格
     */
    public function getOrderCountInfo($order_id){
        try{
            $res = $orderGoodsNums =  OrderGoodsModel::where("company_id",$this->company_id)->where("order_id",$order_id)
                ->select(DB::raw("sum(numbers) as nums, sum(total_price) as totalprice"))->firstOrFail();
            if(!$res) throw new \Exception("没有查询到相关数据");
            $data['nums'] = $res->nums;
            $data['totalprice'] = $res->totalprice;
            return $data;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('获取数据失败');
        }
    }

    /**
     * 获取订单状态
     */
    public function isEditOrder($order_id){
        try{
            $status = OrderModel::where("user_id",$this->user_id)->where("order_id",$order_id)->value('status');
            if($status == OrderModel::$WaitToSubmitStatus) return 1;
            return 0;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('获取数据失败');
        }
    }

    // 改单记录列表
    public function getOrderEditList($data)
    {
        try{
            $page          = I('page', 0, 'intval');
            $limit         = I('limit', 10, 'intval');  
            $erp_order_sn  = I('erp_order_sn', ''); 
            $supplier_name = I('supplier_name', ''); 
            $start_time    = I('start_time', '', 'trim');
            $end_time      = I('end_time', '', 'trim');
            
            $field = ["order_record_id", "order_id", "erp_order_sn", "supplier_id", "supplier_name", "biz_date", "bills_id",
                "bills_sn", "change_type", "entry_id", "old_goods_title", "goods_title", "old_brand", "brand", "old_goods_type",
                "goods_type", "old_qty", "qty", "old_price", "price", "old_amount", "amount", "material_no", "user_id", "create_time"];
              
            $query = OrderRecordModel::SearchErpOrderSn($erp_order_sn)->SearchSupplier($supplier_name)->SearchTime($start_time, $end_time);

            $list = $query->where('company_id', $this->company_id)->select($field)->orderBy('order_record_id', 'desc')->groupBy('order_id')->paginate($limit,[],'page',$page)->toArray();

            $count = $list['total'];

            if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
            if(empty($list['data'])) throw new SupplyException('没有更多数据');

            foreach ($list['data'] as &$v) {
                $v['change_type'] = array_get(OrderRecordModel::$change_type, $v['change_type'], '未知');
            }

            return [$list['data'], $count];
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    // 改单记录详情
    public function getOrderEditDetails($data)
    {
        try{
            $order_id = I('order_id', 0, 'intval');

            if (!$order_id) throw new SupplyException('缺少参数');

            $where['order_id'] = $order_id;
            $where['company_id']  = $this->company_id;
            
            $record = OrderRecordModel::where($where)->orderBy('order_record_id', 'desc')->get()->toArray(); // 改单记录

            foreach ($record as $k=>$v) {
                
                $datas['record']['order_id']                     = $v['order_id'];
                $datas['record']['erp_order_sn']                 = $v['erp_order_sn'];
                $datas['record']['supplier_id']                  = $v['supplier_id'];
                $datas['record']['supplier_name']                = $v['supplier_name'];
                $datas['record']['user_id']                      = $v['user_id'];
                $datas['record']['biz_date']                     = $v['biz_date'];
                $datas['record']['bills_id']                     = $v['bills_id'];
                $datas['record']['bills_sn']                     = $v['bills_sn'];
                $datas['record']['lists'][$k]['order_record_id'] = $v['order_record_id'];
                $datas['record']['lists'][$k]['change_type']     = array_get(OrderRecordModel::$change_type, $v['change_type'], '未知');
                $datas['record']['lists'][$k]['entry_id']        = $v['entry_id'];
                $datas['record']['lists'][$k]['old_goods_title'] = $v['old_goods_title'];
                $datas['record']['lists'][$k]['goods_title']     = $v['goods_title'];
                $datas['record']['lists'][$k]['old_brand']       = $v['old_brand'];
                $datas['record']['lists'][$k]['brand']           = $v['brand'];
                $datas['record']['lists'][$k]['old_goods_type']  = $v['old_goods_type'];
                $datas['record']['lists'][$k]['goods_type']      = $v['goods_type'];
                $datas['record']['lists'][$k]['old_qty']         = $v['old_qty'];
                $datas['record']['lists'][$k]['qty']             = $v['qty'];
                $datas['record']['lists'][$k]['old_price']       = $v['old_price'];
                $datas['record']['lists'][$k]['price']           = $v['price'];
                $datas['record']['lists'][$k]['old_amount']      = $v['old_amount'];
                $datas['record']['lists'][$k]['amount']          = $v['amount'];
                $datas['record']['lists'][$k]['material_no']     = $v['material_no'];
                $datas['record']['lists'][$k]['create_time']     = $v['create_time'];
            }

            $datas['order_info'] = OrderModel::find($order_id); // 订单信息

            foreach ($datas['order_info'] as $val) {
                $datas['order_info']['currency_cn'] = array_get(C('supply_currency'), $datas['order_info']['currency_id'], '未知');
                $datas['order_info']['hongkong_delivery_type_cn'] = array_get(OrderModel::$HongkongDeliveryType, $datas['order_info']['hongkong_delivery_type'], '未知');
                $datas['order_info']['inland_delivery_type_cn'] = array_get(OrderModel::$InlandDeliveryType, $datas['order_info']['inland_delivery_type'], '未知');
                $datas['order_info']['status_cn'] = array_get(OrderModel::$STATUS, $datas['order_info']['status'], '未知');
            }

            $datas['order_address']   = OrderAddressModel::where($where)->first(); // 收货地址
            $datas['inland_delivery'] = InlandDeliveryModel::where(['order_id'=>$order_id])->first(); // 国内物流

            foreach ($datas['inland_delivery'] as $inland) {
                $datas['inland_delivery']['outbound_type_cn'] = array_get(C('outbound_type'), $datas['inland_delivery']['outbound_type'], '未知');
                $datas['inland_delivery']['status_cn'] = array_get(C('inland_delivery_status'), $datas['inland_delivery']['status'], '未知');
            }

            return $datas;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    // 应付明细列表
    public function getVerificationList($data)
    {
        try{
            $page          = I('page', 0, 'intval');
            $limit         = I('limit', 10, 'intval');  
            $order_sn      = I('order_sn', ''); 
            $erp_order_sn  = I('erp_order_sn', ''); 
            $start_time    = I('start_time', '', 'trim');
            $end_time      = I('end_time', '', 'trim');
            
            $field = ["id", "erp_cuntomer_id", "order_id", "order_sn", "user_id", "erp_order_sn", "biz_date", "hxbiz_date", "project", "exchange_rate", "currency", "amount", "local_amount", "hx_amount", "hx_local_amount", "create_time"];
              
            $query = VerificationModel::SearchOrderSn($order_sn)->SearchErpOrderSn($erp_order_sn)->SearchTime($start_time, $end_time);

            $list = $query->where('company_id', $this->company_id)->select($field)->orderBy('id', 'desc')->paginate($limit,[],'page',$page)->toArray();

            $count = $list['total'];

            if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
            if(empty($list['data'])) throw new SupplyException('没有更多数据');

            return [$list['data'], $count];
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    // 订单跟踪
    public function getOrderTracking()
    {
        try{
            $order_id = I('order_id', 0, 'intval');

            if (!$order_id) throw new SupplyException('缺少参数');

            $where['order_id'] = $order_id;
            //$where['company_id']  = $this->company_id;


            $datas['hk_delivery'] = HKDeliveryNoteListModel::where($where)->select('*')->groupBy("bills_sn")->orderBy('create_time')->get(); // 香港到货
            $datas['hk_baoguan']  = BaoGuanOrderListModel::where($where)->where("status",1)->groupBy("bills_sn")->select('*')->orderBy('create_time')->get(); // 香港报关
            $datas['sz_delivery'] = SZDeliveryNoteListModel::where($where)->select('*')->groupBy("bills_sn")->orderBy('create_time')->get(); // 深圳到货
            $datas['sz_shipments'] = SZShipmentsNoteListModel::where($where)->select('*')->groupBy("bills_sn")->orderBy('create_time')->get(); // 深圳发货

//            dump($datas);

            return $datas;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    public function getOrderTrackingPage(){
        try{
            $order_id = I('order_id', 0, 'intval');

            if (!$order_id) throw new SupplyException('缺少参数');

            $where['order_id'] = $order_id;
            $where['company_id']  = $this->company_id;
            $where['bills_sn'] = I('bills_sn', '', 'trim');

            $field = ['id', 'user_id', 'order_id', 'bills_sn', 'material_sn', 'goods_title', 'goods_type', 'brand', 'qty', 'amount', 'create_time'];
            $type = I('type', '', 'trim');
            switch ($type){
                case 'hk_delivery' :
                    return HKDeliveryNoteListModel::where($where)->select($field)->get(); // 香港到货
                    break;
                case 'hk_baoguan' :
                    return BaoGuanOrderListModel::where($where)->where("status", 1)->select($field)->get(); // 香港报关
                    break;
                case 'sz_delivery' :
                    return SZDeliveryNoteListModel::where($where)->select($field)->get(); // 深圳到货
                    break;
                case 'sz_shipments':
                    return SZShipmentsNoteListModel::where($where)->select($field)->get(); // 深圳发货
                    break;
                default :
                    return [];
            };
            return [];
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    //处理订单商品导入
    public function getExportGoods($data){
        try{
            $arr = [];
            unset($data[1]);
            $units = UnitModel::pluck("code","name")->toArray();
//            if(count($data) > 20) throw new ExportGoodsException('最多上传20个产品数据');
            $line = 1;

            $repeatGoods = [];

            foreach($data as $k=>&$item){

                //新增去空白
                if (empty(array_filter($item))){
                    unset($data[$k]);
                    continue;
                }

                foreach ($item as $key=>$value){
                    $item[$key] = trim($value);
                }

                //去除千位分割符
                $item[5] = str_replace(',','',$item[5]);

                //新增自动去重，数量添加
                if (!in_array($itemStr = implode(',',array_slice($item,0,5)),array_keys($repeatGoods))){
                    $repeatGoods[$itemStr] = $k ;
                }else{
                    $arr[$repeatGoods[$itemStr]]['numbers[]'] += $item[5];
                    unset($data[$k]);
                    continue;
                }

                //适配中国台湾
                if (trim($item[3]) == '台湾'){
                    $item[3] = '中国台湾';
                }

                $line += 1;
                //准备返回给客户端数据
                list($arr[$k]['goods_type[]'],$arr[$k]['goods_title[]'],$arr[$k]['brand[]'],$arr[$k]['origin[]'],$arr[$k]['jldw[]']
                    ,$arr[$k]['numbers[]'],$arr[$k]['unit_price[]'],$arr[$k]['material_sn[]'],$arr[$k]['contract_or_order_sn[]'],$arr[$k]['purchase_sn[]']
                    ,$arr[$k]['description[]']) = $item;
                $arr[$k]['measurement[]'] = array_get($units,trim($arr[$k]['jldw[]']),'');
                $arr[$k]['total_price[]'] = round($arr[$k]['unit_price[]']*$arr[$k]['numbers[]'],2);

                //字段验证
                $checkFied = [];
                list($checkFied['goods_type'],$checkFied['goods_title'],$checkFied['brand'],$checkFied['origin'],$checkFied['measurement']
                    ,$checkFied['numbers'],$checkFied['unit_price'],$checkFied['material_sn'],$checkFied['contract_or_order_sn'],$checkFied['purchase_sn']
                    ,$checkFied['description']) = $item;

                (new \Supplychain\Service\OrderService)->checkOrderGoodsValidator($checkFied);
            }
            if(count($arr) > 20) throw new ExportGoodsException('最多上传20个产品数据');
            return $arr;
        }catch(SupplyException $e) {
            throw new SupplyException("第{$line}行 ".$e->getMessage());
        }catch(ExportGoodsException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /*
     * 获取订单价格
     */
    public function getOrderPrice($order_id){
        try{
            $price = OrderGoodsModel::where("company_id",$this->company_id)->where("order_id",intval($order_id))->where("status",">=",0)
                ->sum('total_price');
            $returnData['order_price'] = round($price,2);
            $returnData['pay_amount'] = round(OrderBankInfoModel::where("order_id",intval($order_id))->value('pay_amount'),2);


            if (empty($returnData['pay_amount'])){
                $returnData['pay_amount'] = $returnData['order_price'];
            }

            //********新增：“暂不付款”的自动变更为预付比例和金额都为0
            if (intval(OrderModel::where('order_id',$order_id)->value('overseas_settlement_type')) == 2){
                $returnData['pay_amount']  = 0;
            }

            $returnData['ratio'] = round($returnData['pay_amount'] * 100 /$returnData['order_price'],2)  .'%';
            return $returnData;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 获取产地
     *
     */
    public function getOriginGoods(){
        try{
            $data = OriginGoodsModel::orderBy(DB::raw(" CONVERT(name USING gb2312)  "))->pluck("name","name")->toArray();
            unset($data['香港']);
            unset($data['台湾']);
            $sortData = ['中国', '马来西亚','日本','泰国','菲律宾','美国','韩国','新加坡','印度尼西亚','中国台湾','中国香港','中国澳门'];
            $returnData = [];
            foreach ($sortData as $val){
                $returnData[$val] = $val;
                unset($data[$val]);
            }
            return array_merge($returnData,$data);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 获取erp订单号
     */
    public function getErpOrderSn(){
        try{
            $customer = CustomerModel::where("user_id",$this->user_id)->select("erp_client_id")->firstOrFail();
            if(!$customer->erp_client_id) {
                DingNotify::getErpSnFailNotify(UserRepository::getCustomerName());
                throw new SupplyException("获取入仓单号失败");
            }
            return (new \Supplychain\Controller\ErpPushController)->getErpOrderSn($customer->erp_client_id);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

}