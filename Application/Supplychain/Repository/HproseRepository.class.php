<?php
/**
 * Created by 2021/11/2.
 * User: Joneq
 * Info: 2021/11/2
 * Time: 上午10:28
 */

namespace Supplychain\Repository;


class HproseRepository
{

    static private $connection;

    public function __construct()
    {
        /*
         *  获取付款单列表 getPaymentList
            获取待开票数据列表 getNoInvoiceList
            获取付汇查询 getPayLoaclGood
            获取订单费用 getOrderList
            获取订单费用明细 getOrderDetails
            获取付款信息列表 getForPaymentList
            获取香港仓库 getHKInWoreHouse
            获取支付未打印信息getPaymentNoticePrintData
            获取推送委托方主客服 getPrincipalMainFollower
         */
        vendor('Hprose.HproseHttpClient');
        self::$connection = new \HproseHttpClient('http://**************:50005/');
    }



    public function getPaymentList($data)
    {
        $returnData = self::$connection->getPaymentList($data);
        return $returnData;
    }

    public function getNoInvoiceList($data)
    {
        $returnData = self::$connection->getNoInvoiceList($data);
        return $returnData;
    }

    public function getPayLoaclGood($data)
    {
        $returnData = self::$connection->getPayLoaclGood($data);
        return $returnData;
    }

    public function getOrderList($data)
    {
        $returnData = self::$connection->getOrderList($data);
        return $returnData;
    }


    public function getOrderDetails($data)
    {
        $returnData = self::$connection->getOrderDetails($data);
        return $returnData;
    }


    public function getForPaymentList($data)
    {
        $returnData = self::$connection->getForPaymentList($data);
        return $returnData;
    }


    public function getHKInWoreHouse($data)
    {
        $returnData = self::$connection->getHKInWoreHouse($data);
        return $returnData;
    }

    public function getPaymentNoticePrintData($data)
    {
        $returnData = self::$connection->getPaymentNoticePrintData($data);
        return $returnData;
    }


    public function getPrincipalMainFollower($data)
    {
        $returnData = self::$connection->getPrincipalMainFollower($data);
        return $returnData;
    }





}