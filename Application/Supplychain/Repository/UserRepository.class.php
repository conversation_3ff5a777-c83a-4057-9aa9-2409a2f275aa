<?php
namespace Supplychain\Repository;

use Exception;
use Supplychain\Exception\SupplyException;
use Supplychain\Model\ComBasicinfosModel;
use Supplychain\Model\ComCreditsModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\ComInvoiceModel;
use Supplychain\Model\ContactModel;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OriginGoodsModel;
use Supplychain\Model\Tax\CustomsItemsModel;
use Supplychain\Model\UserAuditModel;
use Supplychain\Model\UserDeliveryModel;
use Supplychain\Model\ComBankModel;
use Supplychain\Model\RegionModel;
use Supplychain\Model\ProductModel;
use Supplychain\Model\SupplierModel;
use Supplychain\Model\SupplierBankModel;
use Supplychain\Model\CountryModel;
use Supplychain\Model\UnitModel;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\OrderModel;
use Supplychain\Model\InlandDeliveryModel;
use Supplychain\Model\CmsModel;
use Supplychain\Model\ReceiptModel;
use Supplychain\Model\UserMessageModel;
use Supplychain\Model\WorldAddress;
use Supplychain\Service\DingNotify;

class UserRepository
{

    public function __construct()
    {
        $this->user_id = cookie('uid');
        $this->company_id = $this->getCompanyId($this->user_id);
    }


    /**
     * 判断用户是否已经提交过资料
     */
    public function checkUserHasInfo(){
        return CustomerModel::where("user_id",$this->user_id)->count();
    }

    /**
     * 判断用户是否已经审核通过
     */
    public function checkUserPass(){
        $status = CustomerModel::where("user_id",$this->user_id)->value('status');
        return $status == 4 ? true : false;
    }

    /*
     * 获取用户登陆名
     * */
    static public function getCustomerName()
    {
        return CustomerModel::where("user_id",cookie('uid'))->value('customer_name');
    }



    /*
     * 判断公司名称是否已经存在了
     *
     */
    public function checkCompanyExists($company){
        $count = CompanyModel::where("company_full_name",trim($company['company_full_name']))->count();
        return $count;
    }



    /**
     * 处理录入信息 判断用户是否可以修改信息
     */
    public function checkUserEdit(){
        $status = CustomerModel::where("user_id",$this->user_id)->value('status');
        return $status == 1 ? true : false;
    }

    /**
     * 新增客户资料信息
     */
    public function addCustomer(){
        $userinfo = (new \Supplychain\Model\UserMainModel)->where("user_id",$this->user_id)->select("mobile","user_name","email")->first();
        return CustomerModel::create([
            'user_id'=>$this->user_id,
            'protocol_id'=>1,
            'customer_name'=>$userinfo->mobile ? $userinfo->mobile : ($userinfo->email ? $userinfo->email : ''),
        ]);
    }

    /**
     * 新增公司信息
     */
    public function addCompany($data){
        $data['registered_currency'] = '人民币';
        return CompanyModel::create($data);
    }


    /**
     * 新增开票资料
     */
    public function addComInvoice($data){
        return ComInvoiceModel::create($data);
    }


    /**
     * 新增联系人信息
     */
    public function addContact($data){
        return ContactModel::create($data);
    }

    /*
     * 通过用户id获取公司id
     * 以后可以更改为redis获取
     */
    public function getCompanyId($user_id){
        if(S("supply:user_id:".$user_id.":company_id")){
            return S("supply:user_id:".$user_id.":company_id");
        }

        $userInfo = CustomerModel::where("user_id",$user_id)->select("company_id")->first();
        if($userInfo && $userInfo->company_id){
            S("supply:user_id:".$user_id.":company_id",$userInfo->company_id,3600);
            return S("supply:user_id:".$user_id.":company_id");
        }
        return 0;

//        return $userInfo ? $userInfo->company_id : 0;
    }


    /**
     * 获取公司信息
     */
    public function getCompany($user_id,$company_id){
        return CompanyModel::where("company_id",$company_id)->first();
    }



    /**
     * 获取发票
     */
    public function getComInvoice($user_id,$company_id){
        return ComInvoiceModel::where("company_id",$company_id)->first();
    }

    /**
     * 获取联系人
     */
    public function getContact($user_id,$company_id){
        return ContactModel::where("company_id",$company_id)->first();
    }

    /**
     * 编辑公司信息
     */
    public function edit_company($data){
        $company_id = $this->getCompanyId($this->user_id);
        $companyModel  = new CompanyModel;
        $company = $companyModel::where("company_id",$company_id)->count();
        if(!$company) return false;
        foreach($data as $key=>$v){
            if(!$companyModel->isFillable($key)) unset($data[$key]);
        }
        return   $companyModel::where("company_id",$company_id)->update($data);

    }

    /**
     * 编辑联系人信息
     */
    public function edit_cotact($data){
        $company_id = $this->getCompanyId($this->user_id);
        $contactModel = new ContactModel;
        $contact = $contactModel::where("company_id",$company_id)->count();
        if($contact === false) return false;
        foreach($data as $key=>$v){
            if(!$contactModel->isFillable($key)) unset($data[$key]);
        }
        return   $contactModel::where("company_id",$company_id)->update($data);

    }

    /**
     * 编辑联发票
     * $isaudit 是否重新审核发票
     */
    public function edit_invoice($data,$isaudit=false){
        $company_id = $this->getCompanyId($this->user_id);
        $comInvoiceModel = new ComInvoiceModel;
        $invoice = $comInvoiceModel::where("company_id",$company_id)->count();
        if($invoice === false) return false;
        foreach($data as $key=>$v){
            if(!$comInvoiceModel->isFillable($key)) unset($data[$key]);
        }
        $data['status'] = 0;
        $bk = $comInvoiceModel::where("company_id",$company_id)->update($data);
        return $bk;
    }



    /**
     * 修改用户审核状态
     */
    public function edit_customer_status(){
        return CustomerModel::where("user_id",cookie('uid'))->update(['status'=>1]);
    }


    /**
     * 获取审核状态
     */
    public function get_user_audit(){
        $status = CustomerModel::where("user_id",$this->user_id)->value('status');
        $audit_advice = "";
        if($status == CustomerModel::$InAuditStatus){
            $audit_advice = "审核中";
        }else if($status == CustomerModel::$PassStatus){
            $audit_advice = "审核通过";
        }else{
            $userAudit = UserAuditModel::where("user_id",$this->user_id)->where("is_pass",-1)->where("type",1)->orderBy("audit_id",desc)->first();
            $audit_advice = $userAudit ? $userAudit->audit_advice : '';
        }
        return [
            'status'=>$status,
            "audit_advice"=>$audit_advice,
        ];
    }

    /**
     * 获取审核状态值
     */
    public function getUserAuditStatus(){
        return $status = CustomerModel::where("user_id",$this->user_id)->value('status');
    }


    /**
     * 添加收货地址
     */
    public function add_shipping($data){
        try{
            $bk = UserDeliveryModel::create($data);
            if($bk && intval($data['is_default']) > 0){
                UserDeliveryModel::where("com_delivery_id",'<>',$bk->com_delivery_id)
                    ->where('address_area',$data['address_area'])
                    ->update(['is_default'=>0]);
            }
            if(!$bk) return false;
            $bk->city_info =  $this->getRegionInfo($bk->province,$bk->city,$bk->district,$bk->address_area);
            return $bk;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }

    }

    /**
     * 修改收货地址
     */
    public function edit_shipping($data){
        try{
            $com_delivery_id = intval($data['com_delivery_id']);
            $model = new UserDeliveryModel;
            foreach($data as $key=>$v){
                if(!$model->isFillable($key)) unset($data[$key]);
            }
            $bk = $model->where("com_delivery_id",$com_delivery_id)->update($data);
            if($bk && intval($data['is_default']) > 0){
                UserDeliveryModel::where("com_delivery_id",'<>',$com_delivery_id)
                    ->where('address_area',$data['address_area'])
                    ->update(['is_default'=>0]);
            }
            if(!$bk) return false;
            $ship = $model->find($com_delivery_id);
            $ship->city_info =  $this->getRegionInfo($ship->province,$ship->city,$ship->district,$ship->address_area);
            return $ship;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }


    /**
     * 删除收货地址
     */
    public function deleteSupply($com_delivery_id){
        $company_id = $this->getCompanyId($this->user_id);
        return UserDeliveryModel::where("company_id",$company_id)->where("com_delivery_id",$com_delivery_id)->update(['status'=>-1]);
    }

    /**
     * 收货地址
     * 设为默认
     */
    public function ship_setdefault($com_delivery_id){
        $company_id = $this->getCompanyId($this->user_id);
        $bk = UserDeliveryModel::where("company_id",$company_id)->where("com_delivery_id",$com_delivery_id)->update(['is_default'=>1]);
        $bk2 = false;
        if($bk !== false){
            $addressArea = UserDeliveryModel::where("com_delivery_id",$com_delivery_id)->value('address_area');
            $bk2 = UserDeliveryModel::where("company_id",$company_id)->where('address_area',$addressArea)->where("com_delivery_id","<>",$com_delivery_id)->update(['is_default'=>0]);
        }
        return $bk2;
    }

    /**
     * 获取收货地址详情
     */
    public function getUserDelivery($data){
        $com_delivery_id = intval($data['com_delivery_id']);
        if(!$com_delivery_id) return false;
        return UserDeliveryModel::where("company_id",$this->company_id)->where("com_delivery_id",$com_delivery_id)->first();
    }

    /**
     * 新增银行账号
     */
    public function add_bank($data){
        $data['user_id'] = $this->user_id;
        $data['company_id'] = $this->getCompanyId($this->user_id);
        return ComBankModel::create($data);
    }

    /**
     * 编辑银行账号
     */
    public function edit_bank($data){
        $company_bank_id = intval($data['company_bank_id']);
        $ComBankModel = new ComBankModel;
        foreach($data as $key=>$v){
            if(!$ComBankModel->isFillable($key)) unset($data[$key]);
        }
        $company_id = $this->getCompanyId($this->user_id);
        return $ComBankModel->where("company_id",$company_id)->where("company_bank_id",$company_bank_id)->update($data);
    }


    /**
     * 获取公司银行账号详情
     */
    public function getComBankInfo($data){
        return ComBankModel::where("company_id",$this->company_id)->where("company_bank_id",$data['company_bank_id'])->first();
    }

    /**
     * 删除公司银行账户
     */
    public function deleteComBank($company_bank_id){
        return ComBankModel::where("company_id",$this->company_id)->where("company_bank_id",$company_bank_id)->update(["status"=>-1]);
    }

    /**
     * 新增产品
     */
    public function add_product($data){
        $data['user_id'] = $this->user_id;
        $data['company_id'] = $this->getCompanyId($this->user_id);
        //erp归类库产品信息 查询产品税率 海关编码等
        $is_exists_goods = CustomsItemsModel::where("goods_title",$data['goods_title'])
            ->where("goods_name",$data['goods_type'])
            ->where("brand_name",$data['brand'])
            ->count("id");
        if($is_exists_goods){
            list($data['tariff_rate'],$data['customs_code'],$data['regulatory_condition']) = (new \Supplychain\Repository\OrderRepository)->getGoodsTax($data);
            $data['status'] = ProductModel::$PassAuditStatus;
        }else{
            $data['tariff_rate'] = 0;
            $data['customs_code'] = '';
            $data['regulatory_condition'] = '';
            $data['status'] = isset($data['status']) ? $data['status'] : 0 ;
        }
        return ProductModel::create($data);
    }


    /**
     * @return array
     * 推送物料数据到erp
     */
    protected function pushGoodsInfo($data){
        try{
            $arr['goods_type'] = $data['goods_type'];
            $arr['brand'] = $data['brand'];//品牌
            $arr['goods_title'] = $data['goods_title'];//品名
            $arr['user_id'] = $data['user_id'];//型号
            (new \Supplychain\Repository\OrderRepository)->addUserGoodsToMq(json_encode($arr));
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    /**
     * 获取收货地址省市区
     */
    public function getRegionInfo($procice_id=0,$city_id=0,$district=0,$addressArea=1){
        if (intval($addressArea)!=3){
            $districtName =  RegionModel::where("region_id",intval($district))->value("region_name");
            $cityName =  RegionModel::where("region_id",intval($city_id))->value("region_name");
            $prociceName =  RegionModel::where("region_id",intval($procice_id))->value("region_name");
        }else{
            $districtName =  WorldAddress::where("id",intval($district))->value("name");
            $cityName =  WorldAddress::where("id",intval($city_id))->value("name");
            $prociceName =  WorldAddress::where("id",intval($procice_id))->value("name");
        }

        return ($prociceName ? $prociceName :'').($cityName ? $cityName :'').($districtName ? $districtName :'');
    }

    /**
     * 修改产品
     */
    public function edit_product($data){
        $ProductModel = new ProductModel;
        $product_id = intval($data['goods_id']);
        foreach($data as $key=>$v){
            if(!$ProductModel->isFillable($key)) unset($data[$key]);
        }
        $product = ProductModel::where("company_id",$this->company_id)->where("goods_id",$product_id)->first();
        if(!$product)  throw new  SupplyException('没有找到对应产品信息');

        //erp归类库产品信息 查询产品税率 海关编码等
        $is_exists_goods = CustomsItemsModel::where("goods_title",$data['goods_title'])
            ->where("goods_name",$data['goods_type'])
            ->where("brand_name",$data['brand'])
            ->count("id");
        if($is_exists_goods){
            list($data['tariff_rate'],$data['customs_code'],$data['regulatory_condition']) = (new \Supplychain\Repository\OrderRepository)->getGoodsTax($data);
            $data['status'] = ProductModel::$PassAuditStatus;
        }else{
            $data['tariff_rate'] = 0;
            $data['customs_code'] = '';
            $data['regulatory_condition'] = '';
            $data['status'] = 0;
        }

        $bk = ProductModel::where("company_id",$this->company_id)->where("goods_id",$product_id)->update($data);

        if($bk && $data['goods_title'] == $product->goods_title && $data['goods_type'] == $product->goods_type && $data['brand'] == $product->brand){
            return 0;
        }

        return $bk;
    }


    public function checkProductField($data){
        return (new ProductModel)->checkStatusShowField($data);
    }

    /**
     * 获取产品列表
     */
    public function getProductList($request){
//        dump($request);
        $page = I('page',0,'intval');
        $limit = I('limit',15,'intval');
        $goodsTitle = I('goods_title','','trim');
        $beginTime = I('begin_time',0,'intval');
        $endTIme = I('end_time',0,'intval');
        try{
            $query = ProductModel::where("company_id",$this->company_id);
            $status = I("status",0);
            $proVersion = I("goods_type",'','trim');
            $query = $query->select("goods_id","goods_title","goods_type","brand","origin","status",'measurement','description')
                ->addSelect("tariff_rate","regulatory_condition","certification","product_specification")
                ->where("status","<>",-2)
                ->SearchStatus($status)
                ->SearchProVersion($proVersion)
                ->orderBy("create_time",'desc');
            if (!empty($goodsTitle))$query = $query->where('goods_title',$goodsTitle);
            if (!empty($beginTime))$query = $query->where('create_time','>=',$beginTime);
            if (!empty($endTIme))$query = $query->where('create_time','<=',$endTIme);
            $count = $query->count();
            if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
            $list = $query->paginate($limit,[],'page',$page)->toArray();
            if(!$list) throw new SupplyException('没有更多数据');
            foreach($list['data'] as $k=>$v){
                $list['data'][$k]['status_cn'] = array_get(ProductModel::$StatusCn,$v['status'],'');
                $list['data'][$k]['measurement_cn'] =  UnitModel::where('code',$v['measurement'])->value('name');
            }
            if(empty($list['data'])) throw new SupplyException('没有更多数据');
            return [$list['data'],$count];
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }
    }

    /**
     * 获取产品详情 12
     */
    public function show_product($goods_id){
        return ProductModel::where("company_id",$this->company_id)->with("Unit")->where("goods_id",$goods_id)
            ->select("goods_id","goods_title","goods_type","brand","origin","measurement","description",
                "product_specification","tariff_rate","tariff_rate_land","regulatory_condition","certification")
            ->first();
    }

    /**
     * 删除用户产品
     */
    public function del_user_product($goods_id){
        return ProductModel::where("company_id",$this->company_id)->where("goods_id",$goods_id)->update(['status'=>-2]);
    }


    /**
     * 提交审核订单
     */
    public function changeGoodsStatus($goods_ids){
        $bk = ProductModel::where("company_id",$this->company_id)->whereIn("status",[-1,0])
            ->whereIn("goods_id",$goods_ids)->update(['status'=>1]);
        return $bk;
    }


    /*
     * 获取用户产品数据
     */
    public function getUserGoodsInfo($goods_id){
        return ProductModel::where("user_id",cookie('uid'))->where("goods_id",intval($goods_id))->first();
    }


    /**
     * 供应商列表
     */
    public function getsupplierList(){
        try{
            $page = I('page',0,'intval');
            $limit = I('limit',15,'intval');
            $query = new SupplierModel;
            $status = I("status",0);
            $supplier_name = I("supplier_name",'','trim');
            $query = $query->select("supplier_id","supplier_name","status",'bus_regist_cert_image')
                ->SearchStatus($status)
                ->SearchSupplierName($supplier_name)
                ->orderBy("create_time","desc");
            $list = $query->paginate($limit,[],'page',$page);
            $list = $this->supplierListCheckField($list)->toArray();
            $count = $list['total'];
            if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
            if(empty($list['data'])) throw new SupplyException('没有更多数据');
            return [$list['data'],$count];
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            DingNotify::getSupplyFailNotify();
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    public function supplierListCheckField($list){
        foreach($list as &$item){
            $item->satus_cn = $item->StatusCn;
        }
        return $list;
    }


    /**
     * 添加供应商银行账号
     */
    public function addSupplierBank($data){
        try{

            $company_id = $this->getCompanyId($this->user_id);
            $count = SupplierBankModel::where("supplier_id",$data['supplier_id'])
                ->where("bank_name",htmlspecialchars($data['bank_name']))->where("bank_account",$data['bank_account'])->where('status',1)->count("supplier_bank_id");
            if($count > 0) throw new SupplyException('已经存在该信息!');
            $data['user_id'] = $this->user_id;
            $data['company_id'] = $company_id;
            $data['bank_user'] = str_replace('&amp;','&',$data['bank_user']);
            $data['bank_name'] = str_replace('&amp;','&',$data['bank_name']);

            $bk = SupplierBankModel::create($data);
            if($bk && $_SERVER['SERVER_NAME'] == "api.ichunt.com"){
                $postBody   = ['company_id'=>$company_id,'supplier_name'=>$data['bank_name']];
                post_curl('http://sc.ichunt.net/mobile/sendSupplyBankRegister', $postBody, array('api-key: crm a1b2c3d4e5f6g7h8i9jk'));
                DingNotify::addSupplyBankNotify($data['bank_name']);
            }
            return $bk;
        }catch(SupplyException $e) {
            throw new SupplyException($e->getMessage());
        }catch(Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new SupplyException("新增失败或账号已存在");
        }

    }

    /**
     * 编辑供应商银行账号
     */
    public function editSupplierBank($data){
        try{
            $SupplierBankModel = new SupplierBankModel;
            $supplier_bank_id = intval($data['supplier_bank_id']);
            foreach($data as $key=>$v){
                if(!$SupplierBankModel->isFillable($key)) unset($data[$key]);
            }
            $data['status'] = 0;
            return SupplierBankModel::where('user_id',cookie('uid'))->where('status',0)->where("supplier_bank_id",$supplier_bank_id)->update($data);
        }catch(Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            return false;
        }

    }

    /**
     * 编辑供应商银行账号
     */
    public function getSupplyBankInfo($data){
        try{
            $supplier_bank_id = intval($data['supplier_bank_id']);
            $bankInfo = SupplierBankModel::where("supplier_bank_id",$supplier_bank_id)->firstOrFail();
            $bankInfo->bank_address = stripslashes($bankInfo->bank_address);
            return $bankInfo;
        }catch(Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }

    }

    /**
     * 供应商银行账户列表
     */
    public function getsupplierBrankList(){
        try{
            $page = I('page',0,'intval');
            $limit = I('limit',15,'intval');
            $supplier_id = I('supplier_id',0,'intval');
            $company_id = $this->getCompanyId($this->user_id);
//            $query = SupplierBankModel::where("company_id",$company_id)->where("supplier_id",$supplier_id);

            $query = SupplierBankModel::where("company_id",'>=',0);
            if (!empty($supplier_id)){
                $query = $query->where('supplier_id',$supplier_id);
            }
            $bank_account = I("bank_account",'','trim');
            $bankUser = I("bank_user",'','trim');
            $bankName = I("supplier_name",'','trim');

            //默认不显示
            if (empty($bank_account) && empty($bankUser) && empty($bankName) && empty($supplier_id)){
                $query = $query->where('supplier_bank_id',-1);
            }

            $query = $query->select("supplier_bank_id","area","currency_id","bank_name","bank_account","bank_code",
                "bank_address","swift_code","recipient_country","bank_information","status","area","supplier_id",'bank_user')
                ->SearchBankAccount($bank_account)
                ->SearchBankUser($bankUser)
                ->SearchBankName($bankName)
                ->orderBy("create_time","desc");
            $list = $query->paginate($limit,[],'page',$page);
            $list = $this->supplierBankListCheckField($list)->toArray();
            foreach ($list['data'] as $key=>$value){
                $list['data'][$key]['bank_address'] = stripslashes($value['bank_address']);
            }
            $count = $list['total'];
            if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
            if(empty($list['data'])) throw new SupplyException('没有更多数据');
            return [$list['data'],$count];
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 获取供应商名称
     */
    public function getsupplierName($supplier_id){
        $name = SupplierModel::where("supplier_id",$supplier_id)->value("supplier_name");
        return $name ? $name : '';
    }

    /**
     * 供应商银行账户列表字段处理
     * @param $list
     * @return mixed
     */
    public function supplierBankListCheckField($list){
        foreach($list as &$item){
            $item->bibie = $item->CruuencyCn;
            $item->quyu = $item->AreaCn;
            $item->status_cn = $item->StatusCn;
            $item->country_cn = $item->Country ? $item->Country->name :'';
            unset($item->Country);
        }
        return $list;
    }

    /**
     * 查询搜索国家
     */
    public function getCountryList($data){
        try{
            $countryName = isset($data['country_name']) ? trim($data['country_name']) : '';
//            if(!$countryName) throw new SupplyException('请输入要搜索的国家名称');
            $query = CountryModel::select("code","name");
            if($countryName)$query = $query->where("name","like","%".$countryName."%");
            $list = $query->get();
            if($list->isEmpty()) throw new SupplyException('没有查询到数据');
            return $list->toArray();
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 获取计量单位
     */
    public function getunit(){
        try{
            $list = UnitModel::where("status",0)->select("code","name")->get();
            unset($list[6]);unset($list[29]);unset($list[17]);unset($list[0]);unset($list[5]);
            if($list->isEmpty()) throw new SupplyException('没有查询到数据');
            $list = $list->toArray();
            array_unshift($list,['code'=>'006','name'=>'套']);
            array_unshift($list,['code'=>'001','name'=>'台']);
            array_unshift($list,['code'=>'018','name'=>'卷']);
            array_unshift($list,['code'=>'030','name'=>'米']);
            array_unshift($list,['code'=>'007','name'=>'个']);
            return $list;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 搜索产品信息  品名 品牌 型号 模糊匹配
     *
     */
    public function searchGoodInfo($data){
        try{
            $search_key_field = [1=>"goods_title",2=>"brand_name",3=>"goods_name"];
            $goods_title = trim($data['goods_title']);//品名
            $goods_type = trim($data['goods_type']);//品牌
            $brand = trim($data['brand']);//型号
            $search_key = intval($data['search_key']);//搜搜的字段

            //验证
            if($goods_title == "" && $goods_type=="" && $brand == "" ) throw new \Exception("没有更多数据");
            if(!in_array($search_key,[1,2,3])) throw new \Exception("没有更多数据22");

            $model = new \Supplychain\Model\Tax\CustomsItemsModel;
            $field = array_get($search_key_field,$search_key);
            $list = $model->SearchGoodsTitle($goods_title,$field)->SearchGoodsType($goods_type,$field)->SearchBrand($brand,$field)
                ->select(DB::raw("distinct {$field}"))->limit(20)->get()->toArray();
            $arr=[];
            foreach($list as $item){
                if($item[$field]) array_push($arr,$item[$field]);
            }
            return $arr;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @param $data
     * 查询用户是否已经有相关的产品信息
     * 如果有就返回产地和计量单位
     */
    public function getGoodExtensionInfo($data){
        try {
            $orderGoods = ProductModel::where("user_id", $this->user_id)->where([
                'goods_title' => $data['goods_title'],
                'goods_type' => $data['goods_type'],
                'brand' => $data['brand'],
            ])->select("origin", "measurement")->first();
            if(!$orderGoods) throw new SupplyException('没有查询到更多数据');
            $orderGoods->jldw = UnitModel::getName($orderGoods->measurement);
            return $orderGoods->toArray();
        }catch(SupplyException $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    // 获取供应链首页信息
    public function getHomeInfo($user_id)
    {
        try{
            if (!$user_id) throw new SupplyException('缺少参数');

            //检测服务协议，失效就发送钉钉消息
            try{
                (new \Supplychain\Repository\OrderRepository)->checkUserServiceAgreement($user_id);
            }catch (SupplyException $exception){
                DingNotify::loginCheckServiceNotify(self::getCustomerName());
            }

            // 资产信息
            $customer = CustomerModel::where('user_id', $user_id)->first();

            // 商务QQ
            $datas['business_qq'] = '';
            
            if (isset($customer) && $customer['follow_people']) {
                $CmsModel = new CmsModel();

                $where['name'] = $customer['follow_people'];
                $cmsInfo = $CmsModel->getData('user_info', $where, 'find');

                $datas['business_qq'] = $cmsInfo ? $cmsInfo['qq'] : '';
            }

            $datas['usd_account'] = $customer ? $customer['usd_account'] : 0; // 美元
            $datas['rmb_account'] = $customer ? $customer['rmb_account'] : 0; // 人民币
            $datas['have_rmb_account'] = $customer ? $customer['have_rmb_account'] : 0; // 已走账金额

            // 订单信息
            $datas['no_pass'] = OrderModel::where(['company_id' => $this->company_id, 'status' => -1])->count(); // 审核不通过
            $datas['pick_up'] = OrderModel::where('company_id',$this->company_id)
                ->where("status",">=",OrderModel::$PassStatus)->where("status","<",OrderModel::$SuccessStatus)->count(); // 待自提订单
            $datas['order_count'] = OrderModel::where('company_id', $this->company_id)->count(); // 订单数

            return $datas;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception('获取供应链首页信息失败');
        }
    }

    // 收款单列表
    public function getReceiptList($data)
    {
        try{
            $page         = I('page', 0, 'intval');
            $limit        = I('limit', 10, 'intval');  
            $bill_sn      = I('bill_sn', ''); 
            $status       = I('status', ''); 
            $start_time   = I('start_time', '', 'trim');
            $end_time     = I('end_time', '', 'trim');
            
            $field = ["rec_id", "bill_id", "bill_sn", "currency_id", "erp_customer_id", "rec_com_name", "rec_time", "remit_amount", "exchange_rate", "exchange_amount", "customer_rate", "customer_amount", "user_id", "status", "create_time"];
                
            $query = ReceiptModel::SearchBillSn($bill_sn)->SearchStatus($status)->SearchTime($start_time, $end_time);

            $list = $query->where('company_id', $this->company_id)->select($field)->orderBy('rec_id', 'desc')->paginate($limit,[],'page',$page)->toArray();

            $count = $list['total'];

            if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
            if(empty($list['data'])) throw new SupplyException('没有更多数据');

            foreach ($list['data'] as &$v) {
                $v['rec_time']    = $v['rec_time'] ? date('Y-m-d H:i:s', $v['rec_time']) : '';
                $v['currency_cn'] = $v['currency_id'] ? C('supply_currency')[$v['currency_id']] : '未知';
                $v['status_cn']   = $v['status'] == 0 ? '已付款' : '取消付款';
            }

            return [$list['data'], $count];
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @param $supply_id
     * 获取供应商银行账号列表
     */
    public function getSupplyBankList($supply_id){
        try{
            if(!$supply_id) throw new SupplyException('没找到对应供应商');
            $bankList = SupplierBankModel::where("supplier_id",intval($supply_id))
                ->whereIn('status',[0,1])
                ->orderBy('use_time','desc')
                ->groupBy("bank_account")
                ->get();
            if($bankList->isEmpty()) throw new SupplyException("没有更多数据");

            foreach ($bankList as &$value){
                $value->recipient_country_cn = CountryModel::where('code',$value->recipient_country)->value('name');
            }

            return $bankList;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * @param $supply_id
     * 获取供应商银行详情
     */
    public function getOrderSupplyBankInfo($supply_id){
        try{

        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /*
     * 搜索商品归类信息
     */
    public function searchGoodsClass($request){
        try{
            $goods_type = isset($request['goods_type']) ? $request['goods_type'] : '';
            $goods_title = isset($request['goods_title']) ? $request['goods_title'] : '';
            $brand = isset($request['brand']) ? $request['brand'] : '';
            if(!$goods_type && !$goods_title && !$brand){
                throw new SupplyException('没有更多数据');
            }
            $query = CustomsItemsModel::SearchGoodsTypePage($goods_type)
                ->SearchGoodsTitlePage($goods_title)->SearchBrandPage($brand)->with(['customs123'=>function($q){
                    $q->select("id","supervision_con","tax_rate_low","tax_rate_land","tax_rate_added");
                }])->select("id","customs_id","goods_name","goods_title","brand_name","is_control")->orderBy("id",'desc');
            $page = I('get.page',0,'intval');
            $limit = I('get.limit',15,'intval');
            $count = $query->count();
            if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
            $list = $query->paginate($limit,[],'page',$page)->toArray();
            return $list;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /*
    * 新增消息定制
    */
    public function addUserMessage($request){
        try{
            $request['custom_function'] = array_filter($request['custom_function'],function($val){
                if($val) return true;
            });
            sort($request['custom_function']);
            $data = (new \Supplychain\Service\CustomerService)->UserMsgValidator($request);
            if(!is_array($data['custom_function']) || empty($data['custom_function'])){
                throw new SupplyException('请选择定时功能');
            }

            //验证手机号
            if($data['type'] == 1 && !preg_match_all("/^1[3456789]\d{9}$/", trim($data['send_user']))){
                throw new SupplyException('请填写正确的手机号');
            }

            //验证邮箱
            $email = "/^([0-9A-Za-z\\-_\\.]+)@([0-9a-z]+\\.[a-z]{2,3}(\\.[a-z]{2})?)$/i";
            if($data['type'] == 2 && !preg_match_all($email, trim($data['send_user']))){
                throw new SupplyException('请填写正确的邮箱');
            }



            $bk = UserMessageModel::updateOrcreate([
                'company_id'=>$this->company_id,
                'type'=>$data['type'],
                'send_user'=>$data['send_user'],
            ],[
                'user_id'=>$this->user_id,
                'custom_fun'=>implode(",",$data['custom_function']),
            ]);
            if($bk == false) throw new SupplyException('操作失败');

        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 删除消息
     */
    public function delMsginfo($data){
        try{
            return !!UserMessageModel::where("id",$data['id'])->where("company_id",$this->company_id)->delete();
        }catch(SupplyException $e){
            throw $e;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /*
     * 获取消息定制配置
     */
    public function getuserMsgconfig(){
        return [
            "type"=>UserMessageModel::$TYPE,
            "func"=>UserMessageModel::$CUSTOM_FUN,
        ];
    }


    /*
     * 获取消息定制详情
     */
    public function getuserMsgInfo($request){
        try{
            $id = isset($request['id']) ? intval($request['id']) : 0;
            if(!$id) throw new SupplyException('没有更多数据');
            $info = UserMessageModel::where("company_id",$this->company_id)->where("id",$id)->select("id","type","send_user","custom_fun")->firstOrFail();
            return $info->toArray();
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    /*
     * 获取用户消息定制列表
     */
    public function getuserMsglist(){
        try{
            $query = new UserMessageModel();
            $page = I('get.page',0,'intval');
            $limit = I('get.limit',15,'intval');
            $list = $query->where("company_id",$this->company_id)->select("*")->orderBy("id",'desc')->paginate($limit,[],'page',$page)->toArray();
            if($page > ceil($list['total']/$limit)) throw new SupplyException('没有更多数据');
            return $list;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($e->getMessage());
        }
    }


    //创建信用数据
    public function createCreditApply($postData)
    {
        $checkEmpty = ['company_name','apply_account','id_card_front','id_card_back','legal_representative'];
        foreach ($checkEmpty as $key){
            if (!isset($postData[$key]) || empty($postData[$key])){
                throw new \Exception($key.' is null');
            }
        }


        $account = get_inte_mobile($postData['apply_account'],'0086');

        $code = session_sms($account.'.1');

        if ($code !== pwdhash($postData['msg_code'], C('SMS_SALT'))) {
            throw new \Exception('短信验证码错误，请重新获取');
        }


        if (ComCreditsModel::where('supplier_id',cookie('uid'))->first()){
            throw new \Exception('该账号已经申请，如有疑问请联系客服');
        }

        $this->curlGet($postData["company_name"],$postData['legal_representative']);

        $insertComCreditData['company_name'] = $postData['company_name'];
        $insertComCreditData['apply_account'] = $postData['apply_account'];
        $insertComCreditData['create_time'] = time();
        $insertComCreditData['update_time'] = time();
        $insertComCreditData['supplier_id'] = cookie('uid');
        $insertId = ComCreditsModel::insertGetId($insertComCreditData);


        $insertComBasicData['com_name'] = $postData['company_name'];
        $insertComBasicData['legal_mobile_phone'] = $postData['apply_account'];
        $insertComBasicData['id_card_front'] = $postData['id_card_front'];
        $insertComBasicData['id_card_back'] = $postData['id_card_back'];
        $insertComBasicData['legal_representative'] = $postData['legal_representative'];
        if (isset($postData['pay_duty_pic']))$insertComBasicData['pay_duty_pic'] = $postData['pay_duty_pic'];
        $insertComBasicData['com_credits_id'] = $insertId;
        $insertComBasicData['create_time'] = time();
        ComBasicinfosModel::insertGetId($insertComBasicData);

        return $insertId;
    }



    public function getUserCredit()
    {
        $creditData = ComCreditsModel::where('supplier_id',cookie('uid'))->select('total_credit_score','ichunt_hunting_core','recommended_amount','status')->first();
        return $creditData;
    }

    public function curlGet($companyName,$name)
    {
        $this->redistGet();
        $url = 'http://open.api.tianyancha.com/services/open/ic/baseinfoV2/2.0?name='.urlencode($companyName);
        $headers = [
            'Authorization:4920e638-52c6-43c5-bdec-78affa06bdbe',
        ];
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $dom = curl_exec($ch);
        curl_close($ch);
        $json = json_decode($dom,true);
        if (isset($json['result']['legalPersonName'])){
            if($name != $json['result']['legalPersonName']){
                throw new \Exception('该公司法人填写错误，如有疑问请联系客服');
            }
        }else{
            throw new \Exception('该公司法人不存在，如有疑问请联系客服');
        }
    }

    public function redistGet()
    {
        $key = 'supply:user:credit:apply'.cookie('uid');

        $Redis = new \Redis();
        $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $Redis->auth(C('REDIS_PASSWORD'));

        if (intval($num = $Redis->get($key)) > 3){
            throw new \Exception('当天的申请已经超过三次，请明天重试');
        }

        $beginToday=mktime(0,0,0,date('m'),date('d'),date('Y'));
        $expireTime = 86400 - (time()-$beginToday);
        //如果为空
        if (empty($num)){
            $Redis->set($key,1);
        }else{
            $Redis->set($key,($num+1));
        }
        $Redis->expire($key,$expireTime);
    }


    //增加用户信用
    public function addUserCredit()
    {
        ini_set('max_execution_time', 300);
        $waitAddUser = CompanyModel::join('customer','company.company_id',"=",'customer.company_id')
            ->select('customer_code','erp_client_code','company_full_name')
            ->get()
            ->toArray();

        $customerCode = [
            'GGN0000162'=>'WT00143',
            'GGN0000399'=>'WT00231',
            'GGN0000495'=>'WT00260',
            'GGN0000159'=>'WT00211',
            'GGN0000650'=>'WT00461',
            'GGN0001769'=>'WT01480',
        ];

        foreach ($waitAddUser as $value){

            //是否在重复里面,在的话跳过
            if (array_key_exists($value['customer_code'],$customerCode) && ($customerCode[$value['customer_code']] != $value['erp_client_code'])){
                continue;
            }

            //查找是否有公司
            if (!mb_strpos($value['company_full_name'],'公司')){
                continue;
            }

            //如果公司已经存在则跳过
            if ($name = ComCreditsModel::where('company_name',$value['company_full_name'])->value('id')){
                continue;
            }

            $insertComCreditData = [];
            $insertComCreditData['company_name'] = $value['company_full_name'];
            $insertComCreditData['erp_company_code'] = $value['customer_code'];
            $insertComCreditData['create_time'] = time();
            $insertComCreditData['update_time'] = time();
            $insertComCreditData['status'] = '5';
            $insertId = ComCreditsModel::insertGetId($insertComCreditData);

            $insertComBasicData = [];
            $insertComBasicData['com_credits_id'] = $insertId;
            $insertComBasicData['create_time'] = time();
            ComBasicinfosModel::insertGetId($insertComBasicData);
        }

    }

    public function updateTycNum()
    {
        $companyInfo = ComCreditsModel::where('create_time','>=',time()-86400)->select('company_name','id')->get()->toArray();
        foreach ($companyInfo as $value){
            ComBasicinfosModel::where('com_credits_id',$value['id'])->update([
                'com_name'=>$value['company_name'],
                'tyc_select_num'=>0
            ]);
        }
    }

}