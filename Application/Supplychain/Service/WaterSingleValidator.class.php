<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class WaterSingleValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            "amount"=>'required|numeric', // 金额
            "attachment"=>'required',
            "pay_cost"=>'required', // 预付款单ID
        ];

        $this->title = [
            "amount"=>'金额',
            "attachment"=>'水单附件',
            "pay_cost"=>'预付款单',
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });
        // if(isset($this->data['arrival_time'])){
        //     $this->data['arrival_time'] = strtotime($this->data['arrival_time']);
        // }
        $this->fieldArr = $this->data;
    }
}