<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class OrderValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            "currency_id"=>'required|numeric',
            "supplier_name"=>'required',
            "supplier_id"=>'required|numeric',
//            "arrival_time"=>'required',
            "overseas_settlement_type"=>'required|numeric',
            "overseas_settlement_days"=>'required|numeric',
            "order_invoice_file"=>'required',

        ];

        $this->title = [
            "currency_id"=>'币别',
            "supplier_name"=>'供应商名称',
            "supplier_id"=>'供应商id',
//            "arrival_time"=>'预计到货时间',
            "overseas_settlement_type"=>'境外结算方式',
            "overseas_settlement_days"=>'境外结算期限',
            "order_invoice_file"=>'订单发票',
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });
        if(isset($this->data['arrival_time'])){
            $this->data['arrival_time'] = strtotime($this->data['arrival_time']);
        }
        $this->fieldArr = $this->data;
    }
}