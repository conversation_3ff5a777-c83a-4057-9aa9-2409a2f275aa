<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class CustomerValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            "company.company_full_name"=>'required',#公司全称
            "company.address"=>'required',#公司地址
            "company.legal_person"=>'required',#公司法人
            "company.industry_involved"=>'required|numeric',#所属行业  1制造工厂 2贸易商  3代理商
//            "company.bus_license_number"=>'required',#营业执照号
            "company.bus_license_image"=>'required',#营业执照
            #"company.organization_code"=>'required',#组织机构代码
//            "company.country_id"=>'required',#国家
//            "company.city_id"=>'required',#省份
//            "company.area_id"=>'required',#城市


            //******** 去除验证
//            "invoice.type"=>'required|numeric',#发票类型
//            "invoice.tax_certificate_no"=>'required',#纳税登记证号
//            "invoice.invoice_address"=>'required',#开票地址
//            "invoice.invoice_mobile"=>'required',#开票电话
//            "invoice.bank_name"=>'required',#开户行名称
//            "invoice.bank_number"=>'required|numeric',#开户行账号
//            "invoice.invoice_image"=>'required',#发票资料图片


//            "contact.contact_name"=>'required',#联系人称呼
            "contact.contact_phone"=>'required|numeric',#联系人手机
//            "contact.special_plane"=>'required',#座机
            "contact.source"=>'required|numeric',#从哪里了解到猎芯供应链的 1百度搜索 2朋友推荐  3展会  4其它

        ];

        $this->title = [
            "company.company_full_name"=>'公司全称',#公司全称
            "company.address"=>'公司地址',#公司地址
            "company.legal_person"=>'公司法人',#公司法人
            "company.industry_involved"=>'所属行业',#所属行业  1制造工厂 2贸易商  3代理商
            "company.bus_license_number"=>'营业执照号',#营业执照号
            "company.bus_license_image"=>'营业执照',#营业执照
            "company.organization_code"=>'组织机构代码',#组织机构代码
            "company.country_id"=>'国家',#国家
            "company.city_id"=>'城市',#省份
            "company.area_id"=>'区域',#城市

            "invoice.type"=>'发票类型',#发票类型
            "invoice.tax_certificate_no"=>'纳税登记证号',#纳税登记证号
            "invoice.invoice_address"=>'开票地址',#开票地址
            "invoice.invoice_mobile"=>'开票电话',#开票电话
            "invoice.bank_name"=>'开户行名称',#开户行名称
            "invoice.bank_number"=>'开户行账号',#开户行账号
            "invoice.invoice_image"=>'发票资料图片',#发票资料图片


            "contact.contact_name"=>'联系人称呼',#联系人称呼
            "contact.contact_phone"=>'联系人手机',#联系人手机
            "contact.special_plane"=>'座机',#座机
            "contact.source"=>'从哪里了解到猎芯供应链',#从哪里了解到猎芯供应链的 1百度搜索 2朋友推荐  3展会  4其它
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });

        //所属行业
        $this->data['company']['industry_involved'] =  intval($this->data['company']['industry_involved']);
//        $this->data['company']['user_id'] =  cookie('uid');
        //发票类型
        $this->data['invoice']['user_id'] = cookie('uid');
        $this->data['invoice']['type'] = intval($this->data['invoice']['type']);
        //从哪里了解到猎芯供应链的
        $this->data['contact']['user_id'] = cookie('uid');
        $this->data['contact']['source'] = intval($this->data['contact']['source']);

        $this->fieldArr = $this->data;
    }
}