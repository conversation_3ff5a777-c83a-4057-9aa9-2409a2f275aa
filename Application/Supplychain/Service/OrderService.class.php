<?php
namespace Supplychain\Service;

use Exception;
use Supplychain\Exception\SupplyException;
use Supplychain\Model\HongKongDeliveryModel;
use Supplychain\Model\InlandDeliveryModel;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\HongKongOrderLogic;
use Supplychain\Model\OrderBankInfoModel;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Service\OrderInfoValidator;
use Supplychain\Service\OrderGoodsValidator;
use Supplychain\Service\WaterSingleValidator;
use Supplychain\Model\OrderModel;
use Supplychain\Model\OrderAddressModel;
use Illuminate\Database\Capsule\Manager as DB;


class OrderService
{

    public function __construct()
    {
        $this->user_id = cookie('uid');
    }

    /**
     * 验证数据合法性
     */
    public function validator($validator,$data){
        try{
            $checkData = LaravelApp($validator)->setData($data);
            $ruleMessage = $checkData->getMessage();
            if($ruleMessage !== true){
                if(is_object($ruleMessage)){
                    throw new SupplyException($ruleMessage->first(),20009);
                }
                throw new SupplyException("字段类型错误",20009);
            }
            $fields = $checkData->fieldArr;
            return $fields;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }


    /**
     * 录入订单商品验证
     */
    public function OrderInfoValidator($data){
        try{
            if (empty($data['supplier_id'])){
                throw new Exception('请选择下拉中已有的供应商');
            }
            return call_user_func_array([$this,'validator'],[OrderInfoValidator::class,$data]);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }

    }


    /**
     * 字段处理
     * 下单批量增加商品数据
     *
     */
    public function getCreateOrderGoodsFields($data){
        try{
            $arr = [];
            $fields = array_keys($data);
            $count = count($data['goods_title']);
            if($count > 20) throw new SupplyException("一次最多添加20个商品");
            $arr = [];
            for($i=0;$i<$count;$i++){
                foreach($fields as $field){
                    $keyArr = CommonLogic::orderDiffGetCreateOrderGoodsFields();
                    if(!in_array($field,$keyArr)) continue;
                    if(!isset($data[$field][$i])){
                        throw new SupplyException("请填写完整后在提交");
                    }
                    $arr[$i][$field] = $data[$field][$i] ? $data[$field][$i] : '';
                }
            }
            return $arr;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     * 添加订单商品验证 数据合法性
     */
    public function checkOrderGoodsValidator($data){
        try{
            return call_user_func_array([$this,'validator'],[OrderGoodsValidator::class,$data]);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     * 添加水单验证 数据合法性
     */
    public function WaterSingleValidator($data){
        try{
            return call_user_func_array([$this,'validator'],[WaterSingleValidator::class,$data]);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     * 前台用户订单 改单 生成新的订单相关附表
     */
    public function EorderCNewOrder($order_id){
        try{
            $neworder_id = 0;
            DB::connection('SUPPLYCHAIN')->transaction(function()use($order_id,&$neworder_id){
                //新增订单
                $orderInfo = OrderModel::findOrFail($order_id);
                $orderInfo->create_time = time();
                $orderInfo->update_time = time();
                $orderData = $orderInfo->toArray();
                $orderData['order_sn'] = OrderModel::findSn();
                $orderData['id_edit_order'] = 1;//是否是改单的订单
                $orderData['edit_order_id'] = $order_id;//关联的改单id
                $orderData['status'] = OrderModel::$WaitToSubmitStatus;//待提交
                $bk = OrderModel::create($orderData);
                if(!$bk) throw  new SupplyException("新增改单记录订单附表失败");
                $neworder_id = $bk->order_id;
                $orderInfo->edit_order_id = $bk->order_id;

                //新增订单关联的商品
                $orderGoods = OrderGoodsModel::where("order_id",$order_id)->get();
                foreach($orderGoods as $goods){
                    $goods->order_id = $bk->order_id;
                    if(!OrderGoodsModel::create($goods->toArray())){
                        throw  new SupplyException("新增改单记录商品附表失败");
                    }

                }

                //订单收货地址 国内
                $orderAddress = OrderAddressModel::where("order_id",$order_id)->first();
                if($orderAddress){
                    $orderAddressData = $orderAddress->toArray();
                    $orderAddressData['order_id'] = $bk->order_id;
                    if(!OrderAddressModel::create($orderAddressData)){
                        throw  new SupplyException("新增改单记录收货地址附表失败");
                    }
                }

                //订单扩展表
                $orderBankInfo = OrderBankInfoModel::where("order_id",$order_id)->first();
                if($orderBankInfo){
                    $orderBankInfoData = $orderBankInfo->toArray();
                    $orderBankInfoData['order_id'] = $bk->order_id;
                    if(!OrderBankInfoModel::create($orderBankInfoData)){
                        throw  new SupplyException("新增改单记录订单扩展表失败");
                    }
                }

                //香港交货方式
                $hongKongDelivery = HongKongDeliveryModel::where("order_id",$order_id)->first();
                if($hongKongDelivery){
                    $hongKongDeliveryData = $hongKongDelivery->toArray();
                    $hongKongDeliveryData['order_id'] = $bk->order_id;
                    HongKongDeliveryModel::create($hongKongDeliveryData);
                }

                //国内物流信息
                $inlandDeliveryM = InlandDeliveryModel::where("order_id",$order_id)->first();
                if($inlandDeliveryM){
                    $inlandDeliveryMData = $inlandDeliveryM->toArray();
                    $inlandDeliveryMData['order_id'] = $bk->order_id;
                    InlandDeliveryModel::create($inlandDeliveryMData);
                }

                $orderInfo->save();
            });

            return $neworder_id;

        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(Exception $e){
            \Log::info($e->getMessage());
            throw $e;
        }
    }


    /**
     * 同步改单表数据到主订单
     */
    public function syncChangeOrderToMasterOrder($MasterOrder,$ChangeOrder){
        try{
            //同步主订单表
            $MasterOrder->update([
                "currency_id"=>$ChangeOrder->currency_id,
                "order_price"=>$ChangeOrder->order_price,
                "supplier_name"=>$ChangeOrder->supplier_name,
                "supplier_id"=>$ChangeOrder->supplier_id,
                "arrival_time"=>$ChangeOrder->arrival_time,
                "overseas_settlement_type"=>$ChangeOrder->overseas_settlement_type,
                "overseas_settlement_days"=>$ChangeOrder->overseas_settlement_days,
                "order_invoice_file"=>$ChangeOrder->order_invoice_file,
                "supplier_contact"=>$ChangeOrder->supplier_contact,
                "supplier_phone"=>$ChangeOrder->supplier_phone,
                "landline_code"=>$ChangeOrder->landline_code,
                "supplier_email"=>$ChangeOrder->supplier_email,
                "hk_delivery_address_id"=>$ChangeOrder->hk_delivery_address_id,
                "hongkong_delivery_type"=>$ChangeOrder->hongkong_delivery_type,
                "inland_delivery_type"=>$ChangeOrder->inland_delivery_type,
                "attachment"=>$ChangeOrder->attachment,
                "taking_delivery"=>$ChangeOrder->taking_delivery,
                "invoice_remark"=>$ChangeOrder->invoice_remark,
                "is_push"=>1,
                "edit_order_id"=>0,
            ]);

            //同步子单订单关联的商品到主订单
            $orderGoods = OrderGoodsModel::where("order_id",$ChangeOrder->order_id)->get();
            OrderGoodsModel::where("order_id",$MasterOrder->order_id)->delete();
            foreach($orderGoods as $goods){
                $goods->order_id = $MasterOrder->order_id;
                if(!OrderGoodsModel::create($goods->toArray())){
                    throw  new SupplyException("同步订单失败");
                }

            }


            //重新计算订单总价格


            //订单收货地址 国内
            $orderAddress = OrderAddressModel::where("order_id",$ChangeOrder->order_id)->first();
            if($orderAddress){
                OrderAddressModel::where("order_id",$MasterOrder->order_id)->delete();
                $orderAddressData = $orderAddress->toArray();
                $orderAddressData['order_id'] = $MasterOrder->order_id;
                if(!OrderAddressModel::create($orderAddressData)){
                    throw  new SupplyException("同步改单记录收货地址附表失败");
                }
            }


            //订单扩展表
            $orderBankInfo = OrderBankInfoModel::where("order_id",$ChangeOrder->order_id)->first();
            if($orderBankInfo){
                OrderBankInfoModel::where("order_id",$MasterOrder->order_id)->delete();
                $orderBankInfoData = $orderBankInfo->toArray();
                $orderBankInfoData['order_id'] = $MasterOrder->order_id;
                if(!OrderBankInfoModel::create($orderBankInfoData)){
                    throw  new SupplyException("同步改单记录订单扩展表失败");
                }
            }

            //香港交货方式
            $hongKongDelivery = HongKongDeliveryModel::where("order_id",$ChangeOrder->order_id)->first();
            if($hongKongDelivery){
                HongKongDeliveryModel::where("order_id",$MasterOrder->order_id)->delete();
                $hongKongDeliveryData = $hongKongDelivery->toArray();
                $hongKongDeliveryData['order_id'] = $MasterOrder->order_id;
                HongKongDeliveryModel::create($hongKongDeliveryData);
            }

            //国内物流信息
            $inlandDeliveryM = InlandDeliveryModel::where("order_id",$ChangeOrder->order_id)->first();
            if($inlandDeliveryM){
                InlandDeliveryModel::where("order_id",$MasterOrder->order_id)->delete();
                $inlandDeliveryMData = $inlandDeliveryM->toArray();
                $inlandDeliveryMData['order_id'] = $MasterOrder->order_id;
                InlandDeliveryModel::create($inlandDeliveryMData);
            }


        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage());
        }catch(Exception $e){
            \Log::info($e->getMessage());
            throw $e;
        }
    }

}