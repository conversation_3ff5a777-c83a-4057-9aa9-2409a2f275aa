<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class CompanyValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            "company.company_full_name"=>'required',#公司全称
            "company.address"=>'required',#公司地址
            "company.legal_person"=>'required',#公司法人
            "company.industry_involved"=>'required|numeric',#所属行业  1制造工厂 2贸易商  3代理商
            "company.bus_license_number"=>'required',#营业执照号
            "company.bus_license_image"=>'required',#营业执照
        ];

        $this->title = [
            "company.company_full_name"=>'公司全称',#公司全称
            "company.address"=>'公司地址',#公司地址
            "company.legal_person"=>'公司法人',#公司法人
            "company.industry_involved"=>'所属行业',#所属行业  1制造工厂 2贸易商  3代理商
            "company.bus_license_number"=>'营业执照号',#营业执照号
            "company.bus_license_image"=>'营业执照',#营业执照

        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });

        $this->fieldArr = $this->data;
    }
}