<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class ShippingValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            "consignee"=>'required',#收货人姓名
            "province"=>'required|numeric',
            //"city"=>'required|numeric',
            //"district"=>'required|numeric',
            "detail_address"=>'required',
//            "intl_code"=>'required',
            "mobile"=>'required',
//            "seat_number_code"=>'required',
//            "seat_number"=>'required',
            "is_default"=>'required',
        ];

        $this->title = [
            "consignee"=>'收货人信息',
            "province"=>'省',
            //"city"=>'市',
            //"district"=>'区',
            "detail_address"=>'详细地址',
            "intl_code"=>'手机号 国际群号',
            "mobile"=>'手机号',
//            "seat_number_code"=>'座机号区段',
            "seat_number"=>'座机号',
            "is_default"=>'设为默认地址',
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });

        $this->data['user_id']=cookie('uid');
        $this->data['company_id'] = (new \Supplychain\Repository\UserRepository)->getCompanyId($this->data['user_id']);

        $this->fieldArr = $this->data;
    }
}