<?php
namespace Supplychain\Service;


class DingNotify
{

    private $notifyType = [
        'autoSyncMessage'=>[
            'mobile'=>""
        ]
    ];

    const REQURL = 'https://oapi.dingtalk.com/robot/send?access_token=688445bb86773f7190bccc06a6d923e99d08bec4d6e8644fde5bcf6023152bfc';



    //登陆检测服务协议失败发送钉钉通知
    static public function loginCheckServiceNotify($user)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$user."用户登陆中，但服务协议已经失效, @*********** 请手动推送"),
            'at'=>["atMobiles"=>[***********]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }

    //签约服务协议发送钉钉通知
    static public function signServiceNotify($user)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$user."用户签约服务协议, @*********** 请手动推送"),
            'at'=>["atMobiles"=>[***********]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }

    //登陆检测服务协议失败发送钉钉通知
    static public function addSupplyNotify($user)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$user."供应商信息已经提交, @*********** 请审核"),
            'at'=>["atMobiles"=>[***********]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }

    //登陆检测服务协议失败发送钉钉通知
    static public function addSupplyBankNotify($user)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$user."供应商银行信息已经提交, @*********** 请审核"),
            'at'=>["atMobiles"=>[***********]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }

    //获取供应商信息失败通知
    static public function getSupplyFailNotify()
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>"获取供应商信息失败，尽快处理, @*********** 请尽快处理"),
            'at'=>["atMobiles"=>[***********]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }


    //获取Unionid失败
    static public function getUnionidFalse()
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>"获取供应商信息失败，尽快处理, @*********** 请尽快处理"),
            'at'=>["atMobiles"=>[***********]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }


    //获取供应商信息失败通知
    static public function getErpSnFailNotify($user)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$user."用户获取入仓号失败，原因是没有erp登陆ID, @18002590124 请及时处理"),
            'at'=>["atMobiles"=>[18002590124]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }

    //用户下单失败
    static public function createOrderFailNotify($user,$orderSn,$reason)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$user.'-'.$orderSn."用户下单失败, @18002590124 请及时处理,具体原因是:".$reason),
            'at'=>["atMobiles"=>[18002590124]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }

    //自动推送订单失败
    static public function pushOrderFailNotify($orderId,$reason)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$orderId."订单推送金蝶失败, @18002590124 请及时处理,具体原因是:".$reason),
            'at'=>["atMobiles"=>[18002590124]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }

    //自动推送订单失败
    static public function autoPushOrderFailNotify($orderId)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$orderId."自动订单推送金蝶失败, @*********** 请及时处理,具体原因是:创建子进程失败"),
            'at'=>["atMobiles"=>[***********]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }


    //用户自动生成付汇
    static public function autoCreatePaymentApplyFailNotify($orderSn,$reason)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$orderSn."自动生成付汇失败, @*********** 请及时处理,具体原因是:".$reason),
            'at'=>["atMobiles"=>[***********]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }

    //用户手动生成付汇
    static public function createPaymentApplyFailNotify($user,$orderSn,$reason)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$user.'-'.$orderSn."订单手动生成付汇失败, @*********** 请及时处理，具体原因是".$reason),
            'at'=>["atMobiles"=>[***********]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }


    //用户手动修改付汇失败
    static public function updatePaymentApplyFailNotify($user,$orderSn,$reason)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$user.'-'.$orderSn."订单手动修改付汇失败, @*********** 请及时处理，具体原因是".$reason),
            'at'=>["atMobiles"=>[***********]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }

    //用户手动修改付汇失败
    static public function confirmTallyGoods($orderSn)
    {
        $data = array (
            'msgtype' => 'text',
            'text' => array ('content' =>$orderSn."订单存在金蝶不存在平台, @*********** 请确认"),
            'at'=>["atMobiles"=>[***********]],
            'isAtAll'=>false
        );
        self::curlDingDing($data);
    }

    static public function curlDingDing($data)
    {
        if ($_SERVER['SERVER_NAME'] != "api.ichunt.com"){
            return false;
        }

        $ch = curl_init();
        $data_string = json_encode($data);
        curl_setopt($ch, CURLOPT_URL, self::REQURL);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array ('Content-Type: application/json;charset=utf-8'));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 线下环境不用开启curl证书验证, 未调通情况可尝试添加该代码
        curl_setopt ($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }
}