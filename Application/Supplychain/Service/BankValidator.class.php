<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class BankValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            "account_name"=>'required',
            "bank_name"=>'required',
            "bank_number"=>'required|numeric',
            "currency_id"=>'required|numeric',

        ];

        $this->title = [
            "account_name"=>'账户名称',
            "bank_name"=>'银行名称',
            "bank_number"=>'银行账号',
            "currency_id"=>'币别',
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });
        $this->fieldArr = $this->data;
    }
}