<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class OrderGoodsValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            "goods_title"=>'required',
            "goods_type"=>'required',
            "brand"=>'required',
            "origin"=>'required',//产地
            "measurement"=>'required',//计量单位
            "numbers"=>'required|numeric',//数量
            "unit_price"=>'required|numeric',//单价

        ];

        $this->title = [
            "goods_title"=>'品名',
            "goods_type"=>'型号',
            "brand"=>'品牌',
            "origin"=>'产地',
            "measurement"=>'计量单位',
            "numbers"=>'数量',
            "unit_price"=>'单价',
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });
        $this->fieldArr = $this->data;
    }
}