<?php
/**
 * Created by PhpStorm.
 * User: ICHUNT
 * Date: 2018/12/18
 * Time: 13:33
 */

namespace Supplychain\Service;


abstract  class CommonValidator
{

    public $validatorsMessage;
    public $rules;
    public $title;
    public $data;

    public function __construct()
    {
        $this->Validator = \Pcb\Service\Validator::class;
        //初始化数据
        call_user_func_array([$this,'validator_rules'],[]);
    }

    /**
     * 字段
     * 并调用validators验证方法
     * @param $data
     * @return $this
     */
    public function setData($data){
        $this->data = $data;
        call_user_func_array([$this,'check'],[]);
        call_user_func_array([$this,'setFields'],[]);
        return $this;
    }

    /**
     * 调用Validator的validators方法 验证方法在这里
     */
    public function check(){
        $va =  call_user_func_array([$this->Validator,'validators'],[$this->rules,$this->data,$this->title]);
        $this->validatorsMessage = $va;
    }


    /**
     * 获取验证消息
     * @return bool|mixed
     */
    public function getMessage(){
        if($this->validatorsMessage){
            return true;
        }else{
            return call_user_func_array([$this->Validator,'getMessage'],[]);
        }
    }

    /*
     * 实现validator_rules
     * rule规则
     */
    abstract  public function validator_rules();

    /**
     * 处理返回的data字段
     * @return mixed
     */
    abstract  public function setFields();


}