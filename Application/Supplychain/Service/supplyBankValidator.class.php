<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class supplyBankValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
//            "area"=>'required|numeric',
            "bank_name"=>'required',
//            "swift_code"=>'required',
//            "bank_code"=>'required',
            "bank_address"=>'required',
//            "recipient_country"=>'required',
//            "bank_user"=>'required',
            "bank_account"=>'required',
//            "currency_id"=>'required|numeric',
            "bank_information"=>'required',
            "supplier_id"=>'required|numeric',

        ];

        $this->title = [
//            "area"=>'区域',
            "bank_name"=>'银行名称',
            "swift_code"=>'swift_code',
//            "bank_code"=>'银行代码',
            "bank_address"=>'银行地址',
            "recipient_country"=>'收款方所在国家',
//            "bank_user"=>'收款账号名称',
            "bank_account"=>'银行账号',
            "currency_id"=>'币别',
            "bank_information"=>'银行资料',
            "supplier_id"=>'供应商ID',
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });
        $this->fieldArr = $this->data;
    }
}