<?php
namespace Supplychain\Service;

use Exception;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\CustomerModel;
use Supplychain\Repository\UserRepository;
use Supplychain\Service\CustomerValidator;
use Supplychain\Service\CompanyValidator;
use Supplychain\Service\ContactValidator;
use Supplychain\Service\InvoiceValidator;
use Supplychain\Service\ShippingValidator;
use Supplychain\Service\BankValidator;
use Supplychain\Exception\SupplyException;
use Supplychain\Service\supplyBankValidator;
use Supplychain\Service\ProductValidator;
use Supplychain\Service\HongkongDeliveryValidator;
use Supplychain\Service\OrderValidator;
use Supplychain\Service\OrderGoodsValidator;
use Supplychain\Model\SupplierModel;
use Supplychain\Model\SupplierBankModel;
class CustomerService
{

    public function __construct()
    {
        $this->user_id = cookie('uid');
    }



    /**
     * 判断用户是否已为协议用户
     */
    public function checkUser($user_id){
        try{
            $userInfo = CustomerModel::where("user_id",intval($user_id))->firstOrFail();
            if(!$userInfo) throw new \Exception('请先录入基本信息');
            if($userInfo->status != CustomerModel::$PassStatus)throw new \Exception('暂未审核通过');
            if(!$userInfo->company_id) throw new \Exception('账号未绑定委托方，请联系商务');
            return true;
        }catch(\Exception $e){
            \Think\Log::write($e->getMessage(),"WARN");
            return false;
        }
    }





    /**
     * 验证前端数据 并返回处理后的数据
     * @param $company
     * @param $invoice
     * @param $contact
     * @return mixed
     * @throws Exception
     */
    public function customerValidatorDatas($company,$invoice,$contact){
        try{
            $checkData = LaravelApp(CustomerValidator::class)->setData(['company'=>$company,"invoice"=>$invoice,"contact"=>$contact]);
            $ruleMessage = $checkData->getMessage();
            if($ruleMessage !== true){
                if(is_object($ruleMessage)){
                    throw new SupplyException($ruleMessage->first(),20007);
                }
                throw new SupplyException("字段类型错误",20007);
            }
            $fields = $checkData->fieldArr;
            return $fields;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }



    /**
     * 验证
     * 编辑公司
     */
    public function companyValidatorDatas($data){
        try{
            $checkData = LaravelApp(CompanyValidator::class)->setData($data);
            $ruleMessage = $checkData->getMessage();
            if($ruleMessage !== true){
                if(is_object($ruleMessage)){
                    throw new Exception($ruleMessage->first(),20008);
                }
                throw new Exception("编辑公司字段类型错误",20008);
            }
            $fields = $checkData->fieldArr;
            return $fields;
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     * 验证
     * 编辑联系人
     */
    public function contactValidatorDatas($data){
        try{
            $checkData = LaravelApp(ContactValidator::class)->setData($data);
            $ruleMessage = $checkData->getMessage();
            if($ruleMessage !== true){
                if(is_object($ruleMessage)){
                    throw new Exception($ruleMessage->first(),20008);
                }
                throw new Exception("编辑联系人字段类型错误",20008);
            }
            $fields = $checkData->fieldArr;
            return $fields;
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     * 验证
     * 编辑发票
     */
    public function invoiceValidatorDatas($data){
        try{
            $checkData = LaravelApp(InvoiceValidator::class)->setData($data);
            $ruleMessage = $checkData->getMessage();
            if($ruleMessage !== true){
                if(is_object($ruleMessage)){
                    throw new Exception($ruleMessage->first(),20008);
                }
                throw new Exception("编辑发票字段类型错误",20008);
            }
            $fields = $checkData->fieldArr;
            return $fields;
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }


    /**
     *收货地址验证
     */
    public function shippingValidatorDatas($data){
        try{
            $checkData = LaravelApp(ShippingValidator::class)->setData($data);
            $ruleMessage = $checkData->getMessage();
            if($ruleMessage !== true){
                if(is_object($ruleMessage)){
                    throw new Exception($ruleMessage->first(),20009);
                }
                throw new Exception("收货地址字段类型错误",20009);
            }
            $fields = $checkData->fieldArr;
            return $fields;
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }


    /**
     * 获取资料详情 处理相关数据
     */
    public function dealUserInfoField(&$data){
//        foreach($data as &$v){
//            if($v){
//                if(isset($v['user_id'])) unset($v['user_id']);
//                if(isset($v['update_time'])) unset($v['update_time']);
//            }else{
//                $v = [];
//            }
//        }
        return $data;
    }


    /**
     * 银行账号 新增
     */
    public function validator($validator,$data){
        try{
            $checkData = LaravelApp($validator)->setData($data);
            $ruleMessage = $checkData->getMessage();
            if($ruleMessage !== true){
                if(is_object($ruleMessage)){
                    throw new SupplyException($ruleMessage->first(),20009);
                }
                throw new SupplyException("字段类型错误",20009);
            }
            $fields = $checkData->fieldArr;
            return $fields;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     * 银行账号 新增
     */
    public function bankValidator($data){
        try{
            return call_user_func_array([$this,'validator'],[BankValidator::class,$data]);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }

    }

    /**
     * 新增产品
     */
    public function productValidator($data){
        try{
            return call_user_func_array([$this,'validator'],[ProductValidator::class,$data]);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }

    }

    /**
     * 商品提交审核
     */
    public function changeGoodsStatus($goods_ids){
        try{
            $userRepository = LaravelApp('\Supplychain\Repository\UserRepository');
            $bk = $userRepository->changeGoodsStatus($goods_ids);
            if($bk > 0){
                //商品提交审核后 推送商品到诚意关税系统  物料归类
                foreach($goods_ids as $goods_id){
                    $goods_info = $userRepository->getUserGoodsInfo($goods_id);
                    if(!$goods_info) continue;
                    $data = $goods_info->toArray();
                    $arr['goods_type'] = $data['goods_type'];
                    $arr['brand'] = $data['brand'];//品牌
                    $arr['goods_title'] = $data['goods_title'];//品名
                    $arr['user_id'] = $data['user_id'];//型号
                    $arr['goods_id'] = $goods_id;//商品id
                    (new \Supplychain\Repository\OrderRepository)->addUserGoodsToMq(json_encode($arr));
                }

                return true;
            }else{
                return false;
            }
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }


    /**
     * 增加供应商
     */
    public function addSupplier($data){
        try{
            $spaceStr = ' ';
            $supplierInsertData['supplier_name'] = str_replace($spaceStr,' ',stripslashes($data['supplier_name']));
            $supplierInsertData['create_time'] = time();
            $supplierInsertData['user_id'] = $this->user_id;
            $supplierInsertData['company_id'] = (new \Supplychain\Repository\UserRepository)->getCompanyId($this->user_id);
            $supplierInsertData['bus_regist_cert_image'] = $data['bank_information'];
            $supplierInsertData['supplier_address'] = $data['supplier_address'];
            $supplierInsertData['supplier_file'] = $data['supplier_file'];

            //******** 新增海外国内的录入 1大陆2海外
            $supplierInsertData['region'] = $data['region'];

            $nums = SupplierModel::where("supplier_name",$supplierInsertData['supplier_name'])->whereIn('status',[0,1])->first();
            if($nums) throw new Exception("供应商已存在");

            $bk = SupplierModel::insertGetId($supplierInsertData);
            if (empty($bk))throw new Exception("添加供应商失败");

            if (!empty($data['isBankInformation'])){
                $data['supplier_id'] = $bk;
                (new UserRepository())->addSupplierBank($data);
            }

            if($bk && $_SERVER['SERVER_NAME'] == "api.ichunt.com"){
                $postBody   = ['company_id'=>$supplierInsertData['company_id'],'supplier_name'=>$data['supplier_name']];
                post_curl('http://sc.ichunt.net/mobile/sendSupplyRegister', $postBody, array('api-key: crm a1b2c3d4e5f6g7h8i9jk'));
                DingNotify::addSupplyNotify($data['supplier_name']);
            }

            return $bk;
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     * 编辑供应商
     */
    public function editSupplier($data){
        try{
            $company_id = (new \Supplychain\Repository\UserRepository)->getCompanyId($this->user_id);
            $bk = SupplierModel::where("company_id",$company_id)->where("supplier_id",intval($data['supplier_id']))->where('user_id',cookie('uid'))->where('status',0)->update([
                'supplier_name' =>trim($data['supplier_name']),
                'status'=>0,
            ]);
            return $bk > 0 ? true : false;
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            \Think\Log::write($e->getMessage(),'WARN');
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }



    /*
     * 添加供应商银行账号 验证
     */
    public function supplyBankValidator($data){
        try{
            return call_user_func_array([$this,'validator'],[supplyBankValidator::class,$data]);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     * 录入订单商品验证
     */
    public function OrderGoodsValidator($data){
        try{
            return call_user_func_array([$this,'validator'],[OrderGoodsValidator::class,$data]);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }

    }

    /**
     *订单数据验证
     */
    public function OrderValidator($data){
        try{
            return call_user_func_array([$this,'validator'],[OrderValidator::class,$data]);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     *订单数据验证
     */
    public function HongkongDeliveryValidator($data){
        try{
            return call_user_func_array([$this,'validator'],[HongkongDeliveryValidator::class,$data]);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     *订单数据验证  国内物流整批发货
     */
    public function batchDeliveryValidator($data){
        try{
            return call_user_func_array([$this,'validator'],[HongkongDeliveryValidator::class,$data]);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }

    /**
     *订单数据验证  国内物流整批发货
     */
    public function UserMsgValidator($data){
        try{
            return call_user_func_array([$this,'validator'],[UserMessageValidator::class,$data]);
        }catch(SupplyException $e){
            throw new SupplyException($e->getMessage(),$e->getCode());
        }catch(Exception $e){
            throw new Exception($e->getMessage(),$e->getCode());
        }
    }



}