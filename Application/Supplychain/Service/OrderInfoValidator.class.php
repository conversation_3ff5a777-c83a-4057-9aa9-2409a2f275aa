<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class OrderInfoValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            "currency_id"=>'required|numeric',
            "supplier_name"=>'required',
            "supplier_id"=>'required|numeric',
//            "arrival_time"=>'required',
            "overseas_settlement_type"=>'required|numeric',
            "overseas_settlement_days"=>'required|numeric',
            "order_invoice_file"=>'required',
//            "supplier_bank_id"=>'required',
//            "bank_charges"=>'required|in:1,2,3',
//            "pay_ratio"=>'required|numeric|min:0|max:100',
//            "pay_amount"=>'required',
//            "pay_date"=>'required|date_format:Y-m-d',
//            "pay_remark"=>'required',
            "erp_order_sn"=>'required',

        ];

        $this->title = [
            "currency_id"=>'币别',
            "supplier_name"=>'供应商名称',
            "supplier_id"=>'供应商关联id',
//            "arrival_time"=>'预计到货时间',
            "overseas_settlement_type"=>'境外结算方式',
            "overseas_settlement_days"=>'境外结算期限',
            "order_invoice_file"=>'订单发票（INVOICE,PI）',
//            "supplier_bank_id"=>'银行账号',
            "bank_charges"=>'银行手续费',
//            "pay_ratio"=>'支付比率',
            "pay_amount"=>'支付金额',
//            "pay_date"=>'支付日期',
//            "pay_remark"=>'支付备注',
            "erp_order_sn"=>'入仓单号',
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });
        $this->fieldArr = $this->data;
    }
}