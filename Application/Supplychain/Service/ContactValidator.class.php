<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class ContactValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            "contact.contact_name"=>'required',#联系人称呼
            "contact.contact_phone"=>'required|numeric',#联系人手机
            "contact.special_plane"=>'required',#座机
            "contact.source"=>'required|numeric',#从哪里了解到猎芯供应链的 1百度搜索 2朋友推荐  3展会  4其它
        ];

        $this->title = [
            "contact.contact_name"=>'联系人称呼',#联系人称呼
            "contact.contact_phone"=>'联系人手机',#联系人手机
            "contact.special_plane"=>'座机',#座机
            "contact.source"=>'从哪里了解到猎芯供应链',#从哪里了解到猎芯供应链的 1百度搜索 2朋友推荐  3展会  4其它
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });

        $this->data['contact']['source'] = intval($this->data['contact']['source']);


        $this->fieldArr = $this->data;
    }
}