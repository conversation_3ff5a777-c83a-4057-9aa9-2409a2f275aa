<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class HongkongDeliveryValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            "instead_contact"=>'required',
            "instead_address"=>'required',
            "instead_mobile"=>'required',
            "instead_iandline"=>'required',
            "instead_area_code"=>'required',
            "delivery_type"=>'required',

        ];

        $this->title = [
            "instead_contact"=>'代为提货联系人',
            "instead_address"=>'代为提货提货地址',
            "instead_mobile"=>'代为提货手机号',
            "instead_iandline"=>'代为提货座机',
            "instead_area_code"=>'代为提货座机区号',
            "delivery_type"=>'香港交货方式',
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });
        $this->fieldArr = $this->data;
    }
}