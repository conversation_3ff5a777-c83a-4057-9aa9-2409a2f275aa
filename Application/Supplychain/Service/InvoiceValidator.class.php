<?php
namespace Supplychain\Service;
use \Pcb\Service\Validator as v;
use Pcb\Service\AAA;
use Pcb\Service\CommonValidator;

class InvoiceValidator extends CommonValidator
{

    public $fieldArr;
    public function __construct()
    {
        parent::__construct();

    }

    /*
     * 实现validator_rules
     * rule规则
     */
    public function validator_rules(){
        //验证规则
        $this->rules = [
            "invoice.type"=>'required',#发票类型
            "invoice.tax_certificate_no"=>'required',#纳税登记证号
            "invoice.invoice_address"=>'required',#开票地址
            "invoice.invoice_mobile"=>'required',#开票电话
            "invoice.bank_name"=>'required',#开户行名称
            "invoice.bank_number"=>'required|numeric',#开户行账号
            "invoice.invoice_image"=>'required',#发票资料图片
        ];

        $this->title = [
            "invoice.type"=>'发票类型',#发票类型
            "invoice.tax_certificate_no"=>'纳税登记证号',#纳税登记证号
            "invoice.invoice_address"=>'开票地址',#开票地址
            "invoice.invoice_mobile"=>'开票电话',#开票电话
            "invoice.bank_name"=>'开户行名称',#开户行名称
            "invoice.bank_number"=>'开户行账号',#开户行账号
            "invoice.invoice_image"=>'发票资料图片',#发票资料图片
        ];
    }

    /**
     * 处理返回的data字段
     * @return mixed
     */
    public function setFields(){

        array_walk_recursive($this->data,function(&$val,$key){
            $val = trim($val);
        });

        $this->data['invoice']['type'] = intval($this->data['invoice']['type']);


        $this->fieldArr = $this->data;
    }
}