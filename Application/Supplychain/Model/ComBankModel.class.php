<?php
namespace Supplychain\Model;


class ComBankModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'com_bank';
    protected $primaryKey = 'company_bank_id';
    protected $guarded = ['company_bank_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $fillable = ["account_name","bank_name","bank_number","currency_id","user_id","company_id"];


    public static $STATUS=[
            1=>'审核中',
            2=>'审核通过',
            3=>'审核不通过',
    ];


    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public function getStatusCnAttribute(){
        return array_get(static::$STATUS,intval($this->status));
    }


}