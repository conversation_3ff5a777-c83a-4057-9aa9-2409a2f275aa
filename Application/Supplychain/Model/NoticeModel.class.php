<?php
/**
 * Created by 2021/6/25.
 * User: Joneq
 * Info: 2021/6/25
 * Time: 下午1:55
 */

namespace Supplychain\Model;


class NoticeModel extends BaseModelLaravel
{

    protected $connection = "SUPPLYCHAIN";
    protected $table = 'notice';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];



    //获取数据列表
    static public function getList($data)
    {
        $obj = self::where('status',1)->orderBy('notice_time','desc');
        foreach ($data as $key=>$value){
            if (empty($value))continue;
            switch ($key){
                case 'id':
                case 'notice_type':
                $obj = $obj->where($key,$value);break;
                default:
                    continue;
            }
        }

        $page = I('get.page',0,'intval');
        $limit = I('get.limit',10,'intval');
        $list = $obj->select("*")->paginate($limit,[],'page',$page)->toArray();
        if($page > ceil($list['total']/$limit)) throw new \Exception('没有更多数据');

        return $list;
    }

}