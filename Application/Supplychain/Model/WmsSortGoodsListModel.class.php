<?php
namespace Supplychain\Model;


class WmsSortGoodsListModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'wms_sort_goods_list';
    protected $primaryKey = 'wsstgslt_id';
    const CREATED_AT = 'create_time';
    public $timestamps = false;


    public function getWhereObj($data)
    {
        $obj = self::where('wsstgslt_id','>',0);
        foreach ($data as $key=>$val){
            if ($val === '')continue;
            switch ($key){
                case 'company_name':
                case 'erp_order_sn':
                case 'sort_goods_status':
                    $obj = $obj->where($key,$val);break;
                case 'begin_time':
                    $obj = $obj->where('check_in_time','>=',strtotime($val));break;
                case 'end_time':
                    $obj = $obj->where('check_in_time','<=',strtotime($val));break;
            }
        }
        return $obj;
    }
}
