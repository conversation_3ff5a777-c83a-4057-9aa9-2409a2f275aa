<?php
namespace Supplychain\Model\Message;

use Supplychain\Model\BaseModelLaravel;

class MessageModel extends BaseModelLaravel
{
    protected $connection="MESSAGE";
    protected $table = 'msg_tpl';
    protected $primaryKey = 'tpl_id';
    protected $guarded = ['tpl_id'];
    public $timestamps = false;



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }



}