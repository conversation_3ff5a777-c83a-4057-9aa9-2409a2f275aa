<?php
namespace Supplychain\Model;

use  Illuminate\Database\Eloquent\Model  as Eloquent;

class BaseModelLaravel extends Eloquent
{
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
//        LaravelApp('\Pcb\Model\LARAVELDB');
    }


    public function paginate($page_size,$page){
        return $this->paginate($page_size,['*'],'page',$page);
    }
}