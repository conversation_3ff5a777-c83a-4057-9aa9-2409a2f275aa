<?php
namespace Supplychain\Model;


class CustomerModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'customer';
    protected $primaryKey = 'customer_id';
    protected $guarded = ['customer_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $fillable = ["customer_code","erp_client_code","erp_client_id","customer_name","protocol_id","follow_people",
        "usd_account","rmb_account","auditor","status","auditor_time","user_id","company_id","is_push","erp_cuntomer_id","customer_short_name","mnemonic_code",
        'dlfcp_rate','low_dlfAmt'];

    public static $PassStatus = 4;//审核通过
    public static $InAuditStatus = 1;//审核中
    public static $ApprovedStatus = -1;//审核驳回

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }





}