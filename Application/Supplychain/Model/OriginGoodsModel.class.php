<?php
namespace Supplychain\Model;


class OriginGoodsModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'origin_goods';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = NULL;
    const UPDATED_AT = NULL;
    public $timestamps = false;

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public static function getOriginName($code){
        return static::where("code",trim($code))->value("name");
    }


    /**
     * 查找国家
     */
    public static function getCodeByName($name){
        return static::where("name",trim($name))->value("code");
    }

}