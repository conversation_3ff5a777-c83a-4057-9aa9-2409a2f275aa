<?php
namespace Supplychain\Model;



class OrderRecordModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'order_edit_record';
    protected $primaryKey = 'order_record_id';
    protected $guarded = ['order_record_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = null;
    public $timestamps = true;
    public static $change_type = [
        'model'=>'改品牌型号',
        'price'=>'改单价',
        'qty'=>'改数量',
        'addMaterial'=>'添加物料',
        'g'=>'商品修改',
        'd'=>'交货信息修改',
        'a'=>'附件修改',
        'all'=>'整单修改']; // 改单方式

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }

    /**
     * 入仓单号查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchErpOrderSn($query, $erp_order_sn)
    {
        if($erp_order_sn != ''){
            $query = $query->where('erp_order_sn', $erp_order_sn);
        }
        return $query;
    }

    /**
     * 供应商查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchSupplier($query, $supplier_name)
    {
        if($supplier_name != ''){
            $query = $query->where('supplier_name', 'like', $supplier_name.'%');
        }
        return $query;
    }

    /**
     * 时间查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchTime($query, $start_time, $end_time)
    {
        if($start_time != '' && $start_time){
            $query = $query->where('create_time',">=",strtotime($start_time." 00:00:00"));
        }
        if($end_time != '' && $end_time){
            $query = $query->where('create_time',"<=",strtotime($end_time." 23:59:59"));
        }
        return $query;
    }

}