<?php
/**
 * Created by 2021/8/31.
 * User: Joneq
 * Info: 2021/8/31
 * Time: 下午3:44
 */

namespace Supplychain\Model;


class AskCustomerOptionModel extends BaseModelLaravel
{

    protected $connection = 'SUPPLYCHAIN';
    protected $table = 'ask_customer_option';
    protected $primaryKey = 'ask_customer_option_id';
    public $timestamps = false;


    //问客状态
    static public $status = [
        '1'=>'使用中',
        '2'=>'已停用',
    ];



    static public function getWhereObj($data)
    {
        $obj = self::where('ask_customer_option_id','>',0);
        foreach ($data as $key=>$val){
            if ($val === '')continue;
            switch ($key){
                case 'update_user':
                    $obj = $obj->where($key,$val);break;
                case 'content':
                    $obj = $obj->where('content','like','%'.$val.'%');break;
            }
        }
        return $obj;
    }


    public static function getAllBussinessName()
    {
        return self::where('update_user','!=','')->groupBy('update_user')->pluck('update_user','update_user');
    }

    public static function getAllOption()
    {
        return self::pluck('content','ask_customer_option_id');
    }


    public static function getList($data)
    {
        $obj = self::getWhereObj($data);
        $returnData = $obj->orderBy('ask_customer_option_id','desc')->paginate(10)->toArray();
        $data = $returnData['data'];


        foreach ($data as $key=>$value){
            $data[$key]['status_cn'] =  array_get(self::$status,$value['status']);
            $data[$key]['update_time_cn'] =  $value['update_time'] ? date('Y-m-d H:i:s', $value['update_time']) : '';
        }
        $returnData['data'] = $data;
        return $returnData;
    }

}