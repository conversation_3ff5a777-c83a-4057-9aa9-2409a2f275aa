<?php
namespace Supplychain\Model;

use \Supplychain\Model\UnitModel;

class ProductModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'goods';
    protected $primaryKey = 'goods_id';
    protected $guarded = ['goods_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $fillable = ["goods_title","goods_type","brand","origin","measurement","description","customs_code","tariff_rate","tariff_rate_land",
        "regulatory_condition","certification","product_specification","user_id","company_id","status"];

    public static $PassAuditStatus = 5;//审核通过
    public static $SubmittedAuditStatus = 1;//已提交审核

    public static $StatusCn = [
        -2=>'已删除',
        -1=>'不通过',
        0=>'待提交审核',
        1=>'已提交审核',
        5=>'审核通过',
    ];

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    /**
     * 状态查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchStatus($query,$status){
        if($status != ''){
            $query = $query->where('status',$status);
        }
        return $query;
    }

    /**
     * 状态查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchProVersion($query,$proVersion){
        if($proVersion){
            $query = $query->where('goods_type','like',trim($proVersion)."%");
        }
        return $query;
    }


    /**
     * 列表页根据状态显示其他参数
     */
    public function checkStatusShowField($data){
        foreach($data as $k=>&$v){
            if($v['status'] != static::$PassAuditStatus){
                $v['customs_code'] = '';
                $v['tariff_rate'] = '';
                $v['regulatory_condition'] = '';
                $v['certification'] = '';
            }
        }
        return $data;
    }

    /**
     * 计量单位
     */
    public function getMeasurementCnAttribute($value){

    }

    public function Unit(){
        return $this->hasOne(UnitModel::class,"code","measurement");
    }


    static public function getOrderStatusNum($userId)
    {
        // 1已经提交审核
        $data['audit_order'] = self::where('user_id',$userId)->where('status',1)->count('goods_id');
        //审核不通过 -1
        $data['no_pass_order'] = self::where('user_id',$userId)->where('status',-1)->count('goods_id');
        //审核通过 5
        $data['pass_audit'] = self::where('user_id',$userId)->where('status',5)->count('goods_id');
        return $data;
    }
}