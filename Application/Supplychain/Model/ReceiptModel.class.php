<?php
namespace Supplychain\Model;



class ReceiptModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'receipt';
    protected $primaryKey = 'rec_id';
    protected $guarded = ['rec_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }

    /**
     * 付款单号查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchBillSn($query, $bill_sn)
    {
        if($bill_sn != ''){
            $query = $query->where('bill_sn', $bill_sn);
        }
        return $query;
    }

    /**
     * 状态查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchStatus($query, $status)
    {
        if($status != ''){
            $query = $query->where('status', $status);
        }
        return $query;
    }

    /**
     * 时间查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchTime($query, $start_time, $end_time)
    {
        if($start_time != '' && $start_time){
            $query = $query->where('rec_time',">=",strtotime($start_time." 00:00:00"));
        }
        if($end_time != '' && $end_time){
            $query = $query->where('rec_time',"<=",strtotime($end_time." 23:59:59"));
        }
        return $query;
    }

}