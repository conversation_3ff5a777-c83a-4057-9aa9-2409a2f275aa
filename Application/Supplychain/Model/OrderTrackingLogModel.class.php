<?php

namespace Supplychain\Model;

use Supplychain\Model\Logic\WechatWmsLogic;

class OrderTrackingLogModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'order_tracking_log';
    protected $primaryKey = 'log_id';
    public $timestamps = false;

    const admin_user_id = 1000;
    const admin_user_name = 'admin';

    const order = 1;
    const orderDetail = 2;

    /**
     * 1:香港仓库到货登记,
     * 2:香港仓库取消到货,
     * 3:香港仓库理货完成(等待装箱),
     * 4:香港仓库理货完成(等待产地税确认),
     * 5:香港仓库取消理货,
     * 6:产地税已确认,
     * 7:封箱完成，
     * 8:等待报关,
     * 9:重新开箱,
     * 10:开始报关,
     * 11:报关完成,
     * 12:等待深圳仓库到货,
     * 13:等待深圳仓库入库,
     * 14:深圳仓库入库完成
     */
    const log_type = 0;
    const log_type_1 = -1;
    const log_type_01 = 1;
    const log_type_02 = 2;
    const log_type_03 = 3;
    const log_type_04 = 4;
    const log_type_05 = 5;
    const log_type_06 = 6;
    const log_type_07 = 7;
    const log_type_08 = 8;
    const log_type_09 = 9;
    const log_type_10 = 10;
    const log_type_11 = 11;
    const log_type_12 = 12;
    const log_type_13 = 13;

    public function addLog($log_type, $bill_type, $bill_id, $content, $create_id, $create_user, $create_time)
    {
        self::insertGetId([
            'log_type'=>$log_type,
            'bill_type'=>$bill_type,
            'bill_id'=>$bill_id,
            'content'=>$content,
            'create_user_id'=>$create_id,
            'create_user_name'=>$create_user,
            'create_time'=>$create_time,
        ]);
    }
}