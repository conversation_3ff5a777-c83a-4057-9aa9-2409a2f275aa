<?php
/**
 * Created by 2021/8/31.
 * User: Joneq
 * Info: 2021/8/31
 * Time: 下午6:58
 */

namespace Supplychain\Model;


class AskCustomerExceptionModel extends BaseModelLaravel
{

    protected $connection = 'SUPPLYCHAIN';
    protected $table = 'ask_customer_exception';
    protected $primaryKey = 'ask_customer_exception_id';
    public $timestamps = false;


    //问客状态
    static public $status = [
        '1'=>'使用中',
        '2'=>'已停用',
    ];

    static public $pkeyEkey = [
        'goods_title'=>'material',
        'goods_type'=>'model',
        'origin'=>'',
        'numbers'=>'qty',
        'brand'=>'brand',
    ];



    static public function getWhereObj($data)
    {
        $obj = self::where('ask_customer_exception_id','>',0);
        foreach ($data as $key=>$val){
            if ($val === '')continue;
            switch ($key){
                case 'create_user':
                case 'ask_customer_exception_id':
                    $obj = $obj->where($key,$val);break;
            }
        }
        return $obj;
    }


    //获取添加异常的商务
    public static function getAllBussinessName()
    {
        return self::where('update_user','!=','')->groupBy('update_user')->pluck('update_user','update_user');
    }


    //获取所有的异常 ,去除产地
    public static function getAllException()
    {
        return self::where('status',1)->where('data_field','!=','origin')->select('content','ask_customer_exception_id')->get();
    }

    //获取第一个选项
    public static function getFirstException()
    {
        return self::value('content');
    }


    public static function getList($data)
    {
        $obj = self::getWhereObj($data);
        $returnData = $obj->orderBy('ask_customer_exception_id','desc')->paginate(10)->toArray();
        $data = $returnData['data'];


        foreach ($data as $key=>$value){
            $data[$key]['status_cn'] =  array_get(self::$status,$value['status']);
            $data[$key]['update_time_cn'] =  $value['update_time'] ? date('Y-m-d H:i:s', $value['update_time']) : '';
        }
        $returnData['data'] = $data;
        return $returnData;
    }


    //获取所有的异常类型
    public static function getAllExceptionOption()
    {
        $allExceptionOption = self::where('ask_customer_exception_id','>=',0)->get()->toArray();
        foreach ($allExceptionOption as $key=>$value){

            $currentExceptionOption = AskCustomerExceptionOptionModel::where('ask_customer_exception_id',$value['ask_customer_exception_id'])->get()->toArray();

            foreach ($currentExceptionOption as $k=>$v){
                $currentExceptionOption[$k]['option_detail'] = AskCustomerOptionModel::where('ask_customer_option_id',$v['ask_customer_option_id'])->first()->toArray();
            }

            $allExceptionOption[$key]['current_option'] = $currentExceptionOption;
        }
        return $allExceptionOption;
    }


    public static function getExceptionData($data)
    {
        //获取平台字段
        $pKey = AskCustomerExceptionModel::where('ask_customer_exception_id',$data['ask_customer_exception_id'])->value('data_field');
        //获取erp字段
        $ekey = array_get(self::$pkeyEkey,$pKey);
        $returnData = [];
        foreach ($data['entrys']['entrys'] as $value){
            $returnData[] = [
                'model'=>$value['model'],
                'order_data'=>$value[$ekey],
            ];
        }
        return $returnData;
    }

}