<?php

namespace Supplychain\Model;

class AppVersionModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'wms_app_version';
    protected $primaryKey = 'id';
    public $timestamps = false;

    public function getAppLatestVersion(){
        $query = self::select('*');
        $res = $query->orderBy('id', 'desc');
        return ($res) ? $res->first():null;
    }
}