<?php
namespace Supplychain\Model;


class WmsCheckinUnusualListModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'wms_checkin_unusual_list';
    protected $primaryKey = 'wscnullt_id';
    public $timestamps = false;

    static public $claimStatus = [
        0=>'未认领',
        1=>'已认领',
    ];


    public function getWhereObj($data)
    {
        $obj = self::where('wscnullt_id','>',0);
        foreach ($data as $key=>$val){
            if ($val === '')continue;
            switch ($key){
                case 'company_name':
                case 'logistics_number':
                case 'erp_order_sn':
                case 'delivery_method':
                case 'claim_status':
                    $obj = $obj->where($key,$val);break;
                case 'begin_time':
                    $obj = $obj->where('check_in_time','>=',strtotime($val));break;
                case 'end_time':
                    $obj = $obj->where('check_in_time','<=',strtotime($val));break;
            }
        }
        return $obj;
    }

    public static function getSerialNum() {
        $formattedString = sprintf('S%s%02d', substr(date('Y'), 2, 4), date('m'));
        $count = self::where('abnormal_serial_num', 'like', $formattedString.'%')->count();
        $countAdd = $count+1;
        return $formattedString.sprintf('%03d', $countAdd);
    }
}
