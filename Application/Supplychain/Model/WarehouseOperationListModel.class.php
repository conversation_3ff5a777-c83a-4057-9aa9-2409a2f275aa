<?php
/**
 * Created by 2021/11/29.
 * User: Joneq
 * Info: 2021/11/29
 * Time: 下午5:15
 */

namespace Supplychain\Model;

class WarehouseOperationListModel extends BaseModelLaravel
{

    protected $connection = 'SUPPLYCHAIN';
    protected $table = 'warehouse_operation_list';
    protected $primaryKey = 'weonlt_id';
    public $timestamps = false;

    //作业状态
    static public $operetionStatus =[
        1=>'待作业',
        2=>'已作业',
        3=>'取消作业'
    ];

    //审核状态
    static public $auditStatus =[
        1=>'待审核',
        2=>'已审核',
    ];

    //到货状态
    static public $arrivalStatus =[
        1=>'未到货',
        2=>'部分到货',
        3=>'已到货',
    ];


    //箱子类型
    static public $isUnpacking = [
        0=>'整箱作业',
        1=>'拆箱作业',
    ];

    //目标区域位置
    static public $operationTargetWarehouse = [
        1=>'发货区',
        2=>'放回原位置',
    ];


    //仓库
    static public $operationWarehouse = [
        1=>'香港仓',
    ];





    public function getWhereObj($data)
    {
        $obj = self::where('weonlt_id','>',0);
        foreach ($data as $key=>$val){
            if ($val === '')continue;
            switch ($key){
                case 'operation_status':
                case 'audit_status':
                case 'weonit_sn':
                case 'weonlt_id':
                    $obj = $obj->where($key,$val);break;
                case 'company_name':
                    $obj = $obj->where($key,'like','%'.$val.'%');break;
                case 'begin_time':
                    $obj = $obj->where('create_time','>=',strtotime($val));break;
                case 'end_time':
                    $obj = $obj->where('create_time','<=',strtotime($val));break;
                case 'erp_order_sn':
                    $weonltIdArr = WarehouseOperationDetailModel::where('erp_order_sn',$val)->pluck('weonlt_id');
                    $obj = $obj->whereIn('weonlt_id',$weonltIdArr);break;
            }
        }
        return $obj;
    }




}