<?php
namespace Supplychain\Model\Tax;

use \Supplychain\Model\UnitModel;
use Supplychain\Model\Tax\CustomsModel;
use Supplychain\Model\BaseModelLaravel;
class CustomsItemsModel extends BaseModelLaravel
{
    protected $connection="TAX";
    protected $table = 'customs_items';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = false;



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public function customs(){
        return $this->hasOne(CustomsModel::class,"id","customs_id");
    }


    public function customs123(){
        return $this->belongsTo(CustomsModel::class,"customs_id","id");
    }


    /**
     * @param $query
     * @param $goods_title
     * 品名
     */
    public function scopeSearchGoodsTitle($query,$goods_title,$searchField=''){
        if($goods_title != ''){
            if($searchField != 'goods_title'){
                $query = $query->where('goods_title',$goods_title);
            }else{
                $query = $query->where('goods_title',"like","%".$goods_title."%");
            }

        }
        return $query;
    }


    /**
     * @param $query
     * @param $goods_type
     * @return mixed
     * 型号
     */
    public function scopeSearchGoodsType($query,$goods_type,$searchField=''){
        if($goods_type != ''){
            if($searchField != 'goods_name'){
                $query = $query->where('goods_name',$goods_type);
            }else{
                $query = $query->where('goods_name',"like","%".$goods_type."%");
            }

        }
        return $query;
    }

    /**
     * @param $query
     * @param $goods_type
     * @return mixed
     * 型号
     */
    public function scopeSearchBrand($query,$brand,$searchField=''){
        if($brand != ''){
            if($searchField != 'brand_name'){
                $query = $query->where('brand_name',$brand);
            }else{
                $query = $query->where('brand_name',"like","%".$brand."%");
            }
        }
        return $query;
    }



    /**
     * @param $query
     * @param $goods_type
     * @return mixed
     * 型号
     */
    public function scopeSearchGoodsTypePage($query,$goods_type){
        if($goods_type != ''){
            $query = $query->where('goods_name',"like","%".$goods_type."%");
        }
        return $query;
    }


    /**
     * @param $query
     * @param $goods_title
     * 品名
     */
    public function scopeSearchGoodsTitlePage($query,$goods_title){
        if($goods_title != ''){
            $query = $query->where('goods_title',"like","%".$goods_title."%");
        }
        return $query;
    }

    /**
     * @param $query
     * @param $goods_type
     * @return mixed
     * 型号
     */
    public function scopeSearchBrandPage($query,$brand){
        if($brand != ''){
            $query = $query->where('brand_name',"like","%".$brand."%");
        }
        return $query;
    }

}