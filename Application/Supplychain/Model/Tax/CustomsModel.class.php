<?php
namespace Supplychain\Model\Tax;

use \Supplychain\Model\UnitModel;
use Supplychain\Model\BaseModelLaravel;
class CustomsModel extends BaseModelLaravel
{
    protected $connection="TAX";
    protected $table = 'customs';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = false;



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }




}