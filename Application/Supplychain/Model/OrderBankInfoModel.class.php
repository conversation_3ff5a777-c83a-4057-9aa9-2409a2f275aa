<?php
namespace Supplychain\Model;



class OrderBankInfoModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'order_bank_info';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;

    protected $fillable = [
        "order_id","user_id","supplier_bank_id","bank_account","bank_charges","pay_ratio","pay_amount","pay_date","pay_remark","area","bank_name","swift_code","bank_code"
        ,"recipient_country","currency_id","bank_address","create_time","update_time","company_id",'payable_to'
    ];


    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


}