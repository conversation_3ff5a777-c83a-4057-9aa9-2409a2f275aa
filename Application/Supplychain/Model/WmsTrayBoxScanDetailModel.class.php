<?php
/**
 * Created by 2022/4/24.
 * User: Joneq
 * Info: 2022/4/24
 * Time: 下午3:47
 */

namespace Supplychain\Model;


class WmsTrayBoxScanDetailModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'wms_tray_box_scan_detail';
    protected $primaryKey = 'wstybxsndl_id';
    public $timestamps = false;

    public function getWhereObj($data)
    {
        $obj = self::where('is_del',0);
        foreach ($data as $key=>$val){
            if ($val === '')continue;
            switch ($key){
                case 'is_scan':
                case 'is_del':
                case 'wstyty_id':
                case 'search_key':
                case 'erp_order_sn':
                case 'base_erp_order_sn':
                    $obj = $obj->where($key,$val);break;
                case 'erp_order_sn_like':
                    $obj = $obj->where('erp_order_sn','like','%'.$val.'%');break;
                case 'today':
                    $todayStart = strtotime($val);
                    $obj = $obj->where('create_time','>=',$todayStart);
                    $obj = $obj->where('create_time','<=',($todayStart+86400));
                    break;

            }
        }
        return $obj;
    }


    //判断扫描数量
    public function checkScanNum($noScanNum,$sumNum)
    {

        if (intval($noScanNum) == 0){
            return '全部已经扫描';
        }

        if (intval($noScanNum) == intval(intval($sumNum))){
            return '未扫描';
        }

        return '剩余'.$noScanNum;
    }

    //获取托盘箱子总数
    static public function getTraySumBox($wstytyId)
    {
        $printTwoSum =  WmsTrayBoxScanDetailModel::where('is_del',0)->where('wstyty_id',$wstytyId)->where('print_type',2)->count('wstybxsndl_id');

        if (empty($sum = WmsTrayBoxScanDetailModel::where('is_del',0)->where('wstyty_id',$wstytyId)->where('print_type',1)->sum('sum'))){
            $sum = 0;
        }
        return $printTwoSum+$sum;
    }
    //获取未扫描入仓号总数量In
    static public function getNoScanErpOrderSnInSumBox($erpOrderSnArr)
    {
        $printTwoSum =  WmsTrayBoxScanDetailModel::where('is_del',0)->where('is_scan',0)->whereIn('erp_order_sn',$erpOrderSnArr)->where('print_type',2)->count('wstybxsndl_id');

        if (empty($sum = WmsTrayBoxScanDetailModel::where('is_del',0)->where('is_scan',0)->whereIn('erp_order_sn',$erpOrderSnArr)->where('print_type',1)->sum('sum'))){
            $sum = 0;
        }
        return $printTwoSum+$sum;
    }

    //获取入仓号总数量In
    static public function getErpOrderSnInSumBox($erpOrderSnArr)
    {
        $printTwoSum =  WmsTrayBoxScanDetailModel::where('is_del',0)->whereIn('erp_order_sn',$erpOrderSnArr)->where('print_type',2)->count('wstybxsndl_id');

        if (empty($sum = WmsTrayBoxScanDetailModel::where('is_del',0)->whereIn('erp_order_sn',$erpOrderSnArr)->where('print_type',1)->sum('sum'))){
            $sum = 0;
        }
        return $printTwoSum+$sum;
    }

    static public function getWstytyIdByErpOrderSnAndBoxSn($erpOrderSn)
    {
        return self::where('is_del',0)->where('erp_order_sn',$erpOrderSn)->value('wstyty_id');
    }
}