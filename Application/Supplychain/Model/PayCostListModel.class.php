<?php
namespace Supplychain\Model;

use \Supplychain\Model\UnitModel;
use \Supplychain\Model\WaterSingleModel;

class PayCostListModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'pay_cost_list';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = null;
    public $timestamps = true;
    protected $dateFormat = 'Y-m-d';
    // protected $fillable = [];

    public function fromDateTime($value)
    {
        return strtotime(parent::fromDateTime($value));
    }


    
}