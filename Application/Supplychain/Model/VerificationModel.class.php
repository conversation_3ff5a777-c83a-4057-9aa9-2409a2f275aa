<?php
namespace Supplychain\Model;



class VerificationModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'verification';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = null;
    public $timestamps = true;

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }

    /**
     * 订单号查询
     * @param $query
     * @param $order_sn
     * @return mixed
     */
    public function scopeSearchOrderSn($query, $order_sn)
    {
        if($order_sn != ''){
            $query = $query->where('order_sn', $order_sn);
        }
        return $query;
    }

    /**
     * 入仓单号查询
     * @param $query
     * @param $erp_order_sn
     * @return mixed
     */
    public function scopeSearchErpOrderSn($query, $erp_order_sn)
    {
        if($erp_order_sn != ''){
            $query = $query->where('erp_order_sn', $erp_order_sn);
        }
        return $query;
    }

    /**
     * 时间查询
     * @param $query
     * @param $start_time
     * @param $end_time
     * @return mixed
     */
    public function scopeSearchTime($query, $start_time, $end_time)
    {
        if($start_time != '' && $start_time){
            $query = $query->where('rec_time',">=",strtotime($start_time." 00:00:00"));
        }
        if($end_time != '' && $end_time){
            $query = $query->where('rec_time',"<=",strtotime($end_time." 23:59:59"));
        }
        return $query;
    }

}