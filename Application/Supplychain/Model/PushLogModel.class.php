<?php
namespace Supplychain\Model;

use \Supplychain\Model\UnitModel;
use \Supplychain\Model\PayCostListModel;

class PushLogModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'push_log';
    protected $primaryKey = 'push_log_id';
    protected $guarded = ['push_log_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = null;
    public $timestamps = true;
    protected $dateFormat = 'Y-m-d H:i:s';
    // protected $fillable = [];

    public function fromDateTime($value)
    {
        return strtotime(parent::fromDateTime($value));
    }


    
}