<?php
namespace Supplychain\Model;

use \Supplychain\Model\CountryModel;

class SupplierBankModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'supplier_bank';
    protected $primaryKey = 'supplier_bank_id';
    protected $guarded = ['supplier_bank_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $fillable = ["bank_name","bank_user","bank_account","bank_address","bank_code","currency_id","area","swift_code",
            "recipient_country","bank_information","supplier_id","status","user_id","company_id","erp_entry_id"];


    public static $Status=[
        -1 => '审核不通过',
        -2  => '作废',
        0  => '审核中',
        1  => '审核通过',
    ];

    public static $Area=[
        1 => '香港境内',
        2  => '香港境外',
    ];

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public function Country(){
        return $this->hasOne(CountryModel::class,"code","recipient_country");
    }


    /**
     * 状态查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchBankAccount($query,$bank_account){
        if($bank_account != ''){
            $query = $query->where('bank_account',$bank_account);
        }
        return $query;
    }

    /**
     * 状态查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchBankUser($query,$bankUser){
        if($bankUser != ''){
            $query = $query->where('bank_user',$bankUser);
        }
        return $query;
    }


    /**
     * 状态查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchBankName($query,$bankName){
        if(($bankName != '') && !empty($supplierId = SupplierModel::where('supplier_name',$bankName)->value('supplier_id'))){
            $query = $query->where('supplier_id',$supplierId);
        }
        return $query;
    }


    /**
     * @return mixed
     * 币别
     */
    public function getCruuencyCnAttribute(){
        return array_get(C("supply_currency"),$this->currency_id,'');
    }

    /**
     * @return mixed
     * 区域
     */
    public function getAreaCnAttribute(){
        return array_get(static::$Area,$this->area,'');
    }


    public function getStatusCnAttribute(){
        return array_get(static::$Status,$this->status,'');
    }

}