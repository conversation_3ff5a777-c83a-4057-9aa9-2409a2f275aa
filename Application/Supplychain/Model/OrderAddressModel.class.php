<?php
namespace Supplychain\Model;

use \Supplychain\Model\UnitModel;

class OrderAddressModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'order_address';
    protected $primaryKey = 'order_address_id';
    protected $guarded = ['order_address_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;


    public static $Inland_delivery_zhengpi=1;


    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }



    /*
     * TODO 20190925新增收货信息
     * RECEIVERCOMPANY 收货方即客户公司名（有收货地址的时候，收货方不能为空）
        RECEIVER 收货联系人
        RECEIVERPHONE 收货联系人手机
        RECEIVERADDR 收货地址
     *
     * */
    static public function getOrderAddress($orderid,$arr)
    {
        //没有就返回空
        if (empty($address = self::where('order_id','=',intval($orderid))->first())){
            $arr['RECEIVERADDR'] = ' ';
            $arr['RECEIVERPHONE'] = ' ';
            $arr['RECEIVER'] = ' ';
            $arr['RECEIVERCOMPANY'] = ' ';
            return $arr;
        }

        $arr['RECEIVERADDR'] = $address->detail_address;
        $arr['RECEIVERPHONE'] = $address->mobile;
        $arr['RECEIVER'] = $address->consignee;

        $companyName = ' ';
        if (!empty($address->com_delivery_id)){
            $companyName = UserDeliveryModel ::where('com_delivery_id','=',$address->com_delivery_id)->value('com_name');
            if (empty($companyName)){
                $companyName = '';
            }
        }

        $arr['RECEIVERCOMPANY'] = $companyName;
        return $arr;
    }




}