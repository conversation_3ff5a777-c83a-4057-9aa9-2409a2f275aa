<?php
/**
 * Created by 2021/9/2.
 * User: Joneq
 * Info: 2021/9/2
 * Time: 下午7:08
 */

namespace Supplychain\Model;



class AskCustomerProblemGoodsModel extends BaseModelLaravel
{


    protected $connection = 'SUPPLYCHAIN';
    protected $table = 'ask_customer_problem_goods';
    protected $primaryKey = 'ask_customer_problem_goods_id';
    public $timestamps = false;


    //获取问题订单商品
    static public function getProblemOrderGoods($problemId)
    {
        return [];
    }


    //获取异常问题ID的订单商品数据
    static public function getAskCUstomerProblemListIdOrderGoods($askCustomerProblemListId)
    {
        if (empty($askCustomerProblemInfo= AskCustomerProblemListModel::where('ask_customer_problem_list_id',$askCustomerProblemListId)->first()->toArray())){
            throw new \Exception('没有获取到异常ID');
        }
        $returnData['ask_customer_exception_cn'] = AskCustomerExceptionModel::where('ask_customer_exception_id',$askCustomerProblemInfo['ask_customer_exception_id'])->value('content');
        $returnData['ask_customer_remark'] =$askCustomerProblemInfo['ask_customer_remark'];

        $orderGoods = self::where('ask_customer_problem_list_id',$askCustomerProblemListId)->get()->toArray();
        $returnData['order_goods'] = $orderGoods;
        return $returnData;
    }

}