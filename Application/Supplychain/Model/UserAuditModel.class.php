<?php
namespace Supplychain\Model;



class UserAuditModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'company_audit';
    protected $primaryKey = 'audit_id';
    protected $guarded = ['audit_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }





}