<?php
namespace Supplychain\Model;

use \Supplychain\Model\UnitModel;
use \Supplychain\Model\PayCostListModel;

class PayCostModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'pay_cost';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = null;
    public $timestamps = true;
    protected $dateFormat = 'Y-m-d';
    // protected $fillable = [];

    public function fromDateTime($value)
    {
        return strtotime(parent::fromDateTime($value));
    }

    // 获取预计应收款关联信息
    public function hasManyPayCostList()
    {
        return $this->hasMany(PayCostListModel::class, 'pay_cost_id', 'id');
    }

    /**
     * 入仓单号查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchErpOrderSn($query, $erp_order_sn)
    {
        if($erp_order_sn != ''){
            $query = $query->where('erp_order_sn', $erp_order_sn);
        }
        return $query;
    }

    /**
     * 时间查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchTime($query, $start_time, $end_time)
    {
        if($start_time != '' && $start_time){
            $query = $query->where('create_time',">=",strtotime($start_time." 00:00:00"));
        }
        if($end_time != '' && $end_time){
            $query = $query->where('create_time',"<=",strtotime($end_time." 23:59:59"));
        }
        return $query;
    }
    
}