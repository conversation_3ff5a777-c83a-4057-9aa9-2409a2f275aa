<?php
namespace Supplychain\Model;


Class AskCustomerProblemListModel extends BaseModelLaravel
{
    protected $connection = 'SUPPLYCHAIN';
    protected $table = 'ask_customer_problem_list';
    protected $primaryKey = 'ask_customer_problem_list_id';
    public $timestamps = false;


    //问客状态
    static public $askCustomerStatus = [
        '1'=>'待确认',
        '2'=>'已确认',
        '3'=>'已取消',
    ];

    //问客状态
    static public $handleType = [
        '1'=>'确认处理',
        '2'=>'人工处理',
    ];


    //仓库执行状态
    static public $storeExecuteStatus = [
        '1'=>'待执行',
        '2'=>'执行完成',
        '3'=>'已取消',
    ];

    //获取列表
    static public function getList($data)
    {
        $page = I('page',0,'intval');
        $limit = I('limit',10,'intval');
        $obj = self::getWhereObj($data);
        $returnData = $obj->orderBy('ask_customer_id','desc')
            ->select('create_time','ask_customer_exception_id','erp_order_sn','ask_customer_problem_list_id','handle_type','ask_customer_status','store_execute_status','ask_customer_confirm_time','store_execute_time'
            ,'ask_customer_sw_status','upload_img','ask_customer_remark','ask_customer_sw_confirm_remark','bussiness_name','store_execute_name','ask_customer_id')
            ->paginate($limit,[],'page',$page)->toArray();


        $waitHandleNum = self::getWhereObj($data)->count('ask_customer_problem_list_id');

        $data = $returnData['data'];
        if(empty($returnData['data'])){
            throw new \Exception('暂无更多数据');
        }


        foreach ($data as $key=>$value){

            $askCustomerInfo = AskCustomerModel::where('ask_customer_id',$value['ask_customer_id'])->first();
            if (empty($askCustomerInfo)){
                $value['company_name'] = '';
                $value['is_goods_check'] = 0;
            }else{
                $value['company_name'] = $askCustomerInfo['company_name'];
                $goodsCheck = BaoGuanOrderListModel::where('erp_order_sn',$value['erp_order_sn'])->value('is_goods_check');
                $value['is_goods_check'] = $goodsCheck;
            }

            //异常
            if (!isset($excepiton[$value['ask_customer_exception_id']])){
                $excepiton[$value['ask_customer_exception_id']] = AskCustomerExceptionModel::where('ask_customer_exception_id',$value['ask_customer_exception_id'])->value('content');
            }

            $value['ask_customer_exception_id_cn'] = $excepiton[$value['ask_customer_exception_id']];
            //问客时间
            $difftime = time() - $value['create_time'];

            $value['ask_customer_status_cn'] =  array_get(self::$askCustomerStatus,$value['ask_customer_status']);
            $value['ask_customer_sw_status_cn'] =  array_get(self::$askCustomerStatus,$value['ask_customer_sw_status']);
            $value['store_execute_status_cn'] =  array_get(self::$storeExecuteStatus,$value['store_execute_status']);
            $value['ask_customer_exception_id_cn'] = $excepiton[$value['ask_customer_exception_id']];

            $value['create_time_cn'] =  $value['create_time'] ? date('Y-m-d H:i:s', $value['create_time']) : '';
            $value['ask_customer_confirm_time_cn'] =  $value['ask_customer_confirm_time'] ? date('m-d H:i:s', $value['ask_customer_confirm_time']) : '';
            $value['store_execute_time_cn'] =  $value['store_execute_time'] ? date('m-d H:i:s', $value['store_execute_time']) : '';


            if ((intval($value['ask_customer_status']) == 1) && (intval($value['store_execute_status']) == 1) && $difftime > (1800) ){
                $value['is_urgent'] = '紧急';
            }else if ((intval($value['ask_customer_status']) == 2) && (intval($value['store_execute_status']) == 1)){
                $value['is_urgent'] = '确认处理';
            }else if(intval($value['store_execute_status']) == 2){
                $value['is_urgent'] = '完成操作';
            }else{
                $value['is_urgent'] = '';
            }


            $value['ask_time'] = self::getUseTime($difftime);

            if (intval($value['handle_type']) === 2){
                $value['handle_type_cn'] = '人工处理';
            }else{
                $value['handle_type_cn'] = $value['is_urgent'];
            }


            $data[$key] = $value;
        }

        $returnData['data'] = $data;
        $returnData['wait_handle_num'] = $waitHandleNum;
        return $returnData;
    }

    static public function getUseTime($time)
    {
        $returnTime = '';
        if ($hour = intval($time/3600)){
            $returnTime = $hour.'h';
        }
        return $returnTime.intval(($time%3600)/60).'m';
    }


    static public function getWhereObj($data)
    {
        $obj = self::where('ask_customer_id','>',0);
        foreach ($data as $key=>$val){
            if ($val === '')continue;
            switch ($key){
                case 'ask_customer_status':
                    if (intval($val) === 2){
                        $obj = $obj->where($key,$val)->where('store_execute_status',1);break;
                    }else{
                        $obj = $obj->where($key,$val);break;
                    }
                case 'store_execute_status':
                    if (intval($val) === 1){
                        $obj = $obj->where($key,$val)->where('ask_customer_status',2);break;
                    }else{
                        $obj = $obj->where($key,$val);break;
                    }

                case 'ask_customer_exception_id':
                case 'ask_customer_problem_list_id':
                case 'erp_order_sn':
                case 'bussiness_name':
                case 'ask_customer_sw_status':
                    $obj = $obj->where($key,$val);break;
                case 'ask_begin_time':
                    $obj = $obj->where('create_time','>=',strtotime($val));break;
                case 'ask_end_time':
                    $obj = $obj->where('create_time','<=',strtotime($val));break;
                case 'company_name':
                    $askCutomerIdArr = AskCustomerModel::where('company_name',$val)->pluck('ask_customer_id');
                    $obj = $obj->whereIn('ask_customer_id',$askCutomerIdArr);break;
                case 'ask_customer_sw_confirm_time_begin':
                    $obj = $obj->where('ask_customer_sw_confirm_time','>=',strtotime($val));break;
                case 'ask_customer_sw_confirm_time_end':
                    $obj = $obj->where('ask_customer_sw_confirm_time','<=',strtotime($val)+86400);break;
                case 'store_execute_time_begin':
                    $obj = $obj->where('store_execute_time','>=',strtotime($val));break;
                case 'store_execute_time_end':
                    $obj = $obj->where('store_execute_time','<=',strtotime($val)+86400);break;

                case 'store_execute_time':
                case 'ask_customer_sw_confirm_time':
                case 'create_time':
                    $obj = $obj->where($key,'>=',strtotime($val));
                    $obj = $obj->where($key,'<=',strtotime($val)+86400);break;


            }
        }
        return $obj;
    }


    static public function getAskNum()
    {
        $todayTime = strtotime(date('Y-m-d'));
        return [
            'today_ask_customer'=>self::getWhereObj(['ask_begin_time'=>$todayTime,'ask_end_time'=>$todayTime+86400])->count('ask_customer_problem_list_id'),
            'ask_customer_status_1'=>self::getWhereObj(['ask_customer_status'=>1])->count('ask_customer_problem_list_id'),
            'store_execute_status_1'=>self::getWhereObj(['store_execute_status'=>1])->count('ask_customer_problem_list_id'),
        ];
    }





    //增加问客
    //参数案例
    /*
     *
     {
  "problem" : [
    {
      "orderGoods" : [
        {
          "order_goods_id" : "1", 订单商品ID
          "store_result" : "100" 仓库结果
           "data_field" : "goods_title" 数据库字段
        }
      ],
      "upload_img" : "https:\/\/mufanhome.com\/static\/uploads\/images\/2021\/08\/30\/1630290471612c422744a33.jpg,https:\/\/mufanhome.com\/static\/uploads\/images\/2021\/08\/30\/1630290471612c422744a33.jpg",
      "ask_customer_exception_id" : "异常类型id",
      "ask_customer_remark" : "问客备注"
    }
  ],
  "upload_img" : "https:\/\/mufanhome.com\/static\/uploads\/images\/2021\/08\/30\/1630290471612c422744a33.jpg,https:\/\/mufanhome.com\/static\/uploads\/images\/2021\/08\/30\/1630290471612c422744a33.jpg",
  "ask_time" : "2021-08-25 18:08:00", 提交时间
  "erp_order_sn" : "B2222" 委托单号
}
     *
     */
    static public function addAskCustomerProblem($data)
    {
        $param['erp_order_sn'] = 'B50294';//委托单号
        $param['ask_time'] = '2021-08-25 18:08';//提交时间
        $param['upload_img'] = 'https://mufanhome.com/static/uploads/images/2021/08/30/1630290471612c422744a33.jpg,https://mufanhome.com/static/uploads/images/2021/08/30/1630290471612c422744a33.jpg';
        $orderGoods['order_goods_id'] = '1';//订单商品ID
        $orderGoods['store_result'] = '100'; //仓库结果
        $orderGoods['data_field'] = 'goods_title'; //仓库结果
        $problem['upload_img'] = $param['upload_img'];
        $problem['ask_customer_remark'] = '问客备注';
        $problem['ask_customer_exception_id'] = '1';
        $problem['orderGoods'][] = $orderGoods;
        $param['problem'][] = $problem;
        $param['user_name'] = $data['user_name'];
        $data = $param;

        try{
            DB::connection('supply')->beginTransaction();

            $orderInfo = OrderModel::where('erp_order_sn',$data['erp_order_sn'])->first()->toArray();
            if (empty($orderInfo)){
                throw new \Exception('该订单不存在');
            }
            $askCustomerInsertData['erp_order_sn'] = $data['erp_order_sn'];
            $askCustomerInsertData['company_name'] = CompanyModel::where('company_id',$orderInfo['company_id'])->value('company_full_name');
            $askCustomerInsertData['order_time'] = $orderInfo['create_time'];
            $askCustomerInsertData['upload_img'] = $data['upload_img'];
            $askCustomerInsertData['ask_time'] = strtotime($data['ask_time']);
            $askCustomerInsertData['create_time'] = time();
            $askCustomerInsertData['create_user_name'] = $data['user_name'];

            //新增问客
            $askCustomerId = AskCustomerModel::insertGetId($askCustomerInsertData);

            foreach ($param['problem'] as $key=>$value){
                $askCustomerProblemInsertData['ask_customer_id'] = $askCustomerId;
                $askCustomerProblemInsertData['bussiness_name'] = $askCustomerProblemInsertData['create_user_name'] = $data['user_name'];
                $askCustomerProblemInsertData['upload_img'] = $value['upload_img'];
                $askCustomerProblemInsertData['ask_time'] = time();
                $askCustomerProblemInsertData['ask_customer_remark'] = $value['ask_customer_remark'];
                $askCustomerProblemInsertData['login_type'] = 2;
                $askCustomerProblemId = AskCustomerProblemListModel::insertGetId($askCustomerProblemInsertData);

                foreach ($value['orderGoods'] as $k=>$v){
                    $askCustomerProblemGoodsData['ask_customer_problem_list_id'] = $askCustomerProblemId;
                    $askCustomerProblemGoodsData['create_time'] = $askCustomerProblemId;
                    $askCustomerProblemGoodsData['create_user_name'] = $data['user_name'];
                    $askCustomerProblemGoodsData['data_field'] = $v['data_field'];
                    $askCustomerProblemGoodsData['order_goods_id'] = $v['order_goods_id'];
                    $askCustomerProblemGoodsData['order_result'] = OrderGoodsModel::where('order_goods_id',$v['order_goods_id'])->value($v['data_field']);
                    $askCustomerProblemGoodsData['store_result'] = $v['store_result'];
                    $askCustomerProblemGoodsId = AskCustomerProblemGoodsModel::insertGetId($askCustomerProblemGoodsData);
                }
            }

            DB::connection('supply')->commit();
        }catch (\Exception $exception){
            DB::connection('supply')->rollback();
            return $exception->getMessage();
        }


        return [];
    }



    //修改问客状态
    static public function changeAskCustomerStatus($data)
    {
        $returnData = [];
        return $returnData;
    }

    //商务确认
    static public function bussinessConfirm($data)
    {
        $returnData = [];
        return $returnData;
    }

    //执行作业
    static public function executeJob($data)
    {
        $returnData = [];
        return $returnData;
    }

}