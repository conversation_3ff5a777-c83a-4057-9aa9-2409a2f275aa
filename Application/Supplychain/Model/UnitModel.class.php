<?php
namespace Supplychain\Model;



class UnitModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'unit';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = false;
    protected $fillable = ["code","name","status"];



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public static function getName($code){
        $jldw =  static ::where("code",trim($code))->value("name");
        return $jldw ? $jldw : '';
    }

    public static function getUnitCode($str){
        try{
            $code = static::where("name",trim($str))->value("code");
            if(!$code){
                $unit = static::orderBy("id",desc)->first();
                $newModel = static::create([
                    'code'=>strval(intval($unit->code)+1),
                    'name'=>trim($str),
                ]);
                if($newModel){
                    return $newModel->code;
                }else{
                    return '007';
                }
            }else{
                return $code;
            }
        }catch(\Exception $e){
            return '007';
        }
    }

}