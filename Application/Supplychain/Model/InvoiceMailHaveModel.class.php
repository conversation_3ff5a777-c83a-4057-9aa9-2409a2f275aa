<?php
/**
 * Created by 2022/4/24.
 * User: Joneq
 * Info: 2022/4/24
 * Time: 下午3:52
 */

namespace Supplychain\Model;


class InvoiceMailHaveModel extends BaseModelLaravel
{

    protected $connection = 'SUPPLYCHAIN';
    protected $table = 'invoice_mail_have';
    protected $primaryKey = 'iemlhe_id';
    public $timestamps = false;

    Const MAIL_STATUS_ONE = 1;
    Const MAIL_STATUS_TWO = 2;
    Const MAIL_STATUS_THREE = 3;
    Const MAIL_STATUS_FOUR = 4;
    Const MAIL_STATUS_FIVE = 5;

    //1等待揽收2已揽收3运输中4已签收5已取消
    static $MAIL_STATUS = [
        1=>'等待揽收',
        2=>'已揽收',
        3=>'运输中',
        4=>'已签收',
        5=>'已取消',
    ];
}