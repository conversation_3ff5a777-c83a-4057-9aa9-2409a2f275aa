<?php
namespace Supplychain\Model;

use Supplychain\Model\BaoGuanOrderListModel;

class SZDeliveryNoteModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'sz_delivery_note';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = NULL;
    public $timestamps = true;




    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public function hk_delivery_note_list(){
        return $this->hasMany(HKDeliveryNoteListModel::class,"hk_delivery_note_id","id");
    }





}