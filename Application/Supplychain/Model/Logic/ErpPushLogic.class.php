<?php
/**
 * Created by 2021/6/22.
 * User: Joneq
 * Info: 2021/6/22
 * Time: 下午5:28
 */

namespace Supplychain\Model\Logic;


class ErpPushLogic
{

    //{"entrustNo":"B143323-1"}
    public function getOrderCheckDetailsApi($data)
    {
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $orderErp = new \SoapClient(ERP_SUPPLY_DOMAIN.'/ormrpc/services/WSIchuntCustomTaskFacade?wsdl');
            $res = $orderErp->getOrderCheckDetails(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if(isset($res['4444'])){
                throw new \Exception($res['4444']);
            }
            return $res;
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['4444']);
        }
    }


    //{"entrustNo":"B143323-1"}
    public function checkPaperTally($data)
    {
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $orderErp = new \SoapClient(ERP_SUPPLY_DOMAIN.'/ormrpc/services/WSIchuntCustomTaskFacade?wsdl');
            $res = $orderErp->checkPaperTally(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if($res['code'] !== 0){
                throw new \Exception($res['msg']);
            }
            return $res;
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['msg']);
        }
    }

    // {"entrustId":"xxxxxx"}
    public function checkEntrustNo($data)
    {
        try {
            \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
            $orderErp = new \SoapClient(ERP_SUPPLY_DOMAIN.'/ormrpc/services/WSIchuntCustomTaskFacade?wsdl');
            $res = $orderErp->checkEntrustNo(\GuzzleHttp\json_encode($data));
            $res = json_decode($res, true);
            if($res['code'] !== 0){
                throw new \Exception($res['msg']);
            }
            return $res;
        } catch (\Exception $e) {
            \Think\Log::write($e->getMessage(),'WARN');
            throw new \Exception($res['msg']);
        }
    }
}