<?php
/**
 * Created by 2021/6/25.
 * User: Joneq
 * Info: 2021/6/25
 * Time: 下午2:02
 */

namespace Supplychain\Model\Logic;


use Shipping\Controller\Kuaidi100Controller;
use Supplychain\Model\ActionLogModel;
use Supplychain\Model\CmsModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\HKDeliveryNoteModel;
use Supplychain\Model\HongKongDeliveryModel;
use Supplychain\Model\InlandDeliveryModel;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\OrderRecordModel;
use Supplychain\Model\OrderStatusModel;
use Supplychain\Model\PaymentApplyModel;
use Supplychain\Model\SupplierBankModel;
use Supplychain\Model\SupplierModel;
use Supplychain\Model\SZDeliveryNoteListModel;
use Supplychain\Model\SZDeliveryNoteModel;
use Supplychain\Model\SZShipmentsNoteModel;
use Supplychain\Repository\OrderRepository;

class OrderLogic
{

    static public $editSelectField = ['change_type', 'erp_order_sn', 'order_id','create_time','amount','supplier_name','supplier_id','create_time'];



    /*
     * add_basic_order_info
     * add_hongkong_delivery
     * add_inland_delivery
     * add_other_order_info
     * goto_adding_order
     */
    static public function createOrder($data)
    {
        $orderRepository = new OrderRepository();
        try{
            CommonLogic::checkField('createOrder',$data);

            //如果提交就提交订单，否则不提交
            if ($data['is_submit']){
//                self::changeSupplierLastUseTime($data);

                $orderRepository->goto_adding_order($data['order_id']);

                if (isset($data['is_jd_order']) && intval($data['is_jd_order']) === 1){

                    //京东订单去京东金融申请金额 判断余额是否充足
                    $jdjrInfo = CustomerLogic::getJdJrInfo();
                    if($jdjrInfo['jdjr_info']['data']['status'] !== 'CREDIT_SUCCESS' ){
                        //创建订单失败，修改订单为待提交
                        OrderModel::where('order_id',$data['order_id'])->update(['status'=>-2]);
                        throw new \Exception('京东金融授信状态暂未通过，请使用其他方式');
                    }

                    $totalMount = round(OrderGoodsModel::where("order_id",intval($data['order_id']))->where("status",">=",0)->sum('total_price'),2);

                    if (round($jdjrInfo['jdjr_info']['data']['remainLimit'],2) < $totalMount ){
                        //创建订单失败，修改订单为待提交
                        OrderModel::where('order_id',$data['order_id'])->update(['status'=>-2]);
                        throw new \Exception('京东金融剩余额度不足以支付，请使用其他方式');
                    }

                    (new LxJdJrLogic())->createOrder(['order_id'=>$data['order_id'],'total_amount'=>$totalMount]);
                    //创建成功修改订单为京东订单
                    OrderModel::where("order_id",intval($data['order_id']))->update(['order_type'=>2]);
                }
                // 增加操作日期
                if (empty($data['user_id'])) {
                    (new ActionLogModel())->addLog($data['order_id'], '创建订单', 'admin', 1);
                } else {
                    $userData = (new CmsModel())->getData('user_info',['userId'=>$data['user_id']],'find','name');
                    if (empty($userData['name'])) {
                        (new ActionLogModel())->addLog($data['order_id'], '创建订单', 'admin', 1);
                    } else {
                        (new ActionLogModel())->addLog($data['order_id'], '创建订单', $userData['name'], 1);
                    }
                }
            }else{
                $orderRepository->add_basic_order_info($data);
                $orderRepository->add_hongkong_delivery($data);
                $orderRepository->add_inland_delivery($data);
                $orderRepository->add_other_order_info($data);
            }

        }catch (\Exception $exception){
            //创建订单失败，修改订单为待提交
            throw new \Exception($exception->getMessage());
        }
        return 'ok';
    }

    static public function changeOrder($data)
    {
        $orderRepository = new OrderRepository();
        try{
            CommonLogic::checkField('changeOrder',$data);
            $orderRepository->add_basic_order_info($data);
            $orderRepository->add_hongkong_delivery($data);
            $orderRepository->add_inland_delivery($data);
            $orderRepository->add_other_order_info($data);
            //如果提交就提交订单，否则不提交
            if ($data['is_submit']){
//                self::changeSupplierLastUseTime($data);
                $orderRepository->goto_adding_order($data['order_id']);
            }
        }catch (\Exception $exception){
            throw new \Exception($exception->getMessage());
        }
        return 'ok';
    }

    //修改最后使用时间
    static public function changeSupplierLastUseTime($data)
    {
        SupplierModel::where('supplier_id',$data['supplier_id'])->update(['use_time'=>time()]);
        SupplierBankModel::where('supplier_bank_id',$data['supplier_bank_id'])->update(['use_time'=>time()]);
    }


    //获取个人中心的订单动态
    static public function getUserInfoBasicOrder($userId)
    {
        $dataObj = OrderModel::where('user_id',$userId)
            ->where('erp_order_sn','!=','')
            ->where('status','!=',-3)
            ->limit(4)
            ->orderBy('order_id','desc');

        $dataObj = $dataObj->where('is_hk_order',HongKongOrderLogic::$isHongKong);

        $data = $dataObj->select('erp_order_sn','status','create_time','order_id')
                ->get();
        if (empty($data)){
            return [];
        }
        $data = $data->toArray();
        foreach ($data as $key=>$val){
            $data[$key]['status_cn'] = array_get(OrderModel::$STATUS,$val['status']);

        }
        return $data;
    }


    //获取数据列表 状态顺序判断
    static public function getOrderList($data)
    {

        $obj = new OrderModel();
        $obj = $obj->where('id_edit_order',0)->where('erp_order_sn','!=','')->where('user_id',$data['user_id']);
        foreach ($data as $key=>$value){
            if (trim($value) === "")continue;
            switch ($key){
                case 'status':
                case 'erp_order_sn':
                case 'supplier_id':
                case 'is_hk_order':
                    $obj = $obj->where($key,$value);break;
                case 'begin_time':
                    $obj = $obj->where('create_time','>=',strtotime($value));break;
                case 'end_time':
                    $obj = $obj->where('create_time','<=',strtotime($value));break;
                case 'hk_delivery_status';
                case 'supplier_name':
                    $obj = $obj->where('supplier_name','like','%'.$value.'%');break;
                case 'baoguan_status';
                case 'sz_delivery_status';
                $orderIdArr = OrderStatusModel::where('user_id',$data['user_id'])->where($key,$value)->pluck('order_id');
                        $obj = $obj->whereIn('order_id',$orderIdArr);break;
                default:
                    continue;
            }
        }


        $page = I('get.page',0,'intval');
        $limit = I('get.limit',10,'intval');
        $list = $obj->orderBy('create_time','desc')->select("*")->paginate($limit,[],'page',$page)->toArray();
        if($page > ceil($list['total']/$limit)) throw new \Exception('没有更多数据');


        foreach ($list['data'] as &$v) {
            $v['order_status'] = self::getOrderStatus($v['order_id']);
            $v['status_cn'] = array_get(OrderModel::$STATUS,$v['status'],'');
            $v['currency_cn'] = array_get(C("supply_currency"),$v['currency_id'],'');

            if (!empty($v['old_erp_order_sn'])){
                $v['erp_order_sn'] = $v['erp_order_sn'].' 原入仓号（'.$v['old_erp_order_sn'].'）';
            }
            $v['hongkong_delivery_type_cn'] = array_get(OrderModel::$HongkongDeliveryType,$v['hongkong_delivery_type'],'');
            $v['have_payment_money'] = PaymentApplyModel::where('entrust_bill_number',$v['erp_order_sn'])->where('status',1)->sum('verifi_amount');
            if (empty($outboundType = InlandDeliveryModel::where('order_id',$v['order_id'])->value('outbound_type'))){
                $outboundType = 4;
            }
            $v['outbound_type_cn'] = array_get(C('outbound_type'),$outboundType,'暂无国内物流');
            $v['hk_port'] = HongKongDeliveryModel::where('order_id',$v['order_id'])->value('hk_port');
        }
        $list['order_num'] = self::getOrderNum($data['user_id']);

        return $list;
    }


    //获取数据列表
    static public function getEditOrderList($data)
    {
        $obj = (new OrderRecordModel())->where('user_id',$data['user_id']);
        foreach ($data as $key=>$value){
            if (trim($value)==="")continue;
            switch ($key){
                case 'erp_order_sn':
                case 'supplier_id':
                case 'is_hk_order':
                    $obj = $obj->where($key,$value);break;
                case 'status':
                    $obj = $obj->whereIn('order_id',OrderModel::where($key,$value)->where('company_id',(new \Supplychain\Repository\UserRepository)->getCompanyId(cookie('uid')))->pluck('order_id'));break;
                case 'begin_time':
                    $obj = $obj->where('create_time','>=',strtotime($value));break;
                case 'end_time':
                    $obj = $obj->where('create_time','<=',strtotime($value));break;
                default:
                    continue;
            }
        }


        $page = I('get.page',0,'intval');
        $limit = I('get.limit',10,'intval');
        $list = $obj->orderBy('create_time','desc')->select(self::$editSelectField)->paginate($limit,[],'page',$page)->toArray();
        if($page > ceil($list['total']/$limit)) throw new \Exception('没有更多数据');

        foreach ($list['data'] as &$v) {
            $orderInfo = OrderModel::where('order_id',$v['order_id'])->where('id_edit_order',1)->first();
            $v['change_type'] = self::getOrderRecordChangeType($v['change_type']);
            $v['order_status'] = self::getOrderStatus($v['order_id']);
            $v['status'] = $orderInfo->status;
            $v['amount'] = $orderInfo->order_price;
            $v['status_cn'] = array_get(OrderModel::$STATUS,$orderInfo->status,'');
            $v['hk_port'] = HongKongDeliveryModel::where('order_id',$v['order_id'])->value('hk_port');
        }
        $list['order_num'] = self::getOrderNum($data['user_id'],1);
        return $list;
    }

    static public function getOrderRecordChangeType($str='')
    {
        $returnStr = [];
        foreach (explode(',',$str) as $value){
            $returnStr[]= array_get(OrderRecordModel::$change_type, $value, '未知');
        }
        return $returnStr;
    }



    static public function getOrderStatus($orderId)
    {
       $data =  OrderStatusModel::where('order_id',$orderId)->select('pay_status','hk_delivery_status','baoguan_status','sz_delivery_status','sz_send_status')->first();
       if (!empty($data)) {
           $data['hk_delivery_status_cn'] = array_get(OrderStatusModel::$hkDeliveryStatus, $data['hk_delivery_status'], '');
           $data['baoguan_status_cn'] = array_get(OrderStatusModel::$baoguan_status, $data['baoguan_status'], '');
           $data['sz_delivery_status_cn'] = array_get(OrderStatusModel::$sz_delivery_status, $data['sz_delivery_status'], '');
       }else{
           $data['pay_status'] = '';
           $data['hk_delivery_status'] = '';
           $data['baoguan_status'] = '';
           $data['sz_delivery_status'] = '';
           $data['sz_send_status'] = '';
           $data['hk_delivery_status_cn'] = '';
           $data['baoguan_status_cn'] = '待报关';
           $data['sz_delivery_status_cn'] = '';
       }
       return $data;
    }

    static public function getOrderNum($userId,$isEdit=0)
    {
//        $orderObj = OrderModel::where('user_id',$userId)->where('id_edit_order',$isEdit);
        //审核中
        $data['audit_order'] = OrderModel::where('user_id',$userId)->where('erp_order_sn','!=','')->where('id_edit_order',$isEdit)->where('status',OrderModel::$AuditINGStatus)->count('order_id');
        //审核通过
        $data['pass_order'] = OrderModel::where('user_id',$userId)->where('erp_order_sn','!=','')->where('id_edit_order',$isEdit)->where('status',OrderModel::$PassStatus)->count('order_id');
        //审核不通过
        $data['no_pass_audit'] = OrderModel::where('user_id',$userId)->where('erp_order_sn','!=','')->where('id_edit_order',$isEdit)->where('status',OrderModel::$NOPassStatus)->count('order_id');
        //取消改单 已取消
        $data['cancel_order'] = OrderModel::where('user_id',$userId)->where('erp_order_sn','!=','')->where('id_edit_order',$isEdit)->where('status',OrderModel::$CancelStatus)->count('order_id');
        //草稿
        $data['wait_submit_order'] = OrderModel::where('user_id',$userId)->where('erp_order_sn','!=','')->where('id_edit_order',$isEdit)->where('status',OrderModel::$WaitToSubmitStatus)->count('order_id');
        return $data;
    }


    //获取当前的订单ID
    static public function getCurrentOrderStatus($orderInfo)
    {
        //1 下单，2审核订单，3代付款，4香港等收货，5待清关，6待配送,7完成
        $orderStatus = 1;
        if (intval($orderInfo['status']) === 0 ){
            $orderStatus = 2;
        }

        if (intval($orderInfo['status']) === 1 ){
            $orderStatus = 4;
        }

        if (HKDeliveryNoteModel::where('erp_order_sn',$orderInfo['erp_order_sn'])->value('id')){
            $orderStatus = 5;
        }

        if (SZDeliveryNoteModel::where('erp_order_sn',$orderInfo['erp_order_sn'])->value('id')){
            $orderStatus = 6;
        }

        if(SZShipmentsNoteModel::where('erp_order_sn',$orderInfo['erp_order_sn'])->value('id')){
            $orderStatus = 7;
        }
        return $orderStatus;
    }


    static public function getChangeOrderType()
    {
        $str = '';
        //获取修改类型
        if (isset($_POST['is_delivery']) && !empty($_POST['is_delivery'])){
            $str.='d,';
        }
        if (isset($_POST['is_attachment']) && !empty($_POST['is_attachment'])){
            $str.='a,';
        }
        if (isset($_POST['is_goods']) && !empty($_POST['is_goods'])){
            $str.='g,';
        }

        if (empty($str)){
            return 'all';
        }
        return trim($str,',');
    }

    //搜索商品信息
    static public function searchGoodInfo($post)
    {
        $returnData = OrderGoodsModel::where('goods_type','like',$post['goods_type'].'%')
            ->where('status',5)
            ->where('create_time','>=',1641541569)
            ->select('goods_title','goods_type','brand','origin','measurement')
            ->limit(20)
            ->orderBy('order_goods_id','desc')
            ->get();
        return $returnData;
    }


    //获取订单是否商检
    static public function getOrderIsGoodsCheck($data)
    {
        //查询所有的数据
        $orderGoodsData = OrderGoodsModel::where('order_id',$data['order_id'])->pluck('erp_other_param')->toArray();
        if (empty($orderGoodsData = array_filter($orderGoodsData))){
            return 2;
        }

        \Think\Log::write('$orderGoodsData:'.\GuzzleHttp\json_encode($orderGoodsData),'WARN');

        foreach ($orderGoodsData as $value){
            $erpOtherParam = \GuzzleHttp\json_decode($value,true);
            if ($erpOtherParam['ISINSPORG']){
                return 1;
            }
        }

        return 0;
    }

    //获取订单物流信息
    static public function getOrderlogistics($data)
    {
        if (empty($logisticsInfo = SZShipmentsNoteModel::where('order_id',$data['order_id'])->where('user_id',CommonLogic::getLoginUid())->first())){
            throw new \Exception('如需获取当前物流信息，请联系商务人员');
        }
        $logisticsInfo = $logisticsInfo->toArray();

        if (empty($LogisticCode = $logisticsInfo['stream_number'])){
            throw new \Exception('物流单号为空');
        }


        $config = C('KD100_CONFIG');
        $datas = array(
            'num' => $LogisticCode,
            'key' => $config['appKey'],
        );
        $info = get_curl($config['autoUrl'], $datas);
        $list = json_decode($info, true);
        if (empty($list)) {
            throw new \Exception('没有获取到快递公司');
        }
        $com = array_column($list, 'comCode');
        if (count($com) == 1) {
            $com = $com[0];
        }

        $info = C('KD100_CONFIG');
        $info['num'] = $LogisticCode;
        $info['com'] = $com;
        if ($com == 'shunfeng') {//顺丰需要传收寄件人手机号
            if (empty($phone = $logisticsInfo['tele_phone'])){
                throw new \Exception('联系人手机号为空');
            }
            $info['phone'] = $phone;
        }

         $json_data = array(
            'com' => $info['com'],
            'num' => $info['num'],
            'phone' => $info['phone'],
        );
        $requestData = json_encode($json_data);
        $datas = array(
            'customer' => $info['customer'],
            'param' => $requestData,
            'sign' => strtoupper(md5($requestData.$info['appKey'].$info['customer'])),
        );

        $logisticsInfoJson = post_curl($info['reqUrl'], $datas);

        $result = json_decode($logisticsInfoJson, true);
        if($result['message'] === 'ok') {
            foreach ($result['data'] as &$v) {
                //统一变量名
                $item = array(
                    'AcceptTime' => $v['time'],
                    'AcceptStation' => $v['context'],
                );
                $v = $item;
            }
            $result['out_store_time_cn'] = $logisticsInfo['create_time'];//出库时间
            $result['carrier'] = $logisticsInfo['carrier']; //配送方式
            return $result;
        }else {
            throw new \Exception('没有查找到物流信息:'.$result['message']);
        }
    }








}