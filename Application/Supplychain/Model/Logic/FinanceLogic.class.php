<?php
/**
 * Created by 2021/7/1.
 * User: Joneq
 * Info: 2021/7/1
 * Time: 下午3:10
 */

namespace Supplychain\Model\Logic;


use Supplychain\Model\OrderModel;

class FinanceLogic
{



    //待开票清单
    public function getNoInvoiceList($data)
    {
        $creditData = (new \Supplychain\Controller\ErpPushController)->getNoInvoiceList(json_encode($data));
        return $creditData;
    }

    //付款记录
    public function getPaymentList($data)
    {
        if(isset($data['is_hk_order']) && $data['is_hk_order']){
            $data['bizType'] = '9';
        }else{
            $data['bizType'] = '7';
        }
        $creditData = (new \Supplychain\Controller\ErpPushController)->getPaymentList(json_encode($data));
        return $creditData;
    }

    //订单列表
    public function getOrderList($data)
    {
        if(isset($data['is_hk_order']) && $data['is_hk_order']){
            $data['bizType'] = '富开本港单';
        }
        $creditData = (new \Supplychain\Controller\ErpPushController)->getOrderList(json_encode($data));
        return $creditData;
    }

    //订单详情
    public function getOrderDetails($data)
    {
        $creditData = (new \Supplychain\Controller\ErpPushController)->getOrderDetails(json_encode($data));
        return $creditData;
    }

    //付款详情
    public function getPaymentDetails($data)
    {
        $creditData = (new \Supplychain\Controller\ErpPushController)->getPaymentDetails(json_encode($data));
        return $creditData;
    }


    //添加附件
    public function addOrderAttachment($data)
    {
        $orderInfo = OrderModel::where('order_id',$data['order_id'])
            ->where('user_id',cookie('uid'))
            ->select('erp_order_sn','attachment','pick_box_file','payment_apply_file','order_invoice_file')
            ->first();
        if (empty($orderInfo)){
            throw new \Exception('暂无该条订单');
        }

        $updateData = ['number'=>$orderInfo->erp_order_sn,'attachment'=>$data['attachment']];
        $updateData['ORDERINVOICEFILE'] = $orderInfo->order_invoice_file;//发票文件
        $updateData['pickBoxFileAttach'] = $orderInfo->pick_box_file;//装箱单文件
        $updateData['paymentApplyFileAttach'] = $orderInfo->payment_apply_file;//付汇委托书文件

        $creditData = (new \Supplychain\Controller\ErpPushController)->addOrderAttachment(json_encode($updateData));
        OrderModel::where('order_id',$data['order_id'])->update(['attachment'=>$data['attachment']]);
        return $creditData;
    }

    //校验入仓号
    public function checkOrderNo($data)
    {
        $creditData = (new \Supplychain\Controller\ErpPushController)->checkOrderNo(json_encode($data));
        return $creditData;
    }

    //校验是否能改单
    public function checkChangeBill($data)
    {
        $creditData = (new \Supplychain\Controller\ErpPushController)->checkChangeBill(json_encode($data));
        return $creditData;
    }

    //待付汇列表
    public function getForPaymentList($data)
    {
        $creditData = (new \Supplychain\Controller\ErpPushController)->getForPaymentList(json_encode($data));
        return $creditData;
    }

    //同步物流信息接口
    public function synLogisticsInfo($data)
    {
        $creditData = (new \Supplychain\Controller\ErpPushController)->synLogisticsInfo(json_encode($data));
        return $creditData;
    }

    //香港入仓接口
    public function getHKInWoreHouse($data)
    {
        if(isset($data['is_hk_order']) && $data['is_hk_order']){
            $data['bizType'] = '9';
        }else{
            $data['bizType'] = '7';
        }
        $creditData = (new \Supplychain\Controller\ErpPushController)->getHKInWoreHouse(json_encode($data));
        return $creditData;
    }


    //付款通知书
    public function getPaymentNoticePrintData($data)
    {
        if (!$creditData = (new \Supplychain\Controller\ErpPushController)->getPaymentNoticePrintData(json_encode($data))){
            return [];
        }
        echo  self::getHtml($creditData);die;
    }

    //付款通知书
    public function getPaymentNoticePrintDataWechatPdf($data)
    {
        if (!$creditData = (new \Supplychain\Controller\ErpPushController)->getPaymentNoticePrintData(json_encode($data))){
            return [];
        }
        $pdfHtml = self::getHtml($creditData);

        vendor("mpdf.mpdf");
        $mpdf = new \mPDF('zh-cn','A2','','',32,25,27,25,16,13);
        $mpdf->WriteHTML($pdfHtml);
        $file = $data['number'].'_pay.pdf';
        $mpdf->Output($file,'f');
        $imgUrl = (new CommonLogic())->uploadToOss(realpath($file));
        $data = \GuzzleHttp\json_decode($imgUrl,true);
        if (!isset($data['code']) || $data['code'] != 200){
            throw new \Exception('文件上传失败');
        }
        unlink(realpath($file));
        return str_replace('http','https',$data['data'][0]);
    }



    //付款通知书
    public function getPaymentNoticePrintDataPdf($data)
    {
        if (!$creditData = (new \Supplychain\Controller\ErpPushController)->getPaymentNoticePrintData(json_encode($data))){
            return [];
        }
        $pdfHtml = self::getHtml($creditData);

        vendor("mpdf.mpdf");
        $mpdf = new \mPDF('zh-cn','A2','','',32,25,27,25,16,13);
        $mpdf->WriteHTML($pdfHtml);
        $mpdf->Output();
    }


    static public function getHtml($creditData)
    {
        $pic1 = 'data:image/png;base64,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';
        $pic2 = 'data:image/png;base64,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';
        $orderSn = $creditData['FNumber'];
        $FMainbusinessName = $creditData['FMainbusinessName'];
        $FCustomerName = $creditData['FCustomerName'];
        $date = $creditData['FBizDate'];

        $tr = '';
        $allAmount2 =  0;
        $FCurrencyName    ='';
        foreach ($creditData['Details'] as $key=>$value){
            $tr .= "<tr>
                <td>".$value['FEntrysProductName']."</td>
                <td>".$value['FEntrysModel']."</td>
                <td>".$value['FEntrysQty2']."</td>
                <td>".$value['FEntrysPrice2']."</td>
                <td>".$value['FEntrysAmount2']."</td>
               
                <td>".$value['FEntrysTariffRate']."</td>
                <td>".$value['FEntrysTariffAmount']."</td>
                
                <td>".$value['FEntrysOriginTax']."</td>
                <td>".$value['FEntrysOriginAmt']."</td>
                
                <td>".$value['FEntrysIsInspOrg']."</td>
                
                <td>".$value['FEntrysVatRate']."</td>
                <td>".$value['FEntrysVatAmount']."</td>
                
                <td>".$creditData['CFDlfRate']."</td>
                <td>".$value['FEntrysDlfAmount']."</td>
                
                <td>".$creditData['CFRate']."</td>
                <td>".round(($value['FEntrysAmount2']*$creditData['CFRate']+$value['FEntrysTariffAmount']+$value['FEntrysVatAmount']+$value['FEntrysOriginAmt'] +$value['FEntrysDlfAmount'])/$value['FEntrysQty2'],4)."</td>
                <td>".round(($value['FEntrysAmount2'] * $creditData['CFRate'] + $value['FEntrysDlfAmount']+ $value['FEntrysTariffAmount']+$value['FEntrysVatAmount']+$value['FEntrysOriginAmt'] ),2)."</td>
                <td></td>
            </tr>";
            $allAmount2 += $value['FEntrysAmount2'];
            $FCurrencyName = $value['FCurrencyName'];
        }




        $html = '
<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"><title>付款通知书</title></head><body style="margin: 0; padding: 0;">
<style>
    .table{text-align: center; border-collapse: collapse;}
    .table tr td {border: 1px solid #ccc}
</style>

<table class="table" cellpadding="0" cellspacing="0" width="95%" style="margin:  0 auto">
    <tr>
        <td rowspan="4"><img src="'.$pic1.'" alt=""></td>
        <td align="left">深圳市猎芯供应链有限公司</td>
        <td rowspan="4" colspan="2"><h1>付款通知书</h1></td>
        <td></td>
        <td align="right">第一页</td>
    </tr>
    <tr>
        <td align="left">地址：深圳市龙岗区坂田街道<br>清丽路1号<br>宝能科技园南区12栋10楼</td>
        <td align="right">委托单号：</td>
        <td align="left">'.$orderSn.'</td>
    </tr>
    <tr>
        <td align="left">电话：0755-82560956</td>
        <td align="right">业务：</td>
        <td align="left">'.$FMainbusinessName.'</td>
    </tr>
    <tr>
        <td align="left">传真：0755-82563952</td>
        <td align="right">商务：</td>
        <td align="left">'.trim($creditData['FCustomerserviceName']).'</td>
    </tr>
    <tr>
        <td align="right">客户：</td>
        <td align="left">'.$FCustomerName.'</td>
        <td align="right">供应商：</td>
        <td align="left">'.$creditData['FVendorName'].'</td>
        <td align="right">日期：</td>
        <td align="left">'.$date.'</td>
    </tr>

</table>


<table class="table" cellpadding="0" cellspacing="0" width="95%" style="margin:  0 auto">

    <thead class="firstRow">

        <tr>
            <td><b>开票品名</b></td>
            <td><b>型号</b></td>
            <td><b>卖方销售数量</b></td>
            <td><b>卖方销售单价</b></td>
            <td><b>卖方销售货值</b></td>
            <td><b>关税率</b></td>
            <td><b>关税额</b></td>
            <td><b>产地税率</b></td>
            <td><b>产地税额</b></td>
            <td><b>商检</b></td>
            <td><b>增值税率</b></td>
            <td><b>增值税额</b></td>
            <td><b>服务费率</b></td>
            <td><b>服务费(RMB)</b></td>
            <td><b>货款汇率</b></td>
            <td><b>含税单价(RMB)</b></td>
            <td><b>价税合计(RMB)</b></td>
            <td><b>杂费</b></td>
        </tr>
    </thead>
    <tbody>
    
        '.$tr.'
        <tr>
            <td colspan="2">卖方销售币别</td>
            <td>'.$FCurrencyName.'</td>
            <td colspan="3">'.$allAmount2.'</td>

            <td>'.$creditData['CFTariffAmount'].'</td>
            <td> </td>
            <td>'.$creditData['CFOriginAmt'].'</td>
            <td> </td>
            <td> </td>
            <td>'.$creditData['CFVatAmount'].'</td>
            <td> </td>
            <td>'.$creditData['CFDflAmount'].'</td>
            <td> </td>
            <td> </td>
            <td> </td>
            <td>'.$creditData['CFMiscellaneousFee'].'</td>
        </tr>
        <tr>
            <td colspan="3">货款合计（人民币）</td>
            <td colspan="3">'.round(($allAmount2*$creditData['CFRate']),2).'</td>
            <td colspan="2">应收金额</td>
            <td colspan="3">
                '.($creditData['CFArTotal']).'
            </td>
            <td colspan="8">应收金额=已付货款金额+已上货金额对应的税金和代理费+杂费</td>
        </tr>
        <tr>
            <td colspan="3">代理费计价差异 </td>
            <td colspan="3">'.$creditData['CFDlfDifference'].'</td>
        </tr>
        <tr>
            <td colspan="3">付款方式</td>
            <td colspan="3"></td>
            <td colspan="2">开票金额</td>
            <td colspan="3">
                '.$creditData['CFInvoiceAmount'].'
            </td>
            <td colspan="8">开票金额=已上货货款金额+已上货金额对应的税金和代理费+杂费</td>
        </tr>
        <tr>
            <td colspan="10" align="left" valign="top">
                备注: '.$creditData['FDescription'].'
            </td>
            <td colspan="4" align="left">
                <img src="'.$pic2.'" alt="">
            </td>
        </tr>
    </tbody>
</table>





<table class="table" cellpadding="0" cellspacing="0" width="95%" style="margin:  0 auto">
    <tbody>
    <tr class="firstRow">
        <td valign="middle" rowspan="1" colspan="14" style="word-break: break-all;" align="center">
            <b>重要提示</b>
        </td>
    </tr>

    <tr>
        <td  align="left" rowspan="1" colspan="14" >
            <b>尊敬的客户</b><br/>
        </td>
    </tr>

    <tr>
        <td  valign="top" align="left">请核对以上“商品名称、型号，数量，货款合计”，确认无误，请按以下金额安排付款。若于供应商送货前未提出异议，视同为确认此页面的报关内容。在实际到货无异常时，我司将按此内容进行进口申报。一旦申报通关后，将无法更改。 敬请知情！</td>
        </td>
    </tr>
    <tr>
        <td  valign="top" align="left">产地为美国的非集成电路电子产品，将有5%-25%的的美国产地关税，请与供应商核实来货产地 ！
    </tr>
    <tr>
        <td  valign="top" align="left">

            请将款项汇至：深圳市猎芯供应链有限公司    开户银行：中国建设银行股份有限公司深圳梅林支行        账号：44250 10000 69 00000 437
        </td>
    </tr>

    </tbody>
</table>
<p>
    <br/>
</p>
</body></html>';
        return $html;
    }


}