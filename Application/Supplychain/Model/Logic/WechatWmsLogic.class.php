<?php
/**
 * Created by 2021/12/3.
 * User: Joneq
 * Info: 2021/12/3
 * Time: 下午2:13
 */

namespace Supplychain\Model\Logic;


use Supplychain\Model\BaoGuanOrderListModel;
use Supplychain\Model\CmsModel;
use Supplychain\Model\WmsAccountModel;
use Supplychain\Model\WmsBoxNumSnModel;
use Supplychain\Model\WmsTrayBoxScanDetailModel;
use Supplychain\Model\WmsTrayListModel;
use Supplychain\Model\WmsTrayPrintLabelModel;

class WechatWmsLogic
{
    static public $userId;
    const WMSAPPID = 'wx61a41a064fa2fb3c';
    const WMSSECRET = '853f11f01d2e74849c768aca031412b3';
    const WMSACCOUNTPRE = 1000000;

    static public function login($data)
    {
        //如果不存在@ 则走账号登陆
        if (!strpos($data['account'],'@')){
            return self::wmsAccountLogin($data);
        }

        $returnData = post_curl(GenerateApiUrl('', 'user').'/api/login',[
            'name'=> $data['account'],
            'passwd'=> md5($data['passwd']),
        ], array('api-key: crm a1b2c3d4e5f6g7h8i9jk'));

        $returnData = \GuzzleHttp\json_decode($returnData,true);
        if (!isset($returnData['retcode']) || intval($returnData['retcode']) != 0){
            throw new \Exception('登陆失败');
        }

        $userId = $returnData['data']['userId'];

        $returnData = (new CmsModel())->getData('user_info',['userId'=>$returnData['data']['userId']],'find','name,email,status');
        if (intval($returnData['status']) == 4){
            throw new \Exception('该员工已经离职');
        }


        $returnData['token'] = self::passport_encrypt(strval($userId));
        return $returnData;
    }

    static public function wmsAccountLogin($data)
    {

        $accountInfo = WmsAccountModel::where('name',$data['account'])->where('password',$data['passwd'])->first();

        if (empty($accountInfo)){
            throw new \Exception('账号秘密不正确');
        }

        if (intval($accountInfo['job_status']) !== 1){
            throw new \Exception('离职人员不能登陆');
        }
        $returnData['name'] = $accountInfo['name'];
        $returnData['email'] = $accountInfo['relation_admin_account'];
        $returnData['status'] = $accountInfo['job_status'];
        $returnData['token'] = self::passport_encrypt(strval(self::WMSACCOUNTPRE+$accountInfo['wsat_id']));
        //更新最后登陆时间
        WmsAccountModel::where('wsat_id',$accountInfo['wsat_id'])->update([
            'last_login_time'=>time()
        ]);

        return $returnData;
    }


    static public function userInfo()
    {
        //如果存在则是wms账号用户
        if (self::$userId > self::WMSACCOUNTPRE){
            return WmsAccountModel::getLogInfo(self::$userId-self::WMSACCOUNTPRE);
        }else{
            $userId = self::$userId;
            $returnData = (new CmsModel())->getData('user_info',['userId'=>$userId],'find','name,email');
        }

        return $returnData;
    }


    //检测是否重复打印
    public function checkRepeatPrintLabel($data)
    {
        if (intval($data['batch']) !==  0){
            $data['erp_order_sn'] = $data['erp_order_sn'].'#'.$data['batch'];
        }

        if ((new WmsTrayBoxScanDetailModel())->getWhereObj(['erp_order_sn'=>$data['erp_order_sn']])->value('wstybxsndl_id')){
            throw new \Exception('该入仓号已经打印，请确认');
        }

        return 'ok';
    }

    //打印标签
    public function printLabel($data)
    {

        //生成数据
        $time = time();
        $userName = self::getCurrentName();
        $email = self::getCurrentEmail();


        //原始入仓号
        $baseErpOrderSn = $data['erp_order_sn'];
        if (intval($data['batch']) !==  0){
            $data['erp_order_sn'] = $data['erp_order_sn'].'#'.$data['batch'];
            $batch = '#'.$data['batch'];
            //$syncErpOrderSn = $syncErpOrderSn.'-'.$data['batch'];
        }else{
            $batch = '';
        }

//        ErpRequestLogic::getData([
//            'entrustNo'=>$syncErpOrderSn,
//            'type'=>'tagPrint',
//            'tagPrintDate'=>date('Y-m-d H:i:s'),
//        ],'synTallyDate');


        $printType = (new WmsTrayBoxScanDetailModel())->getWhereObj(['erp_order_sn'=>$data['erp_order_sn']])->value('print_type');

        //如果是追加打印就进行判断
        if (isset($data['is_add']) && intval($data['is_add']) === 1){
            //如果追加打印类型一致，就不允许打印
            if ($printType){
                if (intval($printType) === intval($data['print_type'])){
                    throw new \Exception('该入仓号已经打印了'.array_get(WmsTrayPrintLabelModel::$printType,intval($printType)).'不可追加打印相同类型');
                }
            }else{
                throw new \Exception('该入仓号暂未打印，不可追加打印');
            }

            $data['erp_order_sn'] .='*';
        }else{
            //如果打印类型不一致，就不允许打印
            if ($printType){
                throw new \Exception('该入仓号已经打印了,不可继续打印，请作废后重新打印');
            }
        }





        //如果是卡板返回总箱数，如果是散箱返回数量列表

        $insetId = WmsTrayPrintLabelModel::insertGetId([
            'erp_order_sn_pre'=>$data['batch'],
            'print_type'=>$data['print_type'],
            'erp_order_sn'=>$data['erp_order_sn'],
            'base_erp_order_sn'=>$baseErpOrderSn,
            'label_sort'=>$data['label_num'],
            'label_num'=>$data['label_num'],
//            'tray_remark'=>$data['tray_remark'],
            'create_time'=>$time,
            'create_uid'=>self::$userId,
            'create_user'=>$userName,
            'warehouse_code'=>'香港富开',
            'search_key'=>date('Ymd',$time)
        ]);

        $erpOrderSnSum = self::getErpOrderSum($insetId,$data['erp_order_sn'],$data['label_num']);

        $today = date('YmdHi',$time);
        if (intval($data['print_type']) === 1){



            //如果有多个卡板就循环打印
            if (isset($data['numbers']) && !empty($data['numbers'])){

                foreach ($data['numbers'] as $val){
                    $numVal = $val['value'];
                    $boxsn = self::getIdSn($insetId,$numVal);
                    $returnData[] = [
                        'erp_order_sn'=>$data['erp_order_sn'],
                        'batch'=>$batch,
                        'label_sort'=>($numVal+$erpOrderSnSum),
                        'label_num'=>($erpOrderSnSum+$numVal),
                        'create_time'=>$time,
                        'warehouse_code'=>'HK01',
                        'create_time_cn'=>$today,
                        'box_code'=>$data['erp_order_sn'].'-'.($numVal+$erpOrderSnSum).'/'.($erpOrderSnSum+$numVal),
                        'wstyptll_id_sn' => $boxsn,
                        'print_type'=>$data['print_type'],
                        'email'=>$email,
                        'wstyptll_id'=>$insetId
                    ];

                    //新增托盘箱子扫描记录
                    (new WmsTrayBoxScanDetailModel())->insertGetId([
                        'wstyptll_id'=>$insetId,
                        'box_sn'=>$boxsn,
                        'create_time'=>$time,
                        'create_user_name'=>self::getCurrentName(),
                        'sort'=>$numVal,
                        'erp_order_sn'=>$data['erp_order_sn'],
                        'base_erp_order_sn'=>$baseErpOrderSn,
                        'box_code'=>$data['erp_order_sn'].'-'.($numVal+$erpOrderSnSum).'/'.($erpOrderSnSum+$numVal),
                        'print_type'=>$data['print_type'],
                        'search_key'=>date('Ymd',$time),
                        'sum'=>$numVal,
                    ]);
                }

            }else{
                $boxsn = self::getIdSn($insetId,$data['label_num']);
                $returnData[] = [
                    'erp_order_sn'=>$data['erp_order_sn'],
                    'batch'=>$batch,
                    'label_sort'=>($data['label_num']+$erpOrderSnSum),
                    'label_num'=>($erpOrderSnSum+$data['label_num']),
                    'create_time'=>$time,
                    'warehouse_code'=>'HK01',
                    'create_time_cn'=>$today,
                    'box_code'=>$data['erp_order_sn'].'-'.($data['label_num']+$erpOrderSnSum).'/'.($erpOrderSnSum+$data['label_num']),
                    'wstyptll_id_sn' => $boxsn,
                    'print_type'=>$data['print_type'],
                    'email'=>$email,

                    'wstyptll_id'=>$insetId
                ];

                //新增托盘箱子扫描记录
                (new WmsTrayBoxScanDetailModel())->insertGetId([
                    'wstyptll_id'=>$insetId,
                    'box_sn'=>$boxsn,
                    'create_time'=>$time,
                    'create_user_name'=>self::getCurrentName(),
                    'sort'=>$data['label_num'],
                    'erp_order_sn'=>$data['erp_order_sn'],
                    'base_erp_order_sn'=>$baseErpOrderSn,
                    'box_code'=>$data['erp_order_sn'].'-'.($data['label_num']+$erpOrderSnSum).'/'.($erpOrderSnSum+$data['label_num']),
                    'print_type'=>$data['print_type'],
                    'search_key'=>date('Ymd',$time),
                    'sum'=>$data['label_num'],
                ]);
            }



        }else{

            for ($i=1;$i<=$data['label_num'];$i++){
                $boxsn = self::getIdSn($insetId,$i);
                $returnData[] = [
                    'erp_order_sn'=>$data['erp_order_sn'],
                    'batch'=>$batch,
                    'label_sort'=>($i+$erpOrderSnSum),
                    'label_num'=>($erpOrderSnSum+$data['label_num']),
                    'create_time'=>$time,
                    'warehouse_code'=>'HK01',
                    'create_time_cn'=>$today,
                    'wstyptll_id_sn' => $boxsn,
                    'print_type'=>$data['print_type'],
                    'email'=>$email,

                    'box_code'=>$data['erp_order_sn'].'-'.($i+$erpOrderSnSum).'/'.($erpOrderSnSum+$data['label_num']),
                    'wstyptll_id'=>$insetId
                ];

                //新增托盘箱子扫描记录
                (new WmsTrayBoxScanDetailModel())->insertGetId([
                    'wstyptll_id'=>$insetId,
                    'box_sn'=>$boxsn,
                    'box_code'=>$data['erp_order_sn'].'-'.($i+$erpOrderSnSum).'/'.($erpOrderSnSum+$data['label_num']),
                    'create_time'=>$time,
                    'create_user_name'=>self::getCurrentName(),
                    'sort'=>$i,
                    'erp_order_sn'=>$data['erp_order_sn'],
                    'base_erp_order_sn'=>$baseErpOrderSn,
                    'print_type'=>$data['print_type'],
                    'search_key'=>date('Ymd',$time),
                    'sum'=>$data['label_num'],
                ]);

            }
        }

        BaoGuanOrderListModel::updateIsPrintFromErpOrderSn($baseErpOrderSn);

        return $returnData;
    }


    //获取打印标签
    public function getPrintLabel($data)
    {
        //判断是否打印过了
        if (empty(WmsTrayPrintLabelModel::where('erp_order_sn',$data['erp_order_sn'])->value('wstyptll_id'))){
            throw new \Exception('无法补打');
        }

        $returnData = WmsTrayPrintLabelModel::where('erp_order_sn',$data['erp_order_sn'])

            ->select('erp_order_sn','label_sort','label_num','create_time','warehouse_code','wstyptll_id')->get();

        //获取之前所有的标签数量
        foreach ($returnData as &$val) {
            $val['label_sort'] = $data['label_sort'];
            $val['create_time_cn'] = date('YmdHi',$val['create_time']);
            $val['wstyptll_id_sn'] = self::getIdSn($val['wstyptll_id'],$val['label_sort']);

        }
        return $returnData;
    }

    //获取入仓号打印类型
    public function getErpOrderSnPrintLabelType($data)
    {
        //判断是否打印过了
        if (empty($printType = WmsTrayPrintLabelModel::where('erp_order_sn',$data['erp_order_sn'])->value('print_type'))){
            throw new \Exception('该入仓号没有打印记录');
        }
        return $printType;
    }

    //获取托盘下拉列表
    public function getTraySelectOption()
    {
        return WmsTrayListModel::where('status',1)->select('tray_sn','wstylt_id')->get()->toArray();
    }

    public function bindUserInfo($data)
    {
        if (empty($data['wechat_unionid'])){
            throw new \Exception('微信uionid不能为空');
        }

        if (WechatWmsLogic::$userId > self::WMSACCOUNTPRE){
            return WmsAccountModel::where('wsat_id',WechatWmsLogic::$userId-self::WMSACCOUNTPRE)->update(['wechat_unionid'=>$data['wechat_unionid']]);
        }else{
            return (new CmsModel())->updateData('user_info',['userId'=>WechatWmsLogic::$userId],['wechat_unionid'=>$data['wechat_unionid']]);
        }

    }


    public function unBindUserInfo($data)
    {
        if (WechatWmsLogic::$userId > self::WMSACCOUNTPRE){
            return WmsAccountModel::where('wsat_id',WechatWmsLogic::$userId-self::WMSACCOUNTPRE)->update(['wechat_unionid'=>'']);
        }else{
            return (new CmsModel())->updateData('user_info',['userId'=>WechatWmsLogic::$userId],['wechat_unionid'=>'']);
        }
    }


    public function getWechatInfo($data)
    {
        $code = $data['code'];

        $client = new \GuzzleHttp\Client();
        $response = $client->request('GET','https://api.weixin.qq.com/sns/jscode2session',[
            'query' => [
                'appid' => WechatWmsLogic::WMSAPPID,
                'secret' =>WechatWmsLogic::WMSSECRET,
                'js_code' => $code,
                'grant_type' => 'authorization_code',
            ]
        ]);



        $res = json_decode($response->getBody()->getContents(),true);


        if(!isset($res['unionid']) || empty($res['unionid'])) {
            throw new \Exception('获取微信返回数据失败');
        }


        $returnData = (new CmsModel())->getData('user_info',['wechat_unionid'=>$res['unionid']],'find','userId,status');


        if (empty($returnData)){

            $wmsAccountInfo = WmsAccountModel::where('wechat_unionid',$res['unionid'])->first();

            if (empty($wmsAccountInfo)){
                return ['token'=>'','wechat_info'=>$res];
            }

            if (intval($wmsAccountInfo['job_status']) !== 1){
                throw new \Exception('离职人员不能登陆');
            }

            $returnData['userid'] = self::WMSACCOUNTPRE + $wmsAccountInfo['wsat_id'];
        }else{
            if (intval($returnData['status']) == 4){
                throw new \Exception('该员工已经离职');
            }
            throw new \Exception('邮箱账号不支持一键登录');
        }


        //如果绑定了，直接返回用户的token
        return ['token'=>WechatWmsLogic::passport_encrypt(strval($returnData['userid'])),'wechat_info'=>$res];
    }




    static public function getIdSn($lasgRid,$labelSort)
    {
        //获取之前所有的标签数量 + 当前的标签数量，散箱子有几个就是几个，整箱子只有一个
        $lasgRid = WmsTrayPrintLabelModel::where('wstyptll_id','<',$lasgRid)->where('print_type',2)->sum('label_num')+WmsTrayPrintLabelModel::where('wstyptll_id','<',$lasgRid)->where('print_type',1)->count('wstyptll_id');
        $id = $lasgRid+$labelSort;
        $pre = 'R';
        if ($id < 1000000){
            $id = substr(strval(1000000+$id),1);
        }
        $number = strval($id);
        return $pre.$number;
    }

    static public function getErpOrderSum($insetId,$erpOrderSn)
    {
        //获取之前所有的标签数量 + 当前的标签数量
        $sum = WmsTrayBoxScanDetailModel::where('wstyptll_id','<',$insetId)->where('erp_order_sn',$erpOrderSn)->where('print_type',2)->where('is_del',0)->where('erp_order_sn',$erpOrderSn)->count('wstybxsndl_id')+WmsTrayBoxScanDetailModel::where('wstyptll_id','<',$insetId)->where('erp_order_sn',$erpOrderSn)->where('print_type',1)->where('is_del',0)->where('erp_order_sn',$erpOrderSn)->sum('sum');
        return $sum;
    }


    //获取当前人登陆人的姓名
    static public function getCurrentName()
    {
        if (WechatWmsLogic::$userId > self::WMSACCOUNTPRE){
            return WmsAccountModel::where('wsat_id',WechatWmsLogic::$userId-self::WMSACCOUNTPRE)->value('name');
        }else{
            return (new CmsModel())->getData('user_info',['userId'=>WechatWmsLogic::$userId],'find','name')['name'];
        }
    }

    static public function getNewCurrentName()
    {
        if (WechatWmsLogic::$userId > self::WMSACCOUNTPRE){
            return WmsAccountModel::where('wsat_id',WechatWmsLogic::$userId-self::WMSACCOUNTPRE)->value('name');
        }else{
            return (new CmsModel())->getData('user_info',['userId'=>WechatWmsLogic::$userId],'find','name')['name'];
        }
    }

    static public function getCurrentUserId()
    {
        $currentEmail = self::getCurrentEmail();
        $userId = (new CmsModel())->getData('user_info',['email'=>$currentEmail],'find','userId')['userid'];
        return empty($userId) ? 0 : $userId;
    }

    static public function getCurrentUserIdByEmail($email)
    {
        $userId = (new CmsModel())->getData('user_info',['email'=>$email],'find','userId')['userid'];
        return empty($userId) ? 0 : $userId;
    }

    static public function getCurrentUserIdByName($salesName)
    {
        $userId = (new CmsModel())->getData('user_info',['name'=>$salesName],'find','userId')['userid'];
        return empty($userId) ? 0 : $userId;
    }

    //获取当前人登陆人的姓名
    static public function getDepartmentName($salesName)
    {
        $departmentName =  (new CmsModel())->getData('user_info',['name'=>$salesName],'find','department_name')['department_name'];
        if (empty($departmentName)){
            return '/暂无部门';
        }
        return '/'.$departmentName;
    }

    //获取当前人登陆人的姓名
    static public function getCurrentEmail()
    {
        if (WechatWmsLogic::$userId > self::WMSACCOUNTPRE){
            return WmsAccountModel::where('wsat_id',WechatWmsLogic::$userId-self::WMSACCOUNTPRE)->value('relation_admin_account');
        }else{
            return (new CmsModel())->getData('user_info',['userId'=>WechatWmsLogic::$userId],'find','email')['email'];
        }
    }


    public function submitBoxSnAndNum($requestData)
    {
        $time = time();
        $noDelId = [];
        $wmsBoxNumSnModel = new WmsBoxNumSnModel();

        $list = \GuzzleHttp\json_decode($requestData['list'],true);


        foreach ($list as $key=>$value){

            //当前箱子有这个数据更新，没有就新增
            if ($value['wsbxnmsn_id'] === ''){
                $noDelId[] = $wmsBoxNumSnModel->insertGetId([
                    'goods_sn'=>$value['goods_sn'],
                    'goods_num'=>$value['goods_num'],
                    'box_sn'=>$requestData['box_sn'],
                    'scan_time'=>$time
                ]);
            }else{
                $noDelId[] = $value['wsbxnmsn_id'];
                $wmsBoxNumSnModel->where('wsbxnmsn_id',$value['wsbxnmsn_id'])->update([
                    'goods_sn'=>$value['goods_sn'],
                    'goods_num'=>$value['goods_num'],
                ]);
            }
        }

        $wmsBoxNumSnModel->where('box_sn',$requestData['box_sn'])->whereNotIn('wsbxnmsn_id',$noDelId)->update(['is_del'=>1]);
    }

    public function getBoxSnAndNum($requestData)
    {
        $wmsBoxNumSnModel = new WmsBoxNumSnModel();
        if (isset($requestData['box_sn']) && !empty($requestData['box_sn'])){

            $returnData = $wmsBoxNumSnModel->where('is_del',0)->orderBy('wsbxnmsn_id','desc')->select(['goods_num','goods_sn','scan_time','wsbxnmsn_id'])->where('box_sn',$requestData['box_sn'])->get()->toArray();
            foreach ($returnData as $key=>&$value){
                $value['id'] = $key+1;
                $value['scan_time_cn'] = date('Y-m-d H:i:s',$value['scan_time']);
            }

            if (isset($requestData['export']) && (intval($requestData['export']) === 1)){
                return ExportLogic::exportExcelNormal('BoxSnAndNum',$returnData,$requestData['box_sn'].'数据汇总');
            }

        }else{
            $returnData = $wmsBoxNumSnModel->where('is_del',0)->selectRaw('box_sn,count(box_sn) as box_sn_goods_num')->groupBy('box_sn')->get();
            foreach ($returnData as $key=>&$value){
                $value['scan_time'] = $wmsBoxNumSnModel->where('is_del',0)->orderBy('scan_time','desc')->value('scan_time');
                $value['scan_time_cn'] = date('Y-m-d H:i:s',$value['scan_time']);
            }


        }
        return $returnData;
    }



    public function identifyQrCodeNumAndSn($data)
    {
        $num = 0;
        $sn = '';
        $code = $data['code_str'];

        switch ($data['code_type']){
            case 'Chip1stop'://NY0015615541 %$G01-00020 PANA-0338532 102
                $codeArr = explode(' ',$code);
                $num = $codeArr[3];
                $sn = $codeArr[2];
                break;
            case 'Rochester': //[)>06KB1298541PR1LV0108ESF-5SI#B0Q800.009D12144LCN1T4515198
                $codeArr = explode('_',$code);
                $num = trim(explode('.',$codeArr[6])[0],'Q');
                $sn = trim($codeArr[4],'1P');
                break;
            case 'TME': //QTY:500 PN:PR03-1R CPN:PR03000201008JAC00 PO:18365781/11 CPO:B131190+A64120 MFR:VISHAY MPN:PR03000201008JAC00 RoHS
                $codeArr = explode(' ',$code);
                $num = trim($codeArr[0],'QTY:');
                $sn = explode(':',$codeArr[1])[1];
                break;
            case 'digikey': //[)>06PECH-U1H152GB51PECH-U1H152GB5KB577651K6593718010K7605250111K14LJPQ111ZPICK12Z25598813Z44458920Z000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
                $qEnd = strripos($code,'11ZPICK');
                $qStart = strripos($code,'Q');
                if (empty($qStart)){
                    throw new \Exception('数量识别错误');
                }
                $num = substr($code,$qStart+1,($qEnd-$qStart)-1);

                $snStart = strripos($code,'1P');
                if (empty($snStart)){
                    throw new \Exception('型号识别错误');
                }
                $snEndOne = strripos($code,'KA');
                $snEndTwo = strripos($code,'KB');

                if (intval($snEndOne) > intval($snEndTwo)){
                    $snEnd = $snEndOne;
                }else{
                    $snEnd = $snEndTwo;
                }
                $sn = substr($code,$snStart+2,($snEnd-$snStart)-2);

                break;
            default:
                throw new \Exception('暂不支持这种规则扫码');
        }

        if ($num == 0 || $sn== ''){
            throw new \Exception('扫描识别失败');
        }

        return ['sn'=>$sn,'num'=>$num];
    }



    static public function passport_encrypt($txt, $key = 'signfor') {
        srand((double)microtime() * 1000000);
        $encrypt_key = md5(rand(0, 32000));
        $ctr = 0;
        $tmp = '';
        for($i = 0;$i < strlen($txt); $i++) {
            $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
            $tmp .= $encrypt_key[$ctr].($txt[$i] ^ $encrypt_key[$ctr++]);
        }
        return urlencode(base64_encode(self::passport_key($tmp, $key)));
    }

    static public function passport_key($txt, $encrypt_key) {
        $encrypt_key = md5($encrypt_key);
        $ctr = 0;
        $tmp = '';
        for($i = 0; $i < strlen($txt); $i++) {
            $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
            $tmp .= $txt[$i] ^ $encrypt_key[$ctr++];
        }
        return $tmp;
    }

    static public function passport_decrypt($txt, $key = 'signfor') {
        $txt = self::passport_key(base64_decode(urldecode($txt)), $key);
        $tmp = '';
        for($i = 0;$i < strlen($txt); $i++) {
            $md5 = $txt[$i];
            $tmp .= $txt[++$i] ^ $md5;
        }
        return $tmp;
    }


    static public function getParam()
    {

        if (!empty($data = file_get_contents("php://input"))){

            $data = \GuzzleHttp\json_decode(file_get_contents("php://input"),true);
        }else{
            $data = $_REQUEST;

        }
        return $data;
    }



}
