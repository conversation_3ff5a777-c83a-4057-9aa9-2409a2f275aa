<?php
/**
 * Created by 2023/5/17.
 * User: <PERSON>F<PERSON>
 * Info: ...
 * Time: 下午5:17
 */

namespace Supplychain\Model\Logic;

class WmsRequestLogic
{

    protected function requestPur($method,$data)
    {


        $res = post_curl(WMS_DOMAIN . $method, $data);
        $res = json_decode($res, true);

        if ($res['code'] != 0){
            throw new \Exception($res['msg']);
        }
        return $res;

    }



    public function printLabel($data)
    {
        $printJson = [
            'abnormal_batch' => $data['abnormal_batch'],
            'erp_order_sn' => $data['erp_order_sn'],
            'is_usa' => $data['is_usa'],
            'is_goods_check' => $data['is_goods_check'],
            'goods_type' => $data['goods_type'],
            'brand'=>$data['brand'],
            'tally_num'=>$data['tally_num'],
        ];

        $data = [
            'type'=>2,
            'email'=> WechatWmsLogic::getCurrentEmail(),
            'print_json' => \GuzzleHttp\json_encode([$printJson])
        ];

        $this->requestPur('/open/label/addSCTallyData',$data);
    }







}