<?php
/**
 * Created by 2021/7/14.
 * User: Joneq
 * Info: 2021/7/14
 * Time: 下午5:43
 */

namespace Supplychain\Model\Logic\JdpayRequest;



class OrderStatus implements \JsonSerializable
{
    /**
     * type: String
     */
    private $reqNo;
    /**
     * type: String
     */
    private $reqDate;
    /**
     * type: String
     */
    private $productCode;
    /**
     * type: List
     */
    private $orders;
    /**
     * type: Map
     */
    private $ext;

    public function getReqNo() {
        return $this->reqNo;
    }

    public function setReqNo($reqNo) {
        $this->reqNo = $reqNo;
    }
    public function getReqDate() {
        return $this->reqDate;
    }

    public function setReqDate($reqDate) {
        $this->reqDate = $reqDate;
    }
    public function getProductCode() {
        return $this->productCode;
    }

    public function setProductCode($productCode) {
        $this->productCode = $productCode;
    }
    public function getOrders() {
        return $this->orders;
    }

    public function setOrders($orders) {
        $this->orders = $orders;
    }
    public function getExt() {
        return $this->ext;
    }

    public function setExt($ext) {
        $this->ext = $ext;
    }

    public function jsonSerialize()
    {
        $data = [];
        foreach ($this as $key => $val) {
            if ($val !== null) {
                $data[$key] = $val;
            }
        }
        return $data;
    }
}