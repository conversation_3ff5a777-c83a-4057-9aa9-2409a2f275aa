<?php
/**
 * Created by 2021/7/14.
 * User: Joneq
 * Info: 2021/7/14
 * Time: 下午5:46
 */

namespace Supplychain\Model\Logic\JdpayRequest;



class Order implements \JsonSerializable
{
    /**
     * type: String
     */
    private $orderCode;
    /**
     * type: String
     */
    private $orderDesc;
    /**
     * type: String
     */
    private $orderStatus;
    /**
     * type: String
     */
    private $payStatus;

    public function getOrderCode() {
        return $this->orderCode;
    }

    public function setOrderCode($orderCode) {
        $this->orderCode = $orderCode;
    }
    public function getOrderDesc() {
        return $this->orderDesc;
    }

    public function setOrderDesc($orderDesc) {
        $this->orderDesc = $orderDesc;
    }
    public function getOrderStatus() {
        return $this->orderStatus;
    }

    public function setOrderStatus($orderStatus) {
        $this->orderStatus = $orderStatus;
    }
    public function getPayStatus() {
        return $this->payStatus;
    }

    public function setPayStatus($payStatus) {
        $this->payStatus = $payStatus;
    }

    public function jsonSerialize()
    {
        $data = [];
        foreach ($this as $key => $val) {
            if ($val !== null) {
                $data[$key] = $val;
            }
        }
        return $data;
    }
}