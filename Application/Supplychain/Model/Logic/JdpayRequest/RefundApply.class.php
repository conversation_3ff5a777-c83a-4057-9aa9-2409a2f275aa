<?php
/**
 * Created by 2021/7/15.
 * User: Joneq
 * Info: 2021/7/15
 * Time: 下午2:34
 */

namespace Supplychain\Model\Logic\JdpayRequest;
class RefundApply implements \JsonSerializable
{
    /**
     * type: String
     */
    private $bizNo;
    /**
     * type: String
     */
    private $reqDate;
    /**
     * type: String
     */
    private $productCode;
    /**
     * type: String
     */
    private $settleUserCode;
    /**
     * type: String
     */
    private $orderCode;
    /**
     * type: String
     */
    private $refundDesc;
    /**
     * type: String
     */
    private $refundAmount;
    /**
     * type: String
     */
    private $notifyUrl;
    /**
     * type: Map
     */
    private $ext;
    /**
     * type: String
     */
    private $custCode;
    /**
     * type: Map
     */
    private $riskControlParams;

    public function getBizNo() {
        return $this->bizNo;
    }

    public function setBizNo($bizNo) {
        $this->bizNo = $bizNo;
    }
    public function getReqDate() {
        return $this->reqDate;
    }

    public function setReqDate($reqDate) {
        $this->reqDate = $reqDate;
    }
    public function getProductCode() {
        return $this->productCode;
    }

    public function setProductCode($productCode) {
        $this->productCode = $productCode;
    }
    public function getSettleUserCode() {
        return $this->settleUserCode;
    }

    public function setSettleUserCode($settleUserCode) {
        $this->settleUserCode = $settleUserCode;
    }
    public function getOrderCode() {
        return $this->orderCode;
    }

    public function setOrderCode($orderCode) {
        $this->orderCode = $orderCode;
    }
    public function getRefundDesc() {
        return $this->refundDesc;
    }

    public function setRefundDesc($refundDesc) {
        $this->refundDesc = $refundDesc;
    }
    public function getRefundAmount() {
        return $this->refundAmount;
    }

    public function setRefundAmount($refundAmount) {
        $this->refundAmount = $refundAmount;
    }
    public function getNotifyUrl() {
        return $this->notifyUrl;
    }

    public function setNotifyUrl($notifyUrl) {
        $this->notifyUrl = $notifyUrl;
    }
    public function getExt() {
        return $this->ext;
    }

    public function setExt($ext) {
        $this->ext = $ext;
    }
    public function getCustCode() {
        return $this->custCode;
    }

    public function setCustCode($custCode) {
        $this->custCode = $custCode;
    }
    public function getRiskControlParams() {
        return $this->riskControlParams;
    }

    public function setRiskControlParams($riskControlParams) {
        $this->riskControlParams = $riskControlParams;
    }

    public function jsonSerialize()
    {
        $data = [];
        foreach ($this as $key => $val) {
            if ($val !== null) {
                $data[$key] = $val;
            }
        }
        return $data;
    }
}