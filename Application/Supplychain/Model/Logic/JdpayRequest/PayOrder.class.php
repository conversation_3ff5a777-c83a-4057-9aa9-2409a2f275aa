<?php
/**
 * Created by 2021/7/14.
 * User: Joneq
 * Info: 2021/7/14
 * Time: 下午4:06
 */

namespace Supplychain\Model\Logic\JdpayRequest;

class PayOrder implements \JsonSerializable
{
    /**
     * type: String
     */
    private $bizNo;
    /**
     * type: String
     */
    private $reqDate;
    /**
     * type: String
     */
    private $productCode;
    /**
     * type: String
     */
    private $custCode;
    /**
     * type: String
     */
    private $orderCode;
    /**
     * type: BigDecimal
     */
    private $orderAmount;
    /**
     * type: BigDecimal
     */
    private $applyAmount;
    /**
     * type: String
     */
    private $orderIndate;
    /**
     * type: String
     */
    private $orderDesc;
    /**
     * type: List
     */
    private $productCategorys;
    /**
     * type: String
     */
    private $orderStatus;
    /**
     * type: String
     */
    private $payStatus;
    /**
     * type: String
     */
    private $createDate;
    /**
     * type: String
     */
    private $receiveCardNo;
    /**
     * type: String
     */
    private $receiveName;
    /**
     * type: Map
     */
    private $riskControlParams;
    /**
     * type: Map
     */
    private $attachment;
    /**
     * type: String
     */
    private $notifyUrl;
    /**
     * type: Map
     */
    private $ext;
    /**
     * type: String
     */
    private $settleUserCode;
    /**
     * type: Integer
     */
    private $termCount;
    /**
     * type: String
     */
    private $repayNotifyUrl;
    /**
     * type: Map
     */
    private $subOrders;

    public function getBizNo() {
        return $this->bizNo;
    }

    public function setBizNo($bizNo) {
        $this->bizNo = $bizNo;
    }
    public function getReqDate() {
        return $this->reqDate;
    }

    public function setReqDate($reqDate) {
        $this->reqDate = $reqDate;
    }
    public function getProductCode() {
        return $this->productCode;
    }

    public function setProductCode($productCode) {
        $this->productCode = $productCode;
    }
    public function getCustCode() {
        return $this->custCode;
    }

    public function setCustCode($custCode) {
        $this->custCode = $custCode;
    }
    public function getOrderCode() {
        return $this->orderCode;
    }

    public function setOrderCode($orderCode) {
        $this->orderCode = $orderCode;
    }
    public function getOrderAmount() {
        return $this->orderAmount;
    }

    public function setOrderAmount($orderAmount) {
        $this->orderAmount = $orderAmount;
    }
    public function getApplyAmount() {
        return $this->applyAmount;
    }

    public function setApplyAmount($applyAmount) {
        $this->applyAmount = $applyAmount;
    }
    public function getOrderIndate() {
        return $this->orderIndate;
    }

    public function setOrderIndate($orderIndate) {
        $this->orderIndate = $orderIndate;
    }
    public function getOrderDesc() {
        return $this->orderDesc;
    }

    public function setOrderDesc($orderDesc) {
        $this->orderDesc = $orderDesc;
    }
    public function getProductCategorys() {
        return $this->productCategorys;
    }

    public function setProductCategorys($productCategorys) {
        $this->productCategorys = $productCategorys;
    }
    public function getOrderStatus() {
        return $this->orderStatus;
    }

    public function setOrderStatus($orderStatus) {
        $this->orderStatus = $orderStatus;
    }
    public function getPayStatus() {
        return $this->payStatus;
    }

    public function setPayStatus($payStatus) {
        $this->payStatus = $payStatus;
    }
    public function getCreateDate() {
        return $this->createDate;
    }

    public function setCreateDate($createDate) {
        $this->createDate = $createDate;
    }
    public function getReceiveCardNo() {
        return $this->receiveCardNo;
    }

    public function setReceiveCardNo($receiveCardNo) {
        $this->receiveCardNo = $receiveCardNo;
    }
    public function getReceiveName() {
        return $this->receiveName;
    }

    public function setReceiveName($receiveName) {
        $this->receiveName = $receiveName;
    }
    public function getRiskControlParams() {
        return $this->riskControlParams;
    }

    public function setRiskControlParams($riskControlParams) {
        $this->riskControlParams = $riskControlParams;
    }
    public function getAttachment() {
        return $this->attachment;
    }

    public function setAttachment($attachment) {
        $this->attachment = $attachment;
    }
    public function getNotifyUrl() {
        return $this->notifyUrl;
    }

    public function setNotifyUrl($notifyUrl) {
        $this->notifyUrl = $notifyUrl;
    }
    public function getExt() {
        return $this->ext;
    }

    public function setExt($ext) {
        $this->ext = $ext;
    }
    public function getSettleUserCode() {
        return $this->settleUserCode;
    }

    public function setSettleUserCode($settleUserCode) {
        $this->settleUserCode = $settleUserCode;
    }
    public function getTermCount() {
        return $this->termCount;
    }

    public function setTermCount($termCount) {
        $this->termCount = $termCount;
    }
    public function getRepayNotifyUrl() {
        return $this->repayNotifyUrl;
    }

    public function setRepayNotifyUrl($repayNotifyUrl) {
        $this->repayNotifyUrl = $repayNotifyUrl;
    }
    public function getSubOrders() {
        return $this->subOrders;
    }

    public function setSubOrders($subOrders) {
        $this->subOrders = $subOrders;
    }

    public function jsonSerialize()
    {
        $data = [];
        foreach ($this as $key => $val) {
            if ($val !== null) {
                $data[$key] = $val;
            }
        }
        return $data;
    }
}