<?php
/**
 * Created by 2021/7/13.
 * User: Joneq
 * Info: 2021/7/13
 * Time: 上午9:42
 */
namespace Supplychain\Model\Logic\JdpayRequest;


class PreCreditWhiteRequest implements \JsonSerializable
{
    /**
     * type: String
     */
    private $reqNo;
    /**
     * type: String
     */
    private $reqDate;
    /**
     * type: String
     */
    private $productCode;
    /**
     * type: Integer
     */
    private $batchNo;
    /**
     * type: Boolean
     */
    private $batchSendFinish;
    /**
     * type: List
     */
    private $whiteList;

    public function getReqNo() {
        return $this->reqNo;
    }

    public function setReqNo($reqNo) {
        $this->reqNo = $reqNo;
    }
    public function getReqDate() {
        return $this->reqDate;
    }

    public function setReqDate($reqDate) {
        $this->reqDate = $reqDate;
    }
    public function getProductCode() {
        return $this->productCode;
    }

    public function setProductCode($productCode) {
        $this->productCode = $productCode;
    }
    public function getBatchNo() {
        return $this->batchNo;
    }

    public function setBatchNo($batchNo) {
        $this->batchNo = $batchNo;
    }
    public function getBatchSendFinish() {
        return $this->batchSendFinish;
    }

    public function setBatchSendFinish($batchSendFinish) {
        $this->batchSendFinish = $batchSendFinish;
    }
    public function getWhiteList() {
        return $this->whiteList;
    }

    public function setWhiteList($whiteList) {
        $this->whiteList = $whiteList;
    }

    public function jsonSerialize()
    {
        $data = [];
        foreach ($this as $key => $val) {
            if ($val !== null) {
                $data[$key] = $val;
            }
        }
        return $data;
    }
}