<?php
/**
 * Created by 2021/8/11.
 * User: Joneq
 * Info: 2021/8/11
 * Time: 下午4:10
 */

namespace Supplychain\Model\Logic\JdpayRequest;


class HttpServletRequest
{
    private $encrypt;
    private $gwEncryptType;
    private $jrgwEnvKey;
    private $gwSign;
    private $jrgwNotifyNo;
    private $jrgwRequestTime;

    /**
     * @return mixed
     */
    public function getEncrypt()
    {
        return $this->encrypt;
    }

    /**
     * @return mixed
     */
    public function getGwEncryptType()
    {
        return $this->gwEncryptType;
    }

    /**
     * @return mixed
     */
    public function getGwSign()
    {
        return $this->gwSign;
    }

    /**
     * @return mixed
     */
    public function getJrgwEnvKey()
    {
        return $this->jrgwEnvKey;
    }

    /**
     * @param mixed $jrgwRequestTime
     */
    public function setJrgwRequestTime($jrgwRequestTime)
    {
        $this->jrgwRequestTime = $jrgwRequestTime;
    }

    /**
     * @param mixed $jrgwNotifyNo
     */
    public function setJrgwNotifyNo($jrgwNotifyNo)
    {
        $this->jrgwNotifyNo = $jrgwNotifyNo;
    }

    /**
     * @return mixed
     */
    public function getJrgwNotifyNo()
    {
        return $this->jrgwNotifyNo;
    }

    /**
     * @param mixed $gwEncryptType
     */
    public function setGwEncryptType($gwEncryptType)
    {
        $this->gwEncryptType = $gwEncryptType;
    }

    /**
     * @return mixed
     */
    public function getJrgwRequestTime()
    {
        return $this->jrgwRequestTime;
    }

    /**
     * @param mixed $encrypt
     */
    public function setEncrypt($encrypt)
    {
        $this->encrypt = $encrypt;
    }

    /**
     * @param mixed $gwSign
     */
    public function setGwSign($gwSign)
    {
        $this->gwSign = $gwSign;
    }

    /**
     * @param mixed $jrgwEnvKey
     */
    public function setJrgwEnvKey($jrgwEnvKey)
    {
        $this->jrgwEnvKey = $jrgwEnvKey;
    }
}