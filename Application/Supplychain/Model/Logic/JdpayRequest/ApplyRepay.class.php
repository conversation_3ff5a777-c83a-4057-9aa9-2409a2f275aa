<?php
/**
 * Created by 2021/7/15.
 * User: Joneq
 * Info: 2021/7/15
 * Time: 下午1:51
 */

namespace Supplychain\Model\Logic\JdpayRequest;
class ApplyRepay implements \JsonSerializable
{
    /**
     * type: String
     */
    private $bizNo;
    /**
     * type: String
     */
    private $reqDate;
    /**
     * type: String
     */
    private $productCode;
    /**
     * type: String
     */
    private $custCode;
    /**
     * type: String
     */
    private $orderCode;
    /**
     * type: String
     */
    private $repayPayWay;
    /**
     * type: String
     */
    private $repayFormType;
    /**
     * type: String
     */
    private $source;
    /**
     * type: BigDecimal
     */
    private $amount;
    /**
     * type: Map
     */
    private $ext;
    /**
     * type: List
     */
    private $orderCodeList;

    public function getBizNo() {
        return $this->bizNo;
    }

    public function setBizNo($bizNo) {
        $this->bizNo = $bizNo;
    }
    public function getReqDate() {
        return $this->reqDate;
    }

    public function setReqDate($reqDate) {
        $this->reqDate = $reqDate;
    }
    public function getProductCode() {
        return $this->productCode;
    }

    public function setProductCode($productCode) {
        $this->productCode = $productCode;
    }
    public function getCustCode() {
        return $this->custCode;
    }

    public function setCustCode($custCode) {
        $this->custCode = $custCode;
    }
    public function getOrderCode() {
        return $this->orderCode;
    }

    public function setOrderCode($orderCode) {
        $this->orderCode = $orderCode;
    }
    public function getRepayPayWay() {
        return $this->repayPayWay;
    }

    public function setRepayPayWay($repayPayWay) {
        $this->repayPayWay = $repayPayWay;
    }
    public function getRepayFormType() {
        return $this->repayFormType;
    }

    public function setRepayFormType($repayFormType) {
        $this->repayFormType = $repayFormType;
    }
    public function getSource() {
        return $this->source;
    }

    public function setSource($source) {
        $this->source = $source;
    }
    public function getAmount() {
        return $this->amount;
    }

    public function setAmount($amount) {
        $this->amount = $amount;
    }
    public function getExt() {
        return $this->ext;
    }

    public function setExt($ext) {
        $this->ext = $ext;
    }
    public function getOrderCodeList() {
        return $this->orderCodeList;
    }

    public function setOrderCodeList($orderCodeList) {
        $this->orderCodeList = $orderCodeList;
    }

    public function jsonSerialize()
    {
        $data = [];
        foreach ($this as $key => $val) {
            if ($val !== null) {
                $data[$key] = $val;
            }
        }
        return $data;
    }
}