<?php
/**
 * Created by 2021/7/15.
 * User: Joneq
 * Info: 2021/7/15
 * Time: 下午1:36
 */

namespace Supplychain\Model\Logic\JdpayRequest;

class QueryBillInfo implements \JsonSerializable
{
    /**
     * type: String
     */
    private $reqNo;
    /**
     * type: String
     */
    private $billNo;
    /**
     * type: String
     */
    private $productCode;

    public function getReqNo() {
        return $this->reqNo;
    }

    public function setReqNo($reqNo) {
        $this->reqNo = $reqNo;
    }
    public function getBillNo() {
        return $this->billNo;
    }

    public function setBillNo($billNo) {
        $this->billNo = $billNo;
    }
    public function getProductCode() {
        return $this->productCode;
    }

    public function setProductCode($productCode) {
        $this->productCode = $productCode;
    }

    public function jsonSerialize()
    {
        $data = [];
        foreach ($this as $key => $val) {
            if ($val !== null) {
                $data[$key] = $val;
            }
        }
        return $data;
    }
}