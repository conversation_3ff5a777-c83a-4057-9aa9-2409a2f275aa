<?php
namespace Supplychain\Model\Logic\JdpayRequest;

class AccessCustInfoRequest implements \JsonSerializable
{
    /**
    * type: String
    */
    private $productCode;
    /**
    * type: String
    */
    private $bizNo;
    /**
    * type: String
    */
    private $reqDate;
    /**
    * type: String
    */
    private $custCode;
    /**
    * type: String
    */
    private $custName;
    /**
    * type: String
    */
    private $custType;
    /**
    * type: String
    */
    private $custCertType;
    /**
    * type: String
    */
    private $custCertNo;
    /**
    * type: String
    */
    private $legalName;
    /**
    * type: String
    */
    private $legalCertType;
    /**
    * type: String
    */
    private $legalCertNo;
    /**
    * type: String
    */
    private $legalPhone;
    /**
    * type: String
    */
    private $settleUserCode;
    /**
    * type: String
    */
    private $settleUserName;
    /**
    * type: String
    */
    private $settleUserType;
    /**
    * type: String
    */
    private $settleCertType;
    /**
    * type: String
    */
    private $settleCertNo;
    /**
    * type: String
    */
    private $settleLegalName;
    /**
    * type: String
    */
    private $settleLegalCertType;
    /**
    * type: String
    */
    private $settleLegalCertNo;
    /**
    * type: String
    */
    private $settleLegalPhone;
    /**
    * type: Map
    */
    private $riskControlParams;
    /**
    * type: Map
    */
    private $attachment;
    /**
    * type: String
    */
    private $notifyUrl;
    /**
    * type: Map
    */
    private $ext;
    /**
    * type: BigDecimal
    */
    private $preCreditLimit;
    /**
    * type: Map
    */
    private $riskModelData;

    public function getProductCode() {
        return $this->productCode;
    }

    public function setProductCode($productCode) {
        $this->productCode = $productCode;
    }
    public function getBizNo() {
        return $this->bizNo;
    }

    public function setBizNo($bizNo) {
        $this->bizNo = $bizNo;
    }
    public function getReqDate() {
        return $this->reqDate;
    }

    public function setReqDate($reqDate) {
        $this->reqDate = $reqDate;
    }
    public function getCustCode() {
        return $this->custCode;
    }

    public function setCustCode($custCode) {
        $this->custCode = $custCode;
    }
    public function getCustName() {
        return $this->custName;
    }

    public function setCustName($custName) {
        $this->custName = $custName;
    }
    public function getCustType() {
        return $this->custType;
    }

    public function setCustType($custType) {
        $this->custType = $custType;
    }
    public function getCustCertType() {
        return $this->custCertType;
    }

    public function setCustCertType($custCertType) {
        $this->custCertType = $custCertType;
    }
    public function getCustCertNo() {
        return $this->custCertNo;
    }

    public function setCustCertNo($custCertNo) {
        $this->custCertNo = $custCertNo;
    }
    public function getLegalName() {
        return $this->legalName;
    }

    public function setLegalName($legalName) {
        $this->legalName = $legalName;
    }
    public function getLegalCertType() {
        return $this->legalCertType;
    }

    public function setLegalCertType($legalCertType) {
        $this->legalCertType = $legalCertType;
    }
    public function getLegalCertNo() {
        return $this->legalCertNo;
    }

    public function setLegalCertNo($legalCertNo) {
        $this->legalCertNo = $legalCertNo;
    }
    public function getLegalPhone() {
        return $this->legalPhone;
    }

    public function setLegalPhone($legalPhone) {
        $this->legalPhone = $legalPhone;
    }
    public function getSettleUserCode() {
        return $this->settleUserCode;
    }

    public function setSettleUserCode($settleUserCode) {
        $this->settleUserCode = $settleUserCode;
    }
    public function getSettleUserName() {
        return $this->settleUserName;
    }

    public function setSettleUserName($settleUserName) {
        $this->settleUserName = $settleUserName;
    }
    public function getSettleUserType() {
        return $this->settleUserType;
    }

    public function setSettleUserType($settleUserType) {
        $this->settleUserType = $settleUserType;
    }
    public function getSettleCertType() {
        return $this->settleCertType;
    }

    public function setSettleCertType($settleCertType) {
        $this->settleCertType = $settleCertType;
    }
    public function getSettleCertNo() {
        return $this->settleCertNo;
    }

    public function setSettleCertNo($settleCertNo) {
        $this->settleCertNo = $settleCertNo;
    }
    public function getSettleLegalName() {
        return $this->settleLegalName;
    }

    public function setSettleLegalName($settleLegalName) {
        $this->settleLegalName = $settleLegalName;
    }
    public function getSettleLegalCertType() {
        return $this->settleLegalCertType;
    }

    public function setSettleLegalCertType($settleLegalCertType) {
        $this->settleLegalCertType = $settleLegalCertType;
    }
    public function getSettleLegalCertNo() {
        return $this->settleLegalCertNo;
    }

    public function setSettleLegalCertNo($settleLegalCertNo) {
        $this->settleLegalCertNo = $settleLegalCertNo;
    }
    public function getSettleLegalPhone() {
        return $this->settleLegalPhone;
    }

    public function setSettleLegalPhone($settleLegalPhone) {
        $this->settleLegalPhone = $settleLegalPhone;
    }
    public function getRiskControlParams() {
        return $this->riskControlParams;
    }

    public function setRiskControlParams($riskControlParams) {
        $this->riskControlParams = $riskControlParams;
    }
    public function getAttachment() {
        return $this->attachment;
    }

    public function setAttachment($attachment) {
        $this->attachment = $attachment;
    }
    public function getNotifyUrl() {
        return $this->notifyUrl;
    }

    public function setNotifyUrl($notifyUrl) {
        $this->notifyUrl = $notifyUrl;
    }
    public function getExt() {
        return $this->ext;
    }

    public function setExt($ext) {
        $this->ext = $ext;
    }
    public function getPreCreditLimit() {
        return $this->preCreditLimit;
    }

    public function setPreCreditLimit($preCreditLimit) {
        $this->preCreditLimit = $preCreditLimit;
    }
    public function getRiskModelData() {
        return $this->riskModelData;
    }

    public function setRiskModelData($riskModelData) {
        $this->riskModelData = $riskModelData;
    }

    public function jsonSerialize()
    {
        $data = [];
        foreach ($this as $key => $val) {
            if ($val !== null) {
                $data[$key] = $val;
            }
        }
        return $data;
    }
}

