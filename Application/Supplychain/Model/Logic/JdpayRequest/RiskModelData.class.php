<?php
/**
 * Created by 2021/7/13.
 * User: Joneq
 * Info: 2021/7/13
 * Time: 下午4:17
 */

namespace Supplychain\Model\Logic\JdpayRequest;



class RiskModelData implements \JsonSerializable
{

    private $status;
    private $createdDate;
    private $preCreditLimit;

    private $coopYearTypeTactics;

    private $payAmount;
    private $orderCount;

    private $purchaseAmounts;
    private $orderCounts;

    private $invoiceAmount;
    private $modCustomerNo;
    private $modCompanyName;
    private $entType;
    private $corpId;


    public function getOrderCounts() {
        return $this->orderCounts;
    }

    public function setOrderCounts($orderCounts) {
        $this->orderCounts = $orderCounts;
    }

    public function getPurchaseAmounts() {
        return $this->purchaseAmounts;
    }

    public function setPurchaseAmounts($purchaseAmounts) {
        $this->purchaseAmounts = $purchaseAmounts;
    }

    public function getCreatedDate() {
        return $this->createdDate;
    }

    public function setCreatedDate($createdDate) {
        $this->createdDate = $createdDate;
    }



    public function getStatus() {
        return $this->status;
    }

    public function setStatus($status) {
        $this->status = $status;
    }


    public function getModCustomerNo() {
        return $this->modCustomerNo;
    }

    public function setModCustomerNo($modCustomerNo) {
        $this->modCustomerNo = $modCustomerNo;
    }


    public function getModCompanyName() {
        return $this->modCompanyName;
    }

    public function setModCompanyName($modCompanyName) {
        $this->modCompanyName = $modCompanyName;
    }

    public function getEntType() {
        return $this->entType;
    }

    public function setEntType($entType) {
        $this->entType = $entType;
    }

    public function getCorpId() {
        return $this->corpId;
    }

    public function setCorpId($corpId) {
        $this->corpId = $corpId;
    }


    public function getCoopYearTypeTactics() {
        return $this->coopYearTypeTactics;
    }

    public function setCoopYearTypeTactics($coopYearTypeTactics) {
        $this->coopYearTypeTactics = $coopYearTypeTactics;
    }

    public function getPayAmount() {
        return $this->payAmount;
    }

    public function setPayAmount($payAmount) {
        $this->payAmount = $payAmount;
    }

    public function getOrderCount() {
        return $this->orderCount;
    }

    public function setOrderCount($orderCount) {
        $this->orderCount = $orderCount;
    }


    public function getInvoiceAmount() {
        return $this->invoiceAmount;
    }

    public function setInvoiceAmount($invoiceAmount) {
        $this->invoiceAmount = $invoiceAmount;
    }


    public function getPreCreditLimit() {
        return $this->preCreditLimit;
    }

    public function setPreCreditLimit($preCreditLimit) {
        $this->preCreditLimit = $preCreditLimit;
    }



    public function jsonSerialize()
    {
        $data = [];
        foreach ($this as $key => $val) {
            if ($val !== null) {
                $data[$key] = $val;
            }
        }
        return $data;
    }
}