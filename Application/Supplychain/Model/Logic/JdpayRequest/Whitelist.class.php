<?php
/**
 * Created by 2021/7/13.
 * User: Joneq
 * Info: 2021/7/13
 * Time: 下午4:01
 */

namespace Supplychain\Model\Logic\JdpayRequest;


class Whitelist implements \JsonSerializable
{
    /**
     * type: String
     */
    private $productCode;
    /**
     * type: String
     */
    private $custCode;
    /**
     * type: String
     */
    private $custName;
    /**
     * type: String
     */
    private $custType;
    /**
     * type: String
     */
    private $settleUserCode;
    /**
     * type: String
     */
    private $settleUserName;
    /**
     * type: String
     */
    private $settleUserType;
    /**
     * type: String
     */
    private $creditUserType;
    /**
     * type: BigDecimal
     */
    private $preCreditLimit;
    /**
     * type: String
     */
    private $creditLimitType;
    /**
     * type: String
     */
    private $status;
    /**
     * type: BigDecimal
     */
    private $settleUserScore;
    /**
     * type: String
     */
    private $settleUserLevel;
    /**
     * type: String
     */
    private $createdDate;
    /**
     * type: String
     */
    private $entTypeTactics;
    /**
     * type: String
     */
    private $coopYearTypeTactics;
    /**
     * type: BigDecimal
     */
    private $purchaseAmount;
    /**
     * type: Integer
     */
    private $orderMonthCount;
    /**
     * type: String
     */
    private $stabilizeFactor;
    /**
     * type: String
     */
    private $monthPurchaseSort;
    /**
     * type: String
     */
    private $summResult;
    /**
     * type: Map
     */
    private $ext;
    /**
     * type: Map
     */
    private $riskModelData;
    /**
     * type: List
     */
    private $payAmount;
    /**
     * type: List
     */
    private $orderCount;
    /**
     * type: List
     */
    private $refundAmount;
    /**
     * type: List
     */
    private $refundCount;
    /**
     * type: Integer
     */
    private $cooperation;

    public function getProductCode() {
        return $this->productCode;
    }

    public function setProductCode($productCode) {
        $this->productCode = $productCode;
    }
    public function getCustCode() {
        return $this->custCode;
    }

    public function setCustCode($custCode) {
        $this->custCode = $custCode;
    }
    public function getCustName() {
        return $this->custName;
    }

    public function setCustName($custName) {
        $this->custName = $custName;
    }
    public function getCustType() {
        return $this->custType;
    }

    public function setCustType($custType) {
        $this->custType = $custType;
    }
    public function getSettleUserCode() {
        return $this->settleUserCode;
    }

    public function setSettleUserCode($settleUserCode) {
        $this->settleUserCode = $settleUserCode;
    }
    public function getSettleUserName() {
        return $this->settleUserName;
    }

    public function setSettleUserName($settleUserName) {
        $this->settleUserName = $settleUserName;
    }
    public function getSettleUserType() {
        return $this->settleUserType;
    }

    public function setSettleUserType($settleUserType) {
        $this->settleUserType = $settleUserType;
    }
    public function getCreditUserType() {
        return $this->creditUserType;
    }

    public function setCreditUserType($creditUserType) {
        $this->creditUserType = $creditUserType;
    }
    public function getPreCreditLimit() {
        return $this->preCreditLimit;
    }

    public function setPreCreditLimit($preCreditLimit) {
        $this->preCreditLimit = $preCreditLimit;
    }
    public function getCreditLimitType() {
        return $this->creditLimitType;
    }

    public function setCreditLimitType($creditLimitType) {
        $this->creditLimitType = $creditLimitType;
    }
    public function getStatus() {
        return $this->status;
    }

    public function setStatus($status) {
        $this->status = $status;
    }
    public function getSettleUserScore() {
        return $this->settleUserScore;
    }

    public function setSettleUserScore($settleUserScore) {
        $this->settleUserScore = $settleUserScore;
    }
    public function getSettleUserLevel() {
        return $this->settleUserLevel;
    }

    public function setSettleUserLevel($settleUserLevel) {
        $this->settleUserLevel = $settleUserLevel;
    }
    public function getCreatedDate() {
        return $this->createdDate;
    }

    public function setCreatedDate($createdDate) {
        $this->createdDate = $createdDate;
    }
    public function getEntTypeTactics() {
        return $this->entTypeTactics;
    }

    public function setEntTypeTactics($entTypeTactics) {
        $this->entTypeTactics = $entTypeTactics;
    }
    public function getCoopYearTypeTactics() {
        return $this->coopYearTypeTactics;
    }

    public function setCoopYearTypeTactics($coopYearTypeTactics) {
        $this->coopYearTypeTactics = $coopYearTypeTactics;
    }
    public function getPurchaseAmount() {
        return $this->purchaseAmount;
    }

    public function setPurchaseAmount($purchaseAmount) {
        $this->purchaseAmount = $purchaseAmount;
    }
    public function getOrderMonthCount() {
        return $this->orderMonthCount;
    }

    public function setOrderMonthCount($orderMonthCount) {
        $this->orderMonthCount = $orderMonthCount;
    }
    public function getStabilizeFactor() {
        return $this->stabilizeFactor;
    }

    public function setStabilizeFactor($stabilizeFactor) {
        $this->stabilizeFactor = $stabilizeFactor;
    }
    public function getMonthPurchaseSort() {
        return $this->monthPurchaseSort;
    }

    public function setMonthPurchaseSort($monthPurchaseSort) {
        $this->monthPurchaseSort = $monthPurchaseSort;
    }
    public function getSummResult() {
        return $this->summResult;
    }

    public function setSummResult($summResult) {
        $this->summResult = $summResult;
    }
    public function getExt() {
        return $this->ext;
    }

    public function setExt($ext) {
        $this->ext = $ext;
    }
    public function getRiskModelData() {
        return $this->riskModelData;
    }

    public function setRiskModelData($riskModelData) {
        $this->riskModelData = $riskModelData;
    }
    public function getPayAmount() {
        return $this->payAmount;
    }

    public function setPayAmount($payAmount) {
        $this->payAmount = $payAmount;
    }
    public function getOrderCount() {
        return $this->orderCount;
    }

    public function setOrderCount($orderCount) {
        $this->orderCount = $orderCount;
    }
    public function getRefundAmount() {
        return $this->refundAmount;
    }

    public function setRefundAmount($refundAmount) {
        $this->refundAmount = $refundAmount;
    }
    public function getRefundCount() {
        return $this->refundCount;
    }

    public function setRefundCount($refundCount) {
        $this->refundCount = $refundCount;
    }
    public function getCooperation() {
        return $this->cooperation;
    }

    public function setCooperation($cooperation) {
        $this->cooperation = $cooperation;
    }

    public function jsonSerialize()
    {
        $data = [];
        foreach ($this as $key => $val) {
            if ($val !== null) {
                $data[$key] = $val;
            }
        }
        return $data;
    }
}