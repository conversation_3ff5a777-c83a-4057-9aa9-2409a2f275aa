<?php
/**
 * Created by 2022/6/9.
 * User: Jone
 * Info: 2022/6/9
 * Time: 下午4:13
 */

namespace Supplychain\Model\Logic;


use Supplychain\Model\WorldAddress;
use Supplychain\Repository\OrderRepository;

class HongKongOrderLogic
{

    static public $isHongKong = 0;

    /*
     * add_basic_order_info
     * add_hongkong_delivery
     * add_inland_delivery
     * add_other_order_info
     * goto_adding_order
     */
    static public function createOrder($data)
    {

        //正常的订单流程
        OrderLogic::createOrder($data);

        return 'ok';
    }


    static public function changeOrder($data)
    {
        //正常的改单流程
        OrderLogic::changeOrder($data);
        return 'ok';
    }


    //获取国际地址
    static public function getInternationalAddress($data)
    {
        if (!isset($data['id']) || empty($data['id'])){
            $data['id'] = 0;
        }
        return WorldAddress::where('parent_id',$data['id'])->orderBy('id','asc')->pluck('name','id');
    }
}