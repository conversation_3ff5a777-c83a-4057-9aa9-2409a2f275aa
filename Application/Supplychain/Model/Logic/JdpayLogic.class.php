<?php
/**
 * Created by 2021/7/9.
 * User: Joneq
 * Info: 2021/7/9
 * Time: 下午2:08
 */
namespace Supplychain\Model\Logic;


use Supplychain\Model\Logic\JdpayRequest\AccessCustInfoRequest;
use Supplychain\Model\Logic\JdpayRequest\ApplyRepay;
use Supplychain\Model\Logic\JdpayRequest\Order;
use Supplychain\Model\Logic\JdpayRequest\OrderStatus;
use Supplychain\Model\Logic\JdpayRequest\PayOrder;
use Supplychain\Model\Logic\JdpayRequest\PreCreditWhiteRequest;
use Supplychain\Model\Logic\JdpayRequest\QueryBillInfo;
use Supplychain\Model\Logic\JdpayRequest\QueryCustInfo;
use Supplychain\Model\Logic\JdpayRequest\RefundApply;
use Supplychain\Model\Logic\JdpayRequest\RiskModelData;
use Supplychain\Model\Logic\JdpayRequest\Whitelist;

class JdpayLogic
{
    /*
     *
     * 文档地址
     * https://open.jddglobal.com/docs/detail?prodcode=355&ic=3_288
     *
     *
     * 结算主体和客户有什么区别和联系吗？
     *
     * 1.我们有的合作方对客户维护，比如一个客户下面有多个采购主体，这样结算主体就是多个
     *
     *
     *
     *
     */


    public function execute($method,$request)
    {
        import('Jdpay.aop.client.DefaultJddClient');

        $client = new \DefaultJddClient();
        $client->url = C('JDPAY.server');
        $client->openPublicKey = C('JDPAY.public_key');
        $client->openMD5 = C('JDPAY.md5_salt');
        $client->partnerId = C('JDPAY.appid');
        $client->privateKey = C('JDPAY.private_key');
        $client->method = $method;
        return $client->execute($request);
    }

    //京东魔盒请求数据 第一次先请求这个数据，把所有计算好的用户发送过去
    /*
        params={
          "company_name" : "福泉市一条龙物流有限公司",
          "company_id" : "113",
          "company_code" : "91520181MA6DYT1W2K",
          "first_order_create_time" : "2021-07-24",
          "index_bymonth" : [
            {
              "invoice_price" : 345,
              "order_status" : "已完成",
              "order_goods" : "芯片",
              "order_goods_price" : 345,
              "order_sn" : 11,
              "invoice_time" : "2021-04",
              "ID" : 1001002,
              "order_time" : "2021-07"
            },
            {
              "invoice_price" : 1678,
              "order_status" : "已完成",
              "order_goods" : "芯片",
              "order_goods_price" : 1678,
              "order_sn" : 12,
              "invoice_time" : "2021-05",
              "ID" : 1001003,
              "order_time" : "2021-08"
            }
          ],
          "company_address" : "贵州省贵阳市清镇市马上到公路港信息长廊D-113",
          "last_order_create_time" : "2021-07-24",
          "company_type" : "企业"
        }
     */
    public function jdMoheRequest($data)
    {
        import('Jdpay.aop.client.DefaultJddClient');

        $jsonData['company_name']  = $data['company_name'];
        $jsonData['company_id']  = $data['id'];
        $jsonData['company_code']  = $data['erp_company_code'];
        $jsonData['first_order_create_time']  = "2021-07-24";
        $jsonData['company_address']  = $data['company_address'];
        $jsonData['last_order_create_time']  = "2021-07-24";
        $jsonData['company_type']  = "企业";

        $indexBymonthDetail['invoice_price'] = 345;
        $indexBymonthDetail['order_status'] = '已完成';
        $indexBymonthDetail['order_goods'] = '芯片';
        $indexBymonthDetail['order_goods_price'] = 345;
        $indexBymonthDetail['order_sn'] = '11';
        $indexBymonthDetail['invoice_time'] = '2021-04';
        $indexBymonthDetail['ID'] = 1001002;
        $indexBymonthDetail['order_time'] = '2021-07';

        $jsonData['index_bymonth'][] = $indexBymonthDetail;
        $jsonData['index_bymonth'][] = $indexBymonthDetail;

        $request = \GuzzleHttp\json_encode($jsonData);

        $client = new \GuzzleHttp\Client();
        $response = $client->post(C('JDPAY.mohe_url').'/liexin/lx/operation', [
            'form_params' => [  //参数组
                'params' => $request,
            ],
        ]);

        if ($response->getStatusCode() !== 200){
            throw new \Exception('参数请求code出错:'.$response->getStatusCode().':'.$request);
        }

        return $response->getBody()->getContents();
    }



    /*
     * '/smapi/v1/co.yfk/preCreditWhitelist'  预授信白名单
     * 京东侧在合作方部署风控模盒用于分析合作方客户信息得出预授信白名单 合作方定期通过此接口向京东侧推送预授信白名单列表
            $request->setProductCode('');//string 否 产品编号
            $request->setCustCode('');//string 否 经销商编号
            $request->setCustName('');//string 否 经销商名称
            $request->setCustType('');//string 否 经销商类型
            $request->setSettleUserCode('');//string 否 结算主体编号
            $request->setSettleUserName('');//string 否 结算主体名称
            $request->setSettleUserType('');//string 否 结算主体类型
            $request->setCreditUserType('');//string 否 授信主体类型
            $request->setPreCreditLimit('');//amount 否 预授信额度
            $request->setCreditLimitType('');//string 否 预授信类型
            $request->setStatus('');//string 否 白名单状态
            $request->setSettleUserScore('');//string 否 结算主体评分
            $request->setSettleUserLevel('');//string 否 结算主体评级
            $request->setCreatedDate('');//string 否 创建时间yyyyMMddHHmmss
            $request->setEntTypeTactics('');//string 否 企业类型策略
            $request->setCoopYearTypeTactics('');//string 否 合作年限策略
            $request->setPurchaseAmount('');//string 否 最近13个月采购额
            $request->setOrderMonthCount('');//integer 否 最近13个月有订单的月份个数
            $request->setStabilizeFactor('');//string 否 业务稳定系数
            $request->setMonthPurchaseSort('');//string 否 最近12个月月均采购排名
            $request->setSummResult('');//string 否 策略汇总结果
            $request->setExt('');//json 否 扩展字段
            $request->setPayAmount('');//array 否 最近24个月采购额
            $request->setOrderCount('');//array 否 最近24个月订单数
            $request->setRefundAmount('');//array 否 最近24个月退款金额
            $request->setRefundCount('');//array 否 最近24个月退款订单数
            $request->setRiskModelData('');//json 否 风控模盒数据
            $request->setCooperation('');//integer 否 合用月数
     */
    public function preCreditWhitelist($data=[])
    {
        $request =  new PreCreditWhiteRequest();
        $request->setProductCode(self::getProductCode());
        $request->setReqDate(self::getReqDate());
        $request->setReqNo(self::getReqNo());
        $request->setBatchNo(self::getBatchNo());
        $request->setBatchSendFinish(true);

        $whiteList = new Whitelist();
        $whiteList->setProductCode(self::getProductCode());
        $whiteList->setCustCode($data['setCustCode']);
        $whiteList->setCustName($data['setCustName']);
        $whiteList->setCustType('COMPANY');
        $whiteList->setCreditLimitType('MAX');
        $whiteList->setCreditUserType('CUSTUSER');
        $whiteList->setSettleUserCode($data['setSettleUserCode']);
        $whiteList->setSettleUserName($data['setSettleUserName']);
        $whiteList->setSettleUserType('COMPANY');
        $whiteList->setOrderCount([10,10,10,10,10,10,10,10,10,10,10,10]); // TODO
        $whiteList->setPayAmount([100,100,100,100,100,100,100,100,100,100,100,100]); // TODO
        $whiteList->setRefundAmount([0,0,0,0,0,0,0,0,0,0,0,0]);
        $whiteList->setRefundCount([0,0,0,0,0,0,0,0,0,0,0,0]);
        $whiteList->setCooperation(12); // TODO
        $whiteList->setStatus('ACCESS');

        $riskModelData = new RiskModelData();
        $riskModelData->setCreatedDate(self::getCreatedDate());
        $riskModelData->setStatus('ACCESS');
        $riskModelData->setPreCreditLimit(1000);//额度
        $riskModelData->setCoopYearTypeTactics(12);//合作时长 TODO
        //$riskModelData->setPayAmount('100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100');//最近N个月采购额 TODO
        //$riskModelData->setOrderCount('10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10');//最近N个月订单数 TODO
        $riskModelData->setPurchaseAmounts('100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100');//最近N个月采购额
        $riskModelData->setOrderCounts('10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10');//最近N个月订单数
        $riskModelData->setInvoiceAmount('100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100');//最近N个月开票金额 TODO
        $riskModelData->setModCustomerNo($data['setModCustomerNo']);
        $riskModelData->setModCompanyName($data['setModCompanyName']);
        $riskModelData->setEntType('COMPANY');
        $riskModelData->setCorpId($data['setCorpId']);


        $whiteList->setRiskModelData($riskModelData);

        $request->setWhiteList([$whiteList]);
        return ['request'=>$request,'method'=>'/smapi/v1/co.yfk/preCreditWhitelist'];

        //string(114) "{"reqNo":"f44cf48de4fc42b691cb63104fd848","resCode":"0000000","resDate":"20210714111644","data":{"failedList":[]}}"
    }



    /*
     * /smapi/v1/co.yfk/accessCustInfo 客户信息推送
     * 合作方客户发起合作产品开通请求前，合作方通过此接口向京东侧推送客户相关信息，信息推送成功后跳转至产品页面。
        $request->setSettleLegalName('');//
        $request->setSettleLegalCertType('');//
        $request->setSettleLegalCertNo('');//
        $request->setSettleLegalPhone('');//
        $request->setRiskControlParams('');//风控需要的参数
        $request->setAttachment('');//附件
        $request->setExt('');//
        $request->setPreCreditLimit('');//
        $request->setRiskModelData('');//当前用户对应的最新风控模盒数据
        $request->setLegalName('');//法人名字
        $request->setLegalCertType('');//法人证件类型
        $request->setLegalCertNo('');//法人证件号
        $request->setLegalPhone('');//手机号
     *
     * */
    static public function accessCustInfo($data=[])
    {

        $request = new AccessCustInfoRequest();

        $request->setProductCode(self::getProductCode());//产品编码
        $request->setBizNo(self::getBizno());//请求号
        $request->setReqDate(self::getReqDate());//请求时间yyyyMMddHHmmss
        $request->setCustCode($data['setCustCode']);//客户编号
        $request->setCustName($data['setCustName']);//客户名称
        $request->setCustType('COMPANY');//客户类型
        $request->setCustCertType('USCC');//客户证件类型
        $request->setCustCertNo($data['setCustCertNo']);//客户证件号
        $request->setNotifyUrl(self::getAccessCustInfoNotifyUrl());//审核通知地址

        $request->setSettleUserCode($data['setSettleUserCode']);//结算主体编号
        $request->setSettleUserName($data['setSettleUserName']);//结算主体名称
        $request->setSettleUserType('COMPANY');//结算主体类型
        $request->setSettleCertType('USCC');//结算主体证件类型
        $request->setSettleCertNo($data['setSettleCertNo']);//结算主体证件号
        $request->setRiskControlParams(self::getAccessCustInfoRiskControlParams($data));

        //设置风控信息
        $riskModelData = new RiskModelData();
        $riskModelData->setCreatedDate(self::getCreatedDate());
        $riskModelData->setStatus('ACCESS');
        $riskModelData->setPreCreditLimit(1000);//额度
        $riskModelData->setCoopYearTypeTactics(12);//合作时长 TODO
        //$riskModelData->setPayAmount('100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100');//最近N个月采购额 TODO
        //$riskModelData->setOrderCount('10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10');//最近N个月订单数 TODO

        $riskModelData->setPurchaseAmounts('100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100');//最近N个月采购额
        $riskModelData->setOrderCounts('10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10|10');//最近N个月订单数

        $riskModelData->setInvoiceAmount('100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100|100');//最近N个月开票金额 TODO
        $riskModelData->setModCustomerNo($data['modCustomerNo']);
        $riskModelData->setModCompanyName($data['modCompanyName']);
        $riskModelData->setEntType('COMPANY');
        $riskModelData->setCorpId($data['corpId']);

        $request->setRiskModelData($riskModelData);

        return ['request'=>$request,'method'=>'/smapi/v1/co.yfk/accessCustInfo'];
    }


    /*
     * /smapi/v1/co.yfk/queryCustInfo
     * 此接口提供查询客户是否为白名单用户，客户是否已通过授信以及客户可用授信额度。
     *
     * */
    static public function queryCustInfo($data=[])
    {
        $request = new QueryCustInfo();
        $request->setReqNo(self::getReqNo());
        $request->setReqDate(self::getReqDate());
        $request->setProductCode(self::getProductCode());
        $request->setCustCode($data['setCustCode']);
        $request->setSettleUserCode($data['setSettleUserCode']);

        return ['request'=>$request,'method'=>'/smapi/v1/co.yfk/queryCustInfo'];
    }


    /*
     * /smapi/v1/co.yfk/payOrder
     *  合作方客户选择使用京东侧产品进行支付时，合作方先将当前待支付的订单信息通过此接口推送至京东侧，推送成功后跳转至产品支付页面完成订单支付。
    $request->setBizNo('');//string 否 业务编号
    $request->setReqDate('');//string 否 请求时间yyyyMMddHHmmss
    $request->setProductCode('');//string 是 产品编码
    $request->setCustCode('');//string 否 经销商编码,渠道商唯一指定认可标识-结算主体ID
    $request->setOrderCode('');//string 否 订单编号
    $request->setOrderAmount('');//amount 否 订单金额，单位：元
    $request->setApplyAmount('');//amount 否 申请订单支付金额。即借款申请金额，单位：元
    $request->setOrderIndate('');//string 否 订单有效期，yyyyMMddHHmmss
    $request->setOrderDesc('');//string 否 订单描述
    $request->setProductCategorys('');//array 否 订单中商品类列表，List
    $request->setOrderStatus('');//string 否 订单状态
    $request->setPayStatus('');//string 否 支付状态
    $request->setCreateDate('');//string 否 订单创建时间：yyyyMMddHHmmss
    $request->setReceiveCardNo('');//string 否 指定收款的账户号
    $request->setReceiveName('');//string 否 指定收款的账户名称
    $request->setRiskControlParams('');//json 否 风控要求字段
    $request->setAttachment('');//json 否 附件信息
    $request->setNotifyUrl('');//string 否 通知地址
    $request->setExt('');//json 否 扩展字段
    $request->setSettleUserCode('');//string 否 结算主体编号
    $request->setTermCount('');//integer 否 分期数
    $request->setRepayNotifyUrl('');//string 否 还款通知URL
     * */
    //{\"reqNo\":\"bizno48de4f162926520916\",\"resCode\":\"0000000\",\"resDate\":\"20210818134010\",\"payUrl\":\"https:\/\/loan.jd.com\/cgrz\/order?productCode=25041&orderNo=O2021081801002005\",\"resDesc\":\"成功\"}
    //payUrl 就是跳转过来支付融资的页面地址
    static public function payOrder($data=[])
    {
        $request = new PayOrder();

        $orderCode = self::getOrderCode();
        $request->setBizNo(self::getBizno());//业务编号
        $request->setReqDate(self::getReqDate());//请求时间yyyyMMddHHmmss
        $request->setProductCode(self::getProductCode());//产品编码
        $request->setCustCode($data['setCustCode']);//经销商编码,渠道商唯一指定认可标识-结算主体ID
        $request->setOrderCode($orderCode);//订单编号
        $request->setOrderAmount(200);//订单金额，单位：元
        $request->setApplyAmount(25);//申请订单支付金额。即借款申请金额，单位：元
        $request->setOrderIndate(self::getOrderIndate());//订单有效期，yyyyMMddHHmmss
        $request->setOrderDesc('猎芯订单推送');//订单描述
        $request->setSettleUserCode($data['setCustCode']);//结算主体编号
        $request->setProductCategorys(['水果']);
        $request->setOrderStatus('CREATED');
        $request->setPayStatus('UNPAY');
        $request->setCreateDate(self::getCreatedDate());
        $request->setNotifyUrl(self::getPayOrderNotifyUrl());
        $request->setReceiveName('深圳市亿诚电子科技有限公司');
        $request->setReceiveCardNo('202108251643');
        $request->setRiskControlParams(self::getPayOrderRiskControlParams());
        return ['request'=>$request,'method'=>'/smapi/v1/co.yfk/payOrder'];
    }


    /*
     * /smapi/v1/co.yfk/orderStatus
     *
        $request->setReqNo('');//string 否 请求号，外部请求生成，必须唯一
        $request->setReqDate('');//string 否 请求时间
        $request->setProductCode('');//string 否 产品编号

        $request->setOrderCode('');//string 否 订单编号
        $request->setOrderDesc('');//string 否 订单描述
        $request->setOrderStatus('');//string 否 订单状态
        $request->setPayStatus('');//string 否 订单支付状态
     *
     * */
    static public function orderStatus($data=[])
    {
        $request = new OrderStatus();
        $orderRequest = new Order();

        $request->setReqNo(self::getReqNo());//string 否 请求号，外部请求生成，必须唯一
        $request->setReqDate(date('Y-m-d'));//string 否 请求时间 '20210703'
        $request->setProductCode(self::getProductCode());//string 否 产品编号

        $orderRequest->setOrderCode('ORDER5f5197cb6e9c4d44a73593255d50ea04');//string 否 订单编号 //ORDER5f5197cb6e9c4d44a73593255d50ea04
        $orderRequest->setOrderStatus('CREATED');//string 否 订单状态
        $orderRequest->setPayStatus('UNPAY');//string 否 订单支付状态

        $request->setOrders([$orderRequest]);

        return ['request'=>$request,'method'=>'/smapi/v1/co.yfk/orderStatus'];
    }







    /*
     * /smapi/v1/co.yfk/queryBillDetail
     *查询账单详情
     *
     * */
    static public function queryBillDetail($data=[])
    {

        $request = new QueryBillInfo();

        $request->setReqNo(self::getReqNo());//string 是 请求号，外部请求生成，必须唯一
        $request->setBillNo('232');//string 是 账单编号
        $request->setProductCode(self::getProductCode());//string 是

        return ['request'=>$request,'method'=>'/smapi/v1/co.yfk/queryBillDetail'];
    }


    /*
     * /smapi/v1/co.yfk/applyRepay
     * 合作方通过API方式发起订单还款操作
    $request->setBizNo('');//string 是 业务编码
    $request->setReqDate('');//string 是 请求时间
    $request->setProductCode('');//string 是 产品编号
    $request->setCustCode('');//string 是 客户编码
    $request->setOrderCode('');//string 否 订单编号
    $request->setRepayPayWay('');//string 是 还款支付方式
    $request->setRepayFormType('');//string 是 还款方式
    $request->setSource('');//string 是 来源
    $request->setAmount('');//amount 是 还款金额
    $request->setExt('');//json 否 扩展字段
    $request->setOrderCodeList('');//array 否 订单编号列表
     * */
    static public function applyRepay($data=[])
    {
        $request = new ApplyRepay();
        $request->setBizNo(self::getBizno());//string 是 业务编码
        $request->setReqDate(self::getReqDate());//string 是 请求时间
        $request->setProductCode(self::getProductCode());//string 是 产品编号
        $request->setCustCode('Lx001');//string 是 客户编码
        $request->setOrderCode('ORDER5f5197cb6e9c4d44a73593255d50ea04');//string 否 订单编号
        $request->setRepayPayWay('TODO');//string 是 还款支付方式
        $request->setRepayFormType('TODO');//string 是 还款方式
        $request->setSource('TODO');//string 是 来源
        $request->setAmount('1');//amount 是 还款金额
        $request->setOrderCodeList([self::getOrderCode()]);//array 否 订单编号列表
        return ['request'=>$request,'method'=>'/smapi/v1/co.yfk/applyRepay'];
    }


    /*
     * /smapi/v1/co.yfk/refundApply
     * 退款申请
$request->setBizNo('');//string 是 业务编号
$request->setReqDate('');//string 是 请求时间
$request->setProductCode('');//string 是 产品编号
$request->setSettleUserCode('');//string 是 结算主体编号
$request->setOrderCode('');//string 是 订单编号
$request->setRefundDesc('');//string 是 退款原因
$request->setRefundAmount('');//string 是 退款金额
$request->setNotifyUrl('');//string 是 退款结果通知地址
$request->setExt('');//object 是 扩展字段
$request->setCustCode('');//string 是 客户编号
$request->setRiskControlParams('');//object 是 风控参数
     * */
    static public function refundApply($data=[])
    {
        $request = new RefundApply();
        $request->setBizNo(self::getBizno());//string 是 业务编号
        $request->setReqDate(self::getReqDate());//string 是 请求时间
        $request->setProductCode(self::getProductCode());//string 是 产品编号
        $request->setSettleUserCode('12221');//string 是 结算主体编号
        $request->setOrderCode('ORDER5f5197cb6e9c4d44a73593255d50ea04');//string 是 订单编号
        $request->setRefundDesc('测试退款');//string 是 退款原因
        $request->setRefundAmount(25);//string 是 退款金额
        $request->setNotifyUrl(self::getNotifyUrl());//string 是 退款结果通知地址
        $request->setCustCode('lx210628');//string 是 客户编号
        return ['request'=>$request,'method'=>'/smapi/v1/co.yfk/refundApply'];
    }








    //------------------------------------------以下是通知我们---------------------------//
    /*
     * /smapi/v1/co.yfk/accessResultNofity
     *客户准入通知
     * */
    static public function accessResultNofity($data=[])
    {
        return [];
    }


    //账单分期通知
    static public function billPeriodNotify($data=[])
    {
        return [];
    }


    //退款结果通知
    static public function refundNotify($data=[])
    {
        return [];
    }


    //账单生成，逾期通知
    static public function billNotify($data=[])
    {
        return [];
    }


    //支付结果通知
    static public function payResult($data=[])
    {
        return [];
    }

    //订单，账单还款结果通知
    static public function repayNotify($data=[])
    {
        return [];
    }



    //----------------------------------------联合登陆---------------------------//

    //京东登陆获取参数
    static public function jdLogin()
    {
        $param = [
            "processId" => "262",//配置 id，申请的
            "openSys" => "A20200702110",//平台 id，申请的
            "openId" => "lx210628",//平台登录账号 id，人的维度，自己的
            "openUser" => "lx210628",//平台用户 id，企业维度，自己的
            "successUrl" => "www.ichunt.com",//成功后回调地址
            "clientIp" => $_SERVER['REMOTE_ADDR'],//客户端 ip
            "clientAgent" => $_SERVER['HTTP_USER_AGENT'],//浏览器代理
            "failUrl" => "www.ichunt.com",//失败后返回地址
            "openApiParam" => "{'backUrl':'www.ichunt.com'}",//其他参数 json 串
        ];

        import('Jdpay.aop.client.DefaultHapiJddClient');
        $client = new \DefaultHapiJddClient();
        $client->appIdType = cookie('JDPAY.appId_type');
        $client->openPublicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHmBFArlz4wQ0Qz3iFmPG2YHWYJt8dbgJybd6rMykcB/D+HBDZ+S+EpB0zOTst/Y4k8niZe1PvqzjZ2+Npq2f9jTCUJQ4nnTXk6lXd5utFNvA+y6uTfQ1YdbiZAD+OkvYdgL5Wl1LQ6MuWCF4ZI/fm8zDL9zVtCvfYQHm3MnZJNwIDAQAB';
        $client->md5Salt = '74bf4f4d92ea566b0820fffe69a04d20';
        $client->appId = cookie('JDPAY.appid');
        $client->appPrivateKey = 'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL6eI/e8amibnrMH0ap3/kdifYjVDZxpheEs0Y8czDavbavW1VmDD17MgLcJKs8Sqp3H3hbOndJOKTsHzCMy16wTD23G+2F0uYPKbKpBR4A8IlX76gu7M4fZWY7ojAElXChnIjCHZ6rAgTxtYauC/ddp0lgqKJx6MT7XnGL/Ja9DAgMBAAECgYEAtHAUEg1MGOu+xTAQm7JshHxJ4r3y3W3SKn33fOZAtT9IrJJ3cP0sDou9CAZofI3p7eNlaud447vTMarG5UbaeciPFjWSyaH+b3C/DDoOoxvbOd0IJs35oYwFxQ6sFZcG72zQj7wsyM3D8II5djfkKsBIjRwjkosqkpkv2ZRYGpkCQQDr3/aLOLWXFDqvIFFRupYpumQR+wmuMYxvR2Obgks/yPXJJjqSGjCRb/XI50T14uJEOUGhYcI57zdoUFbp+H6VAkEAzuGpBZuDc3obg/qiGGM15lMbYXFyB2TQvi+Hg/uNGuzcsdl+TzXPvewQxxeHwIqZa2wk2LSikoENDVBeYdl4dwJARaHJM5JbMS18oYRl3T265LisoA4+7licP6GQizDsq/jUbjxF4CmxGs41fcigOAJxj3hjopOsddPjxHyrG8kK/QJATH/xaltpLkhW6GTDj9UP102f8FZs3gMPlWQp7koUkYJI0ZMlO9EgMpCaW6R91FrsBGcG8QNKonYKB4RSgkhSXQJAcdYWicTcJFCYOFxrRz4Ag7L6bqT57hiDf9cLLox/NGQ/ZIlOt7CTmv1/sy4yVfF1pKh/9GJM3fb0GGrAAOE8Wg==';
        $client->signType = cookie('JDPAY.sign_type');
        $client->encryptType = cookie('JDPAY.encrypt_type');
        $systemInfo = [
            'sysId' => '123',
            'token' => '456'
        ];
        return $client->getData($systemInfo, $param);

    }

    static public function getAccessCustInfoRiskControlParams($riskdata=[])
    {
        $data = [
            'modCustomerNo'=>$riskdata['modCustomerNo'],//客户编号（渠道商编码）
            'modCompanyName'=>$riskdata['modCompanyName'],//企业名称（渠道商名称）
            'legalCardId'=>'',//法人身份证号码
            'legalPhone'=>'',//法人手机号码
            'corpId'=>$riskdata['corpId'],//统一社会信用代码
            'propertyNature'=>'',//房产性质
            'address'=>'',//地理位置
            'conName'=>'',//实际控制人姓名
            'conIdCard'=>'',//实际控制人身份证号码
            'conTel'=>'',//实际控制人手机号码
        ];
        return $data;
    }



    static public function getPayOrderRiskControlParams($data=[])
    {
        $returnData = [
            'orderCode'=>'liexintestorder'.date("YmdHis"),//订单ID唯一主键
            'orderStatus'=>'待付款',//订单状态合作方推送中文含义：待付款
            'chanel'=>'线上',//订单渠道传输中文解释
            'orderType'=>'01',//订单类型21-退换货单 （终端B2B订单）101-终端B2B实体商品订单 111-终端B2B发货单
            'orderCreateDate'=>date("YmdHis"),//订单时间下单时间
            'orderAmount'=>100,//订单金额订单实际费用（销售订单记录实际支付金额，退换货单记录相应销售订单支付金额）
            'productName'=>'电子芯片',//商品名称
            'productCount'=>1,//商品数量
            'productPrice'=>100,//商品原始价格
            'productOrderPrice'=>100,//商品订单价格
            'discountsAmount'=>100,//商品优惠金额
            'collecterName'=>'猎先生',//收货人姓名
            'collecterAddress'=>'广东省深圳市龙华区宝能科技园12栋11层',//收货人地址
            'collecterProvince'=>'广东省',//收货省份名称
            'collecterCity'=>'深圳市',//收货城市名称
            'collecterCounty'=>'龙华区',//收货区县名称
            'collecterMobile'=>'17600091664',//收货手机号
        ];
        return $returnData;
    }





    static public function getBizno()
    {
        return 'bizno48de4f'.time().rand(1,100);
    }


    static public function getBatchNo()
    {
        return date("Ymd");
    }

    static public function getReqDate()
    {
        return date("YmdHis");
    }

    static public function getCreatedDate()
    {
        return date("YmdHis");
    }

    static public function getOrderIndate()
    {
        return date("YmdHis",time()+3600);
    }


    static public function getReqNo()
    {
        return 'reqNo48de4f'.time().rand(1,100);
    }

    static public function getProductCode()
    {
        return C('JDPAY.product_code');
    }

    static public function getAccessCustInfoNotifyUrl()
    {
        return C('JDPAY.notify_main').'/index.php?m=supplychain&c=jr&a=accessResultNofity';
    }

    static public function getPayOrderNotifyUrl()
    {
        return C('JDPAY.notify_main').'/index.php?m=supplychain&c=jr&a=payResult';
    }

    static public function getNotifyUrl()
    {
        return C('JDPAY.notify_main').'/index.php?m=supplychain&c=jr&a=accessResultNofity';
    }

    static public function getOrderCode()
    {
        return 'order48de4f'.time().rand(1,100);
    }



    //返回的json
    static public function rJson($data,$code=0,$msg='操作成功')
    {
        echo \GuzzleHttp\json_encode(['code'=>$code,'data'=>$data,'msg'=>$msg]);die;
        //成功是array
        if (is_array($data)){
            echo \GuzzleHttp\json_encode(['code'=>$code,'data'=>$data,'msg'=>$msg]);die;
        }
        //否则是失败
        echo \GuzzleHttp\json_encode(['code'=>empty($code)?1:$code,'data'=>$data,'msg'=>$msg]);die;
    }




    /*
     *
     * //京东支付 京东的配置
    'JDPAY'=>[

        'appid'=>'A20200415182',


        'private_key'=>'MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIvmJlMT5ZObgG6QJlxqrUhn2mZAr9m/aCOWVq0brPLmfAD0Ayew4GBNg/nC40ltJEt5QFxBQmLbIaeal1OXsih/wakD/2jv4LdNjqHQb6+ejnpu9aOXpMi+LBJTOfcUH4IOL3avDPJSjlosn7/6wecS3hfoK4C4yBgcax8TZWedAgMBAAECgYBbQse9aj1ZDRe7CE39+5kOorDWA1yzrylADdJ9GpC346MA/C0St0+ngumVBI1AWPJUScSyad/knQ5XR4FwBoUw7qYtQl4TC1FlsvZLzKv/eNQS5O6KU8HhBXRtFeWJB44ySBFmP2iU4tNpNO6kald0WkzqegqSgZ+mhz3IHK5kAQJBAMrVDJXtOPKL64iEikOqPVs2HCFCOvWS5SvAz7MMZUqsE/6LpxVTo65TeoC7m0c380jywQfpJGp01KqIfL3xTg8CQQCwkgA2DRPEAt+r9ivX7yVlVf+vzM1o5R4pqvUmlNt7IdaM72nxZUHtL7AZvIS+S00q3qn++8U9KaJA/0jhxhuTAkBZ+DLC9hzCiYoKXjAuX38jzFah8gzRDT2WMMpwc1kizD16NVwBu73o/6JhI9Z2uryxaOxyo9nBVt1WX4BliHnFAkA6lebuCD7DrbRrST3Y4ueEjRlOSmTpZgQxDSFUUNXWMC8RCisbxl4uhcEneO6OeUu8aq77BKy4E4VbjZiwGNxzAkAuLrWbjOfA6WwuQKEvfGsxkPVrfO3v2xVokKKr7GNCj0ZuQrsXBVQ2HNDFUjl40+gUH3D0Ha4+f8UgJdU67ATL',


        'public_key'=>'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCBLEDGwVOrb4PZbhoiiPHThRMBSjXQgLnneWXpOG3elQafzTsMIT1LzUuBAfpcULCM4O3ohk1zh8FmQyjGZeFlIBi53BBNGEbznq+MiZBRbYlmg4As5ANoSYl9CXRJbgh93LgzAMZ1dJ0WBaHAVly3Llu1CD58Dwkv+AYCDx+IswIDAQAB',


        'private_pkas8_key'=>'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAK2K7rh3Kd4twI9m
Go//uB/BS4ZrnhX/fUjUXJKAGzldlJp2wsb0i76wFADmeLILWztxgOyuF9lnWlli
pLSfhgaXyYHGIcAxmSYJ2FFaA7QyNo2UiZcLXJuknS/w7EbfMjvYQchToMI0CGoY
Avtjo6yAfaVMnxgRMpNVxSrT2WbJAgMBAAECgYABmXIxyQddwNYxfCNqiFTOFh34
Ey3VzhD9hteDkiKuGKqx8b5BzycqjrzgdT9PhMNGfPlPNRuLKWSvxjT2ZTH7AbBO
wA40iGqur0gquRA9/l3tWOHMsNxOlHoKXqMpFqHWoRvyzuhQsXJNnrFzPB4kctUq
JEcaBU1YjfruO+3hwQJBANcY218KbBzVEEj1sOqyhMe6LPzm/jNsDqF2FT8Dadfr
qBlFRFANF9JXFq9S81FyH/hiLF9Ma5IezMKyodFsfH0CQQDOiyp8udWDTBhPyFi1
RAWfE2LK25KhQ9xcvnsLHYkyHVqMum51iamiirpnYuxH3IpsTDmWVRciGcUqLZH4
NEE9AkB5WRehxbJ6jaVTU/DMZ4xLVXg98V9lyUdzxbd0xks3Okaqgw5oDwrqaqFs
peKJh1YL/e7EkMt7Mw8XpElwacDNAkASE8wcLoepfjeChB5/fvye96tl5eHni3D2
DRXn2yXu5PLP7mFMmEfomgvGkLntcDgNUn6X0cq8iVTgZCyqdKhhAkEAgoGO/7Yy
pMcwF2L1kwHyv38pmSedJY4X8SQK1gXxUU09tqMpQc1F5czXFIzKFViTXWAuwkI+
HCbku6pyhoiCtw==',

        'md5_salt'=>'Bfc9CFWaHV4UXEl0L1CVx1Wf',

        'encrypt_type'=>'ENV_RSA',

        'sign_type'=>'MD5_RSA',

        'appId_type'=>'0',

        ///************:9655
        /// ************:8112
        'server'=>'http://*************:1058',

        'product_code'=>'25041',

        'notify_main'=>'https://jrapi.ichunt.com'
    ]

    自己的配置

        'appid'=>'bda8601809db409c82f1beae9cc256a7',


    'private_key'=>'MIICXAIBAAKBgQCtiu64dyneLcCPZhqP/7gfwUuGa54V/31I1FySgBs5XZSadsLG
9Iu+sBQA5niyC1s7cYDsrhfZZ1pZYqS0n4YGl8mBxiHAMZkmCdhRWgO0MjaNlImX
C1ybpJ0v8OxG3zI72EHIU6DCNAhqGAL7Y6OsgH2lTJ8YETKTVcUq09lmyQIDAQAB
AoGAAZlyMckHXcDWMXwjaohUzhYd+BMt1c4Q/YbXg5IirhiqsfG+Qc8nKo684HU/
T4TDRnz5TzUbiylkr8Y09mUx+wGwTsAONIhqrq9IKrkQPf5d7VjhzLDcTpR6Cl6j
KRah1qEb8s7oULFyTZ6xczweJHLVKiRHGgVNWI367jvt4cECQQDXGNtfCmwc1RBI
9bDqsoTHuiz85v4zbA6hdhU/A2nX66gZRURQDRfSVxavUvNRch/4YixfTGuSHszC
sqHRbHx9AkEAzosqfLnVg0wYT8hYtUQFnxNiytuSoUPcXL57Cx2JMh1ajLpudYmp
ooq6Z2LsR9yKbEw5llUXIhnFKi2R+DRBPQJAeVkXocWyeo2lU1PwzGeMS1V4PfFf
ZclHc8W3dMZLNzpGqoMOaA8K6mqhbKXiiYdWC/3uxJDLezMPF6RJcGnAzQJAEhPM
HC6HqX43goQef378nverZeXh54tw9g0V59sl7uTyz+5hTJhH6JoLxpC57XA4DVJ+
l9HKvIlU4GQsqnSoYQJBAIKBjv+2MqTHMBdi9ZMB8r9/KZknnSWOF/EkCtYF8VFN
PbajKUHNReXM1xSMyhVYk11gLsJCPhwm5LuqcoaIgrc=',


    'public_key'=>'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtiu64dyneLcCPZhqP/7gfwUuG
a54V/31I1FySgBs5XZSadsLG9Iu+sBQA5niyC1s7cYDsrhfZZ1pZYqS0n4YGl8mB
xiHAMZkmCdhRWgO0MjaNlImXC1ybpJ0v8OxG3zI72EHIU6DCNAhqGAL7Y6OsgH2l
TJ8YETKTVcUq09lmyQIDAQAB',


    'private_pkas8_key'=>'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAK2K7rh3Kd4twI9m
Go//uB/BS4ZrnhX/fUjUXJKAGzldlJp2wsb0i76wFADmeLILWztxgOyuF9lnWlli
pLSfhgaXyYHGIcAxmSYJ2FFaA7QyNo2UiZcLXJuknS/w7EbfMjvYQchToMI0CGoY
Avtjo6yAfaVMnxgRMpNVxSrT2WbJAgMBAAECgYABmXIxyQddwNYxfCNqiFTOFh34
Ey3VzhD9hteDkiKuGKqx8b5BzycqjrzgdT9PhMNGfPlPNRuLKWSvxjT2ZTH7AbBO
wA40iGqur0gquRA9/l3tWOHMsNxOlHoKXqMpFqHWoRvyzuhQsXJNnrFzPB4kctUq
JEcaBU1YjfruO+3hwQJBANcY218KbBzVEEj1sOqyhMe6LPzm/jNsDqF2FT8Dadfr
qBlFRFANF9JXFq9S81FyH/hiLF9Ma5IezMKyodFsfH0CQQDOiyp8udWDTBhPyFi1
RAWfE2LK25KhQ9xcvnsLHYkyHVqMum51iamiirpnYuxH3IpsTDmWVRciGcUqLZH4
NEE9AkB5WRehxbJ6jaVTU/DMZ4xLVXg98V9lyUdzxbd0xks3Okaqgw5oDwrqaqFs
peKJh1YL/e7EkMt7Mw8XpElwacDNAkASE8wcLoepfjeChB5/fvye96tl5eHni3D2
DRXn2yXu5PLP7mFMmEfomgvGkLntcDgNUn6X0cq8iVTgZCyqdKhhAkEAgoGO/7Yy
pMcwF2L1kwHyv38pmSedJY4X8SQK1gXxUU09tqMpQc1F5czXFIzKFViTXWAuwkI+
HCbku6pyhoiCtw==',
     */


}