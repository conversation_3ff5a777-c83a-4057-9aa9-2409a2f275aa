<?php
/**
 * Created by 2021/11/23.
 * User: Joneq
 * Info: 2021/11/23
 * Time: 下午2:22
 */

namespace Supplychain\Model\Logic;


class BaiduAiPicLogic
{
    //百度图片识别接口

    //获取token的url
    const TOKENURL = 'https://aip.baidubce.com/oauth/2.0/token';
    //获取图片通用文字识别数据的接口
    const OCRURL = 'https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic?access_token=';
    const CLIENTID = '7EknY7cBhd2oUFuiQZA5GqsI';
    const CLIENTSECRET = 'PmAHd4znaeymjyHteLSL9H0jSiRjByq5';
    const BAIDUTOKENREDISKEY = 'baidu_ai_pic_redis_key';


    //获取图片识别的数据
    static public function getOcrData($imageUrl)
    {
        $url = self::OCRURL. self::getToken();
        $bodys = array(
            'image' => $imageUrl
        );
        $res = self::requestPost($url, $bodys);
        $returnData = \GuzzleHttp\json_decode($res,true);
        if (empty($returnData) || !isset($returnData['words_result'])){

            if (intval($returnData['error_code']) == 216202) {
                $res = '上传的图片大小错误，现阶段我们支持的图片大小为：base64编码后小于4M，分辨率不高于4096*4096，请重新上传图片';
            }else if (intval($returnData['error_code']) == 216201){
                $res = '上传的图片格式错误，现阶段我们支持的图片格式为：PNG、JPG、JPEG、BMP，请进行转码或更换图片';
            }

            throw new \Exception('百度识别错误:'.$res);
        }
        return $returnData['words_result'];
    }


    static public function getToken()
    {

        $Redis = new \Redis();
        $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $Redis->auth(C('REDIS_PASSWORD'));
        if (!empty($token = $Redis->get(self::BAIDUTOKENREDISKEY))){
            return $token;
        }

        $post_data['grant_type']    = 'client_credentials';
        $post_data['client_id']     = self::CLIENTID;
        $post_data['client_secret'] = self::CLIENTSECRET;
        $o = "";
        foreach ( $post_data as $k => $v ) {
            $o.= "$k=" . urlencode( $v ). "&" ;
        }
        $post_data = substr($o,0,-1);
        $res = self::requestPost(self::TOKENURL, $post_data);

        //返回数据格式
        //{
        //    "refresh_token": "25.7c6c7a5536f52e2038bd7cd06fa3f67d.315360000.**********.282335-24516086",
        //    "expires_in": 2592000,
        //    "session_key": "9mzdXqFdTZ6T9dSBGm5ukEo4EZ9KtMYvSqz2EFEjgSWr2fv3wsUkw9AkrZcrqxr4Q+/6ixldh4VAltyhfLiZIFE0vAk37A==",
        //    "access_token": "24.ed6c538f900fb506e49699310af05f57.2592000.**********.282335-24516086",
        //    "scope": "public vis-ocr_ocr brain_ocr_scope brain_ocr_general brain_ocr_general_basic vis-ocr_business_license brain_ocr_webimage brain_all_scope brain_ocr_idcard brain_ocr_driving_license brain_ocr_vehicle_license vis-ocr_plate_number brain_solution brain_ocr_plate_number brain_ocr_accurate brain_ocr_accurate_basic brain_ocr_receipt brain_ocr_business_license brain_solution_iocr brain_qrcode brain_ocr_handwriting brain_ocr_passport brain_ocr_vat_invoice brain_numbers brain_ocr_business_card brain_ocr_train_ticket brain_ocr_taxi_receipt vis-ocr_household_register vis-ocr_vis-classify_birth_certificate vis-ocr_台湾通行证 vis-ocr_港澳通行证 vis-ocr_机动车购车发票识别 vis-ocr_机动车检验合格证识别 vis-ocr_车辆vin码识别 vis-ocr_定额发票识别 vis-ocr_保单识别 vis-ocr_机打发票识别 vis-ocr_行程单识别 brain_ocr_vin brain_ocr_quota_invoice brain_ocr_birth_certificate brain_ocr_household_register brain_ocr_HK_Macau_pass brain_ocr_taiwan_pass brain_ocr_vehicle_invoice brain_ocr_vehicle_certificate brain_ocr_air_ticket brain_ocr_invoice brain_ocr_insurance_doc brain_formula brain_ocr_facade brain_ocr_meter brain_doc_analysis brain_ocr_webimage_loc brain_ocr_doc_analysis_office brain_vat_invoice_verification wise_adapt lebo_resource_base lightservice_public hetu_basic lightcms_map_poi kaidian_kaidian ApsMisTest_Test权限 vis-classify_flower lpq_开放 cop_helloScope ApsMis_fangdi_permission smartapp_snsapi_base smartapp_mapp_dev_manage iop_autocar oauth_tp_app smartapp_smart_game_openapi oauth_sessionkey smartapp_swanid_verify smartapp_opensource_openapi smartapp_opensource_recapi fake_face_detect_开放Scope vis-ocr_虚拟人物助理 idl-video_虚拟人物助理 smartapp_component smartapp_search_plugin avatar_video_test b2b_tp_openapi b2b_tp_openapi_online",
        //    "session_secret": "cdcd1f5602b3d1c22bdfcf63b58e2b50"
        //}

        $returnData = \GuzzleHttp\json_decode($res,true);
        if (empty($returnData) || !isset($returnData['access_token'])){
            throw new \Exception('获取百度的access_token失败:'.$res);
        }

        $Redis->set(self::BAIDUTOKENREDISKEY,$returnData['access_token']);
        $Redis->expire(self::BAIDUTOKENREDISKEY,intval($returnData['expires_in']));
    }


    static public function requestPost($url = '', $param = '') {
        if (empty($url) || empty($param)) {
            return false;
        }

        $postUrl = $url;
        $curlPost = $param;
        $curl = curl_init();//初始化curl
        curl_setopt($curl, CURLOPT_URL,$postUrl);//抓取指定网页
        curl_setopt($curl, CURLOPT_HEADER, 0);//设置header
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);//要求结果为字符串且输出到屏幕上
        curl_setopt($curl, CURLOPT_POST, 1);//post提交方式
        curl_setopt($curl, CURLOPT_POSTFIELDS, $curlPost);
        $data = curl_exec($curl);//运行curl
        curl_close($curl);

        return $data;
    }


}