<?php
/**
 * Created by 2021/9/6.
 * User: Joneq
 * Info: 2021/9/6
 * Time: 下午2:23
 */

namespace Supplychain\Model\Logic;


use Home\Model\UserMainModel;
use Supplychain\Model\ActionLogModel;
use Supplychain\Model\AskCustomerExceptionModel;
use Supplychain\Model\AskCustomerExceptionOptionModel;
use Supplychain\Model\AskCustomerModel;
use Supplychain\Model\AskCustomerOptionModel;
use Supplychain\Model\AskCustomerProblemGoodsModel;
use Supplychain\Model\AskCustomerProblemListModel;
use Supplychain\Model\CmsModel;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\LiexinModel;
use Supplychain\Model\ServiceAgreementModel;
use Supplychain\Model\UserMessageModel;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\WechatFormidModel;


class AskCustomerWechatLogic
{

    static public $userId;
    static public $defaultTel = '0755-83237919';
    static public $appid = 'wx47674efb6eef06e1';
    static public $secret = 'da0b422b75907ec8d4e6cac4593e8095';


    static public function getUserInfo($data)
    {
        $userId = $data['user_id'];

        $returnData = [];

        if (empty($returnData['mobile'] = (new LiexinModel())->setTable('user_main')->where('user_id',$userId)->value('mobile'))){
            throw new \Exception('未绑定手机');
        }

        if (empty($email = UserMessageModel::where('user_id',$userId)->where('type',2)->value('send_user'))){
            $returnData['email'] = '';
        }else{
            $returnData['email'] = $email;
        }

        $customerInfo = CustomerModel::where('user_id',$userId)->select('customer_name','company_id','head_pic','wechat_unique_id','erp_client_id')->first()->toArray();
        $companyInfo = CompanyModel::where('company_id',$customerInfo['company_id'])->first()->toArray();
        $returnData['is_bind'] = $customerInfo['wechat_unique_id']?'已绑定':'未绑定';
        $returnData['head_pic'] = $customerInfo['head_pic'];
        $returnData['company_name'] = $companyInfo['company_full_name'];

        //最后一次登陆时间
        if (empty($lastLoginTime = (new LiexinModel())->setTable('user_login_log')->where('user_id',$userId)->orderBy('id','desc')->value('last_login_time'))){
            $returnData['last_login_time'] = '';
        }else{
            $returnData['last_login_time'] = date('Y-m-d H:i:s',$lastLoginTime);
        }

        //签约状态
        $returnData['is_sign'] = '未签约';
        if ($customerInfo['erp_client_id'] && ServiceAgreementModel::where('erp_client_id',$customerInfo['erp_client_id'])->where('status',1)->value('id')){
            $returnData['is_sign'] = '已签约';
        }
        $returnData['user_follow_people'] = CustomerLogic::getUserFollowPeopleInfo();
        return $returnData;
    }


    //绑定微信
    static public function bindWechat($data)
    {
        if (!empty(CustomerModel::where('user_id',self::$userId)->value('wechat_bind_tel'))){
            return [];
        }

        if(!$data['iv'] || !$data['session_key'] || !$data['encryptedData'] || !self::$userId) throw new \Exception('参数不完整');

        $cryptObj = new WxBizDataCrypt(self::$appid,$data['session_key']);
        $errCode = $cryptObj->decryptData($data['encryptedData'],$data['iv'],$data);

        var_dump($data);die;
        if ($errCode == 0) {

            if (!isset($data['phoneNumber'])){
                throw new \Exception('没有获取到手机号');
            }

            if (!preg_match_all("/^1[3456789]\d{9}$/", $data['phoneNumber'])) {
                throw new \Exception('手机格式不正确');
            }
            //自动登录流程
            CustomerModel::where('user_id',self::$userId)->update(['wechat_unique_id'=>$data['openid']]);

        } else {
            throw new \Exception('获取微信数据失败:'.$errCode);
        }
        return [];
    }






    //获取问题列表
    static public function getProblemList($data)
    {
        $data['ask_customer_status'] = 1;
        $data['store_execute_status'] = 1;
        $returnData = AskCustomerProblemListModel::getList($data);
        return $returnData;
    }


    //获取问题详情
    static public function askCustomerProblemDetail($data)
    {
        if (empty($askCustomerProblemInfo = AskCustomerProblemListModel::where('ask_customer_problem_list_id',$data['ask_customer_problem_list_id'])->where('user_id',self::$userId)->first())){
            throw new \Exception('暂无该问题信息');
        }

        $askCustomerProblemInfo = $askCustomerProblemInfo->toArray();
        $returnData = $askCustomerProblemInfo;
        if (intval($returnData['handle_type']) === 2){
            $returnData['ask_customer_confirm_feedback'] = '人工处理';
        }
        $returnData['ask_customer_exception_id_cn'] =  AskCustomerExceptionModel::where('ask_customer_exception_id',$returnData['ask_customer_exception_id'])->value('content');
        $returnData['create_time_cn'] =  empty($returnData['create_time'])?'':date('m-d H:i',$returnData['create_time']);
        $returnData['ask_customer_confirm_time_cn'] =  empty($returnData['ask_customer_confirm_time'])?'':date('m-d H:i',$returnData['ask_customer_confirm_time']);
        $returnData['ask_customer_status_cn'] =  array_get(AskCustomerProblemListModel::$askCustomerStatus,$returnData['ask_customer_status']);

        if (intval($returnData['store_execute_status']) === 2){
            $returnData['store_execute_status_cn'] =  array_get(AskCustomerProblemListModel::$storeExecuteStatus,$returnData['ask_customer_status']);
        }else{
            $returnData['store_execute_status_cn'] = '';
        }

        $returnData['upload_img'] = AskCustomerModel::where('ask_customer_id',$returnData['ask_customer_id'])->value('upload_img').','.$returnData['upload_img'];

        $returnData['order_goods'] = AskCustomerProblemGoodsModel::where('ask_customer_problem_list_id',$returnData['ask_customer_problem_list_id'])->get()->toArray();
        $returnData['action_log'] = ActionLogModel::where('user_id',$returnData['ask_customer_problem_list_id'])->where('type',6)->get()->toArray();
        foreach ($returnData['action_log'] as $key=>$val){
            $returnData['action_log'][$key]['create_time'] = date('Y-m-d H:i:s',$val['create_time']);
        }
        $returnData['mobile'] = self::getShangwuTel();

        return $returnData;
    }


    static public function getShangwuTel()
    {
        if (empty($salesName = CustomerModel::where('user_id',self::$userId)->value('follow_people'))){
            return self::$defaultTel;
        }
        $return = (new CmsModel())->getData('user_info',['name'=>$salesName],'find','engName,name,mobile,tel,qq');
        if (empty($return['mobile'])){
            return self::$defaultTel;
        }
        return $return['mobile'];
    }

    //客户确认问题
    static public function customerConfirmProblem($data)
    {

        $data['order_goods'] = json_decode($data['order_goods'],true);

        if (!isset($data['ask_customer_problem_list_id']) || empty($data['ask_customer_problem_list_id'])){
            throw new \Exception('ask_customer_problem_list_id is null');
        }
        if (!isset($data['order_goods']) || empty($data['order_goods'])){
            throw new \Exception('order_goods is null');
        }

        if (!isset($data['ask_customer_confirm_feedback']) || empty($data['ask_customer_confirm_feedback'])){
            throw new \Exception('ask_customer_confirm_feedback is null');
        }


        if (!AskCustomerProblemListModel::where('ask_customer_problem_list_id',$data['ask_customer_problem_list_id'])->where('ask_customer_status',1)->where('user_id',self::$userId)->value('ask_customer_problem_list_id')){
            throw new \Exception('该问题暂不可操作');
        }

        if (intval(AskCustomerProblemListModel::where('ask_customer_problem_list_id',$data['ask_customer_problem_list_id'])->value('handle_type')) === 2){
            throw new \Exception('该问题已转至人工，客户不可确认');
        }

        try{
            DB::connection('SUPPLYCHAIN')->beginTransaction();

            foreach ($data['order_goods'] as $value){

                if (empty($value['customer_result']) || empty($value['ask_customer_problem_goods_id'])){
                    throw new \Exception('必填参数不可为空');
                }

                $orderGoodsValue = AskCustomerProblemGoodsModel::where('ask_customer_problem_goods_id',$value['ask_customer_problem_goods_id'])->first();
                if ($value['customer_result'] == '订单数据'){
                    $value['customer_result'] = $orderGoodsValue->order_result;
                }
                if ($value['customer_result'] == '来货数据'){
                    $value['customer_result'] = $orderGoodsValue->store_result;
                }


                //设置客户结果
                AskCustomerProblemGoodsModel::where('ask_customer_problem_list_id',$data['ask_customer_problem_list_id'])
                    ->where('ask_customer_problem_goods_id',$value['ask_customer_problem_goods_id'])
                    ->update(['customer_result'=>$value['customer_result']]);
            }

            $updateData = [
                'ask_customer_status'=>2,
                'ask_customer_confirm_time'=>time(),
                'ask_customer_confirm_remark'=>$data['ask_customer_confirm_remark'],
                'ask_customer_confirm_feedback'=>$data['ask_customer_confirm_feedback'],
            ];

            $exceptionId = AskCustomerProblemListModel::where('ask_customer_problem_list_id',$data['ask_customer_problem_list_id'])->value('ask_customer_exception_id');
            $swConfirmException = AskCustomerExceptionModel::whereIn('data_field',['numbers','goods_type'])->where('status',1)->pluck('ask_customer_exception_id')->toArray();
            //如果不是数量和型号，就直接已确认
            if (!in_array($exceptionId,$swConfirmException)){
                $updateData['ask_customer_sw_status'] = 2;
                $updateData['ask_customer_sw_confirm_time'] = time();
                $updateData['ask_customer_sw_confirm_remark'] = '系统自动确认';
            }

            AskCustomerProblemListModel::where('ask_customer_problem_list_id',$data['ask_customer_problem_list_id'])->update($updateData);

            self::writeLog('客户确认问题',$data['ask_customer_problem_list_id']);

            DB::connection('SUPPLYCHAIN')->commit();
        }catch (\Exception $exception){
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($exception->getMessage());
        }

        return [];
    }

    static public function turnManualProcessing($data)
    {
        if (!AskCustomerProblemListModel::where('ask_customer_problem_list_id',$data['ask_customer_problem_list_id'])->where('ask_customer_status',1)->where('user_id',self::$userId)->value('ask_customer_problem_list_id')){
            throw new \Exception('该问题暂不可操作');
        }
        AskCustomerProblemListModel::where('ask_customer_problem_list_id',$data['ask_customer_problem_list_id'])->update([
            'handle_type'=>2,
            'ask_customer_status'=>2,
            'ask_customer_confirm_feedback'=>'转人工',
            'ask_customer_confirm_time'=>time(),
        ]);
        self::writeLog('客户转人工',$data['ask_customer_problem_list_id']);
        return [];
    }



    static public function customerCancelConfirmProblem($data)
    {

        if (!AskCustomerProblemListModel::where('ask_customer_problem_list_id',$data['ask_customer_problem_list_id'])
            ->where('ask_customer_status',2)
            ->where('store_execute_status',1)
            ->where('user_id',self::$userId)
            ->value('ask_customer_problem_list_id')){
            throw new \Exception('该问题暂不可操作');
        }

        AskCustomerProblemListModel::where('ask_customer_problem_list_id',$data['ask_customer_problem_list_id'])->update([
            'ask_customer_status'=>1,
            'ask_customer_confirm_time'=>0,
        ]);
        self::writeLog('客户取消确认',$data['ask_customer_problem_list_id']);
        return [];
    }





    static public function login($data)
    {
        if (empty($userInfo = (new UserMainModel())->where(['mobile'=>$data['account']])->find())){
            if (empty($userInfo = (new UserMainModel())->where(['email'=>$data['account']])->find())){
                throw new \Exception('账号不存在，请重新输入');
            }
        }

        if ($userInfo['password'] != md5(md5($data['pwd']).$userInfo['salt'])){
            throw new \Exception('密码错误，请重新输入');
        }

        self::$userId = $userInfo['user_id'];

        if (empty(CustomerModel::where('user_id',self::$userId)->value('wechat_unique_id'))){
            CustomerModel::where('user_id',self::$userId)->update(['wechat_unique_id'=>$data['unionid']]);
        }

        //增加openid录入
        if (!empty($data['openid']) && empty(WechatFormidModel::where('openid',$data['openid'])->value('id'))){
            WechatFormidModel::insert([
                'formid'=>self::$appid,'get_time'=>time(),
                'openid'=>$data['openid'], 'user_id'=>$userInfo['user_id']
            ]);
        }

        return ['token'=>self::passport_encrypt($userInfo['user_id'],'liexin')];
    }




    static public function passport_encrypt($txt, $key = 'liiu') {
        srand((double)microtime() * 1000000);
        $encrypt_key = md5(rand(0, 32000));
        $ctr = 0;
        $tmp = '';
        for($i = 0;$i < strlen($txt); $i++) {
            $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
            $tmp .= $encrypt_key[$ctr].($txt[$i] ^ $encrypt_key[$ctr++]);
        }
        return urlencode(base64_encode(self::passport_key($tmp, $key)));
    }

    static public function passport_key($txt, $encrypt_key) {
        $encrypt_key = md5($encrypt_key);
        $ctr = 0;
        $tmp = '';
        for($i = 0; $i < strlen($txt); $i++) {
            $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
            $tmp .= $txt[$i] ^ $encrypt_key[$ctr++];
        }
        return $tmp;
    }

    static public function passport_decrypt($txt, $key = 'liiu') {
        $txt = self::passport_key(base64_decode(urldecode($txt)), $key);
        $tmp = '';
        for($i = 0;$i < strlen($txt); $i++) {
            $md5 = $txt[$i];
            $tmp .= $txt[++$i] ^ $md5;
        }
        return $tmp;
    }


    static public function getParam()
    {
        if (!empty($data = file_get_contents("php://input"))){

            $data = \GuzzleHttp\json_decode(file_get_contents("php://input"),true);
        }else{
            $data = $_REQUEST;
        }
        return $data;
    }

    static public function getAskCustomerExcepitonOption($data)
    {
        if (empty($data['ask_customer_exception_id']) || empty($optionIdArr = AskCustomerExceptionOptionModel::where('status',1)->where('ask_customer_exception_id',$data['ask_customer_exception_id'])->pluck('ask_customer_option_id')->toArray())){
            throw new \Exception('问题异常ID不能为空');
        }

        return AskCustomerOptionModel::whereIn('ask_customer_option_id',$optionIdArr)->where('status',1)->pluck('content','data_choose');
    }


    static public function writeLog($content,$id)
    {
        (new ActionLogModel())->addLog($id,$content,CustomerModel::where('user_id',self::$userId)->value('customer_name'),6);
    }

}