<?php
/**
 * Created by 2021/10/28.
 * User: Joneq
 * Info: 2021/10/28
 * Time: 下午4:53
 */

namespace Supplychain\Model\Logic;

use Home\Model\UserMainModel;
use Supplychain\Model\ActionLogModel;
use Supplychain\Model\CmsModel;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\LiexinModel;
use Supplychain\Model\ServiceAgreementModel;
use Supplychain\Model\UserMessageModel;
use Illuminate\Database\Capsule\Manager as DB;

//供应链订单微信小程序逻辑层
class WechatOrderLogic
{


    static public $defaultTel = '0755-********';
    static public $appid = 'wx47674efb6eef06e1';
    static public $secret = 'da0b422b75907ec8d4e6cac4593e8095';





    static public function login($data)
    {
        if (empty($userInfo = (new UserMainModel())->where(['mobile'=>$data['account']])->find())){
            if (empty($userInfo = (new UserMainModel())->where(['email'=>$data['account']])->find())){
                throw new \Exception('账号不存在，请重新输入');
            }
        }

        if ($userInfo['password'] != md5(md5($data['pwd']).$userInfo['salt'])){
            throw new \Exception('密码错误，请重新输入');
        }

        CommonLogic::$loginUid = $userInfo['user_id'];

        if (empty(CustomerModel::where('user_id',CommonLogic::$loginUid )->value('wechat_unique_id'))){
            CustomerModel::where('user_id',CommonLogic::$loginUid )->update(['wechat_unique_id'=>$data['unionid']]);
        }

        return ['token'=>self::passport_encrypt($userInfo['user_id'],'liexin')];
    }





    static public function getParam()
    {
        if (!empty($data = file_get_contents("php://input"))){
            $data = \GuzzleHttp\json_decode(file_get_contents("php://input"),true);
        }else{
            $data = $_REQUEST;
        }
        return $data;
    }



    static public function passport_encrypt($txt, $key = 'liiu') {
        srand((double)microtime() * 1000000);
        $encrypt_key = md5(rand(0, 32000));
        $ctr = 0;
        $tmp = '';
        for($i = 0;$i < strlen($txt); $i++) {
            $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
            $tmp .= $encrypt_key[$ctr].($txt[$i] ^ $encrypt_key[$ctr++]);
        }
        return urlencode(base64_encode(self::passport_key($tmp, $key)));
    }

    static public function passport_key($txt, $encrypt_key) {
        $encrypt_key = md5($encrypt_key);
        $ctr = 0;
        $tmp = '';
        for($i = 0; $i < strlen($txt); $i++) {
            $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
            $tmp .= $txt[$i] ^ $encrypt_key[$ctr++];
        }
        return $tmp;
    }

    static public function passport_decrypt($txt, $key = 'liiu') {
        $txt = self::passport_key(base64_decode(urldecode($txt)), $key);
        $tmp = '';
        for($i = 0;$i < strlen($txt); $i++) {
            $md5 = $txt[$i];
            $tmp .= $txt[++$i] ^ $md5;
        }
        return $tmp;
    }

}