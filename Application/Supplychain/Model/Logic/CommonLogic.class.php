<?php
/**
 * Created by 2021/6/22.
 * User: Joneq
 * Info: 2021/6/22
 * Time: 下午5:28
 */

namespace Supplychain\Model\Logic;


use Supplychain\Controller\ErpPushController;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\CustomerModel;
use Illuminate\Database\Capsule\Manager as Capsule;
use Supplychain\Model\ExchangeRate;
use Supplychain\Model\OrderModel;
use Supplychain\Model\UserMessageModel;
use Supplychain\Service\DingNotify;
use Think\Exception;
use Think\Log;
use GuzzleHttp\Client;

class CommonLogic
{


    static public $loginUid = 0;

    static public $US_TAX_RATE = 1.25;

    static public function getLoginUid()
    {
        if (!empty(cookie('uid'))){
            return cookie('uid');
        }
        return self::$loginUid;
    }


    //写日志
    static public function logsw($info,$fun,$type="WARN")
    {
        \Think\Log::write($fun."：".$info,$type);
    }

    //打印pdf
    static public function printPdf($html)
    {
        vendor("mpdf.mpdf");
        $mpdf = new \mPDF('zh-cn','A2','','',32,25,27,25,16,13);
        $mpdf->WriteHTML(base64_decode($html));
        $file = date('Y-m-d H:i:s').'supply_tray.pdf';
        $mpdf->Output($file,'f');
        $imgUrl = (new CommonLogic())->uploadToOss(realpath($file));
        $data = \GuzzleHttp\json_decode($imgUrl,true);
        if (!isset($data['code']) || $data['code'] != 200){
            throw new \Exception('文件上传失败');
        }
        unlink(realpath($file));

        return $data['data'][0];
    }



    //获取公司名称
    static public function getCompnayName()
    {
        if (empty($companyId = CustomerModel::where('user_id',self::getLoginUid())->value('company_id'))){
            throw new \Exception('没有绑定的公司');
        }
        if (empty($companyName = CompanyModel::where('company_id',$companyId)->value('company_full_name'))){
            throw new \Exception('公司名称不完整');
        }
        return $companyName;
    }


    //获取京东金融信息
    static public function getJdJrInfo()
    {
        $returnData['company_name'] = self::getCompnayName();
        $returnData['company_code'] = CustomerModel::where('user_id',self::getLoginUid())->value('customer_code');
        if ($_SERVER['SERVER_NAME'] == 'api.ichunt.com'){
            //正式
            $returnData['company_name'] = self::getCompnayName();
            $returnData['company_code'] = CustomerModel::where('user_id',self::getLoginUid())->value('customer_code');
        }else{
            //测试
            $returnData['company_name'] = '深圳市亿诚电子科技有限公司';
            $returnData['company_code'] = '0000049';
        }
        return $returnData;
    }


    //检测方法名里面的字段
    static public function checkField($fun,$data)
    {

        switch ($fun){

            //下单改单检测字段
            case 'createOrder':
            case 'changeOrder':
            case 'add_hongkong_delivery':
                if (intval($data['hongkong_delivery_type']) === 2){
                    if (empty($data['estimated_arrival_time'])){
                        throw new \Exception('请选择香港交货预计提货时间');
                    }
                    if (empty($data['hk_com_delivery_id'])){
                        throw new \Exception('请选择香港交货提货地址');
                    }
                }else if (intval($data['hongkong_delivery_type']) === 3){
//                    if (empty($data['express_delivery_country'])){
//                        throw new \Exception('请选择香港交货来货国家/地区');
//                    }
                    if (empty($data['estimated_arrival_time'])){
                        throw new \Exception('请选择香港交货预计来货时间');
                    }
                }
                break;
        }
    }




    //--------订单部分

    //下单类型检验字段适配
    static public function orderDiffGetCreateOrderGoodsFields()
    {
        $default = ['goods_type','goods_title','brand','origin','measurement','unit_price','numbers'];
        if (HongKongOrderLogic::$isHongKong){
            return ['goods_type','goods_title','brand','origin','measurement','unit_price','numbers','hk_unit_price','hk_total_price'];
        }
        return $default;
    }

    //总价适配
    static public function orderDiffAddUserOrderGoods($data)
    {
        $data['total_price'] = floatval($data['unit_price']*$data['numbers']);
        if (HongKongOrderLogic::$isHongKong){
            $data['hk_total_price'] = floatval($data['hk_unit_price']*$data['numbers']);
        }
        return $data;
    }

    //适配公司
    static public function orderDiffCompanyName()
    {
        if (HongKongOrderLogic::$isHongKong){
            return '富开香港有限公司';
        }
        return '深圳市猎芯供应链有限公司';
    }

    //入仓号获取适配
    static public function orderDiffGetErpOrderSn($erp_client_id)
    {
        $data = ["PRINCIPAL"=>$erp_client_id];
        if (HongKongOrderLogic::$isHongKong){
            $data['Type'] = 'A';
        }
        return $data;
    }

    //入仓号正则
    static public function orderDiffErpOrderSnMatch()
    {
        $match = '/^B[0-9]+$/';
        if (HongKongOrderLogic::$isHongKong){
            $match = '/^A[0-9]+$/';
        }
        return $match;
    }



    //-------订单部分




    static public function signDb()
    {


        if ($_SERVER['SERVER_NAME'] == 'jrapi.ichunt.com'){
            $supplychan = [
                'driver'    => 'mysql',
                'host'      => 'web-master.ichunt.db',
                'database'  => 'liexin_sc_sz',
                'username'  => 'Scusz',
                'password'  => 'Tlzs@#$$20aTTfl',
                'charset'   => 'utf8',
                'collation' => 'utf8_general_ci',
                'prefix'    => 'lie_',
                'strict' => false,
                'engine' => null,
                'options' => [
                    // mysql连接3s超时设置
                    \PDO::ATTR_TIMEOUT => 3
                ]
            ];

            //liexin_credit
            $credit =  [
                'driver'    => 'mysql',
                'host'      => 'fkdb-master.ichunt.db',
                'database'  => 'liexin_credit_sz',
                'username'  => 'Creus',
                'password'  => 'Xsl@##@Tll',
                'charset'   => 'utf8',
                'collation' => 'utf8_general_ci',
                'prefix'    => 'lie_',
                'strict' => false,
                'engine' => null,
                'options' => [
                    // mysql连接3s超时设置
                    \PDO::ATTR_TIMEOUT => 3
                ]
            ];

        }else{
            $supplychan = [
                'driver'    => 'mysql',
                'host'      => '*************',
                'database'  => 'liexin_supply_chain',
                'username'  => 'supply_chain',
                'password'  => 'liexin_supply_chain#zsyM',
                'charset'   => 'utf8',
                'collation' => 'utf8_general_ci',
                'prefix'    => 'lie_',
                'strict' => false,
                'engine' => null,
                'options' => [
                    // mysql连接3s超时设置
                    \PDO::ATTR_TIMEOUT => 3
                ]
            ];

            //liexin_credit
            $credit =  [
                'driver'    => 'mysql',
                'host'      => '*************',
                'database'  => 'liexin_credit',
                'username'  => 'liexin_credit',
                'password'  => 'liexin_credit#zsyM',
                'charset'   => 'utf8',
                'collation' => 'utf8_general_ci',
                'prefix'    => 'lie_',
                'strict' => false,
                'engine' => null,
                'options' => [
                    // mysql连接3s超时设置
                    \PDO::ATTR_TIMEOUT => 3
                ]
            ];
        }


        $capsule = new Capsule;
        // 创建链接
        $capsule->addConnection($credit,'credit');
        $capsule->addConnection($supplychan,'supplychan');

        // 设置全局静态可访问DB
        $capsule->setAsGlobal();

        // 启动Eloquent
        $capsule->bootEloquent();
    }

    static public function xmlToArray($data)
    {
        $obj = simplexml_load_string($data, 'SimpleXMLElement', LIBXML_NOCDATA);
        $json = json_encode($obj);
        $arr = json_decode($json, true);
        return $arr;
    }



    public function uploadToOss($Path = '', $data = [])
    {
        if (empty($Path)) {
            return false;
        }
        $field = new \CURLFile($Path);
        if (!$field) {
            return false;
        }
        $data['upload'] = $field;
        $url = API_DOMAIN.'/oss/upload';
        $data['source'] = 1;
        $data['k1'] = time();
        $data['k2'] = MD5(MD5($data['k1']) . C('SUPER_AUTH_KEY'));
        $data['FileType'] = 'pdf';
        $result = self::curl($url, $data, 1);
        return $result;
    }


    static function curl($url, $params = false, $ispost = 0, $header=[], &$httpInfo = [])
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.118 Safari/537.36');
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE); // 对认证证书来源的检查
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE); // 从证书中检查SSL加密算法是否存在
        if(!empty($header)){
            curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        }
        if (is_array($params)) {
            $result = array_filter($params, function($v){
                return is_object($v) && get_class($v) == 'CURLFile';
            });
            if (empty($result)) {
                $params = http_build_query($params);
            }
        }
        if ($ispost) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
            curl_setopt($ch, CURLOPT_URL, $url);
        } else {
            if ($params) {
                curl_setopt($ch, CURLOPT_URL, $url . '?' . $params);
            } else {
                curl_setopt($ch, CURLOPT_URL, $url);
            }
        }
        $response = curl_exec($ch);

        $httpInfo = curl_getinfo($ch);
        if ($response === FALSE) {
//        echo "cURL Error: " . curl_error($ch);
            return false;
        }
        curl_close($ch);
        return $response;
    }


    static public function testOrOnline($test,$online)
    {
        if ($_SERVER['SERVER_NAME'] == 'api.ichunt.com'){
            return $online;
        }

        return $test;
    }


    static public function getVal($arr,$key)
    {
        if (empty($arr) || !isset($arr[$key])){
            return '';
        }
        return $arr[$key];
    }

    static public function sendDuanXin($param,$user,$keyword,$isIgnore=0)
    {


        $client = new Client();
        $time = time();
        $formParam = [
            'data'=>json_encode($param,JSON_UNESCAPED_UNICODE),
            'touser'=>json_encode([$user]),
            'keyword'=>$keyword,
            "k1"=>$time,
            "k2"=>md5(md5($time).'fh6y5t4rr351d2c3bryi'),
            'is_ignore'=>$isIgnore
        ];

        //发盯钉消息
        $response = $client->request('post', API_DOMAIN.'/msg/sendMessageByAuto',[
            'form_params'=>$formParam
        ]);

        $stringBody = $response->getBody()->getContents();

        \Think\Log::write(json_encode($formParam).'返回'.$stringBody);
        $msg_res_info = json_decode($stringBody, true);
        if ($msg_res_info && is_array($msg_res_info)){
            if (intval($msg_res_info['err_code']) === 0){
                return true;
            } else {
                \Think\Log::write("消息发送失败：" . json_encode($formParam));
                return false;
            }
        } else {
            \Think\Log::write("消息发送失败, 服务器返回数据错误" . $stringBody);
            return false;
        }
    }




    static function isPhoneNumberValid($number) {
        $pattern = "/^1[34578]\d{9}$/"; // 手机号正则表达式，以1开头，第二位为3/4/5/7/8，后面跟着9位数字
        if (preg_match($pattern, $number)) {
            return true;
        } else {
            return false;
        }
    }



    //发送异步post请求
    function sendAsyncRequest($url, $data)
    {
        $pid = pcntl_fork();

        if ($pid == -1) {
            // 创建子进程失败
            DingNotify::autoPushOrderFailNotify($data['order_id']);
        } elseif ($pid) {
            // 父进程
            // 可以在这里执行其他操作，不会阻塞子进程的执行
            return [];
        } else {
            CommonLogic::logsw(\GuzzleHttp\json_encode($data),'sendAsyncRequest');
            CommonLogic::logsw($url,'sendAsyncRequest');

            try{
                // 子进程
                // 执行请求，并等待子进程完成
                $order = OrderModel::findOrFail(intval($data['order_id']));
                $erpPushController = new ErpPushController();
                if($order->id_edit_order == 0){
                    //正常订单
                    $erpPushController->syncNewOrder($data,$order);
                }else{
                    //改单订单
                    $erpPushController->syncChangeOrder($data);
                }
            }catch(\Exception $e){
                \Think\Log::write($e->getMessage(),'WARN');
                DingNotify::pushOrderFailNotify($data['order_id'],$e->getMessage());
            }
            return [];
        }
    }


    public function getPayLocalGood()
    {
        $Redis = new \Redis();
        $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $Redis->auth(C('REDIS_PASSWORD'));

        //存储汇率
        //如果没有缓存和时间大于当天九点，就获取一次汇率
        if (empty($redisData = $Redis->get('ErpExchangeRate')) && (time() > strtotime(date('Y-m-d'))+(3600*9))){
            $res = (new \Supplychain\Controller\ErpPushController)->getPayLocalGood();
            if (empty(!$res)){
                $beginToday = mktime(0,0,0,date('m'),date('d'),date('Y'));
                $expireTime = 86400 - (time()-$beginToday);
                $Redis->set('ErpExchangeRate',json_encode($res));
                $Redis->expire('ErpExchangeRate',$expireTime);

                //如果没有今天的就存入
                $today = date('Y-m-d');

                //增加每日美元记录
                foreach ($res as $value){
                    foreach ($value as $k=>$v){

                        if (empty(ExchangeRate::where('rate_time',$today)->where('rate_type',$k)->value('id'))){
                            //获取一天前汇率
                            $lastRate = ExchangeRate::where('rate_type',$k)->orderBy('id','desc')->value('rate_num');
                            if (empty($lastRate)){
                                $lastRate = 6;
                            }
                            ExchangeRate::insertGetId([
                                'rate_type' =>$k,
                                'rate_num' =>$v,
                                'rate_diff' =>$v-$lastRate,
                                'rate_time' =>$today,
                            ]);
                        }
                    }
                }
            }
        }else{
            $res = json_decode($redisData,true);
        }
        return $res;
    }


    static public function getDate($strtotime)
    {
        if (empty($strtotime)){
            return '';
        }
        return date('Y-m-d H:i:s',$strtotime);
    }

    static public function getHaiGuanRate($currencyId)
    {
        $currencyCn = array_get(C("supply_currency"),$currencyId,'');
        if (empty($currencyCn)){
            throw new \Exception('没有获取到对应的币种中文信息');
        }

        $sqlMonth = date('Y-m').'-01';
        $sqlType = '海关'.$currencyCn;

        if (empty($rate = ExchangeRate::where('rate_type',$sqlType)->where('rate_time',$sqlMonth)->value('rate_num'))){
            $erpMonth = date('Ym');
            $requestData = [
                'curreny'=>$currencyCn,
                'month'=>$erpMonth
            ];
            try {
                $rate = ErpRequestLogic::getCodeData($requestData,'getErpCustomsRate')['data'];
                ExchangeRate::insertGetId([
                    'rate_time'=>$sqlMonth,
                    'rate_type'=>$sqlType,
                    'rate_num'=>$rate,
                ]);
            } catch (\Exception $e) {
                // 获取上个月海关汇率
                $targetTimestamp = strtotime('first day of last month', time());
                $sqlMonth = date('Y-m-d', $targetTimestamp);
                $rate = ExchangeRate::where('rate_type',$sqlType)->where('rate_time',$sqlMonth)->value('rate_num');
            }
        }
        return $rate;
    }

    //854232
    static public function checkHeader($customsCode,$pic)
    {
        if(empty($customsCode)){
            return [];
        }

        if (substr($customsCode,0,6) === '854232'){
            if (empty($pic)){
                throw new Exception('海关编码854232开头必须上传图片');
            }
        }
        return [];
    }

}