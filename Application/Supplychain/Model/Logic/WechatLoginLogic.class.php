<?php
/**
 * Created by 2021/9/30.
 * User: Joneq
 * Info: 2021/9/30
 * Time: 上午10:35
 */

namespace Supplychain\Model\Logic;


use Supplychain\Model\CustomerModel;

class WechatLoginLogic
{
    static private $appsecret = '6605d53cedd4f9e5395b8703de065a9f';
    static private $appid = 'wx15a79d955001ec2c';
    static private $redirectUri = 'https://scm.ichunt.com/music.html';


    public function getQr()
    {
        Vendor('Wechat/WechatOpen');
        $wechatQr= new \WechatOpen();
        $wechatQr->appsecret = self::$appsecret;
        $wechatQr->appid = self::$appid;
        $wechatQr->callback = self::$redirectUri;
        $url = $wechatQr->qrConnect(self::$redirectUri,'');
        return $url;
    }





    public function loginForWechatQr($data)
    {
        $code = $data['code'];
        $client = new \GuzzleHttp\Client();


        //获取access_token和openid
        $response = $client->request('GET','https://api.weixin.qq.com/sns/oauth2/access_token',[
            'query' => [
                'appid' => self::$appid,
                'secret' =>self::$appsecret,
                'code' => $code,
                'grant_type' => 'authorization_code',
            ]
        ]);
        $res = json_decode($response->getBody()->getContents(),true);
        //{
        //"access_token":"ACCESS_TOKEN",
        //"expires_in":7200,
        //"refresh_token":"REFRESH_TOKEN","openid":"OPENID",
        //"scope":"SCOPE"
        //}
        if (!isset($res['access_token'])){
            throw new \Exception('获取access_token失败：'.$res['errmsg']);
        }

        //获取unionid
        $response = $client->request('GET','https://api.weixin.qq.com/sns/userinfo',[
            'query' => [
                'access_token' => $res['access_token'],
                'openid' => $res['openid'],
            ]
        ]);
        $res = json_decode($response->getBody()->getContents(),true);

        //{
        //"openid":"OPENID",
        //"nickname":"NICKNAME",
        //"sex":1,
        //"province":"PROVINCE",
        //"city":"CITY",
        //"country":"COUNTRY",
        //"headimgurl": "https://thirdwx.qlogo.cn/mmopen/g3MonUZtNHkdmzicIlibx6iaFqAc56vxLSUfpb6n5WKSYVY0ChQKkiaJSgQ1dZuTOgvLLrhJbERQQ4eMsv84eavHiaiceqxibJxCfHe/0",
        //"privilege":[
        //"PRIVILEGE1",
        //"PRIVILEGE2"
        //],
        //"unionid": " o6_bmasdasdsad6_2sgVt7hMZOPfL"
        //
        //}
        if (!isset($res['unionid'])){
            throw new \Exception('获取unionid失败：'.$res['errmsg']);
        }

        if (empty(CustomerModel::where('wechat_unique_id',$res['unionid'])->value('user_id'))){
            throw new \Exception('获取unionid失败：'.$res['errmsg']);
        }

        return $res;
    }




}