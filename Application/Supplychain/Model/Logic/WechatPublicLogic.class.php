<?php
/**
 * Created by 2021/10/8.
 * User: Joneq
 * Info: 2021/10/8
 * Time: 下午3:53
 */

namespace Supplychain\Model\Logic;


use Com\Wechat;

use Home\Controller\LoginController;
use Supplychain\Model\CustomerModel;
use Supplychain\Service\DingNotify;

class WechatPublicLogic{
    //构造方法
    static $qrcode_url = "https://api.weixin.qq.com/cgi-bin/qrcode/create?";
    static $token_url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&";
    static $qrcode_get_url = "https://mp.weixin.qq.com/cgi-bin/showqrcode?";
    static private $appsecret = '59f67128727c9b87c4dfa4eedb6ecb9c';
    static private $appid = 'wxc1f26c102ff42945';
    static private $token = 'mwcvfgcwitemoxlrhart';
    static private $aesKey = '2kwksrtXqWhMc2iAEpT8ptZjPNSMP6hITJAk2MrFjlk';
    static public $scanAllResult = [
        'wait_scan'=>[1,'正在扫码'],
        'no_user'=>[3,'没有绑定用户或者未注册'],
        'key_expire'=>[4,'key已经失效'],
    ];
    const SUPPLYTOKENREDISKEY = 'supply-wechat-token-key';


    //获取二维码随机数下建
    static  public function getEwmRandKey()
    {
        $erm = date('YmdHis').rand(1000,10000);
        $Redis = new \Redis();
        $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $Redis->auth(C('REDIS_PASSWORD'));
        $Redis->set($erm,'wait_scan');
        $Redis->expire($erm,3600);
        return $erm;
    }


    //生成二维码
    public function getEwm($fqid,$type = 1){
        $ACCESS_TOKEN = $this->getToken();
        $url = $this->getQrcodeurl($ACCESS_TOKEN,$fqid,$type);
        //return $this->DownLoadQr($url,time());
        return $url;
    }

    protected function getQrcodeurl($ACCESS_TOKEN,$fqid,$type = 1){
        $url = self::$qrcode_url.'access_token='.$ACCESS_TOKEN;
        if($type == 1){
            //生成永久二维码
            $qrcode= '{"action_name": "QR_LIMIT_SCENE", "action_info": {"scene": {"scene_str": '.$fqid.'}}}';
        }else{
            //生成临时二维码
            $qrcode = '{"expire_seconds": 7200, "action_name": "QR_STR_SCENE", "action_info": {"scene": {"scene_str": "'.$fqid.'"}}}';
        }

        \Think\Log::write(':生成二维码参数：'.$qrcode,'WARN');

        $result = $this->http_post_data($url,$qrcode);
        $oo = json_decode($result[1]);
        if (empty($oo->ticket)){
            return false;
        }
        if(!$oo->ticket){
            $this->ErrorLogger('getQrcodeurl falied. Error Info: getQrcodeurl get failed');
            exit();
        }
        $url = self::$qrcode_get_url.'ticket='.$oo->ticket.'';
        return $url;
    }

    protected function getToken(){

        $Redis = new \Redis();
        $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $Redis->auth(C('REDIS_PASSWORD'));

        if (!empty($token = $Redis->get(self::SUPPLYTOKENREDISKEY))){
            return $token;
        }

        $ACCESS_TOKEN = file_get_contents(self::$token_url."appid=".self::$appid."&secret=".self::$appsecret);
        $ACCESS_TOKEN = json_decode($ACCESS_TOKEN);
        if (empty( $ACCESS_TOKEN->access_token)){
            throw new \Exception($ACCESS_TOKEN->errmsg);
        }
        $ACCESS_TOKEN = $ACCESS_TOKEN->access_token;

        $Redis->set(self::SUPPLYTOKENREDISKEY,$ACCESS_TOKEN);
        $Redis->expire(self::SUPPLYTOKENREDISKEY,7200);

        return $ACCESS_TOKEN;
    }

    protected function http_post_data($url, $data_string) {

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json; charset=utf-8',
                'Content-Length: ' . strlen($data_string))
        );
        ob_start();
        curl_exec($ch);
        if (curl_errno($ch)) {
            $this->ErrorLogger('curl falied. Error Info: '.curl_error($ch));
        }
        $return_content = ob_get_contents();
        ob_end_clean();
        $return_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        return array($return_code, $return_content);
    }

    //下载二维码到服务器
    protected function DownLoadQr($url,$filestring){
        if($url == ""){
            return false;
        }
        $filename = $filestring.rand(0,99999999999).'.jpg';
        ob_start();
        readfile($url);
        $img=ob_get_contents();
        ob_end_clean();
        $size=strlen($img);
        $fp2=fopen('static/qrcode/'.$filename,"a");
        if(fwrite($fp2,$img) === false){
            $this->ErrorLogger('dolwload image falied. Error Info: 无法写入图片');
            exit();
        }
        fclose($fp2);
        return 'static/qrcode/'.$filename;
    }

    //错误日志
    private function ErrorLogger($errMsg){
        $logger = fopen('log.txt', 'a+');
        fwrite($logger, date('Y-m-d H:i:s')." Error Info : ".$errMsg."\r\n");
        fclose($logger);
    }


    //根据不同的微信公众号回调类型去生成做不同的业务
     public function handleWechatPublicCallback($wechatData= [])
     {
         if (!isset($wechatData['Event']) || empty(isset($wechatData['Event']))){
             return [];
         }

         switch ($wechatData['Event']){
             case 'subscribe':
                 $wechatData['EventKey'] = explode('_',$wechatData['EventKey'])[1];
                 $this->handleScan($wechatData);
                 $this->scanReplay($wechatData);
                 break;
             case 'SCAN':
                 $this->handleScan($wechatData);
                 $this->scanReplay($wechatData);
                 break;
         }
     }

     //关注公众号的处理
    public function handleScan($wechatData)
    {

        $client = new \GuzzleHttp\Client();
        //获取unionid
        $response = $client->request('GET','https://api.weixin.qq.com/cgi-bin/user/info',[
            'query' => [
                'access_token' => $this->getToken(),
                'openid' => $wechatData['FromUserName'],
            ]
        ]);
        $res = json_decode($response->getBody()->getContents(),true);


        $Redis = new \Redis();
        $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $Redis->auth(C('REDIS_PASSWORD'));

        if (empty($Redis->get($wechatData['EventKey']))){
            \Think\Log::write($wechatData['EventKey'].'不存在：'.$res['errmsg'],'WARN');
            return '';
        }

        if (!isset($res['unionid'])){
            DingNotify::getUnionidFalse();
            \Think\Log::write('获取unionid失败：'.$res['errmsg'],'WARN');
            return '';
        }

        if (empty($userId = CustomerModel::where('wechat_unique_id',$res['unionid'])->value('user_id'))){
            $Redis->set($wechatData['EventKey'],'no_user');
            \Think\Log::write('用户暂未绑定unionid：'.$res['errmsg'],'WARN');
            return '';
        }

        \Think\Log::write($wechatData['EventKey'].':扫码成功：'.$userId,'WARN');
        $Redis->set($wechatData['EventKey'],$userId);
    }


    //获取微信登录扫码状态
    public function getLoginWechatPublicQrStatus($key)
    {
        $Redis = new \Redis();
        $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $Redis->auth(C('REDIS_PASSWORD'));

        $result = $Redis->get($key);

        //key失效了
        if (empty($result)){
            return ['code'=>self::$scanAllResult['key_expire'][0],'msg'=>self::$scanAllResult['key_expire'][1]];
        }

        //其他异常
        if (isset( self::$scanAllResult[$result])){
            return ['code'=>self::$scanAllResult[$result][0],'msg'=>self::$scanAllResult[$result][1]];
        }

        //生成用户信息,设置cookie和session
        $userId = $result;

        (new LoginController())->loginAct($userId, '20', 3, CustomerModel::where('user_id',$userId)->value('wechat_unique_id'));

        //生成完之后去除
        $Redis->del($key);

        return ['code'=>0,'msg'=>'登录成功'];
    }


    //扫码回复
    public function scanReplay()
    {
        $wechat = new Wechat(self::$token, self::$appid, self::$aesKey);
        $wechat->replyNewsOnce('您好','HI，终于等到您。欢迎点击下方链接，体验猎芯供应链订单服务！点我跳转到小程序', 'weixin://dl/business/?t=xiad1DEHt1a',
            'https://mmbiz.qpic.cn/mmbiz_png/uNgiaCTJ91oEFJ8N0yedkevSicCfhZqSdbq43vNFELs3T219jofRk5NqiawPiaXecDo8DzcSnGz21y0PIDDPU4EpCg/0?wx_fmt=png');
    }


    public function getLogisticsInfo($logisticsInfo)
    {
        if ($logisticsInfo['code'] !== 'gyyveryshuai'){
            throw new \Exception('验证不通过');
        }

        if (empty($LogisticCode = $logisticsInfo['mail_sn'])){
            throw new \Exception('物流单号为空');
        }

        $config = C('KD100_CONFIG');
        $datas = array(
            'num' => $LogisticCode,
            'key' => $config['appKey'],
        );
        $info = get_curl($config['autoUrl'], $datas);
        $list = json_decode($info, true);
        if (empty($list)) {
            throw new \Exception('没有获取到快递公司');
        }
        $com = array_column($list, 'comCode');
        if (count($com) == 1) {
            $com = $com[0];
        }

        $info = C('KD100_CONFIG');
        $info['num'] = $LogisticCode;
        $info['com'] = $com;
        if ($com == 'shunfeng') {//顺丰需要传收寄件人手机号
            if (empty($phone = $logisticsInfo['phone'])){
                throw new \Exception('联系人手机号为空');
            }
            $info['phone'] = $phone;
        }

        $json_data = array(
            'com' => $info['com'],
            'num' => $info['num'],
            'phone' => $info['phone'],
        );
        $requestData = json_encode($json_data);
        $datas = array(
            'customer' => $info['customer'],
            'param' => $requestData,
            'sign' => strtoupper(md5($requestData.$info['appKey'].$info['customer'])),
        );

        $logisticsInfoJson = post_curl($info['reqUrl'], $datas);

        $result = json_decode($logisticsInfoJson, true);
        if($result['message'] === 'ok') {
            foreach ($result['data'] as &$v) {
                //统一变量名
                $item = array(
                    'AcceptTime' => $v['time'],
                    'AcceptStation' => $v['context'],
                );
                $v = $item;
            }
            $result['out_store_time_cn'] = $logisticsInfo['create_time'];//出库时间
            $result['carrier'] = $logisticsInfo['carrier']; //配送方式
            return $result;
        }else {
            throw new \Exception('没有查找到物流信息:'.$result['message']);
        }
    }



}
