<?php
/**
 * Created by 2023/3/21.
 * User: Jone
 * Info: 2023/3/21
 * Time: 上午10:51
 */

namespace Supplychain\Model\Logic;


class WorkToolLogic
{


    /*
     * https://admin.worktool.asrtts.cn/robot/update/configQa
     * Worktool  注册手机号：13088646975 注册邮箱：<EMAIL> 注册密码：SCMichunt2021
     */

    Const ROBOTID = 'f788da8147974a0f8cea2da14a82239b';








    public function sendQiYeNotify($data)
    {
        $curl = curl_init();


        $sendData = [
            'socketType'=>2,
            'list'=>[
                [
                    'type'=>203,
                    'titleList'=>[$data['title']],
                    'receivedContent'=>$data['content']
                ]
            ]
        ];

        //{
        //    "socketType":2,
        //    "list":[
        //        {
        //            "type":203,
        //            "titleList":[
        //                "仑哥(这里改成你的微信昵称或群名)"
        //            ],
        //            "receivedContent":"你好~"
        //        }
        //    ]
        //}

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://worktool.asrtts.cn/wework/sendRawMessage?robotId='.self::ROBOTID,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>\GuzzleHttp\json_encode($sendData),
            CURLOPT_HTTPHEADER => array(
                'User-Agent: Apifox/1.0.0 (https://www.apifox.cn)',
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
    }
}