<?php

/**
 * Created by 2022/8/19.
 * User: Jone
 * Info: 2022/8/19
 * Time: 上午11:16
 */
namespace Supplychain\Model\Logic\QiyeWechatLogic;
class GetTokenLogic
{

    //获得审批token的url
    Const AUDITURL = 'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=wwe9a094de171b9c81&corpsecret=jAPucIFOZh50-A0-hTbDruXRpZAZKixqh03aIo7U-hU';
    Const AUDITKEY = 'supply-qywechat-audit-token-key';
    //获得群消息token的url
    Const GROUPMSGURL = 'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=wwe9a094de171b9c81&corpsecret=jb9SO6vZmmoK0Yy-yb4p_KFeemN_lXv7Al0-k09Q3iU&debug=1';
    Const GROUPMSGKEY = 'supply-qywechat-groupmsg-token-key';



    public function getAuditToken()
    {
        return $this->getToken(self::AUDITURL,self::AUDITKEY);
    }


    public function getGroupMsgToken()
    {
        return $this->getToken(self::GROUPMSGURL,self::GROUPMSGKEY);
    }



    protected function getToken($url,$tokenKey){

        $Redis = new \Redis();
        $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $Redis->auth(C('REDIS_PASSWORD'));

        if (!empty($token = $Redis->get($tokenKey))){
            return $token;
        }

        $ACCESS_TOKEN = file_get_contents($url);
        $ACCESS_TOKEN = json_decode($ACCESS_TOKEN);
        if (empty( $ACCESS_TOKEN->access_token)){
            throw new \Exception($ACCESS_TOKEN->errmsg);
        }
        $ACCESS_TOKEN = $ACCESS_TOKEN->access_token;

        $Redis->set($tokenKey,$ACCESS_TOKEN);
        $Redis->expire($tokenKey,7100);

        return $ACCESS_TOKEN;
    }
}