<?php
/**
 * Created by 2022/8/19.
 * User: Jone
 * Info: 2022/8/19
 * Time: 上午11:15
 */
namespace Supplychain\Model\Logic\QiyeWechatLogic;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\WorkToolLogic;
use Supplychain\Model\SupplyIc\CreditApprovalModel;
use Supplychain\Model\SupplyIc\DailyPerformanceStatisticsModel;
use Supplychain\Model\SupplyIc\OrderDealRecordModel;
use Supplychain\Model\SupplyIc\OutboundApplicationStatisticsModel;
use Supplychain\Model\SupplyIc\QuotaApplicationModel;
use Supplychain\Model\SupplyIc\WechatUserInfoModel;
use Think\Exception;
use Think\Log;
class BaseLogic
{

    //发群消息url
    Const SENDGROUPMSGURL = 'https://qyapi.weixin.qq.com/cgi-bin/appchat/send?access_token=';

    //获取审批详情
    Const AUDITDETAILURL = 'https://qyapi.weixin.qq.com/cgi-bin/oa/getapprovaldetail?access_token=';

    //获取用户详情
    Const USERINFOURL = 'https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=';

    //获取部门详情
    Const DEPARTMENTINFOURL ='https://qyapi.weixin.qq.com/cgi-bin/department/get?access_token=';


    //企业微信审批设置回调参数，地址 https://work.weixin.qq.com/wework_admin/frame#apps/modApiApp/3010040/approval
    private $token = 'v5k1';
    private $encodingAesKey = '3s528vq8GxLPnr36h3MfpsnJe45mzh2qTKdWj5b1vIe';
    private $receiveId = 'wwe9a094de171b9c81';

    //bug改成1会有分钟限制
    Const ISDEBUG = 0;

    static public function test()
    {
        var_dump(1);die;
    }



    //接受企业通知
    public function acceptQiyeWechatNotify()
    {
        $sMsgSignature = $_GET['msg_signature'];
        $sTimeStamp = $_GET['timestamp'];
        $sNonce = $_GET['nonce'];
        $postData = file_get_contents('php://input');
        $returnData = '';

        vendor('weworkapi_php.callback.WXBizMsgCrypt');

        $weWrokObj = new \WXBizMsgCrypt($this->token,$this->encodingAesKey,$this->receiveId);

        $errCode = $weWrokObj->DecryptMsg($sMsgSignature, $sTimeStamp, $sNonce, $postData, $returnData);

        if ($errCode != 0){
            throw new Exception('微信错误码'.$errCode);
        }

        //xml的结构如下{"ToUserName":"wwe9a094de171b9c81","FromUserName":"sys","CreateTime":"1661237930","MsgType":"event","Event":"sys_approval_change","AgentID":"3010040","ApprovalInfo":{"SpNo":"202208230004","SpName":"\u5916\u51fa\u62dc\u8bbf\u7533\u8bf7","SpStatus":"2","TemplateId":"3WKhWTEicpJG684YcJkHkDF5L9uQF3pJ54rXN1Rz","ApplyTime":"1661221318","Applyer":{"UserId":"LiWei","Party":"18","Vid":"1688857390398741"},"SpRecord":[{"SpStatus":"2","ApproverAttr":"1","Details":{"Approver":{"UserId":"BoKe","Vid":"1688855946343172"},"Speech":[],"SpStatus":"2","SpTime":"1661221636"}},{"SpStatus":"2","ApproverAttr":"1","Details":[{"Approver":{"UserId":"kathyzhou","Vid":"1688857023300556"},"Speech":[],"SpStatus":"1","SpTime":"0"},{"Approver":{"UserId":"WangJingJun","Vid":"1688854673351817"},"Speech":[],"SpStatus":"2","SpTime":"1661237930"}]}],"Notifyer":{"UserId":"ChengZi","Vid":"1688857514387812"},"StatuChangeEvent":"2"}}
        $returnData = CommonLogic::xmlToArray($returnData);

        //$returnData['ApprovalInfo']['SpStatus'] 审批状态 1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付

        //暂时只记录已经通过的
        switch ($returnData['ApprovalInfo']['SpName']){

            case '外出拜访申请':
                $this->outboundApplicationHandle($returnData);
                break;
            case '临时额度申请表':
                if (intval(($returnData['ApprovalInfo']['SpStatus'])) !== 2){
                    break;
                }
                $this->quotaApplicationHandle($returnData);
                break;
            case '授信申请表(垫税50万审批)':
                if (intval(($returnData['ApprovalInfo']['SpStatus'])) !== 1){
                    break;
                }
                $this->creditApprovalHandle($returnData);
                break;
        }

    }



    //额度申请处理
    public function creditApprovalHandle($data)
    {
        //获取已经通过的事项详情
        $auditSpInfo = $this->getSpInfo($data);

        //获取用户详情
        $userInfo = $this->getWechatUserInfo($auditSpInfo['applyer']['userid']);

        //获取部门详情
        $departmentInfo = $this->getWechatDepartMentInfo($auditSpInfo['applyer']['userid'],$auditSpInfo['applyer']['partyid']);

        $now = time();
        $activeData = [

            'work_name'=>$auditSpInfo['apply_data']['contents'][0]['value']['members'][0]['name'],
            'credit_customer_name'=>$auditSpInfo['apply_data']['contents'][1]['value']['text'],
            'front_money'=>$auditSpInfo['apply_data']['contents'][5]['value']['new_money'],
            'agent_money'=>$auditSpInfo['apply_data']['contents'][6]['value']['new_money'],
            'offset_money'=>$auditSpInfo['apply_data']['contents'][7]['value']['new_money'],
            'is_single_begin'=>($auditSpInfo['apply_data']['contents'][8]['value']['selector']['options'][0]['value'][0]['text'] === '单抬头')?1:0,
            'is_have_guarantee'=>($auditSpInfo['apply_data']['contents'][9]['value']['selector']['options'][0]['value'][0]['text'] === '是')?1:0,
            'is_have_invoice'=>($auditSpInfo['apply_data']['contents'][10]['value']['selector']['options'][0]['value'][0]['text'] === '是')?1:0,
            'product_type'=>$auditSpInfo['apply_data']['contents'][11]['value']['text'],
            'average_monthly_import_volume'=>$auditSpInfo['apply_data']['contents'][12]['value']['new_money'],
            'accounting_period_type'=>$auditSpInfo['apply_data']['contents'][15]['value']['selector']['options'][0]['value'][0]['text'],
            'accounting_period_day'=>$auditSpInfo['apply_data']['contents'][16]['value']['new_number'],
            'remark'=>$auditSpInfo['apply_data']['contents'][17]['value']['children'][0]['list'][0]['value']['text'],
            'attachment'=>'',
            'sp_no'=>$data['ApprovalInfo']['SpNo'],
            'create_time'=>$now,
            'audit_time'=>$now,
            'wechat_user_id'=>$auditSpInfo['applyer']['userid'],
            'apply_user_department'=>$departmentInfo['department']['name'],
            'audit_status'=>2,
        ];

        //存在单号更新，不存在添加
        $creditApprovalModel = new CreditApprovalModel();
        if (empty($ctalId = $creditApprovalModel->where('sp_no',$activeData['sp_no'])->value('ctal_id'))){
            $creditApprovalModel->insertGetId($activeData);
        }else{
            $creditApprovalModel->where('sp_no',$activeData['sp_no'])->update($activeData);
        }

    }

    //额度申请处理
    public function quotaApplicationHandle($data)
    {
        //获取已经通过的事项详情
        $auditSpInfo = $this->getSpInfo($data);

        //获取用户详情
        $userInfo = $this->getWechatUserInfo($auditSpInfo['applyer']['userid']);

        //获取部门详情
        $departmentInfo = $this->getWechatDepartMentInfo($auditSpInfo['applyer']['userid'],$auditSpInfo['applyer']['partyid']);

        $activeData = [
            'company_name'=>$auditSpInfo['apply_data']['contents'][1]['value']['text'],
            'wechat_user_id'=>$auditSpInfo['applyer']['userid'],
            'work_user_name'=>$userInfo['name'],
            'apply_user_department'=>$departmentInfo['department']['name'],
            'temporary_limit_money'=>$auditSpInfo['apply_data']['contents'][3]['value']['new_money'],
            'limit_money_type'=>$auditSpInfo['apply_data']['contents'][2]['value']['selector']['options'][0]['value'][0]['text'],
            'temporary_limit_money_begin_time'=>$auditSpInfo['apply_data']['contents'][8]['value']['date']['s_timestamp'],
            'temporary_limit_money_end_time'=>$auditSpInfo['apply_data']['contents'][9]['value']['date']['s_timestamp'],
            'is_have_guarantee'=>($auditSpInfo['apply_data']['contents'][4]['value']['selector']['options'][0]['value'][0]['text'] === '是')?1:0,
            'is_have_invoice'=>($auditSpInfo['apply_data']['contents'][5]['value']['selector']['options'][0]['value'][0]['text'] === '是')?1:0,
            'product_type'=>$auditSpInfo['apply_data']['contents'][6]['value']['text'],
            'average_monthly_import_volume'=>$auditSpInfo['apply_data']['contents'][7]['value']['new_money'],
            'time_range'=>(($auditSpInfo['apply_data']['contents'][9]['value']['date']['s_timestamp'] - $auditSpInfo['apply_data']['contents'][8]['value']['date']['s_timestamp']) /3600).'H',
            'attachment'=>'',
            'remark'=>$auditSpInfo['apply_data']['contents'][10]['value']['children'][0]['list'][0]['value']['text'],
            'audit_status'=>2,
            'sp_no'=>$data['ApprovalInfo']['SpNo'],
        ];

        //存在单号更新，不存在添加
        $quotaAnModel = new QuotaApplicationModel();
        if (empty($odanssId = $quotaAnModel->where('sp_no',$activeData['sp_no'])->value('qaan_id'))){
            $activeData['create_time']=time();
            $quotaAnModel->insertGetId($activeData);
        }else{
            $quotaAnModel->where('sp_no',$activeData['sp_no'])->update($activeData);
        }

    }


    //外外出拜访申请处理
    public function outboundApplicationHandle($data)
    {

        //获取事项详情
        $auditSpInfo = $this->getSpInfo($data);

        //获取用户详情
        $userInfo = $this->getWechatUserInfo($auditSpInfo['applyer']['userid']);

        //获取审批人详情
        if (intval($data['ApprovalInfo']['SpStatus']) !== 2){
            $auditUserName = '';
        }else{
            $auditUserName = $this->getWechatUserInfo($auditSpInfo['sp_record'][0]['details'][0]['approver']['userid'])['name'];
        }

        //获取部门详情
        $departmentInfo = $this->getWechatDepartMentInfo($auditSpInfo['applyer']['userid'],$auditSpInfo['applyer']['partyid']);


        //本周开始结束时间
        $weekTimeBegin = mktime(0, 0, 0, date('m'), (date('d') - (date('w')>0 ? date('w') : 7) + 1), date('Y'));
        $weekTimeEnd = $weekTimeBegin+(86400*7);

        $outAnSsModel = new OutboundApplicationStatisticsModel();
        $time = time();
        $activeData = [
            'sp_no'=>$data['ApprovalInfo']['SpNo'],
            'project_name'=>$auditSpInfo['apply_data']['contents'][0]['value']['text'],
            'out_user_name'=>$userInfo['name'],
            'apply_user_department'=>$departmentInfo['department']['name'],
            'begin_time'=>$auditSpInfo['apply_data']['contents'][3]['value']['attendance']['date_range']['new_begin'],
            'end_time'=>$auditSpInfo['apply_data']['contents'][3]['value']['attendance']['date_range']['new_end'],
            'out_money'=>$auditSpInfo['apply_data']['contents'][4]['value']['new_money'],

            'out_area'=>$auditSpInfo['apply_data']['contents'][2]['value']['text'],
            'out_time'=>(intval($auditSpInfo['apply_data']['contents'][3]['value']['attendance']['date_range']['new_duration'])/3600).'h',
            'remark'=>$auditSpInfo['apply_data']['contents'][5]['value']['text'],
            'audit_user_name'=>$auditUserName,
            'audit_status'=>$data['ApprovalInfo']['SpStatus'],
            'audit_time'=>$time,
            'wechat_user_id'=>$auditSpInfo['applyer']['userid']
        ];

        //存在单号更新，不存在添加
        if (empty($odanssId = $outAnSsModel->where('sp_no',$activeData['sp_no'])->value('odanss_id'))){
            $activeData['out_num_in_week']=1;
            $activeData['create_time']=$time;
            $outAnSsModel->insertGetId($activeData);
        }else{
            $activeData['out_num_in_week']=$outAnSsModel->where('create_time','>=',$weekTimeBegin)->where('create_time','<=',$weekTimeEnd)->where('out_user_name',$userInfo['name'])->count('odanss_id');
            $outAnSsModel->where('sp_no',$activeData['sp_no'])->update($activeData);
        }

    }




    //用来检测企业微信后台网址配置是否可用的
    public function verifyUrl()
    {
        vendor('weworkapi_php.callback.WXBizMsgCrypt');

        $weWrokObj = new \WXBizMsgCrypt($this->token,$this->encodingAesKey,$this->receiveId);

        if (empty($_GET['echostr'])){
            echo '没有进入到回调配置';
        }else{
            $sMsgSignature = $_GET['msg_signature'];
            $sTimeStamp = $_GET['timestamp'];
            $sNonce = $_GET['nonce'];
            $sEchoStr = $_GET['echostr'];
            $sReplyEchoStr = '';
        }

        $errCode = $weWrokObj->VerifyURL($sMsgSignature, $sTimeStamp, $sNonce, $sEchoStr, $sReplyEchoStr);

        if ($errCode == 0){
            echo $sReplyEchoStr;
        }else{
            print ('Err:'.$errCode);
        }
    }



    /*
     *
     * daily_performance_statistics 每日业绩统计推送
     *
     *
     *  业绩项目：全部 /业务一组 /业务二组
        供应链代理费：全部 /业务一组 /业务二组
        供应链费用：全部 /业务一组 /业务二组
        富开代理费：全部 /业务一组 /业务二组
        富开费用：全部 /业务一组 /业务二组
        新增签约客户：全部 /业务一组 /业务二组(可以延后)
        新增授信审批：全部 /业务一组 /业务二组
        外出人次：全部 /业务一组 /业务二组
     *
     *
     *
     */
    public function sendGroupMsg($data)
    {

        $url = self::SENDGROUPMSGURL.(new GetTokenLogic())->getGroupMsgToken().'&debug='.self::ISDEBUG;

        $client = new \GuzzleHttp\Client();

        //获取今天需要发送数据
        $sendData = DailyPerformanceStatisticsModel::where('work_day',$data['date'])->get()->toArray();

        if (empty($sendData)){
            throw new \Exception('今天数据信息没有');
        }

        $orderDealRecordModel = new OrderDealRecordModel();

        $todayStartTime = strtotime(date('Y-m-d'));
        $monthStartTime = strtotime(date('Y-m'));
        $now = time();



        foreach ($sendData as $value) {

            if ($value['group'] == '全部'){
                $todayBussinessMoney = $orderDealRecordModel->where('create_time','>=',$todayStartTime)->where('create_time','<',$now)->sum('order_amt');
                $monthBussinessMoney = $orderDealRecordModel->where('create_time','>=',$monthStartTime)->where('create_time','<',$now)->sum('order_amt');
                $content = '每日业绩汇总
                >**'.date("Y-m-d").'** 
				>
				>业务部门：<font color=\"black\">'.$value['group'].'</font>
				>今日业绩(含税)：<font color=\"black\">'.round($todayBussinessMoney/10000,2).'万元</font>
				>一群业绩(含税)：<font color=\"black\">'.round($orderDealRecordModel->where('group_name','一群')->where('create_time','>=',$todayStartTime)->where('create_time','<',$now)->sum('order_amt')/10000,2).'万元</font>
				>二群业绩(含税)：<font color=\"black\">'.round($orderDealRecordModel->where('group_name','二群')->where('create_time','>=',$todayStartTime)->where('create_time','<',$now)->sum('order_amt')/10000,2).'万元</font>
				>供应链代理费：<font color=\"black\">'.round($value['supply_agent_money'],2).'</font>
				>富开代理费：<font color=\"black\">'.round($value['fukai_agent_money'],2).'</font>
				>本月累计业绩：<font color=\"black\">'.round($monthBussinessMoney/10000,2).'万元</font>
				>新增签约客户：<font color=\"black\">'.round($value['new_customer_num'],2).'</font>
				>新增授信审批：<font color=\"black\">'.round($value['new_credit_apply_money'],2).'</font>
				>外出人次：<font color=\"black\">'.round($value['out_people_num'],2).'</font>
				>
				>';
            }else{
                $todayBussinessMoney = $orderDealRecordModel->where('department',$value['group'])->where('create_time','>=',$todayStartTime)->where('create_time','<',$now)->sum('order_amt');
                $monthBussinessMoney = $orderDealRecordModel->where('department',$value['group'])->where('create_time','>=',$monthStartTime)->where('create_time','<',$now)->sum('order_amt');
                $content = '每日业绩推送
                >**'.date("Y-m-d").'** 
				>
				>业务部门：<font color=\"black\">'.$value['group'].'</font>
				>组长名称：<font color=\"black\">'.$value['group_leader'].'</font>
				>今日业绩(含税)：<font color=\"black\">'.$todayBussinessMoney.'</font>
				>供应链代理费：<font color=\"black\">'.round($value['supply_agent_money'],2).'</font>
				>富开代理费：<font color=\"black\">'.round($value['fukai_agent_money'],2).'</font>
				>本月累计业绩：<font color=\"black\">'.round($monthBussinessMoney/10000,2).'万元</font>
				>
				>';
            }


            $response = $client->request('POST',$url,[
                'json' => [
                    'chatid'=>'notifytest',
                    'msgtype'=>'markdown',
                    'markdown'=>[
                        'content'=>$content
                    ],
                    'safe'=>0,
                ]
            ]);

            $res = json_decode($response->getBody()->getContents(),true);
            if ($res['errcode'] !== 0){
                throw new \Exception($res['errmsg']);
            }
        }

        $this->sendExcel($client,$url);

        return 'ok';
    }




    public function sendCommonErrorNotify($data)
    {
        $url = self::SENDGROUPMSGURL.(new GetTokenLogic())->getGroupMsgToken().'&debug='.self::ISDEBUG;

        $client = new \GuzzleHttp\Client();

        $response = $client->request('POST',$url,[
            'json' => [
                'chatid'=>'supplyCommonErrorNotify',
                'msgtype'=>'text',
                'text'=>[
                    'content'=>$data['content']
                ],
                'safe'=>0,
            ]
        ]);
        $res = json_decode($response->getBody()->getContents(),true);
        if ($res['errcode'] !== 0){
            throw new \Exception('推送微信错误消息异常：'.$res['errmsg']);
        }

        return 'okk';
    }




    //获取审批事项详情
    public function getSpInfo($data)
    {
        $client = new \GuzzleHttp\Client();

        $url = self::AUDITDETAILURL.(new GetTokenLogic())->getAuditToken().'&debug='.self::ISDEBUG;
        $response = $client->request('POST',$url,[
            'json' => ['sp_no'=>$data['ApprovalInfo']['SpNo']]
        ]);
        $auditSpInfo = json_decode($response->getBody()->getContents(),true);
        if ($auditSpInfo['errcode'] !== 0){
            throw new \Exception('获取已经通过的事项详情'.$auditSpInfo['errmsg']);
        }
        $auditSpInfo = $auditSpInfo['info'];
        return $auditSpInfo;
    }

    //传入微信用户ID，返回微信用户信息，如果mysql没有，缓存起来，如果有，直接取mysql，因为微信读取有限制
    public function getWechatUserInfo($userId)
    {
        //如果有就返回，没有就更新
        $wechatUserInfo = new WechatUserInfoModel();
        if (!empty($userInfo = $wechatUserInfo->where('wechat_user_id',$userId)->value('wechat_user_info_json'))){
            return \GuzzleHttp\json_decode($userInfo,true);
        }

        $client = new \GuzzleHttp\Client();

        $url = self::USERINFOURL.(new GetTokenLogic())->getAuditToken().'&userid='.$userId.'&debug='.self::ISDEBUG;
        $response = $client->request('GET',$url);

        $userInfo = json_decode($response->getBody()->getContents(),true);
        if ($userInfo['errcode'] !== 0){
            throw new \Exception('获取用户详情'.$userInfo['errmsg']);
        }


        $wechatUserInfo->insertGetId([
            'create_time'=>time(),
            'wechat_user_id'=>$userId,
            'wechat_user_info_json'=>\GuzzleHttp\json_encode($userInfo)
        ]);


        return $userInfo;
    }

    //传入微信用户ID，返回微信部门信息，如果mysql没有，缓存起来，如果有，直接取mysql，因为微信读取有限制 部门只能修改
    public function getWechatDepartMentInfo($userId,$departMentId)
    {
        //如果有就返回，没有就更新
        $wechatUserInfo = new WechatUserInfoModel();
        if (!empty($departmentInfo = $wechatUserInfo->where('wechat_user_id',$userId)->where('wechat_department_id',$departMentId)->value('wechat_department_info_json'))){
            return \GuzzleHttp\json_decode($departmentInfo,true);
        }

        $client = new \GuzzleHttp\Client();

        $url = self::DEPARTMENTINFOURL.(new GetTokenLogic())->getAuditToken().'&id='.$departMentId.'&debug='.self::ISDEBUG;
        $response = $client->request('GET',$url);
        $departmentInfo = json_decode($response->getBody()->getContents(),true);
        if ($departmentInfo['errcode'] !== 0){
            throw new \Exception('获取部门详情'.$departmentInfo['errmsg']);
        }

        $wechatUserInfo->where('wechat_user_id',$userId)->update([
            'wechat_department_id'=>$departMentId,
            'wechat_department_info_json'=>\GuzzleHttp\json_encode($departmentInfo)
        ]);

        return $departmentInfo;
    }

    //发送excel的数据
    public function sendExcel($client,$url)
    {

        $date = date('Y-m-d');
        $excelData = [
            [
                'title'=>'业务订单金额/含税',
                'url'=>'http://scm-internal.ichunt.net/excel/orderMoneyStatistics',
            ],
            [
                'title'=>'业务订单金额含税分析表',
                'url'=>'http://scm-internal.ichunt.net/excel/orderMoneyAnalysis',
            ],
            [
                'title'=>'业务利润（未税）',
                'url'=>'http://scm-internal.ichunt.net/excel/profitStatistics',
            ],
            [
                'title'=>'业务利润分析表',
                'url'=>'http://scm-internal.ichunt.net/excel/profitAnalysis',
            ],
        ];

        foreach ($excelData as $value){

            $response = $client->request('POST',$url,[
                'json' => [
                    'chatid'=>'notifytest',
                    'msgtype'=>'textcard',
                    'textcard'=>[
                        "title" => $value['title'],
                        'description'=>$date,
                        'url'=>$value['url'],
                    'btntxt'=>'查看明细'
                ],
                    'safe'=>0,
                ]
            ]);

            $res = json_decode($response->getBody()->getContents(),true);
            if ($res['errcode'] !== 0){
                throw new \Exception($res['errmsg']);
            }

        }

        return 'ok';
    }


    public function sendMsgForSignTodayCustomsDeclarationAndReceipt($data)
    {
        $type = $data['type'];
        $user = $data['user'];

        switch ($type){
            case 1:
                $content  = '查车';break;
            case 2:
                $content  = '通关';break;
            case 3:
                $content  = '深圳收货';break;
        }


        $content = $user.$content.'确认，确认时间:'.date('Y-m-d H:i');

        (new WorkToolLogic())->sendQiYeNotify(['title'=>'供应链推送测试群','content'=>$content]);
    }




}