<?php
/**
 * Created by 2022/4/25.
 * User: Jone
 * Info: 2022/4/25
 * Time: 下午5:36
 */

namespace Supplychain\Model\Logic;


use Supplychain\Model\Logic\JdpayRequest\Order;
use Supplychain\Model\Logic\JdpayRequest\PayOrder;
use Supplychain\Model\OrderAddressModel;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\RegionModel;
use Think\Log;
class LxJdJrLogic
{
    //猎芯京东金融逻辑层


    //生成订单
    public function createOrder($data)
    {
        $orderInfo = OrderModel::where('order_id',$data['order_id'])->first();

        $jdjrInfo = CommonLogic::getJdJrInfo();


        $data['setCustCode'] = $jdjrInfo['company_code'];
        $data['setSettleUserCode'] = $jdjrInfo['company_code'];
        $data['companyName'] = $jdjrInfo['company_name'];


        $data['order_sn'] = $orderInfo->order_sn;
        $data['order_amount'] = $orderInfo->order_price;
        $data['order_info'] = $orderInfo;
        Log::write('京东订单请求原始参数:'.\GuzzleHttp\json_encode($data),Log::INFO);

        $requestJd = self::payOrder($data);
        Log::write('京东订单请求原始参数:'.\GuzzleHttp\json_encode($requestJd),Log::INFO);
        $returnData = (new JdpayLogic())->execute($requestJd['method'],$requestJd['request']);
        Log::write('京东响应参数:'.\GuzzleHttp\json_encode($returnData),Log::INFO);
        //成功的返回
        //{"ext":{"orderNo":"O2022042801000001"},"reqNo":"bizno48de4f165113045637","resCode":"0000000","resDate":"20220428152056","payUrl":"https://loan.jd.com/cgrz/order?productCode=25041&orderNo=O2022042801000001","resDesc":"成功"}
        $returnData = \GuzzleHttp\json_decode($returnData,true);
        if (!isset($returnData['resCode']) || $returnData['resCode'] != '0000000'){
            //创建订单失败，修改订单为待提交
            OrderModel::where('order_id',$data['order_id'])->update(['status'=>-2]);
            throw new \Exception('京东金融订单创建失败:'.$returnData['resDesc']);
        }

        return $returnData;
    }

    static public function payOrder($data=[])
    {
        $request = new PayOrder();

        $orderCode = $data['order_sn'];
        $request->setBizNo(JdpayLogic::getBizno());//业务编号
        $request->setReqDate(JdpayLogic::getReqDate());//请求时间yyyyMMddHHmmss
        $request->setProductCode(JdpayLogic::getProductCode());//产品编码
        $request->setCustCode($data['setCustCode']);//经销商编码,渠道商唯一指定认可标识-结算主体ID
        $request->setOrderCode($orderCode);//订单编号
        $request->setOrderAmount($data['order_amount']);//订单金额，单位：元
        $request->setApplyAmount($data['total_amount']);//申请订单支付金额。即借款申请金额，单位：元
        $request->setOrderIndate(JdpayLogic::getOrderIndate());//订单有效期，yyyyMMddHHmmss
        $request->setOrderDesc('猎芯订单推送');//订单描述
        $request->setSettleUserCode($data['setCustCode']);//结算主体编号
        $request->setProductCategorys(['电子元器件']);
        $request->setOrderStatus('CREATED');
        $request->setPayStatus('UNPAY');
        $request->setCreateDate(JdpayLogic::getCreatedDate());
        $request->setNotifyUrl(JdpayLogic::getPayOrderNotifyUrl());
        $request->setReceiveName(C('JDPAY.receiveName'));//TODO  收款账户名称线上要更新
        $request->setReceiveCardNo(C('JDPAY.receiveCardNo'));//TODO 收款账户名称线上要更新
        $request->setRiskControlParams(self::getPayOrderRiskControlParams($data));
        return ['request'=>$request,'method'=>'/smapi/v1/co.yfk/payOrder'];
    }

    static public function getPayOrderRiskControlParams($data=[])
    {
        $orderAddress = OrderAddressModel::where("order_id",$data['order_info']->order_id)->first();


        if (!empty($orderAddress->province)){
            $districtName =  RegionModel::where("region_id",$orderAddress->district)->value("region_name");
            $cityName =  RegionModel::where("region_id",$orderAddress->city)->value("region_name");
            $prociceName =  RegionModel::where("region_id",$orderAddress->province)->value("region_name");
            $detaAddress = $prociceName.$cityName.$districtName.$orderAddress->detail_address;
        }else{
            $districtName =  '';
            $cityName =  '';
            $prociceName =  '';
            $detaAddress = $prociceName.$cityName.$districtName.$orderAddress->detail_address;
        }

        $returnData = [
            'orderCode'=>$data['order_sn'],//订单ID唯一主键
            'orderStatus'=>'待付款',//订单状态合作方推送中文含义：待付款
            'chanel'=>'线上',//订单渠道传输中文解释
            'orderType'=>'01',//订单类型21-退换货单 （终端B2B订单）101-终端B2B实体商品订单 111-终端B2B发货单
            'orderCreateDate'=>date("YmdHis"),//订单时间下单时间
            'orderAmount'=>$data['total_amount'],//订单金额订单实际费用（销售订单记录实际支付金额，退换货单记录相应销售订单支付金额）
            'productName'=>'电子元器件',//商品名称
            'productCount'=> OrderGoodsModel::where('order_id',$data['order_info']->order_id)->sum('numbers'),//商品数量
            'productPrice'=>$data['order_amount'],//商品原始价格
            'productOrderPrice'=>$data['order_amount'],//商品订单价格
            'discountsAmount'=>0,//商品优惠金额
            'collecterName'=>$orderAddress->consignee,//收货人姓名
            'collecterAddress'=>$detaAddress,//收货人地址
            'collecterProvince'=>$prociceName,//收货省份名称
            'collecterCity'=>$cityName,//收货城市名称
            'collecterCounty'=>$districtName,//收货区县名称
            'collecterMobile'=>$orderAddress->mobile,//收货手机号
        ];
        return $returnData;
    }




    //获取客户信息
    public function getUserCompanyInfo()
    {

    }


}