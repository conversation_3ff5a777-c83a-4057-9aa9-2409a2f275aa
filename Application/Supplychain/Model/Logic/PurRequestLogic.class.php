<?php
/**
 * Created by 2023/5/17.
 * User: <PERSON><PERSON><PERSON>
 * Info: ...
 * Time: 下午5:17
 */

namespace Supplychain\Model\Logic;

use Illuminate\Support\Facades\Log;
use Supplychain\Model\OrderModel;
use Supplychain\Model\WmsTallyDetailModel;

class PurRequestLogic
{

    protected function requestPur($method,$data)
    {


        $data['type'] = 2;
        \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');


        $res = post_curl(PURCHASE_DOMAIN . $method, $data);
        \Think\Log::write(\GuzzleHttp\json_encode($res),'WARN');

        $res = json_decode($res, true);
        if ($res['code'] != 0){
            throw new \Exception($res['msg']);
        }
        return $res;
    }

    static public function sendMsg($requestJson)
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => self::getMsgHost()."/add_msg",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS =>$requestJson,
            CURLOPT_HTTPHEADER => [
                "content-type: application/json",
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            throw new \Exception("cURL Error #:" . $err);
        }

        $responseData = json_decode($response,true);
        if (!isset($responseData['code']) || intval($responseData['code']) !== 0){
            throw new \Exception('同步消息体系失败：'.$responseData['msg']);
        }
    }

    protected function requestPurGet($method,$data)
    {
        $data['type'] = 2;
        $res = get_curl(PURCHASE_DOMAIN . $method, $data);
        $res = json_decode($res, true);
        if ($res['code'] != 0){
            throw new \Exception($res['msg']);
        }
        return $res;
    }


    //:{
    //
    //"erp_scm_item_id": "7L/V0uncSDKLdhIrioWUV0rShQk=", //erp委托单明细id
    //
    //"erp_billid":"asdddddd",//erp产地税确认明细id (对应确认接口的id)
    //
    //"origin_tax_rate": "11",//产地税率
    //
    //"new_origin_tax_rate": "22",//最新产地税率
    //
    //"customs_plan": "213",//报关计划单
    //
    //"operator_uid": "1000",//处理人id
    //
    //"operator_name": "管理员",//处理人名称
    //
    //"origin_tax_amount": "213"//产地税金额.
    //"type":1,1-金蝶,2-供应链平台
    //}
    public function addOrigin($data)
    {
        //获取海关汇率
        $haiGuanRate = CommonLogic::getHaiGuanRate(OrderModel::where('order_id',$data['order_id'])->value('currency_id'));
        $originTaxAmount = $data['tally_num'] * $data['unit_price'] * ($data['origin_tax'] /100)* $haiGuanRate;
        $peerTariffAmount = $data['equality_customos'];


        $data = [
            'erp_scm_item_id' => $data['erp_entery_id'],
            'erp_billid' => $data['wstydl_id'],
            'origin_tax_rate' => $data['origin_tax'],
            'new_origin_tax_rate' => $data['origin_tax'],
            'origin_tax_amount' => $originTaxAmount,
            'customs_plan'=>$data['abnormal_batch'],
            'tally_num'=>$data['tally_num'],
            'commodity_inspection'=>$data['is_goods_check'],
            'customs_code'=>$data['tax_sn'],

            //单价X理货数量X当前海关汇率X34%
            'peer_tariff_amount'=>$peerTariffAmount,
            'origin'=>$data['origin'],

        ];




        $this->requestPur('/sync/gyl_scm_order/addPlaceOfOriginTax',$data);
    }


    public function deleteOrigin($data)
    {
        $data = [
            'erp_scm_item_id' => $data['erp_entery_id'],
            'erp_billid' => $data['wstydl_id'],
        ];

        $this->requestPur('/sync/gyl_scm_order/delPlaceOfOriginTax',$data);
    }


    public function getShipmentList($data)
    {
        $data = [
            'shipment_number' => $data['shipment_number'],
        ];

         return $this->requestPurGet('/open/scmOrder/getShipmentList',$data)['data']['list'];
    }


    static public function synWmsEventLog($requestJson)
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => self::getPurHost()."/sync/scmOrder/syncLog",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS =>$requestJson,
            CURLOPT_HTTPHEADER => [
                "content-type: application/json",
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            throw new \Exception("cURL Error #:" . $err);
        }

        $responseData = json_decode($response,true);
        if (!isset($responseData['code']) || intval($responseData['code']) !== 0){
//            throw new \Exception('同步wms操作日志至采购系统失败：'.$responseData['msg']);
        }
    }


    static public function syncAbnormalHandleData($requestJson)
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => self::getPurHost()."/sync/abnormalHandle/syncAbnormalHandleData",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS =>$requestJson,
            CURLOPT_HTTPHEADER => [
                "content-type: application/json",
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            throw new \Exception("cURL Error #:" . $err);
        }

        $responseData = json_decode($response,true);
        if (!isset($responseData['code']) || intval($responseData['code']) !== 0){
            throw new \Exception('请求新增采购同步异常管理失败：'.$responseData['msg']);
        }
    }

    static public function syncHandleResult($requestJson)
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => self::getPurHost()."/sync/abnormalHandle/syncHandleResult",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS =>$requestJson,
            CURLOPT_HTTPHEADER => [
                "content-type: application/json",
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            throw new \Exception("cURL Error #:" . $err);
        }

        $responseData = json_decode($response,true);
        if (!isset($responseData['code']) || intval($responseData['code']) !== 0){
            throw new \Exception('请求采购同步异常管理结果失败：'.$responseData['msg']);
        }
    }

    static public function getPurHost()
    {
        if ($_SERVER['SERVER_NAME'] == 'api.ichunt.com'){
            return 'http://purchase.ichunt.net';
        } else {
            return 'http://pur.liexindev.net';
        }
    }

    static public function getMsgHost()
    {
        if ($_SERVER['SERVER_NAME'] == 'api.ichunt.com'){
            return 'http://msg.ichunt.net';
        } else {
            return 'http://*************:16543';
        }
    }
}