<?php
/**
 * Created by PhpStorm.
 * User: 春风
 * Date: 2024/4/28
 * Time: 10:24
 */

namespace Supplychain\Model\Logic\WmsLogic;


use Supplychain\Model\ActionLogModel;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\CooMapModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\ErpPushLogic;
use Supplychain\Model\Logic\ErpRequestLogic;
use Supplychain\Model\Logic\PurRequestLogic;
use Supplychain\Model\Logic\WechatWmsLogic;
use Supplychain\Model\Logic\WmsRequestLogic;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\OrderTrackingLogModel;
use Supplychain\Model\WmsAbnormalManageModel;
use Supplychain\Model\WmsTallyAbnormalDetailModel;
use Supplychain\Model\WmsTallyDetailModel;
use Supplychain\Model\WmsTallyModel;
use Illuminate\Database\Capsule\Manager as DB;
use Think\Log;

class TallyLogic
{

    public function getBoxSn($requestData)
    {
        $model = (new WmsTallyModel());

        if (isset($requestData['box_sn']) && !empty($requestData['box_sn'])){
            if ($boxInfo = $model->where('box_sn',$requestData['box_sn'])->first()){
                return ['box_sn'=>$boxInfo['box_sn'],'wsty_id'=>$boxInfo['wsty_id']];
            }else{
                throw new \Exception('该箱号不存在');
            }
        }

        $boxSn = $model->getBoxSn();
        $insertId = $model->insertGetId([
            'box_sn'=>$boxSn,
            'tally_user_name'=>WechatWmsLogic::getCurrentName(),
            'create_time'=>time()
        ]);

        // 生成箱标签
        (new TallyTagLogic())->alterTallyTagBySubBoxQty([
            'sub_box_qty'=>1,
            'box_sn'=>$boxSn,
        ]);
        (new ActionLogModel())->addLog($insertId, '新增加了箱子', WechatWmsLogic::getCurrentName(), 7);

        return ['box_sn'=>$boxSn,'wsty_id'=>$insertId];
    }

    public function getTallyData($requestData)
    {
        //新增理货数据
        if (isset($requestData['erp_order_sn']) && !empty($requestData['erp_order_sn'])){
            $requestData['erp_order_sn'] = strtoupper($requestData['erp_order_sn']);
            $erpOrderSn = $requestData['erp_order_sn'];
            // 锁定前先判断入仓号是否存在风险
            if (!empty($erp_order_id = OrderModel::where('erp_order_sn', $erpOrderSn)->value('erp_order_id'))) {
                (new ErpPushLogic())->checkEntrustNo(['entrustId'=>$erp_order_id]);
            }
            //判断入仓号是否有采购单
            (new ErpPushLogic())->checkPaperTally(['entrustNo'=>$erpOrderSn]);

            $this->insertTallyData($erpOrderSn);
        }

        if (isset($requestData['erp_order_sn']) && $requestData['erp_order_sn'] == '') {
            $requestData['erp_order_sn'] = 'GGGGGGG';
        }

        $detail = WmsTallyDetailModel::getWhereObj($requestData)->orderBy('is_goods_check', 'desc')->get()->toArray();

        $allScan = $haveScan = 0;

        foreach ($detail as &$value){
            if ($value['tally_status'] === 3){
                $value['total_price'] = ($value['unit_price'] * $value['tally_num']);
            }else{
                $value['total_price'] = ($value['unit_price'] * $value['wait_tally_num']);
            }

            $value['sync_status_cn'] = array_get(WmsTallyDetailModel::$SYNC_STATUS_ALL,$value['sync_status'],'');
            $value['update_time_cn'] = CommonLogic::getDate($value['update_time']);


            $allScan++;
            if ($value['tally_status'] ===  3){
                $haveScan +=1;
            }

            $wstyInfo = WmsTallyModel::where('wsty_id',$value['wsty_id'])->first();
            if (empty($wstyInfo)){
                $value['box_tally_status_cn'] = '';
                $value['box_tally_status'] = 0;
            }else{
                $value['box_tally_status'] = $wstyInfo['tally_status'];
                $value['box_tally_status_cn'] =  WmsTallyModel::$tallyStatusCn[$wstyInfo['tally_status']];;
            }

            if (empty($wstyaldlInfo = WmsTallyAbnormalDetailModel::where('wstydl_id',$value['wstydl_id'])->first())){
                $value['abnormal_batch'] = '';
            }else{
                $value['abnormal_batch'] = $wstyaldlInfo['abnormal_batch'];
            }

            $value['declaration_type_val'] =  array_get(WmsTallyDetailModel::$DECLARATION_TYPE_ALL, $value['declaration_type'], '');
            $value['tally_time'] = ($value['is_base']==1)?'':CommonLogic::getDate($value['create_time']);
        }

        $returnData = [
            'create_time_cn'=>date('Y-m-d'),
            'goods_sn_num'=>$allScan,
            'box_sum'=>1,
            'tally_user_name'=>WechatWmsLogic::getCurrentName(),
            'have_scan_num'=>$haveScan,
            'no_scan_num'=>$allScan - $haveScan,
            'detail'=>$detail,
            'salesname'=>self::getsalesName($requestData)
        ];

        return $returnData;
    }



    public function getsalesName($data)
    {
        if (!isset($data['erp_order_sn'])) {
            return '暂无数据';
        }

        if (empty($salesName = OrderModel::where('erp_order_sn', $data['erp_order_sn'])
            ->where('salesman', '!=', '')->value('salesman'))) {
            return '暂无数据';
        }

        return $salesName . WechatWmsLogic::getDepartmentName($salesName);
    }

    public function getBoxData($requestData)
    {
        if (empty($requestData['limit'])){
            $requestData['limit'] = 10;
        }
        if (empty($requestData['page'])){
            $requestData['page'] = 1;
        }

        $list = new WmsTallyModel();
        $field = ['wt.box_sn','wt.tally_status','wt.create_time','wt.update_time','wt.update_user_name',
            'wt.tally_user_name','wt.box_type','wt.wsty_id','wt.gross_weight','wtdlaldl.abnormal_batch','wt.sub_box_qty', 'wtdl.declaration_type', 'wtdl.is_goods_check'];
        $list = $list->from('wms_tally as wt');
        $list = $list->leftJoin('wms_tally_detail as wtdl','wt.wsty_id','=','wtdl.wsty_id');
        $list = $list->leftJoin('wms_tally_abnormal_detail as wtdlaldl','wt.wsty_id','=','wtdlaldl.wsty_id')->select($field);

        if (isset($requestData['all_search']) && !empty($requestData['all_search'])){
            $list = $list->where(function($query) use ($requestData) {
                // 供应商名称
                $query->where('wt.box_sn', $requestData['all_search'])
                    ->orWhere('wtdl.goods_type', $requestData['all_search'])
                    ->orWhere('wtdl.erp_order_sn', $requestData['all_search'])
                    ->orWhere('wtdlaldl.abnormal_batch', $requestData['all_search']);

            });
        }
        if (isset($requestData['goods_type']) && !empty($requestData['goods_type'])){
            $list = $list->where('wtdl.goods_type', 'like',$requestData['goods_type'].'%');
        }
        $list = $list->where(function($query) use ($requestData) {

            if ($requestData['box_sn']) {
                $query->where('wt.box_sn', $requestData['box_sn']);
            }
        })->where(function($query) use ($requestData) {

            if ($requestData['erp_order_sn']) {
                $query->where('wtdl.erp_order_sn', $requestData['erp_order_sn']);
            }
        });
        $listData = $list->groupBy('wt.box_sn')->paginate($requestData['limit'], ['*'], 'p', $requestData['page'])->toArray();
        foreach ($listData['data'] as &$value){
            $wmsTallyDetail = WmsTallyDetailModel::where('wsty_id',$value['wsty_id'])->get()->toArray();
            $value['erp_order_sn_num'] = count(array_unique(array_column($wmsTallyDetail,'erp_order_sn')));
            $value['goods_type_num'] = count(array_column($wmsTallyDetail,'goods_type'));
            $value['net_weight_sum'] = array_sum(array_column($wmsTallyDetail,'net_weight'));
            $value['close_time_cn'] = CommonLogic::getDate($value['create_time']);
            $value['update_time_cn'] =CommonLogic::getDate($value['update_time']);
            $value['tally_status_cn'] = WmsTallyModel::$tallyStatusCn[$value['tally_status']];
            $value['box_type_cn'] = WmsTallyModel::$boxTypeCn[$value['box_type']];
        }
        return $listData;
    }


    public function insertTallyData($erpOrderSn)
    {
        if ((new WmsTallyDetailModel())->where('erp_order_sn',$erpOrderSn)->value('wstydl_id')){
            return [];
        }
        $orderInfo = (new OrderModel())->getOrderInfoByErpOrderSn($erpOrderSn);

        if (empty($orderInfo)){
            throw new \Exception('该入仓号不存在，请确认');
        }
        //如果没找到商务，默认周红丹
        if (empty($bussinessName = CustomerModel::where('company_id',$orderInfo['company_id'])->value('follow_people'))){
            $bussinessName = '周红丹';
        }
        $customerName = CompanyModel::where('company_id',$orderInfo['company_id'])->value('company_full_name');
        $orderGoodsData = (new OrderGoodsModel())->where('order_id',$orderInfo['order_id'])->where('status',5)->get()->toArray();
        $detail = [];
        foreach ($orderGoodsData as $value){
            $erpOtherParam = \GuzzleHttp\json_decode($value['erp_other_param'],true);
            if ($erpOtherParam['ISINSPORG']){
                $isGoodsCheck = 1;
            }else{
                $isGoodsCheck = 0;
            }
            $detail[] = [
                'material_sn'=>empty($value['material_sn'])?'':$value['material_sn'],
                'goods_title'=>$value['goods_title'],
                'goods_type'=>$value['goods_type'],
                'brand'=>$value['brand'],
                'unit_price'=>$value['unit_price'],
                'tax_sn'=>$value['tax_sn'],
                'tariff'=>$value['tariff'],
                'origin_tax'=>$value['origin_tax'],
                'is_goods_check'=>$isGoodsCheck,
                'order_goods_id'=>$value['order_goods_id'],
                'order_id'=>$value['order_id'],
                'customs_code'=>$value['customs_code'],
                'erp_order_sn'=>$erpOrderSn,
                'vat_rate'=>$value['vat_rate'],
                'tally_num'=>0,
                'origin'=>$value['origin'],
                'wait_tally_num'=>$value['numbers'],
                'order_numbers'=>$value['numbers'],
                'erp_entery_id'=>$value['erp_entery_id'],
                'erp_order_id'=>$orderInfo['erp_order_id'],
                'order_remark'=>$orderInfo['order_remark'],
                'salesman'=>$orderInfo['salesman'],
                'declaration_type'=>$value['declaration_type'],
                'customer_name'=>empty($customerName)?'':$customerName,
                'bussiness_name'=>$bussinessName,
                'currency'=> array_get(C("supply_currentcy_key"),$orderInfo['currency_id'],''),
                'unit'=>$value['measurement'],
                'regulatory_condition'=>$value['regulatory_condition'],
                'is_base'=>1,
            ];
        }
        WmsTallyDetailModel::insert($detail);
        return 0;
    }

    protected function checkIsCanTally($wmsTallyDetailInfo,$wmsTallyInfo, $wmsTallyAbnormalDetailInfo)
    {
        //判断箱子类型是否不一样
        $firstTallyDetailInfo = WmsTallyDetailModel::where('wsty_id',$wmsTallyInfo['wsty_id'])->first();
        if (!empty($firstTallyDetailInfo)){
            if (
                $firstTallyDetailInfo['declaration_type'] != $wmsTallyDetailInfo['declaration_type'] ||
                $firstTallyDetailInfo['is_goods_check'] != $wmsTallyDetailInfo['is_goods_check']
            ){
                throw new \Exception('不同类型的物料，不能放在一个地方');
            }
            if ($wmsTallyDetailInfo['origin'] != $firstTallyDetailInfo['origin']) {

                if (($wmsTallyDetailInfo['origin'] == '美国') || ($firstTallyDetailInfo['origin'] == '美国')){
                    throw new \Exception('美产物料不能和其他物料放在一个箱子里面');
                }
            }
            // add xiongzhi 20240729
            // 美产 需要增加限制
            // 0 无1.客户承担 2.公司承担 3.免美产排除编号 4.产地税率0
            $firstTallyAbnormalDetail = WmsTallyAbnormalDetailModel::where('box_sn',$wmsTallyInfo['box_sn'])->where('payer_type', '<>', 4)->first();
            // 先判断该异常型号是否是该箱子的第一个非产地税为0的型号,如果是第一个型号可有忽略校验
            if (!empty($firstTallyAbnormalDetail)) {
                $payer_type = $wmsTallyAbnormalDetailInfo['payer_type'];
                if ($payer_type == 1 || $payer_type == 2) {
                    // 客户承担，公司承担
                    if ($firstTallyAbnormalDetail['payer_type'] != $payer_type) {
                        throw new \Exception('只允许装美产确认类型为“'.($firstTallyAbnormalDetail['payer_type']==1?'客户承担':'公司承担').'”或产地税率为0的货物，本次货物无法装箱，请取新箱号装箱。');
                    }
                    // 判断入仓号全部相同,不相同需要判断金额
                    if(WmsTallyAbnormalDetailModel::where('box_sn',$wmsTallyInfo['box_sn'])
                            ->select('erp_order_sn')
                            ->distinct()
                            ->count() > 0 || $firstTallyAbnormalDetail['erp_order_sn'] != $firstTallyDetailInfo['erp_order_sn']) {
                        // 判断该箱子历史金额 产地税额+关税额 是否超50
                        $taxAmt = $this->getBoxDataTaxAmt($wmsTallyInfo['box_sn']);
                        if ($taxAmt <= 50){
                            $currentTaxAmt = $this->getCurrentTaxAmt($wmsTallyAbnormalDetailInfo['wstydl_id']);
                            if ($taxAmt + $currentTaxAmt > 50) {
                                throw new \Exception('已装箱货物税额与本次装箱货物税额合计需小于50人民币，本次货物无法装箱，请取新箱号装箱');
                            }
                        }
                    }
                } elseif ($payer_type == 3) {
                    // 免美产排除编号
                    if ($firstTallyAbnormalDetail['payer_type'] != $payer_type) {
                        throw new \Exception('只允许装免产地税编码一致或产地税率为0的货物，本次货物无法装箱，请取新箱号装箱');
                    }
                    $firstTallyDetailInfo = WmsTallyDetailModel::where('wstydl_id',$firstTallyAbnormalDetail['wstydl_id'])->first();
                    $tallyDetailInfo = WmsTallyDetailModel::where('wstydl_id',$wmsTallyAbnormalDetailInfo['wstydl_id'])->first();
                    if ($firstTallyDetailInfo['no_origin_tax_code'] != $tallyDetailInfo['no_origin_tax_code']) {
                        throw new \Exception('只允许装免产地税编码一致或产地税率为0的货物，本次货物无法装箱，请取新箱号装箱');
                    }
                }
            }
        }
    }

    protected function checkDeclarationType($wstydlInfo)
    {
        $firstTallyDetailInfo = WmsTallyDetailModel::where('wsty_id',$wstydlInfo['wsty_id'])->first();
        if (!empty($firstTallyDetailInfo)) {
            if (($wstydlInfo['declaration_type'] != $firstTallyDetailInfo['declaration_type'])){
                throw new \Exception('两步申报和非两步申报不能放在一个箱子里面');
            }
        }
    }

    public function submitTallyDetail($requestData)
    {
        //如果存在产地不同的明细，则新增一条记录，否则叠加
        $abnormalTallyNum = $tallyNum = $requestData['tally_num'];
        $time = time();
        $wmsTallyDetailInfo  = WmsTallyDetailModel::where('wstydl_id',$requestData['wstydl_id'])->first()->toArray();
        if (WmsAbnormalManageModel::where('order_goods_id', $wmsTallyDetailInfo['order_goods_id'])->whereIn('abnormal_status', [0, 1])->exists()) {
            throw new \Exception('该物料存在异常数据，请先处理异常');
        }
        CommonLogic::checkHeader($wmsTallyDetailInfo['customs_code'],$requestData['goods_check_pic']);

        //判断是否可以理货
        if (!empty($requestData['wsty_id'])) {
            $wmsTallyInfo = WmsTallyModel::where('wsty_id',$requestData['wsty_id'])->first();
            //TODO 根据入仓号明细判断
            if ($wmsTallyInfo['tally_status'] >= 2){
                throw new \Exception('该箱子已封箱，不能继续操作');
            }
        }
        $haveTallyNum = $tallyNum + WmsTallyDetailModel::getHaveTallyNum($wmsTallyDetailInfo['order_goods_id']);
        if ( $haveTallyNum > $wmsTallyDetailInfo['order_numbers']){
            throw new \Exception('理货数量大于订单数量，请确认');
        }
        $orderGoodsInfo = WmsTallyDetailModel::where('order_goods_id',$wmsTallyDetailInfo['order_goods_id'])
            ->where('is_base',1)->first();
        try{
            DB::connection('SUPPLYCHAIN')->beginTransaction();
            //理一条新增一条明细
            $insertData = $wmsTallyDetailInfo;unset($insertData['wstydl_id']);
            $insertData['tally_num'] = $tallyNum;
            $insertData['is_base'] = 0;
            $insertData['create_time'] = $time;
            $insertData['origin'] = $requestData['origin'];
            $insertData['wsty_id'] = empty($wmsTallyInfo['wsty_id'])?0:$wmsTallyInfo['wsty_id'];
            $insertData['box_sn'] = empty($wmsTallyInfo['box_sn'])?'':$wmsTallyInfo['box_sn'];
            $insertData['tally_status'] = 3;
            $insertData['goods_check_pic'] = $requestData['goods_check_pic'];
            $insertData['net_weight'] = $requestData['net_weight'];
            $insertData['remark'] = $requestData['remark'];
            $insertData['update_user'] = WechatWmsLogic::getCurrentName();
            $insertData['update_time'] = $time;
            $insertData['create_user'] = WechatWmsLogic::getCurrentName();
            $haiGuanRate = CommonLogic::getHaiGuanRate(OrderModel::where('order_id',$insertData['order_id'])->value('currency_id'));
            $insertData['customs_rate'] = $haiGuanRate;
            $origin_tax_money = $tallyNum * $insertData['unit_price'] * ($insertData['origin_tax'] /100)* $haiGuanRate;

            $peerTariffAmount = $this->getPeerTariffAmount($tallyNum,$insertData,$haiGuanRate);

            //$peerTariffAmount = ($tallyNum * $insertData['unit_price'] * $haiGuanRate) * CommonLogic::$US_TAX_RATE;
            $insertData['origin_tax_money'] = $origin_tax_money;
            $insertData['equality_customos'] = $peerTariffAmount;
            $tariff_amt = $tallyNum * $insertData['unit_price'] * ($insertData['tariff'] /100)* $haiGuanRate;
            $insertData['tariff_amt'] = $tariff_amt;
            $wstydlId = WmsTallyDetailModel::insertGetId($insertData);
            $updateData = [];
            $updateData['wait_tally_num'] = $orderGoodsInfo['order_numbers'] - $haveTallyNum;
            WmsTallyDetailModel::where('wstydl_id',$wmsTallyDetailInfo['wstydl_id'])->update($updateData);
            $this->updateStockInStatus($wmsTallyDetailInfo['erp_order_sn']);
            //异常产地新增
            $abnormalTitle = $this->addAbnormalDetail($wstydlId, $abnormalTallyNum, $wmsTallyInfo, $insertData);
            if (!empty($wmsTallyInfo['wsty_id'])) {
                WmsTallyModel::where('wsty_id',$requestData['wsty_id'])->update(['update_time'=>time(),'update_user_name'=>WechatWmsLogic::getCurrentName()]);
            }

            // 校验是否多箱混装
            if ($wmsTallyInfo['sub_box_qty'] > 1) {
                $count = WmsTallyDetailModel::where('box_sn', $wmsTallyInfo['box_sn'])
                    ->distinct('erp_order_sn')
                    ->count('erp_order_sn');
                if ($count > 1) {
                    throw new \Exception('该箱号有多个子箱，入仓号不一致不允许混装');
                }
            }
            DB::connection('SUPPLYCHAIN')->commit();
        }catch (\Exception $exception){
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($exception->getMessage());
        }
        return $abnormalTitle;
    }


    protected function getPeerTariffAmount($tallyNum,$insertData,$haiGuanRate)
    {
        $cdityBillData = [
            'GOODSNANME' => $insertData['goods_title'],
            'BRAND' => $insertData['brand'],
            'MODEL' => $insertData['goods_type'],
            'NUMBER' => $insertData['erp_order_sn'],
            'eccn' => null,
            'source' => 0,
            'CREATE_NAME' => 'super',
            'CREATE_ACCOUNT' => 'super',
            'ENTRUST_NAME' => '深圳市猎芯科技有限公司',
        ];



        $erp_res = ErpRequestLogic::getDockCodeData($cdityBillData,'createCdityBill');
        $res = $erp_res['data'];
        if ($res['code'] != 0) {
            throw new \Exception($res['msg']);
        }

        return  ($tallyNum * $insertData['unit_price'] * $haiGuanRate) * ($res['reciprocal_tariff']/100);


    }

    protected function updateStockInStatus($erpOrderSn)
    {
        $waitNum = WmsTallyDetailModel::where('erp_order_sn',$erpOrderSn)
            ->where('is_base',1)
            ->sum('wait_tally_num');

        $allNum = WmsTallyDetailModel::where('erp_order_sn',$erpOrderSn)
            ->where('is_base',1)
            ->sum('order_numbers');

        if($waitNum === 0){
            $stockInStatus = 2;
        }else{
            if ($waitNum === $allNum){
                $stockInStatus = 0;
            }else{
                $stockInStatus = 1;
            }
        }
        OrderModel::where('erp_order_sn',$erpOrderSn)
            ->where('status','>=',1)
            ->where('id_edit_order',0)->update(['stock_in_status'=>$stockInStatus]);
    }

    public function getBoxNumByDetailId($requestData)
    {
        $boxNum = WmsTallyDetailModel::from('wms_tally_detail as a')
            ->join('wms_tally as b', 'a.wsty_id', '=', 'b.wsty_id')
            ->where('a.wstydl_id', $requestData['wstydl_id'])
            ->first(['b.sub_box_qty as box_num']);
        return $boxNum['box_num'];
    }

    protected function updateStockInStatusWait($erpOrderSn)
    {
        OrderModel::where('erp_order_sn',$erpOrderSn)
            ->where('status','>=',1)
            ->where('id_edit_order',0)->update(['stock_in_status'=>0]);
    }

    protected function addAbnormalDetail($wstydlId,$tallyNum,$wmsTallyInfo, $insertData)
    {
        $wstydlInfo = WmsTallyDetailModel::where('wstydl_id',$wstydlId)->first();
        $wstydlInfo['abnormal_batch'] = WmsTallyAbnormalDetailModel::getAbnormalSn();
        $title = '';
        $time = time();
        $wstydlInfo['confirm_time'] = 0;
        $goodsCheckCn = $wstydlInfo['is_goods_check']==1?'是':'否';
        $isUsa = self::checkOriginSyncPur($wstydlInfo);
        if (($wstydlInfo['is_goods_check'] === 1)){
            $wstydlInfo['type'] = 2;
            $wstydlInfo['confirm_time'] = $time;
            $title = '产地：'.$wstydlInfo['origin'].' 商检：是  异常批号：'.$wstydlInfo['abnormal_batch'].'请放入商检区等待确认';
        }else if($isUsa){
            $wstydlInfo['type'] = 1;
            $title = '产地：美国（USA）商检：'.$goodsCheckCn.'  异常批号：'.$wstydlInfo['abnormal_batch'].'请放入美产区等待确认';
        }else{
            // 普货需要判断是否有箱号
            if (empty($wmsTallyInfo['wsty_id'])) {
                throw new \Exception('普通货物提交，请先获取箱号');
            }
            $this->checkDeclarationType($insertData);
            $content = '供应链仓库完成理货，型号：'.$wstydlInfo['goods_type'].'，品牌：'.$wstydlInfo['brand'].'，数量：'.$wstydlInfo['tally_num'].'，等待装箱。';
            OrderTrackingLogLogic::addLog([
                'log_type'=>OrderTrackingLogModel::log_type_03,
                'bill_type'=>OrderTrackingLogModel::orderDetail,
                'bill_id'=>$wstydlInfo['order_goods_id'],
                'content'=>$content,
                'create_user_id'=>WechatWmsLogic::getCurrentUserId(),
                'create_user_name'=>WechatWmsLogic::getNewCurrentName(),
                'ext_data'=>[
                    'goods_name'=>$wstydlInfo['goods_type'],
                    'brand_name'=>$wstydlInfo['brand'],
                    'qty'=>$wstydlInfo['tally_num']
                ]
            ]);
            return [];
        }

        //新增
        WmsTallyAbnormalDetailModel::insertGetId([
            'wstydl_id'=>$wstydlId,
            'erp_order_sn'=>$wstydlInfo['erp_order_sn'],
            'order_id'=>$wstydlInfo['order_id'],
            'create_time'=>$time,
            'confirm_time'=>$wstydlInfo['confirm_time'],
            'order_goods_id'=>$wstydlInfo['order_goods_id'],
            'goods_title'=>$wstydlInfo['goods_title'],
            'brand'=>$wstydlInfo['brand'],
            'goods_type'=>$wstydlInfo['goods_type'],
            'tally_num'=>$tallyNum,
            'is_goods_check'=>$wstydlInfo['is_goods_check'],
            'abnormal_batch'=>$wstydlInfo['abnormal_batch'],
            'type'=>$wstydlInfo['type'],
            'origin'=>$wstydlInfo['origin'],
            'goods_check_confirm_status'=>1,
            'origin_confirm_status'=>$isUsa?0:1
        ]);
        if($isUsa){
            (new PurRequestLogic())->addOrigin($wstydlInfo);
        }
        if ($wstydlInfo['is_goods_check']!=1 && $isUsa && !empty($wstydlInfo['origin_tax']))
        {
            /**  日志类型2（理货货物产地为美产且产地税率不为0）：
                操作时间，操作人
                内容：供应链仓库完成理货，型号：ASDKUIHQWIE，品牌：TE牌，数量：700，产地：美国，等待产地税确认。
             */
            $content = '供应链仓库完成理货，型号：'.$wstydlInfo['goods_type'].'，品牌：'.$wstydlInfo['brand'].'，数量：'.$wstydlInfo['tally_num'].'，产地：美国，等待产地税确认。';
            OrderTrackingLogLogic::addLog([
                'log_type'=>OrderTrackingLogModel::log_type_04,
                'bill_type'=>OrderTrackingLogModel::orderDetail,
                'bill_id'=>$wstydlInfo['order_goods_id'],
                'content'=>$content,
                'create_user_id'=>WechatWmsLogic::getCurrentUserId(),
                'create_user_name'=>WechatWmsLogic::getNewCurrentName(),
                'ext_data'=>[
                    'goods_name'=>$wstydlInfo['goods_type'],
                    'brand_name'=>$wstydlInfo['brand'],
                    'qty'=>$wstydlInfo['tally_num'],
                    'is_goods_check'=>0,
                    'coo'=>'美国'
                ]
            ]);
        } else if ($wstydlInfo['is_goods_check']!=1 && $isUsa && empty($wstydlInfo['origin_tax'])) {
            /**  日志类型2（理货货物产地为美产且产地税率为0）：
            操作时间，操作人
            内容：供应链仓库完成理货，型号：ASDKUIHQWIE，品牌：TE牌，数量：700，等待装箱。
             */
            $content = '供应链仓库完成理货，型号：'.$wstydlInfo['goods_type'].'，品牌：'.$wstydlInfo['brand'].'，数量：'.$wstydlInfo['tally_num'].'，等待装箱。';
            OrderTrackingLogLogic::addLog([
                'log_type'=>OrderTrackingLogModel::log_type_03,
                'bill_type'=>OrderTrackingLogModel::orderDetail,
                'bill_id'=>$wstydlInfo['order_goods_id'],
                'content'=>$content,
                'create_user_id'=>WechatWmsLogic::getCurrentUserId(),
                'create_user_name'=>WechatWmsLogic::getNewCurrentName(),
                'ext_data'=>[
                    'goods_name'=>$wstydlInfo['goods_type'],
                    'brand_name'=>$wstydlInfo['brand'],
                    'qty'=>$wstydlInfo['tally_num'],
                    'is_goods_check'=>0,
                ]
            ]);
        } else if ($wstydlInfo['is_goods_check']==1 && (!$isUsa || ($isUsa && empty($wstydlInfo['origin_tax'])))) {
            /**
             * 日志类型3（仅商检，或者商检且美产，产地税率为0）：
             * 操作时间，操作人
             * 内容：供应链仓库完成理货，型号：ASDKUIHQWIE，品牌：TE牌，数量：700，商检，等待装箱。
             */
            $content = '供应链仓库完成理货，型号：'.$wstydlInfo['goods_type'].'，品牌：'.$wstydlInfo['brand'].'，数量：'.$wstydlInfo['tally_num'].'，商检，等待装箱。';
            OrderTrackingLogLogic::addLog([
                'log_type'=>OrderTrackingLogModel::log_type_03,
                'bill_type'=>OrderTrackingLogModel::orderDetail,
                'bill_id'=>$wstydlInfo['order_goods_id'],
                'content'=>$content,
                'create_user_id'=>WechatWmsLogic::getCurrentUserId(),
                'create_user_name'=>WechatWmsLogic::getNewCurrentName(),
                'ext_data'=>[
                    'goods_name'=>$wstydlInfo['goods_type'],
                    'brand_name'=>$wstydlInfo['brand'],
                    'qty'=>$wstydlInfo['tally_num'],
                    'is_goods_check'=>1,
                ]
            ]);
        } else if ($wstydlInfo['is_goods_check']==1 && $isUsa && !empty($wstydlInfo['origin_tax'])) {
            /**
             * 日志类型4（商检且美产，且产地税率不为0）：
             * 操作时间，操作人
             * 内容：供应链仓库完成理货，型号：ASDKUIHQWIE，品牌：TE牌，数量：700，商检，产地：美国，等待产地税确认。
             */
            $content = '供应链仓库完成理货，型号：'.$wstydlInfo['goods_type'].'，品牌：'.$wstydlInfo['brand'].'，数量：'.$wstydlInfo['tally_num'].'，商检，产地：美国，等待产地税确认。';
            OrderTrackingLogLogic::addLog([
                'log_type'=>OrderTrackingLogModel::log_type_04,
                'bill_type'=>OrderTrackingLogModel::orderDetail,
                'bill_id'=>$wstydlInfo['order_goods_id'],
                'content'=>$content,
                'create_user_id'=>WechatWmsLogic::getCurrentUserId(),
                'create_user_name'=>WechatWmsLogic::getNewCurrentName(),
                'ext_data'=>[
                    'goods_name'=>$wstydlInfo['goods_type'],
                    'brand_name'=>$wstydlInfo['brand'],
                    'qty'=>$wstydlInfo['tally_num'],
                    'is_goods_check'=>1,
                    'coo'=>'美国'
                ]
            ]);
        }
        if ($wstydlInfo['type'] != 3){
            (new WmsRequestLogic())->printLabel([
                'abnormal_batch' => $wstydlInfo['abnormal_batch'],
                'erp_order_sn' => $wstydlInfo['erp_order_sn'],
                'is_usa' => $isUsa?1:0,
                'is_goods_check' => $wstydlInfo['is_goods_check'],
                'goods_type' => $wstydlInfo['goods_type'],
                'brand'=>$wstydlInfo['brand'],
                'tally_num'=>$wstydlInfo['tally_num'],
            ]);
        }
        WmsTallyDetailModel::where('wstydl_id',$wstydlId)->update([
            'wsty_id' => 0,
            'box_sn' => '',
        ]);
        return $title;
    }

    static public function checkOriginSyncPur($requestData)
    {
        //如果产地是美国要进行产地税确认
        if (mb_strpos($requestData['origin'], '美国') !== false) {
            return true;
        }
        return false;
    }

    static public function checkOrigin($requestData)
    {
        //如果产地是美国要进行产地税确认
        if (mb_strpos($requestData['origin'], '美国') !== false) {
            return true;
        }
        return false;
    }

    public function cancelTallyDetail($requestData)
    {
        $wmsTallyDetailInfo  = WmsTallyDetailModel::where('wstydl_id',$requestData['wstydl_id'])->first();
        //判断是否可以理货
        $wmsTallyInfo = WmsTallyModel::where('wsty_id',$wmsTallyDetailInfo['wsty_id'])->first();
        if ($wmsTallyInfo['tally_status'] >= 2){
            if ($wmsTallyDetailInfo['sync_status'] === 2 || $wmsTallyDetailInfo['sync_status'] === 3){
                throw new \Exception('该物料已推送金蝶，请联系关务取消推送再进行取消理货');
            }
        }
        if ($wmsTallyInfo['print_status'] == 1) {
            throw new \Exception('该箱号关务已打印纸质单，如需取消理货，请联系关务取消打印。');
        }

        try{
            DB::connection('SUPPLYCHAIN')->commit();
            //如果存在毛重且不为空，且存在推送成功数据，就请求金蝶
            if (isset($requestData['gross_weight']) && !empty($requestData['gross_weight'])){
                if (WmsTallyDetailModel::where('wsty_id',$wmsTallyInfo['wsty_id'])->where('sync_status',3)->value('wsty_id')){
                    // function synKJTallyData
                    ErpRequestLogic::getCodeData([
                        'status'=>2,
                        'datas'=>[
                            [
                                'ctnNo'=>$wmsTallyInfo['box_sn'],
                                'num'=>0,
                                'palletsNum'=>0,
                                'grossWeight'=>$requestData['gross_weight'],
                                'removeOrderLots'=>[$wmsTallyDetailInfo['erp_order_sn']],
                            ]
                        ]
                    ],'synKJTallyData');
                }
            }

            $this->updateStockInStatus($wmsTallyDetailInfo['erp_order_sn']);
            // 合计取消数量
            $cancelSumQty = 0;
            // 判断是否有多箱
            if (!empty($wmsTallyInfo['box_sn']) && $wmsTallyInfo['sub_box_qty'] > 1) {
                // 有多箱子，多箱的货 要全部取消
                // 获取箱全部理货明细记录
                $wmsTallyDetailInfos  = WmsTallyDetailModel::where('wsty_id',$wmsTallyInfo['wsty_id'])->get()->toArray();
                foreach ($wmsTallyDetailInfos as $wmsTallyDetailInfo){
                    $orderGoodsInfo = WmsTallyDetailModel::where('order_goods_id',$wmsTallyDetailInfo['order_goods_id'])->where('is_base',1)->first();
                    $cancelSumQty += $wmsTallyDetailInfo['tally_num'];
                    WmsTallyDetailModel::where('wstydl_id',$orderGoodsInfo['wstydl_id'])->update([
                        'wait_tally_num'=>$orderGoodsInfo['wait_tally_num'] + $wmsTallyDetailInfo['tally_num']
                    ]);
                    //如果是有美产异常，则同步取消 deleteOrigin
                    if (self::checkOrigin($orderGoodsInfo)){
                        (new PurRequestLogic())->deleteOrigin($orderGoodsInfo);
                    }
                }
                WmsTallyModel::where('wsty_id',$wmsTallyInfo['wsty_id'])->update([
                    'gross_weight'=>0,
                    'update_user_name'=>WechatWmsLogic::getNewCurrentName(),
                    'update_time'=>time(),
                ]);
                WmsTallyDetailModel::where('wsty_id',$wmsTallyInfo['wsty_id'])->delete();
                WmsTallyAbnormalDetailModel::where('wsty_id',$wmsTallyInfo['wsty_id'])->delete();
                $this->updateBoxStatus($wmsTallyInfo['wsty_id']);
            } else {
                $orderGoodsInfo = WmsTallyDetailModel::where('order_goods_id',$wmsTallyDetailInfo['order_goods_id'])->where('is_base',1)
                    ->first();
                $cancelSumQty = $wmsTallyDetailInfo['tally_num'];
                WmsTallyDetailModel::where('wstydl_id',$orderGoodsInfo['wstydl_id'])->update([
                    'wait_tally_num'=>$orderGoodsInfo['wait_tally_num'] + $wmsTallyDetailInfo['tally_num']
                ]);
                WmsTallyModel::where('wsty_id',$wmsTallyDetailInfo['wsty_id'])->update([
                    'gross_weight'=>$requestData['gross_weight'],
                    'update_user_name'=>WechatWmsLogic::getNewCurrentName(),
                    'update_time'=>time(),
                ]);
                WmsTallyDetailModel::where('wstydl_id',$requestData['wstydl_id'])->delete();
                WmsTallyAbnormalDetailModel::where('wstydl_id',$requestData['wstydl_id'])->delete();

                //如果是有美产异常，则同步取消 deleteOrigin
                if (self::checkOrigin($wmsTallyDetailInfo)){
                    (new PurRequestLogic())->deleteOrigin($wmsTallyDetailInfo);
                }

                $this->updateBoxStatus($wmsTallyDetailInfo['wsty_id']);
            }
            if (!empty($wmsTallyInfo['box_sn'])) {
                (new TallyTagLogic())->alterTallyTagBySubBoxQty([
                    'sub_box_qty'=>1,
                    'box_sn'=>$wmsTallyInfo['box_sn'],
                ]);
            }

            // 取消理货明细日志
            /**
             * 触发动作：新流程【取消理货】成功
             * 日志：
             * 操作时间，操作人
             * 内容：供应链仓库取消理货，型号：ASDKUIHQWIE，品牌：TE牌，数量：700。
             */
            $content = '供应链仓库取消理货，型号：'.$wmsTallyDetailInfo['goods_type'].'，品牌：'.$wmsTallyDetailInfo['brand'].'，数量：'.$cancelSumQty.'。';
            OrderTrackingLogLogic::addLog([
                'log_type'=>OrderTrackingLogModel::log_type_05,
                'bill_type'=>OrderTrackingLogModel::orderDetail,
                'bill_id'=>$wmsTallyDetailInfo['order_goods_id'],
                'content'=>$content,
                'create_user_id'=>WechatWmsLogic::getCurrentUserId(),
                'create_user_name'=>WechatWmsLogic::getNewCurrentName(),
                'ext_data'=>[
                    'goods_name'=>$wmsTallyDetailInfo['goods_type'],
                    'brand_name'=>$wmsTallyDetailInfo['brand'],
                    'qty'=>$cancelSumQty,
                ]
            ]);
            DB::connection('SUPPLYCHAIN')->commit();
        }catch (\Exception $exception){
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($exception->getMessage());
        }
        return [];
    }

    public function cancelRelease($requestData)
    {
        if (WmsTallyDetailModel::where('erp_order_sn',$requestData['erp_order_sn'])->where('is_base',0)->value('wstydl_id')){
            throw new \Exception('该入仓号存在已理货的明细，不能继续操作');
        }

        $this->updateStockInStatusWait($requestData['erp_order_sn']);
        WmsTallyDetailModel::where('erp_order_sn',$requestData['erp_order_sn'])->delete();
        WmsTallyAbnormalDetailModel::where('erp_order_sn',$requestData['erp_order_sn'])->delete();
        return [];
    }

    public function getBoxStatus($requestData)
    {
        if (!isset($requestData['box_sn']) || empty($requestData['box_sn'])){
            throw new \Exception('箱号不能为空');
        }
        $wmsTallyInfo = WmsTallyModel::where('box_sn',trim($requestData['box_sn']))->first();
        return $wmsTallyInfo['tally_status'];
    }

    public function closeBox($requestData)
    {
        $model = new WmsTallyModel();

        if (!isset($requestData['box_sn']) || empty($requestData['box_sn'])){
            throw new \Exception('箱号不能为空');
        }

        //判断是否可以理货
        $wmsTallyInfo = WmsTallyModel::where('box_sn',trim($requestData['box_sn']))->first();
        if ($wmsTallyInfo['tally_status'] !== 1){
            throw new \Exception('该箱子不处在待理货状态，不能操作封箱');
        }

        if (!WmsTallyDetailModel::where('wsty_id',$wmsTallyInfo['wsty_id'])->value('wsty_id')){
            throw new \Exception('空箱子不能封箱');
        }

        if (WmsTallyDetailModel::where('wsty_id',$wmsTallyInfo['wsty_id'])->sum('net_weight') > $requestData['gross_weight']){
            throw new \Exception('毛重不能小于总净重');
        }
        if ($wmsTallyInfo['sub_box_qty'] > 1) {
            $count = WmsTallyDetailModel::where('box_sn', $wmsTallyInfo['box_sn'])
                ->distinct('erp_order_sn')
                ->count('erp_order_sn');
            if ($count > 1) {
                throw new \Exception('该箱号有多个子箱，入仓号不一致不允许混装');
            }
        }
        if (empty($boxType = WmsTallyAbnormalDetailModel::where('wsty_id',$wmsTallyInfo['wsty_id'])->value('type'))){
            $boxType = 0;
        }

        $model->where('wsty_id',$wmsTallyInfo['wsty_id'])->where('tally_status',1)->update([
            'tally_status'=>2,
            'gross_weight'=>$requestData['gross_weight'],
            'close_box_user'=>WechatWmsLogic::getCurrentName(),
            'close_box_time'=>time(),
            'update_time'=>time(),
            'box_type'=>$boxType,
            'update_user_name'=>WechatWmsLogic::getCurrentName(),
            'erp_order_sn_num'=>count(WmsTallyDetailModel::where('wsty_id',$wmsTallyInfo['wsty_id'])->groupBy('erp_order_sn')->pluck('wstydl_id')->toArray())
        ]);
        WmsTallyDetailModel::where('wsty_id',$wmsTallyInfo['wsty_id'])->where('tally_status',1)->update([
            'tally_status'=>2
        ]);

        (new ActionLogModel())->addLog($wmsTallyInfo['wsty_id'], '进行了封箱操作', WechatWmsLogic::getCurrentName(), 7);

        /**
         * 封箱完成
         * 触发动作：新流程【封箱】成功
         * 日志：
         * 操作时间，操作人
         * 内容：供应链仓库完成封箱，箱号：********，等待报关。
         */
        $order_goods_ids = WmsTallyDetailModel::where('box_sn', $wmsTallyInfo['box_sn'])->groupBy(['order_goods_id'])->pluck('order_goods_id')->toArray();
        foreach ($order_goods_ids as $order_goods_id){
            $content = '供应链仓库完成封箱，箱号：'.$wmsTallyInfo['box_sn'].'，等待报关。';
            OrderTrackingLogLogic::addLog([
                'log_type'=>OrderTrackingLogModel::log_type_07,
                'bill_type'=>OrderTrackingLogModel::orderDetail,
                'bill_id'=>$order_goods_id,
                'content'=>$content,
                'create_user_id'=>WechatWmsLogic::getCurrentUserId(),
                'create_user_name'=>WechatWmsLogic::getNewCurrentName(),
                'ext_data'=>[
                    'box_code'=>$wmsTallyInfo['box_sn'],
                ]
            ]);
        }
        return [];
    }

    public function submitAbnormalBox($requestData)
    {
        try{
            DB::connection('SUPPLYCHAIN')->commit();

            $where = [
                'wstydl_id'=>$requestData['wstydl_id']
            ];

            $wmsTallyAbnormalDetailInfo = WmsTallyAbnormalDetailModel::where($where)->first();

            if (empty($requestData['wsty_id'])){
                if (empty($requestData['wsty_id'] = $wmsTallyAbnormalDetailInfo['wsty_id'])){
                    throw new \Exception('箱号ID不能为空');
                }
            }
            $bosInfo = WmsTallyModel::where('wsty_id',$requestData['wsty_id'])->first();
            $wmsTallyInfo = WmsTallyModel::where('wsty_id',$requestData['wsty_id'])->first();
            if (intval($wmsTallyInfo['tally_status']) === 3){
                throw new \Exception('该箱已装板，请先取消装板');
            }
            if($requestData['type'] == 0){
                //判断是否可以理货
                $wmsTallyDetailInfo = WmsTallyDetailModel::where('wstydl_id',$requestData['wstydl_id'])->first();
                if (intval($wmsTallyDetailInfo['sync_status']) === 2 || intval($wmsTallyDetailInfo['sync_status']) === 3){
                    throw new \Exception('该物料已推送金蝶，请联系关务取消推送再进行取出');
                }
                if ($wmsTallyInfo['print_status'] == 1) {
                    throw new \Exception('该箱号关务已打印纸质单，如需取出，请联系关务取消打印。');
                }
                WmsTallyAbnormalDetailModel::where($where)->update([
                    'wsty_id'=>0,
                    'box_sn'=>'',
                    'pack_time'=>0,
                    'confirm_time'=>0,
                ]);
                WmsTallyDetailModel::where($where)->update([
                    'wsty_id'=>0,
                    'box_sn'=>'',
                ]);
                $this->updateBoxStatus($wmsTallyDetailInfo['wsty_id']);
            }else{
                //判断是否可以理货
                if ($wmsTallyInfo['tally_status'] !== 1){
                    throw new \Exception('该箱子不处在待理货状态，不能操作封箱');
                }
                if ($wmsTallyAbnormalDetailInfo['type'] == 1 && ($wmsTallyAbnormalDetailInfo['origin_confirm_status'] != 1)){
                    throw new \Exception('该物料美国产地税未确认，请先确认');
                }
                if ($wmsTallyInfo['sub_box_qty'] > 1) {
                    if (WmsTallyDetailModel::where('box_sn',$bosInfo['box_sn'])->where('erp_order_sn', '!=', $wmsTallyAbnormalDetailInfo['erp_order_sn'])->exists()) {
                        throw new \Exception('该箱号有多个子箱，入仓号不一致不允许混装');
                    }
                }
                $this->checkIsCanTally(WmsTallyDetailModel::where($where)->first(),$bosInfo, $wmsTallyAbnormalDetailInfo);
                WmsTallyAbnormalDetailModel::where($where)->update([
                    'wsty_id'=>$bosInfo['wsty_id'],
                    'box_sn'=>$bosInfo['box_sn'],
                    'pack_time'=>time(),
                ]);
                WmsTallyDetailModel::where($where)->update([
                    'wsty_id'=>$bosInfo['wsty_id'],
                    'box_sn'=>$bosInfo['box_sn'],
                ]);
                WmsTallyModel::where('wsty_id', $bosInfo['wsty_id'])->update(['update_time'=>time()]);
            }
            DB::connection('SUPPLYCHAIN')->commit();
        }catch (\Exception $exception){
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($exception->getMessage());
        }
    }

    public function getAbnormalTallyDetail($requestData)
    {
        if($requestData['type'] == 0){
            $requestData['type'] = '';
            $requestData['box_sn_not'] = true;
        }
        //已装箱
        if($requestData['type'] == 3){
            $requestData['box_sn_not'] = false;
            unset($requestData['type']);
        }else{
            $requestData['box_sn_not'] = true;
        }
        $abnormalData = WmsTallyAbnormalDetailModel::getWhereObj($requestData)->get()->toArray();
        foreach ($abnormalData as &$value){
            $wstydlInfo = WmsTallyDetailModel::where('wstydl_id',$value['wstydl_id'])->first();
            $wstyInfo = WmsTallyModel::where('wsty_id',$value['wsty_id'])->first();

            $value['is_goods_check_cn'] = empty($value['is_goods_check'])?'否':'是';
            $value['confirm_time_cn'] = empty($value['confirm_time'])?'':date('Y-m-d H:i',$value['confirm_time']);
            $value['pack_time'] = empty($value['pack_time'])?'':date('Y-m-d H:i',$value['pack_time']);
            $value['sync_status_cn'] = WmsTallyDetailModel::$SYNC_STATUS_ALL[$wstydlInfo['sync_status']];
            $value['sync_status'] = $wstydlInfo['sync_status'];
            $value['update_user'] = $wstydlInfo['update_user'];
            $value['update_time'] = CommonLogic::getDate($wstydlInfo['update_time']);
            if (empty($wstyInfo)){
                $value['box_tally_status_cn'] = '';
                $value['box_tally_status'] = 0;
            }else{
                $value['box_tally_status'] = $wstyInfo['tally_status'];
                $value['box_tally_status_cn'] =  WmsTallyModel::$tallyStatusCn[$wstyInfo['tally_status']];;
            }
        }
        $where = ['confirm_time'=>$requestData['confirm_time']];
        $usaArr  = WmsTallyAbnormalDetailModel::getWhereObj(array_merge(['type'=>1,'box_sn_not'=>true],$where))->pluck('origin_confirm_status') ;
        $haveConfirmUsaNum = 0;
        foreach ($usaArr as $status){
            if ($status === 1){
                $haveConfirmUsaNum ++;
            }
        }
        $returnData = [
            'usa_num'=>count($usaArr),
            'goods_check_num'=>WmsTallyAbnormalDetailModel::getWhereObj(array_merge(['type'=>2,'box_sn_not'=>true,'origin_confirm_status'=>1],$where))->count('wstyaldl_id'),
            'have_close_box_num'=>WmsTallyAbnormalDetailModel::getWhereObj(array_merge(['box_sn_not'=>false],['pack_time'=>$requestData['pack_time']]))->count('wstyaldl_id'),
            'detail'=>$abnormalData
        ];

        $returnData['confirm_usa_num'] = $haveConfirmUsaNum;
        $returnData['confirm_goods_check_num'] = $returnData['goods_check_num'];
        $returnData['confirm_all_num'] = $returnData['confirm_usa_num'] + $returnData['confirm_goods_check_num'];
        return $returnData;
    }

    public function fixBox($requestData)
    {
        $boxSnArr = array_filter(explode(',',$requestData['old_box_sn_str']));

        if ($canNotBoxSn = WmsTallyModel::whereIn('box_sn',$boxSnArr)->where('tally_status','>',1)->value('box_sn')){
            throw new \Exception($canNotBoxSn.'已封箱，不能合箱');
        }

        if (WmsTallyModel::whereIn('box_sn',$boxSnArr)->where('sub_box_qty','>',1)->exists()){
            throw new \Exception($canNotBoxSn.'该箱号有多个子箱，不允许合箱');
        }

        if ($canNotErpOrderSn = WmsTallyDetailModel::whereIn('box_sn',$boxSnArr)->whereIn('sync_status',[2,3])->value('erp_order_sn')){
            throw new \Exception($canNotErpOrderSn.'存在已推送的数据，不能合箱');
        }

        $declarationTypeNum = count( WmsTallyDetailModel::whereIn('box_sn',$boxSnArr)->groupBy('declaration_type')->pluck('wstydl_id')->toArray());
        $goodsCheckNum = count(WmsTallyDetailModel::whereIn('box_sn',$boxSnArr)->groupBy('is_goods_check')->pluck('wstydl_id')->toArray());
        $originArr = WmsTallyDetailModel::whereIn('box_sn',$boxSnArr)->groupBy('origin')->pluck('origin')->toArray();


        if ($declarationTypeNum >= 2 || $goodsCheckNum>=2){
            throw new \Exception('不同箱子类型不能一起合箱');
        }

        if (count($originArr) >= 2){

            foreach ($originArr as $origin){


                //如果产地是美国要进行产地税确认
                if (mb_strpos($origin, '美国') !== false) {
                    throw new \Exception('不同箱子类型不能一起合箱');
                }
            }
        }

        $boxSnData = $this->getBoxSn([]);
        WmsTallyDetailModel::whereIn('box_sn',$boxSnArr)->update([
            'wsty_id'=>$boxSnData['wsty_id'],
            'box_sn'=>$boxSnData['box_sn'],
        ]);
        WmsTallyAbnormalDetailModel::whereIn('box_sn',$boxSnArr)->update([
            'wsty_id'=>$boxSnData['wsty_id'],
            'box_sn'=>$boxSnData['box_sn'],
        ]);
        return $boxSnData;
    }

    public function reOpenBox($requestData)
    {
        $model = new WmsTallyModel();

        $wmsInfo = $model->where('box_sn',trim($requestData['box_sn']))->first();

        if (empty($wmsInfo)){
            throw new \Exception('该箱子无效');
        }

        if ($wmsInfo['print_status'] == 1) {
            throw new \Exception('该箱号关务已打印纸质单，如需开箱，请联系关务取消打印。');
        }
        //判断是否可以理货
        $wmsTallyInfo = $model->where('box_sn',trim($requestData['box_sn']))->whereIn('tally_status',[2])->first();
        if (!empty($wmsTallyInfo)) {
            if (WmsTallyDetailModel::where('box_sn',trim($requestData['box_sn']))->whereNotIn('sync_status',[1,4])->value('wstydl_id')){
                throw new \Exception('重新开箱需满足箱状态为“已封箱”，且箱中物料明细均未推送或推送失败。');
            }
        }else{
            throw new \Exception('该箱子不在已封箱状态，请确认');
        }

        $model->where('box_sn',trim($requestData['box_sn']))->update([
            'tally_status'=>1,
            'close_box_time'=>0,
            'close_box_user'=>''
        ]);

        (new ActionLogModel())->addLog($wmsInfo['wsty_id'], '进行了重新开箱操作', WechatWmsLogic::getCurrentName(), 7);
        $detailInfos = WmsTallyDetailModel::where('box_sn',trim($requestData['box_sn']))
            ->select(['order_goods_id'])
            ->get()
            ->toArray();
        if (!empty($detailInfos)) {
            $content = '供应链仓库进行了重新开箱。';
            foreach ($detailInfos as $detailInfo) {
                OrderTrackingLogLogic::addLog([
                    'log_type'=>OrderTrackingLogModel::log_type_09,
                    'bill_type'=>OrderTrackingLogModel::orderDetail,
                    'bill_id'=>$detailInfo['order_goods_id'],
                    'content'=>$content,
                    'create_user_id'=>WechatWmsLogic::getCurrentUserId(),
                    'create_user_name'=>WechatWmsLogic::getNewCurrentName()
                ]);
            }
        }

        return [];
    }


    public function scanQrCode($data)
    {
        CommonLogic::logsw(\GuzzleHttp\json_encode($data),__FUNCTION__,Log::WARN);


        $res = post_curl('queue.ichunt.net/analyzeQRCode',\GuzzleHttp\json_encode(['type' => $data['type'], 'data' => $data['data']]));

        CommonLogic::logsw($res,__FUNCTION__,Log::WARN);


        $res = json_decode($res, true);

        if (intval($res['code']) !== 0) {
            throw new \Exception($res['msg']);
        }
        return $res['data'];
    }

    public function getOriginCn($requestData)
    {
        if (isset($requestData['coo']) && !empty($requestData['coo'])){
            $allData = CooMapModel::where('country_cn',$requestData['coo'])
                ->orWhere('coo_en',$requestData['coo'])
                ->orWhere('coo_short_en',$requestData['coo'])
                ->get()->toArray();
        }else{
            $allData = CooMapModel::get()->toArray();
        }

        $returnData = [];
        foreach ($allData as $value){
            $returnData[] = ['name'=>$value['country_cn'].'|'.$value['coo_en'].'|'.$value['coo_short_en'],'value'=>$value['country_cn']];
        }
        return  $returnData;
    }

    /**
     * 获取入仓号对应的记录数
     * @param $requestData
     * @return array
     */
    public function getTallyList($requestData)
    {
        $returnData = [];

        if (empty($requestData['erp_order_sn'])){
            return $returnData;
        }

        $searchData = OrderModel::getOrderDataByErpOrderSn($requestData['erp_order_sn']);

        if (count($searchData) < 2){
            return $returnData;
        }

        $orderGoodsModel =  new OrderGoodsModel();
        foreach ($searchData as $key=>$value){
            $returnData[$key]['erp_order_sn'] = $value['erp_order_sn'];
            $returnData[$key]['order_goods_num'] = $orderGoodsModel->where('order_id',$value['order_id'])->where('status',5)->count('order_goods_id');
            $returnData[$key]['stock_in_status_cn'] = OrderModel::$stockInStatus[$value['stock_in_status']];
        }

        return $returnData;
    }


    protected function updateBoxStatus($wstyId)
    {
        if (empty(WmsTallyDetailModel::where('wsty_id',$wstyId)->value('wsty_id'))){
            WmsTallyModel::where('wsty_id',$wstyId)->update(['tally_status'=>1,'update_user_name'=>WechatWmsLogic::getCurrentName()]);
        }
    }

    // 修改理货净重
    public function updateTallyNetWeight($params) {
        $wstydl_id = $params['wstydl_id'];
        // 判断是否装箱
        if (!empty($wsty_id = WmsTallyDetailModel::where('wstydl_id',$wstydl_id)->value('wsty_id'))){
            if (WmsTallyModel::where('wsty_id', $wsty_id)->whereIn('tally_status', [2, 3])->exists()) {
                throw new \Exception("已封箱或已装板状态的记录不可以修改净重");
            }
        }
        $old_net_weight = WmsTallyDetailModel::where('wstydl_id',$wstydl_id)->value('net_weight');
        if ($old_net_weight == $params['net_weight']) {
            return 0;
        }
        WmsTallyDetailModel::where('wstydl_id',$wstydl_id)->update([
            'net_weight'=>$params['net_weight'],
            'old_net_weight'=>$old_net_weight
        ]);
        return 0;
    }

    // 补打标签
    public function reTagPrint($params)
    {
        $wstydl_ids = explode(',', $params['wstydl_id']);
        // 只能补打商检和美产的标签
        $wstydlInfos = WmsTallyAbnormalDetailModel::whereIn('wstydl_id', $wstydl_ids)->get()->toArray();
        if (count($wstydl_ids) != count($wstydlInfos)){
            throw new \Exception("普货类型货物不能补打标签");
        }
        foreach ($wstydlInfos as $wstydlInfo){
            (new WmsRequestLogic())->printLabel([
                'abnormal_batch' => $wstydlInfo['abnormal_batch'],
                'erp_order_sn' => $wstydlInfo['erp_order_sn'],
                'is_usa' => $wstydlInfo['type']== 1?1:0,
                'is_goods_check' => $wstydlInfo['is_goods_check'],
                'goods_type' => $wstydlInfo['goods_type'],
                'brand'=>$wstydlInfo['brand'],
                'tally_num'=>$wstydlInfo['tally_num'],
            ]);
        }
        return 0;
    }

    // 发送滞留数据消息
    public function sendRetentionDataMsg() {
        $params = ['tally_type' => 0, 'limit' => 100];
        $retentionInfo = (new AbnormalManageLogic())->getStayHandleCount();
        $unBoxedList = $this->getUnBoxed($params);
        $title = '滞留货物预警通知，截止 '.date('Y-m-d H:i:s', time()).' 共'.$retentionInfo['retentionQty'].'条';
        $content = "消息内容：<br>";
        if (!empty($unBoxedList['list'])){
            $content .= "已理货未装箱 ".count($unBoxedList['list'])." 条：<br>";
            foreach ($unBoxedList['list'] as $unBoxed){
                // 商检，B00001，型号：XXXXX，品牌：XXX，数量：XXX，理货人：XXX，理货时间：2024-01-01  12：00：00
                $content .= ($unBoxed['is_goods_check'])?"商检":"美产"."，".$unBoxed['erp_order_sn']." 型号：".$unBoxed['model']."，品牌：".$unBoxed['brand'].
                    "，数量：".$unBoxed['qty']."，理货人：".$unBoxed['tally_user']."，理货时间：".$unBoxed['tally_time']." <br>";
            }
        }
        $unCloseList = $this->getUnClose($params);
        if (!empty($unCloseList['list'])){
            $content .= "<br>";
            $content .= "已装箱未封箱 ".count($unCloseList['list'])." 箱：<br>";
            // ********，最后更新人：XX，更新时间：2024-01-01  12：00：00
            foreach ($unCloseList['list'] as $unClose){
                $content .= $unClose['box_sn']."，最后更新人：".$unClose['update_user']."，更新时间：".$unClose['update_time']."<br>";
            }
        }
        $unPlateList = $this->getUnPlate($params);
        if (!empty($unPlateList['list'])){
            $content .= "<br>";
            $content .= "已封箱未报关 ".count($unPlateList['list'])." 箱：<br>";
            foreach ($unPlateList['list'] as $unPlate){
               // X1201003，封箱人：XX，封箱时间：2024-01-01  12：00：00
                $content .= $unPlate['box_sn']."，封箱人：".$unPlate['close_box_user']."，封箱时间：".$unPlate['close_box_time']."<br>";
            }
        }
        if ($content != "消息内容：<br>") {
            $msg_data = [
                'title'      => $title,
                'content'    => $content,
                "link"       => "",
                "link_title" => "滞留预警"
            ];

            $specialEmail = '<EMAIL>';
            if (!empty($userId = WechatWmsLogic::getCurrentUserIdByEmail($specialEmail))) {
                PurRequestLogic::sendMsg(json_encode([
                    'user_id'=>$userId,
                    'from_sys_id'=>18,
                    'to_sys_id'=>18,
                    'msg_category_id'=>3,
                    'msg_data'=>$msg_data
                ]));
            }
        }
        return 0;
    }

    // 获取理货滞留数据
    public function getRetentionList($params)
    {
        $retentionInfo = (new AbnormalManageLogic())->getStayHandleCount();
        $retentionDetail = $retentionInfo['retentionDetail'];
        switch ($params['type']) {
            // 1.已理货未装箱
            case 1:
                $list = $this->getUnBoxed($params);
                $list['retentionDetail'] = $retentionDetail;
                return $list;
            // 2.已装箱未封箱
            case 2:
                $list = $this->getUnClose($params);
                $list['retentionDetail'] = $retentionDetail;
                return $list;
            // 3.已封箱未报关
            case 3:
                $list = $this->getUnPlate($params);
                $list['retentionDetail'] = $retentionDetail;
                return $list;
            default: break;
        }
        return ['total'=>0, 'list'=>[], 'retentionDetail' => $retentionDetail];
    }

    // 已封箱未报关
    private function getUnPlate($params) {
        $threeHoursAgo = time() - 3 * 60 * 60;
        $list = WmsTallyModel::from('wms_tally as a')
            ->join('wms_tally_detail as b', 'a.wsty_id', '=', 'b.wsty_id')
            ->where('b.is_base', 0)
            ->where('a.tally_status', 2)
            ->where('a.close_box_time', '>', 0)
            ->where('a.close_box_time', '<', $threeHoursAgo)
            ->where(function ($query) use ($params) {
                if (!empty($params['tally_type'])){
                    $query->where('a.box_type', $params['tally_type']);
                }
            })
            ->where(function ($query) use ($params) {
                if (!empty($params['search_val'])){
                    $query->orWhere('a.box_sn', $params['search_val']);
                    $query->orWhere('b.erp_order_sn', $params['search_val']);
                    $query->orWhere('b.goods_type', $params['search_val']);
                }
            })
            ->groupBy(['a.wsty_id'])
            ->select('a.*')
            ->paginate(isset($params['limit']) ? $params['limit'] : 10, ['*'], 'page', isset($params['page']) ? $params['page'] : 1)
            ->toArray();
        if (empty($list['data'])) {
            return ['total'=>$list['total'],  'list'=>[]];
        }
        $returnData = [];
        foreach ($list['data'] as $value){
            $erp_order_sn = WmsTallyDetailModel::where('wsty_id', $value['wsty_id'])->where('is_base', 0)->groupBy(['erp_order_sn'])->pluck('erp_order_sn')->toArray();
            $model_num = count(WmsTallyDetailModel::where('wsty_id', $value['wsty_id'])->where('is_base', 0)->pluck('goods_type')->toArray());
            $returnData[] = [
                'wsty_id' => $value['wsty_id'],
                'box_sn' => $value['box_sn'],
                'box_type' => $value['box_type'],
                'box_type_val' => array_get(WmsTallyModel::$boxTypeCn, $value['box_type']),
                'tally_status' => $value['tally_status'],
                'box_status_val' => '已封箱',
                'erp_order_sn' => $erp_order_sn,
                'model_num' => $model_num,
                'close_box_time' => date('Y-m-d H:i:s', $value['close_box_time']),
                'close_box_user' => $value['close_box_user'],
                'create_time' => date('Y-m-d H:i:s', $value['create_time']),
                'create_user' => empty($value['tally_user_name'])?$value['update_user_name']:$value['tally_user_name'],
            ];
        }
        return ['total'=>$list['total'],  'list'=>$returnData, ];
    }

    // 已装箱未封箱
    private function getUnClose($params) {
        $oneHoursAgo = time() - 60 * 60;
        $threeHoursAgo = time() - 3 * 60 * 60;

        $list = WmsTallyModel::from('wms_tally as a')
            ->join('wms_tally_detail as b', 'a.wsty_id', '=', 'b.wsty_id')
            ->where('a.tally_status', 1)
            ->where('a.update_time', '>', 0)
            ->where('b.is_base', 0)
            ->where(function ($query) use ($oneHoursAgo, $threeHoursAgo) {
                // 普货装箱1小时没有封箱
                $query->whereOr(function ($query, $oneHoursAgo) use ($oneHoursAgo) {
                    $query->where('box_type', 0)
                        ->where('a.update_time', '<', $oneHoursAgo);
                });
                // 美产商检3小时没有封箱
                $query->whereOr(function ($query) use ($threeHoursAgo) {
                    $query->whereIn('box_type', [1, 2])
                        ->where('a.update_time', '<', $threeHoursAgo);
                });
            })
            ->where(function ($query) use ($params) {
                if (!empty($params['tally_type'])){
                    $query->where('a.box_type', $params['tally_type']);
                }
            })
            ->where(function ($query) use ($params) {
                if (!empty($params['search_val'])){
                    $query->orWhere('a.box_sn', $params['search_val']);
                    $query->orWhere('b.erp_order_sn', $params['search_val']);
                    $query->orWhere('b.goods_type', $params['search_val']);
                }
            })
            ->groupBy(['a.wsty_id'])
            ->select('a.*')
            ->paginate(isset($params['limit']) ? $params['limit'] : 10, ['*'], 'page', isset($params['page']) ? $params['page'] : 1)
            ->toArray();
        if (empty($list['data'])){
            return ['total'=>$list['total'],  'list'=>[]];
        }
        $returnData = [];
        foreach ($list['data'] as $value){
            $erp_order_sn = WmsTallyDetailModel::where('wsty_id', $value['wsty_id'])->where('is_base', 0)->groupBy(['erp_order_sn'])->pluck('erp_order_sn')->toArray();
            $model_num = count(WmsTallyDetailModel::where('wsty_id', $value['wsty_id'])->where('is_base', 0)->pluck('goods_type')->toArray());
            $returnData[] = [
                'wsty_id' => $value['wsty_id'],
                'box_sn' => $value['box_sn'],
                'box_type' => $value['box_type'],
                'box_type_val' => array_get(WmsTallyModel::$boxTypeCn, $value['box_type']),
                'tally_status' => $value['tally_status'],
                'box_status_val' => '理货中',
                'erp_order_sn' => $erp_order_sn,
                'model_num' => $model_num,
                'update_time' => date('Y-m-d H:i:s', $value['update_time']),
                'update_user' => $value['update_user_name'],
                'create_time' => date('Y-m-d H:i:s', $value['create_time']),
                'create_user' => empty($value['tally_user_name'])?$value['update_user_name']:$value['tally_user_name'],
            ];
        }
        return ['total'=>$list['total'],  'list'=>$returnData];
    }

    // 已理货未装箱
    private function getUnBoxed($params)
    {
        $threeHoursAgo = time() - 3 * 60 * 60;
        $list = WmsTallyAbnormalDetailModel::where('create_time', '<', $threeHoursAgo)
            ->where('box_sn', '')
            ->where(function ($query) use ($params) {
                if (!empty($params['tally_type'])){
                    $query->where('type', $params['tally_type']);
                }
            })
            ->where(function ($query) use ($params) {
                if (!empty($params['search_val'])){
                    $query->orWhere('erp_order_sn', $params['search_val']);
                    $query->orWhere('box_sn', $params['search_val']);
                    $query->orWhere('goods_type', $params['search_val']);
                }
            })
            ->paginate(isset($params['limit']) ? $params['limit'] : 10, ['*'], 'page', isset($params['page']) ? $params['page'] : 1)
            ->toArray();
        if (empty($list['data'])){
            return ['total'=>$list['total'],  'list'=>[]];
        }
        $returnData = [];
        foreach ($list['data'] as $value){
            $orderGoods = OrderGoodsModel::where('order_goods_id', $value['order_goods_id'])->first();
            $tallyDetail = WmsTallyDetailModel::where('wstydl_id', $value['wstydl_id'])->first();
            $returnData[] = [
                'abnormal_batch' => $value['abnormal_batch'],
                'erp_order_sn' => $value['erp_order_sn'],
                'box_status_val' => '未装箱',
                'goods_name' => $value['goods_title'],
                'declaration_type_val' => ($orderGoods['declaration_type']==3)?'两步申报':"非两步申报",
                'model' => $value['goods_type'],
                'is_goods_check' => $value['is_goods_check'],
                'brand' => $value['brand'],
                'order_remark' => OrderModel::where('order_id',$orderGoods['order_id'])->value('order_remark'),
                'qty' => $value['tally_num'],
                'origin' => $value['origin'],
                'net_weight' => $tallyDetail['net_weight'],
                'tally_remark'=> $tallyDetail['remark'],
                'tally_time' => date('Y-m-d H:i:s', $value['create_time']),
                'tally_user' => $tallyDetail['create_user'],
                'tally_picture' => $tallyDetail['goods_check_pic'],
            ];
        }
        return ['total'=>$list['total'],  'list'=>$returnData];
    }

    // 获取理货记录
    public function getTallyRecordList($params)
    {
        $list = self::buildLxTallySql($params);
        $list = $list->orderBy('td.create_time', 'desc')->paginate($params['limit'], ['*'], 'p', $params['page'])->toArray();
        if (empty($list)) {
            return $list;
        }
        foreach ($list['data'] as $k => $v) {
            $list['data'][$k]['tally_status_val'] = $v['tally_status'] ? WmsTallyModel::$tallyStatusCn[$v['tally_status']]: '';
            $list['data'][$k]['tally_time'] = $v['tally_time'] ? date('Y-m-d H:i:s', $v['tally_time']) : '';
            $list['data'][$k]['sync_status_val'] = $v['sync_status'] ? WmsTallyDetailModel::$SYNC_STATUS_ALL[$v['sync_status']] : '';
            $list['data'][$k]['box_type_val'] = $v['sync_status'] ? WmsTallyModel::$boxTypeCn[$v['sync_status']] : '';
            $list['data'][$k]['declaration_type_val'] = $v['declaration_type'] ? WmsTallyDetailModel::$DECLARATION_TYPE_ALL[$v['declaration_type']] : '';
        }
        return $list;
    }

    public static function buildLxTallySql($params)
    {
        $field = [
            "td.wsty_id", 'td.wstydl_id',  'td.erp_order_sn', "td.box_sn", 't.tally_status', 'td.sync_status', 'td.goods_title', 'td.goods_type', 'td.brand',
            "t.box_type", 'td.order_remark', 'td.tally_num', 'td.origin', 'td.net_weight', 'td.remark', "td.create_time as tally_time",
            "td.create_user as tally_user_name", 'td.goods_check_pic', 't.box_type', 'td.is_goods_check', 'td.declaration_type'
        ];

        $list = WmsTallyDetailModel::from('wms_tally_detail as td')
            ->leftJoin('wms_tally as t', 't.wsty_id', '=', 'td.wsty_id')
            ->leftJoin('wms_tally_abnormal_detail as ta', 'td.wstydl_id', '=', 'ta.wstydl_id')
            ->where('td.is_base', 0)
            ->where(function ($q) use ($params) {
                if (!empty($params['create_time_begin'])) {
                    $q->where('td.create_time', '>=', strtotime($params['create_time_begin']));
                }

                if (!empty($params['create_time_end'])) {
                    $q->where('td.create_time', '<=', (strtotime($params['create_time_end'])+86399));
                }

                $currentName = WechatWmsLogic::getCurrentName();
                $viewAllEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
                $currentEmail = WechatWmsLogic::getCurrentEmail();
                if ($currentName != 'admin' && !in_array($currentEmail, $viewAllEmails)) {
                    $q->where('td.create_user', $currentName);
                }

                if (!empty($params['tally_type'])) {
                    $q->where('ta.type', $params['tally_type']);
                }
            })->where(function ($q) use ($params) {
                if (!empty($keyword = $params['keyword'])) {
                    $q->orWhere('t.box_sn', 'like', '%'.$keyword.'%');
                    $q->orWhere('td.erp_order_sn', 'like', '%'.$keyword.'%');
                    $q->orWhere('td.goods_type', 'like', '%'.$keyword.'%');
                }
            })
            ->select($field);
        return $list;
    }


    private function getBoxDataTaxAmt($box_sn)
    {
        return WmsTallyDetailModel::from('wms_tally_detail as a')
            ->join('wms_tally_abnormal_detail as b','a.wstydl_id', '=', 'b.wstydl_id')
            ->where('b.box_sn', $box_sn)
            ->whereIn('b.payer_type', [1, 2])
            ->select(DB::raw('SUM(lie_a.origin_tax_money + lie_a.tariff_amt) as total'))
            ->first()
            ->total;
    }

    private function getCurrentTaxAmt($wstydl_id)
    {
        return WmsTallyDetailModel::from('wms_tally_detail as a')
            ->join('wms_tally_abnormal_detail as b','a.wstydl_id', '=', 'b.wstydl_id')
            ->where('a.wstydl_id', $wstydl_id)
            ->whereIn('b.payer_type', [1, 2])
            ->select(DB::raw('SUM(lie_a.origin_tax_money + lie_a.tariff_amt) as total'))
            ->first()
            ->total;
    }
}