<?php

namespace Supplychain\Model\Logic\WmsLogic;

use Supplychain\Model\Logic\PurRequestLogic;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\OrderTrackingLogModel;

class OrderTrackingLogLogic
{
    // 添加日志
    static public function addLog($data)
    {
        $now = time();
        OrderTrackingLogModel::addLog($data['log_type'], $data['bill_type'], $data['bill_id'], $data['content'], $data['create_user_id'], $data['create_user_name'], $now);
        if ($data['bill_type'] == OrderTrackingLogModel::orderDetail) {
            if (self::checkIsPurOrder($data['bill_id'])) {
                // 订单明细的才需要同步给采购系统
                PurRequestLogic::synWmsEventLog(json_encode([
                    'type'=>$data['log_type'],
                    'erp_scm_item_id'=>OrderGoodsModel::where('order_goods_id',$data['bill_id'])->value('erp_entery_id'),
                    'create_uid'=>$data['create_user_id'],
                    'create_name'=>$data['create_user_name'],
                    'msg'=>$data['content'],
                    'ext_data'=>isset($data['ext_data'])?$data['ext_data']:[],
                ]));
            }
        }
    }

    private static function checkIsPurOrder($bill_id)
    {
        $order_id = OrderGoodsModel::where('order_goods_id',$bill_id)->value('order_id');
        return OrderModel::where('order_id',$order_id)->whereIn('company_id',OrderModel::$ichuntCompanyId)->exists();
    }
}