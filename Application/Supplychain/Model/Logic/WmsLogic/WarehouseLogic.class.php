<?php
/**
 * Created by 2022/3/1.
 * User: Joneq
 * Info: 2022/3/1
 * Time: 下午2:13
 */

namespace Supplychain\Model\Logic\WmsLogic;


use Supplychain\Controller\ErpPushController;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\WechatWmsLogic;
use Supplychain\Model\SzScanOutInListModel;
use Supplychain\Model\WarehouseOperationDetailModel;
use Supplychain\Model\WarehouseOperationListModel;
use Supplychain\Model\WarehouseOperationUnitModel;

class WarehouseLogic
{

    public function warehouseOperationList($data)
    {
        $obj = (new WarehouseOperationListModel())->getWhereObj($data);
        $returnData = $obj->orderBy('weonlt_id', 'desc')->where('operation_status', 1)->select('weonlt_id', 'company_name', 'arrival_status', 'operation_remark', 'create_time', 'is_unpacking')
            ->paginate(isset($data['limit']) ? $data['limit'] : 10, ['*'], 'page', isset($data['page']) ? $data['page'] : 1)->toArray();

        foreach ($returnData['data'] as &$value) {

            $value['arrival_status_cn'] = array_get(WarehouseOperationListModel::$arrivalStatus, $value['arrival_status']);
            $value['create_time_cn'] = $value['create_time'] ? date('Y-m-d H:i', $value['create_time']) : '';
            $value['operation_num_sum'] = WarehouseOperationDetailModel::where('weonlt_id', $value['weonlt_id'])->sum('instructions_num');
        }

        return $returnData;
    }


    public function warehouseOperationDetail($data)
    {
        $listdata = (new WarehouseOperationListModel())
            ->getWhereObj($data)
            ->select('weonlt_id', 'company_name', 'arrival_status', 'operation_remark', 'create_time', 'is_unpacking', 'operation_img')
            ->first()->toArray();
        if (empty($listdata)) {
            throw new \Exception('未查找到列表记录');
        }

        $listdata['create_time_cn'] = $listdata['create_time'] ? date('Y-m-d H:i', $listdata['create_time']) : '';

        if ($listdata['is_unpacking']) {
            $listdata['unit'] = implode(',', array_unique(WarehouseOperationDetailModel::where('weonlt_id', $listdata['weonlt_id'])->pluck('unit')->toArray()));
        } else {
            $listdata['unit'] = implode(',', array_unique(WarehouseOperationDetailModel::where('weonlt_id', $listdata['weonlt_id'])->pluck('operation_unit')->toArray()));
        }
        $listdata['is_unpacking_cn'] = array_get(WarehouseOperationListModel::$isUnpacking, $listdata['is_unpacking']);
        $listdata['operation_num_sum'] = WarehouseOperationDetailModel::where('weonlt_id', $listdata['weonlt_id'])->sum('instructions_num');
        $listdata['erp_order_sn_all'] = implode(',', array_unique(WarehouseOperationDetailModel::where('weonlt_id', $listdata['weonlt_id'])->pluck('erp_order_sn')->toArray()));
        $returnData['list'] = $listdata;

        $returnData['detail'] = WarehouseOperationDetailModel::where(['weonlt_id' => $data['weonlt_id']])
            ->select('weondl_id', 'erp_order_sn', 'model', 'operation_type', 'instructions_num', 'pic', 'qty')
            ->orderBy('weonlt_id', 'desc')->get()->toArray();

        foreach ($returnData['detail'] as &$val) {
            $val['operation_type_cn'] = WarehouseOperationUnitModel::where('weonut_id', $val['operation_type'])->value('unit_name');
        }

        return $returnData;
    }

    //执行仓内作业
    public function execWarehouseOperation($data)
    {
        if (intval(WarehouseOperationListModel::where('weonlt_id', $data['weonlt_id'])->value('operation_status')) == 2) {
            throw new \Exception('已经作业，不可重复作业');
        }

        foreach ($data['weondl_id'] as $key => $value) {
            WarehouseOperationDetailModel::where('weondl_id', $value)->where('weonlt_id', $data['weonlt_id'])->update([
                'actual_operation_num' => $data['actual_operation_num'][$key],
                'instructions_num' => $data['actual_operation_num'][$key],
                'sum_price' => $data['actual_operation_num'][$key] * (WarehouseOperationDetailModel::where('weondl_id', $value)->value('unit_price')),
            ]);
        }
        WarehouseOperationListModel::where('weonlt_id', $data['weonlt_id'])->update([
            'operation_img' => $data['operation_img'],
            'operation_status' => 2,
            'exec_time' => time()
        ]);
        return [];
    }


    //查询大于当前ID的数据，如果有就新增，如果没有就返回当前的数据
    public function szScanOutInList($data)
    {
        return (new ErpPushController())->getInStorage($data);
    }


    public function szScanIn($data)
    {

        if (empty($data['is_submit'])) {
            $erpOrderSn = explode('-',$data['erp_order_sn'])[0];
            $erpData = self::getSzOutInInfo($erpOrderSn);
            if (empty($erpData)){
                throw new \Exception('该入仓号暂无数据');
            }
            return ['car_sort' => $erpData['carOrder'], 'num' =>$erpData['boxesNum'] ,'erp_order_sn'=>$erpData['entrustNo']];
        }


        $userInfo = WechatWmsLogic::userInfo();

        $time = time();

        $updateData = $data['list'];

        $erpData = ['type'=>'入库','entrusNos'=>array_column($updateData,'erp_order_sn'),'startTime'=>$data['startTime'],'endTime'=>$data['endTime']];

        (new ErpPushController())->updateStorageStatus($erpData);

        foreach ($updateData as $key => $val) {
            (new SzScanOutInListModel())->insertGetId([
                'erp_order_sn' => $val['erp_order_sn'],
                'car_sort' => $val['car_sort'],
                'num' => $val['num'],
                'is_in_store' => 1,
                'in_scan_time' => $time,
                'in_scan_user' => $userInfo['name']
            ]);
        }
        return [];

    }

    public function szScanOut($data)
    {

        if (empty($data['is_submit'])) {
            $erpOrderSn = explode('-',$data['erp_order_sn'])[0];
            $erpData = self::getSzOutInInfo($erpOrderSn);

            if (empty($erpData)){
                throw new \Exception('该入仓号暂无数据');
            }
            return ['car_sort' => $erpData['carOrder'], 'num' =>$erpData['boxesNum'] ,'erp_order_sn'=>$erpData['entrustNo']];
        }

        $userInfo = WechatWmsLogic::userInfo();

        $time = time();

        $updateData = $data['list'];

        $erpData = ['type'=>'出库','entrusNos'=>array_column($updateData,'erp_order_sn'),'startTime'=>$data['startTime'],'endTime'=>$data['endTime']];

        (new ErpPushController())->updateStorageStatus($erpData);

        foreach ($updateData as $key => $val) {
            (new SzScanOutInListModel())->where('erp_order_sn',$val['erp_order_sn'])->where('is_out_store',0)->update([
                'erp_order_sn' => $val['erp_order_sn'],
                'num' => $val['num'],
                'is_out_store' => 1,
                'out_scan_user' => $userInfo['name'],
                'out_scan_time' => $time,
                'create_time' => $time
            ]);
        }

        return [];
    }

    static public function getSzOutInInfo($erpOrderSn)
    {
        $data =  (new ErpPushController())->getBoxesNumByNo(['entrustNo'=>$erpOrderSn]);

        if(empty($data)){
            throw new \Exception('该入仓号暂无数据');
        }

        if (!isset($data['carOrder']) || empty($data['carOrder'])){
            $data['carOrder'] = '暂无车次';
        }

        return $data;
    }
}