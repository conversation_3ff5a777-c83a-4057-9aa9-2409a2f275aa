<?php


namespace Supplychain\Model\Logic\WmsLogic;

use Supplychain\Controller\ErpPushController;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\ErpPushLogic;
use Supplychain\Model\Logic\ErpRequestLogic;
use Supplychain\Model\Logic\WechatWmsLogic;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\OrderRecordModel;
use Supplychain\Model\OriginGoodsModel;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\WmsTallyGoodsDetailModel;
use Supplychain\Model\WmsTallyGoodsFixModel;
use Supplychain\Model\WmsTallyGoodsModel;
use Supplychain\Model\ActionLogModel;
use Supplychain\Model\AskCustomerExceptionModel;
use Supplychain\Model\AskCustomerModel;
use Supplychain\Model\AskCustomerProblemGoodsModel;
use Supplychain\Model\AskCustomerProblemListModel;
use Supplychain\Service\DingNotify;


class TallyGoodsLogic
{

    //从erp获取理货登记列表
    public function getTallyGoods($data)
    {
        if (empty($data['erp_order_sn'])){
            throw new \Exception('入仓号不能为空');
        }

        if (!empty($askCustomerId = AskCustomerModel::where('erp_order_sn',$data['erp_order_sn'])->value('ask_customer_id'))) {
            if (AskCustomerProblemListModel::where('store_execute_status',1)->where('ask_customer_id',$askCustomerId)->value('ask_customer_problem_list_id')){
                throw new \Exception('该理货单问客记录香港未执行，请执行问客后再提交');
            }
        }

        $returnData = (new ErpPushController())->getGoodsArrangeData(['number'=>$data['erp_order_sn']]);
        if (empty($returnData)){
            throw new \Exception('该入仓号无效，请核对信息');
        }

        $erpOrderSn = explode('-',$data['erp_order_sn'])[0];

        if (empty(OrderModel::where('erp_order_sn',$erpOrderSn)->value('order_id'))){
            DingNotify::confirmTallyGoods($data['erp_order_sn']);
            throw new \Exception('该入仓号在平台不存在，请确认');
        }

        return $returnData[0];
    }


    static public function checkOrigin($requestData)
    {
        //如果产地是美国要进行产地税确认
        if (mb_strpos($requestData['origin'], '美国') !== false) {
            try{
                ErpRequestLogic::getOriginData([
                    'method'=>'confirmOriginTax',
                    'orderEntryID'=>$requestData['entry_id'],
                    'originCountry'=>'美国']);
            }catch (\Exception $exception){
                return [];
            }
        }
    }



    //从erp获取理货以前型号列表
    public function updateCurrentPrevTallyGoods($requestData)
    {
        $updateData = $this->getCurrentPrevTallyGoods(['erp_order_sn'=>$requestData['erp_order_sn'],'wstygs_id'=>$requestData['wstygs_id']]);

        $allData = WmsTallyGoodsDetailModel::where('wstygs_id',$requestData['wstygs_id'])->get()->toArray();

        foreach ($allData as $value){

            if (!isset($updateData[$value['entry_id']])){
                throw new \Exception('金蝶明细Id错误，请确认');
            }

            $updateDataDetail = $updateData[$value['entry_id']];
            $updateDataDetail['total_price'] = $updateDataDetail['unit_price'] * $value['tally_num'];
            WmsTallyGoodsDetailModel::where('wstygsdl_id',$value['wstygsdl_id'])->update($updateDataDetail);
        }
    }

    public function getCurrentPrevTallyGoods($requestData)
    {
        $erpOrderSn = $requestData['erp_order_sn'];


        $erpReturnData = (new ErpPushLogic())->getOrderCheckDetailsApi(['entrustNo'=>$erpOrderSn]);


        if (empty($erpReturnData)){
            throw new \Exception('该入仓号暂无数据，请核对信息');
        }

        $returnData = [];


        foreach ($erpReturnData['details'] as $value){
            $updateData = [
                'goods_title'=>$value['materialName'],
                'unit_price'=>$value['price'],
                'goods_type'=>$value['model'],
                'material_sn'=>empty($value['goodsNo'])?'':$value['goodsNo'],
                'brand'=>$value['brand'],
                'order_numbers'=>$value['qty'],
                'old_material_sn'=>CommonLogic::getVal($value,'materialNameOld'),
                'old_goods_type'=>CommonLogic::getVal($value,'modelOld'),
                'old_brand'=>CommonLogic::getVal($value,'brandOld'),
                'old_unit_price'=>CommonLogic::getVal($value,'priceOld'),
                'old_numbers'=>CommonLogic::getVal($value,'qtyOld'),
            ];


            $updateData['is_change'] = '否';
            $isChangeKeyArr = ['old_material_sn','old_goods_type','old_brand','old_unit_price','old_numbers'];


            foreach ($isChangeKeyArr as $v){
                if (!empty($updateData[$v])){
                    $updateData['is_change'] = '是';
                }
            }


            $returnData[$value['entryID']] = $updateData;
        }
        if (empty($returnData)){
            throw new \Exception('暂未查找到需要修改的数据');
        }

        WmsTallyGoodsModel::where('wstygs_id',$requestData['wstygs_id'])->update(['pur_sn'=>$erpReturnData['orderNo']]);

        return $returnData;
    }


    public function checkErpOrderSnIsTally($requestData)
    {
        if (empty(!WmsTallyGoodsModel::where('erp_order_sn',$requestData['erp_order_sn'])->value('erp_order_sn'))){
            throw new \Exception('该入仓号已经理货,禁止重复理货');
        }

        if (!empty($askCustomerId = AskCustomerModel::where('erp_order_sn',$requestData['erp_order_sn'])->value('ask_customer_id'))) {
            if (AskCustomerProblemListModel::where('store_execute_status',1)->where('ask_customer_id',$askCustomerId)->value('ask_customer_problem_list_id')){
                throw new \Exception('该理货单问客记录香港未执行，请执行问客后再提交');
            }
        }

        $returnData = (new ErpPushController())->getGoodsArrangeData(['number'=>$requestData['erp_order_sn']]);
        if (empty($returnData)){
            throw new \Exception('该入仓号无效，请核对信息');
        }

        return 'ok';
    }



    //支持搜索
    public function getOrigin($requestData)
    {
        return OriginGoodsModel::where('name','like','%'.$requestData['origin'].'%')->pluck('name','name');
    }

    public function getHistoryPic($requestData)
    {
        $srcEntryID = $requestData['srcEntryID'];
        if (empty($srcEntryID)){
            return [];
        }
        $tally_pic_arr = WmsTallyGoodsDetailModel::where('erp_entery_id',$srcEntryID)
            ->select(['create_time', 'goods_check_pic'])->orderBy('create_time', 'desc')
            ->get()->toArray();
        if (empty($tally_pic_arr)){
            return [];
        }
        $pic_arr = [];
        foreach ($tally_pic_arr as $value){
            $goods_check_pic = $value['goods_check_pic'];
            if (empty($goods_check_pic)){
                continue;
            }
            $history_pic_arr = explode(',', $goods_check_pic);
            $list = [];
            foreach ($history_pic_arr as $history_pic){
                $list[] = ['entryID' => $requestData['entryID'], 'pic_address' => $history_pic];
            }
            $pic_arr[] = ['time'=>date('Y-m-d H:i:s', $value['create_time']), 'list' => $list];
        }
        return $pic_arr;
    }

    public function submitTallyGoods($requestData)
    {

        try{

            if (!empty(WmsTallyGoodsModel::where('erp_order_sn',$requestData['erp_order_sn'])->value('erp_order_sn'))){
                throw new \Exception('该入仓号已经理货,禁止重复理货');
            }

            $erpReturnJson = $this->getTallyGoods(['erp_order_sn'=>$requestData['erp_order_sn']]);

            DB::connection('SUPPLYCHAIN')->beginTransaction();

            OrderModel::where('erp_order_sn',$requestData['erp_order_sn'])
                ->where('status','>=',1)
                ->where('id_edit_order',0)->update(['stock_in_status'=>2]);

            $baseOrderGoodsData = [];
            foreach ($erpReturnJson['entrys'] as $value){
                $baseOrderGoodsData[$value['entryID']] = $value;
            }



            $picArr = [];
            foreach ($requestData['pic_json'] as $value){
                $picArr[$value['entryID']] = $value['goods_check_pic'];
            }

            $tallyDetailData = [];
            $entryIdArr = [];
            foreach ($requestData['detail_json'] as $column){
                if (empty($column) || isset($entryIdArr[$column[0]['entryID']])){
                    continue;
                }else{
                    $entryIdArr[$column[0]['entryID']] = 1;
                }
                foreach ($column as $value){
                    $tallyDetailData[$value['entryID']][] = $value;
                }
            }


            if (empty($baseOrderGoodsData)){
                throw new \Exception('理货明细数据不能为空');
            }


            $businessName = $erpReturnJson['serviceMan'];

            $time = time();

            if ($erpReturnJson['customer'] == '深圳市猎芯科技有限公司'){
                $customerType = 2;
            }else{
                $customerType = 1;
            }

            $wmsTallyGoodsInsertData = [
                'erp_order_sn'=>$requestData['erp_order_sn'],
                'customs_clearance_price'=>$requestData['customs_clearance_price'],
                'box_num'=>$requestData['box_num'],
                'board_num'=>$requestData['board_num'],
                'gross_weight'=>$requestData['gross_weight'],
                'pur_pic'=>$requestData['pur_pic'],
                'tally_name'=>WechatWmsLogic::getCurrentName(),
                'bussiness_name'=>$businessName,
                'is_goods_check'=>$erpReturnJson['isInspOrg']?1:0,
                'customer_name'=>$erpReturnJson['customer'],
                'create_time'=>$time,
                'customer_type'=>$customerType,
            ];


            if (!empty(WmsTallyGoodsModel::where('erp_order_sn',$requestData['erp_order_sn'])->value('erp_order_sn'))){
                throw new \Exception('该入仓号已经理货,禁止重复理货');
            }

            $erpRequestData = [
                'entrustNo'=>$requestData['erp_order_sn'],
                'type'=>'submit',
                'tallyDate'=>date('Y-m-d H:i:s'),
                'tallyPerson'=>WechatWmsLogic::getCurrentName()
            ];
            //新增提交时间
            ErpRequestLogic::getData($erpRequestData,'synTallyDate');

            $wstygsId = WmsTallyGoodsModel::insertGetId($wmsTallyGoodsInsertData);

            foreach ($baseOrderGoodsData as $key=>$value){




                $insertData = [
                    'wstygs_id'=>$wstygsId,
                    'order_id'=>0,
                    'order_goods_id'=> empty($value['ptEntryID'])?0:$value['ptEntryID'],
                    'tally_num'=>$value['qty'],
                    'origin'=>isset($value['country'])?$value['country']:'',
                    'create_time'=>$time,
                    'material_sn'=>empty($value['goodsNo'])?'':$value['goodsNo'],
                    'goods_title'=>$value['material'],
                    'brand'=>$value['brand'],
                    'goods_type'=>$value['model'],
                    'order_numbers'=>$value['qty'],
                    'unit'=>$value['unit'],
                    'currency'=>$value['currency'],
                    'unit_price'=>$value['price'] ,
                    'total_price'=>$value['price'] * $value['qty'],
                    'entry_id'=>$value['entryID'],
                    'erp_entery_id'=>$value['srcEntryID'],
                    'goods_check_pic'=>isset($picArr[$value['entryID']])?$picArr[$value['entryID']]:''
                ];

                CommonLogic::checkHeader($insertData['material_sn'],$insertData['goods_check_pic']);

                if (isset($tallyDetailData[$value['entryID']])){
                    foreach ($tallyDetailData[$value['entryID']] as $v){
                        $insertData['origin'] = $v['origin'];
                        self::checkOrigin($insertData);
                        $insertData['tally_num'] = $v['tally_num'];
                        $insertData['total_price'] = $v['tally_num'] * $v['unit_price'];
                        WmsTallyGoodsDetailModel::insertGetId($insertData);
                    }
                }else{
                    self::checkOrigin($insertData);
                    WmsTallyGoodsDetailModel::insertGetId($insertData);
                }
            }

            if (!empty($requestData['fix_erp_order_sn'])){
                foreach (explode(',',$requestData['fix_erp_order_sn']) as $value){
                    if (empty($value))continue;
                    if (WmsTallyGoodsFixModel::where('fix_erp_order_sn',$value)->where('is_del',0)->value('fix_erp_order_sn')){
                        throw new \Exception($value.'：选择合箱的入仓号已被【合箱】，无法提交合箱');
                    }
                    WmsTallyGoodsFixModel::insertGetId([
                        'erp_order_sn'=>$requestData['erp_order_sn'],
                        'fix_erp_order_sn'=>$value,
                    ]);
                }
            }


            //修改理货数据
            $this->updateCurrentPrevTallyGoods(['erp_order_sn'=>$requestData['erp_order_sn'],'wstygs_id'=>$wstygsId]);
            DB::connection('SUPPLYCHAIN')->commit();
        }catch (\Exception $exception){
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($exception->getMessage());
        }





        return [];
    }

    public function getFixErpOrderSn($requestData)
    {
        //支持模糊搜索

        $beginTime = strtotime(date('Y-m-d'));
        $endTime = $beginTime + 86400;

        $obj = new WmsTallyGoodsModel();
        if (!empty($requestData['erp_order_sn'])){
            $obj = $obj->where('erp_order_sn','like','%'.$requestData['erp_order_sn'].'%');
        }
        $allData = $obj->where('create_time','>=',$beginTime)
            ->where('create_time','<=',$endTime)
            ->where('audit_status',0)
            ->pluck('erp_order_sn')->toArray();

        if (empty($allData)){
            return [];
        }

        $returnData = [];

        foreach ($allData as $value){
            if (WmsTallyGoodsFixModel::where('fix_erp_order_sn',$value)->where('is_del',0)->value('wstygsfx_id')){
                continue;
            }
            $returnData[] = ['name'=>$value,'value'=>$value];
        }

        return $returnData;
    }


    public function changeWatch($requestData)
    {
        if (!isset($requestData['ask_customer_problem_list_id'])){
            throw new \Exception('ask_customer_problem_list_id is null');
        }
        $name = WechatWmsLogic::getCurrentName();
        $askCustomerInfo = AskCustomerProblemListModel::where('ask_customer_problem_list_id',$requestData['ask_customer_problem_list_id'])->first();
        if (intval($askCustomerInfo['ask_customer_status']) === 1){
            return '该问题还未确认';
        }
        if (intval($askCustomerInfo['store_execute_status']) === 2){
            return '该问题已经执行';
        }

        AskCustomerProblemListModel::where('ask_customer_problem_list_id',$requestData['ask_customer_problem_list_id'])->update([
            'store_execute_name'=>$name,
            'store_execute_time'=>time(),
            'store_execute_status'=>2,
        ]);
        (new ActionLogModel())->addLog($requestData['ask_customer_problem_list_id'],'仓库执行作业',$name,6);
        return [];
    }


    //获取问题列表
    static public function getProblemList($data)
    {
        $data['store_execute_status'] = 1;
        $returnData = AskCustomerProblemListModel::getList($data);
        return $returnData;
    }


    //获取问题详情
    static public function askCustomerProblemDetail($data)
    {
        if (empty($askCustomerProblemInfo = AskCustomerProblemListModel::where('ask_customer_problem_list_id',$data['ask_customer_problem_list_id'])->first())){
            throw new \Exception('暂无该问题信息');
        }

        $askCustomerProblemInfo = $askCustomerProblemInfo->toArray();
        $returnData = $askCustomerProblemInfo;
        if (intval($returnData['handle_type']) === 2){
            $returnData['ask_customer_confirm_feedback'] = '人工处理';
        }
        $returnData['ask_customer_exception_id_cn'] =  AskCustomerExceptionModel::where('ask_customer_exception_id',$returnData['ask_customer_exception_id'])->value('content');
        $returnData['create_time_cn'] =  empty($returnData['create_time'])?'':date('m-d H:i',$returnData['create_time']);
        $returnData['ask_customer_confirm_time_cn'] =  empty($returnData['ask_customer_confirm_time'])?'':date('m-d H:i',$returnData['ask_customer_confirm_time']);
        $returnData['ask_customer_status_cn'] =  array_get(AskCustomerProblemListModel::$askCustomerStatus,$returnData['ask_customer_status']);

        $returnData['store_execute_status_cn'] =  array_get(AskCustomerProblemListModel::$storeExecuteStatus,$returnData['store_execute_status']);

        $askCustomerInfo = AskCustomerModel::where('ask_customer_id',$returnData['ask_customer_id'])->first();
        $returnData['upload_img'] = $askCustomerInfo['upload_img'].','.$returnData['upload_img'];
        $returnData['is_watch'] = $askCustomerProblemInfo['is_watch'];
        $returnData['company_name'] = $askCustomerInfo['company_name'];
        $returnData['ask_customer_sw_status_cn'] =  array_get(AskCustomerProblemListModel::$askCustomerStatus,$askCustomerProblemInfo['ask_customer_sw_status']);
        $returnData['order_goods'] = AskCustomerProblemGoodsModel::where('ask_customer_problem_list_id',$returnData['ask_customer_problem_list_id'])->get()->toArray();
        $returnData['action_log'] = ActionLogModel::where('user_id',$returnData['ask_customer_problem_list_id'])->where('type',6)->get()->toArray();
        foreach ($returnData['action_log'] as $key=>$val){
            $returnData['action_log'][$key]['create_time'] = date('Y-m-d H:i:s',$val['create_time']);
        }
        $returnData['mobile'] = '';

        return $returnData;
    }


    public function checkIsTallyGoods($requestData)
    {
        if (empty($requestData['batch'])){
            $erpOrderSn = $requestData['erp_order_sn'];
        }else{
            $erpOrderSn = $requestData['erp_order_sn'].'-'.$requestData['batch'];
        }

        if (empty(WmsTallyGoodsModel::where('erp_order_sn',$erpOrderSn)->value('wstygs_id'))){
            throw new \Exception('该入仓号批次未理货');
        }

        return [];
    }



    //检测供应链的理货数据
    public function checkScTallyGoods($data)
    {
        if (empty($data['erp_order_sn'])){
            throw new \Exception('入仓号不能为空');
        }

        if (!empty($askCustomerId = AskCustomerModel::where('erp_order_sn',$data['erp_order_sn'])->value('ask_customer_id'))) {
            if (AskCustomerProblemListModel::where('store_execute_status',1)->where('ask_customer_id',$askCustomerId)->value('ask_customer_problem_list_id')){
                throw new \Exception('该理货单问客记录香港未执行，请执行问客后再提交');
            }
        }

        $returnData = (new ErpPushController())->getGoodsArrangeData(['number'=>$data['erp_order_sn']]);
        if (empty($returnData)){
            throw new \Exception('该入仓号无效，请核对信息');
        }

        return $returnData;
    }

}
