<?php
/**
 * Created by PhpStorm.
 * User: 春风
 * Date: 2023/12/5
 * Time: 9:45
 */

namespace Supplychain\Model\Logic\WmsLogic;

use GuzzleHttp\Client;
use Supplychain\Model\AskCustomerExceptionModel;
use Supplychain\Model\AskCustomerModel;
use Supplychain\Model\AskCustomerProblemGoodsModel;
use Supplychain\Model\BaoGuanOrderListModel;
use Supplychain\Model\CmsModel;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\Logic\OrderLogic;
use Supplychain\Model\Logic\WechatWmsLogic;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\AskCustomerProblemListModel;
use Supplychain\Model\ActionLogModel;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\OrderModel;
use Supplychain\Model\UserMessageModel;

class AskCustomerLogic
{

    public function askCustomerProblemListV2($data)
    {

        $obj = AskCustomerProblemListModel::getWhereObj($data);
        $returnData = $obj->orderBy('ask_customer_id','desc')->paginate(isset($_REQUEST['limit'])?$_REQUEST['limit']:10, ['*'], 'page', isset($_REQUEST['page'])?$_REQUEST['page']:1)->toArray();
        $data = $returnData['data'];

        $askCustomer = [];

        foreach ($data as $key=>$value){

            //问客详情
            if (!isset($askCustomer[$value['ask_customer_id']])){
                $askCustomer[$value['ask_customer_id']] =  AskCustomerModel::where('ask_customer_id',$value['ask_customer_id'])->first()->toArray();
            }

            //异常
            if (!isset($excepiton[$value['ask_customer_exception_id']])){
                $excepiton[$value['ask_customer_exception_id']] = AskCustomerExceptionModel::where('ask_customer_exception_id',$value['ask_customer_exception_id'])->value('content');
            }

            $askCustomer[$value['ask_customer_id']]['main_upload_img'] = $askCustomer[$value['ask_customer_id']]['upload_img'];

            $value = array_merge($askCustomer[$value['ask_customer_id']],$value);

            $value['create_time_cn'] =  $value['create_time'] ? date('Y-m-d H:i', $value['create_time']) : '';
            $value['order_time_cn'] =  $value['order_time'] ? date('Y-m-d H:i', $value['order_time']) : '';
            $value['ask_customer_confirm_time_cn'] =  $value['ask_customer_confirm_time'] ? date('Y-m-d H:i', $value['ask_customer_confirm_time']) : '';
            $value['store_execute_time_cn'] =  $value['store_execute_time'] ? date('Y-m-d H:i', $value['store_execute_time']) : '';


            $value['ask_customer_status_cn'] =  array_get(AskCustomerProblemListModel::$askCustomerStatus,$value['ask_customer_status']);
            $value['ask_customer_sw_status_cn'] =  array_get(AskCustomerProblemListModel::$askCustomerStatus,$value['ask_customer_sw_status']);
            $value['store_execute_status_cn'] =  array_get(AskCustomerProblemListModel::$storeExecuteStatus,$value['store_execute_status']);
            $value['ask_customer_exception_id_cn'] = $excepiton[$value['ask_customer_exception_id']];

            if (intval($value['store_execute_status']) == 2){
                $value['all_ask_time'] = self::getUseTime($value['store_execute_time'] - $value['create_time']);
            }else{
                $value['all_ask_time'] = self::getUseTime(time() - $value['create_time']);
            }



            $value['is_goods_check'] = self::getIsGoodsCheck($value['erp_order_sn']);

            $data[$key] = $value;
        }
        $returnData['data'] = $data;
        return $returnData;
    }

    public function addAskCustomerV2($param)
    {
        $data['erp_order_sn'] = $param['erp_order_sn'];
        $data['user_name'] = WechatWmsLogic::getCurrentName();


        $exceptionTitle = '';

        try{
            DB::connection('SUPPLYCHAIN')->beginTransaction();

            $orderInfo = OrderModel::where('erp_order_sn',$data['erp_order_sn'])->first();
            if (empty($orderInfo)){
                throw new \Exception('该订单不存在');
            }
            $orderInfo = $orderInfo->toArray();
            $askCustomerInsertData['erp_order_sn'] = $data['erp_order_sn'];
            $askCustomerInsertData['company_name'] = CompanyModel::where('company_id',$orderInfo['company_id'])->value('company_full_name');
            $askCustomerInsertData['order_time'] = $orderInfo['create_time'];
            $askCustomerInsertData['ask_time'] = time();
            $askCustomerInsertData['create_time'] = time();
            $askCustomerInsertData['create_user_name'] = $data['user_name'];
            $askCustomerInsertData['user_id'] = $orderInfo['user_id'];


            //新增问客
            $askCustomerId = AskCustomerModel::insertGetId($askCustomerInsertData);

            $askCUstomerExceptionInfo = AskCustomerExceptionModel::where('ask_customer_exception_id',$param['ask_customer_exception_id'])->first()->toArray();
            $askCustomerProblemInsertData['ask_customer_id'] = $askCustomerId;
            $askCustomerProblemInsertData['create_user_name'] = $data['user_name'];

            if (empty($bussinessName = CustomerModel::where('company_id',$orderInfo['company_id'])->value('follow_people'))){
                $bussinessName = '周红丹';
            }

            $askCustomerProblemInsertData['bussiness_name'] = $bussinessName;

//            if (empty($param['upload_img'])){
//                throw new \Exception($askCUstomerExceptionInfo['content'].'上传图片不能为空');
//            }

            $askCustomerProblemInsertData['upload_img'] = $param['upload_img'];

            $exceptionTitle .= $exceptionTitle.$askCUstomerExceptionInfo['content'].'问题';


            $askCustomerProblemInsertData['create_time']  = time();
            //$askCustomerProblemInsertData['ask_customer_remark'] = $param['ask_customer_remark'];
            $askCustomerProblemInsertData['ask_customer_exception_id'] = $param['ask_customer_exception_id'];

            $askCustomerProblemInsertData['user_id'] = $orderInfo['user_id'];
            $askCustomerProblemInsertData['erp_order_sn'] = $orderInfo['erp_order_sn'];
            $askCustomerProblemInsertData['ask_customer_sn'] = $orderInfo['order_sn'];
            $askCustomerProblemInsertData['login_type'] = 2;
            $askCustomerProblemId = AskCustomerProblemListModel::insertGetId($askCustomerProblemInsertData);



            if (empty($param['val'])){
                throw new \Exception('输入数据不能为空');
            }


            $askCustomerProblemGoodsData['ask_customer_problem_list_id'] = $askCustomerProblemId;
            $askCustomerProblemGoodsData['update_time'] = time();
            $askCustomerProblemGoodsData['update_user_name'] = $data['user_name'];
            $askCustomerProblemGoodsData['order_goods_id'] = $param['order_goods_id'];
            $askCustomerProblemGoodsData['goods_type'] =  $param['goods_type'];
            $askCustomerProblemGoodsData['order_result'] = $param['order_data'];
            $askCustomerProblemGoodsData['store_result'] = $param['val'];
            $askCustomerProblemGoodsId = AskCustomerProblemGoodsModel::insertGetId($askCustomerProblemGoodsData);

            (new ActionLogModel())->addLog($askCustomerProblemId,'仓库发起问题',$data['user_name'],6);

            self::sendMsg([
                'user_id'=>$orderInfo['user_id'],'erp_order_sn'=>$orderInfo['erp_order_sn'], 'exception'=>trim($exceptionTitle,','),
                'bussiness_name'=>$bussinessName,
            ]);

            DB::connection('SUPPLYCHAIN')->commit();
        }catch (\Exception $exception){
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($exception->getMessage());
        }

        return [];
    }

    public function getAllExceptionV2($requestData)
    {
        return AskCustomerExceptionModel::where('status',1)->pluck('content','ask_customer_exception_id');
    }


    public function getErpOrderSnOrderGoodsSn($requestData)
    {
        if (empty($orderId = OrderModel::where('erp_order_sn',$requestData['erp_order_sn'])->value('order_id'))){
            throw new \Exception('该入仓号暂无商品数据');
        }
        return OrderGoodsModel::where('order_id',$orderId)->select('goods_type','order_goods_id')->get()->toArray();
    }

    public function getExceptionDataV2($data)
    {
        if (empty($erpOrderSn = $data['erp_order_sn'])){
            throw new \Exception('该入仓号暂无商品数据');
        }

        if (empty($askCustomerExceptionId = $data['ask_customer_exception_id'])){
            throw new \Exception('ask_customer_exception_id is null');
        }

        if (empty($orderId = OrderModel::where('erp_order_sn',$erpOrderSn)->value('order_id'))){
            throw new \Exception('该入仓号暂无商品数据');
        }

        $dataField = AskCustomerExceptionModel::where('ask_customer_exception_id',$askCustomerExceptionId)->value('data_field');
        if (empty($returnData = OrderGoodsModel::where('order_id',$orderId)->where('order_goods_id',$data['order_goods_id'])->select($dataField.' as order_data',
            'order_goods_id','goods_type')->orderBy('order_goods_id','desc')->first())){
            throw new \Exception('该入仓号暂无商品数据');
        }
        return $returnData;
    }
    public function getAskCustomerDetailV2($data)
    {
        //获取入仓号
        $returnData = AskCustomerProblemListModel::getList($data)['data'][0];


        //添加商品信息
        $returnData['goods_info'] = AskCustomerProblemGoodsModel::getAskCUstomerProblemListIdOrderGoods($returnData['ask_customer_problem_list_id'])['order_goods'];



        $baseInfo = AskCustomerModel::where('erp_order_sn',$returnData['erp_order_sn'])->first()->toArray();
        $returnData['order_time_cn'] = $baseInfo['order_time'] ? date('Y-m-d H:i:s', $baseInfo['order_time']) : '';
        $returnData['ask_time_cn'] = $baseInfo['ask_time'] ? date('Y-m-d H:i:s', $baseInfo['ask_time']) : '';
        $returnData['create_time_cn'] = $baseInfo['create_time'] ? date('Y-m-d H:i:s', $baseInfo['create_time']) : '';

        $returnData['is_goods_check'] = self::getIsGoodsCheck($returnData['erp_order_sn']);
        return $returnData;
    }

    public function executeJobV2($requestData)
    {
        $askCustomerInfo = AskCustomerProblemListModel::where('ask_customer_problem_list_id',$requestData['ask_customer_problem_list_id'])->first()->toArray();
        if (intval($askCustomerInfo['ask_customer_status']) === 1){
            return '该问题还未确认';
        }
        if (intval($askCustomerInfo['store_execute_status']) === 2){
            return '该问题已经执行';
        }

        AskCustomerProblemListModel::where('ask_customer_problem_list_id',$requestData['ask_customer_problem_list_id'])->update([
            'store_execute_name'=>WechatWmsLogic::getCurrentName(),
            'store_execute_time'=>time(),
            'store_execute_status'=>2,
        ]);
        (new ActionLogModel())->addLog($requestData['ask_customer_problem_list_id'],'仓库执行作业',WechatWmsLogic::getCurrentName(),6);

        return [];
    }



    static public function getUseTime($time)
    {
        $returnTime = '';
        if ($hour = intval($time/3600)){
            $returnTime = $hour.'h';
        }
        return $returnTime.intval(($time%3600)/60).'m';
    }


    public function getAskNum($data)
    {
        return [
            'ask_customer_status_one_num'=>AskCustomerProblemListModel::getWhereObj($data)->where(['ask_customer_sw_status'=>1])->count('ask_customer_problem_list_id'),
            'ask_customer_status_two_num'=>AskCustomerProblemListModel::getWhereObj($data)->where(['ask_customer_sw_status'=>2])->count('ask_customer_problem_list_id'),
        ];
    }


    static public function getIsGoodsCheck($erpOrderSn)
    {
        return OrderLogic::getOrderIsGoodsCheck(['order_id'=>OrderModel::where('erp_order_sn',$erpOrderSn)->value('order_id')]);
    }


    //发送消息
    static public function sendMsg($data)
    {
        $custom_func = '1';
        if (empty($user = UserMessageModel::where('user_id',$data['user_id'])->where('type',1)->whereRaw("FIND_IN_SET({$custom_func},custom_fun)")->pluck('send_user')->toArray())){
            if (empty($user = UserMessageModel::where('user_id',$data['user_id'])->where('type',2)->whereRaw("FIND_IN_SET({$custom_func},custom_fun)")->pluck('send_user')->pluck('send_user')->toArray())){
                if (empty($customerAccount = CustomerModel::where('user_id',$data['user_id'])->value('customer_name'))){
                    throw new \Exception('该订单客户联系方式为空');
                }else{
                    $user[] = $customerAccount;
                }
            }
        }

        if (strpos($user[0],"@")){
            $msgKey = 'supply_ask_customer_email';
            return 1;
        }else{
            $msgKey = 'supply_ask_customer_mobile';
        }

        //给客户发消息
        $time = time();
        $client = new Client();
        $response = $client->request('post', API_DOMAIN.'/msg/sendMessageByAuto',[
            'form_params'=>[
                'data'=>json_encode([
                    "erp_order_sn"=>$data['erp_order_sn'],
                    "exception"=>$data['exception'],
                    "create_time"=>date('m:d H:i',$time),
                    "shangwu_tel"=>self::getShangwuTel($data['bussiness_name'])
                ],JSON_UNESCAPED_UNICODE),
                'touser'=>json_encode($user),
                'keyword'=>$msgKey,
                "k1"=>$time,
                "k2"=>md5(md5($time).env("API_MD5_STR")),
            ]
        ]);


        if (empty($bussinessEmail = (new CmsModel())->getData('user_info',['name'=>$data['bussiness_name']],'find','email')['email'])){
            return '<EMAIL>';
        }
        //给商务发邮件
        $response = $client->request('post', API_DOMAIN.'/msg/sendMessageByAuto',[
            'form_params'=>[
                'data'=>json_encode([
                    "erp_order_sn"=>$data['erp_order_sn'],
                    "exception"=>$data['exception'],
                    "create_time"=>date('m:d H:i',$time),
                    "shangwu_tel"=>self::getShangwuTel($data['bussiness_name'])
                ],JSON_UNESCAPED_UNICODE),
                'touser'=>json_encode([$bussinessEmail]),
                'keyword'=>'supply_bussiness_ask_customer_em',
                "k1"=>$time,
                "k2"=>md5(md5($time).env("API_MD5_STR")),
            ]
        ]);

        self::sendToErpMsg([
            'number'=>$data['erp_order_sn'],
            'type'=>$data['exception'],
            'time'=>date('Y-m-d H:i:s',$time),
            'person'=>$data['bussiness_name'],
        ]);
    }

    static public function getShangwuTel($salesName)
    {
        $mobile = (new CmsModel())->getData('user_info',['name'=>$salesName],'find','mobile')['mobile'];
        if (empty($mobile)){
            return '0755-83237919';
        }
        return $mobile;
    }


    //给erp发送消息
    static public function sendToErpMsg($data)
    {
        return 1;
        $client = new Client();
        $response = $client->request('post', Config('website.api_domain').'supply/erp_push/sendAskGuestMsg',[
            'form_params'=>$data
        ]);
    }


}