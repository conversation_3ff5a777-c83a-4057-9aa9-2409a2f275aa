<?php
/**
 * Created by 2023/3/6.
 * User: Jone
 * Info: 2023/3/6
 * Time: 下午2:34
 */

namespace Supplychain\Model\Logic\WmsLogic;


use Supplychain\Controller\ErpPushController;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\Logic\CommonLogic;
use Supplychain\Model\Logic\QiyeWechatLogic\BaseLogic;
use Supplychain\Model\Logic\WechatWmsLogic;
use Supplychain\Model\OrderModel;
use Supplychain\Model\UserMessageModel;
use Supplychain\Model\WmsCustomsDeclarationReceiptModel;
use Supplychain\Model\WmsCustomsNonArrivalGoodsModel;
use GuzzleHttp\Client;

class CustomsDeclarationAndReceiptLogic
{


    //报关收货信息逻辑层


    //获取今天的报关收货签到信息
    public function getTodayCustomsDeclarationAndReceipt($data)
    {
        $todayData = (new WmsCustomsDeclarationReceiptModel())->where('date',strtotime(date('Y-m-d',strtotime($data['datetime']))))->first();

        $returnData = [
            'car_confirm_name'=>'','car_confirm_time'=>'',
            'pass_customs_name'=>'','pass_customs_time'=>'',
            'sz_getgoods_name'=>'','sz_getgoods_time'=>'',
        ];

        if (!empty($todayData)){
            $returnData['car_confirm_time'] = empty($todayData['car_confirm_time'])?'':date('Y-m-d H:i:s',$todayData['car_confirm_time']);
            $returnData['pass_customs_time'] = empty($todayData['pass_customs_time'])?'':date('Y-m-d H:i:s',$todayData['pass_customs_time']);
            $returnData['sz_getgoods_time'] = empty($todayData['sz_getgoods_time'])?'':date('Y-m-d H:i:s',$todayData['sz_getgoods_time']);

            $returnData['car_confirm_name'] = $todayData['car_confirm_name'];
            $returnData['pass_customs_name'] = $todayData['pass_customs_name'];
            $returnData['sz_getgoods_name'] = $todayData['sz_getgoods_name'];
        }

        $returnData['datetime'] = date('Y-m-d H:i:s');

        return $returnData;
    }


    //签到今天的报关收货信息
    public function signTodayCustomsDeclarationAndReceipt($wechatData)
    {
        if (empty($wechatData['type'])){
            throw new \Exception('类型不能为空');
        }


        if (empty($wechatData['datetime'])){
            $wechatData['datetime'] = date('Y-m-d');
        }

        $data = $this->getEasRegistrationOfNonArrivalGoods(['date'=>$wechatData['datetime']]);
        $type = intval($wechatData['type']);
        $date = strtotime(date('Y-m-d',strtotime($wechatData['datetime'])));

        $wmsCustomsDeclarationReceiptModel = new WmsCustomsDeclarationReceiptModel();
        $time = time();

        $saveData = [];
        $userName = WechatWmsLogic::getCurrentName();
        if ($type === 1){
            $userKey = 'car_confirm_name';
            $timeKey = 'car_confirm_time';

        }elseif ($type === 2){
            $userKey = 'pass_customs_name';
            $timeKey = 'pass_customs_time';


            $this->sendPhoneNotify($data,2);
        }else{
            $userKey = 'sz_getgoods_name';
            $timeKey = 'sz_getgoods_time';
            $this->sendPhoneNotify($data,3);
        }

        $saveData[$userKey] = $userName;
        $saveData[$timeKey] = $time;

        if(empty($wmsCustomsDeclarationReceiptModel->where('date',$date)->value('wscsrt_id'))){
            $saveData['date'] = $date;
            $wmsCustomsDeclarationReceiptModel->insertGetId($saveData);
        }else{
            $wmsCustomsDeclarationReceiptModel->where('date',$date)->update($saveData);
        }

        //查车确认没触发就触发
        if (empty($wmsCustomsDeclarationReceiptModel->where('date',$date)->value('car_confirm_time')) && ($type === 2)){
            //(new BaseLogic())->sendMsgForSignTodayCustomsDeclarationAndReceipt(['user'=>$userName,'type'=>1]);
            $wmsCustomsDeclarationReceiptModel->where('date',$date)->update(['car_confirm_time'=>$time]);
        }

        (new BaseLogic())->sendMsgForSignTodayCustomsDeclarationAndReceipt(['user'=>$userName,'type'=>$type]);

        return [];
    }

    //获取金蝶的未-来货登记
    public function getEasRegistrationOfNonArrivalGoods($wechatData)
    {
        if (empty($wechatData['date'])){
            throw  new \Exception('时间不能为空');
        }


        $easRequestData = [
            'customsDateFrom'=>$wechatData['date'],
            'customsDateTo'=>$wechatData['date'],
        ];

        $returnData = (new ErpPushController())->getFutureCargoData($easRequestData);

        if (empty($returnData['data'])){
            throw new \Exception('暂无数据');
        }

        $noArrivalErpOrderSn = (new WmsCustomsNonArrivalGoodsModel())->whereIn('erp_order_sn',array_column($returnData['data'],'entrustNo'))->pluck('wscsnnalgs_id','erp_order_sn');

        foreach ($returnData['data'] as &$value){
            if (isset($value['carSeq'])){
                $value['car_sort'] = $value['carSeq'];
            }else{
                $value['car_sort'] = '';
            }

            if (isset($noArrivalErpOrderSn[$value['entrustNo']])){
                $value['status'] = '未来货';
            }else{
                $value['status'] = '正常';
            }
        }

        return $returnData['data'];
    }

    //生成今天的未-来货登记
    public function createRegistrationOfNonArrivalGoods($wechatData)
    {

        if (empty($wechatData['erp_order_sn'])){
            throw  new \Exception('入仓号不能为空');
        }

        $wechatData['erp_order_sn'] = explode(',',$wechatData['erp_order_sn']);
        $wechatData['car_sort'] = explode(',',$wechatData['car_sort']);

        $date = strtotime($wechatData['datetime']);
        $wmsCustomsNonArrivalGoodsModel = new WmsCustomsNonArrivalGoodsModel();

        foreach ($wechatData['erp_order_sn'] as $key=>$erpOrderSn){
            $saveData['erp_order_sn'] = $erpOrderSn;
            $saveData['car_sort'] = $wechatData['car_sort'][$key];
            $saveData['date'] = $date;
            if (empty($wmsCustomsNonArrivalGoodsModel->where($saveData)->value('wscsnnalgs_id'))){
                $wmsCustomsNonArrivalGoodsModel->insertGetId($saveData);
            }
        }
        return [];
    }

    //获取未来货收货统计
    public function getCustomsDeclarationAndReceiptStatistics($wechatData)
    {
        $beginTime = strtotime($wechatData['date']);
        $endTime = strtotime('next month',$beginTime);

        $list = (new WmsCustomsDeclarationReceiptModel())
            ->where('date','>=',$beginTime)->where('date','<',$endTime)
            ->select('date','car_confirm_time','pass_customs_time','sz_getgoods_time')->orderBy('date','desc')->get()->toArray();

        $wmsCustomsNonArrivalGoodsModel = new WmsCustomsNonArrivalGoodsModel();
        foreach ($list as &$value){
            $value['no_num'] = $wmsCustomsNonArrivalGoodsModel->where('date',$value['date'])->count('wscsnnalgs_id');
            $value['date'] = date('Y-m-d',$value['date']);
            $value['car_confirm_time'] = empty($value['car_confirm_time'])?'':date('H:i:s',$value['car_confirm_time']);
            $value['pass_customs_time'] = empty($value['pass_customs_time'])?'':date('H:i:s',$value['pass_customs_time']);
            $value['sz_getgoods_time'] = empty($value['sz_getgoods_time'])?'':date('H:i:s',$value['sz_getgoods_time']);
        }

        return $list;
    }


    //发送短信通知
    public function sendPhoneNotify($data,$type)
    {
        return 1;
        $erpOrderSnArr = array_column($data,'entrustNo');

        if (empty($erpOrderSnArr)){
            return 1;
        }

        $userIdArr = array_filter(OrderModel::whereIn('erp_order_sn',$erpOrderSnArr)->pluck('user_id')->toArray());

        if (empty($userIdArr)){
            return 1;
        }

        $datetime = date('Y-m-d H:i:s');

        foreach ($userIdArr as $userId){

            $user = [];
            $custom_func = '1';
            if (empty($user = UserMessageModel::where('user_id',$userId)->where('type',1)->whereRaw("FIND_IN_SET({$custom_func},custom_fun)")->pluck('send_user')->toArray())){
                if (empty($user = UserMessageModel::where('user_id',$userId)->where('type',2)->whereRaw("FIND_IN_SET({$custom_func},custom_fun)")->pluck('send_user')->toArray())){
                    if (empty($customerAccount = CustomerModel::where('user_id',$userId)->value('customer_name'))){
                        continue;
                    }else{
                        $user[] = $customerAccount;
                    }
                }
            }


            if (strpos($user[0],"@")){
                continue;
            }


            if (CommonLogic::testOrOnline(true,false)){
                $user = ['***********','***********'];
            }

            $orderSnStr = trim('/',implode('/',OrderModel::whereIn('erp_order_sn',$erpOrderSnArr)->where('user_id',$user)->pluck('erp_order_sn')->toArray()));

            //通关确认模版：
            //尊敬的客户:您 $_入仓号1/$_入仓号2 订单，已于$_通关确认时间(日时分) 完成深圳通关 ，请知悉。客服电话0755-********【猎芯供应链】
            //深圳收货确认模版：
            //尊敬的客户:您 $_入仓号1/$_入仓号2 订单，已于$_收货确认确认时间(日时分) 完成深圳入库 ，仓库将尽快安排发货/配送服务。客服电话0755-********【猎芯供应链】

            if ($type === 2){
                $data['data']['content'] = '尊敬的客户:您 '.$orderSnStr.' 订单，已于通关确认时间('.$datetime.') 完成深圳通关 ，请知悉。客服电话0755-********【猎芯供应链】';
            }else{
                $data['data']['content'] = '尊敬的客户:您 '.$orderSnStr.' 订单，收货确认确认时间('.$datetime.') 完成深圳入库 ，请知悉。客服电话0755-********【猎芯供应链】';
            }

            //给客户发消息
            sendMsg('supply_customs_confirm_mobile',$data,$user);
        }





    }


}