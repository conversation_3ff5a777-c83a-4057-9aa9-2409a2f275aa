<?php

namespace Supplychain\Model\Logic\WmsLogic;

use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\Logic\WechatWmsLogic;
use Supplychain\Model\WmsTallyModel;
use Supplychain\Model\WmsTallyTagModel;
use Supplychain\Model\WmsTrayBoxScanDetailModel;

class TallyTagLogic
{
    // 新增标签
    public function alterTallyTagBySubBoxQty($data)
    {
        if(empty($sub_box_qty = $data['sub_box_qty'])){
            $sub_box_qty = 1;
        }
        // 当箱数没有修改的清关下 不需要作废生成新标签
        if (WmsTallyModel::where('box_sn', $data['box_sn'])->exists()) {
            if (WmsTallyTagModel::where('source_tag_no', $data['box_sn'])->where('tag_status', 0)->exists()) {
                $src_box_qty = WmsTallyModel::where('box_sn', $data['box_sn'])->value('sub_box_qty');
                if (!empty($src_box_qty) && intval($src_box_qty) === intval($sub_box_qty)) {
                    // 判断有没有标签记录
                    return [];
                }
            }
        }

        //存在已扫描的箱号就不让作废
        if ($scanTagNo = WmsTallyTagModel::where('source_tag_no', $data['box_sn'])->where('scan_status',1)->where('tag_status',0)->value('tag_no')){
            throw new \Exception($scanTagNo."已扫描，不允许修改,请先取出再修改");
        }

        DB::connection('SUPPLYCHAIN')->beginTransaction();
        try {

            //同时作废掉未装载记录
            if ($oldTagNo = WmsTallyTagModel::where('source_tag_no', $data['box_sn'])->where('tag_status',0)->pluck('tag_no')->toArray()){
                WmsTrayBoxScanDetailModel::whereIn('box_sn',$oldTagNo)->delete();
            }


            // 先废除已有的箱号
            WmsTallyTagModel::where('source_tag_no', $data['box_sn'])->update(['tag_status' => -1]);
            WmsTallyModel::where('box_sn', $data['box_sn'])->update(['sub_box_qty' => $sub_box_qty]);
            for ($i = 0; $i <  $sub_box_qty; $i++) {
                $insertData = [
                    'source_tag_no'=>$data['box_sn'],
                    'tag_no'=> $sub_box_qty == 1 ? $data['box_sn'] : $data['box_sn'].'-'.($i+1).'/'.$sub_box_qty,
                    'tag_type' => 0,
                    'tag_status' => 0,
                    'tag_print_times' => 0,
                    'print_time' => 0,
                    'printer_name' => '',
                    'scan_status' => 0,
                    'scan_time' => 0,
                    'scan_name' => '',
                    'tray_id' => 0,
                    'create_time' => time(),
                    'creator_name' => WechatWmsLogic::getCurrentName()
                ];
                WmsTallyTagModel::insertGetId($insertData);
            }
            DB::connection('SUPPLYCHAIN')->commit();
        } catch (\Exception $exception){
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($exception->getMessage());
        }
        return [];
    }

    // 获取待打印的 标签列表
    public function getPrintScanList($data)
    {
        $box_sn = $data['box_sn'];
        $list = WmsTallyTagModel::where('source_tag_no', $box_sn)
            ->where('tag_status', 0)
            ->select('tag_no')
            ->get()
            ->toArray();
        if (empty($list)) {
            return [];
        }
        return array_column($list, 'tag_no');
    }

    public function updateTagPrintTimes($data)
    {
        $tag_no = $data['tag_no'];
        $tag_nos = array_filter(explode(',', $tag_no));
        if (empty($tag_nos)) {
            throw new \Exception('标签号为空');
        }
        WmsTallyTagModel::whereIn('tag_no', $tag_nos)
            ->where('tag_status', 0)
            ->update([
                'tag_print_times' => DB::raw('tag_print_times + 1'),
                'print_time' => time(),
                'printer_name' => WechatWmsLogic::getCurrentName(),
            ]);

        return [];
    }
}