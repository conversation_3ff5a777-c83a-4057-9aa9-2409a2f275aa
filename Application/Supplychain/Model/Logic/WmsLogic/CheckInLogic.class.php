<?php
/**
 * Created by 2022/2/24.
 * User: Joneq
 * Info: 2022/2/24
 * Time: 下午2:25
 */
namespace Supplychain\Model\Logic\WmsLogic;
use GuzzleHttp\Client;
use Supplychain\Controller\ErpPushController;
use Supplychain\Model\ActionLogModel;
use Supplychain\Model\AskCustomerExceptionModel;
use Supplychain\Model\AskCustomerModel;
use Supplychain\Model\AskCustomerProblemGoodsModel;
use Supplychain\Model\AskCustomerProblemListModel;
use Supplychain\Model\BaoGuanOrderListModel;
use Supplychain\Model\CmsModel;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\Logic\WechatWmsLogic;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\WmsCheckinDetailModel;
use Supplychain\Model\WmsCheckinListModel;
use Supplychain\Model\WmsCheckinUnusualListModel;
use Supplychain\Model\WmsSortGoodsListModel;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\WmsTodayTrayModel;
use Supplychain\Model\WmsTrayBoxScanDetailModel;
use Supplychain\Model\WmsTrayListModel;
use Supplychain\Model\WmsTrayPrintLabelModel;

class CheckInLogic
{
    //从erp获取收货登记列表
    public function getCheckInList($data)
    {
        if (empty($data['number'])){
            unset($data['number']);
        }
        return (new ErpPushController())->getGoodsRegisterData($data);
    }

    //异常收货登记列表
    public function getUnusualSortGoodsList($data)
    {
        $data['erp_order_sn'] = $data['number'];
        $data['sort_goods_status'] = 3;
        $obj = (new WmsSortGoodsListModel())->getWhereObj($data);
        $returnData = $obj->orderBy('wsstgslt_id','desc')
            ->select('is_goods_check','wsstgslt_id','erp_order_sn','supplier_name','create_time','sort_goods_remark')
            ->get()
            ->toArray();
        foreach ($returnData as &$val){
            $val['is_goods_check_cn'] = empty($val['is_goods_check'])?'非商检':'商检';
            $val['create_time_cn'] =  empty($val['create_time'])?'':date('Y-m-d H:i:s',$val['create_time']);
            $status = AskCustomerProblemListModel::where('erp_order_sn',$val['erp_order_sn'])->value('ask_customer_status');
            $val['ask_customer_status_cn'] = array_get(AskCustomerProblemListModel::$askCustomerStatus,empty($status)?1:$status);
        }
        return $returnData;
    }


    //从erp获取理货登记列表
    public function getGoodsArrangeData($data)
    {
        if (empty($data['number'])){
            unset($data['number']);
        }
        return (new ErpPushController())->getGoodsArrangeData($data);
    }

    //新增收货登记
    public function addCheckIn($allData)
    {


        $orderInfo = OrderModel::where('erp_order_sn',$allData['erp_order_sn'])->first();

        $orderInfo['company_id'] = isset($orderInfo['company_id'])?$orderInfo['company_id']:0;


        $insertData = [];
        $insertData['delivery_method'] = $allData['delivery_method'];
        $insertData['logistics_company'] = $allData['logistics_company'];
        $insertData['logistics_number'] = $allData['logistics_number'];
        $insertData['check_in_pic'] = empty($allData['check_in_pic'])?'':implode(',',$allData['check_in_pic']);
        $insertData['check_in_remark'] = $allData['check_in_remark'];
        $insertData['supplier_name'] = isset($orderInfo['supplier_name'])?$orderInfo['supplier_name']:"";
        $insertData['company_name'] = empty(CompanyModel::where('company_id',$orderInfo['company_id'])->value('company_full_name'))?"":CompanyModel::where('company_id',$orderInfo['company_id'])->value('company_full_name');
        $insertData['purchase_remark'] = '';
        $insertData['check_in_status'] = 2;
        $insertData['check_in_time'] = time();
        $insertData['check_in_user'] =  WechatWmsLogic::getCurrentName();


        //如果没找到商务，默认周红丹
        if (empty($bussinessName = CustomerModel::where('company_id',$orderInfo['company_id'])->value('follow_people'))){
            $bussinessName = '周红丹';
        }

        $insertData['bussiness_name'] = $bussinessName;
        $insertData['erp_order_sn'] = $allData['erp_order_sn'];
        $insertData['total_num'] = $allData['total_num'];
        if (empty($allData['plate_num'])) {
            $insertData['plate_num'] = 0;
        } else {
            $insertData['plate_num'] = $allData['plate_num'];
        }
        $insertData['unit'] = $allData['unit'];
        $insertData['order_time'] = $orderInfo['create_time']?strtotime($orderInfo['create_time']):0;



        //同步数据到eas
        $returnData = (new ErpPushController())->createGoodsRegisterData([
            'entrustNo'=>$insertData['erp_order_sn'],
            'registerTime'=>date('Y-m-d H:i:s',$insertData['check_in_time']),
            'recType'=>array_get(WmsCheckinListModel::$deliveryMethod,$insertData['delivery_method']),
            'company'=>$insertData['logistics_company'],
            'logisticsNo'=>$insertData['logistics_number'],
            'qty'=>intval($insertData['total_num']),
            'plateQty' => intval($insertData['plate_num']),
            'unit'=>$insertData['unit'],
            'imageUrl'=>$insertData['check_in_pic'],
            'remark'=>$insertData['check_in_remark'],
            'goodsTime'=>WmsCheckinListModel::where('erp_order_sn',$insertData['erp_order_sn'])->where('check_in_status',2)->count('wscnlt_id')+1
        ]);
        $insertData['eas_id'] = $returnData['id'];


        $insertId = WmsCheckinListModel::insertGetId($insertData);

        $orderGoodsData = OrderGoodsModel::where('order_id',$orderInfo['order_id'])
            ->select('goods_title','goods_type','brand','origin','numbers','measurement')
            ->get()
            ->toArray();

        foreach ( $orderGoodsData as $key=>$value){
            $entryInsertData['brand'] = $value['brand'];
            $entryInsertData['goods_type'] = $value['goods_type'];
            $entryInsertData['order_num'] = $value['numbers'];
            $entryInsertData['measurement'] = $value['measurement'];
            $entryInsertData['goods_title'] = $value['goods_title'];
            $entryInsertData['origin'] = $value['origin'];
            $entryInsertData['wscnlt_id'] = $insertId;
            WmsCheckinDetailModel::insertGetId($entryInsertData);
        }

        if (intval($checkInNum = WmsCheckinListModel::where('erp_order_sn',$insertData['erp_order_sn'])->where('check_in_status',2)->count('wscnlt_id')) >1){
            if (BaoGuanOrderListModel::where('erp_order_sn', $insertData['erp_order_sn'])->exists()) {
                // 收货管理 -$_商务-$_入仓号 $_收货次数 ，原因：已报关多次收货
                $client = new Client();
                $client->request('post', 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=80874d10-85d7-4e36-aa08-ca2dca961d24',[
                    'json'=>[
                        "msgtype"=>"text",
                        "text"=>[
                            "content"=>'收货管理 -'.$insertData['bussiness_name'].' '.$insertData['erp_order_sn'].' 收货'.($checkInNum).'次 ，原因：已报关多次收货 ',
                            "mentioned_mobile_list"=>[(new CmsModel())->getData('user_info',['name'=>$insertData['bussiness_name']],'find','mobile')['mobile']],
                        ]
                    ]
                ]);
            }
            return '该入仓号'.$insertData['erp_order_sn'].'已第'.($checkInNum).'次收货登记，请核对当前入仓号';
        }

        return '操作成功';
    }

    //新增理货
    public function addSortGodds($allData)
    {
        if (WmsSortGoodsListModel::where('erp_order_sn',$allData['number'])->value('erp_order_sn')){
            throw new \Exception('该入仓号已经提交理货');
        }

        //获取传来的entrys所有数据
        $data = $allData['entrys'];
        $baseInfo = $allData['baseinfo'];

        //同步给erp的数据
        $syncerpData = [
            'id'=>$data['id'],
            'num'=>$allData['sort_goods_num']
        ];

        //拿出里面的entrys数据
        $entrys = $data['entrys'];
        $sortGoodsImg = '';
        foreach ($entrys as $key=>$value){
            $entrys[$key]['weight'] = $baseInfo[$key]['weight'];
            $entrys[$key]['pic'] = $baseInfo[$key]['pic'];

            $syncerpData['entry'][] = ['entryID'=>$entrys[$key]['entryID'],'netWeight'=>$baseInfo[$key]['weight']];
            if (!empty($baseInfo[$key]['pic'])){
                $sortGoodsImg .= $baseInfo[$key]['pic'].',';
            }
        }

        $insertData['erp_order_sn'] = $allData['number'];
        $insertData['sort_goods_img'] = trim($sortGoodsImg,',');
        $insertData['sort_goods_remark'] = $allData['sort_goods_remark'];
        $insertData['sort_goods_num'] = $allData['sort_goods_num'];
        $insertData['sort_goods_unit'] = $allData['sort_goods_unit'];
        $insertData['wstylt_id'] = $allData['wstylt_id'];
        $insertData['sort_goods_user'] = WechatWmsLogic::getCurrentName();
        $insertData['sort_goods_time'] = time();

        $insertData['supplier_name'] = $data['supplier'];
        $insertData['company_name'] = $data['customer'];
        $insertData['entrys'] = \GuzzleHttp\json_encode($entrys);
        $insertData['is_goods_check'] = $allData['isInspOrg']?1:0;

        //更新理货
        (new ErpPushController())->updateGoodsArrangeData($syncerpData);

        return WmsSortGoodsListModel::insertGetId($insertData);
    }





    //新增异常收货登记
    public function addUnusualCheckIn($data)
    {
        $insertField = [
            'delivery_method','logistics_company','logistics_number','info_pic',
            'check_in_remark','erp_order_sn','supplier_name','unusual_reason',
            'unusual_num','unit'
        ];

        if (!empty($data['info_pic'])){
            $data['info_pic'] =  implode(',',$data['info_pic']);
        }

        foreach ($data as $key=>$value){
            if (!in_array($key,$insertField)){
                unset($data[$key]);
            }

        }
        $data['abnormal_serial_num'] = WmsCheckinUnusualListModel::getSerialNum();
        $data['check_in_user'] = WechatWmsLogic::getCurrentName();;
        $data['check_in_time'] =$data['create_time'] = time();
        WmsCheckinUnusualListModel::insertGetId($data);
        return [];
    }

    //新增问客
    public function addAskCustomer($param)
    {
        //判断一个问题只能提交一次
        $repeat = [];

//        foreach ($param['form'] as $value){
//            if (isset($repeat[$value['ask_customer_exception_id']]) ){
//                throw new \Exception('一个问题只能提交一次,重复的请删除');
//            }
//            $repeat[$value['ask_customer_exception_id']] = 1;
//        }


        $data['erp_order_sn'] = $param['erp_order_sn'];
        $data['user_name'] = WechatWmsLogic::getCurrentName();


        $exceptionTitle = '';

        try{
            DB::connection('SUPPLYCHAIN')->beginTransaction();

            $orderInfo = OrderModel::where('erp_order_sn',$data['erp_order_sn'])->first();
            if (empty($orderInfo)){
                throw new \Exception('该订单不存在');
            }
            $orderInfo = $orderInfo->toArray();
            $askCustomerInsertData['erp_order_sn'] = $data['erp_order_sn'];
            $askCustomerInsertData['company_name'] = $param['entrys']['customer'];
            $askCustomerInsertData['order_time'] = strtotime($param['entrys']['orderDate']);
            $askCustomerInsertData['ask_time'] = time();
            $askCustomerInsertData['create_time'] = time();
            $askCustomerInsertData['create_user_name'] = $data['user_name'];
            $askCustomerInsertData['user_id'] = $orderInfo['user_id'];

            //新增理货异常
            $sortGoodsListInsetData = [
                'erp_order_sn'=>$data['erp_order_sn'],
                'is_goods_check'=>$param['entrys']['customer']?1:0,
                'company_name'=>$param['entrys']['customer'],
                'supplier_name'=>$param['entrys']['supplier'],
                'busssiness_name'=>$data['user_name'],
                'sort_goods_remark'=>$param['entrys']['remark'],
                'sort_goods_status'=>3,
                'create_time'=>time()
            ];

            WmsSortGoodsListModel::insertGetId($sortGoodsListInsetData);

            //新增问客
            $askCustomerId = AskCustomerModel::insertGetId($askCustomerInsertData);

            foreach ($param['form'] as $key=>$value){
                $askCUstomerExceptionInfo = AskCustomerExceptionModel::where('ask_customer_exception_id',$value['ask_customer_exception_id'])->first()->toArray();
                $askCustomerProblemInsertData['ask_customer_id'] = $askCustomerId;
                $askCustomerProblemInsertData['create_user_name'] = $data['user_name'];

                if (empty($bussinessName = CustomerModel::where('company_id',$orderInfo['company_id'])->value('follow_people'))){
                    $bussinessName = '周红丹';
                }

                $askCustomerProblemInsertData['bussiness_name'] = $bussinessName;

                if (empty($value['sort_goods_img'])){
                    throw new \Exception($askCUstomerExceptionInfo['content'].'上传图片不能为空');
                }

                $askCustomerProblemInsertData['upload_img'] = implode(',',$value['sort_goods_img']);

                $exceptionTitle .= $exceptionTitle.$askCUstomerExceptionInfo['content'].'问题';



                $askCustomerProblemInsertData['create_time']  = time();
                $askCustomerProblemInsertData['ask_customer_remark'] = $param['entrys']['remark'];
                $askCustomerProblemInsertData['ask_customer_exception_id'] = $value['ask_customer_exception_id'];

                $askCustomerProblemInsertData['user_id'] = $orderInfo['user_id'];
                $askCustomerProblemInsertData['erp_order_sn'] = $orderInfo['erp_order_sn'];
                $askCustomerProblemInsertData['ask_customer_sn'] = $orderInfo['order_sn'];
                $askCustomerProblemInsertData['login_type'] = 2;
                $askCustomerProblemId = AskCustomerProblemListModel::insertGetId($askCustomerProblemInsertData);

                (new ActionLogModel())->addLog($askCustomerProblemId,'仓库发起问题',$data['user_name'],6);

                foreach ($value['exception'] as $k=>$v){

                    if (empty($v['val'])){
                        continue;
                    }

                    $askCustomerProblemGoodsData['ask_customer_problem_list_id'] = $askCustomerProblemId;
                    $askCustomerProblemGoodsData['update_time'] = time();
                    $askCustomerProblemGoodsData['update_user_name'] = $data['user_name'];
//                    $askCustomerProblemGoodsData['order_goods_id'] = $v['order_goods_id'];
                    $askCustomerProblemGoodsData['goods_type'] = $value['exceptionlist'][$k]['model'];
                    $askCustomerProblemGoodsData['order_result'] = $value['exceptionlist'][$k]['order_data'];
                    $askCustomerProblemGoodsData['store_result'] = $v['val'];
                    $askCustomerProblemGoodsId = AskCustomerProblemGoodsModel::insertGetId($askCustomerProblemGoodsData);
                }
            }

            DB::connection('SUPPLYCHAIN')->commit();
        }catch (\Exception $exception){
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($exception->getMessage());
        }

        return [];
    }


    static public function addAskCustomerProblem($param)
    {
        $param['orderGoods'] = json_decode($param['orderGoods'],true);
        $data['erp_order_sn'] = $param['erp_order_sn'];
        $data['user_name'] = $param['user_name'];
        $data['upload_img'] = $param['upload_imgs'];
        $problem = [];

        foreach ($param['orderGoods'] as $key=>$value){
            if (empty($value)){
                return '问题'.($key+1).'异常不能为空';
            }
        }

        foreach ($param['ask_customer_exception_id'] as $key=>$value){

            // if (self::where('ask_customer_exception_id',$value)->where('erp_order_sn',$data['erp_order_sn'])->value('ask_customer_problem_list_id')){
            //     return '该订单'.AskCustomerExceptionModel::where('ask_customer_exception_id',$value)->value('content').'已经存在，请确认';
            // }

            $problem[$key]['ask_customer_remark'] = $param['ask_customer_remark'][$key];
            $problem[$key]['ask_customer_exception_id'] = $param['ask_customer_exception_id'][$key];
            $problem[$key]['upload_img'] = $param['upload_img'][$key];
            $problem[$key]['orderGoods'] = [];

            $is_all_execption = false; // 是否存在异常

            foreach ($param['orderGoods'][$key] as $v){
                if (empty($v['store_result'])) continue;

                $problem[$key]['orderGoods'][] = $v;

                $is_all_execption = true;
            }


            if (!$is_all_execption) {
                $exp_content = AskCustomerExceptionModel::where('ask_customer_exception_id',$value)->value('content');
                return '问题'.($key+1).'异常类型（'.$exp_content.'）全部为正常，请检查数据';
            }
        }

        $data['problem'] = $problem;

        $exceptionTitle = '';

        try{
            DB::connection('supply')->beginTransaction();

            $orderInfo = OrderModel::where('erp_order_sn',$data['erp_order_sn'])->first()->toArray();
            if (empty($orderInfo)){
                throw new \Exception('该订单不存在');
            }
            $askCustomerInsertData['erp_order_sn'] = $data['erp_order_sn'];
            $askCustomerInsertData['company_name'] = CompanyModel::where('company_id',$orderInfo['company_id'])->value('company_full_name');
            $askCustomerInsertData['order_time'] = $orderInfo['create_time'];
            $askCustomerInsertData['upload_img'] = $data['upload_img'];
//            $askCustomerInsertData['ask_time'] = strtotime($data['ask_time']);
            $askCustomerInsertData['create_time'] = time();
            $askCustomerInsertData['create_user_name'] = $data['user_name'];
            $askCustomerInsertData['user_id'] = $orderInfo['user_id'];


            //新增问客
            $askCustomerId = AskCustomerModel::insertGetId($askCustomerInsertData);

            foreach ($data['problem'] as $key=>$value){
                $askCUstomerExceptionInfo = AskCustomerExceptionModel::where('ask_customer_exception_id',$value['ask_customer_exception_id'])->first()->toArray();
                $askCustomerProblemInsertData['ask_customer_id'] = $askCustomerId;
                $askCustomerProblemInsertData['create_user_name'] = $data['user_name'];

                if (empty($bussinessName = CustomerModel::where('company_id',$orderInfo['company_id'])->value('follow_people'))){
                    $bussinessName = '周红丹';
                }

                $askCustomerProblemInsertData['bussiness_name'] = $bussinessName;
                $askCustomerProblemInsertData['upload_img'] = $value['upload_img'];

                $exceptionTitle .= $exceptionTitle.$askCUstomerExceptionInfo['content'].'问题';

                if (empty($value['upload_img'])){
                    throw new \Exception($askCUstomerExceptionInfo['content'].'上传图片不能为空');
                }

                $askCustomerProblemInsertData['create_time']  = time();
                $askCustomerProblemInsertData['ask_customer_remark'] = $value['ask_customer_remark'];
                $askCustomerProblemInsertData['ask_customer_exception_id'] = $value['ask_customer_exception_id'];

                $askCustomerProblemInsertData['user_id'] = $orderInfo['user_id'];
                $askCustomerProblemInsertData['erp_order_sn'] = $orderInfo['erp_order_sn'];
                $askCustomerProblemInsertData['ask_customer_sn'] = $orderInfo['order_sn'];
                $askCustomerProblemInsertData['login_type'] = 2;
                $askCustomerProblemId = AskCustomerProblemListModel::insertGetId($askCustomerProblemInsertData);

                (new ActionLogModel())->addLog($askCustomerProblemId,'仓库发起问题',$data['user_name'],6);

                foreach ($value['orderGoods'] as $k=>$v){
                    $orderGoodsInfo = OrderGoodsModel::where('order_goods_id',$v['order_goods_id'])->first()->toArray();

                    $askCustomerProblemGoodsData['ask_customer_problem_list_id'] = $askCustomerProblemId;
                    $askCustomerProblemGoodsData['update_time'] = time();
                    $askCustomerProblemGoodsData['update_user_name'] = $data['user_name'];
                    $askCustomerProblemGoodsData['order_goods_id'] = $v['order_goods_id'];
                    $askCustomerProblemGoodsData['goods_type'] = $orderGoodsInfo['goods_type'];
                    $askCustomerProblemGoodsData['order_result'] = $orderGoodsInfo[$askCUstomerExceptionInfo['data_field']];
                    $askCustomerProblemGoodsData['store_result'] = $v['store_result'];
                    $askCustomerProblemGoodsId = AskCustomerProblemGoodsModel::insertGetId($askCustomerProblemGoodsData);
                }
            }


//            self::sendMsg([
//                'user_id'=>$orderInfo['user_id'],'erp_order_sn'=>$orderInfo['erp_order_sn'], 'exception'=>trim($exceptionTitle,','),
//                'bussiness_name'=>$bussinessName,
//            ]);

            DB::connection('supply')->commit();
        }catch (\Exception $exception){
            DB::connection('supply')->rollback();
            return $exception->getMessage();
        }


        //发送微信模板消息 如果当前这个用户有未使用的模板，并且没超过七天的话
//        if($formIdData  = WechatFormidModel::where('user_id',$orderInfo['user_id'])->where('formid','wx47674efb6eef06e1')->first()){
//            (new WechatSendMessageLogic())->sendTemplateData(
//                [
//                    'keyword1'=>$orderInfo['order_sn'],
//                    'keyword2'=>'待处理',
//                    'keyword3'=>$askCustomerInsertData['erp_order_sn'].'问客异常请确认',
//                    'keyword4'=>date('Y-m-d H:i:s'),
//                    'openid'=>$formIdData['openid'],
//                ]
//            );
//        }


        return [];
    }



    //获取问客详情
    public function getAskCustomerDetail($data)
    {
        //获取入仓号
        $data['ask_customer_status'] = 1;
        $data['store_execute_status'] = 1;
        $returnData = AskCustomerProblemListModel::getList($data)['data'];

        //添加商品信息
        foreach ($returnData as $key=>$value){
            $returnData[$key]['goods_info'] = AskCustomerProblemGoodsModel::getAskCUstomerProblemListIdOrderGoods($value['ask_customer_problem_list_id'])['order_goods'];
        }

        $baseInfo = AskCustomerModel::where('erp_order_sn',$data['erp_order_sn'])->first()->toArray();
        $baseInfo['order_time_cn'] = $baseInfo['order_time'] ? date('Y-m-d H:i:s', $baseInfo['order_time']) : '';
        $baseInfo['ask_time_cn'] = $baseInfo['ask_time'] ? date('Y-m-d H:i:s', $baseInfo['ask_time']) : '';
        $baseInfo['create_time_cn'] = $baseInfo['create_time'] ? date('Y-m-d H:i:s', $baseInfo['create_time']) : '';
        $baseInfo['is_goods_check'] = WmsSortGoodsListModel::where('erp_order_sn',$data['erp_order_sn'])->value('is_goods_check');
        $baseInfo['is_goods_check_cn'] = empty($baseInfo['is_goods_check'])?'非商检':'商检';
        $baseInfo['is_goods_check_cn'] = empty($baseInfo['is_goods_check'])?'非商检':'商检';
        return ['detail'=>$returnData,'base'=>$baseInfo];
    }

    public function rePackageList($requestData)
    {

        if (!isset($requestData['today']) || $requestData['today'] == ''){
            $requestData['today'] = date('Y-m-d');
            if (isset($requestData['erp_order_sn']) && $requestData['erp_order_sn'] !== '') {
                $requestData['today'] = '';
            }
        }

        $obj = (new WmsTodayTrayModel())->getWhereObj($requestData);

        if (isset($requestData['erp_order_sn']) && $requestData['erp_order_sn'] !== ''){
            $wstytyArr = (new WmsTrayBoxScanDetailModel())->getWhereObj(['base_erp_order_sn'=>$requestData['erp_order_sn']])->where('wstyty_id','!=',0)->groupBy('wstyty_id')->pluck('wstyty_id')->toArray();
            $obj = $obj->whereIn('wstyty_id',$wstytyArr);
        }

        $returnData = $obj->orderBy('wstyty_id','desc')->paginate($requestData['limit'], ['*'], 'page',$requestData['page'])->toArray();

        foreach ($returnData['data'] as $key=>&$value){

            $lastInfo = (new WmsTrayBoxScanDetailModel())->getWhereObj(['wstyty_id'=>$value['wstyty_id']])->orderBy('scan_time','desc')->first();

            $value['print_type_cn'] =  array_get(WmsTrayListModel::$trayType,intval($value['tray_type']));
            $value['print_type'] = $value['tray_type'];
            $value['create_time_cn'] = $value['create_time'] ? date('Y-m-d H:i:s', $value['create_time']) : '';
            $value['last_scan_time_cn'] = isset($lastInfo['scan_time']) ? date('Y-m-d H:i:s', $lastInfo['scan_time']) : '';
            $value['erp_order_sn'] = $lastInfo['erp_order_sn'];
            $value['base_erp_order_sn'] = $lastInfo['base_erp_order_sn'];

            //托盘名称
            $trayInfo = WmsTodayTrayModel::where('wstyty_id',$value['wstyty_id'])->first();
            $value['tray_name'] = $trayInfo['tray_name']?$trayInfo['tray_name']:'请选择';
            $value['warehouse'] = $trayInfo['warehouse'];
            $value['audit_status_cn'] = array_get(WmsTodayTrayModel::$auditStatus,intval($trayInfo['audit_status']));
            $value['audit_time_cn'] = $trayInfo['audit_time'] ? date('Y-m-d H:i:s', $trayInfo['audit_time']) : '';


            $trayWhere = ['wstyty_id'=>$value['wstyty_id']];
            //已经放置的入仓号
            $value['have_load_erp_order_sn_num'] = count((new WmsTrayBoxScanDetailModel())->getWhereObj($trayWhere)->groupBy('erp_order_sn')->pluck('wstybxsndl_id'));



            if (empty($sum = WmsTrayBoxScanDetailModel::where('is_del',0)->where('is_scan',1)->where('wstyty_id',$value['wstyty_id'])->where('print_type',1)->sum('sum'))){
                $sum = 0;
            }
            $sum +=  WmsTrayBoxScanDetailModel::where('is_del',0)->where('is_scan',1)->where('wstyty_id',$value['wstyty_id'])->where('print_type',2)->count('wstybxsndl_id');
            //合计已扫描箱子数量
            $value['have_scan_load_box_num'] = $sum;

            //合计货物箱子数量
            $value['have_load_box_num'] = WmsTrayBoxScanDetailModel::getTraySumBox($value['wstyty_id']);

            //已经扫描的入仓号
            $erpOrderSnArr = (new WmsTrayBoxScanDetailModel())->getWhereObj($trayWhere)->groupBy('erp_order_sn')->pluck('erp_order_sn')->toArray();
            $value['have_scan_erp_order_sn_str'] = implode(',',$erpOrderSnArr);

            //入仓号扫描状态
            $value['scan_status_cn'] = (new WmsTrayBoxScanDetailModel())->checkScanNum(WmsTrayBoxScanDetailModel::getNoScanErpOrderSnInSumBox($erpOrderSnArr), WmsTrayBoxScanDetailModel::getErpOrderSnInSumBox($erpOrderSnArr));

            //托盘装载状态
            if ($value['scan_status_cn'] == '未扫描'){
                $value['tray_load_status_cn'] = '待装载';
            }else{
                $value['tray_load_status_cn'] = '已装载';
            }

            //扫描核验人员
            $value['scan_user_str'] = implode(',',array_filter((new WmsTrayBoxScanDetailModel())->getWhereObj($trayWhere)->groupBy('scan_user_name')->pluck('scan_user_name')->toArray()));
        }

        return $returnData;
    }

    public function rePackageListOld($requestData)
    {
        if (!isset($requestData['today']) || $requestData['today'] == ''){
            $requestData['today'] = date('Y-m-d');
        }

        $obj = (new WmsTrayBoxScanDetailModel())->getWhereObj($requestData);

        $returnData = $obj->orderBy('wstybxsndl_id','desc')
            ->where('wstyty_id','!=',0)
            ->groupBy('wstyty_id')
            ->select('wstyty_id','create_time','scan_time','print_type','sum')
            ->paginate($requestData['limit'], ['*'], 'page',$requestData['page'])->toArray();
        $data = $returnData['data'];


        foreach ($data as $key=>&$value){
            $value['print_type_cn'] =  array_get(WmsTrayPrintLabelModel::$printType,$value['print_type']);
            $value['create_time_cn'] = $value['create_time'] ? date('Y-m-d H:i:s', $value['create_time']) : '';
            $value['last_scan_time_cn'] = $value['scan_time'] ? date('Y-m-d H:i:s', $value['scan_time']) : '';

            //托盘名称
            $trayInfo = WmsTodayTrayModel::where('wstyty_id',$value['wstyty_id'])->first();
            $value['tray_name'] = $trayInfo['tray_name']?$trayInfo['tray_name']:'请选择';
            $value['audit_status_cn'] = array_get(WmsTodayTrayModel::$auditStatus,intval($trayInfo['audit_status']));
            $value['audit_time_cn'] = $trayInfo['audit_time'] ? date('Y-m-d', $trayInfo['audit_time']) : '';



            $trayWhere = array_merge($requestData,['wstyty_id'=>$value['wstyty_id']]);

            //合计货物箱子数量
            $value['have_load_box_num'] = WmsTrayBoxScanDetailModel::getTraySumBox($value['wstyty_id']);

            //已经扫描的入仓号
            $value['have_scan_erp_order_sn_str'] = implode(',',(new WmsTrayBoxScanDetailModel())->getWhereObj($trayWhere)->groupBy('erp_order_sn')->pluck('erp_order_sn')->toArray());
            //已经扫描的数量
            $value['scan_status_cn'] = (new WmsTrayBoxScanDetailModel())->checkScanNum(
                count((new WmsTrayBoxScanDetailModel())->getWhereObj($trayWhere)->groupBy('erp_order_sn')->where('is_scan',0)->pluck('wstybxsndl_id')->toArray()),
                $value['have_load_box_num']);

        }
        $returnData['data'] = $data;
        return $returnData;
    }


    public function erpOrderSnCodeManage($requestData)
    {
        if (isset($requestData['erp_order_sn']) && $requestData['erp_order_sn'] !== '') {
            $requestData['erp_order_sn'] = explode('-',$requestData['erp_order_sn'])[0];
        }

        $obj = (new WmsTrayBoxScanDetailModel())->getWhereObj(['erp_order_sn_like'=>$requestData['erp_order_sn']]);

        $returnData = $obj->orderBy('wstyptll_id','desc')
            ->where('create_user_name',WechatWmsLogic::getCurrentName())
            ->groupBy('erp_order_sn')
            ->select('erp_order_sn','create_time','print_type')
            ->paginate($requestData['limit'], ['*'], 'page',$requestData['page'])->toArray();


        foreach ($returnData['data'] as &$value){
            $value['create_time_cn'] = date('Y-m-d H:i:s',$value['create_time']);
            $value['sum'] = (new WmsTrayBoxScanDetailModel())->getWhereObj(['erp_order_sn'=>$value['erp_order_sn']])->count('wstybxsndl_id');
            $value['print_type_cn'] =  array_get(WmsTrayPrintLabelModel::$printType,$value['print_type']);
            $value['is_repair_print'] =  (WmsTrayPrintLabelModel::where('wstyptll_id',$value['wstyptll_id'])->value('tray_remark') === '正常')?'是':'否';
            $value['start_box_code']  = WmsTrayBoxScanDetailModel::getWhereObj([])->where('erp_order_sn',$value['erp_order_sn'])->orderBy('wstybxsndl_id')->value('box_code');
            $value['end_box_code']  = WmsTrayBoxScanDetailModel::getWhereObj([])->where('erp_order_sn',$value['erp_order_sn'])->orderBy('wstybxsndl_id','desc')->value('box_code');
        }

        return $returnData;
    }

    public function delErpOrderSnCodeManage($requestData)
    {

        if (WmsTrayBoxScanDetailModel::getWhereObj([])->where('erp_order_sn',$requestData['erp_order_sn'])->where('is_scan',1)->value('wstybxsndl_id')){
            throw new \Exception('该入仓号已经装箱扫描，需要先反审【装载扫描】后才可进行作废操作');
        }

        WmsTrayBoxScanDetailModel::getWhereObj([])->where('erp_order_sn',$requestData['erp_order_sn'])->update(['is_del'=>1]);
        return 'ok';
    }

    public function delWstyptllIdErpOrderSnCodeManage($requestData)
    {
        WmsTrayBoxScanDetailModel::getWhereObj([])->where('wstyptll_id',$requestData['wstyptll_id'])->update(['is_del'=>1]);
        return 'ok';
    }

    public function getTrayHaveScanErpOrderSn($data)
    {
        if (intval($data['is_scan']) === 1){
            $baseData =  (new WmsTrayBoxScanDetailModel())
                ->getWhereObj($data)
                ->select('print_type','create_time','erp_order_sn','sort','sum','create_user_name','wstybxsndl_id','box_code')
                ->get()
                ->toArray();
        }else{
            $allErpOrderSn = (new WmsTrayBoxScanDetailModel())->getWhereObj([])->where('wstyty_id',$data['wstyty_id'])->pluck('erp_order_sn');
            $baseData =  (new WmsTrayBoxScanDetailModel())
                ->getWhereObj([])
                ->where('is_scan',0)
                ->whereIn('erp_order_sn',$allErpOrderSn)
                ->select('print_type','create_time','erp_order_sn','sort','sum','create_user_name','wstybxsndl_id','box_code')
                ->get()
                ->toArray();
        }


        if (empty($baseData)){
            throw new \Exception('暂无数据');
        }

        foreach ($baseData as $key=> &$value){

            $value['erp_order_sn_cn'] = $value['box_code'];
            $value['print_type_cn'] = array_get(WmsTrayPrintLabelModel::$printType,$value['print_type']);
            $value['create_time_cn'] = empty($value['create_time'])?'':date('Y-m-d H:i:s',$value['create_time']);
        }

        return $baseData;
    }
}