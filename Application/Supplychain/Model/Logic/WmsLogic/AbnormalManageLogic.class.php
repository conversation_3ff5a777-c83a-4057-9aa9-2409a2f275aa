<?php

namespace Supplychain\Model\Logic\WmsLogic;

use Supplychain\Model\ActionLogModel;
use Supplychain\Model\Logic\PurRequestLogic;
use Supplychain\Model\Logic\WechatWmsLogic;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\WmsAbnormalManageModel;
use Supplychain\Model\WmsAbnormalQuickExpressionsModel;
use Illuminate\Database\Capsule\Manager as DB;
use Supplychain\Model\WmsTallyAbnormalDetailModel;
use Supplychain\Model\WmsTallyModel;

class AbnormalManageLogic
{
    // 异常管理列表
    public function abnormalManageList($data)
    {
        $obj = (new WmsAbnormalManageModel())->getWhereObj($data);
        $returnData = $obj->orderBy('abnormal_id', 'desc')->paginate(isset($data['limit']) ? $data['limit'] : 10, ['*'], 'page', isset($data['page']) ? $data['page'] : 1)
            ->toArray();
        foreach ($returnData['data'] as &$val) {
            $val['abnormal_type_val'] = array_get(WmsAbnormalManageModel::$abnormalType, $val['abnormal_type']);
            $val['abnormal_status_val'] = array_get(WmsAbnormalManageModel::$abnormalStatus, $val['abnormal_status']);
            $val['tag_template_type'] = array_get(WmsAbnormalManageModel::$tagTemplateType, $val['tag_template_type']);
            $val['warehouse_confirm_time'] = empty($val['warehouse_confirm_time']) ? '' : date('Y-m-d H:i:s', $val['warehouse_confirm_time']);
            $val['salesman_handle_time'] = empty($val['salesman_handle_time']) ? '' : date('Y-m-d H:i:s', $val['salesman_handle_time']);
            $val['create_time'] = empty($val['create_time']) ? '' : date('Y-m-d H:i:s', $val['create_time']);
            $val['abnormal_pic_urls'] = empty($val['abnormal_pic_url']) ? [] : explode(',', $val['abnormal_pic_url']);
        }
        return $returnData;
    }

    public function getAbnormalNumber()
    {
        $formattedString = sprintf('YC%s%02d%02d', substr(date('Y'), 2, 4), date('m'), date('d'));
        $last_abnormal_number_info = WmsAbnormalManageModel::where('abnormal_number', 'like', $formattedString.'%')->orderBy('abnormal_number', 'desc')->first();
        if (empty($last_abnormal_number_info)){
            $count = 1;
        } else {
            $count = intval(substr($last_abnormal_number_info['abnormal_number'],-3)) + 1;
        }

        return $formattedString.sprintf('%03d', $count);
    }

    // 输入入仓号进行模糊查询，查询，单个时则直接下方展示入仓号型号明细供用户选择 多个入仓号则弹窗用户再次选择入仓号，选择后更新下方入仓号明细信息
    public function getAllOrderDataByEntrustNo($data)
    {
        if (empty($data['entrust_no'])) {
            $data['entrust_no'] = 'A-Z';
        }
        $orderList = OrderModel::where('erp_order_sn', 'like', '%'.trim($data['entrust_no']).'%')
            ->where('status', 1)
            ->select(['order_id', 'erp_order_sn', 'stock_in_status', 'salesman'])
            ->get()
            ->toArray();
        if (empty($orderList)) {
            return [];
        }
        foreach ($orderList as &$val) {
            $val['salesman'] = empty($val['salesman']) ? '' : $val['salesman'].WechatWmsLogic::getDepartmentName($val['salesman']);
        }
        $order_ids = array_column($orderList, 'order_id');
        $counts = OrderGoodsModel::whereIn('order_id', $order_ids)
            ->groupBy(['order_id'])
            ->select(['order_id', DB::raw('count(*) as count')])
            ->get()
            ->toArray();
        $detailCounts = [];
        foreach ($counts as $count) {
            $detailCounts[$count['order_id']] = $count['count'];
        }
        foreach ($orderList as &$val) {
            $val['stock_in_status_val'] = OrderModel::$stockInStatus[$val['stock_in_status']];
            $val['detail_count'] = $detailCounts[$val['order_id']];
        }
        return $orderList;
    }

    // 根据入仓号获取物料明细
    public function getOrderDetailById($data)
    {
        return OrderGoodsModel::where('order_id', $data['order_id'])
            ->select(['order_goods_id', 'goods_type as model', 'brand', 'numbers as qty', 'origin'])
            ->get()
            ->toArray();
    }

    // 新增异常管理
    public function addAbnormal($data)
    {
        $orderDetail = OrderGoodsModel::where('order_goods_id', $data['order_goods_id'])
            ->select(['goods_type', 'brand', 'numbers', 'origin', 'erp_entery_id', 'erp_material_sn', 'order_id'])
            ->first();
        if (empty($orderDetail)) {
            throw new \Exception('订单详情不存在');
        }
        $info = OrderModel::where('order_id', $orderDetail['order_id'])->select(['erp_order_sn', 'salesman'])->first();
        DB::connection('SUPPLYCHAIN')->beginTransaction();
        try {
            $insertData = [
                'abnormal_number'=>self::getAbnormalNumber(),
                'entrust_no'=>$info['erp_order_sn'],
                'abnormal_type'=>$data['abnormal_type'],
                'salesman'=> empty($info['salesman'])?'':$info['salesman'].WechatWmsLogic::getDepartmentName($info['salesman']),
                'order_model'=>$orderDetail['goods_type'],
                'order_brand'=>$orderDetail['brand'],
                'order_qty'=>$orderDetail['numbers'],
                'order_origin'=>$orderDetail['origin'],
                'actual_model'=>$data['actual_model'],
                'actual_brand'=>$data['actual_brand'],
                'actual_qty'=>$data['actual_qty'],
                'actual_origin'=>$data['actual_origin'],
                'tag_template_type'=>0,
                'abnormal_explain'=>$data['abnormal_explain'],
                'abnormal_pic_url'=>$data['abnormal_pic_url'],
                'warehouse_confirm_time'=>0,
                'salesman_handle_time'=>0,
                'create_time'=>time(),
                'order_goods_id'=>$data['order_goods_id'],
                'is_business_processing'=>$data['is_business_processing'],
                'create_user_id'=>WechatWmsLogic::getCurrentUserId()
            ];
            // 是否需要业务处理
            $is_business_processing = $data['is_business_processing'];
            if ($is_business_processing) {
                $insertData['abnormal_status'] = 0;
            } else {
                $insertData['abnormal_status'] = 2;
            }

            $abnormal_id = WmsAbnormalManageModel::insertGetId($insertData);
            if (!$abnormal_id) {
                throw new \Exception('异常理货新增失败');
            }
            // 发送采购系统让采购确认
            if ($is_business_processing) {
                PurRequestLogic::syncAbnormalHandleData(json_encode([
                    'erp_scm_item_id'=>$orderDetail['erp_entery_id'],
                    'warehouse_receipt_sn'=>$info['erp_order_sn'],
                    'abnormal_type'=>$data['abnormal_type'],
                    'abnormal_remark'=>$data['abnormal_explain'],
                    'tally_goods_id'=>0,
                    'tally_goods_sn'=>0,
                    'tally_goods_name'=>$insertData['actual_model'],
                    'tally_brand_id'=>0,
                    'tally_brand_name'=>$insertData['actual_brand'],
                    'tally_goods_number'=>$insertData['actual_qty'],
                    'tally_origin'=>$insertData['actual_origin'],
                    'source_sn'=>$insertData['abnormal_number'],
                    'source_id'=>$abnormal_id,
                    'source_type'=>1,
                    'create_uid'=>$insertData['create_user_id'],
                    'create_name'=>WechatWmsLogic::getCurrentName(),
                    'img_list'=>!empty($data['abnormal_pic_url'])?explode(',', $data['abnormal_pic_url']):[],
                ]));
            }
            (new ActionLogModel())->addLog($abnormal_id, '创建了异常', WechatWmsLogic::getCurrentName(), 9);
            if (!$is_business_processing) {
                (new ActionLogModel())->addLog($abnormal_id, '确认了异常', WechatWmsLogic::getCurrentName(), 9);
            }
            DB::connection('SUPPLYCHAIN')->commit();
        } catch (\Exception $e) {
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($e->getMessage());
        }
        return $abnormal_id;
    }

    // 获取异常详情
    public function getAbnormalDetail($data)
    {
        $abnormalInfo = WmsAbnormalManageModel::where('abnormal_id', $data['abnormal_id'])->firstOrFail();
        $abnormalInfo['abnormal_pic_urls'] = empty($abnormalInfo['abnormal_pic_url']) ? [] : explode(',', $abnormalInfo['abnormal_pic_url']);
        $logs = ActionLogModel::where('user_id', $data['abnormal_id'])->where('type', 9)->orderBy('create_time', 'desc')->get()->toArray();
        foreach ($logs as &$log) {
            $log['create_time'] = date('Y-m-d H:i:s', $log['create_time']);
        }

        $abnormalInfo['logs'] = $logs;
        $abnormalInfo['abnormal_type_val'] = array_get(WmsAbnormalManageModel::$abnormalType, $abnormalInfo['abnormal_type']);
        $abnormalInfo['abnormal_status_val'] = array_get(WmsAbnormalManageModel::$abnormalStatus, $abnormalInfo['abnormal_status']);
        $abnormalInfo['tag_template_type'] = array_get(WmsAbnormalManageModel::$tagTemplateType, $abnormalInfo['tag_template_type']);
        $abnormalInfo['actual_qty'] = isset($abnormalInfo['actual_qty'])?$abnormalInfo['actual_qty']:'';
        return $abnormalInfo;
    }

    // 异常管理确认,仅“待仓库确认”状态操作，操作后状态变为“已确认” 支持批量操作，操作勾选不满足条件的其他类型的校验提醒
    public function confirmAbnormal($data)
    {
        $abnormal_id = $data['abnormal_id'];
        if (WmsAbnormalManageModel::where('abnormal_id', $abnormal_id)->where('abnormal_status', '<>', 1)->exists()) {
            throw new \Exception('仅“待仓库确认”的数据可确认');
        }
        DB::connection('SUPPLYCHAIN')->beginTransaction();
        try {
            WmsAbnormalManageModel::where('abnormal_id', $abnormal_id)->update([
                'abnormal_status'=>2,
                'warehouse_confirm_time'=>time()
            ]);
            if (WmsAbnormalManageModel::where('abnormal_id', $abnormal_id)->value('is_business_processing')) {
                PurRequestLogic::syncHandleResult(json_encode([
                    'source_id'=>$abnormal_id,
                    'source_type'=>1,
                    'status'=>2,
                    'user_id'=>WechatWmsLogic::getCurrentUserId(),
                    'user_name'=>WechatWmsLogic::getCurrentName(),
                ]));
            }
            (new ActionLogModel())->addLog($abnormal_id, '确认了处理方式', WechatWmsLogic::getCurrentName(), 9);
            DB::connection('SUPPLYCHAIN')->commit();
        } catch (\Exception $e) {
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($e->getMessage());
        }
        return 0;
    }

    // 作废异常功能,除“作废”状态其他状态均可操作，操作后状态变为“作废”,支持批量操作，操作勾选不满足条件的其他类型的校验提醒
    public function cancelAbnormal($data)
    {
        $abnormal_id = $data['abnormal_id'];
        if (WmsAbnormalManageModel::where('abnormal_id', $abnormal_id)->where('abnormal_status', -1)->exists()) {
            throw new \Exception('“作废”状态的异常不能作废');
        }
        DB::connection('SUPPLYCHAIN')->beginTransaction();
        try {
            WmsAbnormalManageModel::where('abnormal_id', $abnormal_id)->update([
                'abnormal_status'=>-1
            ]);
            if (WmsAbnormalManageModel::where('abnormal_id', $abnormal_id)->value('is_business_processing')) {
                PurRequestLogic::syncHandleResult(json_encode([
                    'source_id'=>$abnormal_id,
                    'source_type'=>1,
                    'status'=>-1,
                    'user_id'=>WechatWmsLogic::getCurrentUserId(),
                    'user_name'=>WechatWmsLogic::getCurrentName(),
                ]));
            }
            (new ActionLogModel())->addLog($abnormal_id, '作废了异常', WechatWmsLogic::getCurrentName(), 9);
            DB::connection('SUPPLYCHAIN')->commit();
        } catch (\Exception $e) {
            DB::connection('SUPPLYCHAIN')->rollback();
            throw new \Exception($e->getMessage());
        }
        return 0;
    }

    public function getQuickExpressions($data)
    {
        $list = WmsAbnormalQuickExpressionsModel::where('abnormal_type', $data['abnormal_type'])
            ->where('user_id', WechatWmsLogic::getCurrentUserId())->select(['id', 'seq', 'expressions'])->orderBy('seq')->get()->toArray();
        return ['abnormal_type'=>$data['abnormal_type'], 'list'=>$list];
    }

    // 获取待处理的记录条数 待仓库确认
    public function getStayHandleCount()
    {
        $oneHoursAgo = time() - 60 * 60;
        $threeHoursAgo = time() - 3 * 60 * 60;
        // 已理货未装箱
        $unBoxedQty = WmsTallyAbnormalDetailModel::where('create_time', '<', $threeHoursAgo)
            ->where('box_sn', '')
            ->count('wstyaldl_id');
        // 已装箱未封箱

        $unCloseBoxQty = count(WmsTallyModel::from('wms_tally as a')
            ->join('wms_tally_detail as b', 'a.wsty_id', '=', 'b.wsty_id')
            ->where('a.tally_status', 1)
            ->where('a.update_time', '>', 0)
            ->where('b.is_base', 0)
            ->where(function ($query) use ($oneHoursAgo, $threeHoursAgo) {
                // 普货装箱1小时没有封箱
                $query->whereOr(function ($query, $oneHoursAgo) use ($oneHoursAgo) {
                    $query->where('box_type', 0)
                        ->where('a.update_time', '<', $oneHoursAgo);
                });
                // 美产商检3小时没有封箱
                $query->whereOr(function ($query) use ($threeHoursAgo) {
                    $query->whereIn('box_type', [1, 2])
                        ->where('a.update_time', '<', $threeHoursAgo);
                });
            })
            ->groupBy(['a.wsty_id'])
            ->get()->toArray());
        // 已封箱未装板 未推送金蝶
        $unPlateQty = count(WmsTallyModel::from('wms_tally as a')
            ->join('wms_tally_detail as b', 'a.wsty_id', '=', 'b.wsty_id')
            ->where('b.is_base', 0)
            ->where('a.tally_status', 2)
            ->where('a.close_box_time', '>', 0)
            ->where('a.close_box_time', '<', $threeHoursAgo)
            ->groupBy(['a.wsty_id'])
            ->get()->toArray());
        $retentionQty = $unBoxedQty + $unPlateQty + $unCloseBoxQty;
        $abnormalQty = WmsAbnormalManageModel::where('abnormal_status', 1)->count("abnormal_id");
        return ['abnormalQty' => $abnormalQty, 'retentionQty' => $retentionQty, 'retentionDetail' => ['unBoxedQty' => $unBoxedQty, 'unCloseBoxQty' => $unCloseBoxQty, 'unPlateQty' => $unPlateQty]];
    }
}