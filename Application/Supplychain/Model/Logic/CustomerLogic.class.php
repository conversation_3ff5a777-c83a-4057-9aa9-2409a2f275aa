<?php
namespace Supplychain\Model\Logic;
use Illuminate\Support\Facades\DB;
use Shipping\Model\ShippingModel;
use Supplychain\Model\CmsModel;
use Supplychain\Model\ComCreditsModel;
use Supplychain\Model\ComInvoiceModel;
use Supplychain\Model\CompanyModel;
use Supplychain\Model\ContactModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\ExchangeRate;
use Supplychain\Model\LiexinModel;
use Supplychain\Model\OrderModel;
use Supplychain\Model\OrderStatusModel;
use Supplychain\Model\ServiceAgreementModel;
use Supplychain\Model\SupplierBankModel;
use Supplychain\Model\SupplierModel;
use Supplychain\Model\SZShipmentsNoteModel;
use Supplychain\Model\UserDeliveryModel;
use Supplychain\Model\UserMessageModel;
use Supplychain\Repository\UserRepository;
use Supplychain\Service\DingNotify;

/**
 * Created by 2021/6/22.
 * User: Joneq
 * Info: 2021/6/22
 * Time: 下午5:07
 */

class CustomerLogic
{


    //处理签署服务协议
    public function handleSignServiceContract()
    {
        return DingNotify::signServiceNotify(CustomerModel::where('user_id',cookie('uid'))->value('customer_name'));
    }

    //获取账号截止时间
    static public function getAccoutEndTime()
    {
        return (new LiexinModel())->setTable('user_login_log')->where('user_id',cookie('uid'))->value('last_login_time')+(30*86400);
    }


    static public function getUserCompanyId($userid)
    {
        return CustomerModel::where('user_id',$userid)->value('company_id');
    }


    //获取客户基本信息
    static public function getUserBasicInfo($userId)
    {
        $basicInfo = CustomerModel::where('user_id',$userId)->select('customer_name','company_id','head_pic')->first();
        if (empty($basicInfo)){
            $basicInfo->customer_name = '';
            $basicInfo->company_id = 0;
            $basicInfo->head_pic = '';
        }
        $basicInfo->customer_name = (new LiexinModel())->setTable('user_main')->where('user_id',CommonLogic::getLoginUid())->value('mobile');
        if (empty($basicInfo->customer_name)){
            $basicInfo->customer_name = (new LiexinModel())->setTable('user_main')->where('user_id',CommonLogic::getLoginUid())->value('email');
        }


        if (!empty($notifyEmail = UserMessageModel::where('user_id',CommonLogic::getLoginUid())->where('type',2)->first())){
            $basicInfo->notify_email = $notifyEmail;
        }else{
            $notifyEmail['id'] = '';
            $notifyEmail['type'] = '';
            $notifyEmail['send_user'] = '';
            $notifyEmail['custom_fun'] = '';
            $notifyEmail['user_id'] = '';
            $notifyEmail['company_id'] = '';
            $notifyEmail['create_time'] = '';
            $notifyEmail['update_time'] = '';
            $basicInfo->notify_email = $notifyEmail;
        }


        $basicInfo['is_Logistics_notice_bind'] = UserMessageModel::where('user_id',CommonLogic::getLoginUid())->value('id')?1:0;

        $basicInfo['company_name'] = CompanyModel::where('company_id',$basicInfo['company_id'])->value('company_full_name');


        if (empty($lastLoginTime = (new LiexinModel())->setTable('user_login_log')->where('user_id',$userId)->orderBy('id','desc')->value('last_login_time'))){
            $basicInfo->last_login_time = '';
        }else{
            $basicInfo->last_login_time = date('Y-m-d H:i:s',$lastLoginTime);
        }
        $basicInfo->user_follow_people = self::getUserFollowPeopleInfo();
        return $basicInfo;
    }


    //获取用户跟进人的信息
    static public function getUserFollowPeopleInfo()
    {
        //TODO 是否存在无跟进人的情况，无跟进人默认使用谁
        $userId = CommonLogic::getLoginUid();

        $salesName = CustomerModel::where('user_id',$userId)->value('follow_people');
        if (empty($salesName)){
            $return['name'] = '';
            $return['mobile'] = '';
            $return['tel'] = '';
            $return['engName'] = '';
            $return['qq'] = '';
        }else{
            $return = (new CmsModel())->getData('user_info',['name'=>$salesName],'find','engName,name,mobile,tel,qq');
        }
        $return['yw_name'] = '';
        $return['yw_mobile'] = '';
        $return['yw_tel'] = '';
        $return['yw_engName'] = '';
        $return['yw_qq'] = '';
        return $return;
    }


    //获取个人中心的交易信息
    static public function getUserInfoBasicTrade($userId)
    {
        $customerInfo = CustomerModel::where('user_id',$userId)->first();

        $returnData['no_rmb_account'] = $customerInfo->rmb_account;

        $returnData['billing'] = 0;
        $returnData['wait_check_order'] = OrderModel::where('status',OrderModel::$AuditINGStatus)->where('id_edit_order',0)->where('user_id',$userId)->count('order_id');
        $returnData['wait_tongguan'] = OrderStatusModel::whereIn('baoguan_status',[0,1])->where('user_id',$userId)->count('order_id');
        $returnData['hk_wait_get'] = OrderStatusModel::whereIn('hk_delivery_status',[0,1])->where('user_id',$userId)->count('order_id');
        $returnData['sz_delivery'] = SZShipmentsNoteModel::where('user_id',$userId)->where('status',1)->count('order_id');

        return $returnData;
    }


    //获取协议
    static public function getUserInfoBasicServiceAgreement($userId)
    {
        $customer = CustomerModel::where("user_id",$userId)->select("erp_client_id")->first();
        $serviceAgreement = ServiceAgreementModel::where("erp_client_id",$customer->erp_client_id)->where("status",1)->first();
        return $serviceAgreement;
    }


    //获取协议
    static public function getExchange()
    {
        $rate = ExchangeRate::where('rate_type','美元')->limit(8)->orderBy('id','desc')->pluck('rate_num','rate_time');
        $newRate = [];
        foreach ($rate as $key=>$value){
            $newRate[date('m-d',strtotime($key))] = $value;
        }
        return $newRate;
    }



    //修改公司发票信息
    static public function updateComInvoice($data)
    {

        $userId = $data['user_id'];
        $comId = self::getUserCompanyId($data['user_id']);
        if (empty($comId)) throw new \Exception('暂未获取客户公司ID');

        $updateData = [];
        $getKey = ["tax_identifying_sn","type","tax_certificate_no","invoice_address","invoice_mobile",
            "bank_name","bank_number","invoice_image"];

        foreach ($getKey as $value){
            if (!isset($data[$value])){
                continue;
            }
            $updateData[$value] = $data[$value];
        }

        return ComInvoiceModel::updateOrCreate(['user_id'=>$userId],$updateData);
    }

    //修改公司发票信息
    static public function updateCompanyBasicInfo($data)
    {

        $userId = $data['user_id'];
        $comId = self::getUserCompanyId($data['user_id']);
        if (empty($comId)) throw new \Exception('暂未获取客户公司ID');

        $updateData = [];
        $getKey = ["city_id","area_id","country_id","address","province_id",
            "industry_involved"];

        foreach ($getKey as $value){
            if (!isset($data[$value])){
                continue;
            }
            $updateData[$value] = $data[$value];
        }
        if (isset($data['head_pic']) && !empty($data['head_pic'])){
            CustomerModel::where('user_id',$userId)->update(['head_pic'=>$data['head_pic']]);
        }
        return CompanyModel::updateOrCreate(['company_id'=>$comId],$updateData);
    }


    //修改公司联系人
    static public function updateComContact($data)
    {
        $comId = self::getUserCompanyId($data['user_id']);
        if (empty($comId)) throw new \Exception('暂未获取客户公司ID');

        $updateData = [];
        $getKey = ["contact_name","special_plane","fax","qq","wechat", "source",'contact_phone'];

        foreach ($getKey as $value){
            if (!isset($data[$value])){
                continue;
            }
            $updateData[$value] = $data[$value];
        }

        return ContactModel::where(['company_id'=>$comId])->update($updateData);
    }



    //获取当天的汇率
    static public function getTodayRate()
    {
        $preDate = date('Y-m-d',time()-86400);
        $today = date('Y-m-d');
        $usd = ExchangeRate::where('rate_type','美元')->where('rate_time',$today)->first();
        $hk = ExchangeRate::where('rate_type','港币')->where('rate_time',$today)->first();
        $lastusd = ExchangeRate::where('rate_type','美元')->where('rate_time',$preDate)->first();
        $lasthk = ExchangeRate::where('rate_type','港币')->where('rate_time',$preDate)->first();
        return ['usd'=>$usd,'hk'=>$hk,'pre_usd'=>$lastusd,'pre_hk'=>$lasthk];
    }




    //增加合作意向
    static public function addCooperationIntention($data)
    {
        ContactModel::updateOrCreate(
            ['user_id'=>cookie('uid')],[
            'contact_name'=>$data['contract_name'],
            'contact_phone'=>$data['contract_phone'],
        ]);
        CompanyModel::where('user_id',cookie('uid'))->update(
            ['bus_license_image'=>$data['bus_license_image']]
        );
        ComInvoiceModel::where('user_id',cookie('uid'))->update(
            ['invoice_image'=>$data['invoice_image']]
        );

        (new CustomerLogic())->handleSignServiceContract();
        return 'ok';
    }

    //获取
    static public function getAddressInfo($addressId)
    {
        $addressInfo = UserDeliveryModel::where("com_delivery_id",$addressId)
            ->select("com_delivery_id","consignee","seat_number","intl_code","mobile","detail_address","is_default","province","city","district",'com_name','address_area')
            ->orderBy("is_default",'desc')
            ->orderBy("create_time",'desc')
            ->first();

        if(!$addressInfo){
            return [];
        }
        $addressInfo->zuo_ji = $addressInfo->zuo_ji;
        $addressInfo->city_info = (new UserRepository())->getRegionInfo($addressInfo->province,$addressInfo->city,$addressInfo->district,$addressId->address_area);
        return $addressInfo;
    }


    static public function getSupplyBankList($data)
    {
        $obj = new SupplierModel;
        $obj = $obj->whereIn('status',[0,1]);
        foreach ($data as $key=>$value){
            if (trim($value) === "")continue;
            switch ($key){
                case 'status':
                case 'supplier_id':
                    $obj = $obj->where($key,$value);break;
                case 'begin_time':
                    $obj = $obj->where('create_time','>=',strtotime($value));break;
                case 'end_time':
                    $obj = $obj->where('create_time','<=',strtotime($value));break;
                default:
                    continue;
            }
        }

        $userId = cookie('uid');

        $page = I('get.page',0,'intval');
        $limit = I('get.limit',10,'intval');
        $list = $obj->orderBy('create_time','desc')->select("*")->paginate($limit,[],'page',$page)->toArray();
        if($page > ceil($list['total']/$limit)) throw new \Exception('没有更多数据');

        foreach($list['data'] as &$item){
            $item['status_cn'] = array_get(SupplierModel::$Status,$item['status'],'暂无');
            $item['is_self'] = $userId == $item['user_id'] ?1:0;

            $supplierBankInfo = SupplierBankModel::where('supplier_id',$item['supplier_id'])->orderBy('create_time','desc')->whereIn('status',[0,1])->get();
            foreach ($supplierBankInfo as &$value){
                $value->status_cn = array_get(SupplierModel::$Status,$value->status,'暂无');
                $value->is_self = $value->user_id == $userId ?1 :0;
            }
            $item['bank_info'] = $supplierBankInfo;
        }
        return $list;
    }


    //搜索供应链
    static public function searchSupply($data)
    {
        $client = new \GuzzleHttp\Client();
        $response = $client->get(UNITEDDATA_DOMAIN.'/sync/Company/getCompanyListByKeyword?company_type=2&keyword='.$data['keywords'].'&region='.$data['region']);

        if (mb_strlen($data['keywords']) < 5){
            return ['companyList'=>[]];
        }

        if ($response->getStatusCode() !== 200){
            throw new \Exception('参数请求code出错:'.$response->getStatusCode().':'.$response->getBody()->getContents());
        }

        $returnData =  $response->getBody()->getContents();


        return \GuzzleHttp\json_decode($returnData,true)['data'];
    }


    //获取当前用户京东金融信息
    static public function getJdJrInfo()
    {
        $returnData = [];

        //是否可以适用京东金融,有erp编码，有公司额度就可以申请
        $jdjrInfo = CommonLogic::getJdJrInfo();
        if (ComCreditsModel::where('company_name',$jdjrInfo['company_name'])
            ->where('erp_company_code','!=','')
            ->value('id')){
            $returnData['can_user_jdjr'] = 1;
        }else{
            $returnData['can_user_jdjr'] = 0;
        }


        $rqJdData['setCustCode'] = $jdjrInfo['company_name'];
        $rqJdData['setSettleUserCode'] = $jdjrInfo['company_name'];


        \Think\Log::write('jdqueryCustInfo:$param'.json_encode($rqJdData),'WARN');
        //查询京东金融的信息
        $requestJd = JdpayLogic::queryCustInfo($rqJdData);
        $returnData['jdjr_info'] = \GuzzleHttp\json_decode((new JdpayLogic())->execute($requestJd['method'],$requestJd['request']),true);
        \Think\Log::write('jdqueryCustInfo:$param'.json_encode($returnData),'WARN');

        //获取联合登陆信息
        $returnData['jdjr_login'] = self::getJdLoginParam($jdjrInfo);

        return $returnData;
    }


    static public function getJdLoginParam($jdjrInfo)
    {
        $systemInfo = [
            'sysId' => C('JDPAY.login_sysId'),
            'token' => C('JDPAY.login_token')
        ];

        $param = [
            "processId" => C('JDPAY.login_processId'),//配置 id，申请的
            "openSys" => C('JDPAY.login_openSys'),//平台 id，申请的
            "openId" => $jdjrInfo['company_code'],//平台登录账号 id，人的维度，自己的
            "openUser" => $jdjrInfo['company_code'],//平台用户 id，企业维度，自己的
            "successUrl" => "",//成功后回调地址
            "clientIp" => $_SERVER['REMOTE_ADDR'],//客户端 ip
            "clientAgent" => $_SERVER['HTTP_USER_AGENT'],//浏览器代理
            "failUrl" => "",//失败后返回地址
            "openApiParam" => '{"backUrl":"http://loan.jd.com/cgrz/portal?productCode=25041"}',//其他参数 json 串
        ];


        \Think\Log::write('jd:$param'.json_encode($param),'WARN');

        import('Jdpay.aop.client.DefaultHapiJddClient');

        $client = new \DefaultHapiJddClient();
        $client->appIdType = "0";
        $client->openPublicKey = C('JDPAY.login_openPublicKey');
        $client->md5Salt = C('JDPAY.md5_salt');
        $client->appId = C('JDPAY.login_appId');
        $client->appPrivateKey = C('JDPAY.login_appPrivateKey');
        $client->signType = 'MD5_RSA';
        $client->encryptType = 'ENV_RSA';
        $result = $client->getData($systemInfo, $param);
        $result['url'] = C('JDPAY.login_url');
        \Think\Log::write('jd:$client'.json_encode($client),'WARN');
        \Think\Log::write('jd:$result'.json_encode($result),'WARN');
        return $result;
    }

}