<?php
/**
 * Created by 2021/8/7.
 * User: Joneq
 * Info: 2021/8/7
 * Time: 下午2:40
 */

namespace Supplychain\Model\Logic;
use Illuminate\Database\Capsule\Manager as DB;


class JdparamLogic
{
    static public $defaltLimit = 50;
    static public $defaltOffset = 6;



    //京东参数集合
    static public function jdMoheRequest()
    {
        //获取所有的需要推白名单的数据
        CommonLogic::signDb();
        $companyData = DB::connection('credit')->table('com_credits')->whereNotIn('erp_company_code',['','0'])
            ->select('erp_company_code','company_name','id')->limit(self::$defaltLimit)->offset(self::$defaltOffset)->get()->toArray();
        $returnData = [];
        foreach ($companyData as $key=>$val){
            $returnData[$key]['erp_company_code'] = $val->erp_company_code;
            $returnData[$key]['company_name'] = $val->company_name;
            $returnData[$key]['id'] = $val->erp_company_code;

            $comBasicInfos = DB::connection('credit')->table('com_basicinfos')->where('com_credits_id',$val->id)->select('com_organization','com_address')->first();
            $returnData[$key]['com_address'] = empty($comBasicInfos->com_address)?'暂无':$comBasicInfos->com_address;
        }

        //测试用例 TODO 真实参数要线上也布置一份
        $returnData = '[{"erp_company_code":"0000036","company_name":"惠州格利尔科技有限公司","id":"0000036","com_address":"惠州仲恺高新区东江产业园东兴片区东新大道108号B1栋3层"},{"erp_company_code":"0000038","company_name":"上海台津自动化工程有限公司","id":"0000038","com_address":"上海市金山区卫清西路421号五楼927室"},{"erp_company_code":"0000043","company_name":"广州市恒亚电子科技有限公司","id":"0000043","com_address":"广州市天河区天河路549号2005房"},{"erp_company_code":"0000049","company_name":"昆山阿德万斯机电设备有限公司","id":"0000049","com_address":"玉山镇恒龙国际机电五金市场3号楼6室"},{"erp_company_code":"0000056","company_name":"北京科环世纪电磁兼容技术有限责任公司","id":"0000056","com_address":"北京市昌平区北七家镇宏福大厦1104室"},{"erp_company_code":"0000061","company_name":"苏州群宇电子科技有限公司","id":"0000061","com_address":"苏州市吴中区甪直镇甪胜路27号"},{"erp_company_code":"0000065","company_name":"广州拓息信息技术有限公司","id":"0000065","com_address":"广州市天河区中山大道路建工路9号4楼南区A17号（仅限办公）"},{"erp_company_code":"0000069","company_name":"万通智控科技股份有限公司","id":"0000069","com_address":"杭州市余杭区杭州余杭经济技术开发区振兴东路12号"},{"erp_company_code":"0000072","company_name":"深圳市鑫鑫鹏包装科技有限公司","id":"0000072","com_address":"深圳市龙华新区观澜街道大布巷社区对面二巷1号4楼A区"},{"erp_company_code":"0000074","company_name":"乐骐科技（深圳）有限公司","id":"0000074","com_address":"深圳市前海深港合作区前湾一路1号A栋201室（入驻深圳市前海商务秘书有限公司）"},{"erp_company_code":"0000075","company_name":"浙江电牛电气有限公司","id":"0000075","com_address":"浙江省杭州市滨江区东信大道66号6号楼302室"},{"erp_company_code":"0000077","company_name":"无锡智慧达成信息科技有限公司","id":"0000077","com_address":"无锡市南湖大道855号902、903、904室"},{"erp_company_code":"0000079","company_name":"南京润豪鼎成电子科技有限公司","id":"0000079","com_address":"南京市江宁经济技术开发区胜太东路8号同曦街A3幢62室"},{"erp_company_code":"0000080","company_name":"北京泰和盛达商贸有限公司","id":"0000080","com_address":"北京市平谷区镇罗营镇镇罗营东街5号"},{"erp_company_code":"0000084","company_name":"上海韵虹电子有限公司","id":"0000084","com_address":"嘉定区宝安公路2889号2幢A3253室"},{"erp_company_code":"0000086","company_name":"上海松宝科技发展有限公司","id":"0000086","com_address":"上海市松江区石湖荡镇广庵路8号"},{"erp_company_code":"0000088","company_name":"深圳市南方硅谷微电子有限公司","id":"0000088","com_address":"深圳市南山区招商街道南海大道1079号花园城数码大厦B座501A号"},{"erp_company_code":"0000092","company_name":"北京隆盛发科技有限公司","id":"0000092","com_address":"北京市海淀区知春路128号1号楼7层702B"},{"erp_company_code":"0000096","company_name":"深圳北芯生命科技有限公司","id":"0000096","com_address":"深圳市宝安区新安街道留芳路6号庭威产业园3#3楼E区"},{"erp_company_code":"0000097","company_name":"深圳市博瑞生物科技有限公司","id":"0000097","com_address":"深圳市龙华区龙华街道清华社区清龙路6号港之龙科技园H栋9层B区"},{"erp_company_code":"0000099","company_name":"广州市顾德思企业管理咨询有限公司","id":"0000099","com_address":"广州市天河区珠江东路16号4001房之自编05单元"},{"erp_company_code":"0000100","company_name":"青岛萨博海洋仪器科技有限公司","id":"0000100","com_address":"山东省青岛市市南区漳州路110号5号楼1单元402"},{"erp_company_code":"0000102","company_name":"和兴模具(深圳)有限公司","id":"0000102","com_address":"深圳市宝安区燕罗街道燕川社区牛角路30号厂房四101、102"},{"erp_company_code":"0000103","company_name":"青岛国海生物制药有限公司","id":"0000103","com_address":"山东省青岛市城阳区凤锦路16号"},{"erp_company_code":"0000108","company_name":"深圳富昶晖科技有限公司","id":"0000108","com_address":"深圳市南山区南山街道南光社区创业路18号怡海广场东-3307"},{"erp_company_code":"0000110","company_name":"深速达（北京）科技有限公司","id":"0000110","com_address":"北京市房山区良乡凯旋大街建设路18号-D8684"},{"erp_company_code":"0000113","company_name":"苏州英贝迪电子科技有限公司","id":"0000113","com_address":"昆山开发区中小企业园章基路179号"},{"erp_company_code":"0000114","company_name":"青岛昕海杰散热器有限公司","id":"0000114","com_address":"山东省青岛市城阳区上马街道前程社区"},{"erp_company_code":"0000115","company_name":"无锡市密测多友精密仪器有限公司","id":"0000115","com_address":"无锡市新吴区汉江北路191-6号"},{"erp_company_code":"0000116","company_name":"连云港盈泽工贸有限公司","id":"0000116","com_address":"连云港市海州区玉带新村沿街网点D6-D10号楼9号门面"},{"erp_company_code":"0000118","company_name":"上海璟和技创机器人有限公司","id":"0000118","com_address":"上海市青浦区徐泾镇诸光路1588弄568号602室"},{"erp_company_code":"0000119","company_name":"广州静阳光电科技有限公司","id":"0000119","com_address":"广州市黄埔区南翔支路1号A栋405房"},{"erp_company_code":"0000121","company_name":"上海贯驰科技有限公司","id":"0000121","com_address":"上海市嘉定区菊园新区平城路811号1幢3层321室"},{"erp_company_code":"0000124","company_name":"大连佳沅电子科技有限公司","id":"0000124","com_address":"辽宁省大连市金州区站前街道有泉路13-1号1-4层"},{"erp_company_code":"0000125","company_name":"无锡光洋轴承有限公司","id":"0000125","com_address":"无锡市滨湖区胡埭镇翔鸽路30号"},{"erp_company_code":"0000131","company_name":"深圳市科瑞特精密五金有限公司","id":"0000131","com_address":"深圳市龙华新区观澜黎光社区黎光新工业区131号"},{"erp_company_code":"0000132","company_name":"北京鑫田基业机电设备有限公司","id":"0000132","com_address":"北京市大兴区黄村镇兴华中路34号1幢3层316室"},{"erp_company_code":"0000133","company_name":"大连亿宇达科技有限公司","id":"0000133","com_address":"辽宁省大连市西岗区奥林匹克广场7、9、11、13号东E区009-2号"},{"erp_company_code":"0000135","company_name":"长春汇通光电技术有限公司","id":"0000135","com_address":"高新区众恒路456号院内3号楼4楼"},{"erp_company_code":"0000136","company_name":"上海法维莱交通车辆设备有限公司","id":"0000136","com_address":"上海市宝山区富联一路31号"},{"erp_company_code":"0000137","company_name":"上海亿依电子信息科技有限公司","id":"0000137","com_address":"上海市松江区石湖荡镇松蒸公路北侧标准厂房4幢-20"},{"erp_company_code":"0000138","company_name":"深圳市伟健电子科技有限公司","id":"0000138","com_address":"深圳市龙岗区坂田街道五和社区创汇大厦706"},{"erp_company_code":"0000140","company_name":"深圳市木村机电有限公司","id":"0000140","com_address":"深圳市南山区南头街道深南大道12069号海岸时代大厦东座21楼2110室"},{"erp_company_code":"0000141","company_name":"上海奉飞电气有限公司","id":"0000141","com_address":"上海市奉贤区沿钱公路5599号10215室"},{"erp_company_code":"0000142","company_name":"天津市聚天商贸有限公司","id":"0000142","com_address":"天津市南开区鞍山西道与三潭路转角兴业数码广场第二层第A222柜位"},{"erp_company_code":"0000143","company_name":"北京畅想天行医疗技术有限公司","id":"0000143","com_address":"北京市北京经济技术开发区凉水河二街8号院19号楼A座6层601单元"},{"erp_company_code":"0000144","company_name":"深圳派立通科技有限公司","id":"0000144","com_address":"深圳市龙岗区平湖街道山厦社区杉坑工业区杉坑路7号A1厂房二楼、三楼(在杉坑三路B20厂房二楼、三楼、四楼设有经营场所)"},{"erp_company_code":"0000145","company_name":"福迪威西特传感工业控制（天津）有限公司","id":"0000145","com_address":"天津开发区微电子工业区微五路28号"},{"erp_company_code":"0000148","company_name":"上海祥菱电气自动化系统有限公司","id":"0000148","com_address":"上海市奉贤区海湾旅游区奉炮公路448号17幢286室"},{"erp_company_code":"0000152","company_name":"北京中兴宇泰科技有限公司","id":"0000152","com_address":"北京市朝阳区红军营东路18院46幢4601"}]';
        $returnData = \GuzzleHttp\json_decode($returnData,true);

        return $returnData;
    }



    //京东参数集合
    static public function preCreditWhitelist()
    {
        //获取所有的需要推白名单的数据
        CommonLogic::signDb();
        $companyData = DB::connection('credit')->table('com_credits')->whereNotIn('erp_company_code',['','0'])
            ->select('erp_company_code','company_name','id')->limit(self::$defaltLimit)->offset(self::$defaltOffset)->get()->toArray();
        $returnData = [];
        foreach ($companyData as $key=>$val){
            $returnData[$key]['setCustCode'] = $val->erp_company_code;
            $returnData[$key]['setCustName'] = $val->company_name;
            $returnData[$key]['setSettleUserCode'] = $val->erp_company_code;
            $returnData[$key]['setSettleUserName'] = $val->company_name;
            $returnData[$key]['setModCustomerNo'] = $val->erp_company_code;
            $returnData[$key]['setModCompanyName'] = $val->company_name;
            $returnData[$key]['setCorpId'] = DB::connection('credit')->table('com_basicinfos')->where('com_credits_id',$val->id)->value('com_organization');
        }
        return $returnData;
    }


    //京东参数集合
    static public function accessCustInfo($comCreditsId)
    {
        //获取所有的需要推白名单的数据
        CommonLogic::signDb();
        $companyData = DB::connection('credit')->table('com_credits')->where('erp_company_code','!=','')
            ->select('erp_company_code','company_name')->where('id',$comCreditsId)->first();

        if (empty($companyData)){
            throw new \Exception('暂无该公司信息');
        }
        $returnData['setCustCode'] = $companyData->erp_company_code;
        $returnData['setCustName'] = $companyData->company_name;
        $returnData['modCustomerNo'] = $companyData->erp_company_code;
        $returnData['modCompanyName'] = $companyData->company_name;
        $returnData['setSettleUserCode'] = $companyData->erp_company_code;
        $returnData['setSettleUserName'] = $companyData->company_name;
        $returnData['setSettleCertNo'] = $returnData['setCustCertNo'] = $returnData['corpId']  = DB::connection('credit')->table('com_basicinfos')->where('com_credits_id',$comCreditsId)->value('com_organization');;
        return $returnData;
    }

    //京东参数集合
    static public function queryCustInfo()
    {
        //获取所有的需要推白名单的数据
        CommonLogic::signDb();
        $companyData = DB::connection('credit')->table('com_credits')->where('erp_company_code','!=','')
            ->select('erp_company_code','company_name')->first();

        if (empty($companyData)){
            throw new \Exception('暂无该公司信息');
        }
        $returnData['setCustCode'] = $companyData->erp_company_code;
        $returnData['setSettleUserCode'] = $companyData->erp_company_code;
        return $returnData;
    }


    //京东参数集合
    static public function payOrder()
    {
        //获取所有的需要推白名单的数据
        CommonLogic::signDb();
        $companyData = DB::connection('credit')->table('com_credits')->where('erp_company_code','=','0000049')
            ->select('erp_company_code','company_name')->first();

        if (empty($companyData)){
            throw new \Exception('暂无该公司信息');
        }
        $returnData['setCustCode'] = $companyData->erp_company_code;
        $returnData['setSettleUserCode'] = $companyData->erp_company_code;
        $returnData['companyName'] = $companyData->company_name;
        return $returnData;
    }



}