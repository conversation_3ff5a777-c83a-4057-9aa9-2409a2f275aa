<?php
/**
 * Created by 2023/5/17.
 * User: <PERSON><PERSON><PERSON>
 * Info: ...
 * Time: 下午5:17
 */

namespace Supplychain\Model\Logic;


use mysql_xdevapi\Exception;
use Supplychain\Model\CheetahContactModel;
use Supplychain\Model\InvoiceMailAddressModel;
use Supplychain\Model\InvoiceMailHaveModel;
use Supplychain\Model\Logic\WmsLogic\OrderTrackingLogLogic;
use Supplychain\Model\OrderModel;
use Supplychain\Model\OrderTrackingLogModel;
use Supplychain\Model\WmsTallyAbnormalDetailModel;
use Supplychain\Model\WmsTallyDetailModel;
use Illuminate\Database\Capsule\Manager as DB;

class OpenLogic
{


    static public function getUnreceivedShippingSn($requestData)
    {
        CommonLogic::logsw(\GuzzleHttp\json_encode($requestData),__FUNCTION__);
        $shippingData = InvoiceMailHaveModel::whereIn('mail_status',[InvoiceMailHaveModel::MAIL_STATUS_ONE,InvoiceMailHaveModel::MAIL_STATUS_TWO,InvoiceMailHaveModel::MAIL_STATUS_THREE])
            ->where('create_time','>=',$requestData['start_time'])
            ->where('create_time','<=',$requestData['end_time'])
            ->select('get_iemlas_id','mail_sn')->get()->toArray();

        if (empty($shippingData)){
            return [];
        }

        $shippingSnPhone = InvoiceMailAddressModel::whereIn('iemlas_id',array_column($shippingData,'get_iemlas_id'))->pluck('get_phone','iemlas_id');

        $returnData = [];
        foreach ($shippingData as &$value){
            $returnData[] = ['shipping_sn'=>$value['mail_sn'],'mobile'=>$shippingSnPhone[$value['get_iemlas_id']]];
        }

        return ['lists'=>$returnData];

    }


    static public function signShipping($requestData)
    {
        CommonLogic::logsw(\GuzzleHttp\json_encode($requestData),__FUNCTION__);
        InvoiceMailHaveModel::whereIn('mail_status',[InvoiceMailHaveModel::MAIL_STATUS_ONE,InvoiceMailHaveModel::MAIL_STATUS_TWO,InvoiceMailHaveModel::MAIL_STATUS_THREE])
            ->where('mail_sn',$requestData['shipping_sn'])
            ->update(['mail_status'=>4]);

        return [];
    }

    static public function addCheetahContact($requestData)
    {
        $insertData = [
            'name'=>$requestData['name'],
            'contact_info'=>$requestData['contact_info'],
            'type_str'=>$requestData['type_str'],
            'remark'=>$requestData['remark'],
            'create_time'=>time(),
        ];
        CheetahContactModel::insertGetId($insertData);
    }

    /**
     * @throws \Exception
     */
    static public function updateOriginStatus($requestData)
    {
        $idArr = array_filter(explode(',',$requestData['wstydl_id']));


        $successData = [];
        $errorData = [];

        foreach ($idArr as $id){

            if ($abnormalBatch = WmsTallyAbnormalDetailModel::where('wstydl_id',$id)->where('box_sn','!=','')->value('abnormal_batch')){
                $errorData[$id] = '异常编码'.$abnormalBatch.'已封箱不能修改';
                continue;
            }

            WmsTallyAbnormalDetailModel::where('wstydl_id',$id)->update([
                'origin_confirm_status'=>$requestData['origin_confirm_status'],
                'confirm_time'=>time(),
                'payer_type'=>$requestData['PAYERTYPE']

            ]);

            $typeCn = [1=>'客户承担',2=>'公司承担',3=>'免承担'];
            WmsTallyDetailModel::where('wstydl_id',$id)->update([
                'no_origin_tax_code'=>$requestData['NOORIGINTAXCODE'],
                'payer_type'=>$requestData['PAYERTYPE'],
                'equality_customos_confirm'=>$typeCn[$requestData['equality_customos_confirm']],
                'tax_file'=>$requestData['tax_file'],
                'tax_begin_time'=>strtotime($requestData['tax_begin_time']),
            ]);

            // 生成消息提醒
            $abnormalInfo = WmsTallyAbnormalDetailModel::where('wstydl_id',$id)->first();
            $tallyDetailInfo = WmsTallyDetailModel::where('wstydl_id',$id)->first();
            $salesman = OrderModel::where('order_id', $tallyDetailInfo['order_id'])->value('salesman');

            $title = $abnormalInfo['abnormal_batch'].' 入仓号：'.$abnormalInfo['erp_order_sn'].
                ',型号：'.$abnormalInfo['goods_type'].' '.$abnormalInfo['tally_num'].
                'pcs 美产税 '.array_get(WmsTallyAbnormalDetailModel::$ORIGIN_STATUS, $abnormalInfo['origin_confirm_status']);

            $content = '型号：'.$abnormalInfo['goods_type'].'，品牌：'.$abnormalInfo['brand'].'，'.$abnormalInfo['tally_num'].
                'pcs，确认类型：'.array_get(WmsTallyAbnormalDetailModel::$PAYER_TYPE, $abnormalInfo['payer_type']).
                (empty($tallyDetailInfo['no_origin_tax_code'])?'':$tallyDetailInfo['no_origin_tax_code']).'，'.
                (empty($salesman)?'':$salesman.WechatWmsLogic::getDepartmentName($salesman));
            $msg_data = [
                'title'      => $title,
                'content'    => $content,
                "link"       => "/web/lxTallyAbnormalList?abnormal_batch=".$abnormalInfo['abnormal_batch'],
                "link_title" => "产地税确认"
            ];
            $userId = WechatWmsLogic::getCurrentUserIdByName($tallyDetailInfo['update_user']);
            try {
                PurRequestLogic::sendMsg(json_encode([
                    'user_id'=>$userId,
                    'from_sys_id'=>18,
                    'to_sys_id'=>18,
                    'msg_category_id'=>2,
                    'msg_data'=>$msg_data
                ]));
            } catch (\Exception $e) {
                $errorData[$id] = '异常编码'.$abnormalBatch.':'.$e->getMessage();
            }
            try{
                $specialEmail = '<EMAIL>';
                $specialId = WechatWmsLogic::getCurrentUserIdByEmail($specialEmail);
                if ($specialId != $userId) {
                    PurRequestLogic::sendMsg(json_encode([
                        'user_id'=>$specialId,
                        'from_sys_id'=>18,
                        'to_sys_id'=>18,
                        'msg_category_id'=>2,
                        'msg_data'=>$msg_data
                    ]));
                }
            } catch (\Exception $e) {
                $errorData[$id] = '异常编码'.$abnormalBatch.':'.$e->getMessage();
            }
            $successData[] = $id;

            /**
             * 产地税确认
             * 触发动作：新流程韦伯采购系统同步产地税确认到供应链，该条产地税状态变为已确认成功
             * 日志：
             * 操作时间，操作人
             * 内容：产地税已确认，型号：ASDKUIHQWIE，品牌：TE牌，数量：700，等待装箱。
             */
            $content = '产地税已确认，型号：'.$tallyDetailInfo['goods_type'].'，品牌：'.$tallyDetailInfo['brand'].'，数量：'.$abnormalInfo['tally_num'].'。';
            OrderTrackingLogLogic::addLog([
                'log_type'=>OrderTrackingLogModel::log_type_06,
                'bill_type'=>OrderTrackingLogModel::orderDetail,
                'bill_id'=>$tallyDetailInfo['order_goods_id'],
                'content'=>$content,
                'create_user_id'=>OrderTrackingLogModel::admin_user_id,
                'create_user_name'=>OrderTrackingLogModel::admin_user_name,
                'ext_data'=>[
                    'goods_name'=>$tallyDetailInfo['goods_type'],
                    'brand_name'=>$tallyDetailInfo['brand'],
                    'qty'=>$abnormalInfo['tally_num'],
                ]
            ]);
        }

        return ['success_data'=>$successData,'error_data'=>$errorData];
    }

    static public function checkIsCanChange($requestData)
    {
        $erpOrderSnArr = array_filter(explode(',',$requestData['erp_order_sn']));

        $isHaveTallyData = WmsTallyDetailModel::whereIn('erp_order_sn',$erpOrderSnArr)->value('wstydl_id');
        if ($isHaveTallyData){
            throw new \Exception('存在理货数据，不可修改，请先撤销理货');
        }

        return [];
    }
}
