<?php
/**
 * Created by 2021/7/5.
 * User: Joneq
 * Info: 2021/7/5
 * Time: 下午6:38
 */

namespace Supplychain\Model\Logic;

class ExportLogic
{

    //获取excel的title数组
    static public function getExcelZero($titleKey='')
    {
        switch ($titleKey){
            case 'getNoInvoiceList':
                $titleArr = ['入仓单号','收款公司','订单金额','币别', '报关金额(原币)','已付汇金额','未开票金额(CNY)','订单时间'];
                break;
            case 'getForPaymentList':
                $titleArr = ['入仓号','付汇状态','供应商','订单金额', '订单币别','未结金额','可付汇金额'];
                break;
            case 'BoxSnAndNum':
                $titleArr = ['序号','型号','数量','扫描时间'];
                break;
            case 'getOrderList':
                $titleArr = [
                    '业务类型','产地税','代理费','合计代付费用','委托单号','订单币别', '订单日期','货值','进口关税','进口增值税','采购货款',
                ];
                break;
            default:
                $titleArr = [];
        }

        return $titleArr;
    }



    //获取excel的data数组
    static public function getDataKeyArr($dataKey='')
    {
        switch ($dataKey){
            case 'getNoInvoiceList':
                $dataKeyArr = [
                    'orderNo','companyName','orderAmt','currencyName','customsAmt','verifiAmt', 'noInvoiceAmt', 'orderDate'
                ];
                break;
            case 'getForPaymentList':
                $dataKeyArr = [
                    'entrustBillNumber','paymentStatus','supplier','OrginCurrency','settlementAmount','verifiAmount',
                ];
                break;
            case 'getOrderList':
                $dataKeyArr = [
                    '业务类型','产地税','代理费','合计代付费用','委托单号','订单币别', '订单日期','货值','进口关税','进口增值税','采购货款',
                ];
                break;
            case 'BoxSnAndNum':
                $dataKeyArr = ['id','goods_sn','goods_num','scan_time_cn'];
                break;
            default:
                $dataKeyArr = [];
        }

        return $dataKeyArr;
    }


    //获得组合的表格数据
    static public function getExcelAllData($exportKey,$excelNeedData)
    {
        //获取需要的数组，以及行头名称
        $excelZeroArr = self::getExcelZero($exportKey);
        $excelKeyArr = self::getDataKeyArr($exportKey);

        $length = count($excelKeyArr);

        $excelData[0] = $excelZeroArr;

        //拼接数据
        foreach ($excelNeedData as $key=>$value){

            for ($i=0;$i<$length;$i++){
                $excelData[$key+1][$i] = self::getDefaultValue($excelKeyArr[$i],array_get($value,$excelKeyArr[$i],' '));
            }
        }

        return $excelData;
    }

    //获取默认值以及特殊的默认值
    static public function getDefaultValue($name,$value)
    {
        switch ($name){
            case 'is_get':
                $defaultValue = empty($value)?'否':'是';
                break;
            case 'is_need_buy':
                $defaultValue = empty($value)?'否':'是';
                break;
            default:
                $defaultValue = $value;
                break;
        }

        return $defaultValue;
    }

    //输出一般的表格
    static public function exportExcelNormal($exportKey,$excelNeedData,$excelTitle = '表格模板')
    {
        vendor("PHPExcel.PHPExcel");



        //处理数据，获取key
        $keys = self::getDataKeyArr($exportKey);

        $objPHPExcel = new \PHPExcel();
        // 修改sheet名称
        $objPHPExcel->getActiveSheet()->setTitle($excelTitle . '_' . date('Ymd_His'));
        // 读取数组
        for ($j = 1; $j <= count($excelNeedData)+1; $j++) {
            for ($k = 1; $k <= count($keys); $k++) {
                $colname = \PHPExcel_Cell::stringFromColumnIndex($k - 1); // 从o开始
                $colname .= $j;
                if ($j == 1) {
                    $value = self::getExcelZero($exportKey)[$k - 1];
                } else {
                    $key = $keys[$k - 1];
                    $value = $excelNeedData[$j - 2][$key];
                }
                $objPHPExcel->setActiveSheetIndex(0)
                    ->setCellValue($colname, $value);
            }
        }
        //设置列宽
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(49);

        // Set active sheet index to the first sheet, so Excel opens this as the first sheet
        $objPHPExcel->setActiveSheetIndex(0);
        // Redirect output to a client’s web browser (Excel5)
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename=' . $excelTitle . '_' . date('Ymd_His') . '.xls');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $objWriter->save('php://output');
        exit;

    }


    //输出一般的表格
    static public function saveExcelNormal($exportKey,$excelNeedData,$excelTitle = '表格模板')
    {
        vendor("PHPExcel.PHPExcel");

        //处理数据，获取key
        $keys = self::getDataKeyArr($exportKey);

        $objPHPExcel = new \PHPExcel();
        // 修改sheet名称
        $objPHPExcel->getActiveSheet()->setTitle($excelTitle . '_' . date('Ymd_His'));
        // 读取数组
        for ($j = 1; $j <= count($excelNeedData)+1; $j++) {
            for ($k = 1; $k <= count($keys); $k++) {
                $colname = \PHPExcel_Cell::stringFromColumnIndex($k - 1); // 从o开始
                $colname .= $j;
                if ($j == 1) {
                    $value = self::getExcelZero($exportKey)[$k - 1];
                } else {
                    $key = $keys[$k - 1];
                    $value = $excelNeedData[$j - 2][$key];
                }
                $objPHPExcel->setActiveSheetIndex(0)
                    ->setCellValue($colname, $value);
            }
        }
        //设置列宽
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(49);

        // Set active sheet index to the first sheet, so Excel opens this as the first sheet
        $objPHPExcel->setActiveSheetIndex(0);

        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $objWriter->save(THINK_PATH.'../public/boxsnnum/'.$excelTitle.'.xls');

        return API_DOMAIN.'/public/boxsnnum/'.$excelTitle.'.xls';
    }

}