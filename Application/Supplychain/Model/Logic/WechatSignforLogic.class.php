<?php
/**
 * Created by 2021/12/3.
 * User: Joneq
 * Info: 2021/12/3
 * Time: 下午2:13
 */

namespace Supplychain\Model\Logic;


use Supplychain\Model\CmsModel;
use Supplychain\Model\WmsAccountModel;

class WechatSignforLogic
{
    static public $userId;

    static public function login($data)
    {
        //如果不存在@ 则走账号登陆
        if (!strpos($data['account'],'@')){
            return WechatWmsLogic::wmsAccountLogin($data);
        }

        $returnData = post_curl(GenerateApiUrl('', 'user').'/api/login',[
            'name'=> $data['account'],
            'passwd'=> md5($data['passwd']),
        ], array('api-key: crm a1b2c3d4e5f6g7h8i9jk'));

        $returnData = \GuzzleHttp\json_decode($returnData,true);
        if (!isset($returnData['retcode']) || intval($returnData['retcode']) != 0){
            throw new \Exception('登陆失败');
        }

        $userId = $returnData['data']['userId'];

        $returnData = (new CmsModel())->getData('user_info',['userId'=>$returnData['data']['userId']],'find','name,email');
        if (intval($returnData['status']) == 4){
            throw new \Exception('该员工已经离职');
        }

        if (isset($data['unionid']) && !empty($data['unionid'])){
            WechatWmsLogic::$userId = $userId;
            WechatWmsLogic::bindUserInfo(['wechat_unionid'=>$data['unionid']]);
        }

        $returnData['token'] = self::passport_encrypt(strval($userId));
        return $returnData;
    }


    static public function userInfo()
    {
        //如果存在则是wms账号用户
        if (self::$userId > WechatWmsLogic::WMSACCOUNTPRE){
            return WmsAccountModel::getLogInfo(self::$userId-WechatWmsLogic::WMSACCOUNTPRE);
        }else{
            $userId = self::$userId;
            $returnData = (new CmsModel())->getData('user_info',['userId'=>$userId],'find','name,email');
        }

        return $returnData;
    }

    static public function passport_encrypt($txt, $key = 'signfor') {
        srand((double)microtime() * 1000000);
        $encrypt_key = md5(rand(0, 32000));
        $ctr = 0;
        $tmp = '';
        for($i = 0;$i < strlen($txt); $i++) {
            $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
            $tmp .= $encrypt_key[$ctr].($txt[$i] ^ $encrypt_key[$ctr++]);
        }
        return urlencode(base64_encode(self::passport_key($tmp, $key)));
    }

    static public function passport_key($txt, $encrypt_key) {
        $encrypt_key = md5($encrypt_key);
        $ctr = 0;
        $tmp = '';
        for($i = 0; $i < strlen($txt); $i++) {
            $ctr = $ctr == strlen($encrypt_key) ? 0 : $ctr;
            $tmp .= $txt[$i] ^ $encrypt_key[$ctr++];
        }
        return $tmp;
    }

    static public function passport_decrypt($txt, $key = 'signfor') {
        $txt = self::passport_key(base64_decode(urldecode($txt)), $key);
        $tmp = '';
        for($i = 0;$i < strlen($txt); $i++) {
            $md5 = $txt[$i];
            $tmp .= $txt[++$i] ^ $md5;
        }
        return $tmp;
    }


    static public function getParam()
    {
        if (!empty($data = file_get_contents("php://input"))){

            $data = \GuzzleHttp\json_decode(file_get_contents("php://input"),true);
        }else{
            $data = $_REQUEST;
        }
        return $data;
    }


}