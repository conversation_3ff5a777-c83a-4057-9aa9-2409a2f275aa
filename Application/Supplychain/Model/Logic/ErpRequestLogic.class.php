<?php
/**
 * Created by PhpStorm.
 * User: 春风
 * Date: 2024/1/17
 * Time: 13:53
 */

namespace Supplychain\Model\Logic;
use GuzzleHttp\Client;


class ErpRequestLogic
{


    static public function getData($requestData,$method)
    {

        $client = new \SoapClient(ERP_SUPPLY_DOMAIN.'/ormrpc/services/WSIchuntCustomTaskFacade?wsdl');
        \Think\Log::write(\GuzzleHttp\json_encode($requestData),'WARN');
        $res = $client->$method(\GuzzleHttp\json_encode($requestData));
        \Think\Log::write($res,'WARN');
        $res = \GuzzleHttp\json_decode($res,true);

        if (!isset($res['0000'])){
            throw new \Exception($res['4444']);
        }
        return $res;
    }

    static public function getCodeData($requestData,$method)
    {

        $client = new \SoapClient(ERP_SUPPLY_DOMAIN.'/ormrpc/services/WSIchuntCustomTaskFacade?wsdl');
        \Think\Log::write(\GuzzleHttp\json_encode($requestData),'WARN');
        $res = $client->$method(\GuzzleHttp\json_encode($requestData));
        \Think\Log::write($res,'WARN');
        $res = \GuzzleHttp\json_decode($res,true);

        if (!isset($res['code']) || intval($res['code']) !== 0){
            throw new \Exception($res['msg']);
        }

        return $res;
    }
    static public function getDockCodeData($requestData,$method)
    {

        $client = new \SoapClient(ERP_SUPPLY_DOMAIN.'/ormrpc/services/WSIchuntDocking?wsdl');
        \Think\Log::write(\GuzzleHttp\json_encode($requestData),'WARN');
        $res = $client->$method(\GuzzleHttp\json_encode($requestData));
        \Think\Log::write($res,'WARN');
        $res = \GuzzleHttp\json_decode($res,true);

        if (!isset($res['code']) || intval($res['code']) !== 0){
            throw new \Exception($res['msg']);
        }

        return $res;
    }




    static public function getOriginData($data)
    {

        $client = new \SoapClient(ERP_SUPPLY_DOMAIN.'/ormrpc/services/WSIchuntCustomTaskFacade?wsdl');

        \Think\Log::write(\GuzzleHttp\json_encode($data),'WARN');
        $requestData = ['orderEntryID'=>$data['orderEntryID'],'originCountry'=>$data['originCountry']];
        \Think\Log::write(\GuzzleHttp\json_encode($requestData),'WARN');
        $fun = $data['method'];
        $res = $client->$fun(\GuzzleHttp\json_encode($requestData));
        \Think\Log::write($res,'WARN');
        $res = \GuzzleHttp\json_decode($res,true);
        if (!isset($res['code']) || intval($res['code']) !== 0){
            throw new \Exception($res['msg']);
        }

        return $res;
    }
}
