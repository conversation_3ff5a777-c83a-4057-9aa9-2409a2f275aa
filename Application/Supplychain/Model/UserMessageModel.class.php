<?php
namespace Supplychain\Model;



class UserMessageModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'user_message';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $appends = ['custom_funcs'];
    protected $fillable = ["type","send_user","custom_fun","user_id","company_id","create_time","update_time"];

    //定制消息类型
    public static $TYPE = [
        1=>'短信',
        2=>'邮箱',
    ];


    //定制消息类型
    public static $CUSTOM_FUN = [
        1=>'订单审核',
        2=>'香港收货',
        3=>'香港报关',
        4=>'深圳收货',
        5=>'深圳发货',
    ];



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public function getCustomFuncsAttribute()
    {
        $this->attributes['custom_funcs'] = [];
        if(isset($this->attributes['custom_fun'])){
            $arr = explode(",",$this->attributes['custom_fun']);
            foreach($arr as $item){
                $this->attributes['custom_funcs'][] = array_get(static::$CUSTOM_FUN,$item,'');
            }
        }
        return $this->attributes['custom_funcs'];
    }



}