<?php
/**
 * Created by 2022/4/24.
 * User: Joneq
 * Info: 2022/4/24
 * Time: 下午3:52
 */

namespace Supplychain\Model;


class WmsTodayTrayModel extends BaseModelLaravel
{

    protected $connection = 'SUPPLYCHAIN';
    protected $table = 'wms_today_tray';
    protected $primaryKey = 'wstyty_id';
    public $timestamps = false;

    static public $auditStatus = [
        1=>'等待确认',
        2=>'已经确认',
        3=>'已经取消',
    ];

    static public $trayType = [
        1=>'ippc',
        2=>'木板',
        3=>'胶盘',
    ];


    public function getWhereObj($data)
    {
        $obj = self::where('wstyty_id','>=',0);


        foreach ($data as $key=>$val){
            if ($val === '')continue;
            switch ($key){
                case 'is_del':
                case 'wstyty_id':
                case 'search_key':
                    $obj = $obj->where($key,$val);break;
                case 'today':
                    $todayStart = strtotime($val);
                    $obj = $obj->where('create_time','>=',$todayStart);
                    $obj = $obj->where('create_time','<=',($todayStart+86400));
                    break;


            }
        }
        return $obj;
    }

    static public function getTrayName($wstytyId)
    {
        return self::where('wstyty_id',$wstytyId)->value('tray_name');
    }


}