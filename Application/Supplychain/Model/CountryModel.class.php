<?php
namespace Supplychain\Model;


class CountryModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'country';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    public $timestamps = false;
    protected $fillable = ["code","name","iso_code","status","remark"];


    /**
     * 查找国家
     */
    public static function getCountryName($code){
        return static::where("code",trim($code))->value("name");
    }

    /**
     * 查找国家
     */
    public static function getCodeByName($name){

        if(strpos($name,"香港") !== false) return 110;
        if(strpos($name,"澳门") !== false) return 121;
        if(strpos($name,"台湾") !== false) return 143;
        return static::where("name",trim($name))->value("code");
    }



}