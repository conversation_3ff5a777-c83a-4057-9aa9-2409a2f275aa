<?php
namespace Supplychain\Model;



class OrderStatusModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'order_status';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    public $timestamps = false;


    public static $BankCharges = [
        1=>'我司承担',
        2=>'内扣',
        3=>'各占一半',
    ];

    //清关状态 0未报关 1部分报关  2已报关
    public static $hkDeliveryStatus = [
        0=>'未到货',
        1=>'部分到货',
        2=>'已到货',
    ];

    //'清关状态 0未报关 1部分报关  2已报关'
    public static $baoguan_status = [
        0=>'未报关',
        1=>'部分报关',
        2=>'已报关',
    ];

    //深圳到货 0未到货  1部分到货 2已到货
    public static $sz_delivery_status = [
        0=>'未到货',
        1=>'部分到货',
        2=>'已到货',
    ];

    //0未发货 1部分发货  2已发货
    public static $sz_send_status = [
        0=>'未发货',
        1=>'部分发货',
        2=>'已发货',
    ];






}