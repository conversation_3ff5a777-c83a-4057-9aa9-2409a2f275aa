<?php
namespace Supplychain\Model;


class ServiceAgreementListModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'service_agreement_list';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = null;
    public $timestamps = true;


    public static $Status=[
        0  => '已删除',
        1  => '正常',
    ];

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }




}