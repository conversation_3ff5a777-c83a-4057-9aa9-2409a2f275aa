<?php

namespace Supplychain\Model;

class WmsAbnormalManageModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'wms_abnormal_manage';
    protected $primaryKey = 'abnormal_id';
    public $timestamps = false;

    // 异常类型
    static public $abnormalType = [
        0 => '',
        1 => '品牌异常',
        2 => '型号异常',
        3 => '产地异常',
        4 => '数量异常',
        5 => '标签异常',
        6 => '其他异常',
    ];

    // 处理状态
    static public $abnormalStatus = [
        -1 => '作废',
        0 => '待业务处理',
        1 => '待仓库确认',
        2 => '已确认',
    ];

    // 标签模板
    static public $tagTemplateType = [
        0 => '',
        1 => '无批次模板',
        2 => '有批次模板',
    ];

    public function getWhereObj($data)
    {
        $obj = self::where('abnormal_id','>',0);
        foreach ($data as $key=>$val){
            if ($val === '')continue;
            switch ($key){
                case 'keyword':
                    $obj->where(function ($q) use($val){
                        $q->orWhere('abnormal_number','like','%'.$val.'%');
                        $q->orWhere('entrust_no','like','%'.$val.'%');
                        $q->orWhere('order_model','like','%'.$val.'%');
                    });break;
                case 'abnormal_status':
                    $obj = $obj->whereIn($key,explode(',', $val));break;
            }
        }
        return $obj;
    }
}