<?php
namespace Supplychain\Model;


class BaoGuanOrderListModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'baoguan_order_list';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = NULL;
    public $timestamps = true;

    static  $isGoodsCheckCn = [
        0=>'否',
        1=>'是',
        2=>'暂无报关数据'
    ];


    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    /**
     * 获取商品总数据
     */
    public function getGoodsNums($order_id){
        return $this->where("order_id",intval($order_id))->where('status',1)->sum('qty');
    }


    //根据入仓号修改是否报关
    static public function updateIsPrintFromErpOrderSn($erpOrderSn)
    {
        $changIdArr = self::where('erp_order_sn',$erpOrderSn)->pluck('id')->toArray();
        if (empty($changIdArr)){
            return [];
        }
        self::whereIn('id',$changIdArr)->update(['is_print'=>1]);
    }

}