<?php
namespace Supplychain\Model;
use Goods\Model\GoodsModel;

use \Supplychain\Model\UnitModel;

class OrderGoodsModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'order_goods';
    protected $primaryKey = 'order_goods_id';
    protected $guarded = ['order_goods_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = false;


    public static $STATUS=[
        -2=>'已删除',
        -1=>'审核不通过',
        1=>'已提交审核',
        0=>'审核中',
        5=>'审核通过'
    ];

    public static $PASS_STATUS = 5;//订单商品审核通过状态



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }



    public function getStatusCnAttribute(){
        return array_get(static::$STATUS,$this->status,'');
    }


    public function Unit(){
        return $this->hasOne(UnitModel::class,"code","measurement");
    }



    //获取数据列表 状态顺序判断
    static public function oftenGoodSList($data)
    {
        //select *,count(goods_id) add_num from lie_order_goods where user_id = 69413 group by goods_id order by add_num desc

        $obj = self::where('user_id',$data['user_id'])->selectRaw('*,count(goods_id) add_num')->orderBy('add_num','desc')->groupBy('goods_id');
        foreach ($data as $key=>$value){
            if (trim($value) === "")continue;
            switch ($key){
                case 'goods_type':
                case 'brand':
                case 'description':
                    $obj = $obj->where($key,'like',$value.'%');break;
                default:
                    continue;
            }
        }

        $page = I('get.page',0,'intval');
        $limit = I('get.limit',10,'intval');
        $list = $obj->paginate($limit,[],'page',$page)->toArray();
        if($page > ceil($list['total']/$limit)) throw new \Exception('没有更多数据');
        foreach ($list['data'] as &$v) {
            $v['jldw'] = UnitModel::getName($v['measurement']);
            $v['product_specification'] = ProductModel::where('goods_id',$v['goods_id'])->value('product_specification');
        }
        return $list;
    }



}