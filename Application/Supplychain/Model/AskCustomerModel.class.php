<?php 
namespace Supplychain\Model;


Class AskCustomerModel extends BaseModelLaravel
{
    protected $connection = 'SUPPLYCHAIN';
    protected $table = 'ask_customer';
    protected $primaryKey = 'ask_customer_id';


    //获取列表
    static public function getList($data)
    {

        $obj = self::where('ask_customer_id','>',0);
        foreach ($data as $key=>$val){
            if ($val === '')continue;
            switch ($key){
                case '':
                    $obj = $obj->where($key,$val);break;
            }
        }


        $returnData = $obj->orderBy('ask_customer_id','desc')->paginate(10);
        foreach ($returnData as $key=>$value){
            $value->create_time_cn =  $value->create_time ? date('Y-m-d H:i:s', $value->create_time_cn) : '';
        }
        return $returnData;
    }

}