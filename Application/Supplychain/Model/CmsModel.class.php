<?php
namespace Supplychain\Model;
use Think\Model;

class CmsModel extends Model
{
    protected $autoCheckFields = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.CMS');
    }

    /**
     * 获取数据
     * @return [type]              [description]
     */
    public function getData($table, $where, $select='select', $field='*', $limit='', $order='')
    {
       return $this->table($table)->field($field)->where($where)->order($order)->limit($limit)->$select();
    }


    /**
     * 修改数据
     * @return [type]              [description]
     */
    public function updateData($table, $where, $updateData)
    {
        return $this->table($table)->where($where)->save($updateData);
    }

}
