<?php
namespace Supplychain\Model;


class WmsTallyModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'wms_tally';
    protected $primaryKey = 'wsty_id';
    public $timestamps = false;

    //0正常1美产2商检
    static public $boxTypeCn = [
        0=>'普货',
        1=>'美产',
        2=>'商检',
    ];

    //理货状态 1理货中，2已封箱，3已装板
    static public $tallyStatusCn = [
        1=>'理货中',
        2=>'已封箱',
        3=>'已装板',
    ];

    //箱号规则：X开头+月日+3位顺序号
    //如：X0428001
    public function getBoxSn()
    {
        $data = date('Y-m-d');
        $time = strtotime($data);
        $count = self::where('create_time','>=',$time)
                ->where('create_time','<',$time+86400)
                ->count($this->primaryKey)+1;
        $year = date('y');
        $yearLastDigit = substr($year, 1, 1);
        return 'X'.$yearLastDigit.date('md').str_pad($count,3,'0',STR_PAD_LEFT);
    }


}
