<?php
namespace Supplychain\Model;



class UserDeliveryModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'com_delivery';
    protected $primaryKey = 'com_delivery_id';
    protected $guarded = ['com_delivery_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $fillable = ["user_id","company_id","consignee","zipcode","mobile","intl_code","seat_number",
        "seat_number_code","province","city","district","detail_address","is_default","address_type",
        "status","erp_entry_id" ,'address_area','com_name','com_name_hk','address_area'];

    static public $addressArea= [
        1=>'内地',
        2=>'香港',
        3=>'国际'
    ];


    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }

    public function getZuoJiAttribute($value){
        return $this->seat_number;
    }

}