<?php
namespace Supplychain\Model;

class InlandDeliveryModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'inland_delivery';
    protected $primaryKey = 'inland_delivery_id';
    protected $guarded = ['inland_delivery_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;




    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    static public function getOrderInlandDelivery($order_id,$arr)
    {

        $waitTurnUp = [];
        //没有就返回空
        if (empty($inland = self::where('order_id','=',intval($order_id))->first())){
            $waitTurnUp['outboundtype'] = '';
            $waitTurnUp['outboundfile'] = '';
            $waitTurnUp['deliveryramark'] = '';
            $waitTurnUp['contact'] = '';
            $waitTurnUp['mobile'] = '';
            $waitTurnUp['carrier'] = '';
            $waitTurnUp['transport'] = '';
            $waitTurnUp['paymentfreight'] = '';
            $waitTurnUp['status'] = '';
            $waitTurnUp['paymentfreight'] = '';
            $waitTurnUp['idcard'] = '';
            $waitTurnUp['cardmobile'] = '';
            $waitTurnUp['username'] = '';
        }else{
            $waitTurnUp['outboundtype'] = $inland->outbound_type;
            $waitTurnUp['outboundfile'] = $inland->outbound_file;
            $waitTurnUp['deliveryramark'] = $inland->delivery_ramark;
            $waitTurnUp['contact'] = $inland->contact;
            $waitTurnUp['mobile'] = $inland->mobile;
            $waitTurnUp['carrier'] = $inland->carrier;
            $waitTurnUp['transport'] = $inland->transport;
            $waitTurnUp['paymentfreight'] = $inland->payment_freight;
            $waitTurnUp['status'] = $inland->status;
            $waitTurnUp['paymentfreight'] = $inland->payment_freight;
            $waitTurnUp['idcard'] = $inland->id_card;
            $waitTurnUp['cardmobile'] = $inland->card_mobile;
            $waitTurnUp['username'] = $inland->user_name;
        }


        //如果有地址就存
        if (empty($orderAddress = OrderAddressModel::where('order_id',$order_id)->first())){
            $waitTurnUp['address'] = '';
            $waitTurnUp['addressconsignee'] = '';
            $waitTurnUp['addressmobile'] = '';
            $waitTurnUp['seatnumber'] = '';
        }else{
            //如果是香港，换国际地址查询
            if (OrderModel::where('order_id',$order_id)->value('is_hk_order')){
                $region = new WorldAddress();
                $waitTurnUp['address'] =
                    ($proviceName = $region->where('id',$orderAddress->province)->value('name'))
                    .($cityName = $region->where('id',$orderAddress->city)->value('name'))
                        .($regionName = $region->where('id',$orderAddress->district)->value('name'))
                            .$orderAddress->detail_address;
            }else{
                $region = new \Address\Model\RegionModel();
                $waitTurnUp['address'] =
                ( $proviceName = $region->where(['region_id'=>$orderAddress->province])->getField('region_name'))
                    .($cityName = $region->where(['region_id'=>$orderAddress->city])->getField('region_name'))
                        .($regionName = $region->where(['region_id'=>$orderAddress->district])->getField('region_name'))
                            .$orderAddress->detail_address;
            }
            $waitTurnUp['addressconsignee'] = $orderAddress->consignee;
            $waitTurnUp['addressmobile'] = $orderAddress->mobile;
            $waitTurnUp['seatnumber'] = $orderAddress->seat_number;
        }

        foreach ($waitTurnUp as $key=>$value){
            $arr['ORDERINLAND'][strtoupper($key)] = $value;
        }

        if (empty($orderAddress)){
            $arr['ORDERINLAND']['PTAddressID'] = 0;
            $arr['ORDERINLAND']['provinces'] = '';
            $arr['ORDERINLAND']['city'] = '';
            $arr['ORDERINLAND']['region'] = '';
            $arr['ORDERINLAND']['lastUpdateTime'] = 0;
        }else{
            $arr['ORDERINLAND']['PTAddressID'] = $orderAddress->order_address_id;
            $arr['ORDERINLAND']['provinces'] = $proviceName;
            $arr['ORDERINLAND']['city'] = $cityName;
            $arr['ORDERINLAND']['region'] = $regionName;
            $arr['ORDERINLAND']['lastUpdateTime'] = strtotime($inland->update_time);
        }

        return $arr;
    }




}