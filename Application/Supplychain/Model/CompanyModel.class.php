<?php
namespace Supplychain\Model;


class CompanyModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'company';
    protected $primaryKey = 'company_id';
    protected $guarded = ['company_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $hidden = ['update_time'];
    protected $appends = ['industry_involved_cn'];
    protected $dateFormat = 'Y-m-d';
    protected $fillable = ['company_full_name','address',"establishment_date",'organization_code',
        'legal_person','industry_involved','bus_license_number','bus_license_image','user_id',"unified_social_credit_code",
        "registered_currency","trade_registration","registered_capital","city_id","area_id","country_id","address","province_id"];


    public static $IndustryInvolved=[
        1=>"制造工厂",
        2=>"贸易商",
        3=>"代理商",
    ];

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public function getIndustryInvolvedCnAttribute(){
        return array_get(static::$IndustryInvolved,intval($this->industry_involved),'');
    }

    /**
     * 获取公司名称
     */
    public static function getCompany($company_id){
        return static::where("company_id",$company_id)->first();
    }





}