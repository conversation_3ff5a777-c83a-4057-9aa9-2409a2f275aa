<?php
/**
 * Created by 2020/6/9.
 * User: Joneq
 * Info: 2020/6/9
 * Time: 下午5:08
 */

namespace Supplychain\Model;

use Illuminate\Support\Facades\DB;

use Pcb\Model\LiexinUserMain;
use Supplychain\Exception\SupplyException;
class TakeNumberModel extends BaseModelLaravel
{
    protected $connection = "SUPPLYCHAIN";
    protected $table = 'take_number';
    protected $primaryKey = 'take_num_id';
    protected $guarded = ['take_num_id'];
    const CREATED_AT = 'create_time';
    public $timestamps = false;


    //获取数据列表
    static public function getList($data)
    {
        $obj = self::where('company_id',CustomerModel::where('user_id',$data['user_id'])->value('company_id'));
        foreach ($data as $key=>$value){
            if (empty($value))continue;
            switch ($key){
                case 'erp_order_sn':
                    $obj = $obj->where($key,$value);break;
                case 'type':
                    $obj = $obj->where('erp_order_sn','like',$value.'%');break;
                case 'start_time':
                    $obj = $obj->where('create_time','>=',strtotime($value));break;
                case 'end_time':
                    $obj = $obj->where('create_time','<=',strtotime($value)+86400);break;
                default:
                    continue;
            }
        }

        $page = I('get.page',0,'intval');
        $limit = I('get.limit',15,'intval');
        $count = $obj->count();
        if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
        $list = $obj->orderBy('create_time','desc')->select("*")->paginate($limit,[],'page',$page)->toArray();
        foreach ($list['data'] as $key=>$value){
            $list['data'][$key]['create_time_cn'] = date('Y-m-d H:i:s',$value['create_time']);
            $list['data'][$key]['is_use'] = OrderModel::where('erp_order_sn',$value['erp_order_sn'])->value('order_id')?'已使用':'未使用';
        }
        return $list;
    }

    public function insertInfo($data)
    {
        $data['company_id'] = CustomerModel::where('user_id',$data['user_id'])->value('company_id');
        $accountInfo = LiexinUserMain::where('user_id',$data['user_id'])->select('mobile','email')->first();
        $data['take_user'] = $accountInfo->mobile;
        if (empty($data['take_user'])){
            $data['take_user'] = $accountInfo->email;
        }
        $data['create_time'] = time();
        self::insertGetId($data);
    }
}