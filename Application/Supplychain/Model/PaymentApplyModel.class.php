<?php
namespace Supplychain\Model;

use Supplychain\Exception\SupplyException;
use Exception;
class PaymentApplyModel extends BaseModelLaravel
{
    protected $connection = "SUPPLYCHAIN";
    protected $table = 'payment_apply';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    public $timestamps = false;
    static public $status = ['已提交','已审核','已删除','已取消'];


    //获取数据列表
    static public function getList($data)
    {
        $obj = self::whereIn('status',[0,1])->where('number','!=','')->orderBy('create_time','desc')->groupBy('number');
        foreach ($data as $key=>$value){
            if ($value === '')continue;
            switch ($key){
                case 'user_id':
                case 'status':
                case 'supplier_id':
                case 'pool_number':
                case 'entrust_bill_number':
                case 'settlement_currency':
                $obj = $obj->where($key,$value);break;
                case 'begin_time':
                    $obj = $obj->where('complete_time','>=',strtotime($value));break;
                case 'end_time':
                    $obj = $obj->where('complete_time','<=',strtotime($value));break;
                case 'is_hk_order':
                    if (empty($value)){
                        $obj = $obj->where('entrust_bill_number','like','B%');break;
                    }else{
                        $obj = $obj->where('entrust_bill_number','like','A%');break;
                    }
                default:
                    continue;
            }
        }


        $page = I('get.page',0,'intval');
        $limit = I('get.limit',15,'intval');
        $countObj = clone $obj;
        $count = $countObj->get()->count('number');

        if($page > ceil($count/$limit)) throw new SupplyException('没有更多数据');
        $list = $obj->select("*")->paginate($limit,[],'page',$page)->toArray();
        $settlementTypeCn = [1=>'电汇',3=>'现金支票',4=>'京东金融'];
        foreach ($list['data'] as $key=>$value){
            $list['data'][$key]['status_cn'] = array_get(self::$status,$value['status']);
            $list['data'][$key]['create_time_cn'] = date('Y-m-d H:i:s',$value['create_time']);

            if (empty($value['complete_time'])){
                $list['data'][$key]['complete_time_cn'] = "";
            }else{
                $list['data'][$key]['complete_time_cn'] = date('Y-m-d H:i:s',$value['complete_time']);
            }
            $list['data'][$key]['settlement_type_cn'] = array_get($settlementTypeCn,$value['settlement_type'],'');
            $list['data'][$key]['bank_info'] = SupplierBankModel::where('erp_entry_id',$value['bank_info'])->first();
            $list['data'][$key]['orgin_amount'] =  self::where('number',$value['number'])->whereIn('status',[0,1])->sum('orgin_amount');
            $list['data'][$key]['transferred_amount'] =  self::where('number',$value['number'])->whereIn('status',[0,1])->sum('transferred_amount');;

        }
        return $list;
    }


    //前台传来的数据进行插入
    static public function insertData($data)
    {
        /*
         * 元数据
         *         "poolNumber": "",--资金池编码
                    "serveProject": "",--计费项目
                    "orginAmount": "",--原币金额
                    "OrginCurrency": "",--原币币别
                    "settlementCurrency": "",--结算币别
                    "settlementAmount": "",--结算金额
                    "verifiAmount": "",--核销金额
                    "verifiOrgAmount": "",--核销原币金额
                    "exchangeRate": "",--汇率
                    "bizFlow": "",--业务流程
                    "orderRate": "",--下单日汇率
                    "entrustBillNumber": "",--委托单号
                    "paidAmount": "",--已付金额
                    "noPaidAmount": "",--未付金额
                    "convertRMBAmount": "",--折合人民币金额
                    "supplier": ""--供应商
         */

        $insertData = [];
        foreach ($data['details'] as $value){
            $insertData['pool_number'] = $value['poolNumber'];
            $insertData['serve_project'] = $value['serveProject'];
            $insertData['orgin_amount'] = $value['orginAmount'];
            $insertData['orgin_currency'] = $value['OrginCurrency'];
            $insertData['settlement_currency'] = $value['settlementCurrency'];
            $insertData['settlement_amount'] = $value['settlementAmount'];
            $insertData['verifi_amount'] = $value['verifiAmount'];
            $insertData['verifi_org_amount'] = $value['verifiOrgAmount'];
            $insertData['exchange_rate'] = $value['exchangeRate'];
            $insertData['biz_flow'] = $value['bizFlow'];
            $insertData['order_rate'] = $value['orderRate'];
            $insertData['entrust_bill_number'] = $value['entrustBillNumber'];
            $insertData['paid_amount'] = $value['paidAmount'];
            $insertData['no_paid_amount'] = $value['noPaidAmount'];
            $insertData['convert_rmb_amount'] = $value['convertRMBAmount'];
            $insertData['supplier'] = $value['supplier'];
            if (isset($data['supplier_id'])){
                $supplierId = $data['supplier_id'];
            }elseif(!empty($id = SupplierBankModel::where('erp_entry_id',$data['bankInfo'])->value('supplier_id'))){
                $supplierId = $id;
            }elseif(!empty($id = SupplierModel::where('supplier_name',$data['bankInfo'])->value('supplier_id')) ){
                $supplierId = $id;
            }else{
                throw  new Exception('供应商ID不存在');
            }
            $insertData['supplier_id'] = $supplierId;
            $insertData['create_time'] = time();
            $insertData['user_id'] = $data['user_id'];
            $insertData['bank_info'] = $data['bankInfo'];
            $insertData['number'] = $data['number'];
            $insertData['eas_remark'] = isset($data['eas_remark'])?$data['eas_remark']:'';
            $insertData['settlement_type'] = $data['settlement_type'];
            $insertData['file_url'] = $data['attachment'];
            $insertData['transferred_amount'] = empty($data['transferred_amount'])?'':$data['transferred_amount'];
            $insertData['transfer_serial_number'] = empty($data['transfer_serial_number'])?'':$data['transfer_serial_number'];

            //银行手续费自动生成是空,货后自己选
            $insertData['bank_charges'] = $data['bank_charges'];
            OrderModel::where('erp_order_sn',$value['entrustBillNumber'])->update(['is_apply_payment'=>1]);
            self::insertGetId($insertData);
        }

    }


    //前台传来的数据进行更新
    static public function updateData($data)
    {
        /*
         * 元数据
         *         "poolNumber": "",--资金池编码
                    "serveProject": "",--计费项目
                    "orginAmount": "",--原币金额
                    "OrginCurrency": "",--原币币别
                    "settlementCurrency": "",--结算币别
                    "settlementAmount": "",--结算金额
                    "verifiAmount": "",--核销金额
                    "verifiOrgAmount": "",--核销原币金额
                    "exchangeRate": "",--汇率
                    "bizFlow": "",--业务流程
                    "orderRate": "",--下单日汇率
                    "entrustBillNumber": "",--委托单号
                    "paidAmount": "",--已付金额
                    "noPaidAmount": "",--未付金额
                    "convertRMBAmount": "",--折合人民币金额
                    "supplier": ""--供应商
         */

        foreach ($data['details'] as $value){
            self::where('pool_number',$value['pool_number'])
                ->where('user_id',$data['user_id'])
                ->whereIn('status',[0,1])
                ->update([
                    'verifi_amount'=>$value['verifi_amount'],
                    'bank_info'=>$data['bank_info'],
                    'settlement_type'=>$data['settlement_type'],
                ]);
        }

    }



    //自动生成付汇申请数据   https://www.tapd.cn/********/prong/stories/view/11********001003874
    //2、获取到erp委托单审核的消息后。如果为货前TT，且委托单有选择银行信息，且为供应链平台新增的单据。则系统自动生成 付汇申请数据（同现有付汇申请 逻辑。即原在付汇申请中，手动选择 付汇资金流，改为系统自动读取）。并同步至erp。
    //3、获取到erp委托单审核的消息后。如果为COD，且为供应链平台新增的单据。则系统自动生成 付汇申请数据，无银行信息，但以供应商名称，作为收款方记录至付汇申请中。并同步至erp。
    static public function autoInsertData($orderInfo)
    {
        if (empty($orderInfo)){
            throw new Exception('订单信息不存在');
        }

        $data = [];
        $data['company_id'] = $orderInfo->company_id;
        $data['entrustNumber'] = $orderInfo->erp_order_sn;
        $insertData['details'] = self::getPayMentApply($data);
        $insertData['supplier_id'] = $orderInfo->supplier_id;
        if (empty($insertData['details'])){
            throw new Exception($orderInfo->erp_order_sn.'该入仓单号没有获取到付汇数据');
        }

        //修改付汇应付本币金额为订单预付金额
         foreach ($insertData['details'] as $key=>$value){
            $insertData['details'][$key]['verifiAmount'] = OrderBankInfoModel::where('order_id',$orderInfo->order_id)->value('pay_amount');
         }

        //如果为货前tt
        if ($orderInfo->overseas_settlement_type === 1){
            //存在银行信息
            if (!empty($bankId = OrderBankInfoModel::where('order_id',$orderInfo->order_id)->value('supplier_bank_id'))){

                if (empty($supplierBankInfo = SupplierBankModel::where('supplier_bank_id',$bankId)->first())){
                    throw new Exception('bankid'.$bankId.'没有找到银行信息');
                }

                $insertData['user_id'] = $orderInfo->user_id;
                $insertData['bankInfo'] = $supplierBankInfo->erp_entry_id;
                $insertData['order_id'] = $orderInfo->order_id;
                $insertData['supplier_id'] = $orderInfo->supplier_id;
                $insertData['attachment'] = $orderInfo->payment_apply_file;
                //银行手续费自动生成是空
                $insertData['bank_charges'] = 0;
                if (!empty($orderInfo->order_invoice_file)){
                    $insertData['attachment'] .= ','.$orderInfo->order_invoice_file;
                }
                $insertData['settlement_type'] = $orderInfo->overseas_settlement_type;

                self::insertPayMentApply($insertData);
            }else{
                throw new Exception('没有找到订单银行信息');
            }
            //如果为cod
        }elseif($orderInfo->overseas_settlement_type === 3){
            $insertData['user_id'] = $orderInfo->user_id;
            $insertData['bankInfo'] = SupplierModel::where('supplier_id',$orderInfo->supplier_id)->value('supplier_name');
            $insertData['order_id'] = $orderInfo->order_id;
            $insertData['supplier_id'] = $orderInfo->supplier_id;
            $insertData['attachment'] = $orderInfo->payment_apply_file;
            //银行手续费自动生成是空
            $insertData['bank_charges'] = 0;
            if (!empty($orderInfo->order_invoice_file)){
                $insertData['attachment'] .= ','.$orderInfo->order_invoice_file;
            }
            $insertData['settlement_type'] = $orderInfo->overseas_settlement_type;
            self::insertPayMentApply($insertData);
        }
        return [];
    }


    //获取付汇申请信息
    static public function getPayMentApply($post)
    {
        if (!isset($post['entrustNumber']) || empty($post['entrustNumber'])){
            throw new Exception('入仓单号不存在');
        }

        //获取汇率数组
        $Redis = new \Redis();
        $Redis->connect(C('UPDATE_SHOPPING_REMIND_REDIS_HOST'),C('REDIS_PORT'));
        $Redis->auth(C('REDIS_PASSWORD'));
        $exchangeRate = $Redis->get('ErpExchangeRate');
        $exchangeRate = json_decode($exchangeRate,true);
        $rateArr = [];
        foreach($exchangeRate as $value){
            foreach ($value as $k=>$v){
                $rateArr[$k] = $v;
            }
        }

        $creditData = (new \Supplychain\Controller\ErpPushController)->getPayLoaclGoods($post);
        if(!$creditData) return [];
        foreach ($creditData as $key=>$val){
            $supplyInfo = SupplierModel::where('supplier_name',$val['supplier'])->select('supplier_id','user_id')->first();
            if (empty($supplyInfo)){
                $creditData[$key]['erpClientCode'] = "";
                $creditData[$key]['supplierId'] = "";
            }else{
                $creditData[$key]['erpClientCode'] = CustomerModel::where('user_id',$supplyInfo->user_id)->value('erp_client_code');
                $creditData[$key]['supplierId'] = $supplyInfo->supplier_id;
            }
            $creditData[$key]['exchangeRate'] = isset($rateArr[$creditData[$key]['settlementCurrency']])?$rateArr[$creditData[$key]['settlementCurrency']]:1;
        }
        return $creditData;
    }

    //增加付汇申请数据
    static public function insertPayMentApply($post)
    {

        $erp_supplier_id = SupplierModel::where('supplier_id',$post['supplier_id'])->value('erp_supplier_id');
        if(!$erp_supplier_id) throw new \Exception("委托方id为空");

        //需要的数据 ['bankInfo'=>'银行信息','details'=>[['poolNumber'=>'test','verityAmt'=>1]]] ,传给供应链
        $createDataSource['bankInfo'] = $post['bankInfo'];
        $createDataSource['attachment'] = $post['attachment'];
        $createDataSource['settlement_type'] = $post['settlement_type'];
        $createDataSource['remake'] = $post['eas_remark'];
        $createDataSource['supplierID'] = $erp_supplier_id;
        $createDataSource['BANKCHARGES'] = $post['bank_charges'];
        foreach ($post['details'] as $value){
            $createDataSource['details'][] = ['poolNumber'=>$value['poolNumber'],'verityAmt'=>$value['verifiAmount']];
        }


        \Think\Log::write('增加付汇申请数据-----------------','WARN');
        \Think\Log::write(\GuzzleHttp\json_encode($createDataSource),'WARN');



        $creditData = (new \Supplychain\Controller\ErpPushController)->createPayLocalGood(json_encode($createDataSource));
        if(!$creditData) throw new SupplyException("网络繁忙，稍后重试");
        if (!isset($creditData['0000']['number'])){
            if (isset($creditData['4444'])){
                throw new SupplyException($creditData['4444']);
            }else{
                throw new SupplyException('网络繁忙，添加失败，稍后重试');
            }
        }else{
            $number = $creditData['0000']['number'];
        }
        $post['number'] = $number;
        //本地插入
        PaymentApplyModel::insertData($post);
        return ['number'=>$creditData['0000']['number'],'create_time'=>date('Y-m-d H:i:s')];
    }



}