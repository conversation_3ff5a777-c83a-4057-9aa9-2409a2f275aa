<?php
namespace Supplychain\Model;


class WmsTallyAbnormalDetailModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'wms_tally_abnormal_detail';
    protected $primaryKey = 'wstyaldl_id';
    public $timestamps = false;

    //商检确认状态0待确认1已确认
    static public  $GOODS_CHECK_STATUS = [
        0=>'待确认',
        1=>'已确认',
    ];

    //产地确认状态 0待确认，1已确认，2确认失败
    static public  $ORIGIN_STATUS = [
        0=>'待确认',
        1=>'已确认',
        2=>'确认失败',
    ];

    static public $PAYER_TYPE = [
        0=>'无',
        1=>'客户承担',
        2=>'公司承担',
        3=>'免美产排除编号'
    ];

    static public function getWhereObj($where)
    {
        $obj = self::orderBy('wstyaldl_id','desc');
        foreach ($where as $sqlKey=>$value){
            if ($value === ''){
                continue;
            }
            switch ($sqlKey){
                case 'goods_type':
                case 'erp_order_sn':
                case 'wsty_id':
                case 'wstyaldl_id':
                case 'type':
                case 'abnormal_batch':
                case 'origin_confirm_status':
                $obj = $obj->where($sqlKey,$value);break;

                case 'box_sn_not':
                    if ($value === false){
                        $value = '';
                        $obj = $obj->where('box_sn','!=',$value);break;
                    }else{
                        $value = '';
                        $obj = $obj->where('wsty_id','=',0);break;
                    }
                case 'confirm_time':
                    $obj = $obj->where('confirm_time','<=',strtotime($value));break;
                case 'pack_time':
                    $timeRange = explode('~',$value);
                    $obj = $obj->where('pack_time','>=',strtotime(trim($timeRange[0])));
                    $obj = $obj->where('pack_time','<=',strtotime(trim($timeRange[1]))+86400);break;
                    break;
            }
        }

        return $obj;
    }


    //箱号规则：X开头+月日+3位顺序号
    //如：X0428001
    static public function getAbnormalSn()
    {
        $formattedString = sprintf('PC%s%02d%02d', substr(date('Y'), 2, 4), date('m'), date('d'));
        $last_abnormal_batch_info = self::where('abnormal_batch','like',$formattedString.'%')
            ->orderBy('abnormal_batch','desc')
            ->select('abnormal_batch')
            ->first();
        if (empty($last_abnormal_batch_info)){
            $count = 1;
        } else {
            $count = intval(substr($last_abnormal_batch_info['abnormal_batch'],-3)) + 1;
        }
        return $formattedString.sprintf('%03d', $count);
    }
}
