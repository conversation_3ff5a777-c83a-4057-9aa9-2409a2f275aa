<?php
namespace Supplychain\Model;

use \Supplychain\Model\UnitModel;
use \Supplychain\Model\PayCostModel;

class WaterSingleModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'water_single';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $dateFormat = 'Y-m-d';
    // protected $fillable = [];

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }

    // 获取预计应收款
    public function hasManyPayCost()
    {
        return $this->hasMany(PayCostModel::class, 'water_single_id', 'id');
    }

    /**
     * 状态查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchStatus($query, $status)
    {
        if($status != ''){
            $query = $query->where('status', $status);
        }
        return $query;
    }

    /**
     * 时间查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchTime($query, $start_time, $end_time)
    {
        if($start_time != '' && $start_time){
            $query = $query->where('create_time',">=",strtotime($start_time." 00:00:00"));
        }
        if($end_time != '' && $end_time){
            $query = $query->where('create_time',"<=",strtotime($end_time." 23:59:59"));
        }
        return $query;
    }

}