<?php
namespace Supplychain\Model;
use \Supplychain\Model\UnitModel;

class HongKongDeliveryModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'hongkong_delivery';
    protected $primaryKey = 'hk_delivery_id';
    protected $guarded = ['hk_delivery_id'];
    public $timestamps = false;
    protected $fillable = ["instead_contact","order_id","instead_address","instead_mobile","instead_iandline","instead_area_code"
        ,'delivery_number','delivery_weight','remark','express_delivery_country','express_number','get_time','is_need_board','board_remark',
        'hk_com_delivery_id','hk_carrier','estimated_arrival_time','hkplatform_sendcar_file'];

    public static $HongkongDeliveryBySupplierStatus=1;//供应商配送
    public static $HongkongDeliveryByReplaceStatus=2;//代为提货
    public static $HongkongDeliveryByExpresStatus=3;//快递来货

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }



    static public function getOrderDelivery($order_id,$arr)
    {

        $waitTurnUp = [];
        //没有就返回空
        if (empty($delivery = self::where('order_id','=',intval($order_id))->first())){
            $waitTurnUp['insteadcontact'] = '';
            $waitTurnUp['insteadaddress'] = '';
            $waitTurnUp['insteadmobile'] = '';
            $waitTurnUp['insteadiandline'] = '';
            $waitTurnUp['insteadarea_code'] = '';
            $waitTurnUp['deliverynumber'] = '';
            $waitTurnUp['deliveryweight'] = '';
            $waitTurnUp['remark'] = '';
            $waitTurnUp['expressdeliverycountry'] = '';
            $waitTurnUp['expressnumber'] = '';
            $waitTurnUp['get_time'] = '';

            //新增提货卡板
            $waitTurnUp['estimatedarrivaltime'] = '';
            $waitTurnUp['isneedboard'] = '';
            $waitTurnUp['boardremark'] = '';
            $waitTurnUp['hkcarrier'] = '';
            $waitTurnUp['hkport'] = '';
            $waitTurnUp['hkplatformsendcarfile'] = '';
            $waitTurnUp['pthkaddressid'] = '';

        }else{
            $waitTurnUp['insteadcontact'] = $delivery->instead_contact;
            $waitTurnUp['insteadaddress'] = $delivery->instead_address;
            $waitTurnUp['insteadmobile'] = $delivery->instead_mobile;
            $waitTurnUp['insteadiandline'] = $delivery->instead_iandline;
            $waitTurnUp['insteadarea_code'] = $delivery->instead_area_code;
            $waitTurnUp['deliverynumber'] = $delivery->delivery_number;
            $waitTurnUp['deliveryweight'] = $delivery->delivery_weight;
            $waitTurnUp['remark'] = $delivery->remark;
            $waitTurnUp['expressdeliverycountry'] = $delivery->express_delivery_country;
            $waitTurnUp['expressnumber'] = $delivery->express_number;
            $waitTurnUp['get_time'] = $delivery->get_time;

            //新增提货卡板
            $waitTurnUp['estimatedarrivaltime'] = $delivery->estimated_arrival_time;
            $waitTurnUp['isneedboard'] = $delivery->is_need_board;
            $waitTurnUp['boardremark'] = $delivery->board_remark;
            $waitTurnUp['hkcarrier'] = $delivery->hk_carrier;
            $waitTurnUp['hkport'] = $delivery->hk_port;
            $waitTurnUp['hkplatformsendcarfile'] = $delivery->hkplatform_sendcar_file;
            $waitTurnUp['pthkaddressid'] = $delivery->hk_delivery_id;
        }

        //如果有地址就存
        if (empty($orderAddress = OrderAddressModel::where('order_id',$order_id)->first())){
            $waitTurnUp['address'] = '';
            $waitTurnUp['addressconsignee'] = '';
            $waitTurnUp['addressmobile'] = '';
            $waitTurnUp['seatnumber'] = '';
        }else{

            //如果是香港，换国际地址查询
            if (OrderModel::where('order_id',$order_id)->value('is_hk_order')){
                $region = new WorldAddress();
                $waitTurnUp['address'] = $region->where('id',$orderAddress->province)->value('name').$region->where('id',$orderAddress->city)->value('name').$region->where('id',$orderAddress->district)->value('name').$orderAddress->detail_address;
            }else{
                $region = new \Address\Model\RegionModel();
                $waitTurnUp['address'] = $region->where(['region_id'=>$orderAddress->province])->getField('region_name').$region->where(['region_id'=>$orderAddress->city])->getField('region_name').$region->where(['region_id'=>$orderAddress->district])->getField('region_name').$orderAddress->detail_address;
            }

            $waitTurnUp['addressconsignee'] = $orderAddress->consignee;
            $waitTurnUp['addressmobile'] = $orderAddress->mobile;
            $waitTurnUp['seatnumber'] = $orderAddress->seat_number;
        }

        foreach ($waitTurnUp as $key=>$value){
            $arr['ORDERDELIVERY'][strtoupper($key)] = $value;
        }

        return $arr;
    }



    //获取数据列表
    static public function getList($data)
    {
        $obj = self::orderBy('hk_delivery_id','desc');
        foreach ($data as $key=>$value){
            if ($value==="")continue;
            switch ($key){
                case 'user_id':
                    $obj = $obj->whereIn('order_id',OrderModel::where('user_id',$value)->pluck('order_id'));
                    break;
                case 'hk_delivery_status':
                    //待签收是1，已经签收是2
                    if ($value == '1'){
                        $hk_delivery_status = [0,1];
                    }else{
                        $hk_delivery_status = [2];
                    }
                    $obj = $obj->whereIn('order_id',OrderStatusModel::where('user_id',$value)->where('hk_delivery_status',$hk_delivery_status)->pluck('order_id'));break;
                case 'hongkong_delivery_type':
                    $obj = $obj->whereIn('order_id',OrderModel::where('user_id',$value)->where('hongkong_delivery_type',$value)->pluck('order_id'));break;
                case 'erp_order_sn':
                    if (empty($orderId = OrderModel::where('erp_order_sn',$value)->value('order_id'))){
                        $orderId = -1;
                    }
                    $obj = $obj->where('order_id',$orderId);break;
                case 'express_number':
                    $obj = $obj->where('express_number',$value);break;
                case 'begin_time':
                    $obj = $obj->where('get_time','>=',strtotime($value));break;
                case 'end_time':
                    $obj = $obj->where('get_time','<=',strtotime($value));break;
                default:
                    continue;
            }
        }

        $page = I('get.page',0,'intval');
        $limit = I('get.limit',15,'intval');
        $list = $obj->select("*")->paginate($limit,[],'page',$page)->toArray();
        if($page > ceil($list['total']/$limit)) throw new \Exception('没有更多数据');

        foreach ($list['data'] as $key=>$value){
            $orderInfo = OrderModel::where('order_id',$value['order_id'])->first();
            $list['data'][$key]['get_time'] = date('Y-m-d H:i:s',$value['get_time']);
            $list['data'][$key]['estimated_arrival_time'] = date('Y-m-d H:i:s',$value['estimated_arrival_time']);
            $list['data'][$key]['erp_order_sn'] = $orderInfo->erp_order_sn;
            $list['data'][$key]['hongkong_delivery_type'] = $orderInfo->hongkong_delivery_type;
            $list['data'][$key]['hongkong_delivery_type_cn'] = array_get(OrderModel::$HongkongDeliveryType,$orderInfo->hongkong_delivery_type);
            $list['data'][$key]['order_status'] = OrderStatusModel::where('order_id',$value['order_id'])->value('hk_delivery_status');
            $list['data'][$key]['order_status_cn'] = array_get(OrderStatusModel::$hkDeliveryStatus,$value['order_status']?$value['order_status']:1);
        }
        return $list;
    }



}