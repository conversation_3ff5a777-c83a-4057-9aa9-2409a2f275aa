<?php
namespace Supplychain\Model;

use Supplychain\Model\ServiceAgreementListModel;

class ServiceAgreementModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'service_agreement';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = null;
    public $timestamps = true;


    public static $Status=[
        0  => '已删除',
        1  => '正常',
    ];

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public function service_agreement_list(){
        return $this->hasMany(ServiceAgreementListModel::class,"service_agree_id","id");
    }

}