<?php
namespace Supplychain\Model;

use Supplychain\Model\BaoGuanOrderListModel;

class SZDeliveryNoteListModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'sz_delivery_note_list';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = NULL;
    public $timestamps = true;




    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }




    /**
     * 获取商品总数据
     */
    public function getGoodsNums($order_id){
        return $this->where("order_id",intval($order_id))->sum('qty');
    }



}