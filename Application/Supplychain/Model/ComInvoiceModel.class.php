<?php
namespace Supplychain\Model;


class ComInvoiceModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'com_invoice';
    protected $primaryKey = 'com_invoice_id';
    protected $guarded = ['com_invoice_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $hidden = ['update_time'];
    protected $appends = ['type_cn'];

    protected $fillable = ["tax_identifying_sn","type","tax_certificate_no","invoice_address","invoice_mobile",
        "bank_name","bank_number","invoice_image","user_id","company_id","status"];
    protected $casts = [
    ];




    public static $TYPE=[
        1=>"普通发票",
        2=>"增值税专用发票",
    ];

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }

    public function getTypeCnAttribute($value){
        return array_get(static::$TYPE,intval($this->type),'');
    }



}