<?php


namespace Supplychain\Model;



Class ActionLogModel extends BaseModelLaravel
{
    protected $connection = 'SUPPLYCHAIN';
    protected $table = 'action_log';
    protected $primaryKey = 'id';
    public $timestamps = false;

    /**
     * 添加操作记录
     * @param [type] $user_id  [用户ID]
     * @param [type] $content  [操作内容]
     * @param [type] $opreator [操作人]
     * @param [type] $type     [操作类型：1-订单，2-客户资料，3-供应商，4-供应商银行账户，5-客户银行账户,6-问客小程序]
     */
    public function addLog($user_id, $content, $opreator, $type)
    {
        $data['user_id'] = $user_id;
        $data['content'] = $content;
        $data['type']    = $type;
        $data['admin']   = $opreator;
        $data['create_time'] = time();

        return $this->insert($data);
    }

}