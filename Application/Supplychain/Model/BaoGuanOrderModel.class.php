<?php
namespace Supplychain\Model;
use Supplychain\Model\BaoGuanOrderListModel;

class BaoGuanOrderModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'baoguan_order';
    protected $primaryKey = 'baoguan_order_id';
    protected $guarded = ['baoguan_order_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = NULL;
    public $timestamps = true;


    //通关方式
    public static $CfclearanceType=[
        0=>'汇总征税',
        1=>'先征后方',
    ];


    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public function baoguan_order_list(){
        return $this->hasMany(BaoGuanOrderListModel::class,"baoguan_order_id","baoguan_order_id");
    }





}