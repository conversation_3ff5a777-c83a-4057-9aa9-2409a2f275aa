<?php
namespace Supplychain\Model;

class ContactModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'contact';
    protected $primaryKey = 'contact_id';
    protected $guarded = ['contact_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $hidden = ['update_time'];
    protected $appends = ['source_cn'];
    protected $fillable = ["erp_entry_id","contact_name","contact_phone","special_plane","fax","qq","wechat","source","is_customs_agreements","user_id","company_id"];
    protected $casts = [
    ];


    public static $Source=[
        1=>"百度搜索",
        2=>"朋友推荐",
        3=>"展会",
        4=>"其它",
    ];

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    public function getSourceCnAttribute($value){
        return array_get(static::$Source,intval($this->source),'');
    }


}