<?php
namespace Supplychain\Model;

use \Supplychain\Model\UnitModel;

class OrderAddressGoodsModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'order_address_goods';
    protected $primaryKey = 'address_goods_id';
    protected $guarded = ['address_goods_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;





    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }






}