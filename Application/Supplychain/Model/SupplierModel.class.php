<?php
namespace Supplychain\Model;

use Supplychain\Model\SupplierBankModel;

class SupplierModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'supplier';
    protected $primaryKey = 'supplier_id';
    protected $guarded = ['supplier_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $fillable = ["supplier_name","supplier_short_name","status","user_id","company_id","bus_license_number",
        "bus_license_image","bus_regist_cert_image","erp_supplier_code","erp_supplier_id","is_push","status","auditor","audit_time"];


    public static $Status=[
        -1 => '审核未通过',
        0  => '审核中',
        1  => '通过',
    ];

    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }


    /**
     * 状态查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchStatus($query,$status){
        if($status != ''){
            $query = $query->where('status',$status);
        }
        return $query;
    }



    /**
     * 状态查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchSupplierName($query,$supplier_name){
        if($supplier_name != ''){
            $query = $query->where('supplier_name','like',"%".trim($supplier_name)."%");
        }
        return $query;
    }

    public function getStatusCnAttribute(){
        return array_get(static::$Status,$this->status,'');
    }


    /**
     *1对多关联模型
     * 供应商银行
     */
    public function supplier_banks(){
        return $this->belongsTo(SupplierBankModel::class, 'supplier_id', 'supplier_id');
    }

}