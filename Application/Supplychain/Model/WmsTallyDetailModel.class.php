<?php
namespace Supplychain\Model;


class WmsTallyDetailModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'wms_tally_detail';
    protected $primaryKey = 'wstydl_id';
    public $timestamps = false;

    static public $SYNC_STATUS_ALL = [
        1 => '待推送',
        2 => '推送中',
        3 => '推送成功',
        4 => '推送失败',
    ];

    static public $DECLARATION_TYPE_ALL = [
        3 => '两步申报',
        4 => '非两步',
        ];

    static public function getWhereObj($where)
    {
        $obj = self::orderBy('tally_status','asc')->where(function ($query){
            $query->where('is_base',0)
                ->orWhere('wait_tally_num','>',0);
        });
        foreach ($where as $sqlKey=>$value){
            if ($value === ''){
                continue;
            }
            switch ($sqlKey){
                case 'goods_type':
                case 'erp_order_sn':
                case 'wsty_id':
                case 'box_sn':
                case 'is_base':
                $obj = $obj->where($sqlKey,$value);break;
                case 'all_search':
                    $obj->where(function ($query)use($value){
                        $query->where('goods_type',$value)
                            ->orWhere('erp_order_sn',$value);
                    });break;

                    break;
            }
        }

        return $obj;
    }



    static public function getHaveTallyNum($orderGoodsId)
    {
        return self::where('order_goods_id',$orderGoodsId)->sum('tally_num');
    }

}
