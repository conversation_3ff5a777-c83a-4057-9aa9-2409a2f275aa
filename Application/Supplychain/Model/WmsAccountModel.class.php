<?php
namespace Supplychain\Model;


class WmsAccountModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'wms_account';
    protected $primaryKey = 'wsat_id';
    public $timestamps = false;



    static public function getLogInfo($userId)
    {
        $accountInfo = self::where('wsat_id',$userId)->first();
        return ['name'=>$accountInfo['name'],'email'=>$accountInfo['relation_admin_account']];
    }

}
