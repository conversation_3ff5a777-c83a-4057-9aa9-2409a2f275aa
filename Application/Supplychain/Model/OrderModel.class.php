<?php
namespace Supplychain\Model;
use \Supplychain\Model\UnitModel;
use Supplychain\Model\OrderGoodsModel;
use Supplychain\Model\CustomerModel;
use Supplychain\Model\OrderStatusModel;

class OrderModel extends BaseModelLaravel
{
    protected $connection="SUPPLYCHAIN";
    protected $table = 'order';
    protected $primaryKey = 'order_id';
    protected $guarded = ['order_id'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    public $timestamps = true;
    protected $dates = ["arrival_time"];
    protected $hidden = ['update_time'];
    protected $dateFormat = 'Y-m-d H:i:s';
    protected $fillable = ["erp_order_id","erp_order_sn","currency_id","order_price","supplier_name","supplier_id","arrival_time","overseas_settlement_type","overseas_settlement_days",
        "order_invoice_file","supplier_contact","supplier_phone","landline_code","supplier_email","hongkong_delivery_type","inland_delivery_type","attachment",
        "taking_delivery","invoice_remark","status","user_id","company_id","order_sn","hk_delivery_address_id","id_edit_order","edit_order_id","is_push",'erp_other_param','pick_box_file',
        'payment_apply_file','order_remark','old_erp_order_sn','order_type','is_hk_order','carrier','logistics_no','salesman','customs_code'];

    public static $CancelStatus = -3;//取消订单
    public static $WaitToSubmitStatus = -2;//待提交订单
    public static $PassStatus = 1;//审核通过
    public static $NOPassStatus = -1;//审核不通过
    public static $FinishDeliveryStatus = 8;//完全收货
    public static $SuccessStatus = 10;//已完成
    public static $AuditINGStatus = 0;//审核中
    public static $AdjustmentStatus = 3;//调整中

    public static $ichuntCompanyId  = [407,89];
    public static $stockInStatus = [
        0=>'待入库',
        1=>'部分入库',
        2=>'全部入库',
    ];



    public function fromDateTime($value){
        return strtotime(parent::fromDateTime($value));
    }

    public static $InlandDeliveryType = [
        1=>'整批',
        2=>'分批',
    ];

    public static $HongkongDeliveryType = [
        1=>'供应商配送',
        2=>'代为提货',
        3=>'快递来货'
    ];


    public static $STATUS=[
        -3=>"已取消",
        -2=>"待提交订单",
        -1=>"审核不通过",
        0=>"审核中",
        1=>'审核通过',
        3=>'调整中',
        10=>'已完成',
    ];




    public static function findSn()
    {
        $sn = "5".date('Ymd').rand(10000, 99999);
        $order_id = static::where(array('order_sn' => $sn))->pluck('order_id');
        if (!$order_id->isEmpty()) {
            $sn = static::findSn();
        }
        return $sn;
    }



    public static $search_order_status=[
        12=>'hk_dsh',//HK待收货
        13=>'hk_f_delivery',//HK已到货
        14=>'baoguan_f',//清关完成
        15=>'wait_send',//待配送
        16=>'f_send',//已配送
        17=>'wait_delivery',//待收货
        20=>'f_delivery',//已收货
    ];


    //HK待收货
    public function hk_dsh($status,$company_id,$query){
        $query->where("order.status",OrderModel::$PassStatus);
        $query->where("order_status.company_id",$company_id);
        $query->whereIn('hk_delivery_status',[0,1]);
        return $query;
    }


    //HK已到货
    public function hk_f_delivery($status,$company_id,$query){
        $query->where(function($_query) use($company_id){
            $_query->where("order_status.company_id",$company_id)->where('hk_delivery_status', 2)->where("baoguan_status","!=",2);
        })->orWhere(function ($_query) use($company_id) {
            $_query->where("order_status.company_id",$company_id)->where('hk_delivery_status', 1)->where("baoguan_status",0);
        });
        return $query;
    }


    //清关完成
    public function baoguan_f($status,$company_id,$query){
        $query->where(function($_query) use($company_id){
            $_query->where("order_status.company_id",$company_id)->where('baoguan_status', 2)->where("sz_delivery_status","!=",2);
        })->orWhere(function ($_query) use($company_id) {
            $_query->where("order_status.company_id",$company_id)->where('baoguan_status', 1)->where("sz_delivery_status",0);
        });
        return $query;
    }



    //待配送
    public function wait_send($status,$company_id,$query){
        $query->where(function($_query) use($company_id){
            $_query->where("order_status.company_id",$company_id)->where('sz_delivery_status', 2)->where("sz_send_status","!=",2);
        })->orWhere(function ($_query)  use($company_id){
            $_query->where("order_status.company_id",$company_id)->where('sz_delivery_status', 1)->where("sz_send_status",0);
        });
        return $query;
    }



    //已配送
    public function f_send($status,$company_id,$query){
        $query->where(function($_query) use($company_id){
            $_query->where("order_status.company_id",$company_id)->where('sz_send_status',"!=", 0)->where("order.status","!=","8");
        });
        return $query;
    }


    //待收货
    public function wait_delivery($status,$company_id,$query){
        $query = $query->where('order.status',">=",static::$PassStatus)->where('order.status',"<",static::$SuccessStatus);
        return $query;
    }

    //已收货
    public function f_delivery($status,$company_id,$query){
        $query = $query->where('order.status',static::$FinishDeliveryStatus);
        return $query;
    }



    /**
     * 状态查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchStatus($query,$status,$company_id){
        if($status != '' && $status <= static::$SuccessStatus){
            $query = $query->where('status',$status);
        }elseif($status && $status > static::$SuccessStatus){
            $query->join("order_status",'order_status.order_id',"=","order.order_id");
            $query->where(function($query) use($status,$company_id){
                call_user_func_array([$this,array_get(static::$search_order_status,$status)],[$status,$company_id,$query]);
            });

        }
        return $query;
    }

    /**
     * 供应商查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchSupplier($query,$supplier_id){
        if($supplier_id != ''){
            $query = $query->where('supplier_id',$supplier_id);
        }
        return $query;
    }

    /**
     * 订单类型查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchOrderType($query,$order_type){
        if($order_type != ''){
            if($order_type == 1){
                $query = $query->where('overseas_settlement_days',0);
            }elseif($order_type == 2){
                $query = $query->where('overseas_settlement_days',"<>",0);
            }

        }
        return $query;
    }

    /**
     * 币别查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchCurrency($query,$currency_id){
        if($currency_id != ''){
            $query = $query->where('currency_id',$currency_id);
        }
        return $query;
    }


    /**
     * 订单号查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchOrderSn($query,$order_sn){
        if($order_sn != ''){
            $query = $query->where('order_sn',$order_sn);
        }
        return $query;
    }

    /**
     * 订单号查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchWarehousingSn($query,$order_sn){
        if($order_sn != ''){
            $query = $query->where('erp_order_sn',$order_sn);
        }
        return $query;
    }

    /**
     * 时间查询
     * @param $query
     * @param $status
     * @return mixed
     */
    public function scopeSearchTime($query,$start_time,$end_time){
        if($start_time != '' && $start_time){
            $query = $query->where('create_time',">=",strtotime($start_time." 00:00:00"));
        }
        if($end_time != '' && $end_time){
            $query = $query->where('create_time',"<=",strtotime($end_time." 23:59:59"));
        }
        return $query;
    }


    /**
     * 获取账期
     */
    public function getOverseasSettlementDaysStrAttribute(){
        if($this->overseas_settlement_days == 0){
            return "非账期订单";
        }else{
            if(strtotime($this->create_time) == 0){
                return '-';
            }
//            return time()."~".strtotime($this->create_time);
            return $this->overseas_settlement_days-ceil((time()-strtotime($this->create_time))/86400);
        }
    }



    public function order_goods(){
        return $this->hasMany(OrderGoodsModel::class,"order_id","order_id")->orderBy('order_goods_id','desc');
    }

    public function order_status(){
        return $this->hasMany(OrderStatusModel::class,"order_id","order_id");
    }

    public function customer(){
        return $this->belongsTo(CustomerModel::class,"company_id","company_id");
    }

    public function bankInfo(){
        return $this->belongsTo(OrderBankInfoModel::class,"order_id","order_id");
    }



    public function getOrderStatusAttribute(){

        $otherstatus = OrderStatusModel::where("order_id",$this->order_id)->first();
        if(!$otherstatus && $this->status != 1) return [1];


        $status = [];
        if($this->status == 0){
            $status[] =  1;//审核中
        }elseif($this->status == 1){
            if(!$otherstatus ||  ($otherstatus && in_array($otherstatus->hk_delivery_status,[0,1]))){
                $status[] =  2; //审核通过
            }
        }



        if(($otherstatus->hk_delivery_status == 1  &&  $otherstatus->baoguan_status == 0) || ($otherstatus->hk_delivery_status == 2 && $otherstatus->baoguan_status != 2 )){
            $status[] =  3; //香港已到货
        }

        if(($otherstatus->baoguan_status == 1 &&  $otherstatus->sz_delivery_status == 0)|| ($otherstatus->baoguan_status == 2 && $otherstatus->sz_delivery_status != 2 )){
            $status[] =  4; //清关完成
        }

        if(($otherstatus->sz_delivery_status == 1 &&  $otherstatus->sz_send_status == 0)|| ($otherstatus->sz_delivery_status == 2 && $otherstatus->sz_send_status != 2 )){
            $status[] =  5; //代配送
        }

        if(($otherstatus->sz_send_status == 1 ) || ($otherstatus->sz_send_status == 2 && $this->status != 8)){
            $status[] =  6; //已配送
        }

        if($this->status == 8){
            $status[] =  7; //已收货
        }

        return $status;
    }


    public function getOrderInfoByErpOrderSn($erpOrderSn)
    {
        return self::where('erp_order_sn',$erpOrderSn)->orderBy('order_id','desc')->where('status','>=',1)->where('id_edit_order',0)->first();
    }

    static public function getOrderDataByErpOrderSn($erpOrderSn)
    {
        return self::where('erp_order_sn','like',$erpOrderSn.'%')->where('status','>=',1)->where('id_edit_order',0)->get()->toArray();
    }

    public static function getErpOrderSnByLogisticsNo($logistics_no)
    {
        return self::where('logistics_no', 'like', '%' . $logistics_no . '%')->select('erp_order_sn', 'carrier')->get()->toArray();
    }

}