<?php
namespace Coupon\Model;

use Think\Model;

class CmsModel extends Model
{
    protected $autoCheckFields = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.CMS');
    }

    /**
     * 获取分类信息
     * @param  [type] $tags [description]
     * @return [type]       [description]
     */
    public function getBaseCat($tags)
    {
        $data = $this->table('lie_base_cat')->getFieldByTags($tags, 'bcat_id');
        return $data;
    }

    public function getBaseList($tags, $page = '', $order = 'sort DESC', $where='')
    {
        $bcat_id = $this->getBaseCat($tags);
        $map = array(
            'bcat_id' => $bcat_id,
            'status' => 1,
        );
        $map = $this->formatwhere($map, $where);
        limit_page($this, $page);
        $datas = $this->table('lie_base')->where($map)->order($order)->field('base_id, tem_id, title, class, images')->select();
        return $datas;
    }

    private function formatwhere($map, $where)
    {
        if (is_string($where)) {
            if (!empty($where)) {
                $where = array('_string' => $where);
            } else {
                $where = array();
            }
        }
        $map = array_merge($map, $where);
        return $map;
    }

}