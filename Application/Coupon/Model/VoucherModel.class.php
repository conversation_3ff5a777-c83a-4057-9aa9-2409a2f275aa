<?php

namespace Coupon\Model;

use Think\Model;

class VoucherModel extends Model
{
    public function getVoucherListByUserId($userId, $page, $limit)
    {
        $where = [
            'user_id' => $userId,
            'user_deleted' => 0
        ];
        $query = $this->table('lie_voucher');
        $count = $query->where($where)->count();
        $offset = intval($page - 1) * $limit;
        $vouchers = $query->field('id,batch_id,card_number,card_password,period_validity,allocate_time')
            ->where($where)
            ->order('id desc')
            ->limit($offset, $limit)
            ->select();
        foreach ($vouchers as $key => &$voucher) {
//            $voucher['add_time'] = date('Y-m-d H:i:s', $voucher['add_time']);
            $realCardPassword = $this->AESDecrypt($voucher['card_password']);
            $voucher['card_number'] = $this->replaceHideTag($voucher['card_number']);
            $voucher['card_password'] = $this->replaceHideTag($realCardPassword);
            $batch = $this->getVoucherBatch($voucher['batch_id']);
            $voucher['name'] = $batch['name'];
            $voucher['denomination'] = $batch['denomination'];
            $voucher['allocate_time_format'] = $batch['allocate_time'] ? date('Y-m-d H:i:s', $batch['allocate_time']) : '';
            $voucher['image'] = $this->getVoucherImage($voucher['denomination']);
        }

        $data['count'] = (int)$count;
        $data['limit'] = (int)$limit;
        $data['p'] = (int)$page;
//        $data['amount'] = [
//            'all' => (int)$count,
//        ];
        $data['total_page'] = ceil($count / $limit);
        $data['list'] = $vouchers ?: [];
        return $data;
    }

    //获取批次名称
    private function getVoucherBatch($batchId)
    {
        $batch = D('VoucherBatch')->where(['id' => $batchId])->find();
        return $batch;
    }

    private function getVoucherImage($denomination)
    {
        $denominationMap = [
            50 => 'https://img.ichunt.com/images/ichunt/202109/22/8bdd23752dea6c0aacea55ed052c181f.png',
            100 => 'https://img.ichunt.com/images/ichunt/202109/22/b4fe3381a1d6995f8c3ea5860c0296db.png',
            150 => 'https://img.ichunt.com/images/ichunt/202109/22/a0ea19d6cb4d05be67658e4e3c8ab0b8.png',
            200 => 'https://img.ichunt.com/images/ichunt/202109/22/083d1477efbe95e31e201c3315b0085b.png',
        ];

        return isset($denominationMap[(int)$denomination]) ? $denominationMap[(int)$denomination] : '';
    }

    //AES的解密
    private function AESDecrypt($cardPassword)
    {
        $key = C('VOUCHER_AES_KEY');
        return openssl_decrypt($cardPassword, 'AES-128-ECB', $key, 0);
    }

    //替换隐藏标识*号,根据不同长度进行动态变换
    private function replaceHideTag($string)
    {
        $length = mb_strlen($string);
        //获取加星号的长度
        $replaceLength = floatval($length / 2);
        $start = ($length - $replaceLength) / 2;
        $replaceString = str_repeat('*', $replaceLength);
        return substr_replace($string, $replaceString, $start, $replaceLength);
    }

    public function viewVoucherWithOutEncryption($id)
    {
        $voucher = $this->field('id,batch_id,denomination,card_number,card_password,period_validity,allocate_time')
            ->where(['id' => $id])->find();
        $batch = $this->getVoucherBatch($voucher['batch_id']);
        $voucher['name'] = $batch['name'];
        $voucher['card_password'] = $this->AESDecrypt($voucher['card_password']);
        //还要把状态写回去
        $userId = cookie('uid');
        $data['viewed_user_id'] = $userId;
        $data['viewed_time'] = time();
        $this->where(['id' => $id])->save($data);
        return $voucher;
    }

    //检查券是否是自己的
    public function checkVoucherOwner($id, $userId)
    {
        $voucherUserId = $this->where(['id' => $id])->getField('user_id');
        return $voucherUserId == $userId;
    }

    //删除用户对应的券,只是隐藏
    public function deleteVoucher($id)
    {
        return $this->where(['id' => $id])->save([
            'user_deleted' => 1,
            'user_deleted_time' => time(),
        ]);
    }
}