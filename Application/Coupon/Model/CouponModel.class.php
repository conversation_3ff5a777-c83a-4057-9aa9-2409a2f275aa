<?php
/**
 * 优惠券
 * @authors yc ()
 * @date    2017-12-08 11:09:37
 * @version $Id$
 */

namespace Coupon\Model;

use Think\Model;

class CouponModel extends Model
{

    public function getInfoById($id)
    {
        $map = array(
            'coupon_id' => $id,
        );
        $data = $this->where($map)->find();
        return $data;
    }

    public function getInfoBySn($sn)
    {
        $map = array(
            'coupon_sn' => $sn,
        );
        $data = $this->where($map)->find();
        return $data;
    }

    /**
     * 减数量
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    public function decNum($id)
    {
        $map = array(
            'coupon_id' => $id,
            'surplus_num' => array('gt', 0),
        );
        $res = $this->where($map)->setDec('surplus_num', 1);
        if ($res === false || $res == 0) {
            return false;
        } else {
            return $res;
        }
    }

    //获取用户可领取的优惠券列表
    public function getActivityCenterCouponList()
    {
        $map = [
            'is_activity_center_show' => 1,
            'issue_type' => 1,
            'status' => 1,
            //'coupon_get_rule' => 2,
            'end_time' => [
                ['gt', time()]
            ],
        ];

        $map['org_id'] = I('org_id', 1);

        $couponList = $this->where($map)->select();
        $couponList = array_map(function ($coupon) {
            $coupon['start_time_timestamp'] = (int)$coupon['start_time'];
            $coupon['end_time_timestamp'] = (int)$coupon['end_time'];
            $coupon['create_time'] = date('Y-m-d H:i:s', $coupon['create_time']);
            $coupon['end_time'] = date('Y-m-d H:i:s', $coupon['end_time']);
            $coupon['start_time'] = date('Y-m-d H:i:s', $coupon['start_time']);
            $require_index = 1 == $coupon['coupon_type'] ? 0 : 1;
            $descMap = [
                '满%s元减',
                '满%s元享',
            ];
            $coupon['require_desc'] = sprintf($descMap[$require_index], $coupon['require_amount']) . $coupon['sale_amount'];
            return $coupon;
        }, $couponList);
        return $couponList;
    }
}
