<?php
/**
 * 用户优惠券
 * @authors yc ()
 * @date    2017-12-08 11:09:37
 * @version $Id$
 */

namespace Coupon\Model;

use Think\Model;

class UserCouponModel extends Model
{

    public function getListByUser($user_id, $page = '', $order = 'user_coupon_id DESC')
    {
        if (is_array($user_id)) {
            $map = $user_id;
        } else {
            $map = array(
                'user_id' => $user_id,
            );
        }
        limit_page($this, $page);
        $datas = $this->where($map)->order($order)
            ->field('user_coupon_id, coupon_id, coupon_type, order_id, order_sn, create_time, end_time, status')
            ->select();
        return $datas;
    }

    public function getCountByUser($map)
    {
        if (is_array($map)) {
            $map = $map;
        } else {
            $userId = $map;
            $map = array(
                'user_id' => $userId,
            );
        }
        $datas = $this->where($map)->count();
        return $datas;
    }

    //目前获取条件带有coupon_id,不用兼容其它系统
    public function getGetTimeByUser($user_id)
    {
        if (is_array($user_id)) {
            $map = $user_id;
        } else {
            $map = array(
                'user_id' => $user_id,
            );
        }
        $datas = $this->where($map)->field('create_time ,end_time')->find();
        return $datas;
    }

    public function getList($where = '', $page = '', $order = 'UC.user_coupon_id DESC')
    {
        limit_page($this, $page);
        $datas = $this->alias('UC')->join($this->tablePrefix . 'coupon C ON C.coupon_id = UC.coupon_id')
            ->where($where)->order($order)
            ->field('UC.user_coupon_id, UC.coupon_id,UC.order_sn, UC.create_time, UC.end_time, UC.use_time, UC.status, C.coupon_sn, C.coupon_name, C.require_amount, C.sale_amount, C.total_receive_num, C.day_receive_num, C.coupon_type, C.time_type, C.start_time, C.coupon_mall_type, C.coupon_goods_range, C.selected_supplier_id ,C.selected_supplier, C.selected_brand, C.coupon_get_rule, C.reg_start_time, C.max_preferential_amount,C.exclude_brand_ids,C.supplier_ids,C.canals,(select group_concat(lie_coupon_brand.brand_id)  from lie_coupon_brand where lie_coupon_brand.coupon_id=C.coupon_id) as selected_brand_id,
                          (select group_concat(lie_coupon_class.class_id) from lie_coupon_class where lie_coupon_class.coupon_id=UC.coupon_id GROUP BY lie_coupon_class.coupon_id ) as coupon_class_id
                        ')
            ->group('UC.user_coupon_id')
            ->select();
        return $datas;
    }

    public function getCount($where = '')
    {
        $datas = $this->alias('UC')->join($this->tablePrefix . 'coupon C ON C.coupon_id = UC.coupon_id')
            ->where($where)
            ->count();
        return $datas;
    }

    public function getInfo($user_coupon_id, $user_id = 0)
    {
        $map = array(
            'UC.user_coupon_id' => $user_coupon_id
        );
        !empty($user_id) && $map['UC.user_id'] = $user_id;
        $datas = $this->alias('UC')->join($this->tablePrefix . 'coupon C ON C.coupon_id = UC.coupon_id')
            ->where($map)
            ->field('UC.user_coupon_id, UC.order_sn, UC.create_time, UC.end_time, UC.use_time, UC.coupon_type, UC.status, C.coupon_sn, C.coupon_name, C.require_amount, C.sale_amount, C.total_receive_num, C.day_receive_num, C.coupon_mall_type, C.coupon_goods_range, C.selected_supplier_id ,C.selected_supplier, C.selected_brand, C.coupon_get_rule, C.reg_start_time, C.max_preferential_amount,C.exclude_brand_ids,C.supplier_ids,C.canals,(select group_concat(lie_coupon_brand.brand_id)  from lie_coupon_brand where lie_coupon_brand.coupon_id=C.coupon_id) as selected_brand_id')
            ->find();
        return $datas;
    }

    /**
     * 获取订单使用的优惠券
     * @param  [type]  $order_id [description]
     * @param integer $user_id [description]
     * @return [type]            [description]
     */
    public function getInfoByOrder($order_id, $user_id = 0)
    {
        $map = array(
            'UC.order_id' => $order_id,
            'UC.status' => 1,
        );
        !empty($user_id) && $map['UC.user_id'] = $user_id;
        $datas = $this->alias('UC')->join($this->tablePrefix . 'coupon C ON C.coupon_id = UC.coupon_id')
            ->where($map)
            ->field('UC.user_coupon_id, UC.order_sn, UC.create_time, UC.end_time, UC.use_time, UC.coupon_type, UC.status, C.coupon_sn, C.coupon_name, C.require_amount, C.sale_amount, C.total_receive_num, C.day_receive_num, C.coupon_mall_type, C.coupon_goods_range, C.selected_supplier_id ,C.selected_supplier, C.selected_brand, C.coupon_get_rule, C.reg_start_time, C.max_preferential_amount, C.coupon_type, C.issue_type, C.coupon_id,C.exclude_brand_ids,C.supplier_ids,C.canals,(select group_concat(lie_coupon_brand.brand_id)  from lie_coupon_brand where lie_coupon_brand.coupon_id=C.coupon_id) as selected_brand_id')
            ->find();
        return $datas;
    }

    /**
     * 返还订单使用的优惠券
     * @return [type] [description]
     */
    public function returnOrder($order_id, $user_id = 0)
    {
        $map = array(
            'order_id' => $order_id,
            'user_id' => $user_id,
        );
        $save = array(
            'status' => -1,
            'order_id' => 0,
            'order_sn' => '',
            'use_time' => 0,
        );

        // 清空明细表优惠金额
        $data['preferential_price'] = 0;
        D('Order/OrderItems')->where($map)->save($data);
        $res = $this->where($map)->save($save);
        return $res;
    }

    /**
     * 设置删除状态
     * @param [type] $user_coupon_id [description]
     */
    public function setDisable($user_coupon_id)
    {
        $map = array(
            'user_coupon_id' => $user_coupon_id,
        );
        $save = array(
            'status' => -3,
        );
        $res = $this->where($map)->save($save);
        return $res;

    }

    /*
        查询哪个order_sn没有使用过
        @param $arr array order_sn数组
    */
    public function get_order_sn($arr)
    {

        foreach ($arr as $key => $value) {
            $res = $this->where(array('source_order_id' => $value))->select();

            if (!$res) {
                return $value;
            }
        }

        return false;
    }


}
