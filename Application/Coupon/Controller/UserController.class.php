<?php

namespace Coupon\Controller;

use Common\Model\RedisModel;
use Coupon\Controller\BaseController;
use Dompdf\FrameReflower\Page;

class UserController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME), [
                'returncoupon',
                'bestusebyordersystem',
                'usecouponbyorder',
                'infobyorder',
                'getusercouponinfo',
                'lists'
            ]) && CONTROLLER_NAME == 'User') {//其他控制器调用不受影响
            // 检查登录
            if (in_array(strtolower(ACTION_NAME), ['count', 'lists'])) {//弱校验
                $verify_mode = false;
            } else {//强校验
                $verify_mode = true;
            }
            $res = $this->checkLogin($verify_mode);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
        // $this->overtime();
    }

    /**
     * 获取统计数量
     * @param integer $status [description]
     * @param boolean $cache [description]
     * @return [type]          [description]
     */
    public function count($status = 0, $cache = true, $user_id = 0)
    {
        //兼容其它系统
        $org_id = I('org_id', 1);
        $user_id = I('user_id', $user_id);
        empty($user_id) && $user_id = cookie('uid');
        if (!empty($status)) {
            $UserCouponModel = D('UserCoupon');
            $map = array(
                'user_id' => $user_id,
                'status' => $status,
            );
            if ($status == -2) {
                $map['end_time'] = array('lt', $_SERVER['REQUEST_TIME']);
                unset($map['status']);
            }
            if ($status == -1) {
                $map['end_time'] = array('gt', time());
            }
            $map['org_id'] = $org_id;
            $count = $UserCouponModel->getCountByUser($map);
            $info[$status] = $count;
            $count = $info[$status];
            return $count;
        } else {
            $status = C('USER_COUPON_STATUS');
            foreach ($status as $k => $v) {
                $res = $this->count($k, $cache, $user_id);
                $count[$k] = $res;
            }
            //总数=未使用+已过期+已使用
            $count['0'] = (string)($count['-1'] + $count['-2'] + $count['1']);
        }
        return $this->apiReturn(0, '成功', $count);
    }

    /**
     * 获取券跳转URL
     */
    private function getRedirectUrl($coupon_goods_range, $selected_supplier, $selected_brand)
    {
        $pf = platform();
        if (1 == $pf) {
            $url1 = 'https://www.ichunt.com';
        } else {
            $url1 = 'https://m.ichunt.com/v3';
        }


//        $search_key = !empty($selected_supplier) ? $selected_supplier : $selected_brand;
//        $url2 = 'http://www.ichunt.com/v3/s/?k='.$search_key.'&kNums=&adtag=liexin';
//        return ($coupon_goods_range==1) ? $url1 : $url2;
        return $url1;
    }

    /**
     * 获取列表
     * @return [type] [description]
     */
    public function lists()
    {
        $org_id = I('org_id', 1);
        $pf = platform();
        $sort = I('sort', -1, 'intval');
        $sort = $sort == -1 ? 'ASC' : 'DESC';
        $filter = array('UC.status');
        $map = map_filter($filter);
        $user_id = I('uid') ?: cookie('uid');
        $map['UC.user_id'] = $user_id;
        $map['UC.org_id'] = $org_id;
//        if ($pf == 1) {
//            $map['_string'] = 'UC.status IN (-1, -2)';
//        }
        if ($map['UC.status'] == 1) {//已使用的根据使用时间排序
            $order = 'UC.use_time DESC';
        } else {
            //$map['UC.end_time'] = array('gt', time());
        }
        switch ($map['UC.status']) {
            case 1 :
                break;
            case -1:
                $map['UC.end_time'] = array('gt', time());
                break;
            case -2:
                $map['UC.end_time'] = array('lt', $_SERVER['REQUEST_TIME'] );
                unset($map['UC.status']);
                break;

        }
        $UserCouponModel = D('UserCoupon');
        $order = 'UC.status DESC, UC.end_time, C.coupon_mall_type ' . $sort;
        $datas = $UserCouponModel->getList($map, null, $order);

        foreach ($datas as &$v) {
            $v['sale_amount'] = floatval($v['sale_amount']);
            $require_index = $v['require_amount'] > 0 ? 1 : 0;
            $v['require_desc'] = sprintf(C('USER_COUPON_REQUIRE.' . $require_index), $v['require_amount']);
            $v['coupon_type_val'] = C('COUPON_TYPE.' . $v['coupon_type']);
            $v['soon_disable'] = $v['end_time'] - $_SERVER['REQUEST_TIME'] < 3 * 86400 ? 1 : 0;
            $v['create_time'] = date('Y.m.d', $v['create_time']);
            $v['start_time_timestamp'] = (int)$v['start_time'];
            $v['start_time'] = $v['time_type'] == 2 ? $v['create_time'] : date('Y.m.d',
                $v['start_time']);//相对时间 有效开始时间为领取时间
            if ($v['end_time'] < time()) {
                $v['status'] = -2;
            }
            $v['end_time_timestamp'] = (int)$v['end_time'];
            $v['end_time'] = date('Y.m.d', $v['end_time']);
            $v['use_time'] = $v['use_time'] > 0 ? date('Y.m.d', $v['use_time']) : '';
            $v['redirect_url'] = $this->getRedirectUrl($v['coupon_goods_range'], $v['selected_supplier'],
                $v['selected_brand']);
            $v['selected_brand_name'] = implode(',',
                $this->get_brand_name($v['selected_brand_id'], $v['coupon_mall_type']));

            if (!empty($v['coupon_class_id'])) {

                //把分类拆出来
                $coupon_class = explode(',', $v['coupon_class_id']);

                //拿着分类去redis查分类名字
                foreach ($coupon_class as $coupon_class_key => $coupon_class_value) {
                    $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.spu"));
                    $class_name = json_decode($redis->hget('Self_SelfClassInfo', $coupon_class_value));
                    $class_name = !empty($class_name->class_name) ? $class_name->class_name : '未定义优惠券';
                    $v['coupon_class_name'][] = array('class_name' => $class_name, 'class_id' => $coupon_class_value);
                }
            }
        }
        //其它组织直接返回统计数量
        if ($org_id != 1) {
            $result['coupon_list'] = $datas;
            $countData = $this->count(0, false, $user_id);
            if (isset($countData['err_code']) && $countData['err_code'] == 0) {
                $result['count'] = $countData['data'];
            }
            return $this->apiReturn(0, '成功', $result);
        } else {
            return $this->apiReturn(0, '成功', $datas);
        }

    }

    /*
        @param string 品牌id字符串，逗号分割
        @param int   2.自营 3.联营
    */
    private function get_brand_name($selected_brand_id, $coupon_mall_type)
    {

        if (empty($selected_brand_id)) {
            return;
        }

        $map['brand_id'] = array('in', $selected_brand_id);

        if ($coupon_mall_type == 2) {

            $lie_data = D('Common/ZyGoods');

            $result = $lie_data->table('lie_brand')->where($map)->getField('brand_name', true); // 从x开始1000条


        } else {
            if ($coupon_mall_type == 3) {

                $lie_data = D('Common/spu');

                $result = $lie_data->table('lie_brand')->where($map)->getField('brand_name', true); // 从x开始1000条

            }
        }

        return $result;

    }

    /**
     * 最佳使用券
     * param:
     * limit 取几条
     * isBest 是否取最优
     *  1、用户的券
     * 2、有效的券
     * 3、符合金额要求下限
     * 4、优惠金额最高
     * 5、过期时间快到
     * 6、金额要求下限高的优先
     * 7、如果有，则符合指定供应商指定品牌的券
     * @return [type] [description]
     */
    public function bestUse()
    {
        $cart_ids = I('cart_ids', '');
        $res = $this->showUse(0, 1, $cart_ids);
        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        $list = $res['data'];
        $data = array(
            'user_coupon_id' => $list[0]['user_coupon_id'],
            'coupon_type' => $list[0]['coupon_type'],
            'sale_amount' => $list[0]['sale_amount'],
            'preferential' => $list[0]['preferential'],
        );
        return $this->apiReturn(0, '最佳使用券', $data);
    }

    // 订单后台 --- 获取优惠券
    public function bestUseByOrderSystem()
    {
        $cart_ids = I('cart_ids', '');
        $res = $this->showUse(0, 1, $cart_ids);
        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        $list = $res['data'];
        $data = array(
            'user_coupon_id' => $list[0]['user_coupon_id'],
            'coupon_name' => $list[0]['coupon_name'],
            'coupon_type' => $list[0]['coupon_type'],
            'sale_amount' => $list[0]['sale_amount'],
            'preferential' => $list[0]['preferential'],
        );
        return $this->apiReturn(0, '已推荐一条最佳使用券', $data);
    }

    /**
     * 展示可用优惠券
     * @return [type] [description]
     */
    public function showUse($limit = 0, $isBest = 0, $cart_ids = '', $coupon_mall_type = 3)
    {
        //由于自营和联营商城订单合并,所以上面的$coupon_mall_type参数就没用用处了
        empty($cart_ids) && $cart_ids = I('cart_ids', '');
        $page = null;
        if (!empty($limit)) {
            $page = '1,' . intval($limit);
        }
        $user_id = I('uid') ? I('uid') : cookie('uid');

        $UserCouponModel = D('UserCoupon');
        //todo:因为要获取最优券，但折扣券不能用总金额来算，必须用每个可用范围订单的总金额*折扣=优惠金额，所以只能在每个符合条件的
        //todo:小订单总金额中去计算，而且也不能像抵扣券一样排序。eg:小订单1总额1元1折,小订单2总额100元9折-->此时9折比1折更优惠
        //UC是user_coupon表,U是coupon表
        $map = array(
            'UC.user_id' => $user_id,
            'UC.status' => -1,
            'UC.org_id' => I('org_id', 1),
            'C.start_time' => array('elt', $_SERVER['REQUEST_TIME']),//券开始使用时间，并非领取时间
            'UC.end_time' => array('egt', $_SERVER['REQUEST_TIME']),//券失效时间
        );
        //下面的这行是取出该用户的所有优惠券,排序为:优惠金额最大,然后用户对于券失效时间最近
        $couponList = $UserCouponModel->getList($map, $page, 'C.sale_amount DESC, UC.end_time ASC');
        //加入该优惠券适用分类的名称
        foreach ($couponList as $x => $y) {
            if (!empty($y['coupon_class_id'])) {
                //把分类拆出来
                $coupon_class = explode(',', $y['coupon_class_id']);
                // //拿着分类去redis查分类名字
                foreach ($coupon_class as $j => $z) {
                    $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.spu"));
                    $class_name = json_decode($redis->hget('Self_SelfClassInfo', $z));
                    $class_name = !empty($class_name->class_name) ? $class_name->class_name : '未定义优惠券';
                    $couponList[$x]['coupon_class_name'][] = array('class_name' => $class_name, 'class_id' => $z);
                }

            }
        }
        if (empty($couponList) || strpos($_SERVER['HTTP_REFERER'], 'tuangou=1') != false) {
            return $this->apiReturn(25010, '没有可用的优惠券');
        }

        //获取到所有商品以后,要去区分联营和自营商品
        $confirmInfo = $this->getConfirmGoodsData($cart_ids, $user_id);
        //组装优惠券相关信息
        foreach ($couponList as $k => &$v) {
            $v['sale_amount'] = floatval($v['sale_amount']);
            $require_index = $v['require_amount'] > 0 ? 1 : 0;
            //获取优惠券描述
            $v['require_desc'] = sprintf(C('USER_COUPON_REQUIRE.' . $require_index), $v['require_amount']);
            //1优惠券 2折扣券
            $v['coupon_type_val'] = C('COUPON_TYPE.' . $v['coupon_type']);
            //是否快过期
            $v['soon_disable'] = $v['end_time'] - $_SERVER['REQUEST_TIME'] < 3 * 86400 ? 1 : 0;
            $v['create_time'] = date('Y.m.d', $v['create_time']);
            $v['end_time'] = date('Y.m.d', $v['end_time']);
            $v['use_time'] = $v['use_time'] > 0 ? date('Y.m.d', $v['use_time']) : '';
            $v['redirect_url'] = $this->getRedirectUrl($v['coupon_goods_range'], $v['selected_supplier'],
                $v['selected_brand']);
            //需要凑单或订单中无相关商品时的提示
            //这个方法也包含了对不同商城类型的价格的优惠券判断
            $remark = $this->getCouponRemark($v, $confirmInfo['data']);
            $v['remark'] = $remark['remark'];
            if (!empty($v['coupon_class_name'])) {
                //循环这个优惠券支持的分类,如果优惠券支持购物车里面的商品的分类id,则展示 (一个优惠券可能支持多个分类)
                foreach ($v['coupon_class_name'] as $x => $y) {
                    //循环购物车的商品，只要有一个商品是适用的分类，就允许使用
                    foreach ($confirmInfo['data']['list'] as $class_key => $class_value) {
                        if ($class_value['class_id'] == $y['class_id']) {
                            $v['remark'] = 0;
                            $remark['remark'] = 0;
                            break;
                        }
                    }
                    //已经找到，不再继续循环
                    if (!empty($v['remark']) && $v['remark'] == 0) {
                        break;
                    }
                }
                if (!empty($v['remark']) && $v['remark'] == 0) {
                    //最后没有找到适合这个分类的商品，就隐藏
                    unset($couponList[$k]);
                }
            }

            $v['preferential'] = $remark['preferential'];
            //获取推荐优惠券时去除需要其他条件才可用的
            if (1 == $isBest && 0 != $remark['remark']) {
                unset($couponList[$k]);
            }
        }
        unset($v);

        //获取最优优惠券
//        $couponModel = new CouponModel();
//        if ($isBest == 1) {
//            $bestCoupon = $couponModel->getBestCoupon($confirmInfo, $couponList);
//            $couponList = [$bestCoupon];
//        }
        //重排一下
        if (1 == $isBest) {
            //找出优惠力度最大的 排到最前面
            $flag = 0;
            $preferential = 0;
            foreach ($couponList as $key => $val) {
                if ($val['preferential'] > $preferential) {
                    $flag = $key;
                    $preferential = $val['preferential'];
                }
            }
            $couponList = array($couponList[$flag]);
        }

        return $this->apiReturn(0, '可用优惠券', $couponList);
    }

    //获取确认的商品数据
    public function getConfirmGoodsData($cart_ids, $user_id)
    {
        //获取结算商品信息
        $confirmInfo = $this->getConfirmGoods($cart_ids, $user_id);
        foreach ($confirmInfo['data']['list'] as $key => $value) {
            if (!empty($value['goods_id'])) {
                $goods_class_id[] = $value['goods_id'];
            }
        }
        $goodsUrl = GOODS_DOMAIN . '/synchronization';
        //获取商品分类id
        $goods_class_info = json_decode(post_curl($goodsUrl, array('goods_id' => $goods_class_id)), true);
        //插入分类id
        if ($goods_class_info['data']) {
            foreach ($goods_class_info['data'] as $key => $value) {
                foreach ($confirmInfo['data']['list'] as $cart_class_key => $cart_class_value) {
                    if ($value['goods_id'] == $cart_class_value['goods_id']) {
                        $confirmInfo['data']['list'][$cart_class_key]['class_id'] = $value['class_id2'];
                    }
                }
            }

        }
        if ($confirmInfo['data']['allow_coupon'] == 2) {//无法使用
            return $this->apiReturn(25030, '存在无法使用优惠券的商品，没有可用的优惠券');
        }

        return $confirmInfo;
    }

    /**
     * 可用优惠券 区分 可用/需凑单+无可用订单  H5专用
     * @param cart_ids
     * @return 1 可用   2 需要条件可用(凑单、添加相关订单)
     */
    public function usableCount()
    {
        $cart_ids = I('cart_ids', '');
        $enableCount = 0;
        $disableCount = 0;
        $page = null;
        $user_id = cookie('uid');
        $UserCouponModel = D('UserCoupon');
        $map = array(
            'UC.user_id' => $user_id,
            'UC.status' => -1,
            // 'C.require_amount' => array('elt', $amount),
            'C.start_time' => array('elt', $_SERVER['REQUEST_TIME']),//券开始使用时间，并非领取时间
            'UC.end_time' => array('egt', $_SERVER['REQUEST_TIME']),//券失效时间
        );
        $list = $UserCouponModel->getList($map, $page, 'C.sale_amount DESC, UC.end_time ASC');//, C.require_amount DESC
        if (empty($list)) {
            return $this->apiReturn(25010, '没有可用的优惠券');
        }
        $confirmInfo = $this->getConfirmGoods($cart_ids, $user_id);
        foreach ($list as $k => &$v) {
            $v['sale_amount'] = floatval($v['sale_amount']);
//            $require_index = $v['require_amount'] > 0 ? 1 : 0;
//            $v['require_desc'] = sprintf(C('USER_COUPON_REQUIRE.' . $require_index), $v['require_amount']);
//            $v['coupon_type_val'] = C('COUPON_TYPE.'. $v['coupon_type']);
//            $v['soon_disable'] = $v['end_time'] - $_SERVER['REQUEST_TIME'] < 3 * 86400 ? 1 : 0;
//            $v['create_time'] = date('Y.m.d', $v['create_time']);
//            $v['end_time'] = date('Y.m.d', $v['end_time']);
//            $v['use_time'] = $v['use_time'] > 0 ? date('Y.m.d', $v['use_time']) : '';
            //需要凑单或订单中无相关商品时的提示
            $remark = $this->getCouponRemark($v, $confirmInfo['data']);
            $v['remark'] = $remark['remark'];
            if (0 != $remark['remark']) {
                ++$disableCount;
                unset($list[$k]);
            } else {
                ++$enableCount;
            }
        }
        unset($v);
        $count['1'] = $enableCount;
        $count['2'] = $disableCount;
        return $this->apiReturn(0, '可用优惠券分类数量', $count);
    }

    /**
     * 生成优惠券备注信息
     * @return [string] 还差多少金额才能使用该张优惠券 ---> 0不差  -1没有能用这张优惠券的订单
     */
    private function getCouponRemark($coupon, $confirmInfo)
    {
        //需要凑单的金额：
        //0:   不需要凑单
        //正数: 需要凑单则为正数
        //-1:  没有能用这张优惠券的订单为
        $remark = array();
        $remark['remark'] = 0;
        $remark['preferential'] = 0;
        $lyGoodsTypes = [1, 2, 6];
        $zyGoodsTypes = [3, 4];
        //把品牌字符串组装成数组
        $coupon['selected_brand_id'] = $coupon['selected_brand_id'] ? explode(',', $coupon['selected_brand_id']) : [];
        $redis = fs_redis_init();
        //补充sku数据
        foreach ($confirmInfo['list'] as &$goods) {
            $sku = $redis->hget('sku', $goods['goods_id']);
            $sku = json_decode($sku, true);
            $goods['canal'] = $sku['canal'];
        }
        unset($goods);

        //各类情况的金额总和
        $amount = $zyAmount = $lyAmount = 0;
        switch ($coupon['coupon_mall_type']) {
            //注意:goods_type = 1,2 为联营商品,goods_type = 3,4 为自营商品
            case 1://全站(商城级别)
                $amount = $confirmInfo['goods_total'];
                break;
            case 2://自营
                //适用于全站范围
                if ($coupon['coupon_goods_range'] == 1) {
                    //因为现在商品自营和联营合并了,所以商品有可能有自营也有可能有联营
                    //所以在算金额的时候还要去判断商品类型
                    foreach ($confirmInfo['list'] as $goods) {
                        //只取自营类型的商品金额
                        if (in_array($goods['goods_type'], $zyGoodsTypes)) {
                            $amount += $goods['goods_amount'];
                        }
                    }
//                    $amount = $confirmInfo['goods_total'];
                    //适用于品牌范围
                } elseif ($coupon['coupon_goods_range'] == 3) {
                    $supplierIds = explode(',', $coupon['supplier_ids']);
                    $canals = explode(',', $coupon['canals']);
                    $excludeBrandIds = explode(',', $coupon['exclude_brand_ids']);
                    if (!empty($coupon['selected_brand_id'])) {
                        foreach ($confirmInfo['list'] as $goods) {
                            //在排除的品牌里面,跳过
                            if (!empty($excludeBrandIds)) {
                                if (in_array($goods['brand_id'], $excludeBrandIds)) {
                                    continue;
                                }
                            }

                            //在设置的品牌里面,并且是自营商品,才算价格
                            if (in_array($goods['brand_id'], $coupon['selected_brand_id']) && in_array($goods['goods_type'], $zyGoodsTypes)) {
                                $amount += $goods['goods_amount'];
                            }
                        }
                    } else {
                        $remark['remark'] = -1;
                    }
                } elseif ($coupon['coupon_goods_range'] == 6) {
                    $redisKey = 'lie_coupon_sku_' . $coupon['coupon_id'];
                    //这个是根据商品名称来确定是否能使用
                    $exist = $redis->exists($redisKey);
                    if ($exist) {
                        //判断对应的型号是否存在
                        foreach ($confirmInfo['list'] as $goods) {
                            $goodsNameExists = $redis->hget($redisKey, $goods['goods_name']);
                            //在包含的商品名称里面,并且是自营的商品,才匹配到
                            if ($goodsNameExists && in_array($goods['goods_type'], $zyGoodsTypes)) {
                                $amount += $goods['goods_amount'];
                            }
                        }
                    } else {
                        $remark['remark'] = -1;
                    }
                }
                break;
            case 3://联营
                if ($coupon['coupon_goods_range'] == 1) {
                    //全站(供应商/品牌级别) 满足总额大于等于最低要求即可使用
                    foreach ($confirmInfo['list'] as $goods) {
                        //只取自营类型的商品金额
                        if (in_array($goods['goods_type'], $lyGoodsTypes)) {
                            $amount += $goods['goods_amount'];
                        }
                    }
                } elseif ($coupon['coupon_goods_range'] == 2) {
                    //指定供应商的优惠券
                    if (!empty($coupon['selected_supplier_id']) && empty($coupon['selected_brand_id'])) {
                        foreach ($confirmInfo['list'] as $goods) {
                            if ($goods['supplier_id'] == $coupon['selected_supplier_id'] && in_array($goods['goods_type'], $lyGoodsTypes)) {
                                $amount += $goods['goods_amount'];
                            }
                        }
                    } elseif (!empty($coupon['selected_supplier_id']) && !empty($coupon['selected_brand_id'])) {
                        //指定供应商+品牌的优惠券
                        foreach ($confirmInfo['list'] as $goods) {
                            if ($goods['supplier_id'] == $coupon['selected_supplier_id']) {
                                foreach ($coupon['selected_brand_id'] as $y => $z) {
                                    if ($goods['brand_id'] == $z && in_array($goods['goods_type'], $lyGoodsTypes)) {
                                        $amount += $goods['goods_amount'];
                                    }
                                }

                            }
                        }
                    } else {
                        //参数错误 则券不可用
                        $remark['remark'] = -1;
                    }
                } elseif ($coupon['coupon_goods_range'] == 3) {
                    $supplierIds = explode(',', $coupon['supplier_ids']);
                    $canals = explode(',', $coupon['canals']);
                    $excludeBrandIds = explode(',', $coupon['exclude_brand_ids']);
                    //兼容老数据
                    if (!empty($coupon['selected_brand_id']) && empty($supplierIds)) {
                        //现在不仅要判断是否包含对应的品牌了,还要去判断是否是被排除的品牌,以及是否有对应的渠道id和供应商编码
                        foreach ($confirmInfo['list'] as $goods) {
                            foreach ($coupon['selected_brand_id'] as $y => $z) {
                                if ($goods['brand_id'] == $z && in_array($goods['goods_type'], $lyGoodsTypes)) {
                                    $amount += $goods['goods_amount'];
                                }
                            }
                        }
                    } elseif (!empty($supplierIds)) {
                        //新版本的肯定有这个渠道id,所以不为空那肯定是新版本的品牌判断
                        foreach ($confirmInfo['list'] as $goods) {
                            //不属于对应的供应商id,跳过
                            if (!in_array($goods['supplier_id'], $supplierIds)) {
                                continue;
                            }
                            //供应商编码不为空,并且不在里面,跳过
                            if (!empty($canals)) {
                                if (!in_array($goods['canal'], $canals)) {
                                    continue;
                                }
                            }
                            //在排除的品牌里面,跳过
                            if (!empty($excludeBrandIds)) {
                                if (in_array($goods['brand_id'], $excludeBrandIds)) {
                                    continue;
                                }
                            }

                            if (!empty($coupon['selected_brand_id'])) {
                                //在设置的品牌里面,并且是联营商品,才算价格
                                if (in_array($goods['brand_id'], $coupon['selected_brand_id']) && in_array($goods['goods_type'], $lyGoodsTypes)) {
                                    $amount += $goods['goods_amount'];
                                }
                            } elseif (in_array($goods['goods_type'], $lyGoodsTypes)) {
                                //没有设置品牌的话,就不去判断品牌了
                                $amount += $goods['goods_amount'];
                            }
                        }
                    } else {
                        $remark['remark'] = -1;
                    }
                } elseif ($coupon['coupon_goods_range'] == 6) {
                    $supplierIds = explode(',', trim($coupon['supplier_ids'], ','));
                    $redisKey = 'lie_coupon_sku_' . $coupon['coupon_id'];
                    //这个是根据商品名称来确定是否能使用
                    $redis = fs_redis_init();
                    $exist = $redis->exists($redisKey);
                    if ($exist) {
                        //判断对应的型号是否存在
                        foreach ($confirmInfo['list'] as $goods) {
                            //不属于对应的供应商id,跳过
                            if (!in_array($goods['supplier_id'], $supplierIds)) {
                                continue;
                            }
                            //供应商编码不为空,并且不在里面,跳过
                            $canals = $coupon['canals'];
                            if (!empty($canals)) {
                                $canals = explode(',', trim($canals, ','));
                                if (!in_array($goods['canal'], $canals)) {
                                    continue;
                                }
                            }
                            $goodsNameExists = $redis->hexists($redisKey, $goods['goods_name']);
                            if ($goodsNameExists && in_array($goods['goods_type'], $lyGoodsTypes)) {
                                $amount += $goods['goods_amount'];
                            }
                        }
                    } else {
                        $remark['remark'] = -1;
                    }
                }
                break;
            default:
                $remark['remark'] = -1;
                $remark['preferential'] = 0;
                break;
        }
        //根据总金额决定remark值 及 给出优惠金额
        if ($amount == 0) {
            $remark['remark'] = -1;
            $remark['preferential'] = 0;
        } elseif (0 != $amount && $amount < $coupon['require_amount']) {
            $remark['remark'] = $coupon['require_amount'] - $amount;
            $remark['preferential'] = 0;
        } else {
            $remark['remark'] = 0;
            //根据优惠券类型 分别 计算优惠了多少钱
            if ($coupon['coupon_type'] == 1) {
                $remark['preferential'] = $coupon['sale_amount'];
            } else {
                $remark['preferential'] = $amount - price_format($coupon['sale_amount'] * $amount, 0, 2);
                if ($coupon['max_preferential_amount'] > 0 && $remark['preferential'] > $coupon['max_preferential_amount']) {
                    $remark['preferential'] = $coupon['max_preferential_amount'];//折扣有最高减免额
                }
            }
        }
        return $remark;
    }

    /**
     * 根据商品金额计算优惠金额
     * @param $amount $coupon
     * @return $remark
     */
    public function getRemark($amount, $coupon)
    {

    }

    /**
     * 过期优惠券
     * @return [type] [description]
     */
    public function overtime()
    {
        $user_id = cookie('uid');
        $UserCouponModel = D('UserCoupon');
        $map = array(
            'user_id' => $user_id,
            'status' => -1,
            'end_time' => array('lt', $_SERVER['REQUEST_TIME']),
        );
        $res = $UserCouponModel->where($map)->setField('status', -2);
        return $this->apiReturn(0, '过期数据成功');
    }

    /**
     * 获取订单使用的优惠券
     * @return [type] [description]
     */
    public function getOrderCoupon()
    {
        $order_id = I('request.order_id');
        $user_id = cookie('uid');
        $UserCouponModel = D('UserCoupon');
        $info = $UserCouponModel->getInfoByOrder($order_id, $user_id);
        return $this->apiReturn(0, '', $info);
    }

    // 订单后台使用优惠券
    public function useCouponByOrder()
    {
        $user_coupon_id = I('request.user_coupon_id', 0, 'intval');
        $order_id = I('request.order_id', 0, 'intval');
        $order_sn = I('request.order_sn', '', 'trim');
        $user_id = I('uid');
        $UserCouponModel = D('UserCoupon');

        //非内部使用，进行校验
        if (!$this->auth()) {
            //确定订单是当前用户的订单
            $info = $this->getOrder($order_id);
            if ($info['err_code'] != 0) {
                return $this->apiReturn($info['err_code'], $info['err_msg']);
            }
            if ($info['data']['user_id'] != $user_id) {
                return $this->apiReturn(25014, '当前订单无效');
            }
            $order_sn = $info['data']['order_sn'];

            //确定优惠券是当前用户的优惠券
            $info = $UserCouponModel->find($user_coupon_id);
            if (empty($info) || $info['user_id'] != $user_id) {
                return $this->apiReturn(25013, '当前优惠券无效');
            }
        }
        $save = array(
            'user_coupon_id' => $user_coupon_id,
            'order_id' => $order_id,
            'order_sn' => $order_sn,
            'status' => 1,
            'use_time' => $_SERVER['REQUEST_TIME'],
        );
        $res = $UserCouponModel->save($save);
        if ($res === false) {
            return $this->apiReturn(25015, '系统繁忙，请稍后重试');
        }
        return $this->apiReturn(0, '使用成功');
    }

    /**
     * 使用优惠券
     * @return [type] [description]
     */
    public function useCoupon()
    {
        $user_coupon_id = I('request.user_coupon_id', 0, 'intval');
        $order_id = I('request.order_id', 0, 'intval');
        $order_sn = I('request.order_sn', '', 'trim');
        $user_id = cookie('uid');
        $UserCouponModel = D('UserCoupon');

        //确定订单是当前用户的订单
        $info = $this->getOrder($order_id);
        //非内部使用，进行校验
        if (!$this->auth()) {

            if ($info['err_code'] != 0) {
                return $this->apiReturn($info['err_code'], $info['err_msg']);
            }
            if ($info['data']['user_id'] != $user_id) {
                return $this->apiReturn(25014, '当前订单无效');
            }
            $order_sn = $info['data']['order_sn'];

            //确定优惠券是当前用户的优惠券
            $info = $UserCouponModel->find($user_coupon_id);
            if (empty($info) || $info['user_id'] != $user_id) {
                return $this->apiReturn(25013, '当前优惠券无效');
            }
        }
        $save = array(
            'user_coupon_id' => $user_coupon_id,
            'order_id' => $order_id,
            'order_sn' => $order_sn,
            'status' => 1,
            'use_time' => $_SERVER['REQUEST_TIME'],
        );
        $res = $UserCouponModel->save($save);
        if ($res === false) {
            return $this->apiReturn(25015, '系统繁忙，请稍后重试');
        }

        return $this->apiReturn(0, '使用成功');

    }

    // 后台新增订单获取优惠券信息
    public function infoByOrder()
    {
        $user_coupon_id = I('request.user_coupon_id', 0, 'intval');
        $goods_totals = I('request.goods_total', "");
        $cart_ids = I('request.cart_ids', '');
        $user_id = I('request.uid', '');
        $UserCouponModel = D('UserCoupon');

        try {
            $goods_totals = \GuzzleHttp\json_decode($goods_totals, true);
            list($ziTotal, $lyTotal) = $goods_totals;
        } catch (\Exception $e) {
            list($ziTotal, $lyTotal) = [0, 0];
        }

        $goods_total = 0;
        $coupon = $UserCouponModel->getInfo($user_coupon_id, $user_id);
        switch ($coupon["coupon_mall_type"]) {
            case 1 :
                $goods_total = $ziTotal + $lyTotal;
                break;
            case 2:
                $goods_total = $ziTotal;
                break;
            case 3:
                $goods_total = $lyTotal;

        }

        if (empty($coupon)) {
            return $this->apiReturn(25010, '没有可用的优惠券');
        } elseif ($coupon['status'] == 1) {
            return $this->apiReturn(25016, '当前优惠券已被使用，请重新选择');
        } elseif ($coupon['status'] != -1) {
            return $this->apiReturn(25011, '当前优惠券不可使用');
        } elseif ($goods_total > 0 && $coupon['require_amount'] > $goods_total) {
            return $this->apiReturn(25012, '当前优惠券不可使用');
        }
        $confirmInfo = $this->getConfirmGoods($cart_ids, $user_id);
        $remark = $this->getCouponRemark($coupon, $confirmInfo['data']);
        $coupon['preferential'] = $remark['preferential'];
        return $this->apiReturn(0, '获取成功', $coupon);
    }

    /**
     * 优惠券信息
     * @return [type] [description]
     */
    public function info()
    {
        $user_coupon_id = I('request.user_coupon_id', 0, 'intval');
        $goods_totals = I('request.goods_total', '');
        $cart_ids = I('request.cart_ids', '');
        $user_id = cookie('uid');
        $UserCouponModel = D('UserCoupon');


        try {
            $goods_totals = \GuzzleHttp\json_decode($goods_totals, true);
            list($ziTotal, $lyTotal) = $goods_totals;
        } catch (\Exception $e) {
            list($ziTotal, $lyTotal) = [0, 0];
        }

        $goods_total = 0;
        $coupon = $UserCouponModel->getInfo($user_coupon_id, $user_id);
        switch ($coupon["coupon_mall_type"]) {
            case 1 :
                $goods_total = $ziTotal + $lyTotal;
                break;
            case 2:
                $goods_total = $ziTotal;
                break;
            case 3:
                $goods_total = $lyTotal;

        }

        $coupon = $UserCouponModel->getInfo($user_coupon_id, $user_id);
        if (empty($coupon)) {
            return $this->apiReturn(25010, '没有可用的优惠券');
        } elseif ($coupon['status'] == 1) {
            return $this->apiReturn(25016, '当前优惠券已被使用，请重新选择');
        } elseif ($coupon['status'] != -1) {
            return $this->apiReturn(25011, '当前优惠券不可使用');
        } elseif ($goods_total > 0 && $coupon['require_amount'] > $goods_total) {
            return $this->apiReturn(25012, '当前优惠券不可使用');
        }
        $confirmInfo = $this->getConfirmGoods($cart_ids, $user_id);
        $remark = $this->getCouponRemark($coupon, $confirmInfo['data']);
        $coupon['preferential'] = $remark['preferential'];
        return $this->apiReturn(0, '获取成功', $coupon);
    }

    /**
     * 获取用户的优惠券信息
     * @return [type] [description]
     */
    public function getUserCouponInfo()
    {
        $UserCouponModel = D('UserCoupon');
        $user_id = I('request.user_id', 0, 'intval');
        $user_coupon_id = I('request.user_coupon_id', 0, 'intval');
        $coupon = $UserCouponModel->getInfo($user_coupon_id, $user_id);
        if (empty($coupon)) {
            return $this->apiReturn(25010, '未获取到优惠券相关信息');
        }
        return $this->apiReturn(0, '成功', $coupon);
    }

    /**
     * 返还优惠券
     * @return [type] [description]
     */
    public function returnCoupon()
    {
        if (!$this->auth()) {
            return $this->apiReturn(25021, '无效操作');
        }
        $order_id = I('order_id', 0, 'intval');
        $user_id = I('user_id', 0, 'intval');
        empty($user_id) && $user_id = cookie('uid');
        $UserCouponModel = D('UserCoupon');
        $res = $UserCouponModel->returnOrder($order_id, $user_id);
        if ($res === false) {
            return $this->apiReturn(25020, '返还失败');
        }
        $this->count(0, false, $user_id);
        return $this->apiReturn(0, '返还成功');
    }

    /**
     * 删除已失效券
     * @return [type] [description]
     */
    public function delete()
    {
        $user_coupon_id = I('request.user_coupon_id', 0, 'intval');
        $user_id = cookie('uid');
        $UserCouponModel = D('UserCoupon');
        $coupon = $UserCouponModel->getInfo($user_coupon_id, $user_id);
        if (empty($coupon)) {
            return $this->apiReturn(25010, '操作失败，请刷新页面后重试');
        } elseif ($coupon['status'] != -2) {
            return $this->apiReturn(25018, '该优惠券未失效，无法删除');
        }
        $res = $UserCouponModel->setDisable($user_coupon_id);
        if ($res === false) {
            return $this->apiReturn(25019, '系统繁忙，请稍后重试', $user_coupon_id);
        }
        $this->count(0, false);
        return $this->apiReturn(0, '删除成功');
    }


    public function test()
    {
        $res = $this->showUse(0, 0, [10322]);
        p($res);
    }
}
