<?php

namespace Coupon\Controller;

use Coupon\Controller\BaseController;

class CouponController extends BaseController
{
    ////////////////////////////////////
    // 数据库的surplus_num只在初始化时对比队列生成使用  //
    // redis的surplus_num没有作用，不要作为判断标准 //
    // 判断剩余数量通过(**redis队列长度**)来判断     //
    ////////////////////////////////////


    public function _initialize()
    {
        parent::_initialize();
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME),
                array('init', 'destroy', 'showcoupon', 'checkissue'))) {
            //检查登录
            $res = $this->checkLogin();
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    /**
     * 检查接口权限
     * @return [type] [description]
     */
    private function checkPerm()
    {
        $ips = C('ALLOW_VISIT_IP');
        $ip = get_client_ip(0, true);
        if (!in_array($ip, $ips)) {
            // header("HTTP/1.0 404 Not Found");
            // exit();
        }
    }

    /**
     * 券初始化进redis队列
     * @return [type] [description]
     */
    public function init()
    {
        $this->checkPerm();
        $coupon_id = I('id', 0, 'intval');
        $CouponModel = D('Coupon');
        $data = $CouponModel->getInfoById($coupon_id);
        if (empty($coupon_id) || empty($data)) {
            return $this->apiReturn(130011, '未找到相关数据', $coupon_id);
        }
        //用于减少相关数据库读取
        S_coupon($coupon_id, $data);

        if ($data['status'] != 1 && $data['status'] != 2) {
            return $this->apiReturn(130001, '当前状态无法初始化队列', $data);
        }
        $len = Q_coupon($coupon_id, '@len');
        $count = $data['surplus_num'] - $len;

        $error = 0;
        for ($i = 1; $i <= $count; $i++) {
            $result = Q_coupon($coupon_id, $i);
            if (!$result) {
                $error++;
            }
        }
        if ($error > 0) {
            return $this->apiReturn(130002, '存在' . $error . '条优惠券未加入队列', $error);
        }
        return $this->apiReturn(0, '初始化成功');
    }

    /**
     * 清空券的redis队列
     * @return [type] [description]
     */
    public function destroy($coupon_id = 0)
    {
        $this->checkPerm();
        empty($coupon_id) && $coupon_id = I('id', 0, 'intval');
        $CouponModel = D('Coupon');
        $info = S_coupon($coupon_id);

        $res = Q_coupon($coupon_id, null);
        try {
            S_coupon($coupon_id, null);
            // if (!empty($info)) {
            //     $data = $CouponModel->getInfoById($coupon_id);
            //     S_coupon($coupon_id, $data);
            // }
        } catch (Exception $e) {
        }
        if ($res != 1) {
            return $this->apiReturn(130010, '清空队列失败');
        }
        return $this->apiReturn(0, '清空成功');
    }

    /**
     * [getSubscribeCoupon 领取关注服务号优惠券]
     * @return [type] [description]
     */
    public function getSubscribeCoupon()
    {
        $pf = platform();
        $agentInfo = getAgentInfo();
        if (strval($pf) !== '2' || strval($agentInfo['bro']) !== '9') { // pc端 或者 不在微信环境
            return $this->apiReturn(25023, '请搜索并关注公众号“猎芯网”，直接领取优惠券！');
        }
        return $this->apiReturn(0, '领取成功');
    }

    /**
     * [getCoupon 注册领取优惠券（在特定的时间内）]
     * @return [type] [description]
     */
    public function getRegisterCoupon($user_id)
    {
        $userInfo = S_user($user_id);
        $create_time = $userInfo['create_time'];
        $err_info = array();
        if (!$create_time) { //redis中获取不到
            $UserMainModel = D('UserMain');
            $create_time = $UserMainModel->where("user_id = {$user_id}")->getField('create_time');
        }
        //是否在要求的时间内注册的
        if ($create_time >= C('COUPON_REG_TIME.START') && $create_time <= C('COUPON_REG_TIME.END')) {
            //请求优惠券
            return $this->apiReturn(0, '领取成功');
        } else {
            $err_info['create_time'] = $create_time;
            $pf = platform();
            $addBr = '';
            if (strval($pf) === '1') {
                $addBr = '<br>';
            }
            return $this->apiReturn(25022, '目前只有在4月2日-4月30日的新注册用户，才能领取大礼包哦！' . $addBr . '
    老朋友可以搜索并关注公众号“猎芯网”，领取20元无门槛优惠券！', $err_info);
        }
    }

    /**
     * 发放券
     * @return [type] [description]
     */
    public function issue()
    {
        //区分组织发放券
        $org_id = I('org_id', 1, 'intval');
        if (!$this->auth() && $org_id == 1) {
            $supper = false;
            $user_id = cookie('uid');
        } else {
            $supper = true;
            $user_id = I('uid', 0, 'intval');
        }
        $coupon_id = I('id', 0, 'intval');
        $order_id = I('order_id', 0, 'intval');
        $need_send_msg = I('need_send_msg', 1, 'intval');
        //判断是否为前端领取还是后台发放
        $coupon_source = I('coupon_source', 2, 'intval');

        $adtag = adtag();
        $reg_activity_id = C('COUPON_EVENT_OPTION.REG');
        foreach ($reg_activity_id as $key => $value) {
            if ($coupon_id == $value) { //注册送券
                $res = $this->getRegisterCoupon($user_id);
                if ($res['err_code'] != 0) {
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                    die;
                }
            }
        }

        $subscribe_id = C('COUPON_EVENT_OPTION.SUBSCRIBE');
        if ($coupon_id == $subscribe_id) { //关注服务号领券
            $res = $this->getSubscribeCoupon();
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
                die;
            }
        }
        $info = S_coupon($coupon_id);
        if (empty($info)) {
            return $this->apiReturn(130008, '找不到相关券');
        }
        if (!in_array($info['status'], array(1, 2))) {
            return $this->apiReturn(130007, '系统繁忙，请稍后重试');
        }

        $CouponModel = D('Coupon');

        $UserCouponModel = D('UserCoupon');

        $coupon_info = $CouponModel->getInfoById($coupon_id);

        //判断是否有领取时间限制
        if (!empty($coupon_info['limit_time']) && $coupon_info['limit_time'] > 0 && $coupon_info['limit_time'] < 24) {

            $hour_limit = date('H', time());
            if ($hour_limit < $coupon_info['limit_time']) {
                return $this->apiReturn(130012, '优惠券设置为' . $coupon_info['limit_time'] . '时开始发放,请稍后再试');
            }
        }

        //判断是否要校验开始时间和结束时间内才能领取
        if (!empty(I('check_valid_time'))) {
            $now = time();
            if ($now < $coupon_info['start_time']) {
                return $this->apiReturn(130012, '还未到领券时间,请稍后再来');
            }
            if ($now > $coupon_info['end_time']) {
                return $this->apiReturn(130012, '该券已经过期,无法领取');
            }
        }

        //判断该券属性是否是支付后送券
        //todo 目前爱智那边没有这个规则,有的话要考虑兼容
        if ($coupon_info['coupon_get_rule'] == 3) {

            $order_model = D('Order/Order');

            $order_where['status'] = 4;

            $order_where['pay_time'] = array(
                array('GT', $coupon_info['effect_start_time']),
                array('LT', $coupon_info['effect_end_time'])
            );


            //获取在该券时间范围内的支付订单数量有多少
            $user_order_info = $order_model->getUserList($user_id, $order_where);

            foreach ($user_order_info as $key => $value) {
                $in_data[] = $value['order_sn'];
            }

            if (count($in_data) <= 0) {
                return $this->apiReturn(130003, '下单才可以领取优惠券哦~');
            }

            $coupon_num_sn = $UserCouponModel->get_order_sn($in_data);

            if ($coupon_num_sn == false) {
                return $this->apiReturn(130003, '您已经领取过优惠券了');
            } else {
                $order_id = $coupon_num_sn;
            }


            $user_coupon['user_id'] = $user_id;
            $user_coupon['C.coupon_id'] = $coupon_id;
            $user_coupon['UC.status'] = -1;//查询待使用的券

            //获取该用户已经有多少张这个券
            $user_coupon_info = $UserCouponModel->getCount($user_coupon);


            $coupon_num = count($user_order_info) - $user_coupon_info;

            if ($coupon_num <= 0) {
                return $this->apiReturn(130003, '优惠券已领完!');
            }


        }

        //获取队列长度即剩余券数量
        $len = Q_coupon($coupon_id, '@len');
        if ($len == 0) {
            return $this->apiReturn(130003, '优惠券已抢空啦');
        }

        //领取
        $take = Q_coupon($coupon_id);
        if ($take > 0) {
            //先领取 减少数据库读取 失败再重新入队

            //验证新用户领取条件
            $res = $this->valid_new_user_get($coupon_id, $user_id);
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
            //验证条件
            $res = $this->valid($coupon_id, $user_id);
            if (!$supper || $org_id != 1) {
                if ($res['err_code'] != 0) {
                    Q_coupon($coupon_id, $take);
                    $this->behavior_push(9, $res['err_code'], $res['err_msg']);
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
                $can_receive_num = $res['data']['count'] - 1;//领取完还能领取几张
            } else {//后台手动发送时只限制所有券数量，其余不限制
                $can_receive_num = $len - 1;
            }
            $total_received_num = intval($res['data']['total_received_num']) + 1;//已经领取的数量

            $CouponModel->startTrans();
            //减数据库数量
            $res = $CouponModel->decNum($coupon_id);
            if ($res === false) {
                Q_coupon($coupon_id, $take);
                $this->behavior_push(9, 130006, '优惠券已抢空啦');
                return $this->apiReturn(130009, '优惠券已抢空啦');
            }

            //发放
            $today_last_second = mktime(23, 59, 59, date("m"), date("d"), date("Y"));
            $data = array(
                'user_id' => intval($user_id),
                'coupon_id' => $coupon_id,
                'coupon_type' => $info['coupon_type'],
                'create_time' => $_SERVER['REQUEST_TIME'],
                'end_time' => $info['time_type'] == 1 ? $info['end_time'] : $info['usable_time'] * 86400 + $today_last_second,
                //$_SERVER['REQUEST_TIME'],
                'client_ip' => get_client_ip(0, true),
                'source' => get_source(),
                'status' => -1,
                'adtag' => $adtag,
                'source_order_id' => $order_id,
                'org_id' => $org_id,
            );

            $res = $UserCouponModel->add($data);
            if ($res === false) {
                Q_coupon($coupon_id, $take);
                $CouponModel->rollback();
                $this->behavior_push(9, 130006, '系统繁忙，请稍后重试');
                return $this->apiReturn(130006, '系统繁忙，请稍后重试');
            }
            $CouponModel->commit();


            if ($info['time_type'] == 1) {
                $start_time = $info['start_time'];
                $end_time = $info['end_time'];
            } else {
                $start_time = $data['create_time'];
                $end_time = $data['end_time'];
            }
            $resp = array(
                'coupon_mall_type' => $info['coupon_mall_type'],
                'coupon_goods_range' => $info['coupon_goods_range'],
                'selected_supplier' => $info['selected_supplier'],
                'selected_brand' => $info['selected_brand'],
                'can_receive_num' => $can_receive_num,
                'total_received_num' => $total_received_num,
                'sale_amount' => floatval($info['sale_amount']),
                'coupon_name' => $info['coupon_name'],
                'start_time' => date('Y-m-d', $start_time),
                'end_time' => date('Y-m-d', $end_time),
            );
            //非必要，原因数量不通过这个值判断
            try {
                if ($len <= 0) {
                    $this->destroy($coupon_id);
                } else {
                    //减redis合计数量
                    $info['surplus_num']--;
                    S_coupon($coupon_id, $info);
                }

                switch ($org_id) {
                    case 1 :
                        //缓存统计
                        A('Coupon/User')->count(0, false, $user_id);
                        $unit_format = (1 == $info['coupon_type']) ? '元抵扣券' : '折折扣券';
                        $msg = (1 == $info['coupon_type']) ? $info['sale_amount'] : intval($info['sale_amount'] * 100);
                        //50折变5折显示等
                        if ((2 == $info['coupon_type']) && (0 == $msg % 10)) {
                            $msg /= 10;
                        }
                        if ($need_send_msg) {
                            $msg_array = array(
                                'ex_int' => 1,
                                'money' => $msg . $unit_format,
                                'least_money' => $info['require_amount'],
                                'dead_line' => $resp['end_time']
                            );
                            //发送消息
                            switch ($info['coupon_mall_type']) {
                                case 1:
                                    $msg_array['usable_range'] = '全站所有商品';
                                    break;
                                case 2:
                                    if (1 == $info['coupon_goods_range']) {
                                        $msg_array['usable_range'] = '自营所有商品';
                                    } elseif (3 == $info['coupon_goods_range']) {
                                        $msg_array['usable_range'] = '自营-制造商' . $info['selected_brand'] . '的所有商品';
                                    }
                                    break;
                                case 3:
                                    if (1 == $info['coupon_goods_range']) {
                                        $msg_array['usable_range'] = '联营所有商品';
                                    } elseif (2 == $info['coupon_goods_range']) {
                                        if (empty($info['selected_brand'])) {
                                            $msg_array['usable_range'] = '联营-供应商' . $info['selected_supplier'] . '的所有商品';
                                        } else {
                                            $msg_array['usable_range'] = '联营-供应商' . $info['selected_supplier'] . '旗下的' . $info['selected_brand'] . '的所有商品';
                                        }
                                    } else {
                                        $msg_array['usable_range'] = '联营-制造商' . $info['selected_brand'] . '的所有商品';
                                    }
                                    break;
                                default:
                                    break;
                            }

                            //微信消息推送
                            $wechat_data = array( //微信数据传送
                                'keyword1' => array('value' => $msg_array['usable_range'], 'color' => '#173177'),
                                'keyword2' => array('value' => $msg_array['money'], 'color' => '#173177'),
                                'keyword3' => array('value' => $msg_array['dead_line'], 'color' => '#173177'),
                            );
                            $this->sendOrderMsg('coupon-get-new', $user_id, $org_id, $msg_array, $wechat_data);
                        }
                        break;
                    case 3:
                        $unit_format = (1 == $info['coupon_type']) ? '元抵扣券' : '折折扣券';
                        $msg = (1 == $info['coupon_type']) ? $info['sale_amount'] : intval($info['sale_amount'] * 100);
                        //50折变5折显示等
                        if ((2 == $info['coupon_type']) && (0 == $msg % 10)) {
                            $msg /= 10;
                        }
                        if ($need_send_msg) {
                            $msg_array = array(
                                'ex_int' => 1,
                                'money' => $msg . $unit_format,
                                'least_money' => $info['require_amount'],
                                'dead_line' => $resp['end_time']
                            );
                            $this->sendOrderMsg('iedge-coupon-get-new', $user_id, $org_id, $msg_array);
                        }
                        break;
                }


            } catch (\Exception $e) {

            }

            if ($org_id == 1) {
                //行为统计
                !$supper && $this->behavior_push(9, 0,
                    array('coupon_id' => $info['coupon_id'], 'coupon_sn' => $info['coupon_sn']));

                $this->setRollData(5, $user_id, array(
                    'coupon_type' => $info['coupon_type'],
                    'require_amount' => $info['require_amount'],
                    'sale_amount' => $info['sale_amount'],
                ));
            }

            return $this->apiReturn(0, '领取成功', $resp);
        } else {
            $this->behavior_push(9, 130003, '优惠券已抢空啦');
            return $this->apiReturn(130003, '优惠券已抢空啦');
        }
    }

    /**
     * 验证新用户才可领取
     * @param $coupon_id
     * @param $user_id
     * @return array
     */
    public function valid_new_user_get($coupon_id, $user_id)
    {
        $info = S_coupon($coupon_id);
        //验证领取条件 注册可领
        if ($info['coupon_get_rule'] == 1) {
            $userInfo = S_user($user_id);
            $create_time = $userInfo['create_time'];
            if (!$create_time) { //redis中获取不到
                $UserMainModel = D('UserMain');
                $create_time = $UserMainModel->where("user_id = {$user_id}")->getField('create_time');
            }
            if ($create_time < $info['reg_start_time']) {
                return $this->apiReturn(-130006, '新用户才可领取哦~详情请查看活动规则');
            }
        }
    }

    /**
     * 统一验证条件
     * @param  [type] $coupon_id [description]
     * @param  [type] $user_id   [description]
     * @return [type]            [description]
     */
    public function valid($coupon_id, $user_id, $org_id = 1)
    {
        $UserCouponModel = D('UserCoupon');
        $info = S_coupon($coupon_id);
        $count = 0;
        $len = Q_coupon($coupon_id, '@len');
        //验证总领取量
        $map = array(
            'user_id' => $user_id,
            'coupon_id' => $coupon_id,
        );
//        $start_end_time = $UserCouponModel->getGetTimeByUser($map);
        $total_count = $UserCouponModel->getCountByUser($map);
        if ($info['total_receive_num'] != 0 && $info['total_receive_num'] <= $total_count) {
            $arr = array(
                'total_received_num' => $total_count,//已经领取了的数量
                'count' => 0,//今天剩余领取数量
            );
//            //有效期
//            if(!empty($start_end_time)){
//                $arr['start_time'] = $start_end_time['create_time'];
//                $arr['end_time'] = $start_end_time['end_time'];
//            }
            return $this->apiReturn(-130004, '您的优惠券领取次数已用完。', $arr);
        } else {
            //用户总的还剩几张可领
            $count = $info['total_receive_num'] > 0 ? $info['total_receive_num'] - $total_count : $len;
        }
        //验证每天领取量
        $map = array(
            'user_id' => $user_id,
            'coupon_id' => $coupon_id,
            'create_time' => array('gt', strtotime(date('Y-m-d'))),
        );
        $day_count = $UserCouponModel->getCountByUser($map);
        if ($info['day_receive_num'] != 0 && $info['day_receive_num'] <= $day_count) {
            $arr = array(
                'total_received_num' => $total_count,//已经领取了的数量
                'count' => 0,//今天剩余领取数量
            );
//            //有效期
//            if(!empty($start_end_time)){
//                $arr['start_time'] = $start_end_time['create_time'];
//                $arr['end_time'] = $start_end_time['end_time'];
//            }
            return $this->apiReturn(-130005, '您今天优惠券的领取次数已用完，请明天再来吧！', $arr);
        } else {
            //今天还剩余几张可领
            $count = $info['day_receive_num'] > 0 ? $info['day_receive_num'] - $day_count : $count;
        }
        $arr = array(
            'total_received_num' => $total_count,//用户已经领取了的数量
            'day_received_num' => $day_count,//用户今天已经领取了的数量
            'count' => $count,//用户今天剩余领取数量
        );
//        //有效期
//        if(!empty($start_end_time)){
//            $arr['start_time'] = $start_end_time['create_time'];
//            $arr['end_time'] = $start_end_time['end_time'];
//        }
        return $this->apiReturn(0, '符合领取条件', $arr);
    }


    /**
     * 缓存购物车信息 by long
     */
    protected function showCouponCache($cart_ids, $user_id)
    {
        $key = C("SHOWCOUPON_SHOPPING_CART_CACHE");
        $cache_key = $key . ":" . $user_id;
        $cache = S($cache_key);
        $selected_supplier_ids = [];
        $selected_brand_ids = [];
        if (!$cache) {
            //查询购物车
            $confirmInfo = $this->getConfirmGoods($cart_ids, $user_id);
            foreach ($confirmInfo['data']['list'] as $obj) {
                $selected_supplier_ids[] = $obj['supplier_id'];
                $selected_brand_ids[] = $obj['brand_id'];
            }
            if ($selected_supplier_ids || $selected_brand_ids) {
                $selected_supplier_ids = array_unique($selected_supplier_ids);
                $selected_brand_ids = array_unique($selected_brand_ids);
                $cache = S($cache_key,
                    ["selected_supplier_ids" => $selected_supplier_ids, "selected_brand_ids" => $selected_brand_ids],
                    ['expire' => C("SHOWCOUPON_SHOPPING_CART_CACHE_TTL")]
                );
            }

        } else {
            $selected_supplier_ids = isset($cache['selected_supplier_ids']) ? $cache['selected_supplier_ids'] : [];
            $selected_brand_ids = isset($cache['selected_brand_ids']) ? $cache['selected_brand_ids'] : [];
        }

        return [$selected_supplier_ids, $selected_brand_ids];
    }

    /**
     * CMS设置显示前台展示的优惠券
     * @return [type] [description]
     */
    public function showCoupon()
    {
        $tags = I('tags', '');//cms分类配置的标记
        $type = I('type', 0) + 0;//页面类型
        $cart_ids = I('cart_ids', '');//购物车中
        $supplier_id = I('supplier_id', 0);//商详中
        $brand_id = I('brand_id', 0);//商详中
        $coupon_mall_type = I('coupon_mall_type', 2);//默认自营优惠券
        $user_id = cookie('uid');
        $CmsModel = D('Coupon/Cms');
        // $user_id = 0;
        $CouponModel = D('Coupon/Coupon');
        $limit = C('SHOW_NUMBER.' . $tags . '_' . $type);
        empty($limit) && $limit = C('SHOW_NUMBER.default');
        //临时兼容测试使用
        $domain = parse_url($_SERVER['HTTP_REFERER']);
        if (strpos($domain['host'], 'sz') !== false) {
            $tags = 'sz_' . $tags;
        }

        //取出购物车中所有 供应商和品牌 以供 筛选优惠券
        $selected_supplier_ids = array();
        $selected_brand_ids = array();
        if ($user_id && !empty($cart_ids)) {
            list($selected_supplier_ids, $selected_brand_ids) = $this->showCouponCache($cart_ids, $user_id);
        }

        $lists = $CmsModel->getBaseList($tags, '1,' . null, 'sort DESC', 'type=' . $type);
        foreach ($lists as $k => &$v) {
            $coupon = $CouponModel->getInfoById($v['tem_id']);
            if (empty($coupon) || !in_array($coupon['status'], array(1, 2))) {
                unset($lists[$k]);
                continue;
            }
            if ($coupon['time_type'] == 1) {
                if ($coupon['end_time'] < $_SERVER['REQUEST_TIME']) {
                    unset($lists[$k]);
                    continue;
                }
                $v['start_time'] = date('Y.m.d', $coupon['start_time']);
                $v['end_time'] = date('Y.m.d', $coupon['end_time']);
            } else {
                $v['usable_time'] = $coupon['usable_time'];
                //todo:测试下是否可以替换 264的start_end_time+3个有效期+已领取的有效期为相对时间的特殊逻辑 ！！！先测一下当前已提交的没问题再说
                //获取有效时间为相对时间的 开始 结束时间
                $UserCouponModel = D('UserCoupon');
                if (!empty($user_id)) {
                    $map = array(
                        'user_id' => $user_id,
                        'coupon_id' => $v['tem_id'],
                    );
                    $start_end_time = $UserCouponModel->getGetTimeByUser($map);
                    if (!empty($start_end_time)) {
                        $v['start_time'] = date('Y.m.d', $start_end_time['create_time']);
                        $v['end_time'] = date('Y.m.d', $start_end_time['end_time']);
                    }
                }
            }
            $require_index = 1 == $coupon['coupon_type'] ? 0 : 1;
            $v['require_desc'] = sprintf(C('USER_COUPON_REQUIRE.' . $require_index), $coupon['require_amount']);
            $v['coupon_type_val'] = C('COUPON_TYPE.' . $coupon['coupon_type']);
            $v['coupon_type_'] = $coupon['coupon_type'];
            $v['sale_amount'] = floatval($coupon['sale_amount']);
            $v['time_type'] = $coupon['time_type'];
            $v['coupon_mall_type'] = $coupon['coupon_mall_type'];
            $v['coupon_goods_range'] = $coupon['coupon_goods_range'];
            $v['selected_supplier'] = $coupon['selected_supplier'];
            $v['selected_brand'] = $coupon['selected_brand'];
            $v['selected_supplier_id'] = $coupon['selected_supplier_id'];
            $v['selected_brand_id'] = $coupon['selected_brand_id'];

            if (!empty($user_id)) {
                $res = $this->valid($coupon['coupon_id'], intval($user_id));
                $count = intval($res['data']['count']);
//                //已领取的有效期为相对时间的特殊逻辑
//                if($coupon['time_type'] == 2 && !empty($res['data']['start_time'])){
//                    $v['start_time'] = date('Y.m.d', $res['data']['start_time']);
//                    $v['end_time'] = date('Y.m.d', $res['data']['end_time']);
//                }
            } else {
                $count = 1;
            }
            $v['surplus_num'] = Q_coupon($v['tem_id'], '@len');
            $v['can_receive_num'] = $count;
            $v['total_received_num'] = intval($res['data']['total_received_num']);
            //去除不符合条件的券
            //1.去除不符合联营/自营条件的券
            if (1 != $coupon['coupon_mall_type'] && $coupon_mall_type != $coupon['coupon_mall_type']) {
                unset($lists[$k]);
            }
            //2.去除不符合供应商或品牌的券的券
            //购物车
            if (!empty($cart_ids)) {
                if (2 == $coupon['coupon_goods_range']) {
                    if (empty($coupon['selected_brand_id'])) {
                        if (!in_array($coupon['selected_supplier_id'], $selected_supplier_ids)) {
                            unset($lists[$k]);
                        }
                    } else {
                        if ((!in_array($coupon['selected_supplier_id'],
                                $selected_supplier_ids)) || (!in_array($coupon['selected_brand_id'],
                                $selected_brand_ids))) {
                            unset($lists[$k]);
                        }
                    }
                } elseif (3 == $coupon['coupon_goods_range']) {
                    if (!in_array($coupon['selected_brand_id'], $selected_brand_ids)) {
                        unset($lists[$k]);
                    }
                }
            } else {//商详
                if (2 == $coupon['coupon_goods_range']) {
                    if (empty($coupon['selected_brand_id'])) {
                        if ($supplier_id != $coupon['selected_supplier_id']) {
                            unset($lists[$k]);
                        }
                    } else {
                        if (($supplier_id != $coupon['selected_supplier_id']) || ($brand_id != $coupon['selected_brand_id'])) {
                            unset($lists[$k]);
                        }
                    }
                } elseif (3 == $coupon['coupon_goods_range']) {
                    if ($brand_id != $coupon['selected_brand_id']) {
                        unset($lists[$k]);
                    }
                }
            }
            //如果没有券了 用户也没领取过剔除掉
            if (!$v['total_received_num'] && !$v['surplus_num']) {
                unset($lists[$k]);
            }
        }

        //购物车取前5  商详取前3
        if (!empty($cart_ids)) {
            if (count($lists) > 5) {
                $lists = array_slice($lists, 0, $limit);
            }
        } else {
            if (count($lists) > 3) {
                $lists = array_slice($lists, 0, $limit);
            }
        }
        return $this->apiReturn(0, '获取成功', $lists);
    }

    /**
     * 检查是否可领取
     * @return [type] [description]
     */
    public function checkIssue()
    {
        $ids = I('request.ids', '', 'trim');
        $ids = explode(',', $ids);
        $user_id = cookie('uid');
        $CouponModel = D('Coupon/Coupon');
        $lists = array();
        foreach ($ids as $v) {
            $coupon = $CouponModel->getInfoById($v);

            if (empty($coupon) || !in_array($coupon['status'], array(1, 2))) {
                $count = 0;
            } else {
                $count = 1;
                if ($coupon['time_type'] == 1) {
                    if ($coupon['end_time'] < $_SERVER['REQUEST_TIME']) {
                        $count = 0;
                    }
                }
                if (!empty($user_id)) {
                    $res = $this->valid($coupon['coupon_id'], intval($user_id));
                    $count = intval($res['data']['count']);
                }
            }
            $lists[$v]['surplus_num'] = Q_coupon($v, '@len');
            $lists[$v]['can_receive_num'] = $count;
            $lists[$v]['total_received_num'] = intval($res['data']['total_received_num']);
        }
        return $this->apiReturn(0, '获取成功', $lists);
    }
}
