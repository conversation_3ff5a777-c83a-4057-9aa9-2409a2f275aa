<?php

namespace Coupon\Controller;

use Coupon\Model\VoucherModel;

class VoucherController extends BaseController
{

    public function _initialize()
    {
        parent::_initialize();
        if (!$this->auth() && !in_array(strtolower(ACTION_NAME),
                array('init', 'destroy', 'showcoupon', 'checkissue'))) {
            //检查登录
            $res = $this->checkLogin();
            if ($res['err_code'] != 0) {
                return $this->apiReturn($res['err_code'], $res['err_msg']);
            }
        }
    }

    //获取用户的电子券
    public function getVoucherList()
    {
        $userId = cookie('uid');
        $page = I('page', 1);
        $limit = I('limit', 10);
        $voucherModel = new VoucherModel();
        $data = $voucherModel->getVoucherListByUserId($userId, $page, $limit);
        $this->apiReturn(0, 'success', $data);
    }

    public function deleteVoucher()
    {
        $userId = cookie('uid');
        $id = I('id');
        $voucherModel = new VoucherModel();
        if (!$voucherModel->checkVoucherOwner($id, $userId)) {
            return $this->apiReturn(-1, '该券不属于当前请求用户');
        }
        $result = $voucherModel->deleteVoucher($id);
        if (!$result) {
            return $this->apiReturn(-1, '删除券失败');
        }
        return $this->apiReturn(0, 'ok');
    }

    public function viewVoucherWithOutEncryption()
    {
        $userId = cookie('uid');
        $id = I('id');
        $SMSVerify = I('sms_verify', '');
        if (empty($SMSVerify)) {
            return $this->apiReturn(-1, '请输入验证码');
        }
        $account = I('account', '');
        $intlCode = I('intl_code', '', 'trim'); // 国际手机代号
        $account = get_inte_mobile($account, $intlCode);

        //校验短信验证码
        $code = session_sms($account);
        if ($code !== pwdhash($SMSVerify, C('SMS_SALT'))) {
            return $this->apiReturn(-1, '短信验证码错误，请重新获取');
        }

        $voucherModel = new VoucherModel();
        if (!$voucherModel->checkVoucherOwner($id, $userId)) {
            return $this->apiReturn(-1, '该券不属于当前请求用户');
        }
        $voucher = $voucherModel->viewVoucherWithOutEncryption($id);
        return $this->apiReturn(0, 'ok', $voucher);
    }
}