<?php

namespace Coupon\Controller;

class BaseController extends \Common\Controller\BaseController
{

    /**
     * 获取订单信息
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    protected function getOrder($order_id)
    {
        $data['order_id'] = $order_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/order/findOrder', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 发送消息
     * @return [type] [description]
     */
    /**
     * 发送推送消息通知内部人员
     */
    protected function sendOrderMsg($keyword, $to_user, $org_id, $param = '', $wechat = '')
    {
        $data['touser'] = $to_user;
        $data['data'] = json_encode($param, JSON_UNESCAPED_UNICODE);
        $data['wechat_data'] = json_encode($wechat, JSON_UNESCAPED_UNICODE);
        $data['keyword'] = $keyword;
        $data['is_ignore'] = false;
        $data['org_id'] = $org_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/msg/sendmessagebyauto', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取结算时的商品信息
     * @param [string] cart_id
     * @return [array]
     */
    protected function getConfirmGoods($cart_ids, $user_id)
    {
        $data['cart_id'] = $cart_ids;
        $data['uid'] = $user_id;
        $data = array_merge($data, authkey());
        $res = post_curl(API_DOMAIN . '/cart/getConfirmGoods', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 设置滚动数据
     * @param [type] $type    [description]
     * @param [type] $user_id [description]
     * @param array $extend [description]
     */
    protected function setRollData($type, $user_id, $extend = array())
    {
        try {
            if (!empty($user_id)) {
                $data['type'] = $type;
                $data['user_id'] = $user_id;
                $data = array_merge($data, authkey(), $extend);
                $res = post_curl(API_DOMAIN . '/user/recorduserbehaviortoshow', $data);
                if (!empty($res)) {
                    $res = json_decode($res, true);
                }
            }
        } catch (\Exception $e) {
        }
        return $res;
    }
}
