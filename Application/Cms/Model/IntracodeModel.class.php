<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace Cms\Model;
use Think\Model;
/**
 * Description of CmsModel
 *
 * <AUTHOR>
 */
class IntracodeModel extends Model
{
    protected $tableName = 'lie_intracode';
    protected $tablePrefix = '';

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.CMS');
    }

    public function getIntracodeInfo($code_id)
    {
        return $this->where(['code_id' => $code_id])->find();
    }

}
