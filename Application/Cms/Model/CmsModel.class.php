<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace Cms\Model;
use Think\Model;
/**
 * Description of CmsModel
 *
 * <AUTHOR>
 */
class CmsModel extends Model
{
    protected $autoCheckFields = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.CMS');
    }

    /**
     * 获取数据
     * @return [type]              [description]
     */
    public function getData($table, $where, $select='select', $field='*', $limit='', $order='')
    {
       return $this->table($table)->field($field)->where($where)->order($order)->limit($limit)->$select();
    }

}
