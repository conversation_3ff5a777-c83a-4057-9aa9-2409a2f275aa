<?php
/**
 * ------------------------------------------ 信宏泰官网 ---------------------------------------------
 */
namespace Cms\Controller;

use C<PERSON>\Controller\BaseController;

class XhtController extends BaseController
{
    // 获取信宏泰官网产品配置信息
    public function getCompany()
    {
        $datas = S(C('XHT_COMPANY_CACHE_SET')); // 获取缓存

        if (!$datas) {
            $cms = D('Cms');

            $sort = 'sort desc';
            $map['status'] = 1;
            $field = 'com_id, sample_name, logo_pc, logo_h5, url, slogan, status, sort, window_open';

            $datas = $cms->getData('lie_company', $map, 'select', $field, '', $sort);

            if (!$datas) return $this->apiReturn(10001, '未获取到配置数据');

            $options['expire'] = 300; // 缓存5分钟
            S(C('XHT_COMPANY_CACHE_SET'), json_encode($datas), $options);
        }

        return $this->apiReturn(0, '获取数据成功', $datas);
    }

    // 获取对应公司信息
    public function getCompanyInfo()
    {
        $com_id = I('com_id', '');

        if (!$com_id) return $this->apiReturn(10002, '参数缺失');

        $datas = S(C('XHT_COMPANY_INFO_CACHE_SET').'_'.$com_id); // 获取缓存

        if (!$datas) {
            $cms = D('Cms');
            $where = [];
            $where['com_id'] = $com_id; 
            $field = 'com_id, sample_name, logo_pc, logo_h5, url, slogan, status, sort, window_open'; 

            $datas = $cms->getData('lie_company', $where, 'find', $field);
            $datas['company_info'] = $cms->getData('lie_company_info', $where, 'find');

            // 背景图片分类ID
            $bcat = $cms->getData('lie_base_cat', ['tags'=>'xht_banner_bg_img'], 'find', 'bcat_id');

            $base['bcat_id'] = $bcat['bcat_id'];
            $base['status']  = 1;
            $datas['company_bg_img'] = $cms->getData('lie_base', $base, 'find');

            $where['status'] = 1; // 状态
            $sort = 'sort desc';
            $datas['company_img'] = $cms->getData('lie_company_img', $where, 'select', '*', '', $sort); 

            if (!$datas['company_info'] && $datas['company_img']) return $this->apiReturn(10003, '未获取到相关公司信息');

            $options['expire'] = 300; // 缓存5分钟
            S(C('XHT_COMPANY_INFO_CACHE_SET').'_'.$com_id, json_encode($datas), $options);
        }

        return $this->apiReturn(0, '获取数据成功', $datas);
    }

    // 清除缓存
    public function clearCache()
    {
        $com_id = I('com_id', 1);
        S(C('XHT_COMPANY_CACHE_SET'), NULL);
        S(C('XHT_COMPANY_INFO_CACHE_SET').'_'.$com_id, NULL);
    }

}





