<?php
namespace Server\Controller;

use Server\Controller\BaseController;

class PublishController extends BaseController
{

    /**
     * 推送订单信息至队列
     * @return [type] [description]
     */
    public function pushSynOrder()
    {
        $order = $this->getSynOrder();
        if ($order['err_code'] != 0) {
            exit('false');
        }
        $order = $order['data'];
        // dump($order);die();
        foreach ($order as &$v) {
            $res = $this->getErpSendInfo($v);
            if ($res['err_code'] != 0) {
                continue;
            }
            switch($res['data']['info']['status']) {
                case '3' :
                    $this->pushOrder($res['data']);
                    break;
                case '4' :
                    if ($res['data']['info']['order_pay_type'] == 2) {
                        $this->pushAdvance($res['data']);
                    } else {
                        $this->pushOrder($res['data']);
                    }
                    break;
            }
        }
        exit('true');
    }

    /**
     * 发送订单至ERP同步队列
     * @param  [type] $order_id [description]
     * @return [type]           [description]
     */
    public function pushOrder($order_data = 0)
    {
        if (!is_array($order_data)) {//order_id
            $res = $this->getErpSendInfo($order_data);
            if ($res['err_code'] != 0) {
                exit('false');
            }
            $order_data = &$res['data'];
        }
        extract($order_data);//解出$info  $lists  $invoice $user $pay
        foreach ($lists as $v) {
            $list[] = array(
                'RECID' => $v['rec_id'],
                'MATERIAL' => $v['goods_name'],
                'FMODEL' => $v['brand_name'],
                'FQTY' => $v['goods_number'],
                'FTAXAMOUNT' => $v['goods_price'],
            );
        }

        if ($invoice['inv_type'] == 1) {//不开发票使用账号
            $data['invoice'] = array(
                'CUSTOMER' => $user['user_info']['mobile'] ? $user['user_info']['mobile'] : $user['user_info']['email'],
                'FADDRESS' => '',
                'FTXREGISTERNO' => '',
                'FPHONE' => $user['user_info']['mobile'] ? $user['user_info']['mobile'] : $user['user_info']['email'],
                'FBANK' => '',
                'FBANKACCOUNT' => '',
            );
        } else {
            $data['invoice'] = array(
                'CUSTOMER' => $invoice['tax_title'],
                'FADDRESS' => $invoice['company_address'],
                'FTXREGISTERNO' => $invoice['tax_no'],
                'FPHONE' => $invoice['company_phone'],
                'FBANK' => $invoice['bank_name'],
                'FBANKACCOUNT' => $invoice['bank_account'],
            );
        }
        $data['invoice']['DESCRIPTION'] = $info['user_id'];

        $data['info'] = array(
            'NUMBER' => $info['order_sn'],
            'CURRENCY' => C('CURRENCY.'.$info['currency']),
            'BIZDATE' => date('Y-m-d', $info['create_time']),
            'DELIVERTYPE' => C('ERP_DELIVER_TYPE.' . $info['order_shipping_type']),
            'PAYTYPE' => C('ERP_PAY_TYPE.' . $info['order_pay_type']),
            'CUSTOMER' => $data['invoice']['CUSTOMER'],
            'SALEUSER' => $info['sale_name'],
            'AMOUNT' => $info['order_amount'],
            'PAYAMOUNT' => $info['order_pay_type'] == 2 ? $info['advance_amount'] : $info['order_amount'],//预付款订单使用预付金额，其他使用订单金额
            'SOURCE' => 'PT',
            'INVOICETYPE' => C('ERP_INVOICE_TYPE_MAPPING.' . $invoice['inv_type']),
            'DESCRIPTION' => sprintf('优惠金额：%s，附加费：%s', $info['preferential_price'], $info['ext_price']),
            'ENTRYS' => $list,
        );

        foreach ($pay as $v) {
            $data['info']['SETTYPE'] = C('ERP_PAY_WAY.'. $v['pay_name']);
            $data['info']['SETDES'] = '订单号：'.$info['order_sn'].'，流水号：'.$v['serial_number'].'，具体支付渠道：'.$v['pay_name'];
            if ($info['order_pay_type'] == 2 && $info['status'] == 2 && $v['pay_type'] == 2) {//此时同步预付款的信息
                $data['info']['SETTYPE'] = C('ERP_PAY_WAY.'. $info['pay_name']);
                break;
            }
        }
        $RBMQModel = D('Server/RBMQ');
        $res = $RBMQModel->queue('web2erp')->push($data, 'web2erp');
        if ($res) {
            //入队成功，取消标记
            $this->setOrderSyn($info['order_id'], -1);
            return true;

        } else {
            return false;
        }
    }

    /**
     * 推送订单尾款进队列
     * @param  [type] $order_data [description]
     * @return [type]             [description]
     */
    public function pushAdvance($order_data) {
        extract($order_data);//解出$info  $lists  $invoice $user $pay
        foreach ($pay as $v) {
            if ($v['pay_type'] == 3) {
                $data = array(
                    'SETTYPE' => C('ERP_PAY_WAY.'. $v['pay_name']),
                    'SETDES' => '订单号：'.$info['order_sn'].'，流水号：'.$v['serial_number'].'，具体支付渠道：'.$v['pay_name'],
                    'NUMBER' => $info['order_sn'],
                );
            }
        }
        if (empty($data)) {
            return false;
        }
        $RBMQModel = D('Server/RBMQ');
        $res = $RBMQModel->queue('web2erp_advance')->push($data, 'web2erp_advance');
        if ($res) {
            //入队成功，取消标记
            $this->setOrderSyn($info['order_id'], -1);
            return true;

        } else {
            return false;
        }
    }

    public function report_data_to_sensors()
    {
        return "";
        $RBMQModel = D('Common/Rbmq');
        $rp_type = I('rp_type',1);
        if ($rp_type!=1)
        {//搜索用23服务器
            $RBMQModel = $RBMQModel->connect('RBMQ_MSG_CONFIG');
        }
        $RBMQModel->queue(C('QUEUE_REPORT_DATA'));
        $limit = 300;
        while ($limit-- > 0) {
            $ack = true;
            $data = $RBMQModel->pull(false);
            if ($data !== false) {
                $data = json_decode($data, true);


                //超长属性 丢弃掉非正常内容
                if(strlen($data['keyword'])>=8191){
                    continue;
                }

                //过滤掉key值为空的非法提交(正常的提交key不应为空)
                foreach ($data as $x => $y) {
                   if(empty($x)){
                    unset($data[$x]);
                   }
                }

                if (!empty($data)){
                    if (!empty($data['event'])){
                        foreach ($data as $k => &$v){
                            if (!is_scalar($v) && !is_array($v)) {
                                $v = '';
                            }
                        }
                        unset($v);
                        $event = $data['event'];
                        unset($data['event']);
                        $user_id = !empty($data['user_id'])?$data['user_id']:0;
                        unset($data['user_id']);
                        $is_login = true;//用于标识非登陆/注册情况下的 匿名id(未登录加购物车行为)
                        if(isset($data['is_login']) && !$data['is_login']){
                            $is_login = false;
                            unset($data['is_login']);
                        }
                        if (!empty($data['anonymous_id'])){
                            $anonymous_id = $data['anonymous_id'];
                            unset($data['anonymous_id']);
                            $this->sa->track_signup($user_id,$anonymous_id,$data);
                        }
                        $this->sa->track($user_id,$is_login,$event,$data);
                    }else{
                        $ack = false;
                    }
                }
//                if (!empty($data['NUMBER'])) {
//                    $res = $this->pushAdvance($data);
//                    if ($res === false) {
//                        $this->log();
//                        $ack = false;
//                    }
//                }
//                echo $data['NUMBER'], "\t", $res ,"\n";
                if ($ack) {
                    $RBMQModel->ack();
                }
            } else {
                echo "wait queue\n";
            }
        }
    }


    public function add_user_profile(){
        var_dump($this->sa->profile_set_once(70156,true,array('LoginEmail'=>'LoginEmail')));
        var_dump($this->sa->profile_set_once(70156,true,array('Phone'=>'Phone')));
    }

    public function test_task(){
        $RBMQModel = D('Common/Rbmq');
        $RBMQModel = $RBMQModel->connect('RBMQ_MSG_CONFIG');
        $RBMQModel->queue(C('MEMBER_TASK_SYSTEM_LIST'));
        $data_obj = $RBMQModel->push(array('user_id' => $_REQUEST['user_id'], 'pay_amount' => $_REQUEST['price'], 'task_type' => $_REQUEST['task_type']), C('MEMBER_TASK_SYSTEM_LIST'));
    }
    public function sql(){
        $udpate_data = array();
        $udpate_data['task_finish_num'] = 2;
        $udpate_data['update_time'] = 1561367757;
        $hh = M('task_system ')->where(array('user_id'=>$_REQUEST['user_id'],'task_type'=>2))->save($udpate_data);dump($hh);exit;
    }
    public function aa(){
        $rg = S('lie_user_cur_point..'.$_REQUEST['user_id'], '', array('prefix' => ''));dump($rg);exit;
        S('lie_user_cur_point..'.$_REQUEST['user_id'],$rg+200);
        $rg = S('lie_user_cur_point..'.$_REQUEST['user_id']);dump($rg);
    }
    /**
     * 任务体系，处理用户搜索、下单等信息
     */
    public function userTask(){
        $RBMQModel = D('Common/Rbmq');
        $RBMQModel = $RBMQModel->connect('RBMQ_MSG_CONFIG');
        $RBMQModel->queue(C('MEMBER_TASK_SYSTEM_LIST'));

        $data_obj = $RBMQModel->pull();

        while ($data_obj) {
            $data = json_decode($data_obj, true);dump($data);
            if (!empty($data) && $data['user_id']>0){
                $body = array();
                $body['user_id'] = $data['user_id'];
                $body['task_type'] = $data['task_type'];
                if($data['task_type']==2){
                    //搜索任务
                    $res = M('task_system')->field('user_id,task_type,task_finish_num,update_time')->where($body)->find();
                   
                    //添加用户首次搜索数据
                    if(!$res){
                        $body['task_finish_num'] = 1;
                        $body['create_time'] = time();
                        $body['update_time'] = time();
                        M('task_system')->add($body);
                    }

                    //上次搜索时间等于今天
                    if(date('Y-m-d',$res['update_time']) == date('Y-m-d',time())){
                        $flag = -1;
                    }else{
                        $flag = 1;
                    }


                    //更新用户搜索时间
                    if($res){
                        $save_s['update_time'] = time();
                        M('task_system')->where($body)->save($save_s);
                    }

                    //如果这个用户从来没有搜索，或者上次搜索的时间不是今天则送积分
                    if(!$res   || $flag == 1){
                        $user_info = M('user_main')->where(array('user_id'=>$data['user_id']))->field('email,mobile')->find();
                        $log = array();
                        $log['user_id'] = $data['user_id'];
                        $log['user_account'] = $user_info['mobile']?$user_info['mobile']:$user_info['email'];
                        $log['flow_type'] = 1;
                        $log['flow_point'] = 20;
                        $log['flow_time'] = time();
                        $log['flow_reason_type'] = 7;
                        M('point_log')->add($log);
                        //M('user_info')->where(array('user_id'=>$data['user_id']))->field('cur_user_point,cur_total_point,new_point')->find();
                        $udpate_data = array();
                        $udpate_data['cur_user_point'] = array('exp','cur_user_point+20');
                        $udpate_data['cur_total_point'] = array('exp','cur_total_point+20');
                        $udpate_data['new_point'] = array('exp','new_point+20');
                        $rg = S('lie_user_cur_point..'.$data['user_id'], '', array('prefix' => ''));
                        S('lie_user_cur_point..'.$data['user_id'],$rg+20, '', array('prefix' => ''));
                        M('user_info')->where(array('user_id'=>$data['user_id']))->save($udpate_data);
                        
                    }


                }else if($data['task_type']==3){
                    //订单相关任务
                    $body['task_type'] = array('in',array(3,4,5));
                    $res = M('task_system')->field('user_id,task_type,task_amount,task_finish_num,update_time')->where($body)->select();
                    $task_order_list = array();
                    if($res){
                        foreach ($res as $k=>$v){
                            $task_order_list[$v['task_type']] = $v;
                        }
                    }
                    //根据下单数确定是否完成任务
                    if(!isset($task_order_list[3])){
                        $body['task_type'] = 3;
                        $body['task_finish_num'] = 1;
                        $body['create_time'] = time();
                        $body['update_time'] = time();
                        M('task_system')->add($body);
                    }else{
                        if($task_order_list[3]['task_finish_num']<C('TASK_SYSTEM_NUM.3')){
                            $save_s['task_finish_num'] = $task_order_list[3]['task_finish_num']+1;
                            $save_s['update_time'] = time();
                            $body['task_type'] = 3;
                            M('task_system')->where($body)->save($save_s);
                        }
                    }
                    //判断是否是活动上线后注册的用户
                    $user_info = M('user_main')->where(array('user_id'=>$data['user_id']))->find();
                    if($user_info['create_time']<C('TASK_SYSTEM_START_TIME')){
                        $data_obj = $RBMQModel->pull();
                        continue;
                    }
                    //根据下单是否满足1000元算是否完成任务  pay_log
                    if(!isset($task_order_list[4])){
                        $body['task_type'] = 4;
                        $body['task_finish_num'] = 1;
                        $body['task_amount'] = $data['pay_amount'];
                        $body['create_time'] = time();
                        $body['update_time'] = time();
                        M('task_system')->add($body);
                    }else{
                        if($task_order_list[4]['task_amount']<C('TASK_SYSTEM_AMOUNT.4')){
                            $save_s['task_finish_num'] = $task_order_list[4]['task_finish_num']+1;
                            $save_s['task_amount'] = $task_order_list[4]['task_amount']+$data['pay_amount'];
                            $save_s['update_time'] = time();
                            $body['task_type'] = 4;
                            M('task_system')->where($body)->save($save_s);
                        }
                    }
                    
                    //根据下单是否满足5000元判断是否完成任务
                    if(!isset($task_order_list[5])){
                        $body['task_type'] = 5;
                        $body['task_finish_num'] = 1;
                        $body['task_amount'] = $data['pay_amount'];
                        $body['create_time'] = time();
                        $body['update_time'] = time();
                        M('task_system')->add($body);
                    }else{
                        if($task_order_list[5]['task_amount']<C('TASK_SYSTEM_AMOUNT.5') || $task_order_list[5]['task_finish_num']<C('TASK_SYSTEM_NUM.5')){
                            $save_s['task_finish_num'] = $task_order_list[5]['task_finish_num']+1;
                            $save_s['task_amount'] = $task_order_list[5]['task_amount']+$data['pay_amount'];
                            $save_s['update_time'] = time();
                            $body['task_type'] = 5;
                            M('task_system')->where($body)->save($save_s);
                        }
                    }
                }
            }

            //循环拿数据
            $data_obj = $RBMQModel->pull();
        }
        
    }

}