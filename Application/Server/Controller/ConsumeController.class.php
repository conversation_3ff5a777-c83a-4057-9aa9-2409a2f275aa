<?php
namespace Server\Controller;

use Server\Controller\BaseController;

class ConsumeController extends BaseController
{
    public $log = '';

    public function _initialize()
    {
        //登录
        try {
            //ini_set('soap.wsdl_cache_enabled', '0');
            //libxml_disable_entity_loader(false);
            $soap = new \SoapClient(ERP_DOMAIN.'/ormrpc/services/EASLogin?wsdl');
            $res = $soap->login(C('ERP_LOGIN_NAME'), '123456', 'eas', C('ERP_DB_NAME'),  'L2', 1, 'BaseDB');           
        } catch (Exception $e) {
            echo $e->getMessage();die;
        }

        //接口
        $this->erp = new \SoapClient(ERP_DOMAIN.'/ormrpc/services/WSIchuntjKFacade?wsdl');     
    }

    public function exchangeRate()
    {
        $currency = I('currency');
        $time = I('time');
        empty($time) && $time = time();
        $time = date('Y-m-d', $time);
        $currency = C('HLCURRENCY.'. $currency);
        empty($currency) && $currency = C('HLCURRENCY.1');
        $data = array(
            'CURRENCY' => $currency,
            'BIZDATE' => $time,
        );
        $res = $this->erp->getExchangeRate(json_encode($data));
        echo $res;
    }

    /**
     * 完整推送订单(物料、客户、订单)
     * @return [type] [description]
     */
    public function pushErpOrder()
    {
        $RBMQModel = D('Server/RBMQ');
        $limit = 5;
        while ($limit-- > 0) {
            $ack = true;
            $data = $RBMQModel->queue('web2erp')->pull(false);
            if ($data !== false) {
                $data = json_decode($data, true);
                if (!empty($data['invoice'])) {
                    if (empty($data['invoice']['FTXREGISTERNO'])) {
                        unset($data['invoice']['FTXREGISTERNO']);
                    }
                    $res = $this->pushUser($data['invoice']);
                    if ($res === false) {
                        $this->log();
                        $ack = false;
                    }
                }
                if (!empty($data['info']['ENTRYS'])) {
                    foreach ($data['info']['ENTRYS'] as &$v) {
                        $v['MATERIAL'] = trim($v['MATERIAL']);
                        $res = $this->pushGoods($v);
                        if ($res === false) {
                            $this->log();
                            $ack = false;
                        }
                    }
                }
                if (!empty($data['info']['NUMBER'])) {
                    $res = $this->pushOrder($data['info']);
                    if ($res === false) {
                        $this->log();
                        $ack = false;
                    }
                }
                echo $data['info']['NUMBER'], "\t", $res ,"\n";
                $RBMQModel->ack();
                if (!$res) {
                    $RBMQModel->queue('web2erp_error')->push($data);
                }
            } else {
                echo "wait queue\n";
            }
        }
    }

    /**
     * 推送生成尾款收款单
     * @return [type] [description]
     */
    public function pushErpAdvance()
    {
        $RBMQModel = D('Server/RBMQ');
        $RBMQModel->queue('web2erp_advance');
        $limit = 5;
        while ($limit-- > 0) {
            $ack = true;
            $data = $RBMQModel->pull(false);
            if ($data !== false) {
                $data = json_decode($data, true);
                if (!empty($data['NUMBER'])) {
                    $res = $this->pushAdvance($data);
                    if ($res === false) {
                        $this->log();
                        $ack = false;
                    }
                }
                echo $data['NUMBER'], "\t", $res ,"\n";
                if ($ack) {
                    $RBMQModel->ack();
                }
            } else {
                echo "wait queue\n";
            }
        }
    }

    /**
     * 推送订单
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    public function pushOrder($data)
    {
        try {
            $res = $this->erp->createJKorder(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            echo $e->getMessage();
            return false;
        }
        $arr = json_decode($res, true);
        if (empty($arr) || !isset($arr['0000'])) {
            $this->log = __METHOD__."\t".$res;
            return false;
        }
        //标记明细ERPid
        $mark_res = $this->setErpItemsId($arr['ENTRYS']);
        if ($mark_res['err_code'] != 0) {
            $this->log = __METHOD__."\t".$mark_res['msg'];
            return false;
        }

        //标记ERPid
        $mark_res = $this->setErpId($data['NUMBER'], $arr['0000']);
        if ($mark_res['err_code'] != 0) {
            $this->log = __METHOD__."\t".$mark_res['msg'];
            return false;
        }
        return $res;
    }

    /**
     * 推送客户
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    public function pushUser($data)
    {
        try {
            $res = $this->erp->createJKcustomer(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            // echo $e->getMessage();
            return false;    
        }
        $arr = json_decode($res, true);
        if (empty($arr) || !isset($arr['0000'])) {
            $this->log = __METHOD__."\t".$res;
            return false;
        }
        return $res;
    }

    /**
     * 推送物料
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    public function pushGoods($data)
    {
        try {
            $res = $this->erp->createJKmaterial(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            echo $e->getMessage();
            return false;    
        }
        $arr = json_decode($res, true);
        if (empty($arr) || !isset($arr['0000'])) {
            $this->log = __METHOD__."\t".$res;
            return false;
        }
        return $res;
    }

    /**
     * 推送尾款付款
     * @param  [type] $data [description]
     * @return [type]       [description]
     */
    public function pushAdvance($data)
    {
        try {
            $res = $this->erp->createReceivingBill(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            echo $e->getMessage();
            return false;    
        }
        $arr = json_decode($res, true);
        if (empty($arr) || !isset($arr['0000'])) {
            $this->log = __METHOD__."\t".$res;
            return false;
        }
        return $res;
    }

    // 请求ERP账期订单
    public function isAccountOrder($data)
    {
        try {
            $res = $this->erp->getCreditAmount(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            return 'ERP接口异常，请联系技术查看日志';    
        }
        $arr = json_decode($res, true);
        if (empty($arr) || !isset($arr['0000'])) {
            $this->log = __METHOD__."\t".$res;
            $this->log();
            // return false;
            return $res;
        }
        return true;
    }

    // 判断客户与业务员是否绑定
    public function isBindSalesAndCustomer($data)
    {
        try {
            $res = $this->erp->getRelationSaleANDCustomer(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            return ['code' => 1, 'msg' => 'ERP接口异常，请联系技术查看日志'];     
        }
        $arr = json_decode($res, true);
        if (empty($arr) || !isset($arr['0000'])) {
            $this->log = __METHOD__."\t".'请求参数：'. json_encode($data).'，ERP返回：'.$res;
            $this->log();
            return ['code' => 2, 'msg' => $res];
        }
        return ['code' => 0, 'msg' => ''];
    }

    // 订单后台---账期或付款为0的退款，直接删除ERP订单
    public function pushDeleteOrder($data)
    {
        try {
            $res = $this->erp->deleteOrder(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            echo $e->getMessage();
            return false;    
        }
        $arr = json_decode($res, true);
        if (empty($arr) || $arr['code'] != 0) {
            $this->log = __METHOD__."\t".$res;
            $this->log();
            return false;
        }
        return $res;
    }

    // 订单后台---退款申请
    public function pushOrderRefundApply($data)
    {
        try {
            $res = $this->erp->createRefund(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            echo $e->getMessage();
            return false;    
        }
        $arr = json_decode($res, true);
        if (empty($arr) || !isset($arr['0000'])) {
            $this->log = __METHOD__."\t".$res;
            $this->log();
            return false;
        }
        return $res;
    }

    // 支付记录接收支付时间
    public function getPayLogTime($data)
    {
        $this->erp = new \SoapClient(ERP_DOMAIN.'/ormrpc/services/WSichuntToPTJKFacade?wsdl');

        try {
            $res = $this->erp->synUpdateHistoryReceipt(json_encode($data));

        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            echo $e->getMessage();
            return false;    
        }
        
        return json_decode($res, true);
    }

    // 发送采购邮件后，同步采购员、备注、批次
    public function sysOrderItems($data)
    {
        try {
            $res = $this->erp->writeOrderEntry(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            return $e->getMessage();   
        }
        $arr = json_decode($res, true);
        if (empty($arr) || !isset($arr['0000'])) {
            $this->log = __METHOD__."\t".$res;
            $this->log();
            return $res;
        }
        return true;
    }

    // 检查ERP订单是否能删除
    public function pushDeleteOrderStatus($data)
    {
        try {
            $res = $this->erp->deleteOrderStatus(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            echo $e->getMessage();
            return false;    
        }
        $arr = json_decode($res, true);
        if (empty($arr) || !isset($arr['0000'])) {
            $this->log = __METHOD__."\t".$res;
            $this->log();
            return false;
        }
        return $res;
    }

    // 获取公司账期信息 --- 用于CRM系统
    public function getErpCompanyCredit($data)
    {
        try {
            $res = $this->erp->getCustomerCreditFile(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            echo $e->getMessage();
            return false;    
        }
        $arr = json_decode($res, true);
        if (empty($arr) || isset($arr['4444'])) {
            $this->log = __METHOD__."\t".$res;
            $this->log();
            return false;
        }
        return $res;
    }

    // 推送售后明细
    public function pushOrderServiceItems($data)
    {
        try {
            $res = $this->erp->updateOrderStatus(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            return $e->getMessage();    
        }
        $arr = json_decode($res, true);
        if (empty($arr) || !isset($arr['0000'])) {
            $this->log = __METHOD__."\t".$res;
            $this->log();
            return $res;
        }
        return true;
    }

    // 推送收货地址
    public function pushOrderAddress($data)
    {
        try {
            $res = $this->erp->synPTAddressThin(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            return $e->getMessage();    
        }
        $arr = json_decode($res, true);
        if ($arr['returnCode'] == '4444') {
            return $arr['returnMsg'];
        }
        return true;
    }

    // 更新ERP明细 {"msg":"变更后的单价不能比已收款的价格低！","code":4444}
    public function updateErpItems($data)
    {
        try {
            $res = $this->erp->updateOrderStatus(json_encode($data));
        } catch (\Exception $e) {
            $this->log = __METHOD__ . "\t" . $e->getMessage();
            $this->log();
            return $e->getMessage();
        }
        return json_decode($res, true);
    }

    /**
     * 日志记录
     * @return [type] [description]
     */
    public function log()
    {
        static $Log = '';
        if (empty($Log)) {
            $Log = new \Think\Log();
        }
        if (!empty($this->log)) {
            $Log->write($this->log);
            $this->log = '';
        }
    }

    /*
     * 获取采购下单状态接口
     */
    public function getPurOrderStatusByCustomer(){
        try {
            $data['customerName'] = I("customerName","","trim");
            $data['bizDate'] = I("bizDate","","trim");
            $_res = $this->erp->getPurOrderStatusByCustomer(json_encode($data));
            $res = json_decode($_res,true);
            if(isset($res["returnCode"]) && $res["returnCode"] == "0000"){
                return $this->ajaxReturn(["err_code"=>0,"err_msg"=>$res["returnMsg"]]);
            }else{
                return $this->ajaxReturn(["err_code"=>-1,"err_msg"=>$res["returnMsg"]]);
            }
        } catch (\Exception $e) {
            $this->log = __METHOD__."\t".$e->getMessage();
            $this->log();
            return $this->ajaxReturn(["err_code"=>-1,"err_msg"=>$_res]);
        }
    }

    // 获取ERP客户数量 - 线下
    public function getNewCustomers()
    {
        $beginDate   = I('beginDate', '');
        $endDate     = I('endDate', '');
        $salePersons = I('salePersons', '');
        $type        = I('type', 1);

        $data = [];
        $data['beginDate']   = $beginDate;
        $data['endDate']     = $endDate;
        $data['salePersons'] = $salePersons;

        try {
            if ($type == 1) {
                $res = $this->erp->caculateNewCustomers(json_encode($data)); // 获取ERP新增客户数量
            } else {
                $res = $this->erp->caculateNewOrderCustomers(json_encode($data)); // 获取ERP新下单客户数量
            }
        } catch (\Exception $e) {
            return $this->apiReturn(-1, $e->getMessage());    
        }

        $arr = json_decode($res, true);

        if (isset($arr['returnCode']) && $arr['returnCode'] == '0000') return $this->apiReturn(0, '', $arr['data']);

        return $this->apiReturn(-1, 'ERP接口请求失败'); 
    }

}