<?php
namespace Server\Controller;

use Server\Controller\BaseHproseController;

class ServerController extends BaseHproseController
{
    // protected $debug = true;
    protected $alllowMethodList = array('auth', 'createUser', 'createOrder');

    const rediskey = 'erp_login';


    private function check()
    {
        return S(self::rediskey);
    }

    /**
     * 校验登录
     * @param  [type] $args [description]
     * @return [type]       [description]
     */
    public function auth($args)
    {
        $datas = json_decode($args, true);
        $username = $datas['user'];
        $password = $datas['pwd'];
        if (empty($username) || empty($password)) {
            return $this->apiReturn(1, '账号或密码不能为空');
        }
        $client = C('ERP_CLIENT.'.$username);
        if ($client != $password) {
            return $this->apiReturn(2, '密码错误');
        }
        S(self::rediskey, $username, array('expire' => 3600*3));
        return $this->apiReturn(0, '登录成功', S(self::rediskey));
    }

    /**
     * 创建用户发票
     * @param  [type] $user_id [description]
     * @param  [type] $args    [description]
     * @return [type]          [description]
     */
    public function createUserInvoice($user_id, $args)
    {
        $check = $this->check();
        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }
        $datas = json_decode($args, true);

        $invoice = $this->getUserInvoice($user_id, $datas['CUSTOMER']);
        if ($invoice['err_code'] != 0) {
            $data = array(
                'user_id' => $user_id,
                'tax_title' => $datas['CUSTOMER'],
                'company_address' => $datas['FADDRESS'],
                'company_phone' => $datas['FPHONE'],
                'tax_no' => $datas['FTXREGISTERNO'],
                'bank_name' => $datas['FBANK'],
                'bank_account' => $datas['FBANKACCOUNT'],
                'is_default' => 0,
            );
            $invoice = $this->setUserInvoice($data);
            if ($invoice['err_code'] != 0) {
                return $this->apiReturn($invoice['err_code'], $invoice['err_msg']);
            }
        }
        $tax_id = strval($invoice['data']['tax_id']);
        return $this->apiReturn(0, '创建成功', $tax_id);
    }

    /**
     * 创建管理人员（销售或采购）
     * @return [type] [description]
     */
    public function createManageUser($args)
    {
        $check = $this->check();
        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }
        $datas = json_decode($args, true);
        if (empty($datas['SALESPERSONNO'])) {
            return $this->apiReturn(100002, '销售编码不能为空', $datas);
        }
        $mobile = trim($datas['CUSLINKMANPHONE']);//客户手机做关联用
        $email = trim($datas['CUSLINKMANEMAIL']);//客户邮箱做关联用
        $ErpManageUserModel = D('ErpManageUser');
        $type = !empty($datas['TYPE']) ? $datas['TYPE'] : 1;
        $map = array(
            'erp_user_code' => $datas['SALESPERSONNO'],
            'user_type' => $type,
        );
        $manage = $ErpManageUserModel->where($map)->find();
        if (empty($manage)) {
            $data = array(
                'erp_user_code' => $datas['SALESPERSONNO'],
                'erp_user_name' => $datas['SALESPERSON'],
                'user_type' => $type,
                'status' => 1,
            );
            $res = $ErpManageUserModel->add($data);
            if ($res === false) {
                return $this->apiReturn(100001, '创建管理人员失败', $data);
            }
            $sale_id = strval($res);
        } else {
            $sale_id = strval($manage['erp_manage_uid']);
            //关联用户销售员
            if (!empty($mobile) || !empty($email)) {
                if (!empty($mobile)) {
                    $user = $this->getMobileUser($mobile);
                } else {
                    $user = $this->getEmailUser($email);
                }

                if ($user['err_code'] != 0) {
                    return $this->apiReturn($user['err_code'], $user['err_msg']);
                } 
                $res = $this->changeUser($user['data'], $sale_id);
                if ($res['err_code'] != 0) {
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
            }
        }
        return $this->apiReturn(0, '成功', $sale_id);
    }

    /**
     * 创建手机账户
     * @param  [type]  $args      [description]
     * @param  integer $saler_uid [description]
     * @return [type]             [description]
     */
    public function createUserMobile($args)
    {
        $check = $this->check();
        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }

        $datas = json_decode($args, true);
        $company_name = $datas['CUSTOMER'];
        $password = '';
        $code = $datas['CUSTOMERID'];
        $mobile = trim($datas['CUSLINKMANPHONE']);
        $email = trim($datas['CUSLINKMANEMAIL']);
        //查询企业主账号
        // $company = $this->getCompanyUser($company_name);
        // if ($company['err_code'] != 0) {
        //     //创建企业主账号
        //     empty($password) && $password = C('ERP_PASSWORD_INIT');//初始化密码
        //     $res = $this->setUser($company_name, $password, $code);
        //     if ($res['err_code'] != 0) {
        //         return $this->apiReturn($res['err_code'], $res['err_msg']);
        //     }
        //     $parent_id = $res['data'];
        // } else {
        //     $parent_id = $company['data'];
        //     //绑定ERPID
        //     try {
        //         $this->bingErpUser($code);
        //     } catch (Exception $e) {
        //     }
        // }
        if (!empty($mobile)) {
            //查询手机是否有账号
            $user = $this->getMobileUser($mobile);
            if ($user['err_code'] != 0) {
                // 创建账号
                $password = substr($mobile, -6, 6);//初始化密码手机号后6位
                $res = $this->setMobileUser($mobile, $password);
                if ($res['err_code'] != 0) {
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
                $user_id = strval($res['data']);
            } else {
                $user_id = strval($user['data']);
            }
        } elseif (!empty($email)) {
            $user = $this->getEmailUser($email);
            if ($user['err_code'] != 0) {
                // 创建账号
                $password = C('ERP_PASSWORD_INIT');
                $res = $this->setEmailUser($email, $password);
                if ($res['err_code'] != 0) {
                    return $this->apiReturn($res['err_code'], $res['err_msg']);
                }
                $user_id = strval($res['data']);
            } else {
                $user_id = strval($user['data']);
            }
        }

        //绑定ERPID
        try {
            $this->bingErpUser($user_id, $code);
        } catch (Exception $e) {
        }

        if ($datas['FTXREGISTERNO']) {
            $this->createUserInvoice($user_id, $args);
        }
        return $this->apiReturn(0, '注册成功', $user_id);
    }

    /**
     * 创建用户
     * @param  [type] $args [description]
     * @return [type]       [description]
     */
    public function createUser($args)
    {
        $check = $this->check();
        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }
        $datas = json_decode($args, true);

        $user = $datas['CUSTOMER'];
        $password = '';
        $code = $datas['CUSTOMERID'];

        //查询网站是否有用户
        $erpuser = $this->getErpUser($code);
        if ($erpuser['err_code'] != 0) {
            //查询公司名是否有账号
            $company = $this->getCompanyUser($user);
            if ($company['err_code'] != 0) {
                empty($password) && $password = C('ERP_PASSWORD_INIT');//初始化密码
                $res = $this->setUser($user, $password, $code);
                if ($res['err_code'] != 0) {
                    return $this->apireturn($res['err_code'], $res['err_msg']);
                }
                $user_id = strval($res['data']);
            } else {
                //绑定ERPID
                try {
                    $this->bingErpUser($code);
                } catch (Exception $e) {
                }
                $user_id = strval($company['data']);
            }
        } else {
            $user_id = strval($erpuser['data']);
        }
        if ($datas['FTXREGISTERNO']) {
            $this->createUserInvoice($user_id, $args);
        }
        return $this->apiReturn(0, '注册成功', $user_id);
    }

    /**
     * 创建订单
     * @param  [type] $args [description]
     * @return [type]       [description]
     */
    public function createOrder($args)
    {
        $check = $this->check();
        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }
        //创建用户
        // $user = json_decode($this->createUserMobile($args), true);
        // if ($user['err_code'] != 0) {
        //     return $this->apiReturn($info['err_code'], $info['err_msg']);
        // }
        //创建销售员
        // $saler = json_decode($this->createManageUser($args), true);
        // if ($saler['err_code'] != 0) {
        //     return $this->apiReturn($info['err_code'], $info['err_msg']);
        // }
        $datas = json_decode($args, true);
        // if (!empty($datas['CUSLINKMANPHONE'])) {
        //     $user_info = $this->getMobileUser($datas['CUSLINKMANPHONE'], 1);
        //     if ($user_info['err_code'] != 0) {
        //         return $this->apiReturn($user_info['err_code'], $user_info['err_msg']);
        //     }
        // } elseif (!empty($datas['CUSLINKMANEMAIL'])) {
        //     $user_info = $this->getEmailUser($datas['CUSLINKMANEMAIL'], 1);
        //     if ($user_info['err_code'] != 0) {
        //         return $this->apiReturn($user_info['err_code'], $user_info['err_msg']);
        //     }
        // } else {
        //     return $this->apiReturn(-1, '暂不支持固话');
        // }
        $sale_uid = 0;//$user_info['data']['sale_user_id'];
        $user_id = 0;//$user['data'];
        $info = $this->getOrder($datas['FNUMBER'], $user_id);
        if ($info['err_code'] != 0) {
            //创建订单
            $data = array(
                'order_sn' => $datas['FNUMBER'],
                'order_type' => C('ERP_ORDER_SOURCE.' . $datas['SOURCE']),
                'order_pay_type' => 1,
                'user_id' => $user_id,
                'order_amount' => $datas['FTOTALAMOUNT'],
                'currency' => C('ERP_CURRENCY.' . $datas['CURRENCY']),
                'delivery_place' => C('ERP_DELIVERY.' . $datas['CURRENCY']),
                'order_shipping_type' => C('ERP_SHIPPING_TYPE.' . $datas['DELIVERTYPE']),
                'status' => C('ERP_ORDER_STATUS.' . $datas['BASESTATUS']),
                'create_time' => strtotime($datas['BIZDATE']),
                'confirm_time' => strtotime($datas['AUDITTIME']),
                'pay_time' => strtotime($datas['PAYMENTDATE']),
                'finish_time' => strtotime($datas['COMPLETEDATE']),
                'erp_sale_uid' => $sale_uid,
            );
            //补充完成时间
            if (empty($data['finish_time']) && empty($data['pay_time']) && empty($data['confirm_time'])) {
                $data['finish_time'] = $data['create_time'];
                $data['confirm_time'] = 0;
                $data['pay_time'] = 0;
            } elseif (empty($data['finish_time']) && empty($data['pay_time'])) {
                $data['finish_time'] = $data['confirm_time'];
                $data['pay_time'] = 0;
            } elseif (empty($data['finish_time'])) {
                $data['finish_time'] = $data['pay_time'];
            }

            $order = $this->setOrder($data);
            if ($order['err_code'] != 0) {
                return $this->apiReturn($order['err_code'], $order['err_msg']);
            }
            $order_id = strval($order['data']);
        } else {
            $order_id = strval($info['data']['order_id']);
        }
        //创建订单明细
        $data = array(
            'order_id' => $order_id,
            'user_id' => $user_id,
            'erp_rec_id' => $datas['MATERIALID'],
            'goods_name' => $datas['MATERIAL'],
            'brand_name' => $datas['FMODEL'],
            'goods_price' => $datas['FTAXPRICE'],
            'goods_number' => $datas['FQTY'],
        );
        $goods = $this->setOrderItems($data);
        if ($goods['err_code'] != 0) {
            return $this->apiReturn($goods['err_code'], $goods['err_msg']);
        }
        return $this->apiReturn(0, '创建成功', $order_id);
    }

    /**
     * 同步发货
     * @param  [type] $args [description]
     * @return [type]       [description]
     */
    public function shipping($args)
    {
        $check = $this->check();
        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }
        $datas = json_decode($args, true);

        $list = array();
        foreach ($datas['ENTRYS'] as $v) {
            $list[] = array(
                'erp_rec_id' => $v['MATERIALID'],
                'num' => $v['FQTY'],
            );
        }
        $data = array(
            'order_sn' => $datas['NUMBER'],
            'erp_removal_id' => $datas['SALELSSUEID'],
            'order_shipping_type' => C('ERP_SHIPPING_TYPE.' . $datas['DELIVERTYPE']),
            'shipping_no' => $datas['LOGISTICSNUMBER'],
            'shipping_name' => $datas['CARRIER'],
            'shipping_type' => 1,
            'list' => $list,
        );
        $res = $this->synShipping($data);
        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        return $this->apiReturn(0, '同步发货成功', $res['data']);
    }


    /**
     * 获取完整订单列表
     * @param  [type] $args [description]
     * @return [type]       [description]
     */
    public function getOrderList($args)
    {
        $check = $this->check();
        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }
        $datas = json_decode($args, true);
        $s = date('Y-m-d H:i:s', substr($datas['StartDate'], 0, -3));
        $e = date('Y-m-d H:i:s', substr($datas['endDate'], 0, -3));
        $is_type = isset($datas['isType']) ? $datas['isType'] : '';
        $res = $this->getAllOrder($s, $e, $is_type);
        foreach ($res['data'] as &$v) {
            $v['create_time'] = date('Y-m-d', $v['create_time']);
            foreach ($v['list'] as $item) {
                if ($item['goods_number'] <= 0) {
                    $item['goods_number'] = 1;
                } else {
                    $item['goods_number'] = intval($item['goods_number']);
                }
                if ($item['goods_price'] <= 0) {
                    $item['goods_price'] = 0.1;
                }
                $v['items'][] = array(
                    'goods_name' => strval($item['goods_name']),
                    'brand_name' => strval($item['brand_name']),
                    'number' => strval($item['goods_number']),
                    'price' => strval($item['goods_price']),
                    'picking_price ' => strval($item['picking_price']),
                );
            }
            unset($v['list']);
        }
        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }
        return $this->apiReturn(0, '获取成功', $res['data']);
    }

    // 导入ERP用户
    public function getUserFromErp($args) 
    {
        $check = $this->check();

        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }

        $datas = json_decode($args, true);

        $res = $this->setErpUser($datas);

        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        return $this->apiReturn(0, '导入成功', $res['data']);
    }

    // 获取订单退款申请处理结果
    public function getOrderRefundResult($args) 
    {
        $check = $this->check();

        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }

        $datas = json_decode($args, true);

        $res = $this->setOrderRefundResult($datas);

        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        return $this->apiReturn(0, '更新数据成功', $res['data']);
    }

    /*
        测试erp完成订单推送
    */
    // public function test()
    // {
    //     exit;
    //     $args = ["SALENUMBER"=> "12019072426868", "RECLASTTIME"=> time()];

    //     var_dump($this->getPaidLogFromErp(json_encode($args)));
    // }

    // ERP对账---新增支付记录
    public function getPaidLogFromErp($args) 
    {
        $check = $this->check();

        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }

        $datas = json_decode($args, true);

        $res = $this->setPaidLog($datas);

        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        return $this->apiReturn(0, '新增支付记录成功');
    }

    // ERP对账---支付取消
    public function getPaidCancelledFromErp($args) 
    {
        $check = $this->check();

        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }

        $datas = json_decode($args, true);

        $res = $this->delPaidLog($datas);

        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        return $this->apiReturn(0, '删除支付记录成功');
    }

    public function test()
    {
        // $args = '{"ENTRYS":[{"BATCH":"","FPRICE":"20.1","SKUID":"11030","FQTY":"1","ENTRYDES":"","ENTRYID":"T5bweB0oRSeV6YaYNPPERYiIKlg="}],"ERPNUMBER":"ZGJ-SZ202110120006","PTNUMBER":"12021101220714","DES":"优惠金额：0，附加费：0 。","DELIVERY_TYPE":"1"}';

        // p($this->createOfflineOrder($args));

        $args = '{"JKNUMBER": "12022042558664"}';
        p($this->billPay($args));
    }

    // 新增收款记录
    public function createReceiptLog($args) 
    {
        $check = $this->check();

        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }

        $datas = json_decode($args, true);

        $res = $this->setReceiptLog($datas);

        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        return $this->apiReturn(0, '新增收款记录成功');
    }

    // 取消收款记录
    public function cancelReceiptLog($args) 
    {
        $check = $this->check();

        if (!$check) {
            return $this->apiReturn(3, '未登录');
        }

        $datas = json_decode($args, true);

        $res = $this->delReceiptLog($datas);

        if ($res['err_code'] != 0) {
            return $this->apiReturn($res['err_code'], $res['err_msg']);
        }

        return $this->apiReturn(0, '取消收款记录成功');
    }

    // 接口日志记录
    public function apiRecord($info)
    {
        $path = C('LOG_PATH').ACTION_NAME.'/'.date('y_m_d').'.log'; // 接口日志文件
        \Think\Log::write($info, INFO, '', $path);
    }

    // ERP生成自营内部采购订单
    public function createOfflineOrder($args)
    {
        $check = $this->check();

        if (!$check) return $this->apiReturn(3, '未登录');

        $this->apiRecord('ERP生成自营内部采购订单，参数：'.$args); // 记录接口访问信息

        $datas = json_decode($args, true);

        if (!$datas && empty($datas['ENTRYS'])) return $this->apiReturn(103001, '无订单明细');

        // 判断自营订单中ERP订单号是否存在，若存在则不再生成订单
        $OrderModel         = D('Order/Order');
        $OrderItemsModel    = D('Order/OrderItems');
        $OrderItemsExtModel = D('Order/OrderItemsExt');
        $OrderExtendModel   = D('Order/OrderExtend');

        $map = [];
        $map['o.order_goods_type'] = 2;
        $map['o.status']           = ['neq', -1];
        $map['oe.erp_sn']          = $datas['ERPNUMBER'];

        $old_order_sn = $OrderModel->alias('o')
                       ->join('LEFT JOIN __ORDER_EXTEND__ oe ON o.order_id = oe.order_id')
                       ->where($map)
                       ->getField('o.order_sn');

        if ($old_order_sn) return $this->apiReturn(103001, 'ERP订单号已存在，平台订单号：'.$old_order_sn);

        $posts = [];
        $posts['erp_order_sn'] = $datas['ERPNUMBER']; 
        $posts['order_remark'] = $datas['DES'];

        $push_product_data = []; // 推送生产跟踪信息

        $order_info     = $OrderModel->getInfo('', $datas['PTNUMBER']);
        $joint_order_id = $order_info['order_id'];  

        // 获取联营商品明细
        $joint_order_items_ext = $OrderItemsExtModel->where(['order_id' => $joint_order_id])->getField('rec_id, batch');

        foreach ($datas['ENTRYS'] as $k=>$v) {
            $data = [];
            $data['id'] = $v['SKUID'];
            $data = array_merge($data, authkey());

            $res  = json_decode(post_curl(API_DOMAIN.'/goods/info', $data), true);

            if (!$res) return $this->apiReturn(103002, '未获取到商品信息');
            if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']);
            
            // 超过现有库存则阻止下单
            if ($res['data']['goods_number'] < $v['FQTY']) return $this->apiReturn(103003, 'SKUID：'.$v['SKUID'].'已超过现有库存，当前库存：'.$res['data']['goods_number']);

            $posts['items'][$k]['goods_id']     = $v['SKUID'];
            $posts['items'][$k]['goods_number'] = $v['FQTY'];
            $posts['items'][$k]['goods_price']  = $v['FPRICE'];
            $posts['items'][$k]['erp_rec_id']   = $v['ENTRYID'];
            $posts['items'][$k]['remarks']      = $v['ENTRYDES'];

            if (!empty($v['BATCH'])) {
                $posts['items'][$k]['batch'] = $v['BATCH'];
            } else {
                $rec_id = $OrderItemsModel->where(['order_id' => $joint_order_id, 'goods_id' => $v['SKUID']])->getField('rec_id');

                $posts['items'][$k]['batch'] = $joint_order_items_ext[$rec_id];
            }

            $temp = [];
            $temp['RBMQTYPE']     = 'SCZZ';
            $temp['BIZDATE']      = date('Y-m-d H:i:s', time());
            $temp['MSG']          = '已下单自营';
            $temp['CREATOR']      = 'ERP系统生成';
            $temp['ORDERENTRYID'] = $v['ENTRYID'];
            $temp['ORDERID']      = $joint_order_id;
            $temp['STATUS']       = 41; // 生产跟踪状态

            $push_product_data[] = $temp;
        }

        $delivery_type = isset($datas['DELIVERY_TYPE']) ? $datas['DELIVERY_TYPE'] : 1; // 发货方式

        if ($delivery_type == 1) { // 现货发货创建的自营通知单，账户为原客户账户，公司和原客户地址
            // $joint_order_id = $OrderExtendModel->where(['erp_sn' => $datas['ERPNUMBER']])->getField('order_id');
            // $order_info     = $OrderModel->getInfo($joint_order_id);   
      
            $user_id = $order_info['user_id'];

            $user_address         = D('Order/OrderAddress')->getInfo($joint_order_id); // 获取用户订单地址
            $user_invoice_address = D('Order/OrderAddress')->getInfo($joint_order_id, 2); // 获取用户订单发票地址
            $order_invoice        = D('Order/OrderInvoice')->getInfo($joint_order_id); // 获取订单发票

            $posts['sale_id'] = $order_info['sale_id'];
        } else { // 拼单发货创建的自营通知单，账户为***********，猎芯公司以及星火仓的地址
            $user_id = S_account(C('OFFLINE_ACCOUNT'));
            if (!$user_id) $user_id = D('Home/UserMain')->isHasAccount(C('OFFLINE_ACCOUNT'));

            $user_address = D('Address/UserAddress')->getDefaultInfo($user_id); // 获取用户默认地址

            $posts['sale_id'] = 1000; // admin
        }

        if (!$user_id) return $this->apireturn(103006, '未获取到用户ID');

        $posts['currency']            = 1; 
        $posts['user_id']             = $user_id;
        $posts['order_type']          = 1; 
        $posts['sale_type']           = 1; 
        $posts['order_goods_type']    = 2; 
        $posts['order_pay_type']      = 1; 
        $posts['delivery_place']      = 1; 
        $posts['order_shipping_type'] = 1; 
        $posts['status']              = 2; 
        $posts['client_ip']           = get_client_ip(0, true); 
        $posts['type']                = 3; // 自营内部采购
        
        // 订单地址
        $posts['address_id'] = $user_address['address_id'];
        $posts['province']   = $user_address['province'];
        $posts['city']       = $user_address['city'];
        $posts['district']   = $user_address['district'];
        $posts['address']    = $delivery_type == 1 ? $user_address['address'] : $user_address['detail_address'];
        $posts['consignee']  = $user_address['consignee'];
        $posts['mobile']     = $user_address['mobile'];

        // 发票地址
        if (isset($user_invoice_address)) {
            $posts['inv_province']  = $user_invoice_address['province'];
            $posts['inv_city']      = $user_invoice_address['city'];
            $posts['inv_district']  = $user_invoice_address['district'];
            $posts['inv_address']   = $user_invoice_address['address'];
            $posts['inv_consignee'] = $user_invoice_address['consignee'];
            $posts['inv_mobile']    = $user_invoice_address['mobile'];
        }

        // 订单发票信息
        if (isset($order_invoice) && $order_invoice['inv_type'] != 1) {
            $posts['inv_type']        = $order_invoice['inv_type'];
            $posts['tax_id']          = $order_invoice['tax_id'];
            $posts['nick_name']       = $order_invoice['nick_name'];
            $posts['tax_title']       = $order_invoice['tax_title'];
            $posts['company_address'] = $order_invoice['company_address'];
            $posts['company_phone']   = $order_invoice['company_phone'];
            $posts['tax_no']          = $order_invoice['tax_no'];
            $posts['bank_name']       = $order_invoice['bank_name'];
            $posts['bank_account']    = $order_invoice['bank_account'];
            $posts['invoice_status']  = $order_invoice['invoice_status'];
            $posts['fms_syn']         = 1; // 同步发票到财务系统
        }

        // $res = post_curl(ORDERAPI_DOMAIN.'/order/create', $posts);
        $client = new \GuzzleHttp\Client();
        $res = $client->request('POST', ORDERAPI_DOMAIN.'/order/create', ['form_params' => $posts]);
        $res = $res->getBody()->getContents();

        if (!$res) {
            dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.order'), 'ERP生成自营内部采购订单告警，订单服务返回信息：'.$res);
        }

        $res = json_decode($res, true);

        if ($res['errcode'] != 0) return $this->apiReturn($res['errcode'], $res['errmsg']);

        $order_id     = $res['data']['order_id'];
        $order_sn     = $res['data']['order_sn'];
        $order_amount = $res['data']['order_amount'];

        if (!$order_id) return $this->apiReturn(103005, '创建订单接口没有返回订单ID');

        // $wmsorder = json_decode(post_curl(ORDERAPI_DOMAIN.'/order/wmsorder', ['order_id'=>$order_id]), true); // wms预分配

        // if ($wmsorder['errcode'] != 0) return $this->apiReturn($wmsorder['errcode'], $wmsorder['errmsg'], $order_sn);

        // 设置付款
        $save = array(
            'pay_type'      => 1,
            'price'         => $order_amount,
            'pay_order_sn'  => '',
            'serial_number' => '',
            'pay_id'        => 0,
            'pay_name'      => '交通银行',
        );

        $res = A('Order/Pay')->setPayOrder($order_id, $save, 1);

        if (!$res) return $this->apiReturn(103004, '设置付款失败');
        if ($res['err_code'] != 0) return $this->apiReturn($res['errcode'], $res['errmsg'], $order_sn);

        // 自营现卖推送wms队列
        $res = A('Order/Base')->makeOrder($order_id);
        if ($res['err_code'] != 0) return $this->apiReturn($res['err_code'], $res['err_msg']); 
        
        $this->pushProductTracking($push_product_data); // 推送生产跟踪信息

        return $this->apiReturn(0, '创建自营内部采购订单成功', $order_sn);
    }

    // ERP推送售后入库商品
    // {"ENTRY":[{"FQTY":"3","FENTRYID":"LxYAAAGxzrGIiCpY"}],"SERVICE_SN":"F-12021011251242-0"}
    public function putWareHousing($args)
    {
        $check = $this->check();

        if (!$check) return $this->apiReturn(3, '未登录');

        $this->apiRecord('ERP推送售后入库商品，参数：'.$args); // 记录接口访问信息

        $datas = json_decode($args, true);

        if (!$datas && empty($datas['ENTRY'])) return $this->apiReturn(103001, '无入库明细');

        $OrderServiceModel      = D('Order/OrderService');
        $OrderServiceItemsModel = D('Order/OrderServiceItems');
        $OrderServiceLogModel   = D('Order/OrderServiceLog');

        // 获取退货单
        $order_service = $OrderServiceModel->where(['service_sn' => $datas['SERVICE_SN']])->find();
        if (!$order_service) return $this->apiReturn(103002, '未找到售后单');

        $event = '';
        // 更新入库数量
        foreach ($datas['ENTRY'] as $v) {
            $map = [];
            $map['service_id']  = $order_service['id'];
            $map['erp_rec_id'] = $v['FENTRYID'];

            $service_items = $OrderServiceItemsModel->where($map)->field('id, goods_name, adjust_number, put_warehouse_number')->find();

            // 未找到售后退货明细 或 售后退货数量与入库数量一致，则跳过
            if (empty($service_items) || $service_items['adjust_number'] == $service_items['put_warehouse_number']) continue;

            $res = $OrderServiceItemsModel->where($map)->setInc('put_warehouse_number', abs($v['FQTY']));
            if ($res === false) return $this->apiReturn(103003, '更新入库数量失败，ERP明细ID：'.$v['FENTRYID']);

            $event .= '商品：'.$service_items['goods_name'].'，添加入库数量：'.abs($v['FQTY']).'; ';
        }

        // 校验是否全部入库完成，若完成则生成退款单
        $order_service_items = $OrderServiceItemsModel->where(['service_id' => $order_service['id']])->getField('id, adjust_number, put_warehouse_number', true);
        if (empty($order_service_items)) return $this->apiReturn(103004, '未找到售后明细');

        $is_all_done = true; // 是否全部入库完成

        foreach ($order_service_items as $v) {
            if ($v['adjust_number'] != $v['put_warehouse_number']) {
                $is_all_done = false;
                continue;
            }
        }

        if (!$is_all_done) {
            $event && $OrderServiceLogModel->addLog($order_service['id'], 1000, rtrim($event, '; '));
            
            return $this->apiReturn(0, '入库完成');
        }

        $OrderModel            = D('Order/Order');
        $PayLogModel           = D('Order/PayLog');
        $OrderRefundModel      = D('Order/OrderRefund');
        $OrderRefundItemsModel = D('Order/OrderRefundItems');
        $OrderRefundLogModel   = D('Order/OrderRefundLog');

        $order_info = $OrderModel->getInfo($order_service['order_id']);
        $order_service_items = $OrderServiceItemsModel->getServiceItems($order_service['id']); 

        if ($order_info['order_pay_type'] == 2) { // 预付款
            $pay_log = count($all_pay_log) > 1 ? $all_pay_log[1] :  $all_pay_log[0];  
        } else {
            $pay_log = $PayLogModel->getInfo($order_service['order_id'], '*', true);
        }

        $refund_amount = 0; // 退款总额
        foreach ($order_service_items as $v) {
            $goods_amount = $v['adjust_price'] * $v['adjust_number'];
            $refund_amount += $goods_amount;
        }

        $OrderModel->startTrans();

        // 生成退款单
        $refund_info = [];
        
        $count = $OrderRefundModel->countOrderRefund($order_service['order_id']); // 退款单数量
            
        $refund_info['refund_sn']        = 'T-'.$order_info['order_sn'].'-'.$count;
        $refund_info['order_id']         = $order_service['order_id'];
        $refund_info['order_sn']         = $order_info['order_sn'];
        $refund_info['user_id']          = $order_info['user_id'];
        $refund_info['order_goods_type'] = $order_info['order_goods_type'];
        $refund_info['currency']         = $order_info['currency'];
        $refund_info['pay_log_id']       = $pay_log['pay_log_id'];
        $refund_info['serial_number']    = $pay_log['serial_number'];
        $refund_info['pay_name']         = $pay_log['pay_name'];
        $refund_info['pay_amount']       = number_format($refund_amount, 2, '.', '');
        $refund_info['refund_reason']    = 'ERP入库完成后退款';
        $refund_info['create_uid']       = 1000; // 操作人
        $refund_info['is_sys']           = 0;
        $refund_info['create_time']      = time();

        $refund_id = $OrderRefundModel->data($refund_info)->add();
        if ($refund_id === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44007, '新增订单退款表失败');
        }

        // 生成退款明细
        $refund_items_info = [];
        foreach ($order_service_items as $v) {
            $addItemsData = [];

            $addItemsData['refund_id']        = $refund_id;
            $addItemsData['order_id']         = $order_service['order_id'];
            $addItemsData['rec_id']           = $v['rec_id'];
            $addItemsData['goods_id']         = $v['goods_id'];
            $addItemsData['goods_name']       = $v['goods_name'];
            $addItemsData['sku_name']         = $v['sku_name'];
            $addItemsData['brand_id']         = $v['brand_id'];
            $addItemsData['brand_name']       = $v['brand_name'];
            $addItemsData['supplier_id']      = $v['supplier_id'];
            $addItemsData['supplier_name']    = $v['supplier_name'];
            $addItemsData['goods_price']      = $v['goods_price'];
            $addItemsData['single_pre_price'] = $v['single_pre_price'];
            $addItemsData['refund_num']       = $v['adjust_number'];
            $addItemsData['create_time']      = time();

            $refund_items_info[] = $addItemsData;
        }

        $res = $OrderRefundItemsModel->addAll($refund_items_info);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44008, '新增订单退款明细表失败');
        }

        // 非原路返回 生成退款日志
        $refund_log_info = [];
        $refund_log_info['refund_id']     = $refund_id;
        $refund_log_info['order_id']      = $order_service['order_id'];
        $refund_log_info['pay_log_id']    = $pay_log['pay_log_id'];
        $refund_log_info['serial_number'] = $pay_log['serial_number'];
        $refund_log_info['pay_amount']    = number_format($refund_amount, 2);
        $refund_log_info['refund_status'] = 2; // 财务ERP退款

        $res = $OrderRefundLogModel->add($refund_log_info);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44009, '新增订单退款日志表失败');
        }    

        // 回写退款单号到售后单
        $service_update = [];
        $service_update['refund_id']     = $refund_id;
        $service_update['refund_sn']     = $refund_info['refund_sn'];
        $service_update['refund_status'] = 2; // 待退款
        $service_update['return_status'] = 3; // 已入库
        $res = $OrderServiceModel->where(['id' => $order_service['id']])->save($service_update);
        if ($res === false) {
            $OrderModel->rollback();
            return $this->apiReturn(44010, '更新售后单失败');
        }

        $event && $OrderServiceLogModel->addLog($order_service['id'], 1000, rtrim($event, '; ').'，生成退款单：'.$refund_info['refund_sn']);

        $OrderModel->commit();

        return $this->apiReturn(0, '入库完成，并生成退款单', $refund_info['refund_sn']);
    }

    // ERP推送账期支付
    public function billPay($args)
    {
        $check = $this->check();

        if (!$check) return $this->apiReturn(3, '未登录');

        $this->apiRecord('ERP推送账期支付，参数：'.$args); // 记录接口访问信息

        $args = json_decode($args, true);

        $OrderModel = D('Order/Order');
        $offline_order_id = $OrderModel->where(['sale_order_sn' => $args['JKNUMBER']])->getField('order_id');

        if ($offline_order_id) return $this->apiReturn(0, '线下订单不生成账期支付信息');

        $data['order_sn'] = $args['JKNUMBER'];
        $data = array_merge($data, authkey());

        $res = post_curl(API_DOMAIN.'/pay/billpay', $data);

        if (!$res) {
            dingtalk_robot(C('DINGTALK_ROBOT_TOKEN.order'), 'EERP推送账期支付告警，错误信息：'.$res);
        }

        $res = json_decode($res, true);

        return $this->apiReturn($res['err_code'], $res['err_msg']);
    }

}