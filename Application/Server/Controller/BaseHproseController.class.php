<?php
namespace Server\Controller;

use Think\Controller\HproseController;

class BaseHproseController extends HproseController
{
    const pf = 3;
    /**
     * 统一格式返回
     * @param  integer $code   [description]
     * @param  string  $msg    [description]
     * @param  array   $extend [description]
     * @return [type]          [description]
     */
    protected function apiReturn($code = 0, $msg = '', $extend = array())
    {
        $data = array(
            'err_code' => $code,
            'err_msg' => $msg,
            'data' => $extend,
        );
        if ($code != 0) {
            $this->apiLog($code, $msg, $extend);
            if ($code > 0) {
                unset($data['data']);
            }
            $data['err_code'] = abs($data['err_code']);
            \Think\Log::save();
        }
        return json_encode($data);
    }

    /**
     * 获取公司是否有账号使用
     * @param  [type] $name [description]
     * @return [type]     [description]
     */
    protected function getCompanyUser($name)
    {
        $data['name'] = $name;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/user/getcompanybyname', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取ERP是否有账号使用
     * @param  [type] $code [description]
     * @return [type]     [description]
     */
    protected function getErpUser($code)
    {
        $data['code'] = $code;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/user/geterpcode', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取手机对应用户
     * @param  [type] $mobile [description]
     * @return [type]         [description]
     */
    protected function getMobileUser($mobile, $more = 0)
    {
        $data['mobile'] = $mobile;
        $data['more'] = $more;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/user/getmobile', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取邮箱对应用户
     * @param  [type] $mobile [description]
     * @return [type]         [description]
     */
    protected function getEmailUser($email, $more = 0)
    {
        $data['email'] = $email;
        $data['more'] = $more;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/user/getemail', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }


    /**
     * 绑定ERP用户到网站用户
     * @param  [type] $code [description]
     * @return [type]       [description]
     */
    protected function bingErpUser($user_id, $code)
    {
        $data['user_id'] = $user_id;
        $data['code'] = $code;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/user/binderpcode', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    protected function getAllOrder($stime, $etime, $is_type)
    {
        $data['stime'] = $stime;
        $data['etime'] = $etime;
        $data['is_type'] = $is_type;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/allList', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取订单
     * @param  [type] $name [description]
     * @return [type]     [description]
     */
    protected function getOrder($sn, $user_id)
    {
        $data['order_sn'] = $sn;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/findorder', $data, array('Cookie:'.C('COOKIE_PREFIX').'uid='.$user_id));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 设置订单
     * @param [type] $data [description]
     */
    protected function setOrder($data)
    {
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/createorder', $data, array('Cookie:'.C('COOKIE_PREFIX').'uid='.$data['user_id']));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 设置订单明细
     * @param [type] $data [description]
     */
    protected function setOrderItems($data)
    {
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/createitems', $data, array('Cookie:'.C('COOKIE_PREFIX').'uid='.$data['user_id']));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 注册账户
     * @param  [type] $name [description]
     * @return [type]     [description]
     */
    protected function setUser($name, $pwd, $user_code)
    {
        $data['account'] = $name;
        $data['password'] = $pwd;
        $data['user_code'] = $user_code;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/reg/userregadmin', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 修改用户资料
     * @param  [type] $erp_sale_uid [description]
     * @return [type]               [description]
     */
    protected function changeUser($user_id, $erp_sale_uid)
    {
        $data['erp_sale_uid'] = $erp_sale_uid;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/user/changeuserinfo', $data, array('Cookie:'.C('COOKIE_PREFIX').'uid='.$user_id));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;

    }

    /**
     * 注册手机账户
     * @param  [type] $name [description]
     * @return [type]     [description]
     */
    protected function setMobileUser($name, $pwd, $parent_id = 0, $sale_uid = 0)
    {
        $data['account'] = $name;
        $data['password'] = $pwd;
        $data['parent_id'] = $parent_id;
        $data['sale_uid'] = $sale_uid;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/reg/mobileregadmin', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 注册手机账户
     * @param  [type] $name [description]
     * @return [type]     [description]
     */
    protected function setEmailUser($name, $pwd, $parent_id = 0, $sale_uid = 0)
    {
        $data['email'] = $name;
        $data['password'] = $pwd;
        $data['parent_id'] = $parent_id;
        $data['sale_uid'] = $sale_uid;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/reg/emailregadmin', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 发送商品接口
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    protected function getGoods($id)
    {
        $data['id'] = $id;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/goods/info', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取用户的地址
     * @param  [type] $addres_id [description]
     * @return [type]            [description]
     */
    public function getAddress($addres_id)
    {
        $data['address_id'] = $addres_id;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/address/info', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取快递
     * @param  [type] $no [description]
     * @return [type]            [description]
     */
    public function getShipping($no, $id)
    {
        $data['shipping_no'] = $no;
        $data['shipping_id'] = $id;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/shipping/info', $data, array($this->loginCookie()));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }


    /**
     * 获取用户发票的地址
     * @param  [type] $tax_id    [description]
     * @return [type]            [description]
     */
    public function getUserInvoice($user_id, $tax_title)
    {
        $data['tax_title'] = $tax_title;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/invoice/gettaxid', $data, array('Cookie:'.C('COOKIE_PREFIX').'uid='.$user_id));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 设置用户发票的地址
     * @param  [type] $data      [description]
     * @return [type]            [description]
     */
    public function setUserInvoice($data)
    {
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/invoice/create', $data, array('Cookie:'.C('COOKIE_PREFIX').'uid='.$data['user_id']));
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    public function synShipping($data)
    {
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/erpshipping', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    // 导入ERP用户
    public function setErpUser($data)
    {
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/crm/erpuser', $data);

        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    // 订单退款申请处理结果
    public function setOrderRefundResult($data)
    {
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/refund/orderRefundResultFromErp', $data);

        if ($res === false) {
            return $this->apiReturn(44010, '订单退款申请处理结果请求失败');
        }
        return json_decode($res, true);;
    }

    /**
     * 新增支付记录
     * @return [type] [description]
     */
    public function setPaidLog($data)
    {
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/setpaidlog', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 删除支付记录
     * @return [type] [description]
     */
    public function delPaidLog($data)
    {
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/delpaidlog', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 新增收款记录
     * @return [type] [description]
     */
    public function setReceiptLog($data)
    {
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/setReceiptLog', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 取消收款记录
     * @return [type] [description]
     */
    public function delReceiptLog($data)
    {
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/delReceiptLog', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    // 推送生产跟踪到队列
    public function pushProductTracking($data)
    {
        $queue_name = C('ORDER_PRODUCT_TRACK');

        $RbmqModel = D('Common/Rbmq');

        $res = $RbmqModel->connect('RBMQ_CONFIG')->queue($queue_name)->push($data, $queue_name);

        $path = C('LOG_PATH').ACTION_NAME.'/'.date('y_m_d').'.log'; // 接口日志文件
        \Think\Log::write('推送生产跟踪数据：'.json_encode($data).'，推送队列返回：'.$res, INFO, '', $path);

        if ($res) return ['err_code' => 0, 'err_msg' => '推送队列成功'];

        return ['err_code' => -1, 'err_msg' => '推送队列失败'];
    }

}