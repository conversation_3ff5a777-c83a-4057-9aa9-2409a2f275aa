<?php
namespace Server\Controller;

class BaseController extends \Common\Controller\BaseController
{
    const pf = 3;
    /**
     * 统一格式返回
     * @param  integer $code   [description]
     * @param  string  $msg    [description]
     * @param  array   $extend [description]
     * @return [type]          [description]
     */
    // protected function apiReturn($code = 0, $msg = '', $extend = array())
    // {
    //     $data = array(
    //         'err_code' => $code,
    //         'err_msg' => $msg,
    //         'data' => $extend,
    //     );
    //     if ($code != 0) {
    //         $this->apiLog($code, $msg, $extend);
    //         if ($code > 0) {
    //             unset($data['data']);
    //         }
    //         $data['err_code'] = abs($data['err_code']);
    //         \Think\Log::save();
    //     }
    //     return json_encode($data);
    // }

    /**
     * 设置订单ERPid
     * @param [type] $order_id [description]
     * @param [type] $erp_id   [description]
     */
    public function setErpId($order_sn, $erp_id)
    {
        $data['order_sn'] = $order_sn;
        $data['erp_order_id'] = $erp_id;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/updateerpid', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 设置订单明细ERPid
     * @param [type] $mapping [description]
     */
    public function setErpItemsId($mapping)
    {
        $data['mapping'] = $mapping;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/updateitemerpid', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取需要同步入队的订单ID
     * @return [type] [description]
     */
    public function getSynOrder()
    {
        $data = authkey(self::pf);
        $res = post_curl(API_DOMAIN.'/order/synorder', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 设置订单同步标记
     * @param [type]  $order_id [description]
     * @param integer $sign     [description]
     */
    public function setOrderSyn($order_id, $sign = 0)
    {
        $data['order_id'] = $order_id;
        $data['sign'] = $sign;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/updatesynsign', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;

    }

    /**
     * 获取订单发送所需相关内容
     * @return [type] [description]
     */
    public function getErpSendInfo($order_id)
    {
        $data['order_id'] = $order_id;
        $data = array_merge($data, authkey(self::pf));
        $res = post_curl(API_DOMAIN.'/order/erpneedinfo', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

}