<?php
namespace Server\Controller;

use Think\Controller;
// use Server\Controller\BaseController;

class UserController extends Controller
{
    public function test()
    {

        vendor('Hprose.HproseHttpClient');
        $client = new \HproseHttpClient('http://api.liexin.com/Server/Server');
        $res = $client->auth(json_encode(array('user' => 'ichunterp', 'pwd' => C('ERP_CLIENT.ichunterp'))));
        if ($res['err_code'] == 0) {
            echo $client->createOfflineOrder('[]');
            // $data = array(
            //     'SALESPERSONNO' => 'yxx',
            //     'SALESPERSON' => '杨XX',
            //     'CUSLINKMANPHONE' => '***********',
            //     'TYPE' => 1,
            // );
            // $data = json_decode('{"TYPE":1,"CUSLINKMANEMAIL":"<EMAIL>","CUSLINKMANPHONE":"","SALESPERSONNO":"肖霞","SALESPERSON":"XX"}', true);
            // $client->createManageUser(json_encode($data));

            // $data = array(
            //     'CUSTOMER' => '上海凌翊实业有限公司',
            //     'CUSTOMERID' => 'LxYAAAAGny6/DAQO',
            //     'FADDRESS' => '上海市金山区吕巷镇溪南路86号21幢1092室',
            //     'FTXREGISTERNO' => '91310116557490916C',
            //     'FPHONE' => '021-********',
            //     'FBANK' => '温州银行上海分行',
            //     'FBANKACCOUNT' => '905000120190006243',
            //     'CUSLINKMANPHONE' => '***********',
            // );
            // $data = json_decode('{"FBANKACCOUNT":"***************","FBANK":"东莞银行长安支行","FADDRESS":"东莞市长安镇锦厦社区河南工业区锦升路8号","CUSLINKMANEMAIL":"<EMAIL>","CUSLINKMANPHONE":"","FNUMBER":"M0000074","FPHONE":"0769-********","CUSTOMERID":"LxYAAAABTwm/DAQO","CUSTOMER":"广东奥普特科技股份有限公司","FTXREGISTERNO":"***************"}', true);
            // $client->createUserMobile(json_encode($data));

            // $data = array(
            //     'CUSTOMER' => '上海凌翊实业有限公司',
            //     'CUSTOMERID' => 'LxYAAAAGny6/DAQO',
            //     'FNUMBER' => 'test123',
            //     'CURRENCY' => '美元',
            //     'FTOTALAMOUNT' => '3703.7250',
            //     'BIZDATE' => '2017-08-18',
            //     'DELIVERTYPE' => '送货',
            //     'AUDITTIME' => '17-08-18 17:12:07',
            //     'BASESTATUS' => '关闭',
            //     'PAYMENTDATE' => '17-09-26 09:57:30',
            //     'COMPLETEDATE' => '17-09-26 09:58:18',
            //     'MATERIAL' => 'M83513/02-AN',
            //     'FMODEL' => 'Glenair',
            //     'MATERIALID' => 'sfweww3*#$#2',
            //     'FTAXPRICE' => '14.81 ',
            //     'FQTY' => '150.00',
            //     'CUSLINKMAN' => '杨BB',
            //     'CUSLINKMANPHONE' => '***********',
            // );
            // $data = json_decode('{"MATERIALID":"OJhygTP3TUau87nu6SXGpkQJ5/A=","FTOTALAMOUNT":0,"CUSLINKMAN":"朱小姐","CUSLINKMANPHONE":"","CUSLINKMANEMAIL":"<EMAIL>","DELIVERTYPE":"17-07-24 10:39:19","CURRENCY":"人民币","FTAXPRICE":7.86,"MATERIAL":"CSD18531Q5A","FMODEL":"TI","CUSTOMER":"广东奥普特科技股份有限公司","AUDITTIME":"送货","FNUMBER":"XX20170724-0004","FQTY":5000,"CUSTOMERID":"LxYAAAABTwm/DAQO","BIZDATE":"2017-07-24","FTAXAMOUNT":0,"ORDSEQ":1,"BASESTATUS":"关闭"}', true);
            // echo $client->createOrder(json_encode($data));
            
            // $data = array(
            //     'SALELSSUEID' => 'xxxaaabbb',
            //     'LOGISTICSNUMBER' => '',//'aaa11a313',
            //     'CARRIER' => '',//'顺丰速运',
            //     'DELIVERTYPE' => '快递',
            //     'NUMBER' => 'ZXZ-SZ201708310021',
            //     'ENTRYS' => array(
            //         array(
            //             'MATERIALID' => 'AGcSWdJ7ScqNbZNMtgaZm0QJ5/A=',
            //             'FQTY' => 30,
            //         )
            //     ),
            //     'FLAG' => '1',
            // );
            // echo $client->shipping(json_encode($data));
            // $a = $client->getOrderList('{"StartDate":*************,"endDate":*************}');
            $data = json_decode('{"customer":"宁波市江东磐瑞国际贸易有限公司","addess":"江东区会展路181号宁波磐瑞国际贸易展览中心常年展2号馆7D33","txRegisterNo":"***************","tel":"0574-********","bank":"中国工商银行宁波新城支行","bankAccount":"3901120109000101989","salesMan":"李智勇,super,黄彦博","desMan":"xxx","desPhone":"***********","desTel":"0574-********","desEmail":"<EMAIL>"}', true);
            $a = $client->getUserFromErp(json_encode($data));
            dump($a);
        }
        // return $hellow;
    }
}