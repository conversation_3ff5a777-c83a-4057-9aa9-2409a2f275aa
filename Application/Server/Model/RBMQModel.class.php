<?php
/**
 * RBMQ队列
 * @authors yc ()
 * @date    2017-12-08 11:09:37
 * @version $Id$
 */
namespace Server\Model;
use Think\Model;
class RBMQModel extends Model {

    protected $autoCheckFields = false;
    protected $exchange_name = 'api';
    protected $queue_name = 'api';
    protected $route_name = 'api';


    public function _initialize()
    {
        $config = C('RBMQ_CONFIG');
        $this->conn = new \AMQPConnection($config);
        $this->conn->connect() or die('Cannot connect to the borken!<br/>');
        try {
            $this->channel = new \AMQPChannel($this->conn);
            $this->exchange = new \AMQPExchange($this->channel);
            $this->queue = new \AMQPQueue($this->channel);

            // $this->exchange->setName($this->exchange_name);   //交换机名
            // $this->exchange->setType(AMQP_EX_TYPE_DIRECT);    //使用完全匹配路由规则
            // $this->exchange->setFlags(AMQP_DURABLE);          //交换机持久化
            // $this->exchange->declare();
            // $this->queue->setName($this->queue_name);         //队列名
            // $this->queue->setFlags(AMQP_DURABLE);             //队列持久化
            // $this->queue->declare();

            // $this->queue->bind($this->exchange_name, $this->route_name);    //绑定交换机与路由标记
            
        } catch (Exception $e) {
            exit();
        }
    }

    public function __destruct()
    {
        try {
            $this->conn->disconnect();
        } catch (Exception $e) {
        }
    }

    /**
     * 设置新交换机名
     * @param  [type] $name [description]
     * @return [type]       [description]
     */
    public function exchange($name)
    {
        $this->exchange_name = $name;
        $this->exchange->setName($this->exchange_name);   //交换机名
        $this->exchange->setType(AMQP_EX_TYPE_DIRECT);    //使用完全匹配路由规则
        $this->exchange->setFlags(AMQP_DURABLE);          //交换机持久化
        $this->exchange->declare();
        return $this;
    }

    /**
     * 设置新队列名
     * @param  [type] $name [description]
     * @return [type]       [description]
     */
    public function queue($name)
    {
        $this->queue_name = $name;
        $this->queue->setName($this->queue_name);         //队列名
        $this->queue->setFlags(AMQP_DURABLE);             //队列持久化
        $this->queue->declare();
        return $this;
    }

    public function exchangeBind($exchange, $route)
    {
        $this->queue->bind($exchange, $route);    //绑定交换机与路由标记
        return $this;
    }

    /**
     * 设置绑定交换机路由标记
     * @param  [type] $name [description]
     * @return [type]       [description]
     */
    public function route($name)
    {
        $this->route_name = $name;
        $this->queue->bind($this->exchange_name, $this->route_name);    //绑定交换机与路由标记
        return $this;
    }

    /**
     * 入队
     * @param  [type] $msg   [description]
     * @param  string $route [description]
     * @return [type]        [description]
     */
    public function push($msg, $route = '')
    {
        if (empty($route)) {
            $route = $this->route_name;
        }
        if (is_array($msg)) {
            $msg = json_encode($msg);
        }
        $res = $this->exchange->publish($msg, $route);
        return $res;
    }

    /**
     * 出队
     * @return [type] [description]
     */
    public function pull($ack = true)
    {
        $this->response = $this->queue->get();                 //拉取数据
        if (false === $this->response) {
            return false;
        }
        if ($ack) {
            $this->ack();
        }
        return $this->response->getbody();
    }

    public function ack()
    {
        $this->queue->ack($this->response->getDeliveryTag());  //应答
    }
}