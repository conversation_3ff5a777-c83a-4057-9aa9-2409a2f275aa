<?php

namespace Bom\Controller;

class BomMatchController extends BaseController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 转发请求到Laravel项目
     * @param string $endpoint 接口端点
     * @param array $params 请求参数
     * @param bool $isFileDownload 是否为文件下载
     * @return mixed
     */
    private function forwardRequest($endpoint, $params = [], $isFileDownload = false)
    {
        $uid = cookie('uid');
        // 获取所有请求参数
        $requestParams = I('request.');

        // 合并传入的参数
        if (!empty($params)) {
            $requestParams = array_merge($requestParams, $params);
        }

        $requestParams['source'] = 'www';
        $requestParams['userId'] = $uid;
        $user_info = S_user($uid);
        $requestParams['email'] = $user_info['email'];
        $requestParams['customer_user_name'] = $user_info['mobile'] ? $user_info['mobile'] : $user_info['email'];

        // 构建完整的URL
        $url = FRQ_DOMAIN . '/api/bomMatch/' . $endpoint;

        if ($isFileDownload) {
            // 文件下载请求，直接转发文件流
            return $this->forwardFileDownload($url, $requestParams, ['source:www']);
        }
        $header = [
            'source:www'
        ];

        // 发送请求
        $response = post_curl($url, $requestParams, $header);

        // 解析响应
        if (!empty($response)) {
            $result = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $this->apiReturn(isset($result['code']) ? $result['code'] : 0, isset($result['msg']) ? $result['msg'] : '', isset($result['data']) ? $result['data'] : $result);
            }
        }

        return $this->apiReturn(-1, '请求失败', []);
    }

    /**
     * 转发文件下载请求
     * @param string $url 目标URL
     * @param array $params 请求参数
     * @param array $header 请求头
     */
    private function forwardFileDownload($url, $params = [], $header = [])
    {
        // 初始化cURL
        $ch = curl_init();

        // 设置cURL选项
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 先获取响应内容
        curl_setopt($ch, CURLOPT_HEADER, true); // 包含响应头
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300); // 5分钟超时，适合大文件
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        if (!empty($header)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        }

        // 执行请求
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);

        // 检查是否有错误
        if (curl_errno($ch)) {
            curl_close($ch);
            header('Content-Type: application/json');
            echo json_encode([
                'err_code' => -1,
                'err_msg' => '文件下载失败: ' . curl_error($ch),
                'data' => []
            ]);
            return;
        }

        curl_close($ch);

        // 分离响应头和响应体
        $headers = substr($response, 0, $headerSize);
        $body = substr($response, $headerSize);

        // 检查HTTP状态码
        if ($httpCode !== 200) {
            // 如果不是200，可能是JSON错误响应
            if (json_decode($body, true) !== null && json_last_error() === JSON_ERROR_NONE) {
                header('Content-Type: application/json');
                echo $body;
                return;
            }
        }

        // 解析并转发响应头
        $headerLines = explode("\r\n", $headers);
        foreach ($headerLines as $headerLine) {
            $headerLine = trim($headerLine);
            if (!empty($headerLine) && strpos($headerLine, ':') !== false) {
                // 转发重要的响应头
                if (
                    stripos($headerLine, 'Content-Type:') === 0 ||
                    stripos($headerLine, 'Content-Disposition:') === 0 ||
                    stripos($headerLine, 'Content-Length:') === 0 ||
                    stripos($headerLine, 'Cache-Control:') === 0
                ) {
                    header($headerLine);
                }
            }
        }

        // 输出文件内容
        echo $body;
        exit; // 重要：直接退出，不再执行其他代码
    }

    /**
     * 获取BOM匹配列表
     */
    public function getBomMatchList()
    {
        return $this->forwardRequest('getBomMatchList');
    }

    /**
     * 获取BOM匹配数据
     */
    public function getBomMatchData()
    {
        return $this->forwardRequest('getBomMatchData');
    }

    /**
     * 导出BOM匹配数据
     */
    public function exportBomMatch()
    {
        return $this->forwardRequest('exportBomMatch', [], true);
    }

    /**
     * 修改BOM匹配项供应商
     */
    public function changeBomMatchItemSupplierId()
    {
        return $this->forwardRequest('changeBomMatchItemSupplierId');
    }

    /**
     * 上传BOM匹配数据
     */
    public function uploadBom()
    {
        return $this->forwardRequest('uploadBom');
    }

    /**
     * 获取BOM匹配明细
     */
    public function getBomMatchItemList()
    {
        return $this->forwardRequest('getBomMatchItemList');
    }

    /**
     * 获取BOM详情
     */
    public function getBomMatchDetail()
    {
        return $this->forwardRequest('getBomMatchDetail');
    }

    /**
     * 更新BOM匹配项供应商
     */
    public function updateBomMatchItemSupplierIds()
    {
        return $this->forwardRequest('updateBomMatchItemSupplierIds');
    }

    /**
     * 删除BOM匹配
     */
    public function deleteBomMatch()
    {
        return $this->forwardRequest('deleteBomMatch');
    }

    /**
     * 重新整理指定列内容
     */
    public function reorganizeBomMatchItem()
    {
        return $this->forwardRequest('reorganizeBomMatchItem');
    }

    /**
     * 保存BOM匹配
     */
    public function saveBomMatchDetail()
    {
        return $this->forwardRequest('saveBomMatchDetail');
    }

    /**
     * 更新BOM匹配项数量
     */
    public function updateMatchItemAmount()
    {
        return $this->forwardRequest('updateMatchItemAmount');
    }

    /**
     * 更新BOM匹配项商品ID
     */
    public function changeGoodsId()
    {
        return $this->forwardRequest('changeGoodsId');
    }

    /**
     * 重新匹配BOM
     */
    public function reMatchBomMatch()
    {
        return $this->forwardRequest('reMatchBomMatch');
    }

    /**
     * BOM生成询价单
     */
    public function multiMakeInquiry()
    {
        return $this->forwardRequest('multiMakeInquiry');
    }

    /**
     * 删除BOM匹配项
     */
    public function deleteBomMatchItem()
    {
        return $this->forwardRequest('deleteBomMatchItem');
    }

    /**
     * 修正BOM匹配项
     */
    public function rectifyBomMatchItem()
    {
        return $this->forwardRequest('rectifyBomMatchItem');
    }

    /**
     * 更新BOM匹配项
     */
    public function updateBomMatchItem()
    {
        return $this->forwardRequest('updateBomMatchItem');
    }

    /**
     * 搜索SPU
     */
    public function searchSpu()
    {
        return $this->forwardRequest('searchSpu');
    }

    /**
     * 获取BOM操作日志列表
     */
    public function getBomOperationLogList()
    {
        return $this->forwardRequest('getBomOperationLogList');
    }

    /**
     * 保存指定商品信息
     */
    public function saveSpecificGoods()
    {
        return $this->forwardRequest('saveSpecificGoods');
    }

    /**
     * 确认BOM匹配项
     */
    public function confirmBomMatchItem()
    {
        return $this->forwardRequest('confirmBomMatchItem');
    }

    /**
     * 批量导出BOM匹配
     */
    public function batchExportBomMatch()
    {
        return $this->forwardRequest('batchExportBomMatch', [], true);
    }

    /**
     * 获取BOM匹配统计
     */
    public function getBomMatchStatistics()
    {
        return $this->forwardRequest('getBomMatchStatistics');
    }
}
