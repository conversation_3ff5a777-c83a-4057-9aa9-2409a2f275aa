<?php

namespace Bom\Controller;

class BomMatchController extends BaseController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 转发请求到Laravel项目
     * @param string $endpoint 接口端点
     * @param array $params 请求参数
     * @return mixed
     */
    private function forwardRequest($endpoint, $params = [])
    {
        // 获取所有请求参数
        $requestParams = I('request.');

        // 合并传入的参数
        if (!empty($params)) {
            $requestParams = array_merge($requestParams, $params);
        }

        // 构建完整的URL
        $url = FRQ_DOMAIN . '/api/bomMatch/' . $endpoint;

        // 发送请求
        $response = post_curl($url, $requestParams);

        // 解析响应
        if (!empty($response)) {
            $result = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $this->apiReturn($result['code'] ?? 0, $result['msg'] ?? '', $result['data'] ?? $result);
            }
        }

        return $this->apiReturn(-1, '请求失败', []);
    }

    /**
     * 获取BOM匹配列表
     */
    public function getBomMatchList()
    {
        return $this->forwardRequest('getBomMatchList');
    }

    /**
     * 获取BOM匹配数据
     */
    public function getBomMatchData()
    {
        return $this->forwardRequest('getBomMatchData');
    }

    /**
     * 导出BOM匹配数据
     */
    public function exportBomMatch()
    {
        return $this->forwardRequest('exportBomMatch');
    }

    /**
     * 修改BOM匹配项供应商
     */
    public function changeBomMatchItemSupplierId()
    {
        return $this->forwardRequest('changeBomMatchItemSupplierId');
    }

    /**
     * 上传BOM匹配数据
     */
    public function uploadBom()
    {
        return $this->forwardRequest('uploadBom');
    }

    /**
     * 获取BOM匹配明细
     */
    public function getBomMatchItemList()
    {
        return $this->forwardRequest('getBomMatchItemList');
    }

    /**
     * 获取BOM详情
     */
    public function getBomMatchDetail()
    {
        return $this->forwardRequest('getBomMatchDetail');
    }

    /**
     * 更新BOM匹配项供应商
     */
    public function updateBomMatchItemSupplierIds()
    {
        return $this->forwardRequest('updateBomMatchItemSupplierIds');
    }

    /**
     * 删除BOM匹配
     */
    public function deleteBomMatch()
    {
        return $this->forwardRequest('deleteBomMatch');
    }

    /**
     * 重新整理指定列内容
     */
    public function reorganizeBomMatchItem()
    {
        return $this->forwardRequest('reorganizeBomMatchItem');
    }

    /**
     * 保存BOM匹配
     */
    public function saveBomMatchDetail()
    {
        return $this->forwardRequest('saveBomMatchDetail');
    }

    /**
     * 更新BOM匹配项数量
     */
    public function updateMatchItemAmount()
    {
        return $this->forwardRequest('updateMatchItemAmount');
    }

    /**
     * 更新BOM匹配项商品ID
     */
    public function changeGoodsId()
    {
        return $this->forwardRequest('changeGoodsId');
    }

    /**
     * 重新匹配BOM
     */
    public function reMatchBomMatch()
    {
        return $this->forwardRequest('reMatchBomMatch');
    }

    /**
     * BOM生成询价单
     */
    public function multiMakeInquiry()
    {
        return $this->forwardRequest('multiMakeInquiry');
    }

    /**
     * 删除BOM匹配项
     */
    public function deleteBomMatchItem()
    {
        return $this->forwardRequest('deleteBomMatchItem');
    }

    /**
     * 修正BOM匹配项
     */
    public function rectifyBomMatchItem()
    {
        return $this->forwardRequest('rectifyBomMatchItem');
    }

    /**
     * 更新BOM匹配项
     */
    public function updateBomMatchItem()
    {
        return $this->forwardRequest('updateBomMatchItem');
    }

    /**
     * 搜索SPU
     */
    public function searchSpu()
    {
        return $this->forwardRequest('searchSpu');
    }

    /**
     * 获取BOM操作日志列表
     */
    public function getBomOperationLogList()
    {
        return $this->forwardRequest('getBomOperationLogList');
    }

    /**
     * 保存指定商品信息
     */
    public function saveSpecificGoods()
    {
        return $this->forwardRequest('saveSpecificGoods');
    }

    /**
     * 确认BOM匹配项
     */
    public function confirmBomMatchItem()
    {
        return $this->forwardRequest('confirmBomMatchItem');
    }

    /**
     * 批量导出BOM匹配
     */
    public function batchExportBomMatch()
    {
        return $this->forwardRequest('batchExportBomMatch');
    }

    /**
     * 获取BOM匹配统计
     */
    public function getBomMatchStatistics()
    {
        return $this->forwardRequest('getBomMatchStatistics');
    }
}
