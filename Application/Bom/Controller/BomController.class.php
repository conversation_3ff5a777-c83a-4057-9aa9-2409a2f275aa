<?php
namespace Bom\Controller;

use Bom\Controller\BaseController;
use Maatwebsite\Excel\Classes\PHPExcel;

class BomController extends BaseController
{
    private $Login=false;
    public function _initialize()
    {
        parent::_initialize();
        if (in_array(strtolower(ACTION_NAME), array('countbomnum'))) $verify_mode = true;
        else $verify_mode = true;
        $res = $this->checkLogin($verify_mode);
        if ($res['err_code'] === 0){
            $Model=D('Bom');
            $Model->UpdateUser(cookie('gid'),cookie('uid'));//归属未登录的BOM单
            $this->Login=true;
        }
    }

    public function RedirectUrl(){
        $Model=D('Bom');
        $list=$Model->RedirectUrl($this->Login);
        $this->Export($list);
    }

    private function Export($errcode='',$errmsg='',$data=''){
        $callback = I('callback', '');
        if(is_array($errcode)){
            if(!empty($callback)){
                echo $callback.'('.json_encode($this->apiReturn($errcode[0],$errcode[1],empty($errcode[2])?'':$errcode[2])).')';
            }else{
                echo json_encode($this->apiReturn($errcode[0],$errcode[1],empty($errcode[2])?'':$errcode[2]));
            }
        }else{
            if(!empty($callback)){
                echo $callback.'('.json_encode($this->apiReturn($errcode[0],$errcode[1],empty($errcode[2])?'':$errcode[2])).')';
            }else{
                echo json_encode($this->apiReturn($errcode,$errmsg,$data));
            }
        }
    }

    /**
     * Bpm单列表
     * @return [type] [description]
     */
    public function BomList(){
        $Model=D('Bom');
        $list=$Model->BomList($this->Login);
        $this->Export($list);

    }

    /**
     * 批量上传BOM接口
     * @return [type] [description]
     */
    public function UploadBom()
    {
        $Model=D('Bom');
        $data=$Model->UploadBom($this->Login);
        return $this->Export($data);
    }

    /**
     * 删除BOM单
     * @return [type] [description]
     */
    public function DeleteBom()
    {
        $Model=D('Bom');
        $data=$Model->DeleteBom($this->Login);
        return $this->Export($data);
    }

    /**
     * 修改BOM单
     * @return [type] [description]
     */
    public function UpdateBom()
    {
        $Model=D('Bom');
        $data=$Model->UpdateBom($this->Login);
        return $this->Export($data);
    }

    /**
     * 手动创建BOM单
     * @return [type] [description]
     */
    public function AddBom()
    {
        $Model=D('Bom');
        $data=$Model->AddBom($this->Login);
        return $this->Export($data);
    }

    /**
     * BOM单详情页信息
     * @return [type] [description]
     */
    public function BomItemsInfo(){
        $Model=D('Bom');
        $data=$Model->BomItemsInfo($this->Login);
        return $this->Export($data);
    }

    /**
     * 统计BOM单数量
     * @return [type] [description]
     */
    public function CountBomNum(){
        $Model=D('Bom');
        $data=$Model->CountBomNum($this->Login);
        return $this->Export($data);
    }

    /**
     * BOM单详情页列表
     * @return [type] [description]
     */
    public function BomItemsList(){
        $Model=D('Bom');
        $data=$Model->BomItemsList($this->Login);
        return $this->Export($data);
    }

    /**
     * 修改BOM单商品
     * @return [type] [description]
     */
    public function UpdateBomItems(){
        $Model=D('Bom');
        $data=$Model->UpdateBomItems($this->Login);
        return $this->Export($data);
    }

    /**
     * 导出匹配结果
     * @return [type] [description]
     */
    public function ExportBomItems(){
        $Model=D('Bom');
        $data=$Model->ExportBomItems($this->Login);
        return $this->Export($data);
    }
    /**
     * 导出匹配结果生产缓存
     * @return [type] [description]
     */
    public function ApiExportBomItems(){
        $Model=D('Bom');
        $data=$Model->ApiExportBomItems($this->Login);
        return $this->Export($data);
    }
    /**
     * 删除
     * @return [type] [description]
     */
    public function DeleteBomItems(){
        $Model=D('Bom');
        $data=$Model->DeleteBomItems($this->Login);
        return $this->Export($data);
    }
    /**
     * 更换交货地
     * @return [type] [description]
     */
    public function ChangeDelivery (){
        $Model=D('Bom');
        $data=$Model->ChangeDelivery($this->Login);
        return $this->Export($data);
    }
    /**
     * 单个新增商品
     * @return [type] [description]
     */
    public function AddBomItemsOne (){
        $Model=D('Bom');
        $data=$Model->AddBomItemsOne($this->Login);
        return $this->Export($data);
    }
}
