<?php
namespace Bom\Controller;

class BaseController extends \Common\Controller\BaseController
{
    protected function getStoneGoods($goods_id)
    {
        $check['sku_id'] = $goods_id;
        $res = post_curl(self::getStoneUrl($goods_id).'/webapi/goods_details', $check);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 基石获取品牌信息
     * @param  [type]  $brand_id   [description]
     * @param  integer $brand_type 1联营(默认) 2自营
     * @return [type]              [description]
     */
    protected function getStoneBrand($brand_id, $brand_type = 1)
    {
        $check['brand_id'] = $brand_id;
        $check['BrandType'] = $brand_type;
        $res = post_curl(STONE_DOMAIN.'/webapi/Handle_brand', $check);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    /**
     * 获取基石地址 根据类型判断
     * @param  [type] $goods_id [description]
     * @return [type]           [description]
     */
    static public function getStoneUrl($goods_id)
    {
        $second_domain = explode('.', $_SERVER['SERVER_NAME'], 2);
        if ($second_domain[0] == 'szapi') {
            if (strlen($goods_id) < 11) {//自营访问SZ基石
                return SZ_STONE_DOMAIN;
            } else {//联营访问正式基石
                return STONE_DOMAIN;
            }
        } else {
            return STONE_DOMAIN;
        }
    }

    protected function apiReturn($code = 0, $msg = '', $extend = array())
    {
        echo json_encode(parent::apiReturn($code, $msg, $extend));
        exit;
    }
}
