<?php
namespace Bom\Model;

use Think\Model;

class BomItemsModel extends Model
{
    protected $connection ='Bom';
    public function AddBomItems($data)
    {
        return $this->addAll($data);
    }

    /**
     * 单个新增商品
     * @return [type] [description]
     */
    public function AddBomItemsOne($data){
        return $this->add($data);
    }

    /**
     * 更换交货地
     * @return [type] [description]
     */
    public function ChangeDelivery($map,$data){
        return $this->where($map)->save($data);
    }

    /**
     * 修改型号或者采购数量
     * @return [type] [description]
     */
    public function UpdateBomItems($map,$data){
        $data['bom_status']=0;
        $data['goods_id']=0;
        $data['goods_type']=0;
        return $this->where($map)->save($data);
    }
    /**
     * 删除采购商品
     * @return [type] [description]
     */
    public function DeleteBomItems($map,$data){
        return $this->where($map)->delete();
    }

}