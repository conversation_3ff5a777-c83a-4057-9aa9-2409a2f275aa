<?php
namespace Bom\Model;

use Think\Model;

class BomModel extends Model
{
    protected $connection ='Bom';

    //跳转地址
    private function RedirectUrl($Arr,$ToUrl='',$IsTure=false){
        $IE=I('ie');
        $url = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
        $domain=$_SERVER['SERVER_NAME'];
        if($domain=='szapi.ichunt.com'){
            $url .='szbom.ichunt.com';
        }else if($domain=='api.ichunt.com'){
            $url .='bom.ichunt.com';
        }else{
            $url .='bom.liexin.com';
        }
        if($IE!=true && $IsTure==false){
            return $Arr;
        }else{
            if($IsTure===false) return header("location:".$url.$ToUrl.'?errmsg='.$Arr[0]);//跳转到bom单列表
            return header("location:".$url.$ToUrl);
        }
    }
    //搜索服务接口地址生成
    private function SearchUrl(){
        $url = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
        $domain=$_SERVER['SERVER_NAME'];
        if($domain=='szapi.ichunt.com'){
            $url .='szso.ichunt.com';
        }else if($domain=='api.ichunt.com'){
            $url .='so12.ichunt.com';
        }else{
            $url .='soso12.ichunt.com';
        }
        return $url;
    }

    //sku列表地址
    private function SkuListUrl(){
        return GOODS_DOMAIN;
//        $url = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
//        $domain=$_SERVER['SERVER_NAME'];
//        if($domain=='szapi.ichunt.com'){
//            $url .='sz.ichunt.com/v3';
//        }else if($domain=='api.ichunt.com'){
//            $url .='www.ichunt.com/v3';
//        }else{
//            $url .='www.liexin.com';
//        }
//        return $url;
    }

    public function UploadBom($Login)
    {
        set_time_limit(0);
        $gid=cookie('gid');
        $IP=get_client_ip();
        if($Login===false){//限制未登录用户
            $map=['status'=>['eq',1], 'user_id'=>['eq',0], 'gid'=>['eq',$gid]];
            $Find=$this->where($map)->field('bom_id')->find();
            if($Find){
                return $this->RedirectUrl([10001,'未登录用户只能上传一个BOM单，请登录后再上传吧！'],'');
            }
        }

        //处理上传文件
        $upload = new \Think\Upload();// 实例化上传类
        $upload->maxSize   =     1024*100 ;// 设置附件上传大小
        $upload->exts      =     array('xlsx', 'xls');// 设置附件上传类型
        $upload->rootPath  =      './public/'; // 设置附件上传根目录
        $upload->autoSub  =     false;
        $upload->saveName  =     array('uniqid',''); // 设置附件上传（子）目录
        $info   =   $upload->upload();
        if(!$info) return $this->RedirectUrl([10007,'上传失败，请重新上传',$upload->getError()]);

        foreach($info as $file){
            $file_name=$file['savepath'].'/'.$file['savename'];
            $name=rtrim($file['name'],'.xlsx');
            $name=rtrim($name,'.xls');
        }

        //开始提取数据
        vendor("PHPExcel.PHPExcel");
        vendor("PHPExcel.PHPExcel.IOFactory");
        $file_name='./public'.$file_name;
        $extension = strtolower( pathinfo($file_name, PATHINFO_EXTENSION) );
        //兼用xlsx和xls
        if ($extension == 'xlsx') {
            $objReader =\PHPExcel_IOFactory::createReader('Excel2007');
            $objPHPExcel =$objReader->load($file_name, $encode = 'utf-8');
        } else if ($extension == 'xls'){
            $objReader =\PHPExcel_IOFactory::createReader('Excel5');
            $objPHPExcel =$objReader->load($file_name, $encode = 'utf-8');
        }

        //删除文件
        unlink($file_name);
        $sheet = $objPHPExcel->getSheet(0);
        $highestRow = $sheet->getHighestRow();
        if($highestRow>200) return $this->RedirectUrl([10002,'为加快匹配速度，请保证每份文件型号数不超过200']);

//        $highestColumn = $sheet->getHighestColumn();
//        if($highestColumn!='D') return $this->RedirectUrl([10003,'请下载模板，并根据模板格式上传']);

        $Bom=['bom_name'=>$name,'upload_file_url'=>$file_name,'gid'=>$gid,'upload_ip'=>$IP,'add_time'=>time()];
        $this->startTrans();
        $result=$this->add($Bom);
        $BomID=$result;
        for($i=1;$i<(int)$highestRow+1;$i++){
            if($i==1){//判断是不是模板
                if($objPHPExcel->getActiveSheet()->getCell("A".$i)->getValue()!='*型号') return $this->RedirectUrl([10004,'请下载模板，并根据模板格式上传']);
                if($objPHPExcel->getActiveSheet()->getCell("B".$i)->getValue()!='品牌') return $this->RedirectUrl([10004,'请下载模板，并根据模板格式上传']);
                if($objPHPExcel->getActiveSheet()->getCell("C".$i)->getValue()!='数量') return $this->RedirectUrl([10004,'请下载模板，并根据模板格式上传']);
                continue;
            }
            if(empty($objPHPExcel->getActiveSheet()->getCell("A".$i)->getValue())) continue;
            $BrandName=$objPHPExcel->getActiveSheet()->getCell("B".$i)->getValue();
            $GoodsName=$objPHPExcel->getActiveSheet()->getCell("A".$i)->getValue();
            $Num=(int)($objPHPExcel->getActiveSheet()->getCell("C".$i)->getValue());
            $data[$i]['bom_id']=$result;
            $data[$i]['goods_name']=(string)$GoodsName;
            $data[$i]['brand_name']=empty($BrandName)?'':(string)$BrandName;
            $data[$i]['num']=empty($Num)?1:$Num;
            $data[$i]['add_time']=time();
        }

        if(!$result) return $this->RedirectUrl([10005,'上传失败,请重新上传']);
        if(empty($data)) return [10007,'未检测到型号数据，请重新上传'];

        $ItemsModel=D('BomItems');
        $result=$ItemsModel->AddBomItems(array_values($data));
        if(!$result) return $this->RedirectUrl([10006,'上传失败,请重新上传']);
        $this->commit();
        $url=$this->SearchUrl().'/search/bom/pushBomId';
        post_curl($url,['bom_id'=>$BomID]);//推送给搜索服务
        return $this->RedirectUrl([0,'上传成功',$BomID],'/bomdetail?bom_id='.$BomID);

    }


    /**
     * 将未登录用户的Bom单归属到用户
     * @return [type] [description]
     */
    public function UpdateUser($Gid,$Uid){
        if(empty($Gid) || empty($Uid)) return false;
        $map=['status'=>['eq',1], 'user_id'=>['eq',0], 'gid'=>['eq',$Gid]];
        return $this->where($map)->save(['user_id'=>$Uid]);
    }

    /**
     * BomList
     * @return [type] [description]
     */
    public function BomList($Login=false){
        $Login===false ? $map=['gid'=>['eq',cookie('gid')],'user_id'=>['eq',0]]:$map['user_id']=cookie('uid');//判断是否登录
        $map['status']=1;
        $BomName=I('bom_name');
        $AddTime[]=I('start_time')?strtotime(I('start_time')):0;
        $AddTime[]=I('end_time')?strtotime(I('end_time'))+86400:time();
        $BomStatus=I('bom_status');
        empty($BomName) || $map['bom_name']=['like','%'.$BomName.'%'];
        empty($AddTime) || $map['add_time']=['BETWEEN',$AddTime];
        empty($BomStatus) || $map['bom_status']=['eq',$BomStatus];
        $table_items = $this->table('lie_bom_items')->field('bom_id, sum(if(bom_status=1 and goods_type=1,1,null)) as zs,sum(if(bom_status=1 and goods_type=2,1,null)) as ls,
        sum(if(bom_status=2 ,1,null)) as xj,sum(if(bom_status=3,1,null)) as xz,count(bom_id) as num')->where(['status'=>1])->group('bom_id')->select(false);
        $count=$this->alias('B')->join('LEFT JOIN ('.$table_items.') I ON I.bom_id = B.bom_id')->where($map)->field('B.bom_id,bom_name,add_time,I.zs,I.ls,I.xj,I.xz,I.num')
            ->count();
        $list=$this->alias('B')->join('LEFT JOIN ('.$table_items.') I ON I.bom_id = B.bom_id')->where($map)->field('B.bom_id,bom_name,add_time,I.zs,I.ls,I.xj,I.xz,I.num')
            ->order('bom_id desc')->page(empty(I('page'))?1:I('page'),empty(I('limit'))?10:I('limit'))->select();
        if(!$list) return [10001,'没有数据'];

        return [0,$count,$list];
    }

    /**
     * DleteBom
     * @return [type] [description]
     */
    public function DeleteBom($Login=false){
        if($Login===false) return [10001,'未登录不能删除BOM单'];
        $BomID=I('bom_id');
        if(empty($BomID)) return [10002,'删除失败'];
        $map=['bom_id'=>$BomID,'user_id'=>cookie('uid')];
        $result=$this->where($map)->delete();
        if(!$result) return [10003,'删除失败'];
        return [0,'删除成功'];
    }

    /**
     * 手动创建BOM单
     * @return [type] [description]
     */
    public function AddBom($Login=false){
        $Login===false ? $save=['gid'=>cookie('gid'),'user_id'=>0]:$save['user_id']=cookie('uid');//判断是否登录
        $gid=cookie('gid');
        $BomName=I('bom_name');
        if(empty($BomName)) return [10001,'请输入BOM单名称'];
        if($Login===false){//限制未登录用户
            $map=['status'=>['eq',1], 'user_id'=>['eq',0], 'gid'=>['eq',$gid]];
            $Find=$this->where($map)->field('bom_id')->find();
            if($Find) return [10002,'未登录用户只能创建一个BOM单'];
        }
        $save['bom_name']=$BomName;
        $save['upload_ip']=get_client_ip();
        $save['add_time']=time();
        $result=$this->add($save);
        if(!$result) return [10003,'创建失败'];
        return [0,'创建成功',$result];
    }

    /**
     * UpdateBom
     * @return [type] [description]
     */
    public function UpdateBom($Login=false){
        $Login===false ? $map=['gid'=>['eq',cookie('gid')],'user_id'=>['eq',0]]:$map['user_id']=cookie('uid');//判断是否登录
        $map['status']=1;
        $BomID=I('bom_id');
        $BomName=I('bom_name');
        $map['bom_id']=$BomID;
        if(empty($BomID) || empty($BomName)) return [10001,'修改失败'];
        $result=$this->where($map)->save(['bom_name'=>$BomName,'update_time'=>time()]);
        if(!$result) return [10002,'修改失败'];
        return [0,'修改成功'];
    }

    /**
     * 统计BOM单的数量
     * @return [type] [description]
     */
    public function CountBomNum($Login=false){
        $Login===false ? $map=['gid'=>['eq',cookie('gid')],'user_id'=>['eq',0]]:$map['user_id']=cookie('uid');//判断是否登录
        $map['status']=1;
        $Count=$this->where($map)->count('bom_id');
        return [0,'成功',$Count];
    }

    /**
     * BOM单的信息
     * @return [type] [description]
    */
    public function BomItemsInfo($Login=false){
        $Login===false ? $map=['gid'=>['eq',cookie('gid')],'user_id'=>['eq',0]]:$map['user_id']=cookie('uid');//判断是否登录
        $map['status']=1;
        $BomID=I('bom_id');
        $map['B.bom_id']=$BomID;
        if(empty($BomID)) return [10001,'没有查找到数据'];
        $Count=$this->table('lie_bom_items')->field('bom_id, sum(if(bom_status=1,1,null)) as xh,
        sum(if(bom_status=2 ,1,null)) as xj,sum(if(bom_status=3,1,null)) as xz,count(bom_id) as num,sum(if(bom_status=1 and goods_type=1,1,null)) as xhzy,sum(if(bom_status=1 and goods_type=2,1,null)) as xhly')->where(['status'=>1])->group('bom_id')->select(false);
        $info=$this->alias('B')->where($map)->join('LEFT JOIN ('.$Count.') I ON I.bom_id = B.bom_id')->field('bom_name,B.bom_id,refresh_time,xh,xj,xz,num,xhzy,xhly')->find();
        if(!$info) return [10002,'未查找到BOM单'];
        return [0,'成功',$info];
    }

    /**
     * BOM单详情列表
     * @return [type] [description]
     */
    public function BomItemsList($Login=false){
        $Login===false ? $map=['gid'=>['eq',cookie('gid')],'user_id'=>['eq',0]]:$map['user_id']=cookie('uid');//判断是否登录
        $map['status']=1;
        $BomID=I('bom_id');
        $map['bom_id']=$BomID;
        if(empty($BomID)) return [10001,'没有查找到数据'];
        $find=$this->where($map)->field('bom_id')->find();
        if(!$find) return [10002,'没有查找到数据'];
        $BomStatus=I('bom_status');
        $GoodsType=I('goods_type');
        if($BomStatus) $map['bom_status']=$BomStatus;
        if($GoodsType) $map['goods_type']=$GoodsType;
        unset($map['gid'],$map['user_id']);
        $list=$this->table('lie_bom_items')->where($map)
            ->field('id,goods_name,num,brand_name,goods_id,goods_type,delivery_place,brand_status,bom_status')
            ->order('id','asc')
            ->page(empty(I('page'))?1:I('page'),empty(I('limit'))?10:I('limit'))->select();
        if(!$list) return [10003,'没有查找到数据'];
        $count=$this->table('lie_bom_items')->where($map)
            ->field('bom_id')->count();
        $SkuID=authkey();
        foreach ($list as $k=>$v){
            if(!empty($v['goods_id'])){
                $SkuID['goods_id'].=$v['goods_id'].',';
            }
        }
        $SkuID['goods_id']=rtrim($SkuID['goods_id'],',');
        
        #todo 判断新客价
        $user_id = cookie('uid');
        if ($user_id) {
            $redis = new \Think\Cache\Driver\Redisrw();
            $user_info = json_decode($redis->hget('api_user', $user_id), true);
            $newCustomer = isset($user_info['is_new']) && $user_info['is_new'] == 0 ? true : false; // 新客户显示新客价
            $member = true;
        } else {
            $newCustomer = true; // 未登录显示新客价
            $member = false;
        }
        $SkuID["power"]['newCustomer'] = $newCustomer ? "true":"false"; #未登录都传true，登录之后需要判断是否有资格用新客价
        $SkuID["power"]['member'] = $member ? "true":"false"; #未登录传false ,登录传true


        if(!empty($SkuID['goods_id'])){
            $url =$this->SkuListUrl().'/synchronization';
			$SkuList = post_curl($url,$SkuID);
            $SkuList=json_decode($SkuList,true);
            if(!empty($SkuList['data'])){
                foreach ($list as $k=>$v){
                    if(!empty($v['goods_id']) && !empty($SkuList['data'][$v['goods_id']])){
                        $list[$k]['sku']=$SkuList['data'][$v['goods_id']];
                        if($SkuList['data'][$v['goods_id']]['is_buy']==1 && $v['bom_status']!=1){
                            $this->table('lie_bom_items')->where(['id'=>$v['id']])->save(['bom_status'=>1]);
                        }elseif($SkuList['data'][$v['goods_id']]['is_buy']==0 && $v['bom_status']==2){
                            $this->table('lie_bom_items')->where(['id'=>$v['id']])->save(['bom_status'=>2]);
                        }
                    }
                }
            }
        }

        return [0,$count,$list];
    }

    /**
     * 单个新增BOM商品
     * @return [type] [description]
     */
    public function AddBomItemsOne($Login=false){
        $Login===false ? $map=['gid'=>['eq',cookie('gid')],'user_id'=>['eq',0]]:$map['user_id']=cookie('uid');//判断是否登录
        $map['status']=1;
        $BomID=I('bom_id');
        $GoodsName=I('goods_name');
        $BrandName=I('brand_name');
        $num=I('num');
        if(empty($BomID) || empty($GoodsName) || empty($num)) return [10001,'新增失败'];
        $find=$this->where($map)->field('bom_id')->find($BomID);
        if(!$find) return [10002,'新增失败'];
        $ItemsModel=D('BomItems');
        $data=[
            'goods_name'=>$GoodsName,
            'brand_name'=>$BrandName,
            'num'=>$num,
            'bom_id'=>$BomID,
            'add_time'=>time()
        ];
        $result=$ItemsModel->AddBomItemsOne($data);
        $Url=$this->SearchUrl().'/search/bom/findMatchSku';
        post_curl($Url,['id'=>$result,'num'=>$num,'brand_name'=>$BrandName,'goods_name'=>$GoodsName]);
        if(!$result) return [10003,'新增商品失败'];
        return [0,'新增商品成功'];
    }

    /**
     * 更换交货地
     * @return [type] [description]
     */
    public function ChangeDelivery($Login=false){
        $Login===false ? $map=['gid'=>['eq',cookie('gid')],'user_id'=>['eq',0]]:$map['user_id']=cookie('uid');//判断是否登录
        $map['status']=1;
        $BomID=I('bom_id');
        $ID=I('id');
        $DeliveryPlace=I('delivery_place');
        if(empty($BomID) || empty($ID)) return [10001,'更换交货地失败'];
        $find=$this->where($map)->field('bom_id')->find($BomID);
        if(!$find) return [10002,'更换交货地失败'];
        $ItemsModel=D('BomItems');
        $ItemsMap=['id'=>$ID];
        $Update=['delivery_place'=>$DeliveryPlace,'update_time'=>time()];
        $result=$ItemsModel->ChangeDelivery($ItemsMap,$Update);
        if(!$result) return [10003,'更换交货地失败'];
        return [0,'修改成功'];
    }

    /**
     * 修改详情的型号与数量
     * @return [type] [description]
     */
    public function UpdateBomItems($Login=false){
        $Login===false ? $map=['gid'=>['eq',cookie('gid')],'user_id'=>['eq',0]]:$map['user_id']=cookie('uid');//判断是否登录
        $map['status']=1;
        $collert=I('param.');
        if(empty($collert['bom_id']) || empty($collert['id']) || empty($collert['goods_name']) || empty($collert['num'])) return [10001,'修改失败'];
        $Find=$this->where($map)->field('bom_id')->find($collert['bom_id']);
        if(!$Find)return [10002,'没有查找到BOM单'];
        $ItemsModel=D('BomItems');
        $FindItems=$ItemsModel->where(['id'=>$collert['id']])->field('brand_name')->find();
        $result=$ItemsModel->UpdateBomItems(['id'=>$collert['id']],$collert);
        if(!$result)  return [10003,'修改失败'];
        $Url=$this->SearchUrl().'/search/bom/findMatchSku';
        $result=post_curl($Url,['id'=>$collert['id'],'num'=>$collert['num'],'goods_name'=>$collert['goods_name'],'brand_name'=>$FindItems['brand_name']]);
        return [0,'修改成功',$result];
    }

    /**
     * 删除单个商品
     * @return [type] [description]
     */
    public function DeleteBomItems($Login=false){
        $Login===false ? $map=['gid'=>['eq',cookie('gid')],'user_id'=>['eq',0]]:$map['user_id']=cookie('uid');//判断是否登录
        $map['status']=1;
        $BomID=I('bom_id');
        $ID=I('id');
        if(empty($BomID) || empty($ID)) return [10001,'删除失败'];
        $Find=$this->where($map)->field('bom_id')->find($BomID);
        if(!$Find) return [10002,'没有查找到这个BOM单'];
        $ItemsModel=D('BomItems');
        $result=$ItemsModel->DeleteBomItems(['id'=>$ID]);
        if(!$result) return [10003,'删除失败'];
        return [0,'删除成功'];
    }

    /**
     * 导出匹配结果，生产缓存
     * @return [type] [description]
     */
    public function ApiExportBomItems($Login=false){
        $Login===false ? $map=['gid'=>['eq',cookie('gid')],'user_id'=>['eq',0]]:$map['user_id']=cookie('uid');//判断是否登录
        $map['status']=1;
        $BomID=I('bom_id');
        if(empty($BomID)) return [10001,'导出失败'];
        $Find=$this->where($map)->field('bom_id')->find($BomID);
        if(!$Find) return [10002,'没有查找到这个BOM单'];
        $list=$this->table('lie_bom_items')->where(['bom_id'=>$BomID,'status'=>1])
            ->field('goods_id,num')
            ->select();
        if(!$list) return [10003,'没有查找到数据'];
        $SkuID='';
        foreach ($list as $k=>$v){
            if(!empty($v['goods_id'])){
                $SkuID['goods_id'].=$v['goods_id'].',';
            }
        }
        $SkuID['goods_id']=rtrim($SkuID['goods_id'],',');
        if(!empty($SkuID['goods_id'])){
            $url =$this->SkuListUrl().'/synchronization';
            $SkuList=json_decode(post_curl($url,$SkuID),true);
            if(!empty($SkuList['data'])){
                foreach ($list as $k=>$v){
                    unset($list[$k]);
                    if(!empty($v['goods_id']) && !empty($SkuList['data'][$v['goods_id']])){
                        $list[$v['goods_id']]=$SkuList['data'][$v['goods_id']];
                        $list[$v['goods_id']]['num']=$v['num'];
                    }
                }
                S('Bom_ExportBomItems_'.$BomID,$list,100);//缓存100秒
            }else{
                return [10005,'导出失败'];
            }
        }else{
            return [10004,'没有数据可以导出'];
        }
        return [0,'成功'];
    }

    /**
     * 导出匹配结果
     * @return [type] [description]
     */
    public function ExportBomItems($Login=false){
        $Login===false ? $map=['gid'=>['eq',cookie('gid')],'user_id'=>['eq',0]]:$map['user_id']=cookie('uid');//判断是否登录
        $map['status']=1;
        $BomID=I('bom_id');
        if(empty($BomID)) {
            echo "<script>alert('导出失败!');history.back();</script>";
            die;
        };
        $Find=$this->where($map)->field('bom_id,bom_name')->find($BomID);
        if(!$Find) {
            echo "<script>alert('导出失败!');history.back();</script>";
            die;
        }
        $info=S('Bom_ExportBomItems_'.$BomID);//读取缓存
        if(!$info) {
            echo "<script>alert('导出失败!');history.back();</script>";
            die;
        }
        //导入第三方扩展类库
        Vendor('PHPExcel.PHPExcel');
        $objPHPExcel = new \PHPExcel(); //创建PHPExcel对象

        //设置表头
        $key = ord("A");
        $headArr=array("型号","商品类型","数量","品牌","供应商",'库存','起订量','MPQ','大陆货期','香港货期','阶梯','人民币价格','美金价格');
        foreach($headArr as $v){
            $colum = chr($key);
            $objPHPExcel->setActiveSheetIndex(0) ->setCellValue($colum.'1', $v);
            $key += 1;
        }
        $column = 2;
        $objPHPExcel->getDefaultStyle()->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
        $objPHPExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $objActSheet = $objPHPExcel->getActiveSheet()->setTitle($Find['bom_name']);
        foreach($info as $key => $rows){ //行写入
            $span = ord("A");
            if(!isset($rows['goods_type'])) continue;
            $delivery_time_us='';
            if($rows['goods_type']==0 || $rows['goods_type']==3){//自营
                $delivery_time_cn='现货';
            }else{
                $delivery_time_cn=$rows['cn_delivery_time'];
                $delivery_time_us=$rows['hk_delivery_time'];
            }
            $Arr=[
                'goods_name'=>$rows['goods_name'],
                'goods_type'=>$rows['goods_type']==0 || $rows['goods_type']==3 ? '自营':'联营',
                'num'=>$rows['num'],
                'brand_name'=>$rows['brand_name'],
                'supplier_name'=>$rows['supplier_name'],
                'stock'=>$rows['stock'],
                'moq'=>$rows['moq'],
                'mpq'=>$rows['mpq'],
                'delivery_time_cn'=>$delivery_time_cn,
                'delivery_time_us'=>$delivery_time_us
            ];
            if(is_array($rows['ladder_price'])){
                foreach ($rows['ladder_price'] as $k => $v) {
                    $span = ord("A");
                    $Arr['purchases'] = empty($v['purchases']) ? '' : $v['purchases'];
                    $Arr['price_cn'] = empty($v['price_cn']) ? '' : $v['price_cn'];
                    $Arr['price_us'] = empty($v['price_us']) ? '' : $v['price_us'];
                    foreach($Arr as $keyName=>$value) {// 列写入
                        $j = chr($span);
                        $objActSheet->setCellValue($j.$column, $value)->getStyle($j.$column)->getAlignment()->setWrapText(true)->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
                        $span++;
                    }
                    $column++;
                }
            }else{
                foreach($Arr as $keyName=>$value){// 列写入
                    $j = chr($span);
                    $objActSheet->setCellValue($j.$column, $value)->getStyle($j.$column)->getAlignment()->setWrapText(true)->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
                    $span++;
                }
                $column++;
            }
        }

        //设置属性
        $fileName=iconv("utf-8", "gb2312", $Find['bom_name'].'.xls');;
        ob_end_clean();
        ob_start();
        header('Content-Type: application/vnd.ms-excel');
        header("Content-Disposition: attachment;filename=\"$fileName\"");
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $objWriter->save('php://output'); //文件通过浏览器下载
        exit;
    }

}