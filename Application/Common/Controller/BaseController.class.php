<?php
namespace Common\Controller;

use Think\Controller;
use Crm\Model\OemsUserInfoModel;
use Help\Model\FeedbackModel;
use Crm\Model\UserModel;

class BaseController extends Controller
{
    public $sa = null;

    public function _initialize()
    {
        defined('START_MICROTIME') or define('START_MICROTIME', microtime(true));//接口请求开始计时器
        //判断是否存在方法
        if (!method_exists($this, ACTION_NAME)) {
            $trace = debug_backtrace(2,2);
            if (class_basename($trace[1]['class']) == CONTROLLER_NAME . C('DEFAULT_C_LAYER')) {//内部调用不判断
                $this->_empty();
            }
        }
        $this->setHeader();
        if (!$this->auth() &&
            !in_array(strtolower(CONTROLLER_NAME.'/'.ACTION_NAME), C('DISABLE_CSRF')) &&
            strtolower(CONTROLLER_NAME) != 'intraface') {

            if(empty(I('is_liexin')) && I("ygagatetffafa") != 1 && I("pf") != 2){
                $this->csrf();
            }


        }
        $this->sessionNull();

        //初始化神策 SDK
        $this->SA_SERVER_URL = C('SA_SERVER_URL');
        $consumer = new \BatchConsumer($this->SA_SERVER_URL);
        $this->sa = new \SensorsAnalytics($consumer);
    }

    //生成csrf
    protected function addCsrf($gid = "")
    {
        $key2 = 'api_csrf_' . $gid;//新键  保留 旧键过渡
        $csrf = md5(md5(time() . rand()) . 'csrf');
        S_redis($key2, $csrf, array('expire' => 86400));//基础秘钥
        return $csrf ;
    }

    /*
     * 生成gid
     * @param $types 1 随机 2 固定
     */
    protected function addGid($type = 1)
    {
        if ($type == 1 ){
            return md5(md5(time() . rand()) . 'usersign');
        }
        $gid = cookie('gid');
        if (empty($gid)) {
            $gid = $type == 1 ? md5(md5(time() . rand()) . 'usersign') : md5("gagdaga&gaddga38532*gaga");
        }
        return $gid;
    }

    public function __destruct()
    {
        // TODO: Implement __destruct() method.
        if($this->sa){
            $this->sa->close();
        }
    }

    /**
     * 空方法
     * @return [type] [description]
     */
    public function _empty()
    {
        \LogReport::write(\LogReport::anlyError('HTTP/1.1 404 Not Found', __FILE__, __LINE__, 404, 'Common\Controller\BaseController::_initialize', null, null, getUrl(), json_encode($_REQUEST)));
        header("HTTP/1.1 404 Not Found");
        exit;
    }

    /**
     * 权限验证
     * @param  array  $continue [description]
     * @return [type]           [description]
     */
    protected function authRule($continue = array())
    {
        if ($this->auth() || in_array(strtolower(ACTION_NAME), $continue)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 跨域头部设置
     */
    private function setHeader()
    {
        $origin = isset($_SERVER['HTTP_ORIGIN'])? $_SERVER['HTTP_ORIGIN'] : '';
        $origin_arr = explode('//', $origin);
        $allow_origin = C('ALLOW_ORIGIN');
        if(in_array($origin_arr['1'], $allow_origin)){
            header('Access-Control-Allow-Origin:'.$origin);
            header('Access-Control-Allow-Credentials:true');
            header('Access-Control-Allow-Methods:POST');
//            header('Access-Control-Allow-Headers:x-requested-with,content-type');
            header('Access-Control-Allow-Headers:x-requested-with,content-type,hunt_types');
        }
        header("Content-type: text/html; charset=utf-8");
    }

    /**
     * 统一格式返回
     * @param  integer $code   [description]
     * @param  string  $msg    [description]
     * @param  array   $extend [description]
     * @return [type]          [description]
     */
    protected function apiReturn($code = 0, $msg = '', $extend = array())
    {
        $data = array(
            'err_code' => $code,
            'err_msg' => $msg,
            'data' => $extend,
        );
        if ($code != 0) {
            // $this->apiLog($code, $msg, $extend);
            if ($code > 0) {
                unset($data['data']);
            }
            $data['err_code'] = abs($data['err_code']);
        }

        //判断是否内部调用，内部调用不输出只返回
        $near_trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        $backtrace = array_pop($near_trace);
        $backtrace_arr = explode('\\', $backtrace['class']);
        $backtrace_name = array_pop($backtrace_arr);
        $index = strpos($backtrace_name, C('DEFAULT_C_LAYER'));

        $comp1 = strtolower(substr($backtrace_name, 0, $index).':'.$backtrace['function']);
        $comp2 = strtolower(CONTROLLER_NAME.':'.ACTION_NAME);
        $comp = explode(':', $comp1);

        if ($comp1 == $comp2 || $comp[0] == 'base' || $comp[1] == '_initialize') {
            // 直接请求的控制器 或 构造函数终止
            if ($data['err_code'] != 0) {
                $file = !empty($near_trace[0]['file']) ? $near_trace[0]['file'] : '';
                $line = !empty($near_trace[0]['line']) ? $near_trace[0]['line'] : 0;
                $class = isset($backtrace['class']) ? $backtrace['class'] : '';
                $func = isset($backtrace['function']) ? $backtrace['function'] : '';
                $method = trim($class . '::' . $func, '::');
                $request_url = getUrl();
                $request = !empty($_REQUEST) ? json_encode($_REQUEST) : '';
                $log = \LogReport::anlyError($msg, $file, $line, $data['err_code'], $method, null, null, $request_url, $request);
                \LogReport::write($log);

                if (is_array($data['data']) || empty($data['data'])) {
                    $data['data']['unique'] = $log['unique'];
                }
            }
            $callback = I('get.callback', null);
            $callback_type = is_null($callback) ? 'json' : 'jsonp';
            // \Think\Log::save();
            return $this->ajaxReturn($data, $callback_type,256);
        }
        //内部逻辑调用返回
        return $data;
    }

    protected function apiLog($code, $msg, $log)
    {
        $disable_log = C('DISABLE_LOG');
        if (in_array(strtolower($url), $disable_log)) {
            return;
        }
        $param = I('request.');
        $cookie = cookie();
        $data = array(
            'param' => $param,
            'cookie' => $cookie,
            'log' => $log,
        );
        \Think\Log::record(sprintf('code=%s, msg=%s, data=%s;', $code, $msg, json_encode($data)));
    }

    /**
     * 内部免验证通过
     * @return [type] [description]
     */
    protected function auth()
    {
        $k1 = I('request.k1', '', '');
        $k2 = I('request.k2', '', '');
        $cancel_time_verify = I('request.cancel_time_verify', 0); // 取消验证时间，1-是
        $key = C('SUPER_AUTH_KEY');
        if (!$cancel_time_verify && !empty($k1) && $k1 < time() - 600) {
            return false;
        }
        $pwd = pwdhash($k1, $key);
        if ($k2 == $pwd) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * CSRF验证
     * @return [type] [description]
     */
    public function csrf()
    {
        #todo 2022.12.22 增加跳过验证
        if (i("hcy_test") == "1122"){
            return true;
        }
        if (i("request.hcy_test") == "1122"){
            return true;
        }
        $user_id = cookie('uid');
        $csrf = cookie('csrf');
        $gid = cookie('gid');
        empty($csrf) && $csrf = I('request.csrf', '');
        $csrf_key = S_csrf($_COOKIE['PHPSESSID']); //旧键
        $csrf_key2 = S_csrf($gid); //新键
        if (empty($csrf) || ($csrf_key != $csrf && $csrf_key2 != $csrf)) {
            return $this->apiReturn(43003, '系统错误，建议清除浏览器缓存后重试');
        }
    }

    /**
     * 获取配置信息
     * @return [type] [description]
     */
    public function conf()
    {
        $key = I('request.key', '');
        $val = I('request.val', '');
        if ($val !== '') {
            $res = C($key.'.'.$val);
        } else {
            $res = C($key);
        }
        return $this->apiReturn(0, '', $res);
    }

    /**
     * 请求获取各模块配置信息
     * @param  [type] $module [description]
     * @param  [type] $key    [description]
     * @param  [type] $val    [description]
     * @return [type]         [description]
     */
    public function getConf($module, $key, $val = '')
    {
        $data = array(
            'key' => $key,
            'val' => $val,
            'k1' => $_SERVER['REQUEST_TIME'],
            'k2' => pwdhash($_SERVER['REQUEST_TIME'], C('SUPER_AUTH_KEY')),
        );
        $res = post_curl(API_DOMAIN.'/'.$module.'/conf', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
            return $res['data'];
        }
        return null;
    }

    /**
     * session uid清空
     * @return [type] [description]
     */
    public function sessionNull()
    {
        $cookie_uid = cookie('uid');
        $session_uid = session('uid');
        if (!empty($session_uid) && empty($cookie_uid)) {
            session('uid', null);
        } elseif (!empty($session_uid) && !empty($cookie_uid) && $session_uid != $cookie_uid) {
            session('uid', null);
        }
    }

    /**
     * 返回内部curl使用cookie字符串
     * @param  array  $data [description]
     * @return [type]       [description]
     */
    public function loginCookie($data = array())
    {
        $cookie[C('COOKIE_PREFIX').'gid'] = cookie('gid') ? cookie('gid') : '';
        $cookie[C('COOKIE_PREFIX').'uid'] = cookie('uid') ? cookie('uid') : 0;
        $cookie[C('COOKIE_PREFIX').'skey'] = cookie('skey') ? cookie('skey') : '';
        $cookie[C('COOKIE_PREFIX').'lskey'] = cookie('lskey') ? cookie('lskey') : '';
        $cookie = array_merge($cookie, $data);
        $cookie_str = 'Cookie:'.str_replace('&',';',http_build_query($cookie));
        return $cookie_str;
    }

    /**
     * 检测登录
     * @param  boolean $hard 强校验模式
     * @return [type]        [description]
     */
    protected function checkLogin($hard = true)
    {
        $user_id = I('request.uid', 0, 'intval');
        $skey = I('request.skey', '', 'trim');
        $lskey = I('request.lskey', '', 'trim');

        if (I("hcy_test") == 1122){ //调试跳过登录
            return ['err_code'=>0];
        }

        $data = array();
        !empty($user_id) && $data['uid'] = $user_id;
        !empty($skey) && $data['skey'] = $skey;
        !empty($lskey) && $data['lskey'] = $lskey;
        $data = array_merge($data, authkey());

//         $res = post_curl(API_DOMAIN.'/login/check', $data, array($this->loginCookie()));
        $res = A('Home/Login')->check();
        if (!empty($res)) {
            // $res = json_decode($res, true);
            if ($res['err_code'] != 0) {
                //内部请求清空相关cookie
                if ($res['err_code'] == 11030) {//强登录态校验失败、弱登录态校验通过
                    cookie('skey', null);
                    if (!$hard) {//弱校验模式，允许弱登录校验成功返回通过指令
                        $res['err_code'] = 0;
                        $res['err_msg'] = '';
                    }
                } else {//强弱登录态校验失败
                    cookie('uid', null);
                    cookie('skey', null);
                    cookie('lskey', null);
                }
            }
        }
        return $res;
    }

    /**
     * 行为数据推送
     * @return [type] [description]
     */
    public function behavior_push($behavior = 1, $error = 0, $param = array(), $event = 1, $clear = null)
    {
        if (C('BEHAVIOR_LOG')) {
            //判断是否正式站
            $parseUrl = parse_url($_SERVER['HTTP_REFERER']);
            list($protocl) = explode('.', $parseUrl['host'], 2);

            defined('START_MICROTIME') or define('START_MICROTIME', microtime(true));
            $RbmqModel = D('Common/Rbmq');
            if ($error == 0) {
                $result = 1;
                $arr = $param;
                is_null($clear) && $clear = true;
            } else {
                $result = -1;
                if (is_string($param)) {
                    $arr['err_code'] = $error;
                    $arr['err_msg'] = $param;
                }
                is_null($clear) && $clear = false;
            }
            $scene = get_scene($behavior, '', $clear);
            $data = behavior_data($scene, $behavior, $result, $arr, $event);
            if (I("debug") == 3344){
                print_r($data);
            }
            $res = false;
            try {
                $res = $RbmqModel->queue(C('QUEUE_BEHAVIOR'))->route(C('QUEUE_BEHAVIOR'))->push(json_encode($data));
                if (!$res) {
                    \LogReport::write(\LogReport::anlyError('推送行为日志记录失败:'.json_encode($data), __FILE__, __LINE__, 43005, __METHOD__));
                }
            } catch (\Exception $e) {
                \LogReport::write(\LogReport::anlyError('推送行为日志记录失败:'.json_encode($data), __FILE__, __LINE__, 43005, __METHOD__));
            }
        } else {
            cookie(C('BEHAVIOR_KEY'), null);
        }
        return $res;
    }

    /**
     * 上报数据 sensors
     * @param array $properties
     * @return mixed
     */
    public function report_data($properties=array()){
        return true;  //todo 2022.1.12 停止神策
        if (C('EVENT_REPORT')){
            $RbmqModel = D('Common/Rbmq');
            if (empty($properties)){
                return false;
            }
            try {
                //覆盖默认时间
                $properties['$time'] = (int)(time()*1000);
                $RbmqModel->queue(C('QUEUE_REPORT_DATA'))->push($properties, C('QUEUE_REPORT_DATA'));
            } catch (\Exception $e) {

            }
        }else{
            return false;
        }
    }



    /**
     * 监控api接口
     * 上报elk
     */
    public function pushReportMonitorLog($pushdata=[]){

        try{
            if(strrpos(API_DOMAIN,"szapi") !== false){
                return true;
            }
            $request = $_REQUEST;
            if(isset($request['pwd'])){
                $request['pwd'] = "******";
            }elseif(isset($request['passwd'])){
                $request['passwd'] = "******";
            }elseif(isset($request['password'])){
                $request['password'] = "******";
            }elseif(isset($request['pay_password'])){
                $request['pay_password'] = "******";
            }


            //接口类型
            $push["interface_type"] = isset($pushdata['interface_type']) ? strval($pushdata['interface_type']) : '';
            //接口地址
            $push["access_url"] = isset($pushdata['access_url']) ? strval($pushdata['access_url']) : "";
            $push["request_params"] = !empty($request) ? json_encode($request) : '';
            $push["err_msg"] = isset($pushdata['err_msg']) ? strval($pushdata['err_msg']) : '';
            $push["err_code"] = isset($pushdata['err_code']) ? strval($pushdata['err_code']) : '';
            $push["uid"] = isset($pushdata['uid']) ? strval($pushdata['uid']) : '';
            $push["user_name"] = isset($pushdata['user_name']) ? strval($pushdata['user_name']) : '';
            $push["user_ip"] = get_client_ip(0, true);
            $push["remark"] = isset($pushdata['remark']) ? strval($pushdata['remark']) : '';
            $push["create_time"] = time();
            $push["create_time_str"] = date("Y-m-d h:i:s");

            if(cookie("uid")){
                $push["uid"] = cookie("uid");
                $userInfo = S_user(cookie("uid"));
                if($userInfo && $userInfo['mobile']){
                    $push["user_name"] = $userInfo['mobile'];
                }
            }

            $near_trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
            $near_trace_str = is_array($near_trace) ? json_encode($near_trace) : '';
            $push["remark"] .= $near_trace_str;

            if(!$push["access_url"]){
                $push["access_url"] = getUrl();
            }

            $RbmqModel = D('Common/Rbmq');
            $a= $RbmqModel->connect('RBMQ_MSG_CONFIG')->exchange(C("Log_Monitor_Behavior_Exchange"))->queue(C("Log_Monitor_Behavior_Queue"))
                ->exchangeBind(C("Log_Monitor_Behavior_Exchange"), C("Log_Monitor_Behavior_Queue"));
    //        dump($a);
            $bk = $a->push($push, C("Log_Monitor_Behavior_Queue"));
        }catch(\Exception $e){
            return true;
        }
    }


    public function getUidByAdmin()
    {
        $uid = 0;
        if ($this->auth()) {
            $uid = I('request.user_id', 0, 'intval');
        }

        if (!$uid || empty($uid)) {
            $uid = cookie('uid');
        }

        return $uid ? $uid : 0;
    }

    public function getUcidByAdmin()
    {
        $ucid = 0;
        if ($this->auth()) {
            $ucid = I('request.uc_id', 0, 'intval');
        }

        if (!$ucid || empty($ucid)) {
            $ucid = cookie('ucid');
        }

        return $ucid ? $ucid : 0;
    }

}
