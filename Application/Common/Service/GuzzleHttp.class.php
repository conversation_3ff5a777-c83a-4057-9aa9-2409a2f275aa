<?php
namespace Common\Service;


// 其他必要文件（根据实际调用情况补充）
 

 
class GuzzleHttp
{
    protected $client;
 
    public function __construct($baseUri = '')
    {
        $this->client = new  \GuzzleHttp\Client([
            'base_url' => $baseUri, // Guzzle 5.x 用 base_url
            'defaults' => [
                'timeout' => 30,
                'verify' => false   // 禁用 SSL 验证（生产环境建议开启）
            ],
           
        ]);
    }
 
    /**
     * GET 请求
     */
    public function get($url, $params = [], $headers = []) {
        try {
            $options = ['headers' => $headers];
            if (!empty($params)) {
                $options['query'] = $params;
            }
            
            $response = $this->client->get($url, $options)->getBody();
            return json_decode($response, true);
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            return false;
        }
    }
 
    public function post($url, $data = [], $headers = [], $isJson = false) {
        try {
            $options = ['headers' => $headers];
            
            if ($isJson) {
                // 发送 JSON 数据
                $options['headers']['Content-Type'] = 'application/json';
                $options['body'] = json_encode($data);
            } else {
                // 发送表单数据
                $options['form_params'] = $data;
            }
            
            $response = $this->client->post($url, $options)->getBody();
            return json_decode($response, true);
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            return false;
        }
    }
 
    /**
     * POST 请求（JSON 格式）
     */
    public function postJson($url, $data = [], $headers = [])
    {
        $headers['Content-Type'] = 'application/json';
        try {
            $request = $this->client->createRequest('POST', $url, $headers);
            $request->setBody(json_encode($data)); // 设置 JSON 请求体
            $response = $request->send();
            return json_decode($response->getBody(), true);
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            \Think\Log::write("Guzzle POST JSON Error: " . $e->getMessage(), 'ERR');
            return false;
        }
    }

    /**
     * 解析响应结果
     */
    private function parseResponse($response) {
        return [
            // 'status' => $response->getStatusCode(),
            // 'headers' => $response->getHeaders(),
            // 'body' => json_decode($response->getBody(), true) ?: $response->getBody()
        ];
    }
    
    /**
     * 处理异常
     */
    private function handleException($e) {
        $response = $e->getResponse();
        return [
            'status' => $response ? $response->getStatusCode() : 500,
            'error' => $e->getMessage(),
            'body' => $response ? $response->getBody() : null
        ];
    }
}