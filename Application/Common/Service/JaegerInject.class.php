<?php
namespace Common\Service;

use <PERSON><PERSON><PERSON>\Config as Jconfing;
use OpenTracing\Formats;
use OpenTracing\Reference;


class JaegerInject
{

    protected $dsn = '192.168.1.235:6831';

    protected $serviceName;
    public static $spanList = [];
    protected $client;
    protected $tracer;
    protected $clientTracer;
    protected $uname;//开发环境 window mac  linux
    public static $parentSpanName = null;
    public static $instance = null;



    private function __construct(){

    }


    private function __clone(){

    }

    public static function getInstance()
    {
        if(! (self::$instance instanceof self) )
        {
            self::$instance = new self();
        }
        return self::$instance;
    }


    protected  function getClientIp()
    {
        $ip = '0.0.0.0';
        if (getenv('HTTP_CLIENT_IP')) {
            $ip = getenv('HTTP_CLIENT_IP');
        }
        if (getenv('HTTP_X_REAL_IP')) {
            $ip = getenv('HTTP_X_REAL_IP');
        } elseif (getenv('HTTP_X_FORWARDED_FOR')) {
            $ip = getenv('HTTP_X_FORWARDED_FOR');
            $ips = explode(',', $ip);
            $ip = $ips[0];
        } elseif (getenv('REMOTE_ADDR')) {
            $ip = getenv('REMOTE_ADDR');
        }

        return $ip;
    }


    public function getSpanName($spanName){
        if(!$this->uname){
            return "";
        }

        return isset(static::$spanList[$spanName]) ? static::$spanList[$spanName]["current_span"] : "";
    }


    public function setTag($spanName,$key="",$value=""){
        if(!$this->uname){
            return "";
        }
        if(!isset(static::$spanList[$spanName])){
            return ;
        }
        $info = static::$spanList[$spanName];
        $span = $info['current_span'];
        $span->setTag($key,$value);
    }


    /*
     * 记录链路追踪日志
     */
    public function log($spanName,$key="",$value=""){
        if(!$this->uname){
            return "";
        }
        if(!isset(static::$spanList[$spanName])){
            return ;
        }
        $info = static::$spanList[$spanName];
        $span = $info['current_span'];
        if($key && $value){
            $span->log([$key=>$value]);
        }else if($key && !$value){
            $span->log(["message"=>$key]);
        }
    }


    /*
     * 創建父span
     */
    public function init($parentSpanName=""){

        $this->serviceName = C("OPENTRACING_JAEGER_SERVERNAME");
        $this->client = Jconfing::getInstance();
        Jconfing::$propagator = \Jaeger\Constants\PROPAGATOR_JAEGER;
        $this->client->gen128bit();
        $this->tracer = $this->client->initTracer($this->serviceName, C("OPENTRACING_JAEGER_DNS"));

        if(!static::$parentSpanName && !$parentSpanName){
            static::$parentSpanName = "未知的spanName";
            if(isset($_SERVER['REQUEST_URI']) && $_SERVER['REQUEST_URI']){
                $requestUrl = explode("?",$_SERVER['REQUEST_URI']);
                static::$parentSpanName = !empty($requestUrl) ? $requestUrl[0] : static::$parentSpanName;
            }

        }

        if($parentSpanName && !static::$parentSpanName){
            static::$parentSpanName =  $parentSpanName;
        }

        if(!isset(static::$spanList[static::$parentSpanName])) {
            if(!checkPhpUname()){
                $this->uname = false;
                return ;
            }else{
                $this->uname = true;
            }
            $this->startPrentSpan();
        }
    }


    public function simpleInit($parentSpanName="",$spanName=""){
        $this->init($parentSpanName);
        $this->start($spanName);
        $injectHeaders = $this->inject($spanName);
        return $injectHeaders;
    }

    public function simpleInject($spanName,$preSpanName=""){
        if($preSpanName){
            $preSpanName = $this->getSpanName($preSpanName);
        }
        $this->start($spanName,$preSpanName);
        $injectHeaders = $this->inject($spanName);
        return $injectHeaders;
    }

    protected function startPrentSpan(){
        $parentSpanName = static::$parentSpanName;
        $parentContext = $this->tracer->extract(Formats\TEXT_MAP, $this->getAllHeaders());
//        dump($parentContext);
        if (!$parentContext) {
            $serverSpan = $this->tracer->startSpan($parentSpanName);
        } else {
            $serverSpan = $this->tracer->startSpan($parentSpanName, ['references' => [
                Reference::create(Reference::FOLLOWS_FROM, $parentContext),
                Reference::create(Reference::CHILD_OF, $parentContext)
            ]]);
        }
        $serverSpan->setTag("http_host",$this->getClientIp());
        $serverSpan->setTag("time",date("Y-m-d H:i:s"));
        if(isset($_SERVER['REQUEST_SCHEME'])){
            $serverSpan->setTag("request_scheme",$_SERVER['REQUEST_SCHEME']);
        }
        if(isset($_SERVER['REQUEST_URI'])){
            $serverSpan->setTag("request_uri",$_SERVER['REQUEST_URI']);
        }

        if(!empty($_REQUEST)){
            $serverSpan->log(["request_params"=>json_encode($_REQUEST)]);
        }

        $this->tracer->inject($serverSpan->getContext(),Formats\TEXT_MAP, $_SERVER);
        $this->clientTracer = $this->client->initTracer('HTTP');
        static::$spanList[$parentSpanName]=[
            "current_span"=>$serverSpan,
            "parent_context"=>$parentContext
        ];
    }


    /*
     * 創建子span
     */
    public function start($spanName,$parentSpan="")
    {
        if(!$this->uname){
            return ;
        }

        $spanContext = $this->clientTracer->extract(Formats\TEXT_MAP, $_SERVER);
        $clientrSpan = null;
        $parentSpanIsObj = $parentSpan && gettype($parentSpan) == "object";
        if (!$spanContext) {
            $clientrSpan = $this->tracer->startSpan($spanName);
        } else {
            $clientrSpan = $this->tracer->startSpan($spanName, ['references' => [
                Reference::create(Reference::FOLLOWS_FROM, $parentSpanIsObj ? $parentSpan->spanContext :$spanContext),
                Reference::create(Reference::CHILD_OF, $spanContext)
            ]]);
        }

        static::$spanList[$spanName]=[
            "current_span"=>$clientrSpan,
            "parent_context"=>$parentSpanIsObj ? $parentSpan->spanContext :$spanContext,
        ];
    }


    public function inject($spanName)
    {
        if(!$this->uname){
            return [];
        }
        if(!isset(static::$spanList[$spanName])){
            return [];
        }
        $info = static::$spanList[$spanName];
        $span = $info['current_span'];
        $injectHeaders = [];
        $this->clientTracer->inject($span->getContext(), Formats\TEXT_MAP, $injectHeaders);
        return $injectHeaders;
    }


    public function finish( $spanName, array $spanList = [])
    {
        if(!$this->uname){
            return;
        }
        if(!isset(static::$spanList[$spanName])){
            return;
        }
        $info = static::$spanList[$spanName];

        $span = $info['current_span'];
        $parentContext = $info['parent_context'];

        $span->setTag('parentSpan', $parentContext ? $parentContext->spanIdToString() : '');
        $span->setTag("time",date("Y-m-d H:i:s"));
        foreach($spanList ?: [] as $k => $v){
            $span->setTag($k, $v);
        }
        $span->finish();
    }





    private function getAllHeaders()
    {
        $headers = array();

        $copy_server = array(
            'CONTENT_TYPE'   => 'Content-Type',
            'CONTENT_LENGTH' => 'Content-Length',
            'CONTENT_MD5'    => 'Content-Md5',
        );

        foreach ($_SERVER as $key => $value) {
            if (substr($key, 0, 5) === 'HTTP_') {
                $key = substr($key, 5);
                if (!isset($copy_server[$key]) || !isset($_SERVER[$key])) {
                    $key = str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', $key))));
                    $headers[$key] = $value;
                }
            } elseif (isset($copy_server[$key])) {
                $headers[$copy_server[$key]] = $value;
            }
        }

        if(isset($_SERVER["UBER-TRACE-ID"])){
            $headers["UBER-TRACE-ID"] = $_SERVER["UBER-TRACE-ID"];
        }

        if (!isset($headers['Authorization'])) {
            if (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) {
                $headers['Authorization'] = $_SERVER['REDIRECT_HTTP_AUTHORIZATION'];
            } elseif (isset($_SERVER['PHP_AUTH_USER'])) {
                $basic_pass = isset($_SERVER['PHP_AUTH_PW']) ? $_SERVER['PHP_AUTH_PW'] : '';
                $headers['Authorization'] = 'Basic ' . base64_encode($_SERVER['PHP_AUTH_USER'] . ':' . $basic_pass);
            } elseif (isset($_SERVER['PHP_AUTH_DIGEST'])) {
                $headers['Authorization'] = $_SERVER['PHP_AUTH_DIGEST'];
            }
        }

        return $headers;
    }

    public function __destruct()
    {
        if(!$this->uname){
            return ;
        }
        $this->client->flush();
    }

}