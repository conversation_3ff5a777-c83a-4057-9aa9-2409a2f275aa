<?php
require_once(COMMON_PATH . 'Common/function_redis.php');

/*
 * 去掉工作日 周
 */
function deleteDay($str)
{
    $day_filter = ["工作日", "个工作日", "日", "个"];
    $week_filter = ["周"];
    #工作日转换
    foreach ($day_filter as $d) {
        $str = str_replace($d, "", $str);
    }
    #周转换
    foreach ($week_filter as $d) {
        $str = str_replace($d, "", $str);
    }
    return $str;
}


//获取数组数据
function dataGet($arr, $key, $defult = "")
{
    return array_key_exists($key, $arr) ? $arr[$key] : $defult;
}

//todo 计算sku的标准品牌,不存在标准品牌默认旧品牌作为标准品牌
function getStardarBrand($goods_id)
{
    // $goods_id="1163834169350939614";
    $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.spu"));
    $spuRedis = spu_redis_init();

    if (strlen($goods_id) >= 14) { //联营
        $skuInfo = $redis->hget("sku", (string)$goods_id);
        $skuArr = json_decode($skuInfo, true);

        $spu_id = dataGet($skuArr, "spu_id");
        $spuInfo = $spuRedis->hget("spu", $spu_id);
        $spuArr = json_decode($spuInfo, true);

        $brand_name = $redis->hget("brand", $spuArr["brand_id"]);

        $standard_brand_id = $spuArr["brand_id"];
        $standard_brand_name = $brand_name;
    } else { //自营
        $skuInfo = $redis->hget("Self_SelfGoods", (string)$goods_id);
        $skuArr = json_decode($skuInfo, true);
        $spu_id = dataGet($skuArr, "spu_id");

        $brandObj = $redis->hget("Self_Brand", $skuArr["brand_id"]);
        $brandArr = json_decode($brandObj, true);

        $standard_brand_id = $skuArr["brand_id"];
        $standard_brand_name = $brandArr["brand_name"];
    }

    if ($spu_id) {
        $spuInfo = $spuRedis->hget("spu", $spu_id);
        $spuArr = json_decode($spuInfo, true);
        $standard_brand_id_t = $redis->hget("standard_brand_mapping", $spuArr['brand_id']);
        if ($standard_brand_id_t > 0) {
            $standard_brand_info = $redis->hget("standard_brand", $standard_brand_id_t);
            $standard_brand_arr = json_decode($standard_brand_info, true);

            $standard_brand_id = $standard_brand_id_t;
            $standard_brand_name = $standard_brand_arr["brand_name"];
        }
    }
    return ["brand_id" => $standard_brand_id, "brand_name" => $standard_brand_name];
}

/**
 * 使用专用redis读取写入
 * @param [type] $key    [description]
 * @param string $value [description]
 * @param [type] $option [description]
 */
function S_redis($key, $value = '', $option = null)
{
    $options = array(
        'type' => 'redisrw',
    );
    empty($option) && $option = array();
    $user_options = C('REDIS_LIST.user_new'); // 注册、登录用户信息切换到新的Redis服务器 - 20231211
    $options = array_merge($option, $options, $user_options);
    return S($key, $value, $options);
}

/**
 * [str_replace_once 首先找到关键字所在位置，然后使用 substr_replace（系统函数）进行替换操作]
 * @param  [type] $search  [内容里的需要被替换的词]
 * @param  [type] $replace [替换的词]
 * @param  [type] $content [内容]
 * @return [type]          [替换后的内容]
 */
function str_replace_once($search, $replace, $content)
{
    //把图片描述去掉
    $content = preg_replace("/alt=([^ >]+)/is", '', $content);
    $pos = strpos($content, $search);
    if ($pos === false) {
        return false;
    }
    return substr_replace($content, $replace, $pos, strlen($search));
}

/**
 * 输出客户端下载文件
 * @param  [type] $content  [description]
 * @param string $filename [description]
 * @return [type]           [description]
 */
function download($content, $filename = '')
{
    $filesize = strlen($content);
    $filesize = filesize($filepath);
    header("Content-type:application/octet-stream");
    header("Accept-Ranges:bytes");
    header("Content-Disposition: attachment; filename=" . $filename);
    ob_clean();
    echo $content;
}

/**
 * 发送推送消息通知内部人员
 */
function sendMsg($keyword = '', $data = array(), $to_user = '', $cn_uncode = false)
{
    if (!$keyword) {
        return false;
    }
    // 推送给内部人员
    $data['data']['time'] = date('Y-m-d H:i:s', time());
    if ($cn_uncode) {
        $send_data = json_encode($data['data'], JSON_UNESCAPED_UNICODE); //防止中文乱码
        $wechat_data = json_encode($data['wechat_data'], JSON_UNESCAPED_UNICODE); //防止中文乱码
    } else {
        $send_data = json_encode($data['data']); //防止中文乱码
        $wechat_data = json_encode($data['wechat_data']); //防止中文乱码
    }

    $check['touser'] = json_encode($to_user);
    $check['data'] = $send_data;
    $check['wechat_data'] = $wechat_data;
    $check['pf'] = platform();
    $check['keyword'] = $keyword;
    $check['is_ignore'] = false;
    $check = array_merge($check, authkey());
    $res = post_curl(API_DOMAIN . '/msg/sendMessageByAuto', $check);
    if (!empty($res)) {
        $res = json_decode($res, true);
    }
    return $res;
}

function authkey($pf = -1)
{
    return array(
        'pf' => $pf == -1 ? platform() : $pf,
        'k1' => $_SERVER['REQUEST_TIME'],
        'k2' => pwdhash($_SERVER['REQUEST_TIME'], C('SUPER_AUTH_KEY'))
    );
}

/**
 * 生成订单号
 * @return [type] [description]
 */
function order_sn($prefix = '1', $suffix = '')
{
    return $prefix . date('Ymd') . rand(10000, 99999) . $suffix;
}

// 获取替换变量模板
// data array 所有变量
// temp string 模板
function getTempletByVariable($data, $temp)
{
    foreach ($data as $key => $value) {
        $value = str_replace('\u00a5', '￥', $value);
        $value = stripslashes($value);
        $temp = str_replace("{{data." . $key . "}}", $value, $temp);
    }
    return $temp ? $temp : false;
}

//获取需要发送消息的接收者
//根据渠道类型，单一的会员联系（手机，邮箱，userid）,返回符合要求的格式
//20210515 by  sunlong
function getObjUserByMemberLink($channel_type = 0, $member_link = '', $redisPrefixKey = "")
{
    if (!$channel_type) {
        return false;
    }
    if (!$member_link) {
        return false;
    }
    $redis = new \Redis();
    $redis->connect(C("YUNXIN_REDIS_REDIS.host"), C('YUNXIN_REDIS_REDIS.port'));
    $redis->auth(C("YUNXIN_REDIS_REDIS.password"));
    //判断是否是手机号
    if (preg_match_all("/^1[3456789]\d{9}$/", $member_link)) {
        $user_id = $redis->hGet($redisPrefixKey . 'user_mobile', $member_link);
        if (!$user_id) {
            return false;
        }
        $data = $redis->hGet($redisPrefixKey . 'user', intval($user_id));
    } else {
        $data = $redis->hGet($redisPrefixKey . 'user', intval($member_link));
        $user_id = intval($member_link);
    }
    $redis->close();
    if (!$data) {
        return false;
    }
    $data = json_decode($data, true);
    $m_link = false;
    switch (strval($channel_type)) {
        case '1': // 站内信
            $m_link = $user_id;
            break;
        case '2': // 短信
            $m_link = isset($data['mobile']) && $data['mobile'] ? $data['mobile'] : false;
            break;
        case '3': // 邮箱
            $m_link = isset($data['email']) && $data['email'] ? $data['email'] : false;
            break;
        case '4': // 微信通知
            $m_link = isset($data['wechat_oauth']['open_id']) && $data['wechat_oauth']['open_id'] ? $data['wechat_oauth']['open_id'] : false;
            break;
        case '5': // 钉钉
            $data['dtalk_oauth'] = !empty($data['dtalk_oauth']) ? $data['dtalk_oauth'] : [];
            $m_link = isset($data['dtalk_oauth']['user_id']) && $data['dtalk_oauth']['user_id'] ? $data['dtalk_oauth']['user_id'] : false;
            break;
        default:
            # code...
            break;
    }
    return $m_link;
}


function getUcenterObjUser($channel_type = 0, $touser = '', $orgId = 1)
{
    if (!$channel_type || !$touser) {
        return false;
    }
    $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.user_new"));
    //判断是否是手机号
    if (is_mobile($touser)) {
        //通过手机号获取用户id
        $userId = $redis->hGet(C('UCENTER_MOBILE_REDIS_KEY'), sprintf("%s_%s", $touser, $orgId));
        if (!$userId) {
            return false;
        }

    } elseif (is_email($touser)) {
        //通过邮箱获取用户id
        $userId = $redis->hGet(C('UCENTER_EMAIL_REDIS_KEY'), sprintf("%s_%s", $touser, $orgId));
        if (!$userId) {
            return false;
        }

    } else {
        $userId = intval($touser);
    }
    $userId = intval($userId);
    $data = $redis->hGet(C('UCENTER_USER_REDIS_KEY'), sprintf("%s_%s", $userId, $orgId));
    if (!$data) {
        return false;
    }

    $data = json_decode($data, true);
    $mLink = false;
    switch (strval($channel_type)) {
        case '1': // 站内信
            $mLink = $userId;
            break;
        case '2': // 短信
            $mobile = !empty($data['mobile']) ? $data['mobile'] : "";
            if ($mobile && !empty($data['intl_code'])) {
                $mobile = trim($data['intl_code']) . trim($mobile);
            }
            $mLink = !empty($mobile) ? $mobile : false;
            break;
        case '3': // 邮箱
            $mLink = !empty($data['email']) ? $data['email'] : false;
            break;
        case '4': // 微信通知
            $mLink = !empty($data['wechat_oauth']['open_id']) ? $data['wechat_oauth']['open_id'] : false;
            break;
        case '5': // 钉钉
            $data['com_wx_oauth'] = !empty($data['com_wx_oauth']) ? $data['com_wx_oauth'] : [];
            $mLink = !empty($data['com_wx_oauth']['user_id']) ? $data['com_wx_oauth']['user_id'] : false;
            break;
        default:
            # code...
            break;
    }
    return $mLink;
}


// 根据渠道类型，单一的会员联系（手机，邮箱，userid）,返回符合要求的格式
function getRightFormatByChannelType($channel_type = 0, $member_link = '')
{
    if (!$channel_type) {
        return false;
    }
    if (!$member_link) {
        return false;
    }
    //判断是什么，从而获取4个值，再填充到下面四种中
    if (S_user($member_link)) { // member_link一般只会填手机号邮箱或user_id,不会填openid或unionid
        $user_id = $member_link;
    } else {
        $user_id = S_account($member_link);
    }
    if (!$user_id) {
        return false;
    }
    $user_info = S_user($user_id);
    if (!$user_info) {
        return false;
    }
    $mobile = $user_info['mobile'];
    if ($user_info['intl_code']) {
        $mobile = trim($user_info['intl_code']) . $user_info['mobile'];
    }
    $email = $user_info['email'];
    $openid = $user_info['wechat_oauth']['open_id'];
    $dtalk_id = false;
    if (isset($user_info['com_wx_oauth'])) {
        $dtalk_id = !empty($user_info['com_wx_oauth']['user_id']) ? $user_info['com_wx_oauth']['user_id'] : ""; // 企业微信
    }
    switch (strval($channel_type)) {
        case '1': // 站内信
            $m_link = $user_id;
            break;
        case '2': // 短信
            $m_link = $mobile;
            break;
        case '3': // 邮箱
            $m_link = $email;
            break;
        case '4': // 微信通知
            $m_link = $openid;
            break;
        case '5': // 钉钉
            $m_link = $dtalk_id;
            break;
        default:
            # code...
            break;
    }
    return $m_link ? $m_link : false;

}

/**
 * 获取当前完整地址
 * @return [type] [description]
 */
function getUrl($full = false)
{
    $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
    if (class_exists('\Illuminate\Support\Facades\Request')) {
        try {
            $url = \Illuminate\Support\Facades\Request::url();
        } catch (\Exception $e) {
            $url = $http_type . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        }
        if (!$full) {
            $url_split = parse_url($url);
            $url_split['scheme'] = isset($url_split['scheme']) ? $url_split['scheme'] . '://' : $http_type;
            unset($url_split['query'], $url_split['fragment']);
            $url = implode('', $url_split);
        }
    } else {
        if ($full) {
            $url = $http_type . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        } else {
            $url_split = parse_url($_SERVER['REQUEST_URI']);
            $url = $http_type . $_SERVER['HTTP_HOST'] . $url_split['path'];
        }
    }
    return $url;
}

// 会员活动日志
function userActivityLog($resp, $lotteryId, $userId, $action_source = '0')
{
    if (isMobile()) {
        $userType = 1;
    } else {
        $userType = 0;
    }
    //插入数据库
    $UserActivityLogModel = D('UserActivityLog');
    $resp = json_encode($resp);
    $data = array(
        'user_id' => $userId ? $userId : cookie('uid'),
        'lottery_id' => $lotteryId,
        'is_draw' => 1,
        'create_time' => time(),
        'action_result' => $resp,
        'action_source' => $action_source, //默认前台用户0，后台操作1
        'user_type' => $userType
    );
    $res = $UserActivityLogModel->data($data)->add();
    return $res ? $res : false;
}

/*
 * 经典的概率算法，
 * $proArr是一个预先设置的数组，
 * 假设数组为：array(100,200,300，400)，
 * 开始是从1,1000 这个概率范围内筛选第一个数是否在他的出现概率范围之内，
 * 如果不在，则将概率空间，也就是k的值减去刚刚的那个数字的概率空间，
 * 在本例当中就是减去100，也就是说第二个数是在1，900这个范围内筛选的。
 * 这样 筛选到最终，总会有一个数满足要求。
 * 就相当于去一个箱子里摸东西，
 * 第一个不是，第二个不是，第三个还不是，那最后一个一定是。
 * 这个算法简单，而且效率非常高，
 * 这个算法在大数据量的项目中效率非常棒。
 */
function get_rand($proArr)
{
    $result = '';
    //概率数组的总概率精度
    $proSum = array_sum($proArr);
    //概率数组循环
    foreach ($proArr as $key => $proCur) {
        $randNum = mt_rand(1, $proSum);
        if ($randNum <= $proCur) {
            $result = $key;
            break;
        } else {
            $proSum -= $proCur;
        }
    }
    unset ($proArr);
    return $result;
}

function wxoauth($options, $scope = 'snsapi_base')
{
    header('Content-Type:text/html;charset=utf-8 ');
    $we_obj = wechat($options);
    $resData = [];
    \Think\Log::write(sprintf("wxoauth:555555555555555555:%s", json_encode($options)));
    if ($options['code']) { //用户已经同意授权，获取微信用户信息 (获取网页授权接口无上限)
        if ($options['pf'] == 6 && $options['miniProgram'] === 'false') {
            Vendor('Wechat.MiniProgram');
            $we_obj = new \MiniProgram($options);
            $res = $we_obj->getSessionKey($options['code']);
            return $res;
        } else {
            $_GET['code'] = $options['code'];
            // \Think\Log::write(sprintf("wxoauth:66666666:%s",json_encode($_REQUEST)));
            $json = $we_obj->getOauthAccessToken();
            // \Think\Log::write(sprintf("wxoauth:77777777:%s",json_encode($json)));
            if (!$json) {
                $resData = [
                    'status' => 4,
                    'data' => '获取用户授权失败，请重新确认',
                ];
                return $resData;
                die;
            }
        }
        \Think\Log::write(sprintf("wxoauth888888888:%s", json_encode($options)));
        //授权成功，记录unionid及openid
        session('open_id', $json['openid']);
        session('unionid', $json['unionid']);
        $expire = C('WX_EXPIRE_TIME');
        $union_token = $json['unionid'] . '&' . $_SERVER['REQUEST_TIME'];
        $open_token = $json['openid'] . '&' . $_SERVER['REQUEST_TIME'];
        $json['unionkey'] = base64_encode(pwdhash($union_token, C('API_USE_KEY')) . '&' . $_SERVER['REQUEST_TIME']);
        $json['openkey'] = base64_encode(pwdhash($open_token, C('API_USE_KEY')) . '&' . $_SERVER['REQUEST_TIME']);
        cookie('unionid', $json['unionid'], array('expire' => $expire));
        cookie('open_id', $json['openid'], array('expire' => $expire));
        cookie('unionkey', $json['unionkey'], array('expire' => $expire));
        cookie('openkey', $json['openkey'], array('expire' => $expire));

        if ($scope == 'snsapi_userinfo') {
            // 2.1 `snsapi_userinfo` 继续通过access_token和openid拉取用户信息
            $userinfo = $we_obj->getUserInfo($json['openid']);
            if ($userinfo === false) {
                $resData = [
                    'status' => 3,
                    'data' => '网络错误',
                ];
                return $resData;
                die;
            }
            //未关注
            if ($userinfo['subscribe'] == 0) {
                $resData = [
                    'status' => 4,
                    'data' => '未关注公众号',
                    'wx_data' => $json,
                ];
                $userinfo['unionid'] = $json['unionid'];
                S_wechatinfo($json['unionid'], $userinfo, array('expire' => 3600));
                return $resData;
                die;
            }
            S_wechatinfo($json['unionid'], $userinfo);


            //F('wx_user_json', json_encode($userinfo));
            $resData = [
                'status' => 1,
                'data' => $userinfo,
                'wx_data' => $json,
            ];
            return $resData;
            die;
        } else {
            // 2.2 `snsapi_base` 不弹出授权页面，直接跳转，只能获取用户openid
            S_wechatinfo($json['unionid'], $json);
            //F('wx_user_json', json_encode($json));
            $resData = [
                'status' => 1,
                'data' => $json,
                'wx_data' => $json,
            ];
            return $resData;
            die;
        }
    } else { //用户授权页面
        $callback = $options['backUrl'];
        $oauth_url = $we_obj->getOauthRedirect($callback, "wxbase", $scope);
        $resData = [
            'status' => 2,
            'data' => $oauth_url,
        ];
        return $resData;
        die;
        redirect($oauth_url);
    }
}

/*移动端判断*/
function isMobile()
{
    // 如果有HTTP_X_WAP_PROFILE则一定是移动设备
    if (isset ($_SERVER['HTTP_X_WAP_PROFILE'])) {
        return true;
    }
    // 如果via信息含有wap则一定是移动设备,部分服务商会屏蔽该信息
    if (isset ($_SERVER['HTTP_VIA'])) {
        // 找不到为flase,否则为true
        return stristr($_SERVER['HTTP_VIA'], "wap") ? true : false;
    }
    // 脑残法，判断手机发送的客户端标志,兼容性有待提高
    if (isset ($_SERVER['HTTP_USER_AGENT'])) {
        $clientkeywords = array(
            'nokia',
            'sony',
            'ericsson',
            'mot',
            'samsung',
            'htc',
            'sgh',
            'lg',
            'sharp',
            'sie-',
            'philips',
            'panasonic',
            'alcatel',
            'lenovo',
            'iphone',
            'ipod',
            'blackberry',
            'meizu',
            'android',
            'netfront',
            'symbian',
            'ucweb',
            'windowsce',
            'palm',
            'operamini',
            'operamobi',
            'openwave',
            'nexusone',
            'cldc',
            'midp',
            'wap',
            'mobile'
        );
        // 从HTTP_USER_AGENT中查找手机浏览器的关键字
        if (preg_match("/(" . implode('|', $clientkeywords) . ")/i", strtolower($_SERVER['HTTP_USER_AGENT']))) {
            return true;
        }
    }
    // 协议法，因为有可能不准确，放到最后判断
    if (isset ($_SERVER['HTTP_ACCEPT'])) {
        // 如果只支持wml并且不支持html那一定是移动设备
        // 如果支持wml和html但是wml在html之前则是移动设备
        if ((strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') !== false) && (strpos($_SERVER['HTTP_ACCEPT'],
                    'text/html') === false || (strpos($_SERVER['HTTP_ACCEPT'],
                        'vnd.wap.wml') < strpos($_SERVER['HTTP_ACCEPT'], 'text/html')))) {
            return true;
        }
    }
    return false;
}

/**
 * 用户名、邮箱、手机账号中间字符串以*隐藏
 */
function hideStar($str)
{
    if (strpos($str, '@')) {
        $email_array = explode("@", $str);
        $prevfix = (strlen($email_array[0]) < 4) ? "" : substr($str, 0, 2); //邮箱前缀
        $count = 0;
        $str = preg_replace('/([\d\w+_-]{0,100})@/', '***@', $str, -1, $count);
        $rs = $prevfix . $str;
    } else {
        $pattern = '/(1[23456789]{1}[0-9])[0-9]{4}([0-9]{4})/i';
        if (preg_match($pattern, $str)) {
            $rs = preg_replace($pattern, '$1****$2', $str); // substr_replace($name,'****',3,4);
        } else {
            $rs = substr($str, 0, 3) . "***" . substr($str, -1);
        }
    }
    return $rs;
}

/**
 * [base64_upload 图片上传]
 * @param  [type] $base64 [description]
 * @return [type]         [description]
 */
function base64_upload($base64)
{
    $base64_image = str_replace(' ', '+', $base64);
    //post的数据里面，加号会被替换为空格，需要重新替换回来，如果不是post的数据，则注释掉这一行
    if (preg_match('/^(data:\s*image\/(\w+);base64,)/', $base64_image, $result)) {
        //匹配成功
        if ($result[2] == 'jpeg') {
            $image_name = uniqid() . '.jpg';
            //纯粹是看jpeg不爽才替换的
        } else {
            $image_name = uniqid() . '.' . $result[2];
        }
        $image_file = "./Uploads/company/{$image_name}";
        //服务器文件存储路径
        $img_size = file_put_contents($image_file, base64_decode(str_replace($result[1], '', $base64_image)));
        if ($img_size) {
            $img_size = intval($img_size) / 1024; //化为单位为kb
            $img_size = intval($img_size);
            if ($img_size > 1024) { //大于1M的，不能保存，需要删除
                if (file_exists($image_file)) {
                    unlink($image_file);
                }
                return 'oversize';
            } else {
                return $image_name;
            }
        } else {
            return false;
        }
    } else {
        return false;
    }
}

/**
 * 根据brand_id获取brand_info相关信息
 * @param  [type] $brandStr [description]
 * @return [type]           [description]
 */
function getBrandArrByStr($brandStr)
{
    if (!$brandStr) {
        return false;
    }
    $brandArr = explode(",", $brandStr);
    foreach ($brandArr as $key => $value) {
        $BrandModel = D('Brand');
        $brand_info[$key]['brand_id'] = $BrandModel->getFieldByBrandId($value, 'brand_id');
        $brand_info[$key]['brand_name'] = $BrandModel->getFieldByBrandId($value, 'brand_name');
        $brand_info[$key]['brand_logo'] = $BrandModel->getFieldByBrandId($value, 'brand_logo');
    }
    return $brand_info;
}

/**
 * 验证手机   11位          13510507993
 * 验证座机   3-4位区号     7-8位直播号码0755-88695000，88695000
 * 验证分机号 1－4位分机号  0755-88695000-1234  88695000-1234
 */
function is_mobile_phone($val)
{
    return preg_match("(^(1[0-9]{1}[0-9]{1}[0-9]{8}$|189[0-9]{8}$/)|^((\d{7,8})|(\d{4}|\d{3})-(\d{7,8})|(\d{4}|\d{3})-(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d{1})|(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d{1}))$)",
        $val);
}

/**
 * 文件上传函数
 */
function sp_upload($file, $dir = '', $thumb = array(), $save_rule = 'uniqid', $is_amdin = false)
{
    $upload = new \Think\Upload();
    if ($dir) {
        $upload_path = C('PING_ATTACH_PATH') . $dir . '/';
        $upload->savePath = $upload_path;
    }
    if ($thumb) {
        $upload->thumb = true;
        $upload->thumbMaxWidth = $thumb['width'];
        $upload->thumbMaxHeight = $thumb['height'];
        $upload->thumbPrefix = '';
        $upload->thumbSuffix = isset($thumb['suffix']) ? $thumb['suffix'] : '_thumb';
        $upload->thumbExt = isset($thumb['ext']) ? $thumb['ext'] : '';
        $upload->thumbRemoveOrigin = isset($thumb['remove_origin']) ? true : false;
    }
    //自定义上传规则
    $upload = sp_upload_init($upload);
    if ($save_rule != 'uniqid') {
        $upload->saveRule = $save_rule;
    }

    if ($result = $upload->uploadOne($file)) {
        $file = $result['savepath'] . $result['savename'];
        //保存上传信息
        $upload_dir = str_replace(__ROOT__, '', C('TMPL_PARSE_STRING.__UPLOAD__'));
        return array(
            'error' => 0,
            'info' => C('TMPL_PARSE_STRING.__UPLOAD__') . $result['savepath'] . $result['savename']
        );
    } else {
        return array('error' => 1, 'info' => $upload->getError());
    }
}

/**
 * 上传文件默认规则定义
 */
function sp_upload_init($upload)
{
    $allow_max = C('PING_ATTR_ALLOW_SIZE'); //读取配置
    $allow_exts = explode(',', C('PING_ATTR_ALLOW_EXTS')); //读取配置
    $allow_max && $upload->maxSize = $allow_max * 1024;   //文件大小限制

    $allow_exts && $upload->exts = $allow_exts;  //文件类型限制
    $upload->saveRule = 'uniqid';
    return $upload;
}

/**
 * 邮件发送函数
 * $toemail 接收邮件的用户邮箱
 * $title 邮件标题
 * $content 邮件内容《html格式》
 */
function sendMail($toemail, $title, $content, $cc = "")
{
    vendor('PHPMailer.class#email'); //从PHPMailer目录导class.email.php类文件
    $mail = new \smtp();
    $smtpserver = "smtp.mxhichina.com";//SMTP服务器
    $smtpserverport = 25;//SMTP服务器端口
    $smtpusermail = "<EMAIL>";//SMTP服务器的用户邮箱
    $smtpemailto = $toemail;//发送给谁
    $smtpuser = '<EMAIL>';//SMTP服务器的用户帐号
    $smtppass = "IChunt2016";//SMTP服务器的用户密码
    $mailname = '猎芯网';
    $mailtitle = $title;//邮件主题
    $mailcontent = $content;//邮件内容
    $mailtype = "HTML";//邮件格式（HTML/TXT）,TXT为文本邮件
    //************************ 配置信息 ****************************
    $smtp = $mail->smtp($smtpserver, $smtpserverport, true, $smtpuser, $smtppass);//这里面的一个true是表示使用身份验证,否则不使用身份验证.
    $smtp->debug = false;//是否显示发送的调试信息
    $state = $mail->sendmail($smtpemailto, $smtpusermail, $mailtitle, $mailcontent, $mailtype, $mailname, $cc);
    return $state;
}

function sendHunYunMail($toemail, $title, $content, $cc = "")
{
    vendor('PHPMailer.class#email'); //从PHPMailer目录导class.email.php类文件
    $mail = new \smtp();
    $smtpserver = "smtp.mxhichina.com";//SMTP服务器
    $smtpserverport = 25;//SMTP服务器端口
    $smtpusermail = "<EMAIL>";//SMTP服务器的用户邮箱
    $smtpemailto = $toemail;//发送给谁
    $smtpuser = '<EMAIL>';//SMTP服务器的用户帐号
    $smtppass = "IChunt2016";//SMTP服务器的用户密码
    $mailname = '猎芯网';
    $mailtitle = $title;//邮件主题
    $mailcontent = $content;//邮件内容
    $mailtype = "HTML";//邮件格式（HTML/TXT）,TXT为文本邮件
    //************************ 配置信息 ****************************
    $smtp = $mail->smtp($smtpserver, $smtpserverport, true, $smtpuser, $smtppass);//这里面的一个true是表示使用身份验证,否则不使用身份验证.
    $smtp->debug = false;//是否显示发送的调试信息
    $state = $mail->sendmail($smtpemailto, $smtpusermail, $mailtitle, $mailcontent, $mailtype, $mailname, $cc);
    return $state;
}


/**
 * webpower CURL 请求
 * @param $url
 * @param $content
 * @param string $method
 * @return mixed
 */
function msgCurl($data)
{
    $mobile = $data['mobile'];
    if (strstr($mobile, "+")) { // 国际手机
        $mobile = str_replace('+', '', $mobile);
        $data['type'] = 3;
    } else {
        $data['type'] = 1; // 营销性消息暂时用不到
    }
    switch (strval($data['type'])) {
        case '1': // 功能性消息
            $user_name = C('SMS_NEW_VERIFY_NAME') . ':' . C('SMS_NEW_USERNAME');
            $password = C('SMS_NEW_VERIFY_PASSWORD');
            break;
        case '2': // 营销性消息
            $user_name = C('SMS_NEW_MARKET_NAME') . ':' . C('SMS_NEW_USERNAME'); //
            $password = C('SMS_NEW_MARKET_PASSWORD');
            break;
        case '3': // 海外
            $user_name = C('SMS_NEW_OVERSEA_NAME') . ':' . C('SMS_NEW_USERNAME'); //
            $password = C('SMS_NEW_OVERSEA_PASSWORD');
            break;
        default:
            # code...
            break;
    }

    $arrdata = array(
        "username" => $user_name,
        "password" => $password,
        "from" => true,
        "to" => $mobile,
        "content" => urlencode(iconv("UTF-8", "GBK", $data['content'])),
        //"presendTime" => $presendTime,
        //"expandPrefix" => $expandPrefix,
        "isvoice" => 0,
    );
    $sign = getSignature($arrdata);
    $result = get_curl(C('SMS_NEW_URL') . $sign);
    saveMsgCodeLog($data, $result);
    return $result;
}

// 日志表里短信存code验证码
function saveMsgCodeLog($data = array(), $result = array())
{
    //记录日志
    $info = array(
        'mobile' => $data['mobile'],
        'code' => $data['code'] ? $data['code'] : 'no-code', // 日志表里短信存code验证码
        'url' => json_encode($data['content']),
        'response' => $result
    );
    $UserMsgSendModel = D('Home/UserMsgSend');
    $data = array(
        'send_time' => time(),
        'ip' => get_client_ip(0, true),
    );
    $data = array_merge($data, $info);
    $res = $UserMsgSendModel->add($data);
    return false;
}

/**
 * 获取url尾缀
 * @param array $arrdata 参数数组
 * @return boolean|string 参数字符串
 */
function getSignature($arrdata)
{
    ksort($arrdata);
    $paramstring = "";
    foreach ($arrdata as $key => $value) {
        if (strlen($paramstring) == 0) {
            $paramstring .= $key . "=" . $value;
        } else {
            $paramstring .= "&" . $key . "=" . $value;
        }
    }
    return $paramstring;
}

function webpowerCurl($content, $method = 'post')
{
    $url = C('SMS_URL');
    $username = C('SMS_USERNAME');   //短信平台的账号
    $password = C('SMS_PASSWORD');   //短信平台的密码

    $data_string = json_encode($content);
    $header = array(
        "Content-Type: application/json",
        "X-HTTP-Method-Override: $method",
        "Authorization: Basic " . base64_encode($username . ":" . $password)
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_FAILONERROR, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);

    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    $response = curl_exec($ch);
    return $response ? $response : curl_error($ch);
}

/**
 * 获取浏览器及相关设备信息
 * return array
 */
function getAgentInfo()
{
    $agent = $_SERVER['HTTP_USER_AGENT'];
    $brower = array(
        'MSIE' => 1,
        'Trident/' => 1,
        'Firefox' => 2,
        'WindowsWechat' => 12,
        'MicroMessenger' => 9,
        'QQBrowser' => 3,
        'QQ/' => 3,
        'UCBrowser' => 4,
        'Edge' => 5,
        'Chrome' => 6,
        'Opera' => 7,
        'OPR' => 7,
        'Safari' => 8,
    );
    $system = array(
        'Windows' => 1,
        'Android' => 2,
        'iPhone' => 3,
        'Windows Phone' => 4,
        'iPad' => 5
    );
    $browser_num = 0;//未知
    $system_num = 0;//未知
    foreach ($brower as $bro => $val) {
        if (stripos($agent, $bro) !== false) {
            $browser_num = $val;
            break;
        }
    }
    foreach ($system as $sys => $val) {
        if (stripos($agent, $sys) !== false) {
            $system_num = $val;
            break;
        }
    }
    return array('sys' => $system_num, 'bro' => $browser_num);
}

/**
 * 传递数据以易于阅读的样式格式化后输出
 */
function p($data)
{
    // 定义样式
    $str = '<pre style="display: block;padding: 9.5px;margin: 44px 0 0 0;font-size: 13px;line-height: 1.42857;color: #333;word-break: break-all;word-wrap: break-word;background-color: #F5F5F5;border: 1px solid #CCC;border-radius: 4px;">';
    // 如果是boolean或者null直接显示文字；否则print
    if (is_bool($data)) {
        $show_data = $data ? 'true' : 'false';
    } elseif (is_null($data)) {
        $show_data = 'null';
    } else {
        $show_data = print_r($data, true);
    }
    $str .= $show_data;
    $str .= '</pre>';
    echo $str;
}

/**
 * 判断是否手机
 * @param  [type]  $mobile [description]
 * @return boolean         [description]
 */
function is_mobile($mobile)
{
    if (strstr($mobile, "+")) { // 国际手机
        $res = explode("+", $mobile);
        if (intval(strlen($res[1])) > 4 && strval($res[0]) !== '0086') {
            return true;
        }
        if (strval(strlen($res[1])) === '11' && strval($res[0]) === '0086') {
            return true;
        }
    } else {
        if (preg_match("/^1[123456789]{1}\d{9}$/", $mobile)) {
            return true;
        }
    }
    return false;
}

/**
 * 判断是否邮箱
 * @param  [type]  $email [description]
 * @return boolean        [description]
 */
function is_email($email)
{
    if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return true;
    } else {
        return false;
    }
}

/**
 * 统一密码加密
 * @param  [type] $pwd [description]
 * @return [type]      [description]
 */
function pwdhash($pwd, $salt)
{
    return md5(md5($pwd) . $salt);
}

/**
 * 通用验签
 * @return [type] [description]
 */
function validhash($key, $value, $salt, $expire = 60)
{
    if (is_array($key)) {
        $key = implode('&', $key);
    }
    $value = base64_decode($value);
    $tokens = explode('&', $value);
    //存在时间及校验是否过期
    if (!empty($tokens[1]) && (time() - $tokens[1] > $expire)) {
        return false;
    }
    $token = $tokens[0];
    $tokens[0] = '';
    $pwd = pwdhash($key . implode('&', $tokens), $salt);
    if ($pwd != $token) {
        return false;
    }
    return true;
}

/**
 * 随机码
 * @param  [type] $len    长度 32\64\128
 * @param  [type] $type   当长度不存在时可选择使用指定输出类型，0混搭 1字母 2数字 3字母数字
 *                        当长度存在时可作为追加字符加密使用
 * @return [type]         [description]
 */
function hash_key($len = 128, $type_ext = 0)
{
    $letter = 'abcdefghijklmnopqrstuvwxyz';
    $number = '0123456789';
    $special = '!@#$%&*()_+';
    $len_type = array(
        '128' => 'sha512',
        '64' => 'sha256',
        '32' => 'md4',
    );

    if (!isset($len_type[$len])) {
        switch ($type_ext) {
            case 1:
                $base = &$letter;
                break;
            case 2:
                $base = &$number;
                break;
            case 3:
                $base = $number . $letter;
                break;
            default :
                $base = $number . $letter . $special;
                break;
        }
        $data = '';
        $length = strlen($base);
        for ($i = 0; $i < $len; $i++) {
            $data .= $base[rand(0, $length - 1)];
        }
        $key = $data;

    } else {
        $data = time();
        $base = $number . $letter . $special;
        $length = strlen($base);
        for ($i = 0; $i <= 10; $i++) {
            $data .= $base[rand(0, $length - 1)];
        }
        if (!empty($type_ext)) {
            $data .= $type_ext;
        }
        $key = hash($len_type[$len], $data, false);
    }
    return $key;
}

function new_curl($url, $params = false, $ispost = 0, $header = [], &$httpInfo = [])
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    curl_setopt($ch, CURLOPT_USERAGENT,
        'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.118 Safari/537.36');
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 对认证证书来源的检查
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 从证书中检查SSL加密算法是否存在
    if (!empty($header)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    }
    if (is_array($params)) {
        $result = array_filter($params, function ($v) {
            return is_object($v) && get_class($v) == 'CURLFile';
        });
        if (empty($result)) {
            $params = http_build_query($params);
        }
    }
    if ($ispost) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_URL, $url);
    } else {
        if ($params) {
            curl_setopt($ch, CURLOPT_URL, $url . '?' . $params);
        } else {
            curl_setopt($ch, CURLOPT_URL, $url);
        }
    }
    $response = curl_exec($ch);
    $httpInfo = curl_getinfo($ch);
    if ($response === false) {
//        echo "cURL Error: " . curl_error($ch);
        return false;
    }
    curl_close($ch);
    return $response;
}

function footstone_curl($url)
{
    return post_curl('http://footstone_v2.ichunt.net/open/curl?url=' . $url);
}

function get_curl($url, $data = '')
{
    if (!empty($url)) {
        if (is_array($data)) {
            $data = http_build_query($data);
        }
        $arr = parse_url($url);
        if (!empty($arr['query'])) {
            $url .= '&';
        } else {
            $url .= '?';
        }
        $url .= $data;
    }
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 3);
    if (strpos($url, 'https://') !== false) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    }
    $output = curl_exec($ch);
    curl_close($ch);
    return $output;
}

function curlPostJson($url, $data)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);
    return $response;
}


// function post_curl($url, $data, $headers = array(), $conf = array())
// {
//     if (is_array($data)) {
//         $data = http_build_query($data);
//     }
//     if(is_array($headers)){
//         if((isset($_SERVER["HTTP_REFERER"]) && $_SERVER["HTTP_REFERER"] != "")){
//             array_push($headers,'Referer:'.$_SERVER["HTTP_REFERER"]);
//         }else{
//             array_push($headers,'Referer:ichunt-api');
//         }
//     }
//     $timeout = !empty($conf['timeout']) ? $conf['timeout'] : 60;
//     $ch = curl_init();
//     curl_setopt($ch, CURLOPT_URL, $url);
//     curl_setopt($ch, CURLOPT_HEADER, 0);
//     curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
//     curl_setopt($ch, CURLOPT_POST, 1);
//     curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
//     curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
//     curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
//     if (strpos($url, 'https://') !== false) {
//         curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 1);
//         curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
//     }
//     $output = curl_exec($ch);

//     // 检查是否有错误发生
//     if (curl_errno($ch)) {
//         $error_message = curl_error($ch);
//         // 捕获超时异常
//         if ($error_message == 'timeout') {
//             $log = '请求超时';
//         } else {
//             $log = '发生错误，' . $error_message;
//         }

//         $path = C('LOG_PATH') . ACTION_NAME . '/' . date('y_m_d') . '.log'; // 接口日志文件
//         \Think\Log::write('CURL请求失败，请求URL：' . $url . '，失败原因：' . $log, INFO, '', $path);
//     }

//     curl_close($ch);
//     return $output;
// }

function post_curl($url, $data, $headers = array(), $conf = array())
{
    $max_retries = !empty($conf['max_retries']) ? $conf['max_retries'] : 3; // 最大重试次数，默认为3次
    $retry_delay = !empty($conf['retry_delay']) ? $conf['retry_delay'] : 1; // 重试延迟时间，默认为1秒

    for ($retry = 0; $retry <= $max_retries; $retry++) {
        if (is_array($data)) {
            $data = http_build_query($data);
        }
        if (is_array($headers)) {
            if ((isset($_SERVER["HTTP_REFERER"]) && $_SERVER["HTTP_REFERER"] != "")) {
                array_push($headers, 'Referer:' . $_SERVER["HTTP_REFERER"]);
            } else {
                array_push($headers, 'Referer:ichunt-api');
            }
        }
        $timeout = !empty($conf['timeout']) ? $conf['timeout'] : 60;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $output = curl_exec($ch);

        // 检查是否有错误发生
        if (curl_errno($ch)) {
            $error_message = curl_error($ch);
            $err_no = curl_errno($ch);
            $error_no_desc = curl_strerror($err_no);
            $path = C('LOG_PATH') . '/Common/' . date('y_m_d') . '.log'; // 接口日志文件
            $curl_error_info = json_encode([
                'error_type' => 'curl_error',
                'message' => "CURL请求失败，请求URL:{$url},errorno:{$err_no},失败原因:{$error_message},重试次数:{$retry}",
                'reqeust_data' => $data,
                'headers' => $headers,
                'conf' => $conf,
                'error_no' => $err_no,
                'error_no_desc' => $error_no_desc,
            ], JSON_UNESCAPED_UNICODE);
            \Think\Log::write($curl_error_info, \Think\Log::ERR, '', $path);
            curl_close($ch);
            if ($retry < $max_retries) {
                sleep($retry_delay); // 等待一段时间后重试
                continue;
            }
        } else {
            curl_close($ch);
            return $output;
        }
    }

    return false; // 达到最大重试次数后仍然失败，返回false
}


/**
 * 钉钉群机器人通知
 * @param  [type]  $token [description]
 * @param  [type]  $text  [description]
 * @param boolean $at [description]
 * @param string $types [description]
 * @return [type]         [description]
 */
function dingtalk_robot($token, $text, $at = false, $types = 'text')
{
    // if (is_array($text)) {
    //     $text = implode("\r", $text);
    // }
    // $isAtAll = false;
    // if (is_string($at)) {
    //     $at = array($at);
    // } elseif ($at == 'all') {
    //     $at = array();
    //     $isAtAll = true;
    // }

    // // 添加关键词
    // if (strpos(API_DOMAIN, 'sz') !== false) {
    //     $text = '测试站告警，'.$text;
    // } else if (strpos(API_DOMAIN, 'liexin') !== false) {
    //     $text = '本地告警，'.$text;
    // } else {
    //     $text = '正式站告警，'.$text;
    // }

    // $data = array(
    //     'msgtype' => $types,
    //     'text' => array(
    //         'content' => $text,
    //     ),
    //     'at' => array(
    //         'atMobiles'=> $at,
    //         'isAtAll' => $isAtAll
    //     )
    // );
    // $data = json_encode($data);
    // $url = 'https://oapi.dingtalk.com/robot/send?access_token='.$token;
    // $res = post_curl($url, $data, array('Content-Type:application/json;charset=UTF-8'));
    // return $res;
}

/**
 * 获取设置短信发送验证码
 * @param  [type] $mobile [description]
 * @param string $value [description]
 * @return [type]          [description]
 */
function session_sms($mobile, $value = '')
{
    $key = C('MSG_SEND_SESSION_KEY');
    if (I("pf") == 2) { //h5 不能走原来的session 这套
        $redis = new \Redis();
        $redis->connect(C('REDIS_HOME_ROLL_WRITE'), C('REDIS_PORT'));
        $redis->auth(C('REDIS_PASSWORD'));
        if ($value) {
            $redis->set($key . $mobile, json_encode($value));
        }
        $infoStr = $redis->get($key . $mobile);
        $info = json_decode($infoStr, true);
    } else {
        $info = session($key . $mobile, $value);
    }

    if ($value === '') {
        if (empty($info['overtime']) || $info['overtime'] < time()) {
            $info = null;
        } else {
            $info = $info['code'];
        }
    }
    return $info;
}

/**
 * 判断访问站点（优先适用传过来的参数）
 * @return [type] [description]
 */
function platform()
{
    $pf = I('request.pf', -1, 'intval');
    if ($pf == -1) {
        if (empty($_SERVER['HTTP_REFERER']) && $_SERVER['SERVER_NAME'] == 'api.liexin.com') {
            return 1;
        }
        if (cookie('miniProgram') === 'true') {
            return 6;
        }
        $url_arr = explode('//', $_SERVER['HTTP_REFERER']);
        $domain_arr = explode('/', $url_arr['1']);
        switch ($domain_arr['0']) {
            case 'www.liexin.com' :
            case 'www.ichunt.com' :
            case 'bom.ichunt.com' :
            case 'szbom.ichunt.com' :
            case 't.ichunt.com' :
            case 'sz.ichunt.com' :
            case 'ly.ichunt.com' :
            case 'szly.ichunt.com' :
            case 'member.ichunt.net' :
                return 1;
                break;
            case 'm.liexin.com' :
            case 'm.ichunt.com' :
            case 'tm.ichunt.com' :
            case 'szm.ichunt.com' :
                return 2;
                break;
            case 'scm.ichunt.com':
                return 20;
                break;
            default :
                return -1;
        }
    } else {
        return $pf;
    }
}

/**
 * 价格格式化
 * @param  [type]  $price [description]
 * @param integer $sign [description]
 * @param integer $num [description]
 * @return [type]         [description]
 */
function price_format($price, $sign = 0, $num = 2, $sep = '')
{
    $minus = $price < 0 ? '-' : '';
    $price = number_format(abs($price), $num, '.', $sep);

    // 币种，1-CNY，2-USD，3-HKD，4-EUR，5-GBP，6-CHF，7-ADLY，8-CAD，9-JPY，10-XJP，11-TWD，12-THB，13-VND
    $CURRENCY_SYMBOL = [
        1 => '¥',
        2 => '$',
        3 => 'HK$',
        4 => '€',
        5 => '￡',
        6 => '₣',
        7 => 'S/',
        8 => 'C$',
        9 => 'J￥',
        10 => 'S$',
        11 => 'NT',
        12 => '฿', // THB，‌泰铢符号
        13 => '₫', // VND，‌越南盾符号
    ];

    // switch ($sign) {
    //     case 1: $sign = '¥'; break;
    //     case 2: $sign = '$'; break;
    //     default: $sign = ''; break;
    // }
    if (!empty($sign)) {
        $sign = isset($CURRENCY_SYMBOL[$sign]) ? $CURRENCY_SYMBOL[$sign] : '';
        $price = $sign . $price;
    }
    return $minus . $price;
}


/**
 * 条件过滤
 * @param array $filter 需过滤的字段(接收带表别名使用.)
 * @param array $mapping 数据库字段与表单字段映射
 * @param  [type] $func   回调函数
 * @return [type]         [description]
 */
function map_filter($filter, $mapping = array(), $func = null)
{
    $filter_key = I('request.filter_key', '');
    $filter_val = I('request.filter_val', '');
    $map = array();
    foreach ($filter as &$v) {
        $pre = '';
        $field = $v;
        if (strpos($v, '.')) {
            list($pre, $field) = explode('.', $v, 2);
            $pre .= '.';
        }
        if (!empty($filter_val) && $filter_key == $field) {
            $map[$v] = $filter_val;
            continue;
        }
        if (!isset($_REQUEST[$field])) {
            continue;
        }
        if (!empty($mapping) && isset($mapping[$field])) {
            $arg = I('request.' . $mapping[$field], '');
        } else {
            $arg = I('request.' . $field, '');
        }
        if ($arg === '' || $arg === '0') {
            continue;
        }
        switch ($field) {
            case 'order_sn':
                $map['_string'] = '(O.order_sn = "' . $arg . '" or O.sale_order_sn = "' . $arg . '")';
                break;
            // case 'status' :
            //     $status_mapping = C('ORDER_STATUS_MAPPING.' . intval($arg));
            //     if (!empty($status_mapping)) {
            //         !is_null($status_mapping['1']) && $map[$pre.'order_status'] = array('in', $status_mapping['1']);
            //         !is_null($status_mapping['2']) && $map[$pre.'pay_status'] = array('in', $status_mapping['2']);
            //     }
            //     break;
            case 'stime' :
                $stime = strtotime($arg);
                if (isset($map[$pre . 'create_time']['1'])) {
                    $map[$pre . 'create_time'] = array('between', array($stime, $map[$pre . 'create_time']['1']));
                } else {
                    $map[$pre . 'create_time'] = array('egt', $stime);
                }
                break;
            case 'etime' :
                $etime = strtotime($arg . '+1 day');
                if (isset($map[$pre . 'create_time']['1'])) {
                    $map[$pre . 'create_time'] = array('between', array($map[$pre . 'create_time']['1'], $etime));
                } else {
                    $map[$pre . 'create_time'] = array('elt', $etime);
                }
                break;
            // case 'currency' :
            //     if (in_array($arg, array(0,1))) {
            //         $map[$v] = intval($arg);
            //     }
            //     break;
            case 'ids' :
                $map[$v] = array('in', $arg);
            default :
                if (strpos($arg, ',') !== false) {
                    $map[$v] = ['in', explode(',', $arg)]; // 活动和公告 20,31
                } else {
                    $map[$v] = $arg;
                }
                break;
        }
    }
    if (is_callable($func)) {
        $map = $func(I('request.'), $map);
    } elseif (is_string($func) && method_exists($func)) {
        $map = $func(I('request.'), $map);
    }
    return $map;
}

/**
 * 列表数据格式化
 * @param  [type] $data  [description]
 * @param  [type] $count [description]
 * @param  [type] $page  [description]
 * @return [type]        [description]
 */
function page_data($data, $count = null, $page = null)
{
    $count = empty($count) ? count($data) : $count;
    if (!is_null($page)) {
        list($p, $limit) = explode(',', $page);
        empty($p) && $p = 1;
        empty($limit) && $limit = I('request.limit', 10, 'intval') ? I('request.limit', 10,
            'intval') : C('DEFAULT_PAGE_LIMIT');
    } else {
        $p = 1;
        $limit = $count;
    }
    $datas = array(
        'p' => $p,
        'total_page' => ceil($count / $limit),
        'limit' => $limit,
        'count' => $count,
        'list' => $data,
    );
    return $datas;
}

/**
 * 获取省份
 * @param  [type] $id [description]
 * @return [type]     [description]
 */
function get_nation($id)
{
    static $nation_names;
    if (empty($nation_names[$id])) {
        $NationModel = D('Nation');
        $nation_names[$id]['name_cn'] = $NationModel->getFieldByNationId($id, 'name_cn');
        $nation_names[$id]['name_en'] = $NationModel->getFieldByNationId($id, 'name_en');
    }
    return $nation_names[$id];
}

/**
 * 获取省份
 * @param  [type] $id [description]
 * @return [type]     [description]
 */
function get_province($id)
{
    static $province_names;
    if (empty($province_names[$id])) {
        $RegionModel = D('Region');
        $province_names[$id] = $RegionModel->getFieldByRegionId($id, 'region_name');
    }
    return $province_names[$id];
}

/**
 * 获取城市
 * @param  [type] $id [description]
 * @return [type]     [description]
 */
function get_city($id)
{
    static $city_names;
    if (empty($city_names[$id])) {
        $RegionModel = D('Region');
        $city_names[$id] = $RegionModel->getFieldByRegionId($id, 'region_name');
    }
    return $city_names[$id];
}

/**
 * 获取区域
 * @param  [type] $id [description]
 * @return [type]     [description]
 */
function get_district($id)
{
    static $district_names;
    if (empty($district_names[$id])) {
        $RegionModel = D('Region');
        $district_names[$id] = $RegionModel->getFieldByRegionId($id, 'region_name');
    }
    return $district_names[$id];
}

//格式化数字为大写
function cny_number($number)
{
    static $cnums = array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"),
    $cnyunits = array("圆", "角", "分"),
    $grees = array("拾", "佰", "仟", "万", "拾", "佰", "仟", "亿");
    $full = '整';
    list($ns1, $ns2) = explode(".", floatval($number), 2);
    if (!is_null($ns2)) {
        $ns2 = array_filter(array($ns2[1], $ns2[0]), function ($v) {
            return $v !== '';
        });
        $full = '';
    } else {
        $ns2 = array();
    }
    $ret = array_merge($ns2, array(implode("", _cny_map_unit(str_split($ns1), $grees)), ""));
    $ret = implode("", array_reverse(_cny_map_unit($ret, $cnyunits)));
    return str_replace(array_keys($cnums), $cnums, $ret) . $full;
}

function _cny_map_unit($list, $units)
{
    $ul = count($units);
    $xs = array();
    $free = array_filter($list, function ($a) {
        return $a;
    });
    foreach (array_reverse($list) as $x) {
        $l = count($xs);
        if ($x != "0" || !($l % 4)) {
            $i = $x == '0' ? '' : $x;
            $unit = $units[($l - 1) % $ul];
            if (!$i && $unit == '圆') {
                if (!empty($free)) {
                    $n = '';
                } else {
                    $n = '0' . $unit;
                }
            } else {
                $n = $i . $unit;
            }
        } else {
            $n = is_numeric($xs[0][0]) ? $x : '';
            if ($xs[0] === '0') {
                $n = '';
            }
        }
        array_unshift($xs, $n);
    }
    return $xs;
}

/**
 * 获取供应商ID
 * @param  [type] $goods_id [description]
 * @return [type]           [description]
 */
function get_supplier_id($goods_id)
{
    list($goods_id) = explode('.', $goods_id, 2);
    if (strlen($goods_id) >= 11) {
        return substr($goods_id, 0, 2);
    } else {
        return false;
    }
}

/**
 * model快捷设置是否分页
 * @param  [type] &$model model类
 * @param string $page model类的分页
 * @return [type]         [description]
 */
function limit_page(&$model, $page = '')
{
    if (!is_null($page)) {
        list($p, $limit) = explode(',', $page);
        empty($p) && $p = 1;
        empty($limit) && $limit = I('request.limit', 10, 'intval') ? I('request.limit', 10,
            'intval') : C('DEFAULT_PAGE_LIMIT');
        $model->page($p, $limit);
    }
}

// /**
//  * 消息系统队列（用于批量群发）
//  * @param [type] $coupon_id [description]
//  * @param string $value     [description]
//  */
// function Q_message($template_id = '', $value = '')
// {
//     $name = C('MESSAGE_QUEUE_KEY');
//     if ($value === '@len') {
//         return QL($name.$template_id);
//     }
//     return Q($name.$template_id, $value);
// }


/**
 * 微信消息发送
 * $data = array(
 * 'touser' => $touser,
 * 'template_id' => 'n2blMTzJ_15O3NMW558FwqZEF6DzHa7V_M7U4AqN33Q',
 * 'url' => U('Wechat/Account/order_detail',array('order_sn'=>$params['OrderSn']),'',$_SERVER['HTTP_HOST']),
 * 'topcolor' => '',
 * 'data' => array(
 * 'first'=>array('value'=>'尊敬的'.($params['mobile']).'用户，您在猎芯网上的订单有了新的进展','color'=>'#173177'),
 * 'OrderSn'=>array('value'=>$params['OrderSn'],'color'=>'#173177'),
 * 'OrderStatus'=>array('value'=>$params['OrderStatus'],'color'=>'#173177'),
 * 'remark'=>array('value'=>$params['remark'],'color'=>'#173177')
 * )
 * );
 */
function sendWechat($data, $debug = false)
{
    $wechatModel = wechat();
    return $wechatModel->sendTemplateMessage($data, $debug); // 开启开发模式，失败时可获取失败结果,默认关闭
}

function sendHuaYunWechat($data, $debug = false)
{
    //导入类库
    Vendor('Wechat.Wechat');
    //实例化微信
    $option = array(
        'token' => C('WX_PUBLIC.token'),
        //填写你设定的key
        'encodingaeskey' => isset($options["encodingaeskey"]) ? $options["encodingaeskey"] : '',
        //填写加密用的EncodingAESKey
        'appid' => isset($options["app_id"]) ? $options["app_id"] : C('WX_PUBLIC.appid'),
        //填写高级调用功能的app id, 请在微信开发模式后台查询
        'appsecret' => isset($options["app_secret"]) ? $options["app_secret"] : C('WX_PUBLIC.appkey'),
        //填写高级调用功能的密钥
    );
    $wechatModel = new \Wechat($option);
    return $wechatModel->sendTemplateMessage($data, $debug); // 开启开发模式，失败时可获取失败结果,默认关闭
}

/**
 * 实例化Wechat sdk
 */
function wechat($options = array())
{
    //导入类库
    Vendor('Wechat.Wechat');
    //实例化微信
    $option = array(
        'token' => C('WX_PUBLIC.token'),
        //填写你设定的key
        'encodingaeskey' => isset($options["encodingaeskey"]) ? $options["encodingaeskey"] : '',
        //填写加密用的EncodingAESKey
        'appid' => isset($options["app_id"]) ? $options["app_id"] : C('WX_PUBLIC.appid'),
        //填写高级调用功能的app id, 请在微信开发模式后台查询
        'appsecret' => isset($options["app_secret"]) ? $options["app_secret"] : C('WX_PUBLIC.appkey'),
        //填写高级调用功能的密钥
    );
    $we_obj = new \Wechat($option);
    return $we_obj;
}

/**
 * 实例化Wechat sdk  芯硬创
 * 1猎芯平台  2 芯硬创
 */
function wechatPublic($type = 1)
{
    //导入类库
    Vendor('Wechat.Wechat');
    if ($type == 1) {
        $config = C('WX_PUBLIC');
    } elseif ($type == 2) {
        $config = C('WX_PUBLIC_XinYChuang');
    } else {
        $config = C('WX_PUBLIC');
    }
    //实例化微信
    $option = array(
        'token' => $config["token"], //填写你设定的key
        'encodingaeskey' => isset($config["encodingaeskey"]) ? $config["encodingaeskey"] : "", //填写加密用的EncodingAESKey
        'appid' => $config["appid"], //填写高级调用功能的app id, 请在微信开发模式后台查询
        'appsecret' => $config["appkey"], //填写高级调用功能的密钥
    );
    $we_obj = new \Wechat($option);
    return $we_obj;
}

/**
 * 获取adtag
 * @return [type] [description]
 */
function adtag()
{
    $adtag = I('request.adtag', '');
    empty($adtag) && $adtag = $_COOKIE['adtag'];
    is_null($adtag) && $adtag = '';
    return $adtag;
}

/**
 * 获取ptag
 * @return [type] [description]
 */
function ptag()
{
    // $ptag = I('request.ptag', '');
    empty($ptag) && $ptag = $_COOKIE['ptag'];
    is_null($ptag) && $ptag = '';
    return $ptag;
}

/**
 * 判断获取来自页面关键参数
 * @param  [type] $page  来自页面ptag的关键值 例：search
 * @param  [type] $param 搜索字段名 例：k
 * @return [type]        例：array('k' => 'lm358')
 */
function page_param($page, $param)
{
    $mapping = array(
        'k' => 'searchModel',
    );
    if (isset($mapping[$param])) {
        $search = I('request.' . $mapping[$param], '', 'trim');
    } else {
        $search = I('request.' . $param, '', 'trim');
    }

    $extend = false;
    if (!empty($search)) {
        $extend = array(
            $param => $search,
        );
    } else {
        $ptag = ptag();
        $ptags = explode('-', $ptag);
        if ($ptags[0] == $page || substr($ptags[0], 0, strlen($page)) == $page) {//搜索页
            $extend = array($param => cookie($param));
        }
    }
    return $extend;
}

/**
 * 全站通用获取来源
 * @return [type] [description]
 */
function get_source()
{
    $pf = platform();
    $adtag = adtag();

    $param['pf'] = $pf;
    !empty($adtag) && $param['adtag'] = $adtag;
    $param = str_replace('&', ',', urldecode(http_build_query($param)));
    return $param;
}

/**
 * 判断是否是国际号码，如果是，需要拼接
 * @param $intlCode 国际号码，如中国为0086
 * @param $account 手机号，如中国为***********
 */
function get_inte_mobile($account, $intlCode)
{
    if ($intlCode && strval($intlCode) !== '0086') {
        $account = $intlCode . "+" . $account;
    }
    return $account ? $account : '';
}

/**
 * 判断是否国际号码，如是，入数据库时需要拆分
 */
function get_inte_mobile_arr($mobile)
{
    if (strstr($mobile, "+")) { // 国际手机
        $mobile = explode("+", $mobile);
    }
    return $mobile ? $mobile : false;
}

/**
 * 钉钉 通知企业内部人员
 */
function sendDingTalk($data)
{
    header("Content-type: text/html; charset=utf-8");
    if (!$data['touser']) {
        return false;
    }
    if (!$data['content']) {
        return false;
    }
    $DDAccessToken = getDDAccessToken();
    if (!$DDAccessToken) {
        return false;
    }
    $content = $data['content'];
    Vendor('Dtalk/TopSdk');
    $DingTalkClient = new \DingTalkClient();
    $req = new \CorpMessageCorpconversationAsyncsendRequest();
    $AgentId = C('DING_TALK_OAUTH.AGENTID');
    $req->setMsgtype("text");
    $req->setAgentId($AgentId);
    $req->setUseridList($data['touser']); //manager2645
    //$req->setDeptIdList("123,456");
    $req->setToAllUser("false");
    $req->setMsgcontent($content);
    $resp = $DingTalkClient->execute($req, $DDAccessToken);
    $resp = (array)$resp; // xml 转 array
    $resp['result'] = (array)$resp['result'];
    return $resp;
}

/**
 * 获取钉钉access_token(失效时间为2小时)
 * @var string
 */
function getDDAccessToken($is_new_version = '')
{
    if ($is_new_version) {
        $s_key = 'DDAccessToken_v2';
        $url = 'https://oapi.dingtalk.com/gettoken?appkey=' . C('DING_TALK_OAUTH_V2.AppKey') . '&appsecret=' . C('DING_TALK_OAUTH_V2.AppSecret');
    } else {
        $s_key = 'DDAccessToken';
        $url = 'https://oapi.dingtalk.com/gettoken?corpid=' . C('DING_TALK_OAUTH.CORPID') . '&corpsecret=' . C('DING_TALK_OAUTH.CORPSECRET');
    }

    $DDAccessToken = S($s_key);

    if (!$DDAccessToken) {
        $result = dtcurl($url);
        if ($result['errcode'] !== 0) {
            $DDAccessToken = '';
        } else {
            $DDAccessToken = $result['access_token'];
            $option = array(
                'expire' => 7200,
            );
            S($s_key, $DDAccessToken, $option);
        }
    }
    return $DDAccessToken ? $DDAccessToken : '';
}

/**
 * 钉钉发送请求curl方法
 */
function dtcurl($url, $data = null)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    if (!empty($data)) {
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json;charset=UTF-8'));
    }
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    $output = curl_exec($curl);
    curl_close($curl);
    return json_decode($output, true);
}

//将XML转为array
function xmlToArray($xml)
{
    //禁止引用外部xml实体
    libxml_disable_entity_loader(true);
    $values = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
    return $values;
}

/**
 * 获取行为标记
 * @param string $suffix [description]
 * @return [type]         [description]
 */
function get_scene($behavior, $suffix = '', $clear = false)
{
    $act = C('BEHAVIOR_LOG_TYPE.' . $behavior);
    $tag_key = C('BEHAVIOR_KEY');
    $tag = cookie($tag_key);
    $tag_arr[] = $tag;
    !empty($act) && $tag_arr[] = $act;
    !empty($suffix) && $tag_arr[] = $suffix;
    if ($clear) {
        cookie($tag_key, null);
    }
    return implode('.', $tag_arr);
}

/**
 * 拼凑行为数据
 * @param  [type]  $scene    行为标记,通过get_scene()获取
 * @param integer $behavior 1访问 2注册 3登录 4加入购物车 5立即购买 6立即结算 7立即付款 8客服服务
 * @param integer $result 操作结果 1成功 -1失败
 * @param array $param 额外参数
 * @param integer $event 事件 默认1点击
 * @return [type]            [description]
 */
function behavior_data($scene, $behavior = 1, $result = 1, $param = array(), $event = 1)
{
    $consume_time = bcsub(microtime(true), START_MICROTIME, 4);
    $order_sn = '';
    $user_id = cookie('uid') ? cookie('uid') : '';
    $user_sign = cookie('gid') ? cookie('gid') : '';
    $pf = platform();
    $unset = array('order_sn', 'user_id', 'user_sign', 'pf');
    if (is_array($param)) {
        foreach ($unset as $k => $v) {
            if (isset($param[$v])) {
                $$v = $param[$v];
                unset($param[$v]);
            }
        }
        if (empty($param['data'])) {
            unset($param['data']);
        }
    } elseif (is_string($param)) {
        $param = array($param);
    }
    $search = page_param('search', 'k');
    !empty($search) && $param = array_merge($param, $search);
    if (is_array($param)) {
        $param = urldecode(str_replace('&', ',', http_build_query($param)));
    }
    $ip = I('ip', '', 'trim');
    empty($ip) && $ip = get_client_ip(0, true);
    $data = array(
        'user_id' => $user_id,
        'user_sign' => $user_sign,
        'ip' => ip2long($ip),
        'event' => $event,
        'behavior' => $behavior,
        'scene' => $scene,
        'adtag' => adtag(),
        'ptag' => ptag(),
        'param' => $param,
        'order_sn' => $order_sn,
        'result' => $result,
        'platform' => $pf,
        'create_time' => $_SERVER['REQUEST_TIME'],
        'consume_time' => $consume_time,
    );
    // cookie('ptag', null, array('prefix' => ''));
    return $data;
}

//钉钉推送订餐统计
function dinnerpush($message)
{
    $webhook = "https://oapi.dingtalk.com/robot/send?access_token=0f834c1f519a397f281043be4ce1b420845a11158a1d97747da569ed35b40b04";
    //$webhook = "https://oapi.dingtalk.com/robot/send?access_token=6d0fa85e01a02c39347d011ae973fd21b76c6c7ce582d3ea470c6b65a318848d"; // 测试
    $data = array('msgtype' => 'text', 'text' => array('content' => $message));
    $data_string = json_encode($data);
    //$data_string = str_replace("\\\\", "\\", $data_string);
    $result = dtcurl($webhook, $data_string);
    return $result;
}

/**
 * 根据地区获取运费
 */
function get_shipping_price($province = 0, $city = 0)
{
    static $redis = null;
    if (is_null($redis)) {
        $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.spu"));
    }
    $fee = $redis->get('express_fee');
    $res = 0;
    if (!empty($province) && !empty($city)) {
        if ($city == C('LOCATION_CITY_ID')) {//本地
            $res = $fee['sz_inside'];
            // $res = C('SHIPPING_PRICE.0');
        } elseif ($province == C('LOCATION_PROVINCE_ID')) {//本省
            $res = $fee['gd_inside'];
            // $res = C('SHIPPING_PRICE.1');
        } else {//外省
            $res = $fee['gd_outside'];
            // $res = C('SHIPPING_PRICE.-1');
        }
    }
    return $res;
}

function getShortUrl($order_id)
{

//    $sinaUrl = 'http://api.t.sina.com.cn/short_url/shorten.json';
//    $data['source'] = '3271760578';
//    $data['url_long'] = M_DOMAIN . '/v3/user/orderdetail?order_id=' . $order_id;
    $url_long = M_DOMAIN . '/v3/user/orderdetail?order_id=' . $order_id;
//    $res = get_curl($sinaUrl, $data);
//    $res_arr = json_decode($res, true);
//    $res_arr = $res_arr[0];
//    $short_url = $res_arr['url_short'] ? $res_arr['url_short'] : $data['url_long'];

    return $url_long;
//    return $short_url;
}

/**
 * 检查字段是否空
 * @return [type] [description]
 */
function isset_field($data = array(), $field = array())
{
    if (empty($data)) {
        return null;
    }
    foreach ($field as $k => $v) {
        if (array_key_exists(0, $data)) {
            if (!isset($data[0][$v])) {
                return $v;
            }
        } else {
            if (!isset($data[$v])) {
                return $v;
            }
        }
    }
    return 0;
}

/**
 * 接口服务时加密方式
 * @param  [type] $data      [description]
 * @param  [type] $timestamp [description]
 * @param  [type] $key       [description]
 * @return [type]            [description]
 */
function service_token($data, $timestamp, $key = null)
{
    $key = is_null($key) ? C('SERVICE_KEY') : $key;
    $token = md5($data . $timestamp . $key);
    return $token;
}

/**
 * 来自接口服务的校验数据
 * @return [type] [description]
 */
function service_auth($data, $token)
{
    #todo 2020.9.9 增加跳过验证，方便测试
    if (i("hcy_test") == "1122" || in_array(ACTION_NAME, ["removalactnew", "removalActNew"])) {
        return true;
    }
    $key = C('SERVICE_KEY');
    if (empty($data['timestamp'])) {
        return '时间参数缺失';
    }
    // if ($token != md5(stripslashes(htmlspecialchars_decode($data['data'])).$data['timestamp'].$key)) {
    if ($token != service_token($data['data'], $data['timestamp'], $key)) {
        return '签名校验失败';
    }
    if (time() - $data['timestamp'] > 5000000) {
        return '签名超时';
    }
    return true;
}

/**
 * 写日志
 * @param  [type] $log_content [description]
 * @return [type]              [description]
 */
function logger($log_content, $file_name = "log.xml")
{
    $fg_str = "\n============================================================\n";
    $max_size = 500000;
    $log_filename = $file_name;
    if (file_exists($log_filename) and (abs(filesize($log_filename)) > $max_size)) {
        unlink($log_filename);
    }
    if (is_array($log_content)) {
        file_put_contents($log_filename, date('Y-m-d H:i:s') . "\n" . json_encode($log_content) . $fg_str, FILE_APPEND);
    } else {
        file_put_contents($log_filename, date('Y-m-d H:i:s') . "\n" . $log_content . $fg_str, FILE_APPEND);
    }
}

/**
 * 生成接口域名地址
 * @return [type]              [description]
 */
function GenerateApiUrl($http = 'http://', $type = 'cube')
{
    $domain = $_SERVER['SERVER_NAME'];
    if ($domain == 'szapi.ichunt.com') {
        $http .= 'sz' . $type . '.ichunt.net';
    } elseif ($domain == 'api.ichunt.com') {
        $http .= $type . '.ichunt.net';
    } else {
        $http .= $type . '.liexin.net';
    }
    return $http;
}

/**
 * 接口Token加密
 * @param Array $data
 * @return string
 * Created on 2018-04-25
 */
function WMSencryption($Data)
{
    $Key = 'LX@ichunt.com82560956-0755';
    if (empty($Data['timestamp'])) {
        return false;
    }
    $Token = md5($Data['data'] . $Data['timestamp'] . $Key);
    return $Token;
}


/**
 * 获取供应商信息
 * @param  [type] $supplier_id [description]
 * @return [type]              [description]
 */
function getSupplier($supplier_id)
{
    static $suppliers = array();
    if (isset($suppliers[$supplier_id])) {
        $supplier = $suppliers[$supplier_id];
    } else {
        $supplier = S(C('SUPPLIER_REDIS_KEY') . $supplier_id, '', C('REDIS_LIST.spu'));
        if (empty($supplier)) {
            $SpuModel = D('Common/Spu');
            $supplier = $SpuModel->table('lie_supplier_extra')->where(array('supplier_id' => $supplier_id))->find();
            $supplier['price_json'] = json_decode($supplier['price_json'], true);
        }
        $suppliers[$supplier_id] = $supplier;
    }
    return $supplier;
}

/**
 * 价格阶梯乘以系数及根据活动获得优惠价转换
 * @param  [type] $tiered      阶梯数组
 * @param  [type] $supplier_id 供应商ID
 * @param  [type] $goods_type  商品类型
 * @param  [type] $discount_ratio  折扣比例
 * @param  [type] $ac_type     活动类型
 * @return [type]              [description]
 */
function ladder_transform($tiered, $supplier_id, $goods_type, $discount_ratio, $ac_type)
{
    $supplier = getSupplier($supplier_id);
    $coeff = current($supplier['price_json']);
    $allow_ac = true;//是否允许活动价
    $len = count($tiered);
    $two_equal = true;
    // dump($tiered);
    foreach ($tiered as $k => &$val) {
        if (isset($val['price_cn']) || isset($val['price_us'])) {//新格式
            $hk = $val['price_us'];
            $cn = $val['price_cn'];
            $hk_cost = $val['cost_price'];//美元活动成本价
            if (in_array($goods_type, array(1, 2, 6))) {//联营处理价格系数
                if ($supplier_id == 17) {//专卖不乘美元系数
                } elseif ($supplier_id == 14 && $ac_type == 2) {//mouser 活动 价格特殊处理
                    $cn = $val['price_us'] * $coeff['cn'];//原价RMB
                    if (!empty($hk_cost)) {
                        $hk = $hk_cost * $coeff['hk'];
                        $val['price_ac'] = price_format($hk_cost * $coeff['cn'], 0, 4);//活动RMB
                    } else {
                        $hk = $hk * $coeff['hk'];
                        $val['price_ac'] = $hk;
                    }
                    unset($val['cost_price']);
                } elseif (!empty($coeff)) {
                    $hk = $hk * $coeff['hk'];
                    $cn = $val['price_us'] * $coeff['cn'];
                }
                $hk = price_format($hk, 0, 4);
                $cn = price_format($cn, 0, 4);
                //活动优惠价乘系数
                if ($ac_type == 4) {//某些活动联营不要明着显示优惠价
                    $ratio = $discount_ratio / 100;
                    $cn = $cn * $ratio;

                } elseif (!empty($discount_ratio)) {
                    $ratio = $discount_ratio / 100;
                    $hk_ac = $hk * $ratio;
                    $cn_ac = $cn * $ratio;
                    // $v['price_us_ac'] = price_format($hk_ac, 0, 4);//目前不支持美元活动
                    $val['price_ac'] = price_format($cn_ac, 0, 4);
                }
            }

            $val['price_us'] = price_format($hk, 0, 4);
            $val['price_cn'] = price_format($cn, 0, 4);

        } else {//兼容旧格式上线1周后可废除
            if (in_array($goods_type, array(1, 2, 6))) {//联营处理价格系数
                if (!empty($coeff)) {
                    $hk = price_format($val[1] * $coeff['hk'], 0, 4);
                    $cn = price_format($val[1] * $coeff['cn'], 0, 4);
                    $val[1] = $hk;
                    $val[2] = $cn;
                }
            }
            $val['price_us'] = price_format($val[1], 0, 4);
            $val['price_cn'] = price_format($val[2], 0, 4);
            unset($val[1], $val[2]);
        }

        if (in_array($goods_type, array(1, 2, 6))) {//针对联营。自营基石处理了
            //确定是否可用活动价
            if (isset($val['price_ac']) && $allow_ac) {
                //一二阶梯活动价原价价格一致   或  任意阶梯活动价格为0   取消活动价
                if ($val['price_ac'] == 0) {
                    $allow_ac = false;
                } elseif ($len >= 2) {//多阶梯只判断前两个
                    if (in_array($k, array(0, 1))) {
                        if ($val['price_ac'] != $val['price_cn']) {
                            $two_equal = false;
                        }
                        if ($k != 0 && $two_equal) {
                            $allow_ac = false;
                        }
                    }
                } elseif ($val['price_ac'] == $val['price_cn']) {//当阶梯只有1个时
                    $allow_ac = false;
                }
            }
        }
    }
    (!$allow_ac || $ac_type == 4) && $ac_type = 0;
    $data = array(
        'tiered' => $tiered,
        'ac_type' => $ac_type,
    );
    return $data;
}

/**
 * @param $num         科学计数法字符串  如 2.1E-5
 * @param int $double 小数点保留位数 默认5位
 * @return string
 */

function sctonum($num, $double = 8)
{
    if (false !== stripos($num, "e")) {
        $a = explode("e", strtolower($num));
        return bcmul($a[0], bcpow(10, $a[1], $double), $double);
    } else {
        return $num;
    }
}


/**
 * 导出CSV文件
 * @param array $data 数据
 * @param array $header_data 首行数据
 * @param string $file_name 文件名称
 * @return string
 */
function export_csv($data = [], $header_data = [], $file_name = '')
{
    set_time_limit(0);

    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename=' . $file_name);
    header('Cache-Control: max-age=0');

    $fp = fopen('php://output', 'a');

    if (!empty($header_data)) {
        foreach ($header_data as $key => $value) {
            $header_data[$key] = iconv('utf-8', 'gbk', $value);
        }

        fputcsv($fp, $header_data);
    }

    $num = 0;

    $limit = 100000; //每隔$limit行，刷新一下输出buffer，不要太大，也不要太小

    $count = count($data); //逐行取出数据，不浪费内存

    if ($count > 0) {
        for ($i = 0; $i < $count; $i++) {
            $num++;
            //刷新一下输出buffer，防止由于数据过多造成问题
            if ($limit == $num) {
                ob_flush();
                flush();
                $num = 0;
            }

            $row = $data[$i];

            foreach ($row as $key => $value) {
                $row[$key] = iconv('utf-8', 'gbk', $value);
            }

            fputcsv($fp, $row);
        }
    }

    fclose($fp);
    exit;
}

/**
 * 金额转化积分
 * @param  [type]  $money            金额
 * @param integer $order_goods_type 自营2、联营1
 * @param integer $currency 币种
 * @param integer $order_pay_type 是否为账期订单 （1全额付款） （3账期订单）
 * @return [type]                    [description]
 * 联营1倍，自营2倍，小数向上取整，美元*6.8
 */
function conver_mkt_point($money, $order_goods_type = 1, $currency = 1, $order_pay_type = 1)
{
    $rate = $currency == 1 ? 1 : 6.8;
    $money = $money * $rate;
    if ($order_goods_type == 2) {//自营2倍
        $time = time();

        //如果是调试模式 则代表是测试环境 从8月8日开启双倍积分活动
        if (APP_DEBUG) {
            //如果是活动时间，则有双倍积分
            if ($time > 1565193601 && $time < 1567785599) {
                $money = $money * 2 * 2;
            } else {
                $money = $money * 2;
            }
        } else {
            //如果是生产环境 是活动时间，则有双倍积分 从8月19日开始
            if ($time > 1566144001 && $time < 1567785599) {
                $money = $money * 2 * 2;
            } else {
                $money = $money * 2;
            }

        }
    }

    //如果是账期订单则减少30%积分
    if ($order_pay_type == 3) {
        $money = $money * 0.7;
    }

    return ceil($money);
}

/**
 * 获取钱包ID
 * @param  [type] $user_id [description]
 * @return [type]          [description]
 */
function get_wallet($user_id)
{
    $domain = explode('.', $_SERVER['SERVER_NAME'], 2);
    $prefix = '';
    if ($domain[0] == 'szapi') {
        $prefix = 'SZ';
    }
    return $prefix . str_pad($user_id, 9, '0', STR_PAD_LEFT);
}

/*
 * 反爬虫用html标签替换数字,不包括“.”
 * $number 数字串
 */
function numberToHtml($number)
{
    if (I("hkyefgyd") == 1) { //跳过加密
        return $number;
    }

    $arr = C('NUMBER_TO_CLASS');
    if (empty($arr)) {
        return $number;
    }
    $len = strlen($number);
    if ($len > 0) {
        $str = '';
        for ($i = 0; $i < $len; $i++) {
            $num = substr($number, $i, 1);
            if (preg_match('/\d/', $num)) {
                $index = rand(0, 3);
                $class = $arr[$num][$index];
                $other_class = strRandom($class);
                $num = '<font class="' . $class . $other_class . '"></font>';
            }
            $str .= $num;
        }
        return $str;
    } else {
        return $number;
    }
}

/**
 * 生成纯小写字母的字符串
 *
 */
function strRandom($class = '', $len_num = 3)
{
    $randstr = 'asdwpkxmiqplmzacbmeruwulurjlauejrifkfghjklzxcvbnmqwwertyuiopkdsieurnvewjeilweiskvnx';
    $len = strlen($randstr) - 9;
    $res = '';
    //$class_locat = rand(0,3);
    for ($i = 0; $i < $len_num; $i++) {
//        if($class_locat<3 && $class_locat==$i){
//            $res .= ' '.$class;
//        }
        $start = rand(0, $len);
        $str = substr($randstr, $start, 9);
        $res .= ' ' . $str;

    }
//    if($class_locat>=3){
//        $res .= ' '.$class;
//    }
    return $res;
}

/**
 * 数据进行混淆
 * @param array $variate 整个数组变量
 * @param array $fields 需要混淆的字段
 * @return [type]           [description]
 */
function dataConfuse($variate, $fields = false)
{
    empty($fields) && $fields = array(
        'tiered',
        'min_mpq',
        'min_buy',
        'goods_number',
        'stock',
        'mpl',
        'multiple',
        'purchases'
    );
    $data_fields = array_keys($variate);
    if (!is_array(current($variate))) {
        $confuse_fields = array_intersect($data_fields, $fields);//精简遍历字段
    } else {
        $confuse_fields = $data_fields;
    }
    foreach ($confuse_fields as $v) {
        if (is_array($variate[$v])) {
            $variate[$v] = dataConfuse($variate[$v], $fields);
        } else {
            $variate[$v] = numberToHtml($variate[$v]);
        }
    }
    return $variate;
}

/** 注册或支付后送券(同步，需优化为异步)
 * @param $user_id
 * @param $pf
 * @param $order_id -1(不生效) 3(支付后送券)
 */
function regIssueCoupon($user_id, $pf, $order_id = -1)
{
    $coupons_info = json_decode(get_curl(MARKET_DOMAIN . '/webapi/getShouldSendCoupons', array()), true);
    if ($coupons_info['errcode'] == 0 && count($coupons_info['data']) > 0) {
        $coupon_count = 0;
        $total_coupon_amount = 0;//优惠券总额
        $is_include_discount = false;//是否包含折扣券
        $end_time = array();
        foreach ($coupons_info['data'] as $v) {

            //如果调用此处的传参是支付后送券，但是这个券的赠送方式不是支付后送券
            if ($order_id != -1 && $v['coupon_get_rule'] != 3) {
                //跳过非支付后赠送的优惠券
                continue;
            } else {
                if ($order_id != -1 && $v['coupon_get_rule'] == 3) {

                    //检查一笔订单要送的券的张数，
                    for ($i = 0; $i < $v['order_num']; $i++) {
                        //如果是支付后送券
                        $res = get_curl(MARKET_DOMAIN . '/webapi/issueCouponByPassive', array(
                            'coupon_id' => $v['coupon_id'],
                            'user_id' => $user_id,
                            'pf' => $pf,
                            'need_send_msg' => 0,
                            'order_id' => $order_id
                        ));
                    }
                } else {
                    //其他送券
                    $res = get_curl(MARKET_DOMAIN . '/webapi/issueCouponByPassive', array(
                        'coupon_id' => $v['coupon_id'],
                        'user_id' => $user_id,
                        'pf' => $pf,
                        'need_send_msg' => 0
                    ));
                }
            }

            if (2 == $v['coupon_type']) {
                $is_include_discount = true;
            }
            if (1 == $v['time_type']) {
                $end_time[] = $v['end_time'];
            } else {
                $end_time[] = time() + 60 * 60 * 24 * $v['usable_time'];
            }
            $total_coupon_amount += $v['sale_amount'];
            $coupon_count += 1;
        }
        $deadline = date('Y-m-d', min($end_time));
        $data['keyword'] = 'reg_send_coupon_discount';
        $msg_data = array();
        if (!$is_include_discount) {//是否包含折扣券
            $msg_data['total_coupon_amount'] = $total_coupon_amount;
            $data['keyword'] = 'reg_send_coupon';
        }
        $msg_data['coupon_count'] = $coupon_count;
        $msg_data['dead_line'] = $deadline;
        $msg_data['redirect_url'] = WWW_DOMAIN . '/v3/user/coupon.html';
        $data['touser'] = $user_id;
        $data['data'] = json_encode($msg_data, JSON_UNESCAPED_UNICODE);
        $data = array_merge($data, authkey());

        //试图送券,但是没有符合规则的券可以赠送
        if ($coupon_count <= 0) {
            //不发送消息
            return;
        }

        $res = post_curl(API_DOMAIN . '/msg/sendmessagebyauto', $data);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
    }
}

if (!function_exists("mb_str_split")) {
    /**
     * 将字符串分割为数组
     * @param string $str 字符串
     * @return array       分割得到的数组
     */
    function mb_str_split($str)
    {
        return preg_split('/(?<!^)(?!$)/u', $str);
    }
}


//反爬虫加密验证
function checkSignApi($params = array())
{
    //验证必填参数
    if (empty($_SERVER['HTTP_REFERER'])) {
        return 4;
    }
    if (isset($_GET['asdfghjkl']) && !empty($_GET['asdfghjkl'])) {
        $params = $_GET;
    }
    if (isset($_POST['asdfghjkl']) && !empty($_POST['asdfghjkl'])) {
        $params = $_POST;
    }
    $params['Yo4teW_gid'] = $_COOKIE['Yo4teW_gid'];
    $tt = time();
    if (empty($params['asdfghjkl']) || empty($params['Yo4teW_gid']) || empty($params['qwertyuiop'])) {
        return 1;
    }
    //验证有效时间
//        if($params['qwertyuiop']>($tt+C('SEARCH_TOKEN_EXPIRE_TIME')) || $params['qwertyuiop']<($tt-C('SEARCH_TOKEN_EXPIRE_TIME'))){
//            return 2;
//        }
    //根据参数，按照规则进行签名生成
    $f = array();
    foreach ($params as $k => $v) {
        if ($k == 'asdfghjkl' || $k == '_' || $k == 'callback' || $v == 'NaN' || $v == '' || $v == 'undefined' || $v == 'null' || $v == 'NULL' || strlen(strval($v)) <= 0) {
            continue;
        }
        $f[] = $k . "=" . $v;
    }

    sort($f);
    $_f = join("", $f);
    $_f = preg_replace('/[^0-9a-zA-Z]/', '', $_f);
    $_f = strtoupper($_f);
    $_f = preg_replace('/A|B|C/', '', $_f);
    $_s = sha1($_f);

    //验证签名
    if ($_s == $params['asdfghjkl']) {
        return true;
    }
    return 3;
}


/**
 * 校验客户行为进行正常用户评分
 * @param boolean $super 是否超级权限
 * @param boolean $pass_score 及格分数 默认返回分数， 指定分数则返回布尔值
 * @return [type]         [description]
 */
function visit_score($super = false, $pass_score = false)
{
    $score = 100;
    if ($super) {
        return $score;
    }
    //来源评分
    if (empty($_SERVER['HTTP_REFERER'])) {
        $score -= 100;
    } else {
        $referer = parse_url($_SERVER['HTTP_REFERER']);
        if ($referer['scheme'] != 'https') {
            $score -= 10;
        }
        if (!isset($referer['host'])) {
            $score -= 30;
        }
        //不在域名内只是进行扣分
        $host = [
            'www.' . MAIN_DOMAIN,
            'm.' . MAIN_DOMAIN,
            't.' . MAIN_DOMAIN,
            'tm.' . MAIN_DOMAIN,
            'sz.' . MAIN_DOMAIN,
            'szm.' . MAIN_DOMAIN,
            'bom.' . MAIN_DOMAIN,
            'szbom.' . MAIN_DOMAIN,
            'sc2' . MAIN_DOMAIN,
            'supply' . MAIN_DOMAIN,
        ];
        $dis = [
            'api.' . MAIN_DOMAIN,
            'szapi.' . MAIN_DOMAIN,
        ];
        if (in_array($referer['host'], $dis)) {
            $score -= 100;
        } elseif (!in_array($referer['host'], $host)) {
            $score -= 10;
        }
    }

    //浏览器标识评分
    if (empty($_SERVER['HTTP_USER_AGENT'])) {
        $score -= 100;
    }
    //cookie评分
    $cookie = cookie();
    if (empty($cookie)) {
        $score -= 30;
    } elseif (!isset($cookie[C('COOKIE_PREFIX') . 'gid'])) {
        $score -= 20;
    }
    if ($pass_score !== false) {
        return $score < $pass_score ? false : true;
    }
    return $score;
}


/*
    普通redis初始化(非基石redis连接)
    这个方法给予的自由度更高,可以使用全部redis命令
*/
function redis_init()
{
    return redis_cache_init();
}


/*
    基石redis初始化
*/
function fs_redis_init()
{
    return redis_cache_init('redis_list.spu.');
}

/*
    waf_redis
*/
function waf_redis_init()
{
    return redis_cache_init('redis_list.waf.');
}

/*
    spu_redis
*/
function spu_redis_init()
{
    return redis_cache_init('redis_list.spu_new.');
}

/*
    user_new
*/
function user_old_redis_init()
{
    return redis_cache_init('redis_list.user_old.');
}

/*
    redis初始化基础函数
*/
function redis_cache_init($type = '')
{

    if (strlen($type) <= 0) {
        $config = C('redis_list.spu');
    } else {
        $config = C($type);
    }


    $host = explode(',', $config['host'])[0];

    $option = array(
        'host' => $host,
        'port' => $config['port'],
        'password' => $config['password'],
        'prefix' => '',
    );


    return new \Think\Cache\Driver\Redisrw($option);
}

//获取指定日期所在月的第一天和最后一天
function GetTheMonth($date)
{
    $firstday = date("Y-m-01", strtotime($date));
    $lastday = date("Y-m-d", strtotime("$firstday +1 month -1 day"));
    return array($firstday, $lastday);
}

if (!function_exists('arraySequence')) {
    /**
     * 二维数组根据字段进行排序testing123
     * @params array $array 需要排序的数组
     * @params string $field 排序的字段
     * @params string $sort 排序顺序标志 SORT_DESC 降序；SORT_ASC 升序
     */
    function arraySequence($array, $field, $sort = 'SORT_DESC')
    {
        $arrSort = array();
        foreach ($array as $uniqid => $row) {
            foreach ($row as $key => $value) {
                $arrSort[$key][$uniqid] = $value;
            }
        }
        array_multisort($arrSort[$field], constant($sort), $array);
        return $array;
    }
}


function getip()
{

    static $ip = '';

    $ip = $_SERVER['REMOTE_ADDR'];

    if (isset($_SERVER['HTTP_CDN_SRC_IP'])) {

        $ip = $_SERVER['HTTP_CDN_SRC_IP'];

    } elseif (isset($_SERVER['HTTP_CLIENT_IP']) && preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}$/',
            $_SERVER['HTTP_CLIENT_IP'])) {

        $ip = $_SERVER['HTTP_CLIENT_IP'];

    } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR']) and preg_match_all('#\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}#s',
            $_SERVER['HTTP_X_FORWARDED_FOR'], $matches)) {

        foreach ($matches[0] as $xip) {

            if (!preg_match('#^(10|172\.16|192\.168)\.#', $xip)) {

                $ip = $xip;

                break;

            }

        }

    }

    return $ip;
}


/**
 * 跨域头部设置
 */
function setHeader()
{
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    $origin_arr = explode('//', $origin);
    $allow_origin = C('ALLOW_ORIGIN');
    if (in_array($origin_arr['1'], $allow_origin)) {
        header('Access-Control-Allow-Origin:' . $origin);
        header('Access-Control-Allow-Credentials:true');
        header('Access-Control-Allow-Methods:POST');
        header('Access-Control-Allow-Headers:x-requested-with,content-type,hunt_types');
    }
    header("Content-type: text/html; charset=utf-8");
}

/*
    字符串脱敏
*/
function desensitize($string, $start = 0, $length = 0, $re = '*')
{
    if (empty($string) || empty($length) || empty($re)) {
        return $string;
    }
    $end = $start + $length;
    $strlen = mb_strlen($string);
    $str_arr = array();
    for ($i = 0; $i < $strlen; $i++) {
        if ($i >= $start && $i < $end) {
            $str_arr[] = $re;
        } else {
            $str_arr[] = mb_substr($string, $i, 1);
        }
    }
    return implode('', $str_arr);
}


/*
    随机生成手机号
    $length 手机号个数
*/
function rand_phone($length = 10)
{
    //匹配手机号的正则表达式 #^(13[0-9]|14[47]|15[0-35-9]|17[6-8]|18[0-9])([0-9]{8})$#
    $arr = array(
        130,
        131,
        132,
        133,
        134,
        135,
        136,
        137,
        138,
        139,
        144,
        147,
        150,
        151,
        152,
        153,
        155,
        156,
        157,
        158,
        159,
        176,
        177,
        178,
        180,
        181,
        182,
        183,
        184,
        185,
        186,
        187,
        188,
        189,
    );

    for ($i = 0; $i < $length; $i++) {
        $tmp[] = $arr[array_rand($arr)] . '' . mt_rand(1000, 9999) . '' . mt_rand(1000, 9999);
    }

    return array_unique($tmp);
}

/*
 * 自营发送钉钉消息
 */
function dingZy($content)
{
    $data = array(
        'msgtype' => "text",
        'text' => array(
            'content' => "自营订单出库同步wms:\n" . $content,
        ),
    );
    $data = json_encode($data);
    $zyDingUrl = "https://oapi.dingtalk.com/robot/send?access_token=b183a87e7f51463d41956f9b0516d56f402fd7e626f80ea394056d59c907d49c";
    $bk = post_curl($zyDingUrl, $data, array('Content-Type:application/json;charset=UTF-8'));
}

function subtext($text, $length)
{
    if (mb_strlen($text, 'gbk') > $length) {
        return mb_substr($text, 0, $length, 'utf8') . '';
    }
    return $text;
}

/*
 * 判断运行环境是否是linux
 */
function checkPhpUname()
{
    $os_name = PHP_OS;
    if (strpos($os_name, "Linux") !== false) {
        return true;
    } else {
        return false;
    }
}

//将img域名的http改成https
function changeImgDomainToHttps($url)
{
    $searchString = 'http://img.ichunt.com';
    if (strpos($url, $searchString) !== false) {
        $url = str_replace($searchString, 'https://img.ichunt.com', $url);
    }
    return $url;
}

//获取商品链接(区分联营和自营的链接)
function getGoodsUrl($goodsId)
{
    return strlen($goodsId) == 19 ? WWW_DOMAIN . '/goods_' . $goodsId . '.html'
        : WWW_DOMAIN . '/item/' . $goodsId . '.html';
}

//获取站点域名
function getSiteUrl()
{
    $https = isHttps() ? 'https://' : 'http://';
    $domain = $https . $_SERVER['HTTP_HOST'];
    return $domain;
}

function isHttps()
{
    if (defined('HTTPS') && HTTPS) {
        return true;
    }
    if (!isset($_SERVER['HTTPS'])) {
        return false;
    }
    if ($_SERVER['HTTPS'] === 1) {  //Apache
        return true;
    } elseif ($_SERVER['HTTPS'] === 'on') { //IIS
        return true;
    } elseif ($_SERVER['SERVER_PORT'] == 443) { //其他
        return true;
    }
    return false;
}

//获取主页地址,是否包含v3
function getMainDomain()
{
    if (strpos(WWW_DOMAIN, 'liexin') != false) {
        return WWW_DOMAIN . '/v3/';
    }

    return WWW_DOMAIN;
}

function replaceSpace($str)
{
    $str = str_replace(" ", " ", $str);
    //头尾的空格去掉
    $str = trim($str);
    return $str;
}

function isValidEmail($email)
{
    // 如果是单个电子邮件地址，直接进行验证
    if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return true;
    }

    // 将字符串分割成多个电子邮件地址
    $emails = explode(',', $email);
    foreach ($emails as $singleEmail) {
        // 去除可能存在的空格
        $singleEmail = trim($singleEmail);
        // 验证每个单独的电子邮件地址
        if (!filter_var($singleEmail, FILTER_VALIDATE_EMAIL)) {
            return false; // 如果任何一个地址无效，返回false
        }
    }
    return true; // 所有地址都有效，返回true
}


// 将实体编码转为普通字符，并去掉字符转义
function strConv($str)
{
    if (empty($str)) {
        return $str;
    }
    return stripslashes(htmlspecialchars_decode($str));
}


function apiReturn($code = 0, $msg = '', $extend = [])
{
    $data = array(
        'err_code' => $code,
        'err_msg' => $msg,
        'data' => $extend,
    );
    if (isset($_GET['callback']) && !empty($_GET['callback'])) {
        echo $_GET['callback'] . '(' . json_encode($data) . ')';
        exit;
    } else {
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

function get_curl_new($url, $data = [], $header = array())
{
    $ch = curl_init();

    if (!empty($data)) {
        $url = $url . '?' . http_build_query($data);
    }

    curl_setopt($ch, CURLOPT_URL, $url);

    if (!empty($header)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    }

    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    if (strpos($url, 'https://') !== false) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    }
    $output = curl_exec($ch);
    curl_close($ch);
    return $output;
}

function simpleEncrypt($data, $key) {
    // 生成随机盐（8字节）
    $salt = substr(md5(uniqid(rand(), true)), 0, 8);

    // 使用 HMAC-SHA256 生成密钥哈希
    $keyHash = hash_hmac('sha256', $key, $salt);

    // 异或加密（简单对称加密）
    $encrypted = '';
    for ($i = 0; $i < strlen($data); $i++) {
        $encrypted .= $data[$i] ^ $keyHash[$i % strlen($keyHash)];
    }

    // 合并盐 + 加密数据，并做 URL 安全的 Base64
    $combined = $salt . $encrypted;
    $base64 = base64_encode($combined);

    // 替换 Base64 中的 URL 不安全字符
    $urlSafe = strtr($base64, '+/=', '-_,');

    return $urlSafe;
}


function simpleDecrypt($encrypted, $key) {
    // 还原 URL 安全的 Base64
    $base64 = strtr($encrypted, '-_,', '+/=');

    // 解码 Base64
    $combined = base64_decode($base64);
    if ($combined === false) return false;

    // 提取盐（前8字节）
    $salt = substr($combined, 0, 8);
    $encryptedData = substr($combined, 8);

    // 重新生成密钥哈希
    $keyHash = hash_hmac('sha256', $key, $salt);

    // 异或解密
    $decrypted = '';
    for ($i = 0; $i < strlen($encryptedData); $i++) {
        $decrypted .= $encryptedData[$i] ^ $keyHash[$i % strlen($keyHash)];
    }

    return $decrypted;
}

function md5_32($str,$key="ichuntvil1LIV1bx!xX0UIUI")
{
    return md5($str.$key);
}
