<?php
/**
 * 获取注册访问次数
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_reg_visit($value = '', $options = null)
{
    $key = C('REG_VISIT_REDISY_KEY');
    if ($value !== '') {
        $option = array(
            'expire' => strtotime(date('Y-m-d H:00:00') . '+1 hour') - time(),
        );
        is_null($options) && $options = array();
        $options = array_merge($option, $options);
    }
    try {
        return S($key, $value, $options);
    } catch (\Exception $e) {
        return false;
    }
}

/**
 * 获取设置skey
 * @param [type] $user_id [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_lskey($user_id, $platform, $value = '', $options = null)
{
    $key = C('LSKEY_REDIS_KEY');
    $options = C('REDIS_LIST.user_new'); // 注册、登录用户信息切换到新的Redis服务器 - ********

    $platform .= '_';
    if ($value !== '') {
        $option = array(
            'expire' => C('LSKEY_EXPIRE_TIME')
        );
        is_null($options) && $options = array();
        $options = array_merge($option, $options);
    }
    return S($key.$platform.$user_id, $value, $options);
}

/**
 * 获取设置skey
 * @param [type] $user_id [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_skey($user_id, $platform, $value = '', $options = null)
{
    $key = C('SKEY_REDIS_KEY');
    $options = C('REDIS_LIST.user_new'); // 注册、登录用户信息切换到新的Redis服务器 - ********

    $platform .= '_';
    if ($value !== '') {
        $option = array(
            'expire' => C('SKEY_EXPIRE_TIME')
        );
        is_null($options) && $options = array();
        $options = array_merge($option, $options);
    }
    return S($key.$platform.$user_id, $value, $options);
}

/**
 * 获取设置PWD 用于更改|忘记密码时 获取user_id
 * @param [type] $account [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_pwd($account, $value = '', $options = null)
{
    $key = C('PWD_REDIS_KEY');
    $options = C('REDIS_LIST.user_new'); // 注册、登录用户信息切换到新的Redis服务器 - ********

    if ($value !== '') {
        $option = array(
            'expire' => C('PWD_EXPIRE_TIME')
        );
        is_null($options) && $options = array();
        $options = array_merge($option, $options);
    }
    return S($key.$account, $value, $options);
}
/**
 * 获取设置用户信息
 * @param [type] $user_id [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_user($user_id, $value = '', $options = null)
{
    $key = C('USER_REDIS_KEY');
    $options = C('REDIS_LIST.user_new'); // 注册、登录用户信息切换到新的Redis服务器 - ********

    try {
        return S($key.$user_id, $value, $options);
    } catch (\Exception $e) {
        return false;
    }
}

function S_ucenter($field, $value = '')
{
    $preKey = C('UCENTER_USER_REDIS_KEY');
    $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.user_new"));
    return $redis->hget($preKey,$field);
}

/**
 * @param $user_id
 * @param string $value
 * @param null $options
 */
function S_user_point_info($user_id, $value = '', $options = null)
{
    $key = C('USER_POINT_INFO_REDIS_KEY');
    return S($key.$user_id, $value, $options);
}

/**
 * 获取设置钱包信息
 * @param [type] $user_id [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_wallet($user_id, $value = '', $options = null)
{
    $key = C('WALLET_REDIS_KEY');
    return S($key.$user_id, $value, $options);
}

/**
 * 获取设置用户公司信息
 * @param [type] $user_id [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_company($user_id, $value = '', $options = null)
{
    $key = C('COMPANY_REDIS_KEY');
    return S($key.$user_id, $value, $options);
}

/**
 * 获取设置账号
 * @param [type] $account [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_account($account, $value = '', $options = null)
{
    $options = C('REDIS_LIST.user_new'); // 注册、登录用户信息切换到新的Redis服务器 - ********

    if (is_email($account)) {
        $key = C('EMAIL_REDIS_KEY');
    } elseif (is_mobile($account)) {
        $key = C('MOBILE_REDIS_KEY');
    } else {
        $key = C('NAME_REDIS_KEY');
    }
    return S($key.$account, $value, $options);
}

/**
 * 获取设置安全手机校验码
 */
function S_verifysafe($user_id, $value = '', $options = null)
{
    $key = C('VERIFY_SAFE_KEY');
    if ($value !== '') {
        $option = array(
            'expire' => C('VERIFY_SAFE_EXPIRE_TIME')
        );
        is_null($options) && $options = array();
        $options = array_merge($option, $options);
    }
    return S($key.$user_id, $value, $options);
}

/**
 * 获取设置短信验证码
 * @param [type] $mobile  [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_sms($mobile, $channel = null, $value = '', $options = null)
{
    $key = C('SMS_MOBILE_KEY');
    if ($value !== '') {
        $time = mktime(0, 0, -1, date('m'), date('d')+1, date('Y')) - time();//距离第二天秒数
        $option = array(
            'expire' => $time,
        );
        is_null($options) && $options = array();
        $options = array_merge($option, $options);
    }
    if (is_null($channel)) {
        return S($key.$mobile, $value, $options);
    }
    return S($key.$mobile.'_'.$channel, $value, $options);
}

/**
 * 获取设置第三方QQ映射
 * @param [type] $id      [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_qq($id, $value = '', $options = null)
{
    $key = C('QQ_ID_KEY');
    return S($key.$id, $value, $options);
}

/**
 * 获取设置微信UnionID映射
 * @param [type] $id      [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_wechat($id, $value = '', $options = null)
{
    $key = C('WECHAT_ID_KEY');
    $option = array('type'=>'redisrw');
    is_array($options) && $option = array_merge($option, $options);
    return S($key.$id, $value, $option);
}

/**
 * 获取微信授权登录信息
 * @param [type] $token  [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_wechatinfo($token, $value = '', $options = null)
{
    $key = C('WECHAT_INFO_ID_KEY');
    $expire = C('WX_EXPIRE_TIME');
    if ($value !== '') {
        $option = array(
            'expire' => $expire,
        );
        is_null($options) && $options = array();
        $options = array_merge($option, $options);
    }
    return S($key.$token, $value, $options);
}

function S_wechatinfoApi($token, $value = '', $options = null)
{
    $key = 'api_'.C('WECHAT_INFO_ID_KEY');
    $redis =  user_old_redis_init();
    $result = $redis->get($key.$token);
    if (!is_array($result)&& !empty($result)) {
        $result = json_decode($result, true);
    }
    return $result;
    //$expire = C('WX_EXPIRE_TIME');
    //if ($value !== '') {
    //    $option = array(
    //        'expire' => $expire,
    //    );
    //    is_null($options) && $options = array();
    //    $options = array_merge($option, $options);
    //}
    //return S($key.$token, $value, $options);
}

/**
 * 获取设置token
 * @param [type] $token  [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_token($token, $value = '', $options = null)
{
    $key = C('TOKEN_REDIS_KEY');
    $expire = C('EMAIL_EXPIRE_TIME');
    $type = $value['type'];
    if ($value !== '') {
        $option = array(
            'expire' => $expire[$type],
        );
        is_null($options) && $options = array();
        $options = array_merge($option, $options);
    }
    return S($key.$token, $value, $options);
}

/**
 * 获取设置csrf
 * @param [type] $cookie  [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_csrf($cookie, $value = '', $options = null)
{
    $key = C('CSRF_REDIS_KEY');
    $options = C('REDIS_LIST.user_new'); // 注册、登录用户信息切换到新的Redis服务器 - ********

    if ($value !== '') {
        $option = array(
            'expire' => C('CSRF_EXPIRE_TIME'),
        );
        is_null($options) && $options = array();
        $options = array_merge($option, $options);
    }

    // dump(S('api_csrf_2104c885316e99da535d68736305ea26', '', $options));
    return S($key.$cookie, $value, $options);
}

/**
 * 获取设置购物车数量
 * @param [type] $user_id   [description]
 * @param [type] $user_sign [description]
 * @param string $value     [description]
 * @param [type] $options   [description]
 */
function S_cart_num($user_id, $user_sign, $value = '', $options = null)
{
    $key = C('CARTNUM_REDIS_KEY');
    if ($value !== '') {
        $option = array(
            'expire' => C('CARTNUM_REDIS_TIME'),
        );
        is_null($options) && $options = array();
        $options = array_merge($option, $options);
    }
    if (!empty($user_id)) {
        $user = &$user_id;
    } else {
        $user = &$user_sign;
    }
    return S($key.$user, $value, $options);
}


/**
 * 获取设置优惠券
 * @param [type] $coupon_id [description]
 * @param string $value     [description]
 * @param [type] $options   [description]
 */
function S_coupon($coupon_id, $value = '', $options = null)
{
    $key = C('COUPON_REDIS_KEY');
    $option = C('REDIS_LIST.coupon');
    is_null($options) && $options = array();
    $options = array_merge($option, $options);
    return Sredis($key.$coupon_id, $value, $options);
}

/**
 * 获取设置优惠券统计
 * @param [type] $user_id [description]
 * @param string $value   [description]
 */
function S_ucoupon_count($user_id, $value = '')
{
    $options = C('REDIS_LIST.coupon');
    $key = C('UCOUPON_COUNT_KEY');
    if ($value !== '') {
        $option = array(
            'expire' => C('UCOUPON_COUNT_EXPIRE_TIME'),
        );
        is_null($options) && $options = array();
        $options = array_merge($option, $options);
    }
    return Sredis($key.$user_id, $value, $options);
}

/**
 * 优惠券队列
 * @param [type] $coupon_id [description]
 * @param string $value     [description]
 */
function Q_coupon($coupon_id, $value = '')
{
    $options = C('REDIS_LIST.coupon');
    $name = C('COUPON_QUEUE_KEY');
    if ($value === '@len') {
        return QL($name.$coupon_id, $options);
    }
    return Q($name.$coupon_id, $value, $options);
}

/**
 * 队列方法
 * @param [type]  $name  [description]
 * @param string  $value [description]
 * @param boolean $len   [description]
 */
function Q($name = '', $value = '', $options = array())
{
    static $queue = '';
    if (empty($queue)) {
        $queue = new \Think\Cache\Driver\RedisRW($options);
    }
    if (empty($name)) {
        return $queue;
    }
    if (!empty($value)) {
        $result = $queue->qpush($name, $value);
    } elseif (is_null($value)) {
        $result = $queue->rm($name);
    } else {
        $result = $queue->qpop($name);
    }
    return $result;
}

/**
 * 队列长度
 * @param [type] $name [description]
 */
function QL($name, $options = array())
{
    $queue = Q('', '', $options);
    $result = $queue->qlen($name);
    return $result;
}

/**
 * 额外redis缓存 （由于不修改TP源码所以重写，由于切换了连接后，造成后续使用S的不会用回默认连接，导致读错连接）
 * 解决方案
 * 1、不使用S读写redis，使用本方法直接实例化新的
 * 2、所有使用S的都带上options参数

 这个方法缺陷过多,同时还繁琐.而且引用过多不好改了.建议使用redis_init方法进行redis初始化,自由度更高
 * @param string $name    [description]
 * @param string $value   [description]
 * @param array  $options [description]
 */
function Sredis($name = '', $value = '', $options = array())
{
    static $redis = '';
    $mix = to_guid_string($options);
    if (empty($redis[$mix])) {
        $redis[$mix] = new \Think\Cache\Driver\RedisRW($options);
    }
    if (empty($name)) {
        return $redis[$mix];
    }
    if (!is_null($value) && $value !== '') {
        $result = $redis[$mix]->set($name, $value);
    } elseif (is_null($value)) {
        $result = $redis[$mix]->rm($name);
    } else {
        $result = $redis[$mix]->get($name);
    }
    return $result;
}

/**
 * 消息系统队列（用于批量群发）
 * @param [type] $coupon_id [description]
 * @param string $value     [description]
 */
function Q_message($template_id = '', $value = '')
{
    $name = C('MESSAGE_QUEUE_KEY');
    if ($value === '@len') {
        return QL($name.$template_id);
    }
    return Q($name.$template_id, $value);
}

/**
 * 供应商附加费
 * @param [type] $supp_id [description]
 * @param string $value   [description]
 */
function S_suppfee($supp_id, $value = '')
{
    $key = C('SUPP_EXTEND_FEE_KEY');
    return Sredis($key.$supp_id, $value, C('REDIS_LIST.spu'));
}

/** 获取/设置用户签到数据
 * @param $user_id
 * @param string $value
 * @return mixed
 */
function S_sign_in($user_id, $value = ''){
    $key = C('USER_SIGN_IN_KEY');
    return S($key.$user_id,$value);
}
/**
 * 文章链接词(正式)
 * @param [type] $art_id [description]
 * @param string $value   [description]
 * @param [type] $options [description]
 */
function S_article($art_id, $value = '', $options = null)
{
    $key = C('ARTICLE_REDIS_KEY');
    return S($key.$art_id, $value, $options);
}

/** 获取/设置用户分享跳转地址
 * @param $user_id
 * @param string $value
 * @return mixed
 */
function S_orgin_url($user_id, $value = ''){
    $key = C('ORIGIN_URL_KEY');
    return S($key.$user_id,$value);
}


/** 邀请活动 用户扫码统计(扫码总次数)
 * @param $invite_user_id
 * @param $activity_id
 * @param string $value
 * @return mixed
 */
function S_invite_qrcode_count($invite_user_id, $activity_id, $value = ''){
    $key = C('INVITE_QRCODE_COUNT_KEY');
    return S($key.$invite_user_id.'|'.$activity_id,$value);
}

/** 邀请活动 用户扫码统计(去重)
 * @param $invite_user_id
 * @param $activity_id
 * @param string $value
 * @return mixed
 */
function S_invite_qrcode_count_distinct($invite_user_id, $activity_id, $value = ''){
    $key = C('INVITE_QRCODE_COUNT_DISTINCT_KEY');
    return S($key.$invite_user_id.'|'.$activity_id,$value);
}

// 获取样片缓存
function S_sample_goods($goods_id, $data = '')
{
    static $redis = null;
    if (is_null($redis)) {
        $redis = new \Think\Cache\Driver\RedisRW(C("REDIS_LIST.spu"));
    }

    if (!$data) {
        return $redis->hget('lie_sample_list', $goods_id);
    }

    return $redis->hset('lie_sample_list', $goods_id, json_encode($data));
}
