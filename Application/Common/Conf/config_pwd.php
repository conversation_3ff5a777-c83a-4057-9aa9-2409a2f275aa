<?php
/**
 * 过滤文件，发布请手动补充修改内容
 */
return array(
    'SMS_URL'      => 'https://sms.dmavip.com/rest/v2/single_sms',
    'SMS_SALT'     => 'klj24hi1324v2x',
    'SMS_USERNAME' => '<EMAIL>',  //短信平台的账号
    'SMS_PASSWORD' => 'Webp0wer',  //短信平台的密码
    // 新短信平台
    'SMS_NEW_URL' => 'http://gateway.iems.net.cn/GsmsHttp?',
    'SMS_NEW_USERNAME' => 'admin',  //短信平台的账号
    'SMS_NEW_VERIFY_NAME' => '70990', // 验证码账号
    'SMS_NEW_MARKET_NAME' => '70991', // 营销账号
    'SMS_NEW_VERIFY_PASSWORD' => '35868984', // 验证码账号密码
    'SMS_NEW_MARKET_PASSWORD' => '03539397', // 营销账号密码
    'SMS_NEW_OVERSEA_NAME' => '71055',
    'SMS_NEW_OVERSEA_PASSWORD' => '95363928',

    //第三方QQ配置
    'QQ_OAUTH'  =>  array(
        'appid'         => '101391249',
        'appkey'        => 'b07506146dc9d4e522c2c5e3f2cfcebc',
        'callback'      => urlencode(API_DOMAIN). '/login/handleqq',
        'scope'         => 'get_user_info',
        'errorReport'   => true,
        'storageType'   => 'file',
        'host'          => 'localhost',
        'user'          => 'root',
        'password'      => 'root',
        'database'      => 'test'
    ),

    //快递鸟配置
    'KD_CONFIG'=>array(
        'EBusinessID' => '1263195',
        'AppKey'      => '86479ffe-edda-4d44-923d-d0808285386f',
        'ReqURL'      => 'http://api.kdniao.cc/Ebusiness/EbusinessOrderHandle.aspx',
        'state'       => array(
                        '2'=>'在途中','3'=>'签收','4'=>'问题件'
                        ),
    ),

    //快递100
    'KD100_CONFIG' => array(
        'reqUrl' => 'http://poll.kuaidi100.com/poll/query.do', //物流查询URL
        'autoUrl' => 'http://www.kuaidi100.com/autonumber/auto', //识别物流公司URL
        'customer' => '15E724EDF9D69A717EB0C67DB97D3C8E',
        'appKey' => 'aPwgQJHB2402',
    ),

    //中金市场模式配置参数
    'CPCN_MARKET_CONFIG' => array(
        'institution_id'        => '003204',
        'notification_url'      => API_DOMAIN . '/pay/notifyreturn/',
    ),

    //中金商户模式配置参数
    'CPCN_BUSINESS_CONFIG' => array(
        'institution_id'        => '004407',
        'notification_url'      => API_DOMAIN . '/pay/notifyreturn/',
    ),

    //猎芯银行账户
    'ICHUNT_ACCOUNT' => array(
        'company_name' => '深圳市猎芯科技有限公司',
        'bank_number' => '443899991010003511764',
        'bank_name' => '交通银行',
        'bank_address' => '交通银行深圳梅林支行',
        'bank_province' => '广东省',
        'bank_city' => '深圳市',
    ),

    //银联B2C配置参数
    'UNIONPAY_CONFIG' => array(
        'unionpay_mid'          =>'***************',
        'unionpay_cer_password' =>'000000',
        'cert_pfx'              => VENDOR_PATH . 'payment/unionpay/certs/700000000000001_acp.pfx',//测试用商户私钥
        'backUrl'               => API_DOMAIN . '/pay/notifyurl/unionpay', //后台通知地址
        'frontUrl'              => API_DOMAIN . '/pay/returnurl/unionpay/1', //前台通知地址
        'refundUrl'             => API_DOMAIN . '/refund/notifyurl/unionpay', //后台通知退款地址
    ),

    //银联B2B配置参数
    'UNIONPAY_B2B_CONFIG' => array(
        'unionpay_mid'          =>'***************',//测试用账号
        'unionpay_cer_password' =>'000000',//测试用密码
        'cert_pfx'              => VENDOR_PATH . 'payment/unionpay_b2b/certs/acp_test_sign.pfx',//测试用商户私钥
        'backUrl'               => API_DOMAIN . '/pay/notifyurl/unionpay_b2b', //后台通知地址
        'frontUrl'              => API_DOMAIN . '/pay/returnurl/unionpay_b2b/1', //前台通知地址
        'refundUrl'             => API_DOMAIN . '/refund/notifyurl/unionpay_b2b', //后台通知退款地址
    ),

    //微信支付配置
    'WXPAY_CONFIG' => array(
        'notify_url'    => API_DOMAIN. '/pay/notifyurl/wxpay',
        'refund_notify_url' => API_DOMAIN. '/refund/notifyurl/wxpay',
    ),
    //小程序配置
    'MINI_PROGRAM_CONFIG' => array(
        'appid'        => 'wxabf31e83cd600f81',
        'appsecret'    => '0371e5e18a20b9037f7b21bbe8964038',
        // 'appid'        => 'wxdbde6e72f40d89cf',
        // 'appsecret'    => 'ae3dd661d3efe5d11521b8646f6b963a',
        'notify_url'    => API_DOMAIN. '/pay/notifyurl/wxpay',
        'refund_notify_url' => API_DOMAIN. '/refund/notifyurl/wxpay',
    ),

    //支付宝配置参数
    'ALIPAY_CONFIG'  =>  array(
        'partner'       => '2088121811976550', //这里是你在成功申请支付宝接口后获取到的PID；
        'key'           => '6o6nyhhfxtdau7b04lc1vanmozdkkwvk', //这里是你在成功申请支付宝接口后获取到的Key
        'sign_type'     => strtoupper('MD5'),
        'input_charset' => strtolower('utf-8'),
        'cacert'        => VENDOR_PATH . 'payment/alipay/certs/cacert.pem',
        'transport'     => 'http',
        'qr_pay_mode'   => '0',
        'seller_id'     => '2088121811976550',  //这里是卖家的支付宝账号，也就是你申请接口时注册的支付宝账号
        'notify_url'    => API_DOMAIN. '/pay/notifyurl/alipay',//这里是异步通知页面url，提交到项目的Pay控制器的notifyurl方法；
        'return_url'    => API_DOMAIN. '/pay/returnurl/alipay/1',//这里是页面跳转通知url，提交到项目的Pay控制器的returnurl方法；
        'payment_type'  => '1',
        'service'       => 'create_direct_pay_by_user'
    ),

    //支付宝H5配置参数
    'ALIPAY_H5_CONFIG' => array(
        'app_id'                => "2017031306203404",//应用ID,您的APPID
        'merchant_private_key'  => file_get_contents(VENDOR_PATH.'payment/alipay/certs/2017031306203404_private.key'),//商户私钥，您的原始格式RSA私钥
        'alipay_public_key'     => file_get_contents(VENDOR_PATH.'payment/alipay/certs/alipay_public.key'),//支付宝公钥
        'notify_url'            => API_DOMAIN. '/pay/notifyurl/alipay',//异步通知地址
        'return_url'            => API_DOMAIN. '/pay/returnurl/alipay/2',//同步跳转
        'charset'               => "UTF-8",//编码格式
        'sign_type'             => "RSA2",//签名方式
        'gatewayUrl'            => "https://openapi.alipay.com/gateway.do",//支付宝网关
    ),

    //微信公众平台 猎芯
    'WX_PUBLIC' => array(
        'appid'         => 'wx8d33064cd478a6f8', //猎芯网内部 测试
        'appkey'        => 'bd5f7d573d81c3adff5a1dd99185f6e6', //猎芯网内部 测试
        'token'         => 'liexin',
        'first_url'     => 'https://m.ichunt.com/v3/activity/gzhdbgg', // 首次关注 跳转链接
        'first_img'     => 'http://mmbiz.qpic.cn/mmbiz_png/zQoDZb0IYx4qFibBmkABotVpVVZ0KOwqklemgibw3wHxiapWrWGBbAXvW8ibTC2UMHFoK6ibNcsq1dPlBPMYNJ3a1wA/0?wx_fmt=png',
        'first_desc'    => "iPhone XS Max、智能音响、扫地机器人、现金红包等大礼等你拿~~~",
        'first_title'    => '欢迎关注猎芯网',
    ),

    //微信公众平台  芯硬创
    'WX_PUBLIC_XinYChuang' => array(
        'appid'         => 'wx4fa0ac0eb8493fbc', //芯硬创
        'appkey'        => '2c331a2e10015dab877fa99d51269baf', //猎芯网内部 测试
        'encodingaeskey'        => 'U6sXvaNPE8Jh45xGuPB5DCTjd0Ju3fe8GipeVYermhN', //猎芯网内部 测试
        'token'         => 'xinyingchuang',
        'first_url'     => 'https://m.ichunt.com/v3/activity/gzhdbgg', // 首次关注 跳转链接
        'first_img'     => 'http://mmbiz.qpic.cn/mmbiz_png/zQoDZb0IYx4qFibBmkABotVpVVZ0KOwqklemgibw3wHxiapWrWGBbAXvW8ibTC2UMHFoK6ibNcsq1dPlBPMYNJ3a1wA/0?wx_fmt=png',
        'first_desc'    => "欢迎关注芯硬件创客!平台自动回复功能暂时受限，领取资料或者加微信技术群，请添加“小创”微信（13266809366）哦~",
        'first_title'    => '欢迎关注芯硬件创客',
    ),

    //微信开放平台
    'WX_OAUTH' => array(
        'appid'             => 'wx90c235cfa4dfbc67',
        'appsecret'         => '7703d27f19c7f10d5aa1ee064a92ca91',
        'callback'          => API_DOMAIN. '/login/handlewechatpc',
        'api_url'           => 'https://api.weixin.qq.com/sns/',
    ),

    //钉钉开放平台
    'DING_TALK_OAUTH' => array(
        'CORPID'            => 'dinga60eaa15e871b53535c2f4657eb6378f', // ding128da27ba1c42e6a35c2f4657eb6378f
        'CORPSECRET'        => 'IKgDRT5QerGvlwrF0qQqKWx0ovsrvF0ZMgDptXAklWTcRyimXDUXm7EqvhCJodvq', // dNMEFxcRB2u4dxT1N3jPYRlzgnmKsKRMyEJkL_WyICL7bNF3u9VsgjOJ1QYPmy31
        'AGENTID'           => '161262997', // 微应用的id 160827674
    ),

    'DING_TALK_OAUTH_V2' => array(
        'AppKey' => 'dingn1ezj9jax6uobaxc',
        'AppSecret' => 'vHi8BRpig41wTTfjTx9HTjb4W0vDhfY05yb_KjhLnukfiyWsqz6PcaYtLgbvrVCa',
    ),


    //'MSG_SUPPLIER_TYPE' => 'LC', // 蓝创云通讯
    // 'MSG_SUPPLIER_TYPE' => 'DEFAULT', // 原来平台
    'MSG_SUPPLIER_TYPE' => 'MW', // 梦网

    // 短信供应商 （梦网，253云通讯）
    'MSG_PWD_INFO' => array(
        'MW' => array(
            'MSGIP' => array(
                'http://*************:8086/sms/v2/std/', //南方短信节点url地址
                'http://*************:8086/sms/v2/std/', //北方短信节点url地址
            ),
            'USERID' => 'J94690',
            'PWD' => '137731',
            'HW' => array( // 国际短信（含中国港澳台）
                'USERID' => 'GJ1095',
                'PWD' => '789632',
            ),
        ),
        'MW_YINGXIAO' => array(
            'MSGIP' => array(
                'http://*************:8086/sms/v2/std/', //南方短信节点url地址
                'http://*************:8086/sms/v2/std/', //北方短信节点url地址
            ),
            'USERID' => 'J94690',
            'PWD' => '137731',
            'HW' => array( // 国际短信（含中国港澳台）
                'USERID' => 'GJ1095',
                'PWD' => '789632',
            ),
        ),
        'LC' => array(
            'USERID' => 'N1644671',
            'PWD' => 'jiyP61IRZ8e534',
            'HW' => array(
                'USERID' => 'I0000650',
                'PWD' => 'kyKjHMY09F0aad',
            ),
        ),
        'MW_GYL' => array(
            'MSGIP' => array(
                'http://*************:8086/sms/v2/std/', //南方短信节点url地址
                'http://*************:8086/sms/v2/std/', //北方短信节点url地址
            ),
            'USERID' => 'J98165',
            'PWD' => '665233',
            'HW' => array( // 国际短信（含中国港澳台）
                'USERID' => 'GJ1185',
                'PWD' => '445644',
            ),
        ),

        'MW_HUAYUN' => array(
            'MSGIP' => array(
                'http://*************:8086/sms/v2/std/', //南方短信节点url地址
                'http://*************:8086/sms/v2/std/', //北方短信节点url地址
            ),
            'USERID' => 'J98165',
            'PWD' => '665233',
            'HW' => array( // 国际短信（含中国港澳台）
                'USERID' => 'GJ1185',
                'PWD' => '445644',
            ),
        ),
    ),

    'SUPPLIER_EMAIL_SEND' => array(
		'<EMAIL>'	
	),


    // 腾讯验证码
    'TX_CAPTCHA' => array(
        'appid' => '2020469075',
        'secretkey' => '0DQCFxbGbcmUdbLj385D6pg**',
    ),




    //京东支付
    'JDPAY'=>[

        'appid'=>'A20200415182',


        'private_key'=>'MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIvmJlMT5ZObgG6QJlxqrUhn2mZAr9m/aCOWVq0brPLmfAD0Ayew4GBNg/nC40ltJEt5QFxBQmLbIaeal1OXsih/wakD/2jv4LdNjqHQb6+ejnpu9aOXpMi+LBJTOfcUH4IOL3avDPJSjlosn7/6wecS3hfoK4C4yBgcax8TZWedAgMBAAECgYBbQse9aj1ZDRe7CE39+5kOorDWA1yzrylADdJ9GpC346MA/C0St0+ngumVBI1AWPJUScSyad/knQ5XR4FwBoUw7qYtQl4TC1FlsvZLzKv/eNQS5O6KU8HhBXRtFeWJB44ySBFmP2iU4tNpNO6kald0WkzqegqSgZ+mhz3IHK5kAQJBAMrVDJXtOPKL64iEikOqPVs2HCFCOvWS5SvAz7MMZUqsE/6LpxVTo65TeoC7m0c380jywQfpJGp01KqIfL3xTg8CQQCwkgA2DRPEAt+r9ivX7yVlVf+vzM1o5R4pqvUmlNt7IdaM72nxZUHtL7AZvIS+S00q3qn++8U9KaJA/0jhxhuTAkBZ+DLC9hzCiYoKXjAuX38jzFah8gzRDT2WMMpwc1kizD16NVwBu73o/6JhI9Z2uryxaOxyo9nBVt1WX4BliHnFAkA6lebuCD7DrbRrST3Y4ueEjRlOSmTpZgQxDSFUUNXWMC8RCisbxl4uhcEneO6OeUu8aq77BKy4E4VbjZiwGNxzAkAuLrWbjOfA6WwuQKEvfGsxkPVrfO3v2xVokKKr7GNCj0ZuQrsXBVQ2HNDFUjl40+gUH3D0Ha4+f8UgJdU67ATL',


        'public_key'=>'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCBLEDGwVOrb4PZbhoiiPHThRMBSjXQgLnneWXpOG3elQafzTsMIT1LzUuBAfpcULCM4O3ohk1zh8FmQyjGZeFlIBi53BBNGEbznq+MiZBRbYlmg4As5ANoSYl9CXRJbgh93LgzAMZ1dJ0WBaHAVly3Llu1CD58Dwkv+AYCDx+IswIDAQAB',


        'private_pkas8_key'=>'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAK2K7rh3Kd4twI9m
Go//uB/BS4ZrnhX/fUjUXJKAGzldlJp2wsb0i76wFADmeLILWztxgOyuF9lnWlli
pLSfhgaXyYHGIcAxmSYJ2FFaA7QyNo2UiZcLXJuknS/w7EbfMjvYQchToMI0CGoY
Avtjo6yAfaVMnxgRMpNVxSrT2WbJAgMBAAECgYABmXIxyQddwNYxfCNqiFTOFh34
Ey3VzhD9hteDkiKuGKqx8b5BzycqjrzgdT9PhMNGfPlPNRuLKWSvxjT2ZTH7AbBO
wA40iGqur0gquRA9/l3tWOHMsNxOlHoKXqMpFqHWoRvyzuhQsXJNnrFzPB4kctUq
JEcaBU1YjfruO+3hwQJBANcY218KbBzVEEj1sOqyhMe6LPzm/jNsDqF2FT8Dadfr
qBlFRFANF9JXFq9S81FyH/hiLF9Ma5IezMKyodFsfH0CQQDOiyp8udWDTBhPyFi1
RAWfE2LK25KhQ9xcvnsLHYkyHVqMum51iamiirpnYuxH3IpsTDmWVRciGcUqLZH4
NEE9AkB5WRehxbJ6jaVTU/DMZ4xLVXg98V9lyUdzxbd0xks3Okaqgw5oDwrqaqFs
peKJh1YL/e7EkMt7Mw8XpElwacDNAkASE8wcLoepfjeChB5/fvye96tl5eHni3D2
DRXn2yXu5PLP7mFMmEfomgvGkLntcDgNUn6X0cq8iVTgZCyqdKhhAkEAgoGO/7Yy
pMcwF2L1kwHyv38pmSedJY4X8SQK1gXxUU09tqMpQc1F5czXFIzKFViTXWAuwkI+
HCbku6pyhoiCtw==',


        'md5_salt'=>'Bfc9CFWaHV4UXEl0L1CVx1Wf',

        'encrypt_type'=>'ENV_RSA',

        'sign_type'=>'MD5_RSA',

        'appId_type'=>'0',

        ///************:9655
        /// ************:8112
        'server'=>'http://*************:1058',

        'product_code'=>'25041',

        'notify_main'=>'https://jrapi.ichunt.com',

        //魔盒url
        'mohe_url'=>'http://redis.liexindev.me:8082/',

        //指定收款的账户号
        'receiveCardNo'=>'202108251643',

        //指定收款的账户名称
        'receiveName'=>'深圳市亿诚电子科技有限公司',


        //京东金融联合登陆参数

        'login_openPublicKey'=>'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHmBFArlz4wQ0Qz3iFmPG2YHWYJt8dbgJybd6rMykcB/D+HBDZ+S+EpB0zOTst/Y4k8niZe1PvqzjZ2+Npq2f9jTCUJQ4nnTXk6lXd5utFNvA+y6uTfQ1YdbiZAD+OkvYdgL5Wl1LQ6MuWCF4ZI/fm8zDL9zVtCvfYQHm3MnZJNwIDAQAB',
        'login_md5Salt'=>'74bf4f4d92ea566b0820fffe69a04d20',
        'login_sysId'=>'123',
        'login_token'=>'456',
        'login_openSys'=>'A20200702110',
        'login_processId'=>'262',
        'login_url'=>'http://*************:1058/shapi/v1/user/register',


        'login_openId'=>'ltest009',
        'login_openUser'=>'ltest009',
        'login_appId'=>'A20200702110',
        'login_appPrivateKey'=>'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL6eI/e8amibnrMH0ap3/kdifYjVDZxpheEs0Y8czDavbavW1VmDD17MgLcJKs8Sqp3H3hbOndJOKTsHzCMy16wTD23G+2F0uYPKbKpBR4A8IlX76gu7M4fZWY7ojAElXChnIjCHZ6rAgTxtYauC/ddp0lgqKJx6MT7XnGL/Ja9DAgMBAAECgYEAtHAUEg1MGOu+xTAQm7JshHxJ4r3y3W3SKn33fOZAtT9IrJJ3cP0sDou9CAZofI3p7eNlaud447vTMarG5UbaeciPFjWSyaH+b3C/DDoOoxvbOd0IJs35oYwFxQ6sFZcG72zQj7wsyM3D8II5djfkKsBIjRwjkosqkpkv2ZRYGpkCQQDr3/aLOLWXFDqvIFFRupYpumQR+wmuMYxvR2Obgks/yPXJJjqSGjCRb/XI50T14uJEOUGhYcI57zdoUFbp+H6VAkEAzuGpBZuDc3obg/qiGGM15lMbYXFyB2TQvi+Hg/uNGuzcsdl+TzXPvewQxxeHwIqZa2wk2LSikoENDVBeYdl4dwJARaHJM5JbMS18oYRl3T265LisoA4+7licP6GQizDsq/jUbjxF4CmxGs41fcigOAJxj3hjopOsddPjxHyrG8kK/QJATH/xaltpLkhW6GTDj9UP102f8FZs3gMPlWQp7koUkYJI0ZMlO9EgMpCaW6R91FrsBGcG8QNKonYKB4RSgkhSXQJAcdYWicTcJFCYOFxrRz4Ag7L6bqT57hiDf9cLLox/NGQ/ZIlOt7CTmv1/sy4yVfF1pKh/9GJM3fb0GGrAAOE8Wg==',


    ],

    'async_url' => 'http://*************:8700/callback', // 异步转发

);