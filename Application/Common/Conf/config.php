<?php
return array(
    //平台（站点）
    'PLAT_FORM' => array(
        '1' =>  'pc',           //PC
        '2' =>  'mobile',       //H5
        '3' =>  'erp',          //ERP
        '4' =>  'member_system',//会员系统
        '5' =>  'coupon_system',//营销系统
        '6' =>  'applet',       //小程序
        '20' => 'supply',       //供应链会员
    ),
    'LOAD_EXT_CONFIG' => 'config_db,config_url,config_pwd,config_supplier,config_param,config_switch,config_args',

    "api_push_data"=>"http://192.168.1.252:16590/mq/push", //异步同步系统

    //cookie_session设置
    'SESSION_PREFIX' => 'lx_home_',
    'COOKIE_PREFIX' => 'Yo4teW_',
    'API_REDIS' => '',
    'SESSION_OPTIONS' => array(
        'domain' => MAIN_DOMAIN,//实现跨域
    ),

    //session_key
    "MSG_SEND_SESSION_KEY" => 'msg_send_',
    'COOKIE_DOMAIN' => MAIN_DOMAIN,//实现跨域

    //默认使用过滤参数方法
    'DEFAULT_FILTER' => 'trim,htmlspecialchars,addslashes',

    //允许跨域站点
    'ALLOW_ORIGIN' => array(
        'h5.' . MAIN_DOMAIN,
        'www.' . MAIN_DOMAIN,
        'ly.' . MAIN_DOMAIN,
        'sc.' . MAIN_DOMAIN,
        'supply.' . MAIN_DOMAIN,
        'sc2.' . MAIN_DOMAIN,
        'szsc.' . MAIN_DOMAIN,
        'tsc.' . MAIN_DOMAIN,
        'm.' . MAIN_DOMAIN,
        'a.' . MAIN_DOMAIN,
        't.' . MAIN_DOMAIN,
        'tm.' . MAIN_DOMAIN,
        'sz.' . MAIN_DOMAIN,
        'szm.' . MAIN_DOMAIN,
        'bom.' . MAIN_DOMAIN,
        'szbom.' . MAIN_DOMAIN,
        'member.' . NET_DOMAIN,
        'lmember.' . NET_DOMAIN,
        'oss.' . NET_DOMAIN,
        'data.' . NET_DOMAIN,
        'cms.' . NET_DOMAIN,
        'user.' . NET_DOMAIN,
        'cube.' . NET_DOMAIN,
        'cube.' . MAIN_DOMAIN,
        'szcube.' . NET_DOMAIN,
        'footstone.' . NET_DOMAIN,
        'footstone.' . MAIN_DOMAIN,
        'szfootstone.' . NET_DOMAIN,
        'marketing.' . NET_DOMAIN,
        'marketing.' . MAIN_DOMAIN,
        'szmarketing.' . NET_DOMAIN,
        'pur.' . NET_DOMAIN,
        'pur.' . MAIN_DOMAIN,
        'szpur.' . NET_DOMAIN,
        'supplier.' . NET_DOMAIN,
        'szsupplier.' . NET_DOMAIN,
        'ladmin.' . NET_DOMAIN,
        'supply.'.MAIN_DOMAIN,
        'mip'.MAIN_DOMAIN,
        'ldata.' . NET_DOMAIN,
        'www.'. XHT_DOMAIN, // 信宏泰PC
        'm.'. XHT_DOMAIN, // 信宏泰H5
        'access.' . NET_DOMAIN,
        'order.' . NET_DOMAIN,
        'szorder.' . NET_DOMAIN,
        'lorder.' . NET_DOMAIN,
        'crm.' . NET_DOMAIN,
        'bbs.' . NET_DOMAIN,
        'shuju.' . NET_DOMAIN,
        'supply.' . NET_DOMAIN,
        'res.' . MAIN_DOMAIN,
        'frq.' . IC_NET_DOMAIN,
        'sc.' . NET_DOMAIN,
        "frq.liexindev.net",
        'order.liexindev.net',
        'pur.liexindev.net',
        'footstone.liexindev.net',
        'footstone_v2.' . NET_DOMAIN,
        'news.' . NET_DOMAIN,
        'tly.' . NET_DOMAIN,
        'sale.' . NET_DOMAIN,
        'news.liexindev.net',
        'data.liexindev.net',
        'h5.liexindev.net',
        'h5.liexindev.com',
        'localhost:8080',
        'localhost',
        'jianmin.5gzvip.91tunnel.com',
        'iedge.xiaokang.liexinlocal.com',
        'iedge.liexindev.net',
    ),


    //分页显示数量
    'DEFAULT_PAGE_LIMIT' => 10,

    //上传图片参数
    'PING_ATTACH_PATH' => '',
    //上传文件配置参数
    'PING_ATTR_ALLOW_SIZE' => 2048*10,//上传文件大小
    'PING_ATTR_ALLOW_EXTS' => 'jpg,gif,png,jpeg,xls,xlsx',
    'TMPL_PARSE_STRING' => array(
        '__UPLOAD__'    => __ROOT__ . '/Uploads/',
    ),


    ###############2023.5.30 新增配置########################
    //v3订单客户已付金额
    'ORDER_CLIENT_PAYED_STATUS' => array('-1','-2','-3','4'),
    //v3订单已付金额
    'ORDER_PAYED_STATUS' => array('-1','-2','-3','-7','4'),
    //v3订单总未付金额
    'ORDER_TOPAY_STATUS' => array('1','2','3','-8','-7','-6','-5','-4','-3','-2','-1'),
    //v3订单附加费
    'ORDER_EXT_STATUS' => array('2'),
    //v3订单总金额
    'ORDER_PRICE_TOTAL_STATUS' => array('1','2','3','-8','-4','-6'),

    //v3订单状态
    'ORDER_STATUS' => array(
        '-1' => '已取消',
        '-2' => '审核不通过',
        '1' => '待审核',
        '2' => '待付款',
        '3' => '待付尾款',
        '4' => '待发货',
        '7' => '部分发货',
        '8' => '待收货',
        '10' => '交易成功',
    ),

    "uploda_file_url"=>"http://file.liexindev.net"
);
