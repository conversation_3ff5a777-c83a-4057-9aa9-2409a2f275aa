<?php
/**
 * 过滤文件，发布请手动补充修改内容
 */
return array(
    //内部调用免验证秘钥
    'SUPER_AUTH_KEY' => 'fh6y5t4rr351d2c3bryi',

    // 订单状态更改需要通知的内部人员联系方式
    'INNER_PERSON' => array(
        '15989573446',
    ),
    // 订单通知 测试人员接收专用
    'INNER_PERSON_TEST' => array(
        '15989573440',
    ),

    // 订单通知 以下测试人员下单，无需消息通知业务方
    'TEST_PEOPLE_MOBILE' => array('18682161063', '18682159081', '18620303901', '18588257276', '18576670116', '18571954413', '18503060489', '18*********', '17688163498', '17100000000', '15997404637', '15800223273', '13760154557', '13692198893', '13632549256', '13543299551', '13510507993', '13266749470', '13048859801', '13021149399', '1234', '54455585', '13798386756', '13026601557', '15500001111', '15500001112', '15500001113', '15500001114'),

    //注册接口每小时访问限制出现验证码次数
    'REG_VISIT_LIMIT' => 30,
    //非常规访问评分
    'NOT_NORMAL_VISIT_SCORE' => 60,

    // 消息各个渠道 每次获取条数 每5秒请求一次
    'MESSAGE_CHANNEL_NUMS' => array(
        '2' => 50, // 短信
        '1' => 200, // 站内信
        '3' => 15, // 邮件
        '4' => 20, // 微信
        '5' => 20, // 钉钉
    ),

    // 微信模板
    'WECHAT_TEMPLATE' => array(
        'order-confirm-send' => array( // 商品已发出通知 （发货后）提醒确认收货：
            'first' => array('value' => '尊敬的用户，您在猎芯网上的订单有了新的进展', 'color' => '#173177'),
            'delivername' => array('value' => '顺丰快递', 'color' => '#173177'), // 快递公司
            'ordername' => array('value' => '00010000', 'color' => '#173177'), // 快递单号
            'remark' => array('value' => '商品已发出', 'color' => '#173177'),
        ),
        'order-remind-pay' => array( // 订单未付款通知 提醒付款 即后台审核通过
            'frist' => array('value' => '您还有未付款的订单，请尽快支付！', 'color' => '#173177'), // frist 微信自己单词写错，不用修改变量名
            'orderProductPrice' => array('value' => '1000', 'color' => '#173177'), // 订单金额
            'orderProductName' => array('value' => '74HC595', 'color' => '#173177'), // 商品详情 商品名称
            'orderAddress' => array('value' => '00010000', 'color' => '#173177'), // 收货信息 某某，广东省 深圳市 龙岗区
            'orderName' => array('value' => '00010000', 'color' => '#173177'), // 订单编号 订单号
            'remark' => array('value' => '商品已发出', 'color' => '#173177'),
        ),
        'order-full-paid' => array( // 订单付款成功
            'first' => array('value' => '我们已收到您的货款，开始为您打包商品，请耐心等待: )', 'color' => '#173177'), // frist 微信自己单词写错，不用修改变量名
            'orderMoneySum' => array('value' => '1000', 'color' => '#173177'), // 支付金额
            'orderProductName' => array('value' => '74HC595', 'color' => '#173177'), // 商品详情 商品名称
            'remark' => array('value' => '如有问题请致电0755-88914841或直接在微信留言，小芯将第一时间为您服务！', 'color' => '#173177'),
        ),
        'order-advance-prepaid' => array( // 订单付款成功
            'first' => array('value' => '我们已收到您的货款，开始为您打包商品，请耐心等待: )', 'color' => '#173177'), // frist 微信自己单词写错，不用修改变量名
            'orderMoneySum' => array('value' => '1000', 'color' => '#173177'), // 支付金额
            'orderProductName' => array('value' => '74HC595', 'color' => '#173177'), // 商品详情 商品名称
            'remark' => array('value' => '如有问题请致电0755-88914841或直接在微信留言，小芯将第一时间为您服务！', 'color' => '#173177'),
        ),
        'order-advance-endpaid' => array( // 订单付款成功
            'first' => array('value' => '我们已收到您的货款，开始为您打包商品，请耐心等待: )', 'color' => '#173177'), // frist 微信自己单词写错，不用修改变量名
            'orderMoneySum' => array('value' => '1000', 'color' => '#173177'), // 支付金额
            'orderProductName' => array('value' => '74HC595', 'color' => '#173177'), // 商品详情 商品名称
            'remark' => array('value' => '如有问题请致电0755-88914841或直接在微信留言，小芯将第一时间为您服务！', 'color' => '#173177'),
        ),
        'order-pay-success' => array( // 订单付款成功
            'first' => array('value' => '我们已收到您的货款，开始为您打包商品，请耐心等待: )', 'color' => '#173177'), // frist 微信自己单词写错，不用修改变量名
            'orderMoneySum' => array('value' => '1000', 'color' => '#173177'), // 支付金额
            'orderProductName' => array('value' => '74HC595', 'color' => '#173177'), // 商品详情 商品名称
            'remark' => array('value' => '如有问题请致电0755-88914841或直接在微信留言，小芯将第一时间为您服务！', 'color' => '#173177'),
        ),
    ),

    //注册获取领券的要求时间
    'COUPON_REG_TIME' => array(
        'START' => '1507888611',
        'END' => '1507888612',
    ),
    // 优惠券发放的特殊条件
    'COUPON_EVENT_OPTION' => array(
        'REG' => array(51, 53, 54), // 注册领券
        'SUBSCRIBE' => 52, //关注服务号领券
    ),
    //扫码生成二维码的时间范围，用于统计这段时间生成的二维码
    'QRCODE_CREATE_TIME' => array(
        'START' => '1523808000', //1523289600
        'END' => '1523894400', //1523376000
    ),

    //中间服务秘钥
    'SERVICE_KEY' => 'j9q##VRhaXBEtznIEeDiR@1Hvy0sW3wp',
    //中间服务对列名
    'SERVICE_QUEUE_NAME' => 'wms_service',

    //行为日志队列名
    'QUEUE_BEHAVIOR' => 'web_behavior',


    //数据上报对列名
    'QUEUE_REPORT_DATA' => 'report_data',

    //积分商城队列
    'QUEUE_MKT_POINT' => 'mkt_point',

    //购物车更新队列
    'QUEUE_SHOPPING_CART' => 'web_cart_update',
    // 消息队列名
    'MSG_QUEUE_KEY' => 'msg_mq', // 消息第一通道
    'MSG_QUEUE_SEC_KEY' => 'msg_mq_sec', // 消息第二通道
    'MSG_QUEUE_THD_KEY' => 'msg_mq_thd', // 消息第三通道

    //校验余额队列
    'QUEUE_USER_BALANCE' => 'web_user_balance',
    //余额结算
    'QUEUE_WALLET_SETTLE' => 'web_wallet_settle',


    //积分商城队列
    'QUEUE_MKT_POINT' => 'mkt_point',

    'ARTICLE_WORD' => 'art_mq',
    'MSG_SEND_NUM' => 300, // 每次获取消息数量

    //todo 2020.9.9 自营出库推入erp队列
    'QUEUE_ZY_REMOVAL_ORDER' => 'zy_removal_order',


    //pcb支付完成队列-队列
    'QUEUE_PCB_FINISH_PAY' => 'szliexin_message_pcb',

    //供应链下单后推送消息-队列
    'QUEUE_GONGYINGLIAN_ORDER' => 'szliexin_msg_gongyinglian',

    //供应链审核中订单修改订单新增关联订单附表-队列
    'QUEUE_GONGYINGLIAN_CHANGEORDER' => 'szliexin_msg_gyl_changeorder',

    //pcb支付完成队列-交换机
    'EXCHANGE_NAME_PCB' => 'szliexin_message_tasks',

    //消息系统交换机
    'EXCHANGE_NAME_MESSAGE' => 'szliexin_message_tasks',
    //消息系统-注册登录队列
    'QUEUE_MESSAGE_LOGINREGIST' => 'szliexin_message_login_regist',
    //消息系统-普通消息队列
    'QUEUE_MESSAGE_NORMAL' => 'szliexin_message_tasks',
    //消息系统-普通消息队列
    'QUEUE_MESSAGE_NORMAL_DUANXIN' => 'szliexin_message_tasks_duanxin',
    //消息系统-营销消息队列含有短信和其它消息
    'QUEUE_MESSAGE_YINGXIAO' => 'szliexin_message_tasks_yingxiao',
    //退款状态检测队列
    'REFUND_QUEUE_KEY' => 'web_refund_check',

    //消息系统-供应链-普通消息队列
    'QUEUE_MESSAGE_SUPPLY' => 'szliexin_msg_supply',


    //上报日志mq
    "Log_Monitor_Behavior_Exchange" => "ichunt_monitor_behavior",
    "Log_Monitor_Behavior_Queue" => "ichunt_monitor_user_behavior",

    //消息队列框架rpc服务地址
    'MESSAGE_QUEUE_RPC' => [
        'ip' => '*************',
        'port' => '9998',
        'url' => '/rpc/addMessageQueue',
    ],


    //sunlong消息系统钉钉告警
    'MESSAGE_DINGDING' => "https://oapi.dingtalk.com/robot/send?access_token=1bf6f30460eeb38d30150b76e6da9cebf638d20eee4ff81ca60b549f74d0c6f0",

    //钉钉群机器人通知tonken
    'DINGTALK_ROBOT_TOKEN' => array(
        //测试通知
        'ceshi' => '5a6e422cebd5052e559631dba5334574838bd3d6525637223941da2a2d724370',
        //竞调转普通用户通知
        'jingdiao' => 'eaeae1253e9c125a053bc8a7bcfec5096a23625a98044eaa346591863449ba17',
        //订单告警
        'order' => 'fb2c6baeeae72c65697e83d3fd5ebfd482d37aa16bfe03e1866c6eeb2495b7ac',
        // crm队列告警
        'crm_mq' => '55472761276936b1594f27b377ccf3ae307f4b5b6c2392bcf3598f7c35426bcd',
        // 会员发票
        'invoice' => '41817d0b975a89fb506671cbf1cc58ffda480bd6fe41a164834e53d4d06133f6',
    ),


    // 注册领券活动adtag
    'REG_COUPON_ADTAG' => array(
        'home.roll', //PC首页轮播
        'news.detail', //PC活动/公告区域
        'edm',  //EDM
        'sms',  //SMS
        'wechat.push', //微信文章及阅读原文
        'wechat.menu',//公众号菜单
        'qq', //QQ渠道
        'wx', //微信渠道
    ),

    // 生成联营订单时，若没指定sale_id，则推送信息给郑家锋
    'SEND_MSG_TO_MANAGER' => array(
        13418680837, // 郑家锋
        13430917331, // 张娟
    ),

    // 自营线下订单推送给胡晶晶
    'SELF_OFFLINE_SEND_MSG' => array(
        '15989573440',
        '13320081711',
    ),

    'SUPPLIER_EMAIL_SEND' => array(
        //'<EMAIL>'
        '<EMAIL>'
    ),

    'STM_EMAIL_SEND' => array(
        '<EMAIL>',
    ),

    // 专卖顶级供应商ID
    'ZM_TOP_SUPP_ID' => 17,

    // 供应商招商页邮件通知
    'SUPPLIER_EMAIL_SEND' => array(
        //'<EMAIL>'
        '<EMAIL>'
    ),
    'ARTICLE_WORD' => 'art_mq',

    'ART_SEND_NUM' => 10, // 每次取10条文章消费

    //sensors上报数据地址
    'SA_SERVER_URL' => 'http://shence.ichunt.com:8106/sa?project=default',

    //物料数据同步url
    "pushGoodsUrl" => "http://tax.liexin.net/AddCustomsItems",

    // 猎芯自由账户钱包
    'LIEXIN_WALLET_ACCOUNT' => '*********',

    //供应链物料推送
    "pushGoodsUrl" => "http://192.168.1.194/AddCustomsItems",
    //任务体系队列名
    'MEMBER_TASK_SYSTEM_LIST' => 'sz_member_task_system_list',

    'COMPLETED_ORDER_REFUND_ERP' => 'ichunt_order_refund_erp',

    // 优惠券、订单消息模板
    'KEYWORDS_TPL' => ['self_jd_syn_send', 'order-new-self', 'order-send-to-manager', 'order-self-end-remind-pay', 'order-self-first-remind-pay', 'order-self-pay-success', 'order-self-confirm-send', 'new-order-send-salesman', 'order-pay-time-remind', 'order-send-salesman', 'order-pay-success', 'order-advance-endpaid', 'order-full-paid', 'order-advance-prepaid', 'order-check', 'order-confirm-send', 'order-remind-pay', 'reg_send_coupon', 'reg_send_coupon_discount', 'coupon-send', 'coupon-expire', 'coupon-get-new'],

    //营销消息相关的分类id
    "YINGXIAO_PARENT_ID" => [
        3, 95,76
    ],

    //供应链广告用户短信推送
    'GONGYINGLIAN_GUANGAO_PUSH_USER' => ["13630960196", "13630960196", "18123687146", "13662241566", "13632834879", "13537546310", "13630960196"],

    // 自营订单推入仓库队列
    'SELF_ORDER_PUSH_WMS' => 'order_push_stock',

    // 队列bom单下单后 绑定数据
    'BOM_CREATE_ORDER' => 'api_create_order_bom',

    //团购商品id
    "TUANGOU_GOODSLIST" => ["165514"],

    //djk标识
    "DJK_SUPPLIER_ID" => 7,

    //djk标识
    "DJK_SUPPLIER_NAME" => "digikey",

    // 联营订单ERP负责人
    'ERP_ORDER_MANAGER' => '程凤秀',

    // 推送用户到CRM队列
    'CRM_UPDATE_USER_SALES' => 'crm_update_user_sales',
    'CRM_PUSH_USER' => 'member_user_add',

    //限制未支付的充值单数量
    "NOT_PAY_USER_WALLET_NUMS" => 5,

    //邀好友下单告警
    "YAOHAOYOU_C_ORDER_DING" => "https://oapi.dingtalk.com/robot/send?access_token=087c11504fb541d004507f06285da88c2501fc1a4ecacd0216b9da964aa65518",

    "YAOHAOYOU_ORDER_QUEUE" => "ichunt_addorder_yaohaoyou",

    "YAOHAOYOUAVLIST" => [
        1 => 11033,
        2 => 5566,
    ],

    //工具尺活动的特定商品id
    'RULER_ACTIVITY_GOODS_ID' => 95877,

    // 订单生产跟踪队列
    'ORDER_PRODUCT_TRACK' => 'ichunt_order_product_track',

    //新用户关注微信 推送微信基础信息到云芯 队列
    'PUSHWECHATINFO_TO_YUNXIN' => 'szichunt_wechat_userinfo',


    //opentracing jaeger dns 链路追踪 dns
    "OPENTRACING_JAEGER_DNS" => '192.168.1.235:6831',
    "OPENTRACING_JAEGER_SERVERNAME" => 'ichuntapi_opentracing',

    "eyeCertUrl" => "http://united_data.liexindev.net/sync/Company/getCompanyInfoByName",
    "crmDomain" => "http://crmnew.liexindev.net",
    //用户中心请求url
    "UCENTER_URL" => "http://ucenter.liexindev.net",
    //组织id
    "ORG_LIEXIN" => 1,
    "ORG_IEDGE" => 3,
);

















