<?php
/**
 * 过滤文件，发布请手动补充修改内容
 */
return array(
    'DB_TYPE'                => 'mysql', // 数据库类型
    'DB_HOST'                => '*************', // 服务器地址
    'DB_NAME'                => 'liexin', // 数据库名
    'DB_USER'                => 'liexin', // 用户名
    'DB_PWD'                 => 'liexin#zsyM', // 密码
    'DB_PORT'                => '3306', // 端口
    'DB_PREFIX'              => 'lie_', // 数据库表前缀
    'DB_PARAMS'              => array(), // 数据库连接参数
    'DB_DEBUG'               => true, // 数据库调试模式 开启后可以记录SQL日志
    'DB_FIELDS_CACHE'        => true, // 启用字段缓存
    'DB_CHARSET'             => 'utf8', // 数据库编码默认采用utf8
    'DB_DEPLOY_TYPE'         => 0, // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
    'DB_RW_SEPARATE'         => false, // 数据库读写是否分离 主从式有效
    'DB_MASTER_NUM'          => 1, // 读写分离后 主服务器数量
    'DB_SLAVE_NO'            => '', // 指定从服务器序号
    'DB_OTHER'               => array(
        'CMS'   =>  array(
            'db_type'  => 'mysql',
            'db_user'  => 'ichuntcms',
            'db_pwd'   => 'ichuntcms#zsyM',
            'db_host'  => '*************',
            'db_port'  => '3306',
            'db_name'  => 'ichuntcms',
            'db_charset'=>'utf8',
        ),
        'SPU'   =>  array(
            'db_type'  => 'mysql',
            'db_user'  => 'spu',
            'db_pwd'   => 'spu',
            'db_host'  => '*************',
            'db_port'  => '3306',
            'db_name'  => 'liexin_spu',
            'db_charset'=>'utf8',
            'DB_PREFIX' => 'lie_',
        ),
        'ZYGOODS'   =>  array(
            'db_type'  => 'mysql',
            'db_user'  => 'spu',
            'db_pwd'   => 'spu',
            'db_host'  => '*************',
            'db_port'  => '3306',
            'db_name'  => 'liexin_data',
            'db_charset'=>'utf8',
            'DB_PREFIX' => 'lie_',
        ),
        // 'ICHUNT_ORDER'   =>  array(
        //     'db_type'  => 'mysql',
        //     'db_user'  => 'liexin_order',
        //     'db_pwd'   => 'liexin_order#zsyM',
        //     'db_host'  => '*************',
        //     'db_port'  => '3306',
        //     'db_name'  => 'liexin_order',
        //     'db_charset'=>'utf8',
        // ),
        'ICHUNT_ORDER'   =>  array(
            'db_type'  => 'mysql',
            'db_user'  => 'liexin_order_v2',
            'db_pwd'   => 'liexin_order_v2#zsyM',
            'db_host'  => 'master.db2.liexindev.me',
            'db_port'  => '3306',
            'db_name'  => 'liexin_order_v2',
            'db_charset'=>'utf8',
        ),

    ),


    'Bom'=>array(
        'db_type'  => 'mysql',
        'db_user'  => 'liexin_bom',
        'db_pwd'   => 'liexin_bom#zsyM',
        'db_host'  => '*************',
        'db_port'  => '3306',
        'db_name'  => 'liexin_bom',
        'db_charset'=>'utf8',
        'DB_PREFIX' => 'lie_',
    ),

    'PCB_DB_CONFIG' => array(
        'db_type'  => 'mysql',
        'db_user'  => 'liexin_pcb',
        'db_pwd'   => 'liexin_pcb#zsyM',
        'db_host'  => '*************',
        'db_port'  => '3306',
        'db_name'  => 'liexin_pcb',
        'db_charset'=>'utf8',
        'DB_PREFIX' => 'lie_',
    ),

    //MESSAGE
    'MESSAGE_DB_CONFIG' => array(
        'db_type'  => 'mysql',
        'db_user'  => 'liexin_message',
        'db_pwd'   => 'liexin_message#zsyM',
        'db_host'  => '*************',
        'db_port'  => '3306',
        'db_name'  => 'liexin_message',
        'db_charset'=>'utf8',
    ),

    'MKTWX_DB_CONFIG' => array(
        'db_type'  => 'mysql',
        'db_user'  => 'liexin_marketing',
        'db_pwd'   => 'liexin_marketing#zsyM',
        'db_host'  => '*************',
        'db_port'  => '3306',
        'db_name'  => 'liexin_marketing',
        'db_charset'=>'utf8',
    ),

    //crm
    'CRM_DB_CONFIG' => array(
        'db_type'  => 'mysql',
        'db_user'  => 'liexin_crmv2',
        'db_pwd'   => 'liexin_crmv2#zsyM',
        'db_host'  => '*************',
        'db_port'  => '3306',
        'db_name'  => 'liexin_crmv2',
        'db_charset'=>'utf8',
        'DB_PREFIX' => 'lie_',
    ),

    'SPECIAL_DB_CONFIG' => array(
        'db_type'  => 'mysql',
        'db_user'  => 'special',
        'db_pwd'   => 'special#zsyM',
        'db_host'  => '*************',
        'db_port'  => '3306',
        'db_name'  => 'special',
        'db_charset'=>'utf8',
        'DB_PREFIX' => 'lie_',
    ),

    //PCB
    'PCB_DB_CONFIG' => array(
        'db_type'  => 'mysql',
        'db_user'  => 'liexin_pcb',
        'db_pwd'   => 'liexin_pcb#zsyM',
        'db_host'  => '*************',
        'db_port'  => '3306',
        'db_name'  => 'liexin_pcb',
        'db_charset'=>'utf8',
    ),



    //supply
    'SUPPLY_CHAIN' => [
        'driver'    => 'mysql',
        'host'      => '*************',
        'database'  => 'liexin_supply_chain',
        'username'  => 'supply_chain',
        'password'  => 'liexin_supply_chain#zsyM',
        'charset'   => 'utf8',
        'collation' => 'utf8_general_ci',
        'prefix'    => 'lie_',
        'strict' => false,
        'engine' => null,
        'options' => [
            // mysql连接3s超时设置
            \PDO::ATTR_TIMEOUT => 3
        ]
    ],
    //supply
    'SCM_INTERNAL' => [
        'driver'    => 'mysql',
        'host'      => '*************',
        'database'  => 'liexin_scm_internal',
        'username'  => 'liexin_scm_internal',
        'password'  => 'liexin_scm_internal#zsyM',
        'charset'   => 'utf8',
        'collation' => 'utf8_general_ci',
        'prefix'    => 'lie_',
        'strict' => false,
        'engine' => null,
        'options' => [
            // mysql连接3s超时设置
            \PDO::ATTR_TIMEOUT => 3
        ]
    ],
    //liexin_credit
    'LIEXIN_CREDIT' => [
        'driver'    => 'mysql',
        'host'      => '*************',
        'database'  => 'liexin_credit',
        'username'  => 'liexin_credit',
        'password'  => 'liexin_credit#zsyM',
        'charset'   => 'utf8',
        'collation' => 'utf8_general_ci',
        'prefix'    => 'lie_',
        'strict' => false,
        'engine' => null,
        'options' => [
            // mysql连接3s超时设置
            \PDO::ATTR_TIMEOUT => 3
        ]
    ],

    'LARAVEL_DB_LIEXIN' => [
        'driver'    => 'mysql',
        'host'      => '*************',
        'database'  => 'liexin',
        'username'  => 'liexin',
        'password'  => 'liexin#zsyM',
        'charset'   => 'utf8',
        'collation' => 'utf8_general_ci',
        'prefix'    => 'lie_',
        'strict' => false,
        'engine' => null,
        'options' => [
            // mysql连接3s超时设置
            \PDO::ATTR_TIMEOUT => 3
        ]

    ],


    'LARAVEL_DB_PCB' => [
        'driver'    => 'mysql',
        'host'      => '*************',
        'database'  => 'liexin_pcb',
        'username'  => 'liexin_pcb',
        'password'  => 'liexin_pcb#zsyM',
        'charset'   => 'utf8',
        'collation' => 'utf8_general_ci',
        'prefix'    => 'lie_',
        'strict' => false,
        'engine' => null,
        'options' => [
            // mysql连接3s超时设置
            \PDO::ATTR_TIMEOUT => 3
        ]

    ],

    'LARAVEL_DB_TAX' => [
        'driver'    => 'mysql',
        'host'      => '*************',
        'database'  => 'liexin_tax',
        'username'  => 'liexin_tax',
        'password'  => 'liexin_tax#zsyM',
        'charset'   => 'utf8',
        'collation' => 'utf8_general_ci',
        'prefix'    => 'lie_',
        'strict' => false,
        'engine' => null,
        'options' => [
            // mysql连接3s超时设置
            \PDO::ATTR_TIMEOUT => 3
        ]

    ],


    'LARAVEL_MESSAGE_TAX' => [
        'driver'    => 'mysql',
        'host'      => '*************',
        'database'  => 'liexin_message',
        'username'  => 'liexin_message',
        'password'  => 'liexin_message#zsyM',
        'charset'   => 'utf8',
        'collation' => 'utf8_general_ci',
        'prefix'    => 'lie_',
        'strict' => false,
        'engine' => null,
        'options' => [
            // mysql连接3s超时设置
            \PDO::ATTR_TIMEOUT => 3
        ]

    ],

    'LARAVEL_CRM' => [
        'driver'    => 'mysql',
        'host'      => '*************',
        'database'  => 'liexin_crm',
        'username'  => 'liexin_crm',
        'password'  => 'liexin_crm#zsyM',
        'charset'   => 'utf8',
        'collation' => 'utf8_general_ci',
        'prefix'    => 'lie_',
        'strict' => false,
        'engine' => null,
        'options' => [
            // mysql连接3s超时设置
            \PDO::ATTR_TIMEOUT => 3
        ]

    ],

    //用户行为数据库
    'BEHAVIOR_DB_CONFIG' => [
        'db_type'  => 'mysql',
        'db_user'  => 'liexin_behavior',
        'db_pwd'   => 'liexin_behavior#zsyM',
        'db_host'  => '*************',
        'db_port'  => '3306',
        'db_name'  => 'liexin_behavior',
        'db_prefix'  => 'lie_',
        'db_charset'=>'utf8',
    ],
    //dashboard
    'DASHOBOARD' => [
        'driver' => 'mysql',
        'host' => "*************",
        'database' => "dashboard",
        'username' => "dashboard",
        'password' => "dashboard#zsyM",
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => "lie_",
    ],

    // 采购系统
    'PURCHASE_DB_CONFIG' => array(
        'db_type'    => 'mysql',
        'db_user'    => 'liexin_purchase',
        'db_pwd'     => 'liexin_purchase#zsyM',
        'db_host'    => '*************',
        'db_port'    => '3306',
        'db_name'    => 'liexin_purchase',
        'db_charset' => 'utf8',
        'prefix'     => 'lie_',
    ),

    //wms3
    'WMS_DB_CONFIG' => array(
        'db_type'  => 'mysql',
        'db_user'  => 'wms3',
        'db_pwd'   => 'liexin_wms_v3#zsyM',
        'db_host'  => '*************',
        'db_port'  => '3306',
        'db_name'  => 'liexin_wms_v3',
        'db_charset' => 'utf8',
        'DB_PREFIX' => 'lie_',
    ),

    //frq
    'FRQ_DB_CONFIG' => array(
        'db_type'  => 'mysql',
        'db_user'  => 'liexin_rfq',
        'db_pwd'   => 'liexin_rfq#zsyM',
        'db_host'  => '*************',
        'db_port'  => '3306',
        'db_name'  => 'liexin_rfq',
        'db_charset'=>'utf8',
    ),

    //Mongo
    'DB_MONGO' => array(
        'db_type' => 'mongo',
        'db_user'  => 'ichunt',
        'db_pwd'   => 'huntmon6699',
        'db_host'  => 'redis.liexindev.me',
        'db_port'  => '27017',
        'db_name'  => 'ichunt',
        'db_charset'=>'utf8',
    ),

    //RBMQ 线上服务器为33
//    'RBMQ_CONFIG' => array(
//        'host' => '*************',
//        'port' => '5672',
//        'login' => 'admin',
//        'password' => 'admin',
//        'vhost'=>'/'
//    ),

    'RBMQ_CONFIG' => array(
        'host' => '*************',
        'port' => '5672',
        'login' => 'guest',
        'password' => 'guest',
        'vhost'=>'/'
    ),

    // 推送订单到wms
    'ORDER_PUSH_RBMQ_CONFIG' => array(
        'host' => '*************',
        'port' => '5672',
        'login' => 'guest',
        'password' => 'guest',
        'vhost'=>'/'
    ),

    // RBMQ MSG
    'RBMQ_MSG_CONFIG' => array(
        'host' => '*************',
        'port' => '5672',
        'login' => 'guest',
        'password' => 'guest',
        'vhost'=>'/'
    ),

    //wms服务RBMQ
    'WMS_RBMQ_CONFIG' => array(
        'host' => '*************',
        'port' => '5672',
        'login' => 'guest',
        'password' => 'guest',
        'vhost'=>'/'
    ),

    //wms服务RBMQ2
    'WMS_RBMQ_CONFIG2' => array(
        'host' => '*************',
        'port' => '5672',
        'login' => 'guest',
        'password' => 'guest',
        'vhost'=>'/'
    ),


    'REDIS_HOST'        =>  'redis.liexindev.me',
    'REDIS_PORT'        =>  6379,
    'REDIS_PASSWORD'    =>  'icDb29mLy2s',
    'REDIS_RW_SEPARATE' =>  true,

    //首页滚动数据REDIS 区分读写
    'REDIS_HOME_ROLL_WRITE'   => 'redis.liexindev.me',
    'REDIS_HOME_ROLL_READ'    => '*************',


    //redis
    'REDIS_LIST'   => array(
        'coupon' => array(
            'host'     => 'redis.liexindev.me,*************',
            'port'     => 6379,
            'password' => 'icDb29mLy2s',
            'type'     => 'Redisrw',
            'rw_separate'=> true,
        ),
        'search' => array(
            'host'     => 'redis.liexindev.me,*************',
            'port'     => 6379,
            'password' => 'icDb29mLy2s',
            'type'     => 'Redisrw',
            'rw_separate'=> true,
            'prefix' => '',
        ),
        'spu' => array(
            'host'     => 'redis.liexindev.me,*************',
            'port'     => 6379,
            'password' => 'icDb29mLy2s',
            'type'     => 'Redisrw',
            'rw_separate'=> true,
            'prefix' => '',
        ),
        'spu_new' => array(
            'host'     => '*************,*************',
            'port'     => 6379,
            'password' => 'icDb29mLy1s',
            'type'     => 'Redisrw',
            'rw_separate'=> true,
            'prefix' => '',
        ),
        'user_new' => array(
            'host'     => '*************,*************',
            'port'     => 6379,
            'password' => 'icDb29mLy1s',
            'type'     => 'Redisrw',
            'rw_separate' => true,
            'prefix' => 'api_',
        ),
        'user_old' => array(
            'host'     => '*************,*************',
            'port'     => 6379,
            'password' => 'icDb29mLy2s',
            'type'     => 'Redisrw',
            'rw_separate' => true,
            'prefix' => 'api_',
        ),
    ),

    'DATA_CACHE_TIMEOUT'=>  false,
    'DATA_CACHE_TYPE'   => 'Redisrw',
    'DATA_CACHE_PERSISTENT' => false,
    'DATA_CACHE_PREFIX' => 'api_',

    //Redis Session配置
    'SESSION_TYPE' =>  'Redis', //session类型
    'SESSION_EXPIRE' =>  3600, //session有效期(单位:秒) 0表示永久缓存
    'SESSION_REDIS_HOST' =>  'redis.liexindev.me,*************', //分布式Redis,默认第一个为主服务器
    'SESSION_REDIS_PORT' =>  '6379',       //端口,如果相同只填一个,用英文逗号分隔
    'SESSION_REDIS_AUTH'    =>  'icDb29mLy2s',    //Redis auth认证(密钥中不能有逗号),如果相同只填一个,用英文逗号分隔

    // ERP系统登录DB
    'ERP_DB_NAME' => 'DEMO',
    // ERP登录名称 (删除接口)
    'ERP_LOGIN_NAME' => 'ZJL',

    //供应链erp系统登录db
    'ERP_SUPPLY_DB_NAME' => 'scm_demo',

    //更新购物车的Redis服务器ip
    'UPDATE_SHOPPING_REMIND_REDIS_HOST'=>'redis.liexindev.me',

    //djk map映射关系
    'DJK_MAP_REDIS'=>[
        'host'     => 'redis.liexindev.me',
        'port'     => 6379,
        'password' => 'icDb29mLy2s',
    ],

    //云芯用户redis缓存
    'YUNXIN_REDIS_REDIS'=>[
        'host'     => 'redis.liexindev.me',
        'port'     => 6379,
        'password' => 'icDb29mLy2s',
    ],

    // 基石接口key
    'footstone_api_key' => 'LX@ichunt.com82560956-0755',

    //用户中心缓存
    "UCENTER_USER_REDIS_KEY" => "ucenter_user",
    "UCENTER_EMAIL_REDIS_KEY" => "ucenter_email",
    "UCENTER_MOBILE_REDIS_KEY" => "ucenter_mobile",

);
