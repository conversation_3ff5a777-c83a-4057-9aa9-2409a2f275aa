<?php
return array(
    //redis_key ..使用hash存储 _使用string存储
    'SKEY_REDIS_KEY' => 'skey_',
    'LSKEY_REDIS_KEY' => 'lskey_',
    'USER_REDIS_KEY' => 'user..',
    'USER_POINT_INFO_REDIS_KEY' => 'user_point_info..',
    'WALLET_REDIS_KEY' => 'wallet..',
    'COMPANY_REDIS_KEY' => 'company..',
    'EMAIL_REDIS_KEY' => 'email..',
    'MOBILE_REDIS_KEY' => 'mobile..',
    'NAME_REDIS_KEY' => 'username..',
    'SMS_MOBILE_KEY' => 'sms_',
    'QQ_ID_KEY' =>  'qq..',
    'TOKEN_REDIS_KEY' => 'token_',
    'PWD_REDIS_KEY' => 'pwd_',
    'CSRF_REDIS_KEY' => 'csrf_',
    'CARTNUM_REDIS_KEY' => 'cart_num_',
    'WECHAT_ID_KEY' => 'wechat..',
    'WECHAT_INFO_ID_KEY' => 'wx_', //微信授权相关信息，过期时间为1小时
    'COUPON_REDIS_KEY' => 'coupon..',//券信息
    'COUPON_QUEUE_KEY' => 'coupon_',//券队列
    'UCOUPON_COUNT_KEY' => 'ucoupon_',//用户券数量
    'SUPP_EXTEND_FEE_KEY' => 'supp_extend_fee..',//供应商附加费
    'USER_SIGN_IN_KEY' => 'sign_in..',//用户签到
    'ORIGIN_URL_KEY' => 'origin_url..',//用户分享跳转原地址
    'VERIFY_SAFE_KEY' => 'verify_safe_',//安全手机验证
    'INVITE_QRCODE_COUNT_KEY' =>'invite_qrcode_count..',
    'INVITE_QRCODE_COUNT_DISTINCT_KEY' =>'invite_qrcode_count_distinct..',
    'REG_VISIT_REDISY_KEY' => 'reg_visit',

    'MESSAGE_REDIS_KEY' => 'message..',//消息信息
    'MESSAGE_QUEUE_KEY' => 'message_',//消息队列

    'SUPPLIER_REDIS_KEY' => 'SUPPLIER_REDIS_INFO_..',//供应商信息、阶梯

    'ARTICLE_REDIS_KEY' => 'ARTICLE_REDIS_INFO_..', // 文章资讯词库链接词信息

    //redis 通过忘记密码获取user_id的过期时间
    'WX_EXPIRE_TIME' => 864000, //(10天)
    'PWD_EXPIRE_TIME' => 1800,//(30分钟)
    //redis强登录态过期时间
    'SKEY_EXPIRE_TIME' => 86400,
    //redis弱登录态过期时间
    // 'LSKEY_EXPIRE_TIME' => 2592000,
    'LSKEY_EXPIRE_TIME' => 1296000, // 15天
    //redis短信验证码可发送时间范围
    'SMS_EXPIRE_TIME' => 86400,
    //短信次数限制(一天之内，同一个号码)
    'SMS_LIMIT_NUMBER' => 10,
    //csrf过期时间
    'CSRF_EXPIRE_TIME' => 86400,
    //邮箱相关激活时间
    'EMAIL_EXPIRE_TIME' => array(
        '1' => 86400, //注册邮箱 （一天）
        '2' => 1800, //绑定邮箱 （30分钟）
    ),
    //用户券数量过期时间
    'UCOUPON_COUNT_EXPIRE_TIME' => 300,//5分钟
    //购物车数量过期时间
    'CARTNUM_REDIS_TIME' => 180,//3分钟
    //安全手机验证过期时间
    'VERIFY_SAFE_EXPIRE_TIME' => 600,

    //通用验签key
    'API_USE_KEY' => 'dzemTSvdEkwkHIepjPer',

    //供应商ID
    'OTHER_DB' => array(
        '11'        => 'company',
        '12'        => 'chip1stop',
        '13'        => 'element14',
        '14'        => 'future',
        '15'        => 'digikey',
        '16'        => 'matches',//mei
        '17'        => 'verical',
        '18'        => 'arrow',
        '19'        => 'avnet',
        '20'        => 'alliedelec',
        '21'        => 'rs',
        // '22'        => 'online',//mei
        // '23'        => 'rutronik24',//mei v2已经注释
        '24'        => 'rochester',
        '25'        => 'mouser',
        // '26'        => 'tti',//mei v2已经注释
        '27'        => 'tme',
        // '28'        => 'powerandsignal',//v2已经注释
        '29'        => 'peigenesis',
        '1672'      =>'master',
    ),
    //供应商信息
    'ALLSUPPLIER' => array(
        'rs' => array(
            'flag' => 21,
            'flagName' => 'RS', //RS ONLINE
            'img' => 'RS.jpg',//
            'supplierId' => '32548', //
            'cnTime' => '5-15工作日',
            'hkTime' => '4-13工作日',
            ),
        'tme' => array(
            'flag' => 27,
            'flagName' => 'TME', //TME
            'img' => 'tme.jpg',//
            'supplierId' => '45208', //
            'cnTime' => '5-9工作日',
            'hkTime' => '4-7工作日',
            ),
        /*'powerandsignal' => array(
            'flag' => 28,
            'flagName' => 'powerandsignal', //powerandsignal
            'img' => 'powerandsignal.jpg',//
            'supplierId' => '46915', //待定
            'cnTime' => '5-9工作日',
            'hkTime' => '4-7工作日',
            ),*/
        'peigenesis' => array(
            'flag' => 29,
            'flagName' => 'peigenesis', //peigenesis
            'img' => 'peigenesis.jpg',//
            'supplierId' => '46916', //待定
            'cnTime' => '5-9工作日',
            'hkTime' => '4-7工作日',
            ),
        'COILCRAFT' => array(
            'flag' => 115,
            'flagName' => 'COILCRAFT', //现艺
            'img' => 'coilcraft.jpg',//
            'supplierId' => '45198', //
            'cnTime' => '5-9工作日',
            'hkTime' => '4-7工作日',
            ),
        'MAXIM' => array(
            'flag' => 113,
            'flagName' => 'MAXIM', //美信
            'img' => 'maxim.jpg',//
            'supplierId' => '45124', //待改
            'cnTime' => '5-9工作日',
            'hkTime' => '4-7工作日',
            ),
        'LINEARTECHNOLOGY' => array(
            'flag' => 114,
            'flagName' => 'LINEAR TECHNOLOGY', //凌力尔特
            'img' => 'linear.jpg',//
            'supplierId' => '45123', //待改
            'cnTime' => '5-9工作日',
            'hkTime' => '4-7工作日',
            ),
        'PowerexPowerSemiconductors' => array(
            'flag' => 111,
            'flagName' => 'Powerex Power Semiconductors',
            'img' => 'powerex.jpg',
            'supplierId' => '45086',
            'cnTime' => '5-9工作日',
            'hkTime' => '4-7工作日',
            ),
        'microchip' => array(
            'flag' => 112,
            'flagName' => 'microchip',
            'img' => 'microchip.jpg',
            'supplierId' => '45085',
            'cnTime' => '5-9工作日',
            'hkTime' => '4-7工作日',
            ),
        'rochester' => array(
            'flag' => 24,
            'flagName' => 'Rochester Electronics',
            'img' => 'rochester.jpg',
            'supplierId' => '45002',
            'cnTime' => '6-11工作日',
            'hkTime' => '5-9工作日',
            ),
        'arrow' => array(
            'flag' => 18,
            'flagName' => 'Arrow（艾睿）',
            'img' => 'arrow-h.jpg',
            'supplierId' => '32543',
            'cnTime' => '5-9工作日',
            'hkTime' => '4-7工作日',
            ),
        'alliedelec' => array(
            'flag' => 20,
            'flagName' => 'Allied Electronics',
            'img' => 'Allied.jpg',
            'supplierId' => '32546',
            'cnTime' => '5-9工作日',
            'hkTime' => '4-7工作日',
            ),
        'future' => array(
            'flag' => 14,
            'flagName' => 'Future',
            'img' => 'future1.gif',
            'supplierId' => '29963',
            'cnTime' => '6-11工作日',
            'hkTime' => '5-9工作日',
            ),
        'mouser' => array(
            'flag' => 25,
            'flagName' => 'Mouser Electronics',
            'img' => 'mouser1.jpg',
            'supplierId' => '30025',
            'cnTime' => '6-9工作日',
            'hkTime' => '5-7工作日',
            ),
        'digikey' => array(
            'flag' => 15,
            'flagName' => 'Digikey',
            'img' => 'digikey1.jpg',
            'supplierId' => '30024',
            'cnTime' => '6-9工作日',
            'hkTime' => '5-7工作日',
            ),
        'element14' => array(
            'flag' =>13,
            'flagName' => 'Element14（e络盟）',
            'img' => 'element14-h.jpg',
            'supplierId' => '32536',
            'cnTime' => '8-12工作日',
            'hkTime' => '7-10工作日',
            ),
        'chip1stop' => array(
            'flag' =>12,
            'flagName' => 'Chip One Stop',
            'img' => 'Chiponestop.jpg',
            'supplierId' => '32538',
            'cnTime' => '8-12工作日',
            'hkTime' => '7-10工作日',
            ),
        'verical' => array(
            'flag' =>17,
            'flagName' => 'Verical',
            'img' => 'verical-h.jpg',
            'supplierId' => '32537',
            'cnTime' => '8-12工作日',
            'hkTime' => '7-10工作日',
            ),
        'avnet' => array(
            'flag' => 19,
            'flagName' => 'Avnet',
            'img' => 'avnet-h.jpg',
            'supplierId' => '32542',
            'cnTime' => '5-9工作日',
            'hkTime' => '4-7工作日',
            ),
        'master' => array(
            'flag' => 1672,
            'flagName' => 'Master',
            'img' => '',
            'supplierId' => '0',
            'cnTime' => '6-10工作日',
            'hkTime' => '5-7工作日',
            ),
    ),


    //禁止记录日志(小写)
    'DISABLE_LOG' => array(
        'login/check',
        'login/waitqqauth',
        'login/checkqq',
    ),

    //禁止验证CSRF
    'DISABLE_CSRF' => array(
        'login/handleqq',
        'pay/returnurl',
        'pay/notifyurl',
        'pay/finish',
        'pay/todo',
        'pay/successdraw',
        'pay/check',
        'wechat/callback',
        'wechat/sendemailremianmsg',
        'wechat/callbackxinychuang',
        'message/crontabjob', // 消息系统 定时任务 出队
        'message/crontabjobmass', // 消息系统 定时任务 群发 出队
        'message/msgconsumesec', // 消息系统 定时任务 群发 出队
		'message/dinnerpush', // 订餐统计短信和钉钉推送
		'order/crontabconfirmsend', // 订单定时任务 每天跑一次
        'order/crontaborderpoint', //手动脚本 加订单积分
        'order/updateusercreatetime',//更新用户创建时间create_time=0的真实用户
        'order/sampleinfo', // 无需登录获取简易订单信息
        'login/getwechatinfo', // 获取微信信息
        'article/crobword', // 资讯词库替换文章内容
        'contract/pdf',
        'wms/shippinginsideact',//wms
        'wms/removalact',//wms
        'wms/payorderwmsmark',//wms
        'oss/upload', // oss图片上传
        'oss/delete', // oss删除
        'order/test',
        'index/quote',
        'erppush/synccustomerinfo',
        'public/dingtalkrobot',#测试机器人
        'xht/getcompany', // 信宏泰公司
        'xht/getcompanyinfo', // 信宏泰公司信息
        'xht/clearcache', // 清除信宏泰缓存
        'wallet/operationrecharge',//运营充值
        'refund/recharge',//退款充值
        'user/info',
        'activity/getlxuserrank', // 猎芯排行榜
        'activity/getactivityinfo', // 猎芯排行榜
        'activity/setamount', // 猎芯排行榜
        'order/handlepaylog', // 处理支付时间
        'public/redirecthtml',//退款解析跳转地址
        'server/server',
        'user/setcachefield', // 设置用户缓存字段
        'user/adduserinfo', // 添加用户信息表
        'order/sysiteminfo', // 同步明细到erp
        'order/checktuangouorderisok',//检查团购是否成团
        'google/recaptcha',//谷歌后台验证
        'order/lockskuaction', // 手动锁库、解库操作
        'usergoods/exportusergoods', // 导出物料库csv
    ),

    //行为统一操作词 1访问 2注册 3登录 4加入购物车 5立即购买 6立即结算 7立即付款 8客服 9优惠券 10抽奖
    'BEHAVIOR_LOG_TYPE' => array(
        '1' => 'visit',
        '2' => 'reg',
        '3' => 'login',
        '4' => 'addcart',
        '5' => 'buy',
        '6' => 'confirm',
        '7' => 'pay',
        '8' => 'qq',
        '9' => 'coupon',
        '10' => 'lottery',

    ),
    //行为统计cookie的KEY
    'BEHAVIOR_KEY' => 'action_xk',

    //包邮 订单金额起步
    'FREE_SHIPPING_FLOOR' => 1000,
    //个人信息标识
    'USER_INFO_NO'  => array(
        'mobile'        => 1,//绑定手机号
        'email'         => 2,//绑定邮箱
        'qq'            => 3,//qq账号绑定
        'wechat'        => 4,//微信绑定
        'user_head'     => 5,//会员头像
        'nike_name'     => 6,//会员姓名
        'sex'           => 7,//会员性别
        'com_logo'      => 8,//公司logo
        'com_name'      => 9,//公司名称
        'type_id'       => 10,//公司类型
        'com_desc'      => 11,//公司简介
        'brand_list'    => 12,//公司主营品牌
        'com_address'   => 13,//公司地址
        'com_telphone'  => 14,//公司座机
        'com_fax'       => 15,//公司传真号码
        'com_mobile'    => 16,//公司移动电话
    ),

    //积分上线时间，以前不显示积分获得
    'MKT_POINT_TIME' => **********,

    //中金B2C银行编码
    'CPCN_BANK_B2C' => array(
        '100' => '邮政储蓄银行',
        '102' => '中国工商银行',
        '103' => '中国农业银行',
        '104' => '中国银行',
        '105' => '中国建设银行',
        '301' => '交通银行',
        '302' => '中信银行',
        '303' => '中国光大银行',
        '304' => '华夏银行',
        '305' => '中国民生银行',
        '306' => '广发银行',
        '307' => '平安银行',
        '308' => '招商银行',
        '309' => '兴业银行',
        '310' => '上海浦东发展银行',
        '316' => '浙商银行',
        '401' => '上海银行',
        '403' => '北京银行',
        '440' => '徽商银行',
        '888' => '银联在线-中金网银无卡',
        '889' => '银联在线中金网银',
        '891' => '银联在线金科无卡',
    ),

    //中金B2B银行编码
    'CPCN_BANK_B2B' => array(
        '102' => '中国工商银行',
        '103' => '中国农业银行',
        '104' => '中国银行',
        '105' => '中国建设银行',
        '301' => '交通银行',
        '302' => '中信银行',
        '303' => '中国光大银行',
        '304' => '华夏银行',
        '305' => '中国民生银行',
        '307' => '深圳发展银行',
        '308' => '招商银行',
        '309' => '兴业银行',
        '310' => '上海浦东发展银行',
        '422' => '河北银行',
        '440' => '徽商银行',
        '3001' => '东亚银行',
    ),

    //单号类型区分（单号首字符）
    'ORDER_SN_TYPE' => array(
        '1' => 0,//销售订单
        '2' => 1,//充值订单
        '3' => 2,//提现订单
        '4' => 3,//PCB订单
    ),

    //数字转class字符串，按顺序0-9
    'NUMBER_TO_CLASS' => array(
        array('asfgdqwer','asfgdtyhg','asfgdpolk','asfgdpoqw'),
        array('asfgdrfdf','asfgderfd','asfgdwdsa','asfgdpoer'),
        array('asfgdasde','asfgdqwsz','asfgdrtgd','asfgdpovv'),
        array('asfgdwsxc','asfgdwsxz','asfgdrfvb','asfgdpoee'),
        array('asfgdqazs','asfgdqasd','asfgdqwag','asfgdpogh'),
        array('asfgdrtyh','asfgdyutr','asfgdeews','asfgdpotg'),
        array('asfgdpluj','asfgdikjf','asfgdesgj','asfgdpfff'),
        array('asfgdtrdb','asfgdiksf','asfgdsgkp','asfgdprty'),
        array('asfgdpehl','asfgdstgb','asfgderll','asfgdpokf'),
        array('asfgdpehg','asfgdstgf','asfgderlf','asfgdpogk')
    ),

    //单号类型区分（单号首字符）
    'ORDER_SN_TYPE' => array(
        '1' => 0,//销售订单
        '2' => 1,//充值订单
        '3' => 2,//提现订单
        '4' => 3,//PCB订单
    ),

    // 联营供应商匹配词
    'SUPPLIER_ART_NAME' => array(
        'mouser' => '/(Mouser|贸泽)(?![^<]*(<\/a>|>))/i',
        'element' => '/(Element14|e络盟)(?![^<]*(<\/a>|>))/i',
        'verical' => '/Verical(?![^<]*(<\/a>|>))/i',
        'arrow' => '/(Arrow|艾睿)(?![^<]*(<\/a>|>))/i',
        'chip' => '/Chip One Stop(?![^<]*(<\/a>|>))/i',
        'future' => '/(Future|富昌)(?![^<]*(<\/a>|>))/i',
        'digikey' => '/(Digi-Key|得捷)(?![^<]*(<\/a>|>))/i',
        'avnet' => '/(Avnet|安富利)(?![^<]*(<\/a>|>))/i',
        'master' => '/Master(?![^<]*(<\/a>|>))/i',
        'rs' => '/(RS|欧时)(?![^<]*(<\/a>|>))/',
        'PEI' => '/PEI-Genesis(?![^<]*(<\/a>|>))/i',
        'corestaff' => '/Corestaff(?![^<]*(<\/a>|>))/i',
        'rochester' => '/(RE|罗彻斯特电子)(?![^<]*(<\/a>|>))/',
        'allied' => '/Allied(?![^<]*(<\/a>|>))/i',
    ),

    'REPLACEWORD_BUTTON'=>2,


    //优惠券
    //券类别
    'COUPON_TYPE' => array(
        '1' => '抵扣券',
        '2' => '折扣券',
    ),

    //适用商城
    'COUPON_MALL_TYPE' => array(
        '1' => '全站',
        '2' => '自营',
        '3' => '联营'
    ),
    //使用商品范围
    'COUPON_GOODS_RANGE' => array(
        '1' => '全站',
        '2' => '供应商',
        '3' => '品牌'
    ),

    //领取方式
    'ISSUE_TYPE' => array(
        '1' => '买家领取',
        '2' => '平台发放'
    ),

    //任务体系次数约定
    'TASK_SYSTEM_NUM' =>array(
        1=>1,
        2=>3,//连续三天登录搜索
        3=>1,//有下单记录
        4=>0,//下单次数
        5=>2,//下单次数
    ),
    //任务体系金额约定
    'TASK_SYSTEM_AMOUNT' =>array(
        1=>0,
        2=>0,//连续三天登录搜索
        3=>0,//有下单记录
        4=>1000,//下单金额满足1000
        5=>5000,//下单金额满足5000
    ),
    //任务体系开始时间
    'TASK_SYSTEM_START_TIME'=> 1561367757,


    //无法包邮地区
    'WITHOUT_FREE_SHIPPING_PROVINCE' => array(
        9 => '海南',
        12 => '黑龙江',
        15 => '吉林',
        18 => '辽宁',
        19 => '内蒙古',
        20 => '宁夏',
        21 => '青海',
        28 => '西藏',
        29 => '新疆',
        33 => '香港',
        34 => '澳门',
        35 => '台湾',
    ),

    "WITHOUT_FREE_SHIPPING_PROVINCE_PRICE"=> 18,

    //MRO特殊地区
    'MRO_SPECIEL_SHIPPING_PROVINCE' => array(
        9 => '海南',
        19 => '内蒙古',
        20 => '宁夏',
        21 => '青海',
        28 => '西藏',
        29 => '新疆',
    ),

    "MRO_SHIPPING_AMOUNT_LIMIT" => 600,
    "MRO_SHIPPING_PROVINCE_PRICE" => 15,
    "MRO_SPECIEL_SHIPPING_PROVINCE_PRICE" => 50,

    //获取优惠券 查询购物车时 缓存购物车数据
    "SHOWCOUPON_SHOPPING_CART_CACHE"=>"showcoupon_shopping_cart_cache",
    "SHOWCOUPON_SHOPPING_CART_CACHE_TTL"=>3600,

    //基石商品类型 映射 网站商品类型
    'FOOTSTONE_GOODS_TYPE' => array(
        '0' => '3',//自营
        '1' => '2',//联营
        '2' => '1',//专卖
        '3' => '4',//寄售
        '6' => '6',//京东MRO
    ),

    // 猎芯专营、猎芯备货
    'hk_allowed_supplier_names' => [
        "猎芯专营", 
        "猎芯备货",
    ],


);
