<?php
return array(
    //路由
    /* URL设置 */
    'URL_CASE_INSENSITIVE'   => true, // 默true 表示URL不区分大小写 false则表示区分大小写
    'URL_MODEL'              => 2, // URL访问模式,可选参数0、1、2、3,代表以下四种模式： 0 (普通模式); 1 (PATHINFO 模式); 2 (REWRITE  模式); 3 (兼容模式)  默认为PATHINFO 模式

    'URL_ROUTER_ON'          => true, // 是否开启URL路由
    'URL_ROUTE_RULES'        => array(//动态路由
        //登录注册模块
        '/^login\/(\w+)/' => 'Home/Login/:1',
        '/^reg\/(\w+)/' => 'Home/Reg/:1',
        '/^crmreg\/(\w+)/' => 'Home/CrmReg/:1',
		'/^public\/(\w+)/' => 'Home/Public/:1',
		'/^user\/(\w+)/' => 'Home/User/:1',
        '/^signIn\/(\w+)/' => 'Home/SignIn/:1',
        //积分模块
        '/^point\/(\w+)/' => 'Point/Point/:1',
        //订单模块
        '/^order\/(\w+)$/' => 'Order/Order/:1',
        '/^contract\/(\w+)/' => 'Order/Contract/:1',
        '/^cart\/(\w+)/' => 'Order/Cart/:1',
        '/^pay\/successdraw' => 'Order/Pay/successdraw',//支付异步回调
        '/^pay\/notifyurl\/(\w+)$/' => 'Order/Pay/notifyurl?pay_code=:1',//支付异步回调
        '/^pay\/returnurl\/(\w+)(?:\/(\d+))?$/' => 'Order/Pay/returnurl?pay_code=:1&site_type=:2',//支付同步回调
        '/^pay\/notifyreturn\/(\w+)(?:\/(\d+))?$/' => 'Order/Pay/returnurl?pay_code=:1&site_type=:2&cpcn=1',//中金回调跳转
        '/^pay\/(\w+)/' => 'Order/Pay/:1',
        '/^refund\/notifyurl\/(\w+)$/' => 'Order/Refund/notifyurl?pay_code=:1',//退款异步回调
        '/^refund\/(\w+)/' => 'Order/Refund/:1',
        '/^return\/(\w+)/' => 'Order/Return/:1',
        '/^service\/(\w+)/' => 'Order/OrderService/:1',
        '/^wmsapi\/(\w+)/' => 'Order/Wms/:1',
        '/^removal\/(\w+)/' => 'Order/Removal/:1',
        //地址模块
        '/^address\/(\w+)/' => 'Address/Address/:1',
        //发票模块
        '/^invoice\/(\w+)/' => 'Invoice/Invoice/:1',
        //新闻模块
        '/^article\/(\w+)/' => 'Article/Article/:1',
        //消息模块
        '/^msg\/(\w+)/' => 'Message/Message/:1',
        '/^rpcmsg\/(\w+)/' => 'Message/RpcMessage/:1',
        //供应链模块
        '/^supply\/erp_push\/(\w+)/' => 'Supplychain/ErpPush/:1',
        '/^supply\/(\w+)/' => 'Supplychain/Index/:1',
        '/^supplynew\/(\w+)/' => 'Supplychain/Indexnew/:1',
        '/^supplyhk\/(\w+)/' => 'Supplychain/HongKongOrder/:1',
        '/^supplywechat\/(\w+)/' => 'Supplychain/Wechatapi/:1',
        '/^supplywechatorder\/(\w+)/' => 'Supplychain/WechatOrderapi/:1',
        '/^supplywechatsignfor\/(\w+)/' => 'Supplychain/WechatSignforApi/:1',
        '/^supplywechatwms\/(\w+)/' => 'Supplychain/WechatWmsApi/:1',
        '/^supplyqywechat\/(\w+)/' => 'Supplychain/QiyeWechatApi/:1',
        '/^supplyopen\/(\w+)/' => 'Supplychain/OpenApi/:1',
        //商品模块
        '/^goods\/(\w+)/' => 'Goods/Goods/:1',
        //物流模块
        '/^shipping\/(\w+)/' => 'Shipping/Shipping/:1',
        //活动模块
        '/^activity\/(\w+)/' => 'Activity/Activity/:1',
        // '/^consignment\/(\w+)/' => 'Activity/Consignment/:1',
        // '/^chainfinance\/(\w+)/' => 'Activity/ChainFinance/:1',
        //上传文件
        '/^oss\/(\w+)/' => 'Oss/Oss/:1',
        //营销模块
        '/^coupon\/(\w+)/' => 'Coupon/Coupon/:1',
        '/^voucher\/(\w+)/' => 'Coupon/Voucher/:1',
        '/^ucoupon\/(\w+)/' => 'Coupon/User/:1',
        '/^lottery\/(\w+)/' => 'Activity/Lottery/:1',
        //CRM
        '/^crm\/(\w+)/' => 'Crm/Crm/:1',
        //404
        '/^([^\/]+)$/' => 'Home/Empty',
        // ERP server
        'Server/Server' => 'Server/Server/Server',
        //BOM单
        '/^bom\/(\w+)/' => 'Bom/Bom/:1',
        //BOM匹配
        '/api\/bomMatch\/(\w+)/' => 'Bom/BomMatch/:1',
        '/^cube\/(\w+)/' => 'Activity/Cube/:1',
        //钱包
        '/^wallet\/(\w+)/' => 'Home/Wallet/:1',
        //上报数据消费程序
        '/^publish\/(\w+)/' => 'Server/Publish/:1',
        '/^risk\/(\w+)/' => 'Risk/Risk/:1',
        //供应商模块
        '/^supplier\/(\w+)/' => 'Supplier/Supplier/:1',
    ),

    'URL_MAP_RULES'          => array(//静态路由
        'ldb'       =>  'Home/Ldb/index',
        'remind'    =>  'Activity/Remind/ApiRemind',
        // ERP server
        'Server/Server' => 'Server/Server/Server',
        'apipaymentdays' => 'Activity/Cube/ApiPaymentDays',
        'publish/report_data_to_sensors1' => 'Server/Publish/report_data_to_sensors?rp_type=1',
        'publish/report_data_to_sensors2' => 'Server/Publish/report_data_to_sensors?rp_type=2',

        // 信宏泰配置
        'xht/company' => 'Cms/Xht/getCompany',
        'xht/companyinfo' => 'Cms/Xht/getCompanyInfo',
        'xht/clearcache' => 'Cms/Xht/clearCache',
        'sku/info'     => 'Goods/GoodsInfo/HdGoodsInfo',
        'sample/list'     => 'Goods/GoodsInfo/getSampleList',
        'sample/class/list'  => 'Goods/GoodsInfo/getSampleClassList',
        //推荐商品活动的市场价
        'activity/sku/marketprice'  => 'Goods/GoodsInfo/getSkuMarketPrice',
        //活动新魔方系统活动的商品列表数据
        'activity/sku/getCubeActivityGoodsList'  => 'Goods/GoodsInfo/getCubeActivityGoodsList',
    ),
);
