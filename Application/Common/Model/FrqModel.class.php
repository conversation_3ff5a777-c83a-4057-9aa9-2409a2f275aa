<?php
namespace Common\Model;

use Think\Model;

class FrqModel extends Model
{
    protected $autoCheckFields = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('PURCHASE_DB_CONFIG');
    }

    // 检查明细是否存在采购需求单
    public function isExistsFrq($item_id)
    {
        $map = [];
        if (is_array($item_id)) {
            $map['order_item_id'] = ['in', $item_id];
        } else {
            $map['order_item_id'] = $item_id;
        }

        return $this->where($map)->count();
    }

}