<?php
namespace Common\Model;

use Think\Model;

class SpuModel extends Model
{
    protected $autoCheckFields = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->connection = C('DB_OTHER.SPU');
    }

    public function getBrandStandardArea($standard_brand_id){
        // 根据 standard_brand_id 查询数据
        $result = $this->table('lie_brand_standard')->where(array('standard_brand_id' => $standard_brand_id))->find();
        return $result ?: [];
    }
}