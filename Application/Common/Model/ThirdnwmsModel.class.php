<?php
namespace Common\Model;

use Think\Model;

//异步同步到异构wms函数
class ThirdnwmsModel extends Model
{
    protected $autoCheckFields = false;

    const ERP_QUEUE_NAME = 'lie_queue_egolds_wms'; //新wms 队列名称
    public function _initialize()
    {
        parent::_initialize();
    }

    /*
   * 队列推送队列数据
    *@param  mix $types 类型：
                 1:队列接口soap推送;
                 2:直连soap 推送
                3:队列原生推送，自己拼接json推送到接口

   * @param  array $param  推送数据,如：["CURRENCY"=>'"民币","CUSTOMER"=>"深圳小明电子科技"]
                                      如果 $types=3 ,则是原生推送，自己拼接json推送到接口队列：
   * @param  string $erp_fuc  如果是soap，则是 erp的函数：getCreditAmount 检测账期
   * @param  string $callback_path  相对回调路径：/open/testa
   * @param  string $search_key  日志搜索字段，自定义可以存订单号 ，售后单号 等
   * @param  int $main_time_out  主推送超时时间
   * @param  int $callback_timeout 回调推送时间
   * @param  array $callback_timeout  推送数据
   * @return  array
   */
    private function push($types = 1,$erp_fuc="",$param = [],$callback_path="",$search_key="",$main_time_out=50,$callback_timeout=10)
    {
        $rabmqUrl = C("api_push_data");
        $res = ""; //请求返回结果

        switch ($types){
            case 1: #队列推送
                $uk =  md5(json_encode($param).time());
                $data = [
                    "queue_name"=> self::ERP_QUEUE_NAME,
                    "mq_data"=> [
                        "__from"=>  "ichuntapi",
                        "__insert_time"=> time(),
                        "__timeout"=>$main_time_out,
                        "__route_key"=> $erp_fuc,
                        "__type"=> "http",
                        "__uk"=> $uk,
                        "data"=> (object)$param,
                    ]
                ];
                if ($search_key){
                    $data["mq_data"]["__search_key"] = $search_key;
                }
                if ($callback_path){ //存在回调
                    $data["mq_data"]["__callback"] =   [                                  // 选填字段,有回调时必填
                        "__callback_url"=> $callback_path,    // 回调的url路由地址，暂不支持回调soap接口列
                        "__callback_timeout" => $callback_timeout,            // 回调该接口的超时时间，默认为10秒，选填字段
                        "__callback_type" =>"http",              // 回调该接口的请求方式，同"__type"，选填字段
                        //"__callback_verify"=>"verify"         // 若回调的接口需要校验，如请求龙哥的接口需要该字段，选填字段
                        "__callback_search_key" => $search_key,
                    ];
                }

                $res = post_curl($rabmqUrl,\GuzzleHttp\json_encode($data),["Content-type"=>"application/json"]);
                break;
            case 3: #原生推送，自己组织数据
                $data = $param;
                $res = post_curl($rabmqUrl,$data);
                break;
        }

        if (data_get($_REQUEST,"debuga")) {
            print_r("队列接口：<br/>");
            print_r($rabmqUrl);
            print_r("<br/>请求soap函数：".$erp_fuc);
            print_r("<br/>请求数据：");
            print_r(json_encode($data));
            print_r("<br/>返回结果：");
            print_r($res);
            print_r("<br/>");
        }
        \Think\Log::write(" 出库数据-新wms：".json_encode($data), 1);  //日志
        return $res;
    }

    /*
     * 队列转发
     */
    public function egoldsFinish($input)
    {
         return $this->push(1, "/queue/egolds/egoldsFinish", $input);
    }


}
