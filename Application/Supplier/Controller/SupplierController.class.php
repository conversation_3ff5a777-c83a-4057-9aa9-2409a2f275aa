<?php

namespace Supplier\Controller;

use Article\Controller\BaseController;

use Help\Model\ArticleModel;


class SupplierController extends BaseController
{
    public function _initialize()
    {
        parent::_initialize();
//        $this->login = $this->checkLogin();
//        if (!in_array(strtolower(ACTION_NAME), [])) {
//            //检查登录
//            $res = $this->checkLogin();
//            if ($res['err_code'] != 0) {
//                return $this->apiReturn($res['err_code'], $res['err_msg']);
//            }
//        }
    }

    public function test()
    {
        $code = session_sms(18825159814);
    }

    //判断是否能申请供应商
    public function checkSupplierApply()
    {
        $supplierName = I('supplier_name');
        $url = SUPPLIER_DOMAIN . '/api/external/checkSupplierApply';
        $response = get_curl($url, ['supplier_name' => $supplierName]);
        $response = json_decode($response, true);
        if (isset($response['code'])) {
            return $this->apiReturn($response['code'], $response['msg']);
        }

        return $this->apiReturn(-1, '请求校验供应商接口失败');
    }

    //申请供应商
    public function applySupplier()
    {
        $smsCode = I('sms_code');
         //检查短信验证码
        if (!empty($smsCode)) {
            $code = session_sms(I('mobile'));
            dd($code);
            if ($code !== pwdhash($smsCode, C('SMS_SALT'))) {
                return $this->apiReturn(-1, '短信验证码错误，请重新获取');
            }
        }
        $supplierName = I('supplier_name');
        $url = SUPPLIER_DOMAIN . '/api/external/applySupplier';
        $response = post_curl($url, [
            'supplier_name' => $supplierName,
            'main_product' => I('main_product'),
            'contact_name' => I('contact_name'),
            'email' => I('email'),
            'mobile' => I('mobile'),
            'source' => I('source'),
        ]);
        $response = json_decode($response, true);

        if (isset($response['code'])) {
            return $this->apiReturn($response['code'], $response['msg']);
        }

        return $this->apiReturn(-1, '请求申请供应商接口失败');
    }
}
