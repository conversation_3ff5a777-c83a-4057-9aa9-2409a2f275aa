{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "cfc217bda8599d3f86b3bdd3201c82b9", "packages": [{"name": "doctrine/inflector", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "90b2128806bfde671b6952ab8bea493942c1fdae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/90b2128806bfde671b6952ab8bea493942c1fdae", "reference": "90b2128806bfde671b6952ab8bea493942c1fdae", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "4.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Inflector\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common String Manipulations with regard to casing and singular/plural rules.", "homepage": "http://www.doctrine-project.org", "keywords": ["inflection", "pluralize", "singularize", "string"], "time": "2015-11-06T14:35:42+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.3.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/407b0cb880ace85c9b63c5f9551db498cb2d50ba", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2018-04-22T15:46:56+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "9f83dded91781a01c63574e387eaa769be769115"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/9f83dded91781a01c63574e387eaa769be769115", "reference": "9f83dded91781a01c63574e387eaa769be769115", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2018-12-04T20:46:45+00:00"}, {"name": "illuminate/container", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/container.git", "reference": "c5b8a02a34a52c307f16922334c355c5eef725a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/container/zipball/c5b8a02a34a52c307f16922334c355c5eef725a6", "reference": "c5b8a02a34a52c307f16922334c355c5eef725a6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.4.*", "php": ">=5.6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Container\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Container package.", "homepage": "https://laravel.com", "time": "2017-05-24T14:15:53+00:00"}, {"name": "illuminate/contracts", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "67f642e018f3e95fb0b2ebffc206c3200391b1ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/67f642e018f3e95fb0b2ebffc206c3200391b1ab", "reference": "67f642e018f3e95fb0b2ebffc206c3200391b1ab", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "time": "2017-08-26T23:56:53+00:00"}, {"name": "illuminate/database", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/database.git", "reference": "405aa061a5bc8588cbf3a78fba383541a568e3fe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/database/zipball/405aa061a5bc8588cbf3a78fba383541a568e3fe", "reference": "405aa061a5bc8588cbf3a78fba383541a568e3fe", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/container": "5.4.*", "illuminate/contracts": "5.4.*", "illuminate/support": "5.4.*", "nesbot/carbon": "~1.20", "php": ">=5.6.4"}, "suggest": {"doctrine/dbal": "Required to rename columns and drop SQLite columns (~2.5).", "fzaninotto/faker": "Required to use the eloquent factory builder (~1.4).", "illuminate/console": "Required to use the database commands (5.4.*).", "illuminate/events": "Required to use the observers with Eloquent (5.4.*).", "illuminate/filesystem": "Required to use the migrations (5.4.*).", "illuminate/pagination": "Required to paginate the result set (5.4.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Database\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Database package.", "homepage": "https://laravel.com", "keywords": ["database", "laravel", "orm", "sql"], "time": "2017-08-24T12:07:53+00:00"}, {"name": "illuminate/filesystem", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/filesystem.git", "reference": "b800a1423d06869ee5c2768eee123917f12b693e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/filesystem/zipball/b800a1423d06869ee5c2768eee123917f12b693e", "reference": "b800a1423d06869ee5c2768eee123917f12b693e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4", "symfony/finder": "~3.2"}, "suggest": {"league/flysystem": "Required to use the Flysystem local and FTP drivers (~1.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (~1.0).", "league/flysystem-rackspace": "Required to use the Flysystem Rackspace driver (~1.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Filesystem\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Filesystem package.", "homepage": "https://laravel.com", "time": "2017-08-02T21:58:00+00:00"}, {"name": "illuminate/pagination", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/pagination.git", "reference": "ae1540acf02c8b642666d6901c18d2deb5606b47"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/pagination/zipball/ae1540acf02c8b642666d6901c18d2deb5606b47", "reference": "ae1540acf02c8b642666d6901c18d2deb5606b47", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Pagination\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pagination package.", "homepage": "https://laravel.com", "time": "2017-07-24T13:37:02+00:00"}, {"name": "illuminate/support", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "feab1d1495fd6d38970bd6c83586ba2ace8f299a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/support/zipball/feab1d1495fd6d38970bd6c83586ba2ace8f299a", "reference": "feab1d1495fd6d38970bd6c83586ba2ace8f299a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/inflector": "~1.1", "ext-mbstring": "*", "illuminate/contracts": "5.4.*", "paragonie/random_compat": "~1.4|~2.0", "php": ">=5.6.4"}, "replace": {"tightenco/collect": "self.version"}, "suggest": {"illuminate/filesystem": "Required to use the composer class (5.2.*).", "symfony/process": "Required to use the composer class (~3.2).", "symfony/var-dumper": "Required to use the dd function (~3.2)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}, "files": ["helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "time": "2017-08-15T13:25:41+00:00"}, {"name": "illuminate/translation", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/translation.git", "reference": "b671ddf78cbee60b0b357ad5745eceda2df26082"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/translation/zipball/b671ddf78cbee60b0b357ad5745eceda2df26082", "reference": "b671ddf78cbee60b0b357ad5745eceda2df26082", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.4.*", "illuminate/filesystem": "5.4.*", "illuminate/support": "5.4.*", "php": ">=5.6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Translation package.", "homepage": "https://laravel.com", "time": "2017-06-11T21:19:31+00:00"}, {"name": "illuminate/validation", "version": "v5.4.36", "source": {"type": "git", "url": "https://github.com/illuminate/validation.git", "reference": "c9b7beedfb94e50becfbce1aa354e0851c519809"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/validation/zipball/c9b7beedfb94e50becfbce1aa354e0851c519809", "reference": "c9b7beedfb94e50becfbce1aa354e0851c519809", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/container": "5.4.*", "illuminate/contracts": "5.4.*", "illuminate/support": "5.4.*", "illuminate/translation": "5.4.*", "php": ">=5.6.4", "symfony/http-foundation": "~3.2"}, "suggest": {"illuminate/database": "Required to use the database presence verifier (5.4.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Illuminate\\Validation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Validation package.", "homepage": "https://laravel.com", "time": "2017-08-26T19:43:17+00:00"}, {"name": "jukylin/jaeger-php", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/jukylin/jaeger-php.git", "reference": "f58a6f4a356902e7d70c0f4bad35862babcb33af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jukylin/jaeger-php/zipball/f58a6f4a356902e7d70c0f4bad35862babcb33af", "reference": "f58a6f4a356902e7d70c0f4bad35862babcb33af", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"opentracing/opentracing": "1.0.0-beta5", "packaged/thrift": "0.10.0", "php": ">=5.6.0"}, "require-dev": {"php-coveralls/php-coveralls": "^1.0", "phpunit/phpunit": "^5"}, "type": "library", "autoload": {"psr-4": {"Jaeger\\": "src\\Jaeger"}, "files": ["src/Jaeger/Constants.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "juk<PERSON>in", "email": "<EMAIL>"}], "description": "php client for jaeger", "keywords": ["jaeger", "opentracing", "trace"], "time": "2020-05-28T01:17:38+00:00"}, {"name": "nesbot/carbon", "version": "1.36.1", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "63da8cdf89d7a5efe43aabc794365f6e7b7b8983"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/63da8cdf89d7a5efe43aabc794365f6e7b7b8983", "reference": "63da8cdf89d7a5efe43aabc794365f6e7b7b8983", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7"}, "suggest": {"friendsofphp/php-cs-fixer": "Needed for the `composer phpcs` command. Allow to automatically fix code style.", "phpstan/phpstan": "Needed for the `composer phpstan` command. Allow to detect potential errors."}, "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}}, "autoload": {"psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "description": "A simple API extension for DateTime.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "time": "2018-11-22T18:23:02+00:00"}, {"name": "opentracing/opentracing", "version": "1.0.0-beta5", "source": {"type": "git", "url": "https://github.com/opentracing/opentracing-php.git", "reference": "19591d4084e32eaea061eebd9448b62e5ee3ec19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentracing/opentracing-php/zipball/19591d4084e32eaea061eebd9448b62e5ee3ec19", "reference": "19591d4084e32eaea061eebd9448b62e5ee3ec19", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6||^7.0"}, "require-dev": {"phpunit/phpunit": "~5.7.19", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"OpenTracing\\": "./src/OpenTracing/"}, "files": ["./src/OpenTracing/Tags.php", "./src/OpenTracing/Formats.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "OpenTracing API for PHP", "time": "2018-04-13T13:38:46+00:00"}, {"name": "packaged/thrift", "version": "0.10.0", "source": {"type": "git", "url": "https://github.com/packaged/thrift.git", "reference": "8af3f4c0388319f65a2522844d80d8ded60d211b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/packaged/thrift/zipball/8af3f4c0388319f65a2522844d80d8ded60d211b", "reference": "8af3f4c0388319f65a2522844d80d8ded60d211b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.0.0"}, "type": "library", "autoload": {"psr-0": {"Thrift\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Apache Thrift", "homepage": "http://thrift.apache.org/", "keywords": ["apache", "thrift"], "time": "2017-06-05T07:52:51+00:00"}, {"name": "paragonie/random_compat", "version": "v2.0.17", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "29af24f25bab834fcbb38ad2a69fa93b867e070d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/29af24f25bab834fcbb38ad2a69fa93b867e070d", "reference": "29af24f25bab834fcbb38ad2a69fa93b867e070d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "4.*|5.*"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "autoload": {"files": ["lib/random.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2018-07-04T16:31:37+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "ralouphie/getallheaders", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~3.7.0", "satooshi/php-coveralls": ">=1.0"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2016-02-11T07:05:27+00:00"}, {"name": "sensorsdata/sa-sdk-php", "version": "v1.10.7", "source": {"type": "git", "url": "https://github.com/sensorsdata/sa-sdk-php.git", "reference": "016feba015cc4c308e765d22450d3b044e5fc6e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensorsdata/sa-sdk-php/zipball/016feba015cc4c308e765d22450d3b044e5fc6e3", "reference": "016feba015cc4c308e765d22450d3b044e5fc6e3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5.0"}, "type": "library", "autoload": {"files": ["SensorsAnalytics.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP SDK for Sensors Analytics", "homepage": "http://sensorsdata.cn", "keywords": ["sdk", "sensorsdata"], "time": "2018-12-12T06:55:08+00:00"}, {"name": "symfony/finder", "version": "v3.4.20", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "6cf2be5cbd0e87aa35c01f80ae0bf40b6798e442"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/6cf2be5cbd0e87aa35c01f80ae0bf40b6798e442", "reference": "6cf2be5cbd0e87aa35c01f80ae0bf40b6798e442", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2018-11-11T19:48:54+00:00"}, {"name": "symfony/http-foundation", "version": "v3.4.20", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "ea61dd57c4399b0b2a4162e1820cd9d0783acd38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/ea61dd57c4399b0b2a4162e1820cd9d0783acd38", "reference": "ea61dd57c4399b0b2a4162e1820cd9d0783acd38", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php70": "~1.6"}, "require-dev": {"symfony/expression-language": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2018-11-26T10:17:44+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.10.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "c79c051f5b3a46be09205c73b80b346e4153e494"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/c79c051f5b3a46be09205c73b80b346e4153e494", "reference": "c79c051f5b3a46be09205c73b80b346e4153e494", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2018-09-21T13:07:52+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.10.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "6b88000cdd431cd2e940caa2cb569201f3f84224"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/6b88000cdd431cd2e940caa2cb569201f3f84224", "reference": "6b88000cdd431cd2e940caa2cb569201f3f84224", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"paragonie/random_compat": "~1.0|~2.0|~9.99", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php70\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2018-09-21T06:26:08+00:00"}, {"name": "symfony/translation", "version": "v3.4.20", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "bdbe940ed3ef4179f86032086c32d3a858becc0f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/bdbe940ed3ef4179f86032086c32d3a858becc0f", "reference": "bdbe940ed3ef4179f86032086c32d3a858becc0f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/intl": "^2.8.18|^3.2.5|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2018-11-26T10:17:44+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {"opentracing/opentracing": 10}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.5.9"}, "platform-dev": []}