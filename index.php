<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2014 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// 应用入口文件

// 检测PHP环境
if (version_compare(PHP_VERSION, '5.3.0', '<')) {
    die('require PHP > 5.3.0 !');
}
define('MAIN_DOMAIN', 'liexin.com');
define('NET_DOMAIN', 'liexin.net');
define('IC_NET_DOMAIN', 'ichunt.net');
define('API_DOMAIN', 'http://api.liexin.com');
define('WWW_DOMAIN', 'http://www.liexin.com');
define('M_DOMAIN', 'http://m.liexin.com');
define('ERP_DOMAIN', 'http://192.168.1.238:6999');
define('ERP_SUPPLY_DOMAIN', 'http://192.168.1.235:6777');
define('STONE_DOMAIN', 'http://footstone.liexindev.net');
define('STONE_V2_DOMAIN', 'http://footstone.liexindev.net');
define('SZ_STONE_DOMAIN', 'http://szfootstone.liexin.net');
define('MARKET_DOMAIN', 'http://marketing.liexin.net');
define('CUBE_DOMAIN', 'http://cube.liexin.net');
define('CUBE_NEW_DOMAIN', 'http://cubenew.liexindev.net');
define('FINANCE_DOMAIN', 'http://finance.liexin.net');
define('IS_V', '/v3');
define('SERVICE_DOMAIN', 'http://192.168.1.252:55533');// 开启调试模式 建议开发阶段开启 部署阶段注释或者设为false
define('APP_DEBUG', true);
define('PUR_DOMAIN', 'http://192.168.2.116');
define('XHT_DOMAIN', 'szxht.com');
define('SUPPLIER_DOMAIN', 'http://supplier.liexin.net');

define('ES_DOMAIN', 'http://so.liexin.net');
// define('GOODS_DOMAIN', 'http://192.168.1.252:60004');
define('GOODS_DOMAIN', 'http://192.168.1.237:60014');
define('NEW_GOODS_DOMAIN', 'http://192.168.1.237:60014');
//购物车更新专用商品服务
define('UPDATECART_GOODS_DOMAIN', 'http://192.168.1.252:60004');
define('SZGOODS_DOMAIN', 'http://192.168.1.252:60004');
define('ORDERAPI_DOMAIN', 'http://192.168.1.252:60002');
define('SHUJU_DOMAIN', 'http://shuju.liexin.com');
define('CREDIT_DOMAIN', 'http://192.168.2.229'); // 会员账期
define('CRM_DOMAIN', 'http://crm.liexin.net');
define('CRM_V2_DOMAIN', 'http://crmnew.liexindev.net');
define('SALE_DOMAIN', 'http://order.liexindev.net');
define('PURCHASE_DOMAIN', 'http://pur.liexindev.net');
define('UNITEDDATA_DOMAIN', 'http://united_data.liexindev.net');
define('ASYN_QUEUE_DOMAIN', 'http://192.168.1.252:16590/mq/push'); // 异步队列地址
define('WMS_DOMAIN', 'http://wms.liexindev.net'); // 异步队列地址
define('OPENPLATFORM_DOMAIN', 'http://192.168.1.252:16698');
define('FRQ_DOMAIN', 'http://frq.liexindev.net');


// 定义应用目录
define('APP_PATH', './Application/');

require './ThinkPHP/Library/Vendor/Common/LogReport.php';
require './vendor/autoload.php';
// 引入ThinkPHP入口文件
require './ThinkPHP/ThinkPHP.php';

// 亲^_^ 后面不需要任何代码了 就是如此简单
// 亲这是jenkins测试
